<?php
require_once 'includes/dbh.inc.php';

// Check if table exists
$result = $conn->query("SHOW TABLES LIKE 'fee_types'");
if ($result->num_rows == 0) {
    echo "Table fee_types does not exist!\n";
    exit;
}

// Show table structure
echo "Fee Types Table Structure:\n";
echo "--------------------------\n";
$result = $conn->query("DESCRIBE fee_types");
while ($row = $result->fetch_assoc()) {
    echo "Field: {$row['Field']}, Type: {$row['Type']}, Null: {$row['Null']}, Default: {$row['Default']}\n";
}

// Check if the table has any records
$result = $conn->query("SELECT COUNT(*) as count FROM fee_types");
$row = $result->fetch_assoc();
echo "\nTable has {$row['count']} records.\n";

// Check if amount column exists
$result = $conn->query("SHOW COLUMNS FROM fee_types LIKE 'amount'");
if ($result->num_rows > 0) {
    echo "\nColumn 'amount' exists in fee_types table.\n";
} else {
    echo "\nColumn 'amount' does NOT exist in fee_types table.\n";
}

// Check if is_recurring column exists
$result = $conn->query("SHOW COLUMNS FROM fee_types LIKE 'is_recurring'");
if ($result->num_rows > 0) {
    echo "Column 'is_recurring' exists in fee_types table.\n";
} else {
    echo "Column 'is_recurring' does NOT exist in fee_types table.\n";
} 