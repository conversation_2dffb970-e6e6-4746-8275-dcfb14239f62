/**
 * Title Bar Buffer Fix
 * Prevents title bar from showing buffering status
 */

(function() {
    'use strict';
    
    // Store original title
    let originalTitle = document.title;
    
    // Function to fix title
    function fixTitle() {
        if (document.title !== originalTitle) {
            document.title = originalTitle;
        }
    }
    
    // Function to stop all animations that might cause buffering
    function stopBufferingAnimations() {
        // Stop all marquee elements
        const marquees = document.querySelectorAll('marquee');
        marquees.forEach(function(marquee) {
            if (marquee.stop) {
                marquee.stop();
                setTimeout(function() {
                    if (marquee.start) {
                        marquee.start();
                    }
                }, 100);
            }
        });
        
        // Clear any intervals that might be running
        for (let i = 1; i < 9999; i++) {
            clearInterval(i);
            clearTimeout(i);
        }
    }
    
    // Function to prevent automatic reloads
    function preventAutoReload() {
        // Override location.reload
        if (window.location && window.location.reload) {
            const originalReload = window.location.reload;
            window.location.reload = function(force) {
                console.log('Automatic reload prevented');
                return false;
            };
        }
        
        // Remove any meta refresh tags
        const metaRefresh = document.querySelectorAll('meta[http-equiv="refresh"]');
        metaRefresh.forEach(function(meta) {
            meta.remove();
        });
    }
    
    // Function to optimize page performance
    function optimizePerformance() {
        // Disable automatic form submission
        const forms = document.querySelectorAll('form');
        forms.forEach(function(form) {
            form.addEventListener('submit', function(e) {
                // Allow manual submissions only
                if (!e.isTrusted) {
                    e.preventDefault();
                    return false;
                }
            });
        });
        
        // Optimize images loading
        const images = document.querySelectorAll('img');
        images.forEach(function(img) {
            if (!img.complete) {
                img.addEventListener('load', function() {
                    fixTitle();
                });
                img.addEventListener('error', function() {
                    fixTitle();
                });
            }
        });
    }
    
    // Main initialization function
    function init() {
        // Set original title
        originalTitle = 'নিশাত এডুকেশন সেন্টার';
        document.title = originalTitle;
        
        // Stop buffering animations
        stopBufferingAnimations();
        
        // Prevent auto reload
        preventAutoReload();
        
        // Optimize performance
        optimizePerformance();
        
        // Monitor title changes
        const titleObserver = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.target === document.querySelector('title')) {
                    fixTitle();
                }
            });
        });
        
        // Start observing title changes
        const titleElement = document.querySelector('title');
        if (titleElement) {
            titleObserver.observe(titleElement, {
                childList: true,
                subtree: true
            });
        }
        
        // Fix title periodically
        setInterval(fixTitle, 1000);
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    // Initialize immediately as well
    setTimeout(init, 100);
    
})();
