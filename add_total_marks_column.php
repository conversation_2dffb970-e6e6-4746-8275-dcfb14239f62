<?php
// Database Connection
require_once 'includes/dbh.inc.php';

echo "<h1>Add total_marks Column to Results Table</h1>";

// Check if results table exists
$tableCheck = $conn->query("SHOW TABLES LIKE 'results'");
if ($tableCheck->num_rows == 0) {
    echo "<p style='color:red;'>Results table does not exist!</p>";
    exit;
}

// Check if total_marks column already exists
$columnCheck = $conn->query("SHOW COLUMNS FROM results LIKE 'total_marks'");
if ($columnCheck->num_rows > 0) {
    echo "<p style='color:orange;'>The 'total_marks' column already exists in the results table.</p>";
} else {
    // Add the total_marks column
    $alterQuery = "ALTER TABLE results ADD COLUMN total_marks FLOAT NOT NULL DEFAULT 100 AFTER marks_obtained";
    
    if ($conn->query($alterQuery)) {
        echo "<p style='color:green;'>Successfully added 'total_marks' column to the results table!</p>";
        
        // Update existing records to set a default value
        $updateQuery = "UPDATE results SET total_marks = 100 WHERE total_marks IS NULL OR total_marks = 0";
        if ($conn->query($updateQuery)) {
            echo "<p style='color:green;'>Updated existing records with default total_marks value.</p>";
        } else {
            echo "<p style='color:red;'>Error updating existing records: " . $conn->error . "</p>";
        }
    } else {
        echo "<p style='color:red;'>Error adding column: " . $conn->error . "</p>";
    }
}

// Show the updated table structure
echo "<h2>Updated Table Structure</h2>";
$columnsQuery = "SHOW COLUMNS FROM results";
$columnsResult = $conn->query($columnsQuery);

if ($columnsResult) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($column = $columnsResult->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>Error getting columns: " . $conn->error . "</p>";
}

echo "<p><a href='admin/marks_entry.php'>Go to Marks Entry Page</a></p>";

$conn->close();
?>
