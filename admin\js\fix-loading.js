/**
 * Fix Loading Script
 * This script fixes loading issues without breaking functionality
 */

// Execute immediately
(function() {
    // Stop any existing intervals that might be causing the loading issue
    for (let i = 1; i < 1000; i++) {
        window.clearInterval(i);
    }
    
    // Force page to be visible
    document.body.style.display = 'block';
    document.body.style.visibility = 'visible';
    document.body.style.opacity = '1';
    
    // Fix title
    const originalTitle = document.title;
    setInterval(function() {
        if (document.title !== originalTitle) {
            document.title = originalTitle;
        }
    }, 500);
    
    // Add CSS to hide loading elements
    const style = document.createElement('style');
    style.textContent = `
        #page-loader,
        .loader,
        .loading-spinner,
        .loader-spinner,
        .loader-text {
            display: none !important;
        }
    `;
    document.head.appendChild(style);
})();
