<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Set current page for sidebar highlighting
$currentPage = 'template_manager.php';
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>মার্কস ডিস্ট্রিবিউশন টেমপ্লেট ম্যানেজার - কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css">

    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow: hidden; /* Prevent body scroll */
        }

        /* Container for sidebar and main content */
        .container-fluid {
            height: 100vh;
            overflow: hidden;
        }

        .row {
            height: 100vh;
            margin: 0;
        }

        /* Sidebar Styling with Independent Scrolling */
        .col-md-3 {
            padding: 0;
            height: 100vh;
            overflow-y: auto;
            overflow-x: hidden;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            border-right: 3px solid rgba(255, 255, 255, 0.1);
        }

        /* Enhanced Sidebar Font Colors */
        .col-md-3 * {
            color: #ffffff !important;
        }

        .col-md-3 .nav-link {
            color: #ecf0f1 !important;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .col-md-3 .nav-link:hover {
            color: #3498db !important;
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .col-md-3 .nav-link.active {
            color: #e74c3c !important;
            background: rgba(231, 76, 60, 0.2);
            border-left: 4px solid #e74c3c;
        }

        .col-md-3 .navbar-brand {
            color: #ffffff !important;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .col-md-3 h6 {
            color: #bdc3c7 !important;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .col-md-3 .fas, .col-md-3 .far {
            color: #3498db !important;
        }

        /* Main Content Area with Independent Scrolling */
        .col-md-9 {
            padding: 0;
            height: 100vh;
            overflow-y: auto;
            overflow-x: hidden;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .main-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            min-height: calc(100vh - 40px);
        }

        /* Custom Scrollbar for Sidebar */
        .col-md-3::-webkit-scrollbar {
            width: 8px;
        }

        .col-md-3::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .col-md-3::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }

        .col-md-3::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* Custom Scrollbar for Main Content */
        .col-md-9::-webkit-scrollbar {
            width: 10px;
        }

        .col-md-9::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .col-md-9::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 5px;
        }

        .col-md-9::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }

        .page-header {
            background: linear-gradient(135deg, #4361ee 0%, #3f37c9 100%);
            color: white;
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .page-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 0;
        }

        .template-card {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .template-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #4361ee, #3f37c9, #7209b7);
        }

        .template-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .template-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .template-name {
            font-size: 1.4rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .template-id {
            font-size: 0.9rem;
            color: #718096;
            background: #f7fafc;
            padding: 4px 12px;
            border-radius: 20px;
            display: inline-block;
        }

        .marks-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .marks-item {
            text-align: center;
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .marks-item:hover {
            transform: scale(1.05);
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        }

        .marks-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .marks-label {
            font-size: 0.85rem;
            color: #6c757d;
            font-weight: 500;
        }

        .marks-value.cq { color: #4361ee; }
        .marks-value.mcq { color: #2ecc71; }
        .marks-value.practical { color: #f39c12; }
        .marks-value.total { color: #e74c3c; }

        .status-badge {
            padding: 8px 16px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-active {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .status-inactive {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn-modern {
            border-radius: 12px;
            padding: 10px 20px;
            font-weight: 600;
            border: none;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-modern:hover::before {
            left: 100%;
        }

        .btn-edit {
            background: linear-gradient(135deg, #4361ee 0%, #3f37c9 100%);
            color: white;
        }

        .btn-edit:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
            color: white;
        }

        .btn-toggle {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .btn-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);
            color: white;
        }

        .btn-toggle.inactive {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        }

        .add-template-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .add-template-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .add-template-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .form-control-modern {
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transition: all 0.3s ease;
        }

        .form-control-modern::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .form-control-modern:focus {
            border-color: rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
            color: white;
        }

        .form-label-modern {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 600;
            margin-bottom: 8px;
        }

        .btn-add-template {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 15px 30px;
            font-weight: 700;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn-add-template:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(46, 204, 113, 0.4);
            color: white;
        }

        .form-check-modern {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .form-check-input-modern {
            width: 20px;
            height: 20px;
            border-radius: 6px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            background: transparent;
        }

        .form-check-input-modern:checked {
            background: #2ecc71;
            border-color: #2ecc71;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 20px;
            margin: 20px 0;
        }

        .empty-icon {
            font-size: 4rem;
            color: #cbd5e0;
            margin-bottom: 20px;
        }

        .empty-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #4a5568;
            margin-bottom: 10px;
        }

        .empty-text {
            color: #718096;
            font-size: 1.1rem;
        }

        .alert-modern {
            border: none;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            position: relative;
            overflow: hidden;
        }

        .alert-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: currentColor;
        }

        .alert-success-modern {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
        }

        .alert-danger-modern {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
        }

        .alert-info-modern {
            background: linear-gradient(135deg, #cce7ff 0%, #b3d9ff 100%);
            color: #004085;
        }

        /* Loading Animation */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4361ee;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Notification Styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            min-width: 300px;
            max-width: 500px;
        }

        /* Enhanced Hover Effects */
        .template-card {
            cursor: pointer;
        }

        .template-card:hover .template-name {
            color: #4361ee;
        }

        .marks-item:hover {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            transform: scale(1.05);
        }

        /* Form Enhancements */
        .form-control-modern:focus {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
        }

        /* Button Ripple Effect */
        .btn-modern {
            position: relative;
            overflow: hidden;
        }

        .btn-modern:active::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            transform: translate(-50%, -50%);
            animation: ripple 0.6s ease-out;
        }

        @keyframes ripple {
            to {
                width: 300px;
                height: 300px;
                opacity: 0;
            }
        }

        @media (max-width: 768px) {
            body {
                overflow: auto; /* Allow body scroll on mobile */
            }

            .container-fluid, .row {
                height: auto;
            }

            .col-md-3, .col-md-9 {
                height: auto;
                overflow: visible;
            }

            .col-md-3 {
                position: relative;
                background: linear-gradient(90deg, #2c3e50 0%, #34495e 100%);
            }

            .main-content {
                margin: 10px;
                padding: 20px;
                min-height: auto;
            }

            .page-title {
                font-size: 2rem;
            }

            .marks-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }

            .action-buttons {
                justify-content: center;
                flex-direction: column;
                gap: 8px;
            }

            .btn-modern {
                width: 100%;
                justify-content: center;
            }

            .template-card {
                margin-bottom: 15px;
                padding: 20px;
            }

            .add-template-card {
                padding: 20px;
            }

            .notification {
                right: 10px;
                left: 10px;
                min-width: auto;
            }
        }

        @media (max-width: 480px) {
            .marks-grid {
                grid-template-columns: 1fr;
            }

            .page-header {
                padding: 20px;
                text-align: center;
            }

            .page-title {
                font-size: 1.5rem;
            }

            .template-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>

<body onload="document.body.classList.add('loaded')">
    <div class="container-fluid">
        <div class="row">
            <!-- Include sidebar -->
            <?php include_once 'includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-0">
                <div class="main-content">
                    <!-- Page Header -->
                    <div class="page-header animate__animated animate__fadeInDown">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h1 class="page-title">
                                    <i class="fas fa-cogs me-3"></i>
                                    টেমপ্লেট ম্যানেজার
                                </h1>
                                <p class="page-subtitle">
                                    বিভিন্ন ক্লাস লেভেলের জন্য ডিফল্ট মার্কস বিতরণ টেমপ্লেট পরিচালনা করুন
                                </p>
                            </div>
                            <div class="text-end">
                                <i class="fas fa-chart-pie" style="font-size: 3rem; opacity: 0.3;"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Loading Overlay -->
                    <div class="loading-overlay" id="loadingOverlay">
                        <div class="loading-spinner"></div>
                    </div>

<?php

try {
    // Get all templates
    $templatesQuery = "SELECT * FROM class_level_templates ORDER BY class_level";
    $templates = $conn->query($templatesQuery);

    // Display success/error messages
    if (isset($_SESSION['success_message'])) {
        echo '<div class="alert alert-success-modern alert-modern alert-dismissible fade show animate__animated animate__fadeIn">';
        echo '<i class="fas fa-check-circle me-2"></i>' . htmlspecialchars($_SESSION['success_message']);
        echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
        echo '</div>';
        unset($_SESSION['success_message']);
    }

    if (isset($_SESSION['error_message'])) {
        echo '<div class="alert alert-danger-modern alert-modern alert-dismissible fade show animate__animated animate__fadeIn">';
        echo '<i class="fas fa-exclamation-circle me-2"></i>' . htmlspecialchars($_SESSION['error_message']);
        echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
        echo '</div>';
        unset($_SESSION['error_message']);
    }

    // Templates Display
    echo '<div class="templates-container">';

    if ($templates && $templates->num_rows > 0) {
        $count = 0;
        while ($template = $templates->fetch_assoc()) {
            $count++;
            echo '<div class="template-card animate__animated animate__fadeInUp" style="animation-delay: ' . ($count * 0.1) . 's;">';

            // Template Header
            echo '<div class="template-header">';
            echo '<div>';
            echo '<div class="template-name">' . htmlspecialchars($template['class_level_name']) . '</div>';
            echo '<div class="template-id">' . htmlspecialchars($template['class_level']) . '</div>';
            echo '</div>';
            echo '<div>';
            if ($template['is_active']) {
                echo '<span class="status-badge status-active">সক্রিয়</span>';
            } else {
                echo '<span class="status-badge status-inactive">নিষ্ক্রিয়</span>';
            }
            echo '</div>';
            echo '</div>';

            // Description
            if (!empty($template['description'])) {
                echo '<div class="mb-3">';
                echo '<p class="text-muted mb-0">' . htmlspecialchars($template['description']) . '</p>';
                echo '</div>';
            }

            // Marks Grid
            echo '<div class="marks-grid">';

            // Total Marks
            echo '<div class="marks-item">';
            echo '<div class="marks-value total">' . $template['default_total_marks'] . '</div>';
            echo '<div class="marks-label">মোট নম্বর</div>';
            echo '</div>';

            // CQ Marks
            echo '<div class="marks-item">';
            if ($template['has_cq']) {
                echo '<div class="marks-value cq">' . $template['cq_percentage'] . '%</div>';
                echo '<div class="marks-label">সৃজনশীল (' . $template['default_cq_marks'] . ')</div>';
            } else {
                echo '<div class="marks-value" style="color: #cbd5e0;">N/A</div>';
                echo '<div class="marks-label">সৃজনশীল</div>';
            }
            echo '</div>';

            // MCQ Marks
            echo '<div class="marks-item">';
            if ($template['has_mcq']) {
                echo '<div class="marks-value mcq">' . $template['mcq_percentage'] . '%</div>';
                echo '<div class="marks-label">MCQ (' . $template['default_mcq_marks'] . ')</div>';
            } else {
                echo '<div class="marks-value" style="color: #cbd5e0;">N/A</div>';
                echo '<div class="marks-label">MCQ</div>';
            }
            echo '</div>';

            // Practical Marks
            echo '<div class="marks-item">';
            if ($template['has_practical']) {
                echo '<div class="marks-value practical">' . $template['practical_percentage'] . '%</div>';
                echo '<div class="marks-label">ব্যবহারিক (' . $template['default_practical_marks'] . ')</div>';
            } else {
                echo '<div class="marks-value" style="color: #cbd5e0;">N/A</div>';
                echo '<div class="marks-label">ব্যবহারিক</div>';
            }
            echo '</div>';

            echo '</div>';

            // Action Buttons
            echo '<div class="action-buttons">';
            echo '<button onclick="editTemplate(' . $template['id'] . ')" class="btn-modern btn-edit">';
            echo '<i class="fas fa-edit me-2"></i>সম্পাদনা';
            echo '</button>';

            $toggleClass = $template['is_active'] ? 'btn-toggle' : 'btn-toggle inactive';
            $toggleText = $template['is_active'] ? 'নিষ্ক্রিয় করুন' : 'সক্রিয় করুন';
            $toggleIcon = $template['is_active'] ? 'fa-toggle-off' : 'fa-toggle-on';

            echo '<button onclick="toggleTemplateStatus(' . $template['id'] . ', ' . ($template['is_active'] ? '0' : '1') . ')" class="btn-modern ' . $toggleClass . '">';
            echo '<i class="fas ' . $toggleIcon . ' me-2"></i>' . $toggleText;
            echo '</button>';
            echo '</div>';

            echo '</div>';
        }
    } else {
        echo '<div class="empty-state animate__animated animate__fadeIn">';
        echo '<div class="empty-icon">';
        echo '<i class="fas fa-folder-open"></i>';
        echo '</div>';
        echo '<div class="empty-title">কোন টেমপ্লেট পাওয়া যায়নি</div>';
        echo '<div class="empty-text">নতুন টেমপ্লেট যোগ করতে নিচের ফর্ম ব্যবহার করুন</div>';
        echo '</div>';
    }

    echo '</div>'; // Close templates-container

    // Add new template form
    echo '<div class="add-template-card animate__animated animate__fadeInUp">';
    echo '<div class="add-template-header">';
    echo '<h2 class="add-template-title">';
    echo '<i class="fas fa-plus-circle me-3"></i>নতুন টেমপ্লেট যোগ করুন';
    echo '</h2>';
    echo '<p class="mb-0" style="opacity: 0.8;">কাস্টম মার্কস ডিস্ট্রিবিউশন টেমপ্লেট তৈরি করুন</p>';
    echo '</div>';

    echo '<form id="newTemplateForm">';
    echo '<div class="row">';
    echo '<div class="col-md-6 mb-3">';
    echo '<label class="form-label-modern">টেমপ্লেট আইডি</label>';
    echo '<input type="text" class="form-control-modern" name="class_level" placeholder="যেমন: custom_template_1" required>';
    echo '</div>';
    echo '<div class="col-md-6 mb-3">';
    echo '<label class="form-label-modern">টেমপ্লেট নাম</label>';
    echo '<input type="text" class="form-control-modern" name="class_level_name" placeholder="যেমন: কাস্টম টেমপ্লেট" required>';
    echo '</div>';
    echo '</div>';
    echo '<div class="mb-3">';
    echo '<label class="form-label-modern">বিবরণ</label>';
    echo '<textarea class="form-control-modern" name="description" rows="2" placeholder="টেমপ্লেটের বিবরণ লিখুন"></textarea>';
    echo '</div>';
    echo '<div class="row">';
    echo '<div class="col-md-3 mb-3">';
    echo '<label class="form-label-modern">মোট নম্বর</label>';
    echo '<input type="number" class="form-control-modern" name="default_total_marks" value="100" min="1" required>';
    echo '</div>';
    echo '<div class="col-md-3 mb-3">';
    echo '<label class="form-label-modern">সৃজনশীল নম্বর</label>';
    echo '<input type="number" class="form-control-modern" name="default_cq_marks" value="70" min="0" step="0.01">';
    echo '</div>';
    echo '<div class="col-md-3 mb-3">';
    echo '<label class="form-label-modern">MCQ নম্বর</label>';
    echo '<input type="number" class="form-control-modern" name="default_mcq_marks" value="30" min="0" step="0.01">';
    echo '</div>';
    echo '<div class="col-md-3 mb-3">';
    echo '<label class="form-label-modern">ব্যবহারিক নম্বর</label>';
    echo '<input type="number" class="form-control-modern" name="default_practical_marks" value="0" min="0" step="0.01">';
    echo '</div>';
    echo '</div>';
    echo '<div class="row">';
    echo '<div class="col-md-4 mb-3">';
    echo '<div class="form-check-modern">';
    echo '<input class="form-check-input-modern" type="checkbox" name="has_cq" checked>';
    echo '<label class="form-check-label" style="color: rgba(255,255,255,0.9);">সৃজনশীল প্রশ্ন আছে</label>';
    echo '</div>';
    echo '</div>';
    echo '<div class="col-md-4 mb-3">';
    echo '<div class="form-check-modern">';
    echo '<input class="form-check-input-modern" type="checkbox" name="has_mcq" checked>';
    echo '<label class="form-check-label" style="color: rgba(255,255,255,0.9);">বহুনির্বাচনি প্রশ্ন আছে</label>';
    echo '</div>';
    echo '</div>';
    echo '<div class="col-md-4 mb-3">';
    echo '<div class="form-check-modern">';
    echo '<input class="form-check-input-modern" type="checkbox" name="has_practical">';
    echo '<label class="form-check-label" style="color: rgba(255,255,255,0.9);">ব্যবহারিক পরীক্ষা আছে</label>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '<button type="submit" class="btn-add-template">';
    echo '<i class="fas fa-plus-circle me-2"></i>টেমপ্লেট যোগ করুন';
    echo '</button>';
    echo '</form>';
    echo '</div>';

    echo '</div>'; // Close main-content
    echo '</main>';
    echo '</div>'; // Close row
    echo '</div>'; // Close container-fluid

    // JavaScript for template management
    echo '<script>';
    echo 'function editTemplate(id) {';
    echo '    window.location.href = "edit_template.php?id=" + id;';
    echo '}';
    echo '';
    echo 'function toggleTemplateStatus(id, status) {';
    echo '    if (confirm("আপনি কি নিশ্চিত যে এই টেমপ্লেটের স্ট্যাটাস পরিবর্তন করতে চান?")) {';
    echo '        fetch("toggle_template_status.php", {';
    echo '            method: "POST",';
    echo '            headers: {"Content-Type": "application/x-www-form-urlencoded"},';
    echo '            body: `id=${id}&status=${status}`';
    echo '        })';
    echo '        .then(response => response.json())';
    echo '        .then(data => {';
    echo '            if (data.success) {';
    echo '                location.reload();';
    echo '            } else {';
    echo '                alert("স্ট্যাটাস পরিবর্তন করতে সমস্যা হয়েছে।");';
    echo '            }';
    echo '        });';
    echo '    }';
    echo '}';
    echo '';
    echo 'document.getElementById("newTemplateForm").addEventListener("submit", function(e) {';
    echo '    e.preventDefault();';
    echo '    ';
    echo '    const formData = new FormData(this);';
    echo '    const submitBtn = this.querySelector("button[type=submit]");';
    echo '    const originalText = submitBtn.innerHTML;';
    echo '    ';
    echo '    submitBtn.disabled = true;';
    echo '    submitBtn.innerHTML = "<i class=\"fas fa-spinner fa-spin me-2\"></i>যোগ করা হচ্ছে...";';
    echo '    ';
    echo '    fetch("add_template.php", {';
    echo '        method: "POST",';
    echo '        body: formData';
    echo '    })';
    echo '    .then(response => response.json())';
    echo '    .then(data => {';
    echo '        if (data.success) {';
    echo '            alert(data.message);';
    echo '            location.reload();';
    echo '        } else {';
    echo '            alert("ত্রুটি: " + data.message);';
    echo '        }';
    echo '    })';
    echo '    .catch(error => {';
    echo '        alert("নেটওয়ার্ক ত্রুটি: " + error.message);';
    echo '    })';
    echo '    .finally(() => {';
    echo '        submitBtn.disabled = false;';
    echo '        submitBtn.innerHTML = originalText;';
    echo '    });';
    echo '});';
    echo '';
    echo '// Add real-time validation for new template form';
    echo 'function validateNewTemplateForm() {';
    echo '    const totalMarks = parseFloat(document.querySelector("input[name=default_total_marks]").value) || 0;';
    echo '    const cqMarks = parseFloat(document.querySelector("input[name=default_cq_marks]").value) || 0;';
    echo '    const mcqMarks = parseFloat(document.querySelector("input[name=default_mcq_marks]").value) || 0;';
    echo '    const practicalMarks = parseFloat(document.querySelector("input[name=default_practical_marks]").value) || 0;';
    echo '    ';
    echo '    const totalSum = cqMarks + mcqMarks + practicalMarks;';
    echo '    const isValid = Math.abs(totalSum - totalMarks) < 0.01;';
    echo '    ';
    echo '    const submitBtn = document.querySelector("#newTemplateForm button[type=submit]");';
    echo '    ';
    echo '    if (isValid) {';
    echo '        submitBtn.disabled = false;';
    echo '        submitBtn.className = "btn btn-success";';
    echo '        submitBtn.innerHTML = "<i class=\"fas fa-plus me-2\"></i>টেমপ্লেট যোগ করুন";';
    echo '    } else {';
    echo '        submitBtn.disabled = true;';
    echo '        submitBtn.className = "btn btn-warning";';
    echo '        submitBtn.innerHTML = "<i class=\"fas fa-exclamation-triangle me-2\"></i>মার্কস ঠিক করুন (" + totalSum + "/" + totalMarks + ")";';
    echo '    }';
    echo '}';
    echo '';
    echo '// Add event listeners for real-time validation';
    echo 'document.addEventListener("DOMContentLoaded", function() {';
    echo '    const marksInputs = document.querySelectorAll("#newTemplateForm input[type=number]");';
    echo '    marksInputs.forEach(input => {';
    echo '        input.addEventListener("input", validateNewTemplateForm);';
    echo '    });';
    echo '    ';
    echo '    // Initial validation';
    echo '    validateNewTemplateForm();';
    echo '    ';
    echo '    // Add loading and notification functions';
    echo '    window.showLoading = function() {';
    echo '        document.getElementById("loadingOverlay").classList.add("show");';
    echo '    };';
    echo '    ';
    echo '    window.hideLoading = function() {';
    echo '        document.getElementById("loadingOverlay").classList.remove("show");';
    echo '    };';
    echo '    ';
    echo '    window.showNotification = function(message, type) {';
    echo '        const alertClass = type === "success" ? "alert-success-modern" : "alert-danger-modern";';
    echo '        const icon = type === "success" ? "fa-check-circle" : "fa-exclamation-circle";';
    echo '        ';
    echo '        const notification = document.createElement("div");';
    echo '        notification.className = `alert ${alertClass} alert-modern animate__animated animate__fadeInDown notification`;';
    echo '        notification.innerHTML = `<i class="fas ${icon} me-2"></i>${message}`;';
    echo '        ';
    echo '        document.body.appendChild(notification);';
    echo '        ';
    echo '        setTimeout(() => {';
    echo '            notification.classList.remove("animate__fadeInDown");';
    echo '            notification.classList.add("animate__fadeOutUp");';
    echo '            setTimeout(() => notification.remove(), 500);';
    echo '        }, 3000);';
    echo '    };';
    echo '    ';
    echo '    // Enhanced toggle function';
    echo '    window.toggleTemplateStatus = function(id, status) {';
    echo '        const statusText = status ? "সক্রিয়" : "নিষ্ক্রিয়";';
    echo '        if (confirm(`আপনি কি নিশ্চিত যে এই টেমপ্লেটটি ${statusText} করতে চান?`)) {';
    echo '            showLoading();';
    echo '            fetch("toggle_template_status.php", {';
    echo '                method: "POST",';
    echo '                headers: {"Content-Type": "application/x-www-form-urlencoded"},';
    echo '                body: `id=${id}&status=${status}`';
    echo '            })';
    echo '            .then(response => response.json())';
    echo '            .then(data => {';
    echo '                hideLoading();';
    echo '                if (data.success) {';
    echo '                    showNotification(data.message, "success");';
    echo '                    setTimeout(() => location.reload(), 1500);';
    echo '                } else {';
    echo '                    showNotification("ত্রুটি: " + data.message, "error");';
    echo '                }';
    echo '            })';
    echo '            .catch(error => {';
    echo '                hideLoading();';
    echo '                showNotification("নেটওয়ার্ক ত্রুটি: " + error.message, "error");';
    echo '            });';
    echo '        }';
    echo '    };';
    echo '    ';
    echo '    // Add hover effects to template cards';
    echo '    const templateCards = document.querySelectorAll(".template-card");';
    echo '    templateCards.forEach(card => {';
    echo '        card.addEventListener("mouseenter", function() {';
    echo '            this.style.transform = "translateY(-10px)";';
    echo '        });';
    echo '        ';
    echo '        card.addEventListener("mouseleave", function() {';
    echo '            this.style.transform = "translateY(0)";';
    echo '        });';
    echo '    });';
    echo '});';
    echo '</script>';

} catch (Exception $e) {
    echo '<div class="alert alert-danger">';
    echo '<i class="fas fa-exclamation-circle me-2"></i>ডেটা লোড করতে সমস্যা হয়েছে: ' . htmlspecialchars($e->getMessage());
    echo '</div>';
}
?>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
