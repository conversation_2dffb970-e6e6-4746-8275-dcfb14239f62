<?php
// Initialize variables
$message = '';
$error = '';
$active_tab = isset($_POST['tab']) ? $_POST['tab'] : (isset($_GET['tab']) ? $_GET['tab'] : 'about');

// Define default values
$about_title = "আমাদের পরিচিতি";
$about_text1 = "ZFAW একটি আধুনিক শিক্ষা প্রতিষ্ঠান যা ২০১০ সালে প্রতিষ্ঠিত হয়েছিল। আমরা উচ্চমানের শিক্ষা প্রদানের মাধ্যমে শিক্ষার্থীদের ভবিষ্যৎ গড়তে সাহায্য করি।";
$about_text2 = "আমাদের প্রতিষ্ঠানে অভিজ্ঞ শিক্ষক এবং আধুনিক শিক্ষা উপকরণ রয়েছে। আমরা শিক্ষার্থীদের শুধু একাডেমিক জ্ঞান নয়, বরং জীবনের সামগ্রিক দক্ষতা অর্জনে সাহায্য করি।";
$about_text3 = "আমাদের লক্ষ্য হল শিক্ষার্থীদের একটি উজ্জ্বল ভবিষ্যতের জন্য প্রস্তুত করা এবং তাদের সমাজের দায়িত্বশীল নাগরিক হিসেবে গড়ে তোলা।";

$mission_title = "আমাদের লক্ষ্য";
$mission_text = "আমাদের লক্ষ্য হল শিক্ষার্থীদের জ্ঞান, দক্ষতা এবং মূল্যবোধের সমন্বয়ে একটি সম্পূর্ণ শিক্ষা প্রদান করা, যাতে তারা বিশ্বের যেকোন প্রতিযোগিতায় সফল হতে পারে।";

$vision_title = "আমাদের উদ্দেশ্য";
$vision_text = "আমাদের উদ্দেশ্য হল একটি এমন শিক্ষা পরিবেশ তৈরি করা যেখানে শিক্ষার্থীরা তাদের সম্পূর্ণ সম্ভাবনা অনুসন্ধান করতে পারে এবং সমাজের উন্নয়নে অবদান রাখতে প্রস্তুত হয়।";

// Features section - default values
$features_title = "আমাদের বৈশিষ্ট্যসমূহ";
$features_subtitle = "ZFAW স্কুল ম্যানেজমেন্ট সিস্টেমের প্রধান বৈশিষ্ট্যসমূহ";
$features = [
    [
        'icon' => 'fas fa-user-graduate',
        'title' => 'শিক্ষার্থী ব্যবস্থাপনা',
        'description' => 'শিক্ষার্থীদের তথ্য সংরক্ষণ, হাজিরা, ফলাফল এবং অন্যান্য কার্যক্রম সহজেই পরিচালনা করুন।'
    ],
    [
        'icon' => 'fas fa-chalkboard-teacher',
        'title' => 'শিক্ষক ব্যবস্থাপনা',
        'description' => 'শিক্ষকদের তথ্য, ক্লাস রুটিন, বেতন এবং অন্যান্য কার্যক্রম সহজেই পরিচালনা করুন।'
    ],
    [
        'icon' => 'fas fa-book',
        'title' => 'বিষয় ব্যবস্থাপনা',
        'description' => 'বিভিন্ন শ্রেণির বিষয়সমূহ, সিলেবাস এবং পাঠ্যক্রম সহজেই পরিচালনা করুন।'
    ],
    [
        'icon' => 'fas fa-chart-line',
        'title' => 'ফলাফল ব্যবস্থাপনা',
        'description' => 'পরীক্ষার ফলাফল প্রস্তুত, প্রকাশ এবং বিশ্লেষণ সহজেই করুন।'
    ],
    [
        'icon' => 'fas fa-money-bill-wave',
        'title' => 'ফি ব্যবস্থাপনা',
        'description' => 'শিক্ষার্থীদের বেতন, ফি সংগ্রহ এবং হিসাব সহজেই পরিচালনা করুন।'
    ],
    [
        'icon' => 'fas fa-id-card',
        'title' => 'আইডি কার্ড ও সার্টিফিকেট',
        'description' => 'শিক্ষার্থীদের আইডি কার্ড এবং সার্টিফিকেট সহজেই তৈরি করুন।'
    ]
];
$feature_count = count($features);

// Achievements section - default values
$achievements = [
    [
        'year' => '২০২২',
        'title' => 'জাতীয় বিজ্ঞান অলিম্পিয়াডে প্রথম স্থান',
        'description' => 'আমাদের শিক্ষার্থীরা জাতীয় বিজ্ঞান অলিম্পিয়াডে প্রথম স্থান অর্জন করেছে।'
    ],
    [
        'year' => '২০২১',
        'title' => 'আন্তর্জাতিক গণিত প্রতিযোগিতায় স্বর্ণপদক',
        'description' => 'আমাদের শিক্ষার্থী মোঃ রাকিব হাসান আন্তর্জাতিক গণিত প্রতিযোগিতায় স্বর্ণপদক অর্জন করেছে।'
    ],
    [
        'year' => '২০২০',
        'title' => 'সেরা শিক্ষা প্রতিষ্ঠান পুরস্কার',
        'description' => 'আমাদের প্রতিষ্ঠান শিক্ষা মন্ত্রণালয় থেকে সেরা শিক্ষা প্রতিষ্ঠান পুরস্কার লাভ করেছে।'
    ]
];
$achievement_count = count($achievements);

// Facilities section - default values
$facilities = [
    [
        'icon' => 'fas fa-flask',
        'title' => 'আধুনিক বিজ্ঞান ল্যাব',
        'description' => 'সকল প্রয়োজনীয় সরঞ্জাম সহ আধুনিক বিজ্ঞান ল্যাব।'
    ],
    [
        'icon' => 'fas fa-laptop',
        'title' => 'কম্পিউটার ল্যাব',
        'description' => 'ইন্টারনেট সংযোগ সহ আধুনিক কম্পিউটার ল্যাব।'
    ],
    [
        'icon' => 'fas fa-book',
        'title' => 'সমৃদ্ধ লাইব্রেরি',
        'description' => 'বিভিন্ন বিষয়ের বই সহ সমৃদ্ধ লাইব্রেরি।'
    ],
    [
        'icon' => 'fas fa-futbol',
        'title' => 'খেলার মাঠ',
        'description' => 'বিভিন্ন খেলাধুলার জন্য প্রশস্ত খেলার মাঠ।'
    ]
];
$facility_count = count($facilities);

// History section - default values
$default_history_items = [
    [
        'year' => '২০১০',
        'title' => 'প্রতিষ্ঠান প্রতিষ্ঠা',
        'description' => 'ZFAW প্রতিষ্ঠিত হয় মাত্র ৫০ জন শিক্ষার্থী এবং ১০ জন শিক্ষক নিয়ে।'
    ],
    [
        'year' => '২০১৩',
        'title' => 'নতুন ক্যাম্পাস',
        'description' => 'বর্তমান ক্যাম্পাসে স্থানান্তর এবং নতুন ভবন নির্মাণ।'
    ],
    [
        'year' => '২০১৫',
        'title' => 'আধুনিক ল্যাব প্রতিষ্ঠা',
        'description' => 'বিজ্ঞান ও কম্পিউটার বিষয়ের জন্য আধুনিক ল্যাবরেটরি প্রতিষ্ঠা।'
    ],
    [
        'year' => '২০১৮',
        'title' => 'ডিজিটাল শিক্ষা',
        'description' => 'সকল ক্লাসরুমে ডিজিটাল শিক্ষা উপকরণ যোগ করা হয়।'
    ],
    [
        'year' => '২০২০',
        'title' => 'অনলাইন শিক্ষা',
        'description' => 'কোভিড-১৯ মহামারীর সময় সফলভাবে অনলাইন শিক্ষা কার্যক্রম চালু করা হয়।'
    ]
];

// Initialize history items array
$history_items = [];
$history_count = 0;

// Leadership section - default values
$default_leaders = [
    [
        'name' => 'ড. আবদুল করিম',
        'position' => 'অধ্যক্ষ',
        'description' => '২০ বছরের শিক্ষকতা অভিজ্ঞতা সহ বিশিষ্ট শিক্ষাবিদ।',
        'image' => 'https://via.placeholder.com/150'
    ],
    [
        'name' => 'ড. নাজমা বেগম',
        'position' => 'উপাধ্যক্ষ',
        'description' => '১৫ বছরের শিক্ষকতা অভিজ্ঞতা সহ বিজ্ঞান বিভাগের প্রধান।',
        'image' => 'https://via.placeholder.com/150'
    ],
    [
        'name' => 'মোঃ রফিকুল ইসলাম',
        'position' => 'চেয়ারম্যান',
        'description' => 'শিক্ষা ক্ষেত্রে ২৫ বছরের অভিজ্ঞতা সহ প্রতিষ্ঠানের প্রতিষ্ঠাতা।',
        'image' => 'https://via.placeholder.com/150'
    ]
];

// Initialize leaders array
$leaders = [];
$leader_count = 0;

// Load current values from about.php
if (file_exists('about.php')) {
    $about_content = file_get_contents('about.php');

    // Extract about title and text
    if (preg_match('/<h2 class="about-heading">([^<]*)<\/h2>\s*<p>([^<]*)<\/p>\s*<p>([^<]*)<\/p>\s*<p>([^<]*)<\/p>/s', $about_content, $matches)) {
        $about_title = $matches[1];
        $about_text1 = $matches[2];
        $about_text2 = $matches[3];
        $about_text3 = $matches[4];
    }

    // Extract mission title and text
    if (preg_match('/<h3 class="card-title">([^<]*)<\/h3>\s*<p class="card-text">([^<]*)<\/p>/s', $about_content, $matches)) {
        $mission_title = $matches[1];
        $mission_text = $matches[2];
    }

    // Extract vision title and text
    if (preg_match('/<h3 class="card-title">আমাদের উদ্দেশ্য<\/h3>\s*<p class="card-text">([^<]*)<\/p>/s', $about_content, $matches)) {
        $vision_text = $matches[1];
    }

    // Extract history items
    $history_pattern = '/<div class="timeline-item">\s*<div class="timeline-year">([^<]*)<\/div>\s*<h4>([^<]*)<\/h4>\s*<p>([^<]*)<\/p>\s*<\/div>/s';
    if (preg_match_all($history_pattern, $about_content, $matches, PREG_SET_ORDER)) {
        $history_items = [];
        foreach ($matches as $match) {
            $history_items[] = [
                'year' => $match[1],
                'title' => $match[2],
                'description' => $match[3]
            ];
        }
        $history_count = count($history_items);
    } else {
        // If no history items found, use default
        $history_items = $default_history_items;
        $history_count = count($history_items);
    }

    // Extract leadership items
    $leader_pattern = '/<div class="team-member">\s*<img src="([^"]*)" alt="[^"]*">\s*<h4>([^<]*)<\/h4>\s*<p class="text-muted">([^<]*)<\/p>\s*<p>([^<]*)<\/p>\s*<\/div>/s';
    if (preg_match_all($leader_pattern, $about_content, $matches, PREG_SET_ORDER)) {
        $leaders = [];
        foreach ($matches as $match) {
            $leaders[] = [
                'image' => $match[1],
                'name' => $match[2],
                'position' => $match[3],
                'description' => $match[4]
            ];
        }
        $leader_count = count($leaders);
    } else {
        // If no leaders found, use default
        $leaders = $default_leaders;
        $leader_count = count($leaders);
    }

    // Extract achievements
    $achievement_pattern = '/<div class="achievement-item">\s*<div class="achievement-year">([^<]*)<\/div>\s*<h4>([^<]*)<\/h4>\s*<p>([^<]*)<\/p>\s*<\/div>/s';
    if (preg_match_all($achievement_pattern, $about_content, $matches, PREG_SET_ORDER)) {
        $achievements = [];
        foreach ($matches as $match) {
            $achievements[] = [
                'year' => $match[1],
                'title' => $match[2],
                'description' => $match[3]
            ];
        }
        $achievement_count = count($achievements);
    }

    // Extract facilities
    $facility_pattern = '/<div class="facility-item">\s*<div class="facility-icon">\s*<i class="([^"]*)">\s*<\/i>\s*<\/div>\s*<h4>([^<]*)<\/h4>\s*<p>([^<]*)<\/p>\s*<\/div>/s';
    if (preg_match_all($facility_pattern, $about_content, $matches, PREG_SET_ORDER)) {
        $facilities = [];
        foreach ($matches as $match) {
            $facilities[] = [
                'icon' => $match[1],
                'title' => $match[2],
                'description' => $match[3]
            ];
        }
        $facility_count = count($facilities);
    }
}

// Load features from index.php
if (file_exists('index.php')) {
    $index_content = file_get_contents('index.php');

    // Extract features section title and subtitle
    if (preg_match('/<h2 class="fw-bold">([^<]*)<\/h2>\s*<p class="text-muted">([^<]*)<\/p>/s', $index_content, $matches)) {
        $features_title = $matches[1];
        $features_subtitle = $matches[2];
    }

    // Extract features
    $feature_pattern = '/<div class="feature-box">\s*<div class="feature-icon">\s*<i class="([^"]*)">\s*<\/i>\s*<\/div>\s*<h4>([^<]*)<\/h4>\s*<p>([^<]*)<\/p>\s*<\/div>/s';
    if (preg_match_all($feature_pattern, $index_content, $matches, PREG_SET_ORDER)) {
        $features = [];
        foreach ($matches as $match) {
            $features[] = [
                'icon' => $match[1],
                'title' => $match[2],
                'description' => $match[3]
            ];
        }
        $feature_count = count($features);
    }
}

// Check if form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Set active tab based on form submission
    if (isset($_POST['tab'])) {
        $active_tab = $_POST['tab'];
    }

    // Process about section
    $about_title = $_POST['about_title'] ?? $about_title;
    $about_text1 = $_POST['about_text1'] ?? $about_text1;
    $about_text2 = $_POST['about_text2'] ?? $about_text2;
    $about_text3 = $_POST['about_text3'] ?? $about_text3;

    // Process mission and vision
    $mission_title = $_POST['mission_title'] ?? $mission_title;
    $mission_text = $_POST['mission_text'] ?? $mission_text;
    $vision_title = $_POST['vision_title'] ?? $vision_title;
    $vision_text = $_POST['vision_text'] ?? $vision_text;

    // Check for history actions
    if (isset($_POST['action']) && isset($_POST['history_id'])) {
        $action = $_POST['action'];
        $id = intval($_POST['history_id']);

        if ($action === 'delete_history' && $id >= 0 && $id < $history_count) {
            // Remove the history item
            array_splice($history_items, $id, 1);
            $history_count--;
            $message .= "ইতিহাস আইটেম #" . ($id + 1) . " সফলভাবে মুছে ফেলা হয়েছে।<br>";
        }
    } else if (isset($_POST['add_history'])) {
        // Add a new history item
        $history_items[] = [
            'year' => $_POST['new_history_year'] ?? '',
            'title' => $_POST['new_history_title'] ?? '',
            'description' => $_POST['new_history_description'] ?? ''
        ];
        $history_count++;
        $message .= "নতুন ইতিহাস আইটেম সফলভাবে যোগ করা হয়েছে।<br>";
    } else {
        // Process history items (update existing)
        $history_items = [];
        $history_count = intval($_POST['history_count'] ?? 0);

        for ($i = 0; $i < $history_count; $i++) {
            if (isset($_POST["history_year_$i"]) && isset($_POST["history_title_$i"]) && isset($_POST["history_description_$i"])) {
                $history_items[] = [
                    'year' => $_POST["history_year_$i"],
                    'title' => $_POST["history_title_$i"],
                    'description' => $_POST["history_description_$i"]
                ];
            }
        }
    }

    // Check for leadership actions
    if (isset($_POST['action']) && isset($_POST['leader_id'])) {
        $action = $_POST['action'];
        $id = intval($_POST['leader_id']);

        if ($action === 'delete_leader' && $id >= 0 && $id < $leader_count) {
            // Remove the leader
            array_splice($leaders, $id, 1);
            $leader_count--;
            $message .= "নেতৃত্ব আইটেম #" . ($id + 1) . " সফলভাবে মুছে ফেলা হয়েছে।<br>";
        }
    } else if (isset($_POST['add_leader'])) {
        // Add a new leader
        $new_leader = [
            'name' => $_POST['new_leader_name'] ?? '',
            'position' => $_POST['new_leader_position'] ?? '',
            'description' => $_POST['new_leader_description'] ?? '',
            'image' => 'https://via.placeholder.com/150'
        ];

        // Process new leader image upload
        if (isset($_FILES['new_leader_image']) && $_FILES['new_leader_image']['error'] == 0) {
            $allowed = ['jpg', 'jpeg', 'png', 'gif'];
            $filename = $_FILES['new_leader_image']['name'];
            $filetype = pathinfo($filename, PATHINFO_EXTENSION);

            if (in_array(strtolower($filetype), $allowed)) {
                if (!file_exists('img/leaders')) {
                    mkdir('img/leaders', 0777, true);
                }
                $new_filename = "leader_new_" . time() . "." . $filetype;
                if (move_uploaded_file($_FILES['new_leader_image']['tmp_name'], "img/leaders/$new_filename")) {
                    $new_leader['image'] = "img/leaders/$new_filename";
                    $message .= "নতুন নেতৃত্বের ছবি সফলভাবে আপলোড হয়েছে।<br>";
                } else {
                    $error .= "নতুন নেতৃত্বের ছবি আপলোড করতে সমস্যা হয়েছে।<br>";
                }
            } else {
                $error .= "নতুন নেতৃত্বের ছবির জন্য শুধুমাত্র JPG, JPEG, PNG এবং GIF ফাইল গ্রহণযোগ্য।<br>";
            }
        }

        $leaders[] = $new_leader;
        $leader_count++;
        $message .= "নতুন নেতৃত্ব আইটেম সফলভাবে যোগ করা হয়েছে।<br>";
    } else {
        // Process leadership items (update existing)
        $leaders = [];
        $leader_count = intval($_POST['leader_count'] ?? 0);

        for ($i = 0; $i < $leader_count; $i++) {
            if (isset($_POST["leader_name_$i"]) && isset($_POST["leader_position_$i"]) && isset($_POST["leader_description_$i"])) {
                $leader = [
                    'name' => $_POST["leader_name_$i"],
                    'position' => $_POST["leader_position_$i"],
                    'description' => $_POST["leader_description_$i"],
                    'image' => $_POST["leader_current_image_$i"] ?? 'https://via.placeholder.com/150'
                ];

                // Process leader image upload
                if (isset($_FILES["leader_image_$i"]) && $_FILES["leader_image_$i"]['error'] == 0) {
                    $allowed = ['jpg', 'jpeg', 'png', 'gif'];
                    $filename = $_FILES["leader_image_$i"]['name'];
                    $filetype = pathinfo($filename, PATHINFO_EXTENSION);

                    if (in_array(strtolower($filetype), $allowed)) {
                        if (!file_exists('img/leaders')) {
                            mkdir('img/leaders', 0777, true);
                        }
                        $new_filename = "leader_" . ($i + 1) . "_" . time() . "." . $filetype;
                        if (move_uploaded_file($_FILES["leader_image_$i"]['tmp_name'], "img/leaders/$new_filename")) {
                            $leader['image'] = "img/leaders/$new_filename";
                            $message .= "নেতৃত্ব #" . ($i + 1) . " এর ছবি সফলভাবে আপলোড হয়েছে।<br>";
                        } else {
                            $error .= "নেতৃত্ব #" . ($i + 1) . " এর ছবি আপলোড করতে সমস্যা হয়েছে।<br>";
                        }
                    } else {
                        $error .= "নেতৃত্ব #" . ($i + 1) . " এর ছবির জন্য শুধুমাত্র JPG, JPEG, PNG এবং GIF ফাইল গ্রহণযোগ্য।<br>";
                    }
                }

                $leaders[] = $leader;
            }
        }
    }

    // Check for achievement actions
    if (isset($_POST['action']) && isset($_POST['achievement_id'])) {
        $action = $_POST['action'];
        $id = intval($_POST['achievement_id']);

        if ($action === 'delete_achievement' && $id >= 0 && $id < $achievement_count) {
            // Remove the achievement item
            array_splice($achievements, $id, 1);
            $achievement_count--;
            $message .= "অর্জন আইটেম #" . ($id + 1) . " সফলভাবে মুছে ফেলা হয়েছে।<br>";
            $active_tab = 'achievements';
        }
    } else if (isset($_POST['add_achievement'])) {
        // Add a new achievement item
        $achievements[] = [
            'year' => $_POST['new_achievement_year'] ?? '',
            'title' => $_POST['new_achievement_title'] ?? '',
            'description' => $_POST['new_achievement_description'] ?? ''
        ];
        $achievement_count++;
        $message .= "নতুন অর্জন আইটেম সফলভাবে যোগ করা হয়েছে।<br>";
        $active_tab = 'achievements';
    } else if (isset($_POST['update_achievements'])) {
        // Process achievement items (update existing)
        $achievements = [];
        $achievement_count = intval($_POST['achievement_count'] ?? 0);

        for ($i = 0; $i < $achievement_count; $i++) {
            if (isset($_POST["achievement_year_$i"]) && isset($_POST["achievement_title_$i"]) && isset($_POST["achievement_description_$i"])) {
                $achievements[] = [
                    'year' => $_POST["achievement_year_$i"],
                    'title' => $_POST["achievement_title_$i"],
                    'description' => $_POST["achievement_description_$i"]
                ];
            }
        }
        $message .= "অর্জনসমূহ সফলভাবে আপডেট করা হয়েছে।<br>";
        $active_tab = 'achievements';
    }

    // Check for facility actions
    if (isset($_POST['action']) && isset($_POST['facility_id'])) {
        $action = $_POST['action'];
        $id = intval($_POST['facility_id']);

        if ($action === 'delete_facility' && $id >= 0 && $id < $facility_count) {
            // Remove the facility item
            array_splice($facilities, $id, 1);
            $facility_count--;
            $message .= "সুবিধা আইটেম #" . ($id + 1) . " সফলভাবে মুছে ফেলা হয়েছে।<br>";
            $active_tab = 'facilities';
        }
    } else if (isset($_POST['add_facility'])) {
        // Add a new facility item
        $facilities[] = [
            'icon' => $_POST['new_facility_icon'] ?? 'fas fa-check',
            'title' => $_POST['new_facility_title'] ?? '',
            'description' => $_POST['new_facility_description'] ?? ''
        ];
        $facility_count++;
        $message .= "নতুন সুবিধা আইটেম সফলভাবে যোগ করা হয়েছে।<br>";
        $active_tab = 'facilities';
    } else if (isset($_POST['update_facilities'])) {
        // Process facility items (update existing)
        $facilities = [];
        $facility_count = intval($_POST['facility_count'] ?? 0);

        for ($i = 0; $i < $facility_count; $i++) {
            if (isset($_POST["facility_icon_$i"]) && isset($_POST["facility_title_$i"]) && isset($_POST["facility_description_$i"])) {
                $facilities[] = [
                    'icon' => $_POST["facility_icon_$i"],
                    'title' => $_POST["facility_title_$i"],
                    'description' => $_POST["facility_description_$i"]
                ];
            }
        }
        $message .= "সুবিধাসমূহ সফলভাবে আপডেট করা হয়েছে।<br>";
        $active_tab = 'facilities';
    }

    // Check for feature actions
    if (isset($_POST['action']) && isset($_POST['feature_id'])) {
        $action = $_POST['action'];
        $id = intval($_POST['feature_id']);

        if ($action === 'delete_feature' && $id >= 0 && $id < $feature_count) {
            // Remove the feature item
            array_splice($features, $id, 1);
            $feature_count--;
            $message .= "বৈশিষ্ট্য আইটেম #" . ($id + 1) . " সফলভাবে মুছে ফেলা হয়েছে।<br>";
            $active_tab = 'features';
        }
    } else if (isset($_POST['add_feature'])) {
        // Add a new feature item
        $features[] = [
            'icon' => $_POST['new_feature_icon'] ?? 'fas fa-check',
            'title' => $_POST['new_feature_title'] ?? '',
            'description' => $_POST['new_feature_description'] ?? ''
        ];
        $feature_count++;
        $message .= "নতুন বৈশিষ্ট্য আইটেম সফলভাবে যোগ করা হয়েছে।<br>";
        $active_tab = 'features';
    } else if (isset($_POST['update_features'])) {
        // Process feature items (update existing)
        $features_title = $_POST['features_title'] ?? $features_title;
        $features_subtitle = $_POST['features_subtitle'] ?? $features_subtitle;

        $features = [];
        $feature_count = intval($_POST['feature_count'] ?? 0);

        for ($i = 0; $i < $feature_count; $i++) {
            if (isset($_POST["feature_icon_$i"]) && isset($_POST["feature_title_$i"]) && isset($_POST["feature_description_$i"])) {
                $features[] = [
                    'icon' => $_POST["feature_icon_$i"],
                    'title' => $_POST["feature_title_$i"],
                    'description' => $_POST["feature_description_$i"]
                ];
            }
        }
        $message .= "বৈশিষ্ট্যসমূহ সফলভাবে আপডেট করা হয়েছে।<br>";
        $active_tab = 'features';
    }

    // Process about image upload
    if (isset($_FILES['about_image']) && $_FILES['about_image']['error'] == 0) {
        $allowed = ['jpg', 'jpeg', 'png', 'gif'];
        $filename = $_FILES['about_image']['name'];
        $filetype = pathinfo($filename, PATHINFO_EXTENSION);

        if (in_array(strtolower($filetype), $allowed)) {
            if (!file_exists('img')) {
                mkdir('img', 0777, true);
            }
            if (move_uploaded_file($_FILES['about_image']['tmp_name'], 'img/about-image.jpg')) {
                $message .= "আমাদের সম্পর্কে সেকশনের ছবি সফলভাবে আপলোড হয়েছে।<br>";
            } else {
                $error .= "আমাদের সম্পর্কে সেকশনের ছবি আপলোড করতে সমস্যা হয়েছে।<br>";
            }
        } else {
            $error .= "আমাদের সম্পর্কে সেকশনের ছবির জন্য শুধুমাত্র JPG, JPEG, PNG এবং GIF ফাইল গ্রহণযোগ্য।<br>";
        }
    }

    // Update about.php with new content
    if (file_exists('about.php')) {
        $about_content = file_get_contents('about.php');

        // Update about section title and text
        $about_content = preg_replace('/<h2 class="about-heading">([^<]*)<\/h2>\s*<p>([^<]*)<\/p>\s*<p>([^<]*)<\/p>\s*<p>([^<]*)<\/p>/s',
            '<h2 class="about-heading">' . $about_title . '</h2>' . "\n" .
            '                        <p>' . $about_text1 . '</p>' . "\n" .
            '                        <p>' . $about_text2 . '</p>' . "\n" .
            '                        <p>' . $about_text3 . '</p>',
            $about_content);

        // Update mission title and text
        $about_content = preg_replace('/<h3 class="card-title">([^<]*)<\/h3>\s*<p class="card-text">([^<]*)<\/p>/s',
            '<h3 class="card-title">' . $mission_title . '</h3>' . "\n" .
            '                                <p class="card-text">' . $mission_text . '</p>',
            $about_content, 1);

        // Update vision title and text
        $about_content = preg_replace('/<h3 class="card-title">আমাদের উদ্দেশ্য<\/h3>\s*<p class="card-text">([^<]*)<\/p>/s',
            '<h3 class="card-title">' . $vision_title . '</h3>' . "\n" .
            '                                <p class="card-text">' . $vision_text . '</p>',
            $about_content);

        // Update history items
        $history_html = '';
        foreach ($history_items as $item) {
            $history_html .= '                            <div class="timeline-item">' . "\n" .
                            '                                <div class="timeline-year">' . $item['year'] . '</div>' . "\n" .
                            '                                <h4>' . $item['title'] . '</h4>' . "\n" .
                            '                                <p>' . $item['description'] . '</p>' . "\n" .
                            '                            </div>' . "\n";
        }

        // Replace history section
        $about_content = preg_replace('/<div class="timeline">(.*?)<\/div>\s*<\/div>\s*<\/div>/s',
            '<div class="timeline">' . "\n" . $history_html . '                        </div>' . "\n" .
            '                    </div>' . "\n" .
            '                </div>',
            $about_content);

        // Update leadership items
        $leaders_html = '';
        foreach ($leaders as $leader) {
            $leaders_html .= '                    <div class="col-md-4">' . "\n" .
                            '                        <div class="team-member">' . "\n" .
                            '                            <img src="' . $leader['image'] . '" alt="' . $leader['name'] . '">' . "\n" .
                            '                            <h4>' . $leader['name'] . '</h4>' . "\n" .
                            '                            <p class="text-muted">' . $leader['position'] . '</p>' . "\n" .
                            '                            <p>' . $leader['description'] . '</p>' . "\n" .
                            '                        </div>' . "\n" .
                            '                    </div>' . "\n";
        }

        // Replace leadership section
        $about_content = preg_replace('/<div class="row">\s*<div class="col-md-4">\s*<div class="team-member">.*?<\/div>\s*<\/div>\s*<\/div>/s',
            '<div class="row">' . "\n" . $leaders_html . '                </div>',
            $about_content);

        // Update achievements section
        $achievements_html = '';
        foreach ($achievements as $item) {
            $achievements_html .= '                            <div class="achievement-item">' . "\n" .
                                '                                <div class="achievement-year">' . $item['year'] . '</div>' . "\n" .
                                '                                <h4>' . $item['title'] . '</h4>' . "\n" .
                                '                                <p>' . $item['description'] . '</p>' . "\n" .
                                '                            </div>' . "\n";
        }

        // Check if achievements section exists, if not, add it
        if (strpos($about_content, 'আমাদের অর্জন') === false) {
            // Add achievements section after leadership section
            $about_content = preg_replace('/<\/section>\s*<section class="container my-5">/s',
                '</section>' . "\n\n" .
                '    <!-- Achievements Section -->' . "\n" .
                '    <section class="container my-5">' . "\n" .
                '        <div class="row">' . "\n" .
                '            <div class="col-12 text-center mb-4">' . "\n" .
                '                <h2 class="section-title">আমাদের অর্জন</h2>' . "\n" .
                '                <p class="text-muted">আমাদের প্রতিষ্ঠানের উল্লেখযোগ্য অর্জনসমূহ</p>' . "\n" .
                '            </div>' . "\n" .
                '        </div>' . "\n" .
                '        <div class="row">' . "\n" .
                '            <div class="col-lg-10 mx-auto">' . "\n" .
                '                <div class="achievements">' . "\n" .
                $achievements_html .
                '                </div>' . "\n" .
                '            </div>' . "\n" .
                '        </div>' . "\n" .
                '    </section>' . "\n\n" .
                '    <section class="container my-5">',
                $about_content);
        } else {
            // Replace existing achievements section
            $about_content = preg_replace('/<div class="achievements">(.*?)<\/div>\s*<\/div>\s*<\/div>/s',
                '<div class="achievements">' . "\n" . $achievements_html . '                </div>' . "\n" .
                '            </div>' . "\n" .
                '        </div>',
                $about_content);
        }

        // Update facilities section
        $facilities_html = '';
        foreach ($facilities as $item) {
            $facilities_html .= '                    <div class="col-md-3 mb-4">' . "\n" .
                               '                        <div class="facility-item">' . "\n" .
                               '                            <div class="facility-icon">' . "\n" .
                               '                                <i class="' . $item['icon'] . '"></i>' . "\n" .
                               '                            </div>' . "\n" .
                               '                            <h4>' . $item['title'] . '</h4>' . "\n" .
                               '                            <p>' . $item['description'] . '</p>' . "\n" .
                               '                        </div>' . "\n" .
                               '                    </div>' . "\n";
        }

        // Check if facilities section exists, if not, add it
        if (strpos($about_content, 'আমাদের সুবিধাসমূহ') === false) {
            // Add facilities section before footer
            $about_content = preg_replace('/<\/section>\s*<footer/s',
                '</section>' . "\n\n" .
                '    <!-- Facilities Section -->' . "\n" .
                '    <section class="container my-5">' . "\n" .
                '        <div class="row">' . "\n" .
                '            <div class="col-12 text-center mb-4">' . "\n" .
                '                <h2 class="section-title">আমাদের সুবিধাসমূহ</h2>' . "\n" .
                '                <p class="text-muted">আমাদের প্রতিষ্ঠানে উপলব্ধ সুবিধাসমূহ</p>' . "\n" .
                '            </div>' . "\n" .
                '        </div>' . "\n" .
                '        <div class="row">' . "\n" .
                $facilities_html .
                '        </div>' . "\n" .
                '    </section>' . "\n\n" .
                '<footer',
                $about_content);
        } else {
            // Replace existing facilities section
            $about_content = preg_replace('/<div class="row">\s*<div class="col-md-3 mb-4">\s*<div class="facility-item">.*?<\/div>\s*<\/div>\s*<\/div>/s',
                '<div class="row">' . "\n" . $facilities_html . '        </div>',
                $about_content);
        }

        // Save the updated about.php file
        if (file_put_contents('about.php', $about_content)) {
            $message .= "about.php ফাইল সফলভাবে আপডেট করা হয়েছে।<br>";
        } else {
            $error .= "about.php ফাইল আপডেট করতে সমস্যা হয়েছে।<br>";
        }
    } else {
        $error .= "about.php ফাইল পাওয়া যায়নি।<br>";
    }

    // Update index.php with new features content
    if (file_exists('index.php')) {
        $index_content = file_get_contents('index.php');

        // Update features section title and subtitle
        $index_content = preg_replace('/<h2 class="fw-bold">([^<]*)<\/h2>\s*<p class="text-muted">([^<]*)<\/p>/s',
            '<h2 class="fw-bold">' . $features_title . '</h2>' . "\n" .
            '            <p class="text-muted">' . $features_subtitle . '</p>',
            $index_content);

        // Update features items
        $features_html = '';
        foreach ($features as $item) {
            $features_html .= '            <div class="col-md-4">' . "\n" .
                             '                <div class="feature-box">' . "\n" .
                             '                    <div class="feature-icon">' . "\n" .
                             '                        <i class="' . $item['icon'] . '"></i>' . "\n" .
                             '                    </div>' . "\n" .
                             '                    <h4>' . $item['title'] . '</h4>' . "\n" .
                             '                    <p>' . $item['description'] . '</p>' . "\n" .
                             '                </div>' . "\n" .
                             '            </div>' . "\n\n";
        }

        // Replace features section
        $index_content = preg_replace('/<div class="row g-4">(.*?)<\/div>\s*<\/section>/s',
            '<div class="row g-4">' . "\n" . $features_html . '        </div>' . "\n" .
            '    </section>',
            $index_content);

        // Save the updated index.php file
        if (file_put_contents('index.php', $index_content)) {
            $message .= "index.php ফাইল সফলভাবে আপডেট করা হয়েছে।<br>";
        } else {
            $error .= "index.php ফাইল আপডেট করতে সমস্যা হয়েছে।<br>";
        }
    } else {
        $error .= "index.php ফাইল পাওয়া যায়নি।<br>";
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>আমাদের সম্পর্কে আপডেট</title>

    <!-- Bootstrap CSS -->
    

    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: #f8f9fa;
        }

        .form-container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-top: 30px;
            margin-bottom: 30px;
        }

        .section-title {
            border-left: 4px solid #006A4E;
            padding-left: 10px;
            margin-bottom: 20px;
            color: #006A4E;
        }

        .form-label {
            font-weight: 500;
        }

        .preview-image {
            max-width: 150px;
            max-height: 150px;
            border-radius: 5px;
            margin-top: 10px;
            border: 1px solid #ddd;
        }

        .btn-primary {
            background-color: #006A4E;
            border-color: #006A4E;
        }

        .btn-primary:hover {
            background-color: #00563B;
            border-color: #00563B;
        }

        .alert {
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="form-container">
                    <h2 class="text-center mb-4">আমাদের সম্পর্কে আপডেট করুন</h2>

                    <?php if (!empty($message)): ?>
                        <div class="alert alert-success">
                            <?php echo $message; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger">
                            <?php echo $error; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Tab Navigation -->
                    <ul class="nav nav-tabs mb-4" id="myTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link <?php echo $active_tab == 'about' ? 'active' : ''; ?>" id="about-tab" data-bs-toggle="tab" data-bs-target="#about-tab-pane" type="button" role="tab" aria-controls="about-tab-pane" aria-selected="<?php echo $active_tab == 'about' ? 'true' : 'false'; ?>">
                                পরিচিতি ও লক্ষ্য
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link <?php echo $active_tab == 'history' ? 'active' : ''; ?>" id="history-tab" data-bs-toggle="tab" data-bs-target="#history-tab-pane" type="button" role="tab" aria-controls="history-tab-pane" aria-selected="<?php echo $active_tab == 'history' ? 'true' : 'false'; ?>">
                                ইতিহাস ও নেতৃত্ব
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link <?php echo $active_tab == 'features' ? 'active' : ''; ?>" id="features-tab" data-bs-toggle="tab" data-bs-target="#features-tab-pane" type="button" role="tab" aria-controls="features-tab-pane" aria-selected="<?php echo $active_tab == 'features' ? 'true' : 'false'; ?>">
                                আমাদের বৈশিষ্ট্যসমূহ
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link <?php echo $active_tab == 'achievements' ? 'active' : ''; ?>" id="achievements-tab" data-bs-toggle="tab" data-bs-target="#achievements-tab-pane" type="button" role="tab" aria-controls="achievements-tab-pane" aria-selected="<?php echo $active_tab == 'achievements' ? 'true' : 'false'; ?>">
                                আমাদের অর্জন
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link <?php echo $active_tab == 'facilities' ? 'active' : ''; ?>" id="facilities-tab" data-bs-toggle="tab" data-bs-target="#facilities-tab-pane" type="button" role="tab" aria-controls="facilities-tab-pane" aria-selected="<?php echo $active_tab == 'facilities' ? 'true' : 'false'; ?>">
                                আমাদের সুবিধাসমূহ
                            </button>
                        </li>
                    </ul>

                    <form action="" method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="tab" id="active_tab" value="<?php echo $active_tab; ?>">
                        <!-- About Section -->
                        <div class="mb-4">
                            <h4 class="section-title">আমাদের পরিচিতি</h4>

                            <div class="mb-3">
                                <label for="about_title" class="form-label">শিরোনাম</label>
                                <input type="text" class="form-control" id="about_title" name="about_title" value="<?php echo htmlspecialchars($about_title); ?>">
                            </div>

                            <div class="mb-3">
                                <label for="about_text1" class="form-label">প্রথম অনুচ্ছেদ</label>
                                <textarea class="form-control" id="about_text1" name="about_text1" rows="3"><?php echo htmlspecialchars($about_text1); ?></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="about_text2" class="form-label">দ্বিতীয় অনুচ্ছেদ</label>
                                <textarea class="form-control" id="about_text2" name="about_text2" rows="3"><?php echo htmlspecialchars($about_text2); ?></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="about_text3" class="form-label">তৃতীয় অনুচ্ছেদ</label>
                                <textarea class="form-control" id="about_text3" name="about_text3" rows="3"><?php echo htmlspecialchars($about_text3); ?></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="about_image" class="form-label">আমাদের সম্পর্কে সেকশনের ছবি (600x400 পিক্সেল সাইজ সুপারিশ করা হয়)</label>
                                <input type="file" class="form-control" id="about_image" name="about_image">
                                <div class="mt-2">
                                    <img id="about_preview" class="preview-image" src="#" alt="আমাদের সম্পর্কে ছবি প্রিভিউ" style="display: none;">
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">বর্তমান ছবি: <a href="img/about-image.jpg" target="_blank">দেখুন</a></small>
                                </div>
                            </div>
                        </div>

                        <!-- Mission & Vision Section -->
                        <div class="mb-4">
                            <h4 class="section-title">আমাদের লক্ষ্য ও উদ্দেশ্য</h4>

                            <div class="mb-3">
                                <label for="mission_title" class="form-label">লক্ষ্য শিরোনাম</label>
                                <input type="text" class="form-control" id="mission_title" name="mission_title" value="<?php echo htmlspecialchars($mission_title); ?>">
                            </div>

                            <div class="mb-3">
                                <label for="mission_text" class="form-label">লক্ষ্য বিবরণ</label>
                                <textarea class="form-control" id="mission_text" name="mission_text" rows="3"><?php echo htmlspecialchars($mission_text); ?></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="vision_title" class="form-label">উদ্দেশ্য শিরোনাম</label>
                                <input type="text" class="form-control" id="vision_title" name="vision_title" value="<?php echo htmlspecialchars($vision_title); ?>">
                            </div>

                            <div class="mb-3">
                                <label for="vision_text" class="form-label">উদ্দেশ্য বিবরণ</label>
                                <textarea class="form-control" id="vision_text" name="vision_text" rows="3"><?php echo htmlspecialchars($vision_text); ?></textarea>
                            </div>
                        </div>

                        <!-- History Section -->
                        <div class="mb-4">
                            <h4 class="section-title">আমাদের ইতিহাস</h4>

                            <input type="hidden" name="history_count" value="<?php echo $history_count; ?>">

                            <div id="history-items-container">
                                <?php for ($i = 0; $i < $history_count; $i++): ?>
                                    <div class="history-item card mb-3" id="history-item-<?php echo $i; ?>">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h5 class="card-title mb-0">ইতিহাস #<?php echo $i + 1; ?></h5>
                                                <button type="submit" class="btn btn-sm btn-danger" name="action" value="delete_history" onclick="document.getElementById('history_id').value='<?php echo $i; ?>'">
                                                    <i class="fas fa-trash-alt"></i> মুছুন
                                                </button>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-2">
                                                    <label for="history_year_<?php echo $i; ?>" class="form-label">বছর</label>
                                                    <input type="text" class="form-control" id="history_year_<?php echo $i; ?>" name="history_year_<?php echo $i; ?>" value="<?php echo htmlspecialchars($history_items[$i]['year'] ?? ''); ?>">
                                                </div>
                                                <div class="col-md-4">
                                                    <label for="history_title_<?php echo $i; ?>" class="form-label">শিরোনাম</label>
                                                    <input type="text" class="form-control" id="history_title_<?php echo $i; ?>" name="history_title_<?php echo $i; ?>" value="<?php echo htmlspecialchars($history_items[$i]['title'] ?? ''); ?>">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="history_description_<?php echo $i; ?>" class="form-label">বিবরণ</label>
                                                    <textarea class="form-control" id="history_description_<?php echo $i; ?>" name="history_description_<?php echo $i; ?>" rows="2"><?php echo htmlspecialchars($history_items[$i]['description'] ?? ''); ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endfor; ?>
                            </div>

                            <!-- Add New History Item -->
                            <div class="card mb-3 border-success">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">নতুন ইতিহাস যোগ করুন</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-2">
                                            <label for="new_history_year" class="form-label">বছর</label>
                                            <input type="text" class="form-control" id="new_history_year" name="new_history_year" placeholder="২০২৩">
                                        </div>
                                        <div class="col-md-4">
                                            <label for="new_history_title" class="form-label">শিরোনাম</label>
                                            <input type="text" class="form-control" id="new_history_title" name="new_history_title" placeholder="নতুন অর্জন">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="new_history_description" class="form-label">বিবরণ</label>
                                            <textarea class="form-control" id="new_history_description" name="new_history_description" rows="2" placeholder="এখানে বিবরণ লিখুন..."></textarea>
                                        </div>
                                    </div>
                                    <div class="text-end mt-3">
                                        <button type="submit" class="btn btn-success" name="add_history">
                                            <i class="fas fa-plus"></i> যোগ করুন
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <input type="hidden" id="history_id" name="history_id" value="">
                        </div>

                        <!-- Leadership Section -->
                        <div class="mb-4">
                            <h4 class="section-title">আমাদের নেতৃত্ব</h4>

                            <input type="hidden" name="leader_count" value="<?php echo $leader_count; ?>">

                            <div id="leaders-container">
                                <?php for ($i = 0; $i < $leader_count; $i++): ?>
                                    <div class="leader-item card mb-3" id="leader-item-<?php echo $i; ?>">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h5 class="card-title mb-0">নেতৃত্ব #<?php echo $i + 1; ?></h5>
                                                <button type="submit" class="btn btn-sm btn-danger" name="action" value="delete_leader" onclick="document.getElementById('leader_id').value='<?php echo $i; ?>'">
                                                    <i class="fas fa-trash-alt"></i> মুছুন
                                                </button>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <label for="leader_image_<?php echo $i; ?>" class="form-label">ছবি</label>
                                                    <input type="file" class="form-control" id="leader_image_<?php echo $i; ?>" name="leader_image_<?php echo $i; ?>">
                                                    <input type="hidden" name="leader_current_image_<?php echo $i; ?>" value="<?php echo htmlspecialchars($leaders[$i]['image'] ?? ''); ?>">
                                                    <div class="mt-2">
                                                        <img id="leader_preview_<?php echo $i; ?>" class="preview-image" src="<?php echo $leaders[$i]['image'] ?? '#'; ?>" alt="নেতৃত্ব ছবি প্রিভিউ" style="<?php echo isset($leaders[$i]['image']) ? 'display: block;' : 'display: none;'; ?>">
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <label for="leader_name_<?php echo $i; ?>" class="form-label">নাম</label>
                                                    <input type="text" class="form-control" id="leader_name_<?php echo $i; ?>" name="leader_name_<?php echo $i; ?>" value="<?php echo htmlspecialchars($leaders[$i]['name'] ?? ''); ?>">
                                                </div>
                                                <div class="col-md-2">
                                                    <label for="leader_position_<?php echo $i; ?>" class="form-label">পদবি</label>
                                                    <input type="text" class="form-control" id="leader_position_<?php echo $i; ?>" name="leader_position_<?php echo $i; ?>" value="<?php echo htmlspecialchars($leaders[$i]['position'] ?? ''); ?>">
                                                </div>
                                                <div class="col-md-4">
                                                    <label for="leader_description_<?php echo $i; ?>" class="form-label">বিবরণ</label>
                                                    <textarea class="form-control" id="leader_description_<?php echo $i; ?>" name="leader_description_<?php echo $i; ?>" rows="2"><?php echo htmlspecialchars($leaders[$i]['description'] ?? ''); ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endfor; ?>
                            </div>

                            <!-- Add New Leader -->
                            <div class="card mb-3 border-success">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">নতুন নেতৃত্ব যোগ করুন</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label for="new_leader_image" class="form-label">ছবি</label>
                                            <input type="file" class="form-control" id="new_leader_image" name="new_leader_image">
                                            <div class="mt-2">
                                                <img id="new_leader_preview" class="preview-image" src="#" alt="নতুন নেতৃত্ব ছবি প্রিভিউ" style="display: none;">
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="new_leader_name" class="form-label">নাম</label>
                                            <input type="text" class="form-control" id="new_leader_name" name="new_leader_name" placeholder="নাম লিখুন">
                                        </div>
                                        <div class="col-md-2">
                                            <label for="new_leader_position" class="form-label">পদবি</label>
                                            <input type="text" class="form-control" id="new_leader_position" name="new_leader_position" placeholder="পদবি লিখুন">
                                        </div>
                                        <div class="col-md-4">
                                            <label for="new_leader_description" class="form-label">বিবরণ</label>
                                            <textarea class="form-control" id="new_leader_description" name="new_leader_description" rows="2" placeholder="বিবরণ লিখুন..."></textarea>
                                        </div>
                                    </div>
                                    <div class="text-end mt-3">
                                        <button type="submit" class="btn btn-success" name="add_leader">
                                            <i class="fas fa-plus"></i> যোগ করুন
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <input type="hidden" id="leader_id" name="leader_id" value="">
                        </div>

                        <!-- Features Section -->
                        <div class="mb-4" id="features-tab-pane" role="tabpanel" aria-labelledby="features-tab" tabindex="0" style="display: <?php echo $active_tab == 'features' ? 'block' : 'none'; ?>">
                            <h4 class="section-title">আমাদের বৈশিষ্ট্যসমূহ</h4>

                            <div class="mb-3">
                                <label for="features_title" class="form-label">শিরোনাম</label>
                                <input type="text" class="form-control" id="features_title" name="features_title" value="<?php echo htmlspecialchars($features_title); ?>">
                            </div>

                            <div class="mb-3">
                                <label for="features_subtitle" class="form-label">উপশিরোনাম</label>
                                <input type="text" class="form-control" id="features_subtitle" name="features_subtitle" value="<?php echo htmlspecialchars($features_subtitle); ?>">
                            </div>

                            <input type="hidden" name="feature_count" value="<?php echo $feature_count; ?>">

                            <div id="feature-items-container">
                                <?php for ($i = 0; $i < $feature_count; $i++): ?>
                                    <div class="feature-item card mb-3" id="feature-item-<?php echo $i; ?>">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h5 class="card-title mb-0">বৈশিষ্ট্য #<?php echo $i + 1; ?></h5>
                                                <button type="submit" class="btn btn-sm btn-danger" name="action" value="delete_feature" onclick="document.getElementById('feature_id').value='<?php echo $i; ?>'">
                                                    <i class="fas fa-trash-alt"></i> মুছুন
                                                </button>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <label for="feature_icon_<?php echo $i; ?>" class="form-label">আইকন</label>
                                                    <input type="text" class="form-control" id="feature_icon_<?php echo $i; ?>" name="feature_icon_<?php echo $i; ?>" value="<?php echo htmlspecialchars($features[$i]['icon'] ?? ''); ?>" placeholder="fas fa-check">
                                                    <small class="text-muted">Font Awesome আইকন ক্লাস (উদাহরণ: fas fa-user-graduate)</small>
                                                </div>
                                                <div class="col-md-3">
                                                    <label for="feature_title_<?php echo $i; ?>" class="form-label">শিরোনাম</label>
                                                    <input type="text" class="form-control" id="feature_title_<?php echo $i; ?>" name="feature_title_<?php echo $i; ?>" value="<?php echo htmlspecialchars($features[$i]['title'] ?? ''); ?>">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="feature_description_<?php echo $i; ?>" class="form-label">বিবরণ</label>
                                                    <textarea class="form-control" id="feature_description_<?php echo $i; ?>" name="feature_description_<?php echo $i; ?>" rows="2"><?php echo htmlspecialchars($features[$i]['description'] ?? ''); ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endfor; ?>
                            </div>

                            <!-- Add New Feature Item -->
                            <div class="card mb-3 border-success">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">নতুন বৈশিষ্ট্য যোগ করুন</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label for="new_feature_icon" class="form-label">আইকন</label>
                                            <input type="text" class="form-control" id="new_feature_icon" name="new_feature_icon" placeholder="fas fa-check">
                                            <small class="text-muted">Font Awesome আইকন ক্লাস (উদাহরণ: fas fa-user-graduate)</small>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="new_feature_title" class="form-label">শিরোনাম</label>
                                            <input type="text" class="form-control" id="new_feature_title" name="new_feature_title" placeholder="বৈশিষ্ট্যের শিরোনাম">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="new_feature_description" class="form-label">বিবরণ</label>
                                            <textarea class="form-control" id="new_feature_description" name="new_feature_description" rows="2" placeholder="বৈশিষ্ট্যের বিবরণ লিখুন..."></textarea>
                                        </div>
                                    </div>
                                    <div class="text-end mt-3">
                                        <button type="submit" class="btn btn-success" name="add_feature">
                                            <i class="fas fa-plus"></i> যোগ করুন
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <input type="hidden" id="feature_id" name="feature_id" value="">

                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg px-5" name="update_features">বৈশিষ্ট্যসমূহ আপডেট করুন</button>
                            </div>
                        </div>

                        <!-- Achievements Section -->
                        <div class="mb-4" id="achievements-tab-pane" role="tabpanel" aria-labelledby="achievements-tab" tabindex="0" style="display: <?php echo $active_tab == 'achievements' ? 'block' : 'none'; ?>">
                            <h4 class="section-title">আমাদের অর্জন</h4>

                            <input type="hidden" name="achievement_count" value="<?php echo $achievement_count; ?>">

                            <div id="achievement-items-container">
                                <?php for ($i = 0; $i < $achievement_count; $i++): ?>
                                    <div class="achievement-item card mb-3" id="achievement-item-<?php echo $i; ?>">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h5 class="card-title mb-0">অর্জন #<?php echo $i + 1; ?></h5>
                                                <button type="submit" class="btn btn-sm btn-danger" name="action" value="delete_achievement" onclick="document.getElementById('achievement_id').value='<?php echo $i; ?>'">
                                                    <i class="fas fa-trash-alt"></i> মুছুন
                                                </button>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-2">
                                                    <label for="achievement_year_<?php echo $i; ?>" class="form-label">বছর</label>
                                                    <input type="text" class="form-control" id="achievement_year_<?php echo $i; ?>" name="achievement_year_<?php echo $i; ?>" value="<?php echo htmlspecialchars($achievements[$i]['year'] ?? ''); ?>">
                                                </div>
                                                <div class="col-md-4">
                                                    <label for="achievement_title_<?php echo $i; ?>" class="form-label">শিরোনাম</label>
                                                    <input type="text" class="form-control" id="achievement_title_<?php echo $i; ?>" name="achievement_title_<?php echo $i; ?>" value="<?php echo htmlspecialchars($achievements[$i]['title'] ?? ''); ?>">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="achievement_description_<?php echo $i; ?>" class="form-label">বিবরণ</label>
                                                    <textarea class="form-control" id="achievement_description_<?php echo $i; ?>" name="achievement_description_<?php echo $i; ?>" rows="2"><?php echo htmlspecialchars($achievements[$i]['description'] ?? ''); ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endfor; ?>
                            </div>

                            <!-- Add New Achievement Item -->
                            <div class="card mb-3 border-success">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">নতুন অর্জন যোগ করুন</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-2">
                                            <label for="new_achievement_year" class="form-label">বছর</label>
                                            <input type="text" class="form-control" id="new_achievement_year" name="new_achievement_year" placeholder="২০২৩">
                                        </div>
                                        <div class="col-md-4">
                                            <label for="new_achievement_title" class="form-label">শিরোনাম</label>
                                            <input type="text" class="form-control" id="new_achievement_title" name="new_achievement_title" placeholder="অর্জনের শিরোনাম">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="new_achievement_description" class="form-label">বিবরণ</label>
                                            <textarea class="form-control" id="new_achievement_description" name="new_achievement_description" rows="2" placeholder="অর্জনের বিবরণ লিখুন..."></textarea>
                                        </div>
                                    </div>
                                    <div class="text-end mt-3">
                                        <button type="submit" class="btn btn-success" name="add_achievement">
                                            <i class="fas fa-plus"></i> যোগ করুন
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <input type="hidden" id="achievement_id" name="achievement_id" value="">

                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg px-5" name="update_achievements">অর্জনসমূহ আপডেট করুন</button>
                            </div>
                        </div>

                        <!-- Facilities Section -->
                        <div class="mb-4" id="facilities-tab-pane" role="tabpanel" aria-labelledby="facilities-tab" tabindex="0" style="display: <?php echo $active_tab == 'facilities' ? 'block' : 'none'; ?>">
                            <h4 class="section-title">আমাদের সুবিধাসমূহ</h4>

                            <input type="hidden" name="facility_count" value="<?php echo $facility_count; ?>">

                            <div id="facility-items-container">
                                <?php for ($i = 0; $i < $facility_count; $i++): ?>
                                    <div class="facility-item card mb-3" id="facility-item-<?php echo $i; ?>">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h5 class="card-title mb-0">সুবিধা #<?php echo $i + 1; ?></h5>
                                                <button type="submit" class="btn btn-sm btn-danger" name="action" value="delete_facility" onclick="document.getElementById('facility_id').value='<?php echo $i; ?>'">
                                                    <i class="fas fa-trash-alt"></i> মুছুন
                                                </button>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <label for="facility_icon_<?php echo $i; ?>" class="form-label">আইকন</label>
                                                    <input type="text" class="form-control" id="facility_icon_<?php echo $i; ?>" name="facility_icon_<?php echo $i; ?>" value="<?php echo htmlspecialchars($facilities[$i]['icon'] ?? ''); ?>" placeholder="fas fa-check">
                                                    <small class="text-muted">Font Awesome আইকন ক্লাস (উদাহরণ: fas fa-flask)</small>
                                                </div>
                                                <div class="col-md-3">
                                                    <label for="facility_title_<?php echo $i; ?>" class="form-label">শিরোনাম</label>
                                                    <input type="text" class="form-control" id="facility_title_<?php echo $i; ?>" name="facility_title_<?php echo $i; ?>" value="<?php echo htmlspecialchars($facilities[$i]['title'] ?? ''); ?>">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="facility_description_<?php echo $i; ?>" class="form-label">বিবরণ</label>
                                                    <textarea class="form-control" id="facility_description_<?php echo $i; ?>" name="facility_description_<?php echo $i; ?>" rows="2"><?php echo htmlspecialchars($facilities[$i]['description'] ?? ''); ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endfor; ?>
                            </div>

                            <!-- Add New Facility Item -->
                            <div class="card mb-3 border-success">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">নতুন সুবিধা যোগ করুন</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label for="new_facility_icon" class="form-label">আইকন</label>
                                            <input type="text" class="form-control" id="new_facility_icon" name="new_facility_icon" placeholder="fas fa-check">
                                            <small class="text-muted">Font Awesome আইকন ক্লাস (উদাহরণ: fas fa-flask)</small>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="new_facility_title" class="form-label">শিরোনাম</label>
                                            <input type="text" class="form-control" id="new_facility_title" name="new_facility_title" placeholder="সুবিধার শিরোনাম">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="new_facility_description" class="form-label">বিবরণ</label>
                                            <textarea class="form-control" id="new_facility_description" name="new_facility_description" rows="2" placeholder="সুবিধার বিবরণ লিখুন..."></textarea>
                                        </div>
                                    </div>
                                    <div class="text-end mt-3">
                                        <button type="submit" class="btn btn-success" name="add_facility">
                                            <i class="fas fa-plus"></i> যোগ করুন
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <input type="hidden" id="facility_id" name="facility_id" value="">

                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg px-5" name="update_facilities">সুবিধাসমূহ আপডেট করুন</button>
                            </div>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg px-5">সংরক্ষণ করুন</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Function to show image preview
        function showImagePreview(input, previewId) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();

                reader.onload = function(e) {
                    document.getElementById(previewId).src = e.target.result;
                    document.getElementById(previewId).style.display = 'block';
                }

                reader.readAsDataURL(input.files[0]);
            }
        }

        // Set up event listeners for file inputs
        document.getElementById('about_image').addEventListener('change', function() {
            showImagePreview(this, 'about_preview');
        });

        // Set up event listeners for leader image inputs
        for (let i = 0; i < <?php echo $leader_count; ?>; i++) {
            document.getElementById('leader_image_' + i).addEventListener('change', function() {
                showImagePreview(this, 'leader_preview_' + i);
            });
        }

        // Set up event listener for new leader image
        document.getElementById('new_leader_image').addEventListener('change', function() {
            showImagePreview(this, 'new_leader_preview');
        });

        // Tab switching functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Get all tab buttons
            const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');

            // Add click event listeners to each tab button
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Get the target tab pane ID
                    const targetId = this.getAttribute('data-bs-target').substring(1);

                    // Hide all tab panes
                    document.querySelectorAll('[role="tabpanel"]').forEach(pane => {
                        pane.style.display = 'none';
                    });

                    // Show the target tab pane
                    document.getElementById(targetId).style.display = 'block';

                    // Update the active tab in the hidden input
                    document.getElementById('active_tab').value = this.id.replace('-tab', '');

                    // Update active state on tabs
                    tabButtons.forEach(btn => {
                        btn.classList.remove('active');
                        btn.setAttribute('aria-selected', 'false');
                    });

                    this.classList.add('active');
                    this.setAttribute('aria-selected', 'true');
                });
            });
        });
    </script>
</body>
</html>
