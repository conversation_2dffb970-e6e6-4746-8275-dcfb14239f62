<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get parameters
$examId = isset($_GET['exam_id']) ? intval($_GET['exam_id']) : 0;
$classGroup = isset($_GET['class_group']) ? $_GET['class_group'] : '';

if (!$examId) {
    die("পরীক্ষা ID প্রয়োজন।");
}

// Initialize default values
$exam = [
    'exam_name' => 'নমুনা পরীক্ষা',
    'subject_name' => 'বাংলা',
    'class_name' => 'ক্লাস ১',
    'exam_date' => date('Y-m-d'),
    'start_time' => '10:00:00',
    'end_time' => '12:00:00',
    'total_marks' => 100,
    'passing_marks' => 33,
    'class_id' => 1
];

try {
    // Get exam details
    $examTableName = 'exams_primary_lower';
    $resultsTableName = 'results_primary_lower';

    $examQuery = "SELECT e.*, c.class_name, s.subject_name
                  FROM $examTableName e
                  LEFT JOIN classes c ON e.class_id = c.id
                  LEFT JOIN subjects s ON e.subject_id = s.id
                  WHERE e.id = $examId";
    $examResult = $conn->query($examQuery);

    if ($examResult && $examResult->num_rows > 0) {
        $exam = $examResult->fetch_assoc();
    }
} catch (Exception $e) {
    // Use default values if database error
}

// Get students with marks
$studentsResult = null;
try {
    $studentsQuery = "SELECT s.*,
                      COALESCE(s.student_name, s.first_name) as name,
                      COALESCE(s.roll_number, s.student_id) as roll,
                      r.obtained_marks,
                      r.attendance,
                      r.remarks
                      FROM students s
                      LEFT JOIN $resultsTableName r ON s.id = r.student_id AND r.exam_id = $examId
                      WHERE s.class_id = {$exam['class_id']}
                      ORDER BY CAST(COALESCE(s.roll_number, s.student_id) AS UNSIGNED)";
    $studentsResult = $conn->query($studentsQuery);
} catch (Exception $e) {
    // Handle database error
}

// Calculate statistics
$totalStudents = 0;
$presentStudents = 0;
$passedStudents = 0;
$totalMarks = 0;
$highestMarks = 0;
$lowestMarks = $exam['total_marks'];

if ($studentsResult) {
    $studentsResult->data_seek(0);
    while ($student = $studentsResult->fetch_assoc()) {
        if ($student['obtained_marks'] !== null) {
            $totalStudents++;
            if ($student['attendance'] === 'present') {
                $presentStudents++;
                $marks = floatval($student['obtained_marks']);
                $totalMarks += $marks;
                $highestMarks = max($highestMarks, $marks);
                $lowestMarks = min($lowestMarks, $marks);
                
                if ($marks >= $exam['passing_marks']) {
                    $passedStudents++;
                }
            }
        }
    }
}

$averageMarks = $presentStudents > 0 ? $totalMarks / $presentStudents : 0;
$passPercentage = $presentStudents > 0 ? ($passedStudents / $presentStudents) * 100 : 0;

function calculateGrade($obtainedMarks, $totalMarks, $passingMarks) {
    if ($obtainedMarks < $passingMarks) return 'F';
    
    $percentage = ($obtainedMarks / $totalMarks) * 100;
    
    if ($percentage >= 80) return 'A+';
    if ($percentage >= 70) return 'A';
    if ($percentage >= 60) return 'A-';
    if ($percentage >= 50) return 'B';
    if ($percentage >= 40) return 'C';
    return 'D';
}

function getGradePoint($grade) {
    switch ($grade) {
        case 'A+': return 5.00;
        case 'A': return 4.00;
        case 'A-': return 3.50;
        case 'B': return 3.00;
        case 'C': return 2.00;
        case 'D': return 1.00;
        case 'F': return 0.00;
        default: return 0.00;
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>মার্কশীট - <?php echo htmlspecialchars($exam['exam_name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: #f8f9fa;
        }
        .marksheet-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
        }
        .school-name {
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .marksheet-title {
            font-size: 24px;
            font-weight: 600;
            color: #007bff;
            margin-bottom: 15px;
        }
        .exam-info {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
        }
        .stats-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .marksheet-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .marksheet-table th {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            font-weight: 600;
            text-align: center;
            padding: 15px 10px;
            border: none;
        }
        .marksheet-table td {
            padding: 12px 10px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }
        .grade-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 12px;
        }
        .grade-a-plus { background: #28a745; color: white; }
        .grade-a { background: #007bff; color: white; }
        .grade-a-minus { background: #17a2b8; color: white; }
        .grade-b { background: #ffc107; color: #212529; }
        .grade-c { background: #fd7e14; color: white; }
        .grade-d { background: #6c757d; color: white; }
        .grade-f { background: #dc3545; color: white; }
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #6c757d;
            margin-top: 5px;
        }
        @media print {
            body { background: white !important; }
            .no-print { display: none !important; }
            .marksheet-table { box-shadow: none !important; }
            .stats-section { box-shadow: none !important; }
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Print Button -->
        <div class="no-print mb-3">
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print"></i> প্রিন্ট করুন
            </button>
            <button onclick="window.close()" class="btn btn-secondary ms-2">
                <i class="fas fa-times"></i> বন্ধ করুন
            </button>
        </div>

        <!-- Header -->
        <div class="marksheet-header">
            <div class="school-name">আপনার স্কুলের নাম</div>
            <div class="marksheet-title">পরীক্ষার ফলাফল</div>
        </div>

        <!-- Exam Info -->
        <div class="exam-info">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-clipboard-list me-2"></i>পরীক্ষার তথ্য</h5>
                    <p class="mb-1"><strong>পরীক্ষা:</strong> <?php echo htmlspecialchars($exam['exam_name']); ?></p>
                    <p class="mb-1"><strong>বিষয়:</strong> <?php echo htmlspecialchars($exam['subject_name'] ?? 'সকল বিষয়'); ?></p>
                    <p class="mb-0"><strong>শ্রেণি:</strong> <?php echo htmlspecialchars($exam['class_name']); ?></p>
                </div>
                <div class="col-md-6">
                    <h5><i class="fas fa-calendar me-2"></i>সময় ও নম্বর</h5>
                    <p class="mb-1"><strong>তারিখ:</strong> <?php echo date('d/m/Y', strtotime($exam['exam_date'])); ?></p>
                    <p class="mb-1"><strong>সময়:</strong> <?php echo date('h:i A', strtotime($exam['start_time'])); ?> - <?php echo date('h:i A', strtotime($exam['end_time'])); ?></p>
                    <p class="mb-0"><strong>পূর্ণমান:</strong> <?php echo $exam['total_marks']; ?> | <strong>পাশের নম্বর:</strong> <?php echo $exam['passing_marks']; ?></p>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-section">
            <h5 class="mb-4"><i class="fas fa-chart-bar me-2"></i>পরিসংখ্যান</h5>
            <div class="row">
                <div class="col-md-2">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $totalStudents; ?></div>
                        <div class="stat-label">মোট পরীক্ষার্থী</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $presentStudents; ?></div>
                        <div class="stat-label">উপস্থিত</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $passedStudents; ?></div>
                        <div class="stat-label">পাশ</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($passPercentage, 1); ?>%</div>
                        <div class="stat-label">পাশের হার</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $presentStudents > 0 ? $highestMarks : 0; ?></div>
                        <div class="stat-label">সর্বোচ্চ নম্বর</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($averageMarks, 1); ?></div>
                        <div class="stat-label">গড় নম্বর</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Marksheet Table -->
        <div class="marksheet-table">
            <table class="table table-bordered mb-0">
                <thead>
                    <tr>
                        <th style="width: 8%;">ক্রমিক</th>
                        <th style="width: 12%;">রোল নং</th>
                        <th style="width: 30%;">ছাত্র/ছাত্রীর নাম</th>
                        <th style="width: 12%;">প্রাপ্ত নম্বর</th>
                        <th style="width: 10%;">গ্রেড</th>
                        <th style="width: 10%;">জিপিএ</th>
                        <th style="width: 10%;">ফলাফল</th>
                        <th style="width: 8%;">হাজিরা</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    if ($studentsResult && $studentsResult->num_rows > 0):
                        $studentsResult->data_seek(0);
                        $serial = 1;
                        while ($student = $studentsResult->fetch_assoc()): 
                            $obtainedMarks = $student['obtained_marks'] ?? 0;
                            $grade = calculateGrade($obtainedMarks, $exam['total_marks'], $exam['passing_marks']);
                            $gpa = getGradePoint($grade);
                            $result = $obtainedMarks >= $exam['passing_marks'] ? 'পাশ' : 'ফেল';
                            $attendance = $student['attendance'] ?? 'present';
                    ?>
                        <tr>
                            <td><?php echo $serial++; ?></td>
                            <td><strong><?php echo htmlspecialchars($student['roll'] ?? 'N/A'); ?></strong></td>
                            <td style="text-align: left; padding-left: 15px;">
                                <?php echo htmlspecialchars($student['name'] ?? 'নাম নেই'); ?>
                            </td>
                            <td>
                                <strong><?php echo $obtainedMarks; ?></strong> / <?php echo $exam['total_marks']; ?>
                            </td>
                            <td>
                                <span class="grade-badge grade-<?php echo strtolower(str_replace(['+', '-'], ['_plus', '_minus'], $grade)); ?>">
                                    <?php echo $grade; ?>
                                </span>
                            </td>
                            <td><strong><?php echo number_format($gpa, 2); ?></strong></td>
                            <td>
                                <span class="badge <?php echo $result === 'পাশ' ? 'bg-success' : 'bg-danger'; ?>">
                                    <?php echo $result; ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge <?php echo $attendance === 'present' ? 'bg-primary' : 'bg-warning'; ?>">
                                    <?php echo $attendance === 'present' ? 'উপস্থিত' : 'অনুপস্থিত'; ?>
                                </span>
                            </td>
                        </tr>
                    <?php 
                        endwhile;
                    else: 
                    ?>
                        <tr>
                            <td colspan="8" class="text-center text-muted py-4">
                                কোন ফলাফল পাওয়া যায়নি।
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Footer -->
        <div class="mt-4 text-center" style="font-size: 12px; color: #6c757d;">
            <p>এই মার্কশীট <?php echo date('d/m/Y h:i A'); ?> তারিখে তৈরি করা হয়েছে।</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php if (isset($conn)) $conn->close(); ?>
