<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Fetch departments
$departmentsQuery = $conn->query("SELECT id, department_name FROM departments ORDER BY department_name");
$departments = [];
while ($row = $departmentsQuery->fetch_assoc()) {
    $departments[] = $row;
}

// Fetch classes
$classesQuery = $conn->query("SELECT id, class_name FROM classes ORDER BY class_name");
$classes = [];
while ($row = $classesQuery->fetch_assoc()) {
    $classes[] = $row;
}

// Fetch sessions
$sessionsQuery = $conn->query("SELECT id, session_name FROM sessions ORDER BY session_name");
$sessions = [];
while ($row = $sessionsQuery->fetch_assoc()) {
    $sessions[] = $row;
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>CSV সহায়ক তথ্য - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/hind-siliguri.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include('includes/sidebar.php'); ?>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">CSV আপলোড সহায়ক তথ্য</h1>
                    <div>
                        <a href="add_student.php" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-1"></i> ফিরে যান
                        </a>
                    </div>
                </div>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    CSV ফাইলে সঠিক ID ব্যবহার করার জন্য নিচের তালিকা দেখুন।
                </div>
                
                <!-- Departments -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-building me-2"></i>বিভাগ তালিকা (Department IDs)</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>ID</th>
                                        <th>বিভাগের নাম</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($departments as $dept): ?>
                                    <tr>
                                        <td><code><?php echo $dept['id']; ?></code></td>
                                        <td><?php echo htmlspecialchars($dept['department_name']); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php if (empty($departments)): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            কোন বিভাগ পাওয়া যায়নি। <a href="departments.php">প্রথমে বিভাগ যোগ করুন</a>।
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Classes -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-chalkboard me-2"></i>ক্লাস তালিকা (Class IDs)</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>ID</th>
                                        <th>ক্লাসের নাম</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($classes as $class): ?>
                                    <tr>
                                        <td><code><?php echo $class['id']; ?></code></td>
                                        <td><?php echo htmlspecialchars($class['class_name']); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php if (empty($classes)): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            কোন ক্লাস পাওয়া যায়নি। <a href="classes.php">প্রথমে ক্লাস যোগ করুন</a>।
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Sessions -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-calendar-alt me-2"></i>সেশন তালিকা (Session IDs)</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>ID</th>
                                        <th>সেশনের নাম</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($sessions as $session): ?>
                                    <tr>
                                        <td><code><?php echo $session['id']; ?></code></td>
                                        <td><?php echo htmlspecialchars($session['session_name']); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php if (empty($sessions)): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            কোন সেশন পাওয়া যায়নি। <a href="sessions.php">প্রথমে সেশন যোগ করুন</a>।
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- CSV Format Guide -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-file-csv me-2"></i>CSV ফরম্যাট গাইড</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>গুরুত্বপূর্ণ তথ্য:</strong>
                            <ul class="mb-0 mt-2">
                                <li>শুধুমাত্র প্রয়োজনীয় ফিল্ডগুলো পূরণ করা বাধ্যতামূলক</li>
                                <li>ডুপ্লিকেট Student ID স্বয়ংক্রিয়ভাবে স্কিপ হবে (ত্রুটি নয়)</li>
                                <li>বাংলা ও ইংরেজি উভয় ভাষায় ডেটা এন্ট্রি করা যাবে</li>
                                <li>ঐচ্ছিক ফিল্ড খালি রাখলে কোনো সমস্যা নেই</li>
                            </ul>
                        </div>

                        <h6>প্রয়োজনীয় কলাম (Mandatory Columns):</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item bg-danger bg-opacity-10"><code>student_id</code> - শিক্ষার্থী আইডি (ইউনিক)</li>
                                    <li class="list-group-item bg-danger bg-opacity-10"><code>first_name</code> - নামের প্রথম অংশ</li>
                                    <li class="list-group-item bg-danger bg-opacity-10"><code>last_name</code> - নামের শেষ অংশ</li>
                                    <li class="list-group-item bg-danger bg-opacity-10"><code>phone</code> - ফোন নম্বর</li>
                                    <li class="list-group-item bg-danger bg-opacity-10"><code>address</code> - ঠিকানা</li>
                                    <li class="list-group-item bg-danger bg-opacity-10"><code>dob</code> - জন্ম তারিখ (YYYY-MM-DD)</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item bg-danger bg-opacity-10"><code>gender</code> - লিঙ্গ (Male/Female/Other)</li>
                                    <li class="list-group-item bg-danger bg-opacity-10"><code>batch</code> - ব্যাচ</li>
                                    <li class="list-group-item bg-danger bg-opacity-10"><code>admission_date</code> - ভর্তির তারিখ (YYYY-MM-DD)</li>
                                    <li class="list-group-item bg-danger bg-opacity-10"><code>department_id</code> - বিভাগ ID (উপরের তালিকা দেখুন)</li>
                                    <li class="list-group-item bg-danger bg-opacity-10"><code>username</code> - ইউজারনেম (ইউনিক)</li>
                                    <li class="list-group-item bg-danger bg-opacity-10"><code>password</code> - পাসওয়ার্ড</li>
                                </ul>
                            </div>
                        </div>
                        
                        <h6 class="mt-4">ঐচ্ছিক কলাম (Optional Columns):</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item"><code>roll_number</code> - রোল নম্বর</li>
                                    <li class="list-group-item"><code>email</code> - ইমেইল</li>
                                    <li class="list-group-item"><code>class_id</code> - ক্লাস ID</li>
                                    <li class="list-group-item"><code>session_id</code> - সেশন ID</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item"><code>guardian_name</code> - অভিভাবকের নাম</li>
                                    <li class="list-group-item"><code>guardian_relation</code> - সম্পর্ক</li>
                                    <li class="list-group-item"><code>guardian_phone</code> - অভিভাবকের ফোন</li>
                                    <li class="list-group-item"><code>guardian_email</code> - অভিভাবকের ইমেইল</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item"><code>father_name</code> - পিতার নাম</li>
                                    <li class="list-group-item"><code>mother_name</code> - মাতার নাম</li>
                                    <li class="list-group-item"><code>father_phone</code> - পিতার ফোন</li>
                                    <li class="list-group-item"><code>mother_phone</code> - মাতার ফোন</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>গুরুত্বপূর্ণ:</strong>
                            <ul class="mb-0 mt-2">
                                <li>CSV ফাইলে প্রথম সারিতে অবশ্যই কলামের নাম থাকতে হবে</li>
                                <li>টেমপ্লেট ডাউনলোড করে সঠিক ফরম্যাট দেখুন</li>
                                <li>UTF-8 এনকোডিং ব্যবহার করুন বাংলা টেক্সটের জন্য</li>
                                <li>Excel থেকে CSV সেভ করার সময় "CSV UTF-8" নির্বাচন করুন</li>
                            </ul>
                        </div>

                        <div class="alert alert-info mt-3">
                            <i class="fas fa-language me-2"></i>
                            <strong>ভাষা সাপোর্ট:</strong>
                            আপনি বাংলা ও ইংরেজি উভয় ভাষায় ডেটা এন্ট্রি করতে পারেন।
                            উদাহরণ: "রহিম আহমেদ" অথবা "John Doe" - দুটোই গ্রহণযোগ্য।
                        </div>
                    </div>
                </div>
                
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
