<?php
session_start();

// Modified session check - allows access without redirecting
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    $isAdmin = false;
} else {
    $isAdmin = true;
}

require_once "../includes/dbh.inc.php";

// Create uploads directory if it doesn't exist
$uploads_dir = "../uploads/committee";
if (!file_exists($uploads_dir)) {
    mkdir($uploads_dir, 0777, true);
}

// Handle add member form submission
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST["add_member"])) {
    $name = $_POST["name"];
    $position = $_POST["position"];
    $details = $_POST["details"];
    $priority = $_POST["priority"];
    
    // Handle photo upload
    $photo_path = null;
    
    if (!empty($_FILES['photo']['name'])) {
        $file_name = $_FILES['photo']['name'];
        $file_tmp = $_FILES['photo']['tmp_name'];
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
        
        // Check if file type is allowed
        $allowed_ext = array("jpg", "jpeg", "png", "gif");
        
        if (in_array($file_ext, $allowed_ext)) {
            // Generate a unique filename
            $new_file_name = uniqid() . '.' . $file_ext;
            $upload_path = $uploads_dir . '/' . $new_file_name;
            
            if (move_uploaded_file($file_tmp, $upload_path)) {
                $photo_path = "uploads/committee/" . $new_file_name;
            } else {
                echo "<script>alert('ছবি আপলোড করতে সমস্যা হয়েছে।');</script>";
            }
        } else {
            echo "<script>alert('অনুমোদিত ফাইল টাইপ: JPG, JPEG, PNG, GIF');</script>";
        }
    }
    
    // Insert member information
    $sql = "INSERT INTO committee_members (name, position, details, photo, priority) VALUES (?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ssssi", $name, $position, $details, $photo_path, $priority);
    
    if ($stmt->execute()) {
        echo "<script>alert('সদস্য সফলভাবে যোগ করা হয়েছে।');</script>";
    } else {
        echo "<script>alert('সদস্য যোগ করা যায়নি। সমস্যা: " . $stmt->error . "');</script>";
    }
    
    $stmt->close();
}

// Handle update member
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST["update_member"])) {
    $id = $_POST["member_id"];
    $name = $_POST["edit_name"];
    $position = $_POST["edit_position"];
    $details = $_POST["edit_details"];
    $priority = $_POST["edit_priority"];
    
    // Get current photo info
    $get_photo = "SELECT photo FROM committee_members WHERE id = ?";
    $stmt = $conn->prepare($get_photo);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $stmt->bind_result($current_photo);
    $stmt->fetch();
    $stmt->close();
    
    // Handle photo upload for update
    $photo_path = $current_photo;
    
    if (!empty($_FILES['edit_photo']['name'])) {
        $file_name = $_FILES['edit_photo']['name'];
        $file_tmp = $_FILES['edit_photo']['tmp_name'];
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
        
        // Check if file type is allowed
        $allowed_ext = array("jpg", "jpeg", "png", "gif");
        
        if (in_array($file_ext, $allowed_ext)) {
            // Delete old file if exists
            if (!empty($current_photo)) {
                $old_file_path = "../" . $current_photo;
                if (file_exists($old_file_path)) {
                    unlink($old_file_path);
                }
            }
            
            // Generate a unique filename
            $new_file_name = uniqid() . '.' . $file_ext;
            $upload_path = $uploads_dir . '/' . $new_file_name;
            
            if (move_uploaded_file($file_tmp, $upload_path)) {
                $photo_path = "uploads/committee/" . $new_file_name;
            } else {
                echo "<script>alert('ছবি আপডেট করতে সমস্যা হয়েছে।');</script>";
            }
        } else {
            echo "<script>alert('অনুমোদিত ফাইল টাইপ: JPG, JPEG, PNG, GIF');</script>";
        }
    }
    
    // Update member information
    $sql = "UPDATE committee_members SET name = ?, position = ?, details = ?, photo = ?, priority = ? WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ssssii", $name, $position, $details, $photo_path, $priority, $id);
    
    if ($stmt->execute()) {
        echo "<script>alert('সদস্য সফলভাবে আপডেট করা হয়েছে।');</script>";
    } else {
        echo "<script>alert('সদস্য আপডেট করা যায়নি। সমস্যা: " . $stmt->error . "');</script>";
    }
    
    $stmt->close();
}

// Handle delete member
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST["delete_member"])) {
    $id = $_POST["delete_id"];
    
    // Get photo path before deleting
    $get_photo = "SELECT photo FROM committee_members WHERE id = ?";
    $stmt = $conn->prepare($get_photo);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $stmt->bind_result($photo_path);
    $stmt->fetch();
    $stmt->close();
    
    // Delete the member
    $sql = "DELETE FROM committee_members WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    
    if ($stmt->execute()) {
        // Delete the photo file if exists
        if (!empty($photo_path)) {
            $file_path = "../" . $photo_path;
            if (file_exists($file_path)) {
                unlink($file_path);
            }
        }
        echo "<script>alert('সদস্য সফলভাবে মুছে ফেলা হয়েছে।');</script>";
    } else {
        echo "<script>alert('সদস্য মুছতে ব্যর্থ হয়েছে। সমস্যা: " . $stmt->error . "');</script>";
    }
    
    $stmt->close();
}

// Check if committee_members table exists
$sql = "SHOW TABLES LIKE 'committee_members'";
$result = $conn->query($sql);

if ($result->num_rows == 0) {
    // Create committee_members table
    $sql = "CREATE TABLE committee_members (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        position VARCHAR(255) NOT NULL,
        details TEXT,
        photo VARCHAR(255),
        priority INT(11) DEFAULT 100,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "<div class='alert alert-success'>পরিচালনা পর্ষদ টেবিল সফলভাবে তৈরি করা হয়েছে।</div>";
        
        // Add sample members
        $sql = "INSERT INTO committee_members (name, position, details, priority) VALUES 
        ('মাননীয় অধ্যক্ষ', 'সভাপতি', 'অভিজ্ঞ শিক্ষাবিদ এবং প্রশাসক, ২০১০ সাল থেকে সংস্থার সাথে যুক্ত।', 1),
        ('মাননীয় সচিব', 'সদস্য সচিব', 'অভিজ্ঞ প্রশাসক এবং শিক্ষাবিদ, ২০১২ সাল থেকে সংস্থার সাথে যুক্ত।', 2),
        ('মোঃ আব্দুল কাদের', 'সদস্য', 'বিশিষ্ট ব্যবসায়ী এবং সমাজসেবক, ২০১৪ সাল থেকে সংস্থার সাথে যুক্ত।', 3)";
        
        if ($conn->query($sql) === TRUE) {
            echo "<div class='alert alert-success'>নমুনা সদস্য যোগ করা হয়েছে।</div>";
        }
    }
}

// Get all committee members ordered by priority
$sql = "SELECT * FROM committee_members ORDER BY priority ASC";
$result = $conn->query($sql);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>পরিচালনা পর্ষদ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .action-buttons .btn {
            margin-right: 5px;
        }
        .member-photo {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 50%;
            border: 2px solid #ddd;
        }
        .member-photo-large {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 5px;
            border: 2px solid #ddd;
        }
        .default-photo {
            width: 100px;
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #e9ecef;
            color: #6c757d;
            border-radius: 50%;
            font-size: 40px;
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar placeholder - you would ideally include this from a common file -->
            <?php 
            if (file_exists('includes/sidebar.php')) {
                include 'includes/sidebar.php';
            } else {
                // Fallback if sidebar include doesn't exist
                echo '<div class="col-md-3 col-lg-2 d-none d-md-block bg-light sidebar">
                    <div class="position-sticky">
                        <h3 class="text-center my-4">অ্যাডমিন প্যানেল</h3>
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link" href="dashboard.php">
                                    <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="students.php">
                                    <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="notices.php">
                                    <i class="fas fa-bullhorn me-2"></i> নোটিশ
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link active" href="committee.php">
                                    <i class="fas fa-users me-2"></i> পরিচালনা পর্ষদ
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="../includes/logout.inc.php">
                                    <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>';
            }
            ?>
            
            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <h1 class="mb-4">পরিচালনা পর্ষদ ব্যবস্থাপনা</h1>
                
                <!-- Add Member Form -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">নতুন সদস্য যোগ করুন</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="" enctype="multipart/form-data">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">নাম</label>
                                        <input type="text" class="form-control" id="name" name="name" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="position" class="form-label">পদবি</label>
                                        <input type="text" class="form-control" id="position" name="position" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="priority" class="form-label">অগ্রাধিকার (ক্রম)</label>
                                        <input type="number" class="form-control" id="priority" name="priority" value="100" min="1" required>
                                        <div class="form-text">ছোট সংখ্যা উচ্চ অগ্রাধিকার নির্দেশ করে (১ হল সর্বোচ্চ)</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="details" class="form-label">বিবরণ</label>
                                        <textarea class="form-control" id="details" name="details" rows="4"></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="photo" class="form-label">ছবি (ঐচ্ছিক)</label>
                                        <input type="file" class="form-control" id="photo" name="photo">
                                        <div class="form-text">অনুমোদিত ফাইল: JPG, JPEG, PNG, GIF (সর্বোচ্চ 2MB)</div>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" name="add_member" class="btn btn-primary">যোগ করুন</button>
                        </form>
                    </div>
                </div>
                
                <!-- Members List -->
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">সকল সদস্য</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($result && $result->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>ক্রম</th>
                                            <th>ছবি</th>
                                            <th>নাম</th>
                                            <th>পদবি</th>
                                            <th>পদক্ষেপ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($row = $result->fetch_assoc()): ?>
                                            <tr>
                                                <td><?php echo $row["priority"]; ?></td>
                                                <td>
                                                    <?php if (!empty($row["photo"])): ?>
                                                        <img src="../<?php echo $row["photo"]; ?>" class="member-photo" alt="<?php echo htmlspecialchars($row["name"]); ?>">
                                                    <?php else: ?>
                                                        <div class="default-photo">
                                                            <i class="fas fa-user"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($row["name"]); ?></td>
                                                <td><?php echo htmlspecialchars($row["position"]); ?></td>
                                                <td class="action-buttons">
                                                    <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#viewModal<?php echo $row["id"]; ?>">
                                                        <i class="fas fa-eye"></i> দেখুন
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#editModal<?php echo $row["id"]; ?>">
                                                        <i class="fas fa-edit"></i> সম্পাদনা
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?php echo $row["id"]; ?>">
                                                        <i class="fas fa-trash"></i> মুছুন
                                                    </button>
                                                </td>
                                            </tr>
                                            
                                            <!-- View Modal -->
                                            <div class="modal fade" id="viewModal<?php echo $row["id"]; ?>" tabindex="-1" aria-labelledby="viewModalLabel<?php echo $row["id"]; ?>" aria-hidden="true">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="viewModalLabel<?php echo $row["id"]; ?>"><?php echo htmlspecialchars($row["name"]); ?></h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body text-center">
                                                            <?php if (!empty($row["photo"])): ?>
                                                                <img src="../<?php echo $row["photo"]; ?>" class="member-photo-large mb-3" alt="<?php echo htmlspecialchars($row["name"]); ?>">
                                                            <?php else: ?>
                                                                <div class="default-photo mx-auto mb-3" style="width: 150px; height: 150px; font-size: 70px;">
                                                                    <i class="fas fa-user"></i>
                                                                </div>
                                                            <?php endif; ?>
                                                            
                                                            <h4><?php echo htmlspecialchars($row["name"]); ?></h4>
                                                            <h6 class="text-muted"><?php echo htmlspecialchars($row["position"]); ?></h6>
                                                            
                                                            <?php if (!empty($row["details"])): ?>
                                                                <div class="mt-3 text-start">
                                                                    <p><?php echo nl2br(htmlspecialchars($row["details"])); ?></p>
                                                                </div>
                                                            <?php endif; ?>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বন্ধ করুন</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- Edit Modal -->
                                            <div class="modal fade" id="editModal<?php echo $row["id"]; ?>" tabindex="-1" aria-labelledby="editModalLabel<?php echo $row["id"]; ?>" aria-hidden="true">
                                                <div class="modal-dialog modal-lg">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="editModalLabel<?php echo $row["id"]; ?>">সদস্য সম্পাদনা করুন</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <form method="post" action="" enctype="multipart/form-data">
                                                                <input type="hidden" name="member_id" value="<?php echo $row["id"]; ?>">
                                                                <div class="row">
                                                                    <div class="col-md-6">
                                                                        <div class="mb-3">
                                                                            <label for="edit_name<?php echo $row["id"]; ?>" class="form-label">নাম</label>
                                                                            <input type="text" class="form-control" id="edit_name<?php echo $row["id"]; ?>" name="edit_name" value="<?php echo htmlspecialchars($row["name"]); ?>" required>
                                                                        </div>
                                                                        <div class="mb-3">
                                                                            <label for="edit_position<?php echo $row["id"]; ?>" class="form-label">পদবি</label>
                                                                            <input type="text" class="form-control" id="edit_position<?php echo $row["id"]; ?>" name="edit_position" value="<?php echo htmlspecialchars($row["position"]); ?>" required>
                                                                        </div>
                                                                        <div class="mb-3">
                                                                            <label for="edit_priority<?php echo $row["id"]; ?>" class="form-label">অগ্রাধিকার (ক্রম)</label>
                                                                            <input type="number" class="form-control" id="edit_priority<?php echo $row["id"]; ?>" name="edit_priority" value="<?php echo $row["priority"]; ?>" min="1" required>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <div class="mb-3">
                                                                            <label for="edit_details<?php echo $row["id"]; ?>" class="form-label">বিবরণ</label>
                                                                            <textarea class="form-control" id="edit_details<?php echo $row["id"]; ?>" name="edit_details" rows="4"><?php echo htmlspecialchars($row["details"]); ?></textarea>
                                                                        </div>
                                                                        
                                                                        <div class="mb-3">
                                                                            <label for="edit_photo<?php echo $row["id"]; ?>" class="form-label">ছবি পরিবর্তন করুন (ঐচ্ছিক)</label>
                                                                            <input type="file" class="form-control" id="edit_photo<?php echo $row["id"]; ?>" name="edit_photo">
                                                                            
                                                                            <?php if (!empty($row["photo"])): ?>
                                                                                <div class="mt-2">
                                                                                    <span class="badge bg-info">বর্তমান ছবি:</span>
                                                                                    <img src="../<?php echo $row["photo"]; ?>" alt="Current Photo" class="mt-2" style="max-width: 100px; max-height: 100px;">
                                                                                </div>
                                                                            <?php endif; ?>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <button type="submit" name="update_member" class="btn btn-primary">আপডেট করুন</button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- Delete Modal -->
                                            <div class="modal fade" id="deleteModal<?php echo $row["id"]; ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?php echo $row["id"]; ?>" aria-hidden="true">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="deleteModalLabel<?php echo $row["id"]; ?>">সদস্য মুছুন</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <p>আপনি কি নিশ্চিত যে আপনি এই সদস্যকে মুছতে চান?</p>
                                                            <p><strong>নাম:</strong> <?php echo htmlspecialchars($row["name"]); ?></p>
                                                            <p><strong>পদবি:</strong> <?php echo htmlspecialchars($row["position"]); ?></p>
                                                            <?php if (!empty($row["photo"])): ?>
                                                                <p class="text-danger"><strong>সতর্কতা:</strong> এর সাথে সংযুক্ত ছবিও মুছে যাবে।</p>
                                                            <?php endif; ?>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <form method="post" action="">
                                                                <input type="hidden" name="delete_id" value="<?php echo $row["id"]; ?>">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল করুন</button>
                                                                <button type="submit" name="delete_member" class="btn btn-danger">মুছুন</button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">কোন সদস্য পাওয়া যায়নি।</div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 