<?php
require_once 'includes/dbh.inc.php';

// Check if session_id column exists in exams table
$result = $conn->query("SHOW COLUMNS FROM exams LIKE 'session_id'");
if ($result->num_rows == 0) {
    // Add session_id column to exams table
    $alterQuery = "ALTER TABLE exams ADD COLUMN session_id INT(11) NULL AFTER department_id";
    if ($conn->query($alterQuery)) {
        echo "session_id column added to exams table successfully!";
    } else {
        echo "Error adding session_id column: " . $conn->error;
    }
} else {
    echo "session_id column already exists in exams table.";
}
?>
