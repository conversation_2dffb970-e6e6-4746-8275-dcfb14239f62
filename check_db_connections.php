<?php
echo "<h1>Database Connection Check</h1>";

// Check direct connection to zfaw database
echo "<h2>Direct Connection Test:</h2>";
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "zfaw";

$conn1 = new mysqli($servername, $username, $password, $dbname);
if ($conn1->connect_error) {
    echo "<p style='color:red'>Direct connection to zfaw failed: " . $conn1->connect_error . "</p>";
} else {
    echo "<p style='color:green'>Direct connection to zfaw successful!</p>";
    
    // Check if users table exists
    $result = $conn1->query("SHOW TABLES LIKE 'users'");
    if ($result->num_rows > 0) {
        echo "<p style='color:green'>users table exists in zfaw database</p>";
    } else {
        echo "<p style='color:red'>users table does NOT exist in zfaw database</p>";
    }
    $conn1->close();
}

// Check connection through dbh.inc.php
echo "<h2>Connection through dbh.inc.php:</h2>";
require_once 'includes/dbh.inc.php';
if (isset($conn) && !$conn->connect_error) {
    echo "<p style='color:green'>Connection through dbh.inc.php successful!</p>";
    
    // Check which database is selected
    $result = $conn->query("SELECT DATABASE()");
    $row = $result->fetch_row();
    echo "<p>Current database selected: <strong>" . $row[0] . "</strong></p>";
    
    // Check if users table exists
    $result = $conn->query("SHOW TABLES LIKE 'users'");
    if ($result->num_rows > 0) {
        echo "<p style='color:green'>users table exists in current database</p>";
    } else {
        echo "<p style='color:red'>users table does NOT exist in current database</p>";
    }
} else {
    echo "<p style='color:red'>Connection through dbh.inc.php failed</p>";
}

// Check connection through config.php if it exists
echo "<h2>Connection through config.php:</h2>";
if (file_exists('includes/config.php')) {
    require_once 'includes/config.php';
    if (isset($conn) && !$conn->connect_error) {
        echo "<p style='color:green'>Connection through config.php successful!</p>";
        
        // Check which database is selected
        $result = $conn->query("SELECT DATABASE()");
        $row = $result->fetch_row();
        echo "<p>Current database selected: <strong>" . $row[0] . "</strong></p>";
    } else {
        echo "<p style='color:red'>Connection through config.php failed</p>";
    }
} else {
    echo "<p style='color:red'>config.php file not found</p>";
}

// Check login.php to see which database it's using
echo "<h2>Login.php Analysis:</h2>";
$login_content = file_get_contents('login.php');
echo "<p>Login.php includes these files:</p><ul>";
if (preg_match_all('/require(_once)?\s+[\'"]([^\'"]+)[\'"]/', $login_content, $matches)) {
    foreach ($matches[2] as $included_file) {
        echo "<li>" . htmlspecialchars($included_file) . "</li>";
    }
}
echo "</ul>";

echo "<p><a href='database_setup.php'>Run Database Setup Again</a></p>";
?>