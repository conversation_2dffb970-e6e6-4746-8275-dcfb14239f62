<?php
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Create classes table if it doesn't exist
$classesTableQuery = "CREATE TABLE IF NOT EXISTS classes (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    class_name VARCHAR(100) NOT NULL,
    department_id INT(11) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
$conn->query($classesTableQuery);

// Check if department_id column exists in classes table
$checkDeptIdColumnQuery = "SHOW COLUMNS FROM classes LIKE 'department_id'";
$deptIdColumnResult = $conn->query($checkDeptIdColumnQuery);
if ($deptIdColumnResult->num_rows == 0) {
    $addDeptIdColumnQuery = "ALTER TABLE classes ADD COLUMN department_id INT(11) NULL";
    $conn->query($addDeptIdColumnQuery);
}

// Create sessions table if it doesn't exist
$sessionsTableQuery = "CREATE TABLE IF NOT EXISTS sessions (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    session_name VARCHAR(50) NOT NULL UNIQUE,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
$conn->query($sessionsTableQuery);

// Check if session_id column exists in students table
$checkSessionIdColumnQuery = "SHOW COLUMNS FROM students LIKE 'session_id'";
$sessionIdColumnResult = $conn->query($checkSessionIdColumnQuery);
if ($sessionIdColumnResult->num_rows == 0) {
    $addSessionIdColumnQuery = "ALTER TABLE students ADD COLUMN session_id INT(11) NULL";
    $conn->query($addSessionIdColumnQuery);
}

// Check if class_id column exists in students table
$checkClassIdColumnQuery = "SHOW COLUMNS FROM students LIKE 'class_id'";
$classIdColumnResult = $conn->query($checkClassIdColumnQuery);
if ($classIdColumnResult->num_rows == 0) {
    $addClassIdColumnQuery = "ALTER TABLE students ADD COLUMN class_id INT(11) NULL";
    $conn->query($addClassIdColumnQuery);
}

// Get all departments
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Handle success and error messages
$success_msg = '';
$error_msg = '';

// Check if we're editing a class
$editClass = null;
if (isset($_GET['edit'])) {
    $classId = $conn->real_escape_string($_GET['edit']);
    $editQuery = "SELECT * FROM classes WHERE id = ?";
    $stmt = $conn->prepare($editQuery);
    $stmt->bind_param("i", $classId);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result->num_rows > 0) {
        $editClass = $result->fetch_assoc();
    } else {
        $error_msg = "ক্লাস খুঁজে পাওয়া যায়নি";
    }
}

// Handle class update
if (isset($_POST['update_class'])) {
    $classId = $conn->real_escape_string($_POST['class_id']);
    $className = $conn->real_escape_string($_POST['class_name']);
    
    if (empty($className)) {
        $error_msg = "ক্লাসের নাম আবশ্যক";
    } else {
        // Check if class name already exists for other classes
        $checkQuery = "SELECT * FROM classes WHERE class_name = ? AND id != ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param("si", $className, $classId);
        
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $error_msg = "এই ক্লাস নামটি ইতিমধ্যে বিদ্যমান";
        } else {
            $updateQuery = "UPDATE classes SET class_name = ? WHERE id = ?";
            $stmt = $conn->prepare($updateQuery);
            $stmt->bind_param("si", $className, $classId);
            
            if ($stmt->execute()) {
                $success_msg = "ক্লাস সফলভাবে আপডেট করা হয়েছে";
                // Redirect to remove the edit parameter
                header("Location: classes.php?success=updated");
                exit();
            } else {
                // Check if error is due to duplicate entry (unique constraint)
                if (strpos($conn->error, 'Duplicate entry') !== false) {
                    $error_msg = "এই ক্লাস নামটি ইতিমধ্যে বিদ্যমান (Database Constraint)";
                } else {
                    $error_msg = "ক্লাস আপডেট করতে সমস্যা: " . $conn->error;
                }
            }
        }
    }
}

// Handle class addition
if (isset($_POST['add_class'])) {
    $className = $conn->real_escape_string($_POST['class_name']);
    
    if (empty($className)) {
        $error_msg = "ক্লাসের নাম আবশ্যক";
    } else {
        // Check if class already exists
        $checkQuery = "SELECT * FROM classes WHERE class_name = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param("s", $className);
        
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $error_msg = "এই ক্লাস ইতিমধ্যে বিদ্যমান";
        } else {
            $insertQuery = "INSERT INTO classes (class_name) VALUES (?)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param("s", $className);

            if ($stmt->execute()) {
                $success_msg = "ক্লাস সফলভাবে যোগ করা হয়েছে";
            } else {
                // Check if error is due to duplicate entry (unique constraint)
                if (strpos($conn->error, 'Duplicate entry') !== false) {
                    $error_msg = "এই ক্লাস ইতিমধ্যে বিদ্যমান (Database Constraint)";
                } else {
                    $error_msg = "ক্লাস যোগ করতে সমস্যা: " . $conn->error;
                }
            }
        }
    }
}

// Handle class deletion
if (isset($_GET['delete'])) {
    $classId = $conn->real_escape_string($_GET['delete']);
    
    // Check if any students are in this class
    $checkStudentsQuery = "SELECT COUNT(*) as count FROM students WHERE class_id = '$classId'";
    $studentsResult = $conn->query($checkStudentsQuery);
    $studentCount = $studentsResult->fetch_assoc()['count'];
    
    if ($studentCount > 0) {
        $error_msg = "ক্লাস মুছা যাবে না কারণ $studentCount জন শিক্ষার্থী এই ক্লাসে নিযুক্ত আছে";
    } else {
        $deleteQuery = "DELETE FROM classes WHERE id = '$classId'";
        
        if ($conn->query($deleteQuery)) {
            $success_msg = "ক্লাস সফলভাবে মুছে ফেলা হয়েছে";
        } else {
            $error_msg = "ক্লাস মুছতে সমস্যা: " . $conn->error;
        }
    }
}

// Check if we have a success message from redirect
if (isset($_GET['success']) && $_GET['success'] === 'updated') {
    $success_msg = "ক্লাস সফলভাবে আপডেট করা হয়েছে";
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>ক্লাস ব্যবস্থাপনা | অ্যাডমিন ড্যাশবোর্ড</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar {
            height: 100vh;
            background-color: #343a40;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            display: block;
        }
        .sidebar a:hover {
            background-color: #495057;
        }
        .active {
            background-color: #0d6efd;
        }
        .content {
            margin-left: 220px;
            padding: 20px;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar">
                <h2 class="text-white text-center mb-4">অ্যাডমিন প্যানেল</h2>
                <a href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড</a>
                <a href="students.php"><i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী</a>
                <a href="sessions.php"><i class="fas fa-calendar-alt me-2"></i> সেশন</a>
                <a href="classes.php" class="active"><i class="fas fa-chalkboard me-2"></i> ক্লাস</a>
                <a href="departments.php"><i class="fas fa-building me-2"></i> বিভাগ</a>
                <a href="subjects.php"><i class="fas fa-book-open me-2"></i> বিষয়</a>
                <a href="../includes/logout.inc.php"><i class="fas fa-sign-out-alt me-2"></i> লগআউট</a>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-10 content">
                <h1 class="mb-4">ক্লাস ব্যবস্থাপনা</h1>
                
                <!-- Success and Error Messages -->
                <?php if (!empty($success_msg)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($error_msg)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <div class="row">
                    <!-- Add/Edit Class Form -->
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0"><?php echo isset($editClass) ? 'ক্লাস সম্পাদনা করুন' : 'নতুন ক্লাস যোগ করুন'; ?></h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="<?php echo isset($editClass) ? 'classes.php?edit=' . $editClass['id'] : 'classes.php'; ?>">
                                    <?php if (isset($editClass)): ?>
                                        <input type="hidden" name="class_id" value="<?php echo $editClass['id']; ?>">
                                    <?php endif; ?>
                                    
                                    <div class="mb-3">
                                        <label for="class_name" class="form-label">ক্লাসের নাম*</label>
                                        <input type="text" class="form-control" id="class_name" name="class_name" 
                                               value="<?php echo isset($editClass) ? htmlspecialchars($editClass['class_name']) : ''; ?>" required>
                                        <small class="form-text text-muted">উদাহরণ: ক্লাস ১, প্রথম বর্ষ, ১০ম শ্রেণি, ইত্যাদি।</small>
                                    </div>
                                    
                                    <?php if (isset($editClass)): ?>
                                        <button type="submit" name="update_class" class="btn btn-primary">আপডেট করুন</button>
                                        <a href="classes.php" class="btn btn-secondary">বাতিল করুন</a>
                                    <?php else: ?>
                                        <button type="submit" name="add_class" class="btn btn-primary">ক্লাস যোগ করুন</button>
                                    <?php endif; ?>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Classes List -->
                    <div class="col-md-8 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">ক্লাস তালিকা</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>ক্লাসের নাম</th>
                                                <th>শিক্ষার্থী সংখ্যা</th>
                                                <th>অ্যাকশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            // Get student counts for each class
                                            $studentCountQuery = "SELECT class_id, COUNT(*) as count FROM students GROUP BY class_id";
                                            $studentCounts = $conn->query($studentCountQuery);
                                            $classCounts = [];
                                            
                                            if ($studentCounts && $studentCounts->num_rows > 0) {
                                                while ($row = $studentCounts->fetch_assoc()) {
                                                    $classCounts[$row['class_id']] = $row['count'];
                                                }
                                            }
                                            
                                            // Get all classes with department names (prevent duplicates)
                                            $classesQuery = "SELECT DISTINCT c.id, c.class_name, c.department_id FROM classes c ORDER BY c.class_name";
                                            $classes = $conn->query($classesQuery);

                                            if ($classes && $classes->num_rows > 0):
                                                $seenClasses = [];
                                                while ($class = $classes->fetch_assoc()):
                                                    // Prevent duplicate class names in display
                                                    if (!in_array($class['class_name'], $seenClasses)):
                                                        $seenClasses[] = $class['class_name'];
                                                        $studentCount = $classCounts[$class['id']] ?? 0;
                                            ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($class['class_name']); ?></td>
                                                    <td><?php echo $studentCount; ?></td>
                                                    <td>
                                                        <a href="classes.php?edit=<?php echo $class['id']; ?>" class="btn btn-sm btn-warning">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="classes.php?delete=<?php echo $class['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই ক্লাসটি মুছে ফেলতে চান?')">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php
                                                    endif;
                                                endwhile;
                                            else:
                                            ?>
                                                <tr>
                                                    <td colspan="3" class="text-center">কোন ক্লাস পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 