<?php
// This is a temporary direct access file for notices
require_once "includes/dbh.inc.php";

echo "<h1>All Notices</h1>";

$sql = "SELECT * FROM notices ORDER BY date DESC";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    echo "<table border='1' cellpadding='10' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Title</th><th>Date</th><th>Content</th></tr>";
    
    while($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row["title"]) . "</td>";
        echo "<td>" . $row["date"] . "</td>";
        echo "<td>" . nl2br(htmlspecialchars($row["content"])) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>No notices found</p>";
}

echo "<p><a href='index.php'>Back to Home</a></p>";
?>