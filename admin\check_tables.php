<?php
require_once '../includes/dbh.inc.php';

echo "<h2>Database Table Check</h2>";

// Tables to check
$tables = [
    'departments',
    'classes',
    'sessions',
    'students',
    'subjects',
    'subject_departments',
    'student_subjects'
];

foreach ($tables as $table) {
    $query = "SHOW TABLES LIKE '$table'";
    $result = $conn->query($query);
    
    if ($result && $result->num_rows > 0) {
        echo "<p>✅ Table '$table' exists</p>";
        
        // Check row count
        $countQuery = "SELECT COUNT(*) as count FROM $table";
        $countResult = $conn->query($countQuery);
        if ($countResult && $countRow = $countResult->fetch_assoc()) {
            echo "<p>   - Row count: " . $countRow['count'] . "</p>";
        }
    } else {
        echo "<p>❌ Table '$table' does not exist</p>";
    }
}
?>
