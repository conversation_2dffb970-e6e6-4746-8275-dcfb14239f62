<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Get student ID from request
$studentId = isset($_GET['student_id']) ? intval($_GET['student_id']) : 0;

// Basic validation
if ($studentId <= 0) {
    header("Location: fee_management.php");
    exit();
}

// Get student information
$studentQuery = "SELECT s.*, c.class_name, d.department_name, ss.session_name 
                FROM students s 
                LEFT JOIN classes c ON s.class_id = c.id 
                LEFT JOIN departments d ON s.department_id = d.id 
                LEFT JOIN sessions ss ON s.session_id = ss.id 
                WHERE s.id = ?";
$stmt = $conn->prepare($studentQuery);
$stmt->bind_param("i", $studentId);
$stmt->execute();
$studentResult = $stmt->get_result();

if ($studentResult->num_rows === 0) {
    header("Location: fee_management.php");
    exit();
}

$student = $studentResult->fetch_assoc();

// Get all fees with payment details
$feesQuery = "SELECT f.*, 
              COALESCE(SUM(fp.amount), 0) as total_payments,
              COUNT(fp.id) as payment_count,
              GROUP_CONCAT(
                  CONCAT(fp.payment_date, '|', fp.amount, '|', fp.payment_method, '|', fp.receipt_no, '|', COALESCE(fp.notes, ''))
                  ORDER BY fp.payment_date DESC
                  SEPARATOR ';;'
              ) as payment_details
              FROM fees f
              LEFT JOIN fee_payments fp ON f.id = fp.fee_id
              WHERE f.student_id = ?
              GROUP BY f.id
              ORDER BY f.due_date ASC";

$stmt = $conn->prepare($feesQuery);
$stmt->bind_param("i", $studentId);
$stmt->execute();
$feesResult = $stmt->get_result();

$fees = [];
$totalAmount = 0;
$totalPaid = 0;
$totalDue = 0;
$allPayments = [];

while ($fee = $feesResult->fetch_assoc()) {
    $amount = floatval($fee['amount']);
    $paid = floatval($fee['total_payments']);
    
    // Data validation: paid should not exceed amount
    if ($paid > $amount) {
        $paid = $amount;
    }
    
    $due = $amount - $paid;
    
    $fee['calculated_paid'] = $paid;
    $fee['calculated_due'] = $due;
    
    // Determine status
    if ($due <= 0) {
        $fee['status'] = 'paid';
        $fee['status_text'] = 'পরিশোধিত';
        $fee['status_class'] = 'success';
    } elseif ($paid > 0) {
        $fee['status'] = 'partial';
        $fee['status_text'] = 'আংশিক';
        $fee['status_class'] = 'warning';
    } else {
        $fee['status'] = 'due';
        $fee['status_text'] = 'বকেয়া';
        $fee['status_class'] = 'danger';
    }
    
    // Parse payment details
    $payments = [];
    if (!empty($fee['payment_details'])) {
        $paymentEntries = explode(';;', $fee['payment_details']);
        foreach ($paymentEntries as $entry) {
            if (!empty($entry)) {
                $parts = explode('|', $entry);
                if (count($parts) >= 4) {
                    $payment = [
                        'date' => $parts[0],
                        'amount' => floatval($parts[1]),
                        'method' => $parts[2],
                        'receipt_no' => $parts[3],
                        'notes' => $parts[4] ?? '',
                        'fee_type' => $fee['fee_type'],
                        'fee_id' => $fee['id']
                    ];
                    $payments[] = $payment;
                    $allPayments[] = $payment;
                }
            }
        }
    }
    $fee['payments'] = $payments;
    
    $totalAmount += $amount;
    $totalPaid += $paid;
    $totalDue += $due;
    
    $fees[] = $fee;
}

// Sort all payments by date (newest first)
usort($allPayments, function($a, $b) {
    return strtotime($b['date']) - strtotime($a['date']);
});
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ফি সারসংক্ষেপ - <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: "Hind Siliguri", sans-serif;
        }
        
        .student-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .summary-card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .summary-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .fee-card {
            border-radius: 10px;
            border: 1px solid #e0e0e0;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .fee-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .payment-badge {
            border-radius: 20px;
            padding: 0.3rem 0.8rem;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 1.5rem;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -23px;
            top: 8px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #007bff;
            border: 3px solid white;
            box-shadow: 0 0 0 2px #007bff;
        }
        
        @media print {
            .no-print { display: none !important; }
            .summary-card { box-shadow: none; border: 1px solid #ddd; }
            body { font-size: 12px; }
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <!-- Header -->
        <div class="row no-print">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4 bg-white p-3 rounded shadow-sm">
                    <div>
                        <h2 class="mb-0">
                            <i class="fas fa-chart-line text-primary me-2"></i>
                            ফি সারসংক্ষেপ
                        </h2>
                        <small class="text-muted">ছাত্রের সম্পূর্ণ ফি ও পেমেন্ট ইতিহাস</small>
                    </div>
                    <div>
                        <a href="student_dues_summary.php" class="btn btn-secondary me-2">
                            <i class="fas fa-arrow-left me-1"></i> বকেয়া তালিকা
                        </a>
                        <a href="student_payment_form.php?student_id=<?php echo $studentId; ?>" class="btn btn-success me-2">
                            <i class="fas fa-credit-card me-1"></i> পেমেন্ট করুন
                        </a>
                        <a href="student_payment_receipt.php?student_id=<?php echo $studentId; ?>" class="btn btn-info me-2">
                            <i class="fas fa-receipt me-1"></i> রসিদ দেখুন
                        </a>
                        <button onclick="window.print()" class="btn btn-primary">
                            <i class="fas fa-print me-1"></i> প্রিন্ট করুন
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Student Information -->
        <div class="student-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h3 class="mb-2">
                        <i class="fas fa-user-graduate me-2"></i>
                        <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>
                    </h3>
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-1"><i class="fas fa-id-card me-2"></i> রোল: <?php echo htmlspecialchars($student['student_id']); ?></p>
                            <p class="mb-1"><i class="fas fa-graduation-cap me-2"></i> শ্রেণী: <?php echo htmlspecialchars($student['class_name'] ?? 'N/A'); ?></p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-1"><i class="fas fa-building me-2"></i> বিভাগ: <?php echo htmlspecialchars($student['department_name'] ?? 'N/A'); ?></p>
                            <p class="mb-1"><i class="fas fa-calendar me-2"></i> সেশন: <?php echo htmlspecialchars($student['session_name'] ?? 'N/A'); ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <div class="bg-white bg-opacity-20 rounded p-3">
                        <h4 class="mb-1">মোট বকেয়া</h4>
                        <h2 class="mb-0">৳ <?php echo number_format($totalDue, 2); ?></h2>
                        <small><?php echo count($fees); ?> টি ফি</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card summary-card text-center">
                    <div class="card-body">
                        <i class="fas fa-money-bill-wave fa-2x text-primary mb-2"></i>
                        <h5>মোট ফি</h5>
                        <h3 class="text-primary">৳ <?php echo number_format($totalAmount, 2); ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card summary-card text-center">
                    <div class="card-body">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <h5>পরিশোধিত</h5>
                        <h3 class="text-success">৳ <?php echo number_format($totalPaid, 2); ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card summary-card text-center">
                    <div class="card-body">
                        <i class="fas fa-exclamation-circle fa-2x text-danger mb-2"></i>
                        <h5>বকেয়া</h5>
                        <h3 class="text-danger">৳ <?php echo number_format($totalDue, 2); ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card summary-card text-center">
                    <div class="card-body">
                        <i class="fas fa-receipt fa-2x text-info mb-2"></i>
                        <h5>মোট পেমেন্ট</h5>
                        <h3 class="text-info"><?php echo count($allPayments); ?> টি</h3>
                    </div>
                </div>
            </div>
        </div>
