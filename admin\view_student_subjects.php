<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get student ID from URL
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: subject_categories.php");
    exit();
}

$studentId = $_GET['id'];

// Get student data with department information
$studentQuery = "SELECT s.*, d.department_name
                 FROM students s
                 LEFT JOIN departments d ON s.department_id = d.id
                 WHERE s.student_id = ?";
$stmt = $conn->prepare($studentQuery);
$stmt->bind_param("s", $studentId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header("Location: subject_categories.php");
    exit();
}

$student = $result->fetch_assoc();

// Get student's database ID first
$studentDbIdQuery = "SELECT id FROM students WHERE student_id = ?";
$stmt = $conn->prepare($studentDbIdQuery);
$stmt->bind_param("s", $studentId);
$stmt->execute();
$studentDbIdResult = $stmt->get_result();
$studentDbId = $studentDbIdResult->fetch_assoc()['id'];

// Get student's selected subjects with category information
$selectedSubjectsQuery = "SELECT DISTINCT ss.id, ss.category, s.subject_name, s.subject_code
                         FROM student_subjects ss
                         JOIN subjects s ON ss.subject_id = s.id
                         WHERE ss.student_id = ?
                         ORDER BY FIELD(ss.category, 'required', 'optional', 'fourth'), s.subject_name";
$stmt = $conn->prepare($selectedSubjectsQuery);
$stmt->bind_param("i", $studentDbId);
$stmt->execute();
$selectedSubjects = $stmt->get_result();

// Count subjects by category
$requiredCount = 0;
$optionalCount = 0;
$fourthCount = 0;
$totalSubjects = 0;

if ($selectedSubjects && $selectedSubjects->num_rows > 0) {
    while ($subject = $selectedSubjects->fetch_assoc()) {
        $totalSubjects++;
        if ($subject['category'] == 'required') {
            $requiredCount++;
        } elseif ($subject['category'] == 'optional') {
            $optionalCount++;
        } elseif ($subject['category'] == 'fourth') {
            $fourthCount++;
        }
    }
    // Reset result pointer
    $selectedSubjects->data_seek(0);
}

// Get session information for the student's selections
$sessionQuery = "SELECT DISTINCT s.session_name
                FROM student_subjects ss
                JOIN sessions s ON ss.session_id = s.id
                WHERE ss.student_id = ?";
$stmt = $conn->prepare($sessionQuery);
$stmt->bind_param("i", $studentDbId);
$stmt->execute();
$sessionResult = $stmt->get_result();
$session = $sessionResult->fetch_assoc();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>শিক্ষার্থী বিষয় নির্বাচন - কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .subject-card {
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }
        .subject-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .student-info {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subject_categories.php">
                            <i class="fas fa-tags me-2"></i> বিষয় ক্যাটাগরি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="student_subject_selection.php">
                            <i class="fas fa-list-check me-2"></i> বিষয় নির্বাচন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>শিক্ষার্থী বিষয় নির্বাচন বিবরণ</h2>
                        <p class="text-muted">শিক্ষার্থী দ্বারা নির্বাচিত বিষয়সমূহের বিস্তারিত তথ্য</p>
                    </div>
                    <div class="col-auto">
                        <a href="student_subject_selection.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>শিক্ষার্থী তালিকায় ফিরে যান
                        </a>
                    </div>
                </div>

                <!-- Student Information -->
                <div class="student-info">
                    <div class="row">
                        <div class="col-md-6">
                            <h4><?php echo $student['first_name'] . ' ' . $student['last_name']; ?></h4>
                            <p><strong>শিক্ষার্থী আইডি:</strong> <?php echo $student['student_id']; ?></p>
                            <p><strong>ইমেইল:</strong> <?php echo $student['email']; ?></p>
                            <p><strong>ফোন:</strong> <?php echo $student['phone']; ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>বিভাগ:</strong> <?php echo $student['department_name']; ?></p>
                            <p><strong>সেশন:</strong> <?php echo $session['session_name'] ?? 'N/A'; ?></p>
                            <p><strong>মোট নির্বাচিত বিষয়:</strong> <span class="badge <?php echo ($totalSubjects == 7) ? 'bg-success' : 'bg-danger'; ?>"><?php echo $totalSubjects; ?>/7</span></p>
                            <p>
                                <strong>বিষয় বিভাজন:</strong>
                                <span class="badge bg-primary">আবশ্যিক: <?php echo $requiredCount; ?></span>
                                <span class="badge bg-info">ঐচ্ছিক: <?php echo $optionalCount; ?></span>
                                <span class="badge bg-warning">৪র্থ বিষয়: <?php echo $fourthCount; ?></span>
                            </p>
                        </div>
                    </div>
                </div>

                <?php if ($totalSubjects == 0): ?>
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>এই শিক্ষার্থী এখনও কোন বিষয় নির্বাচন করেনি।
                    </div>
                <?php else: ?>
                    <!-- Display all subjects in order: Required, Optional, Fourth -->
                    <?php
                    // Create an array to store subjects by category
                    $subjectsByCategory = [
                        'required' => [],
                        'optional' => [],
                        'fourth' => []
                    ];

                    // Group subjects by category
                    if ($selectedSubjects && $selectedSubjects->num_rows > 0) {
                        $selectedSubjects->data_seek(0);
                        while ($subject = $selectedSubjects->fetch_assoc()) {
                            if (isset($subjectsByCategory[$subject['category']])) {
                                $subjectsByCategory[$subject['category']][] = $subject;
                            }
                        }
                    }

                    // Display Required Subjects
                    ?>
                    <div class="mb-4">
                        <h4 class="mb-3">আবশ্যিক বিষয়সমূহ</h4>
                        <div class="row">
                            <?php
                            if (!empty($subjectsByCategory['required'])) {
                                foreach ($subjectsByCategory['required'] as $subject) {
                            ?>
                                    <div class="col-md-4">
                                        <div class="card subject-card">
                                            <div class="card-body">
                                                <h5 class="card-title"><?php echo $subject['subject_name']; ?></h5>
                                                <h6 class="card-subtitle mb-2 text-muted"><?php echo $subject['subject_code']; ?></h6>
                                                <span class="badge bg-primary">আবশ্যিক</span>
                                            </div>
                                        </div>
                                    </div>
                            <?php
                                }
                            } else {
                                echo '<div class="col-12"><div class="alert alert-info">কোন আবশ্যিক বিষয় নির্বাচন করা হয়নি</div></div>';
                            }
                            ?>
                        </div>
                    </div>

                    <!-- Optional Subjects -->
                    <div class="mb-4">
                        <h4 class="mb-3">ঐচ্ছিক বিষয়সমূহ</h4>
                        <div class="row">
                            <?php
                            if (!empty($subjectsByCategory['optional'])) {
                                foreach ($subjectsByCategory['optional'] as $subject) {
                            ?>
                                    <div class="col-md-4">
                                        <div class="card subject-card">
                                            <div class="card-body">
                                                <h5 class="card-title"><?php echo $subject['subject_name']; ?></h5>
                                                <h6 class="card-subtitle mb-2 text-muted"><?php echo $subject['subject_code']; ?></h6>
                                                <span class="badge bg-info">ঐচ্ছিক</span>
                                            </div>
                                        </div>
                                    </div>
                            <?php
                                }
                            } else {
                                echo '<div class="col-12"><div class="alert alert-info">কোন ঐচ্ছিক বিষয় নির্বাচন করা হয়নি</div></div>';
                            }
                            ?>
                        </div>
                    </div>

                    <!-- Fourth Subjects -->
                    <div class="mb-4">
                        <h4 class="mb-3">৪র্থ বিষয়</h4>
                        <div class="row">
                            <?php
                            if (!empty($subjectsByCategory['fourth'])) {
                                foreach ($subjectsByCategory['fourth'] as $subject) {
                            ?>
                                    <div class="col-md-4">
                                        <div class="card subject-card">
                                            <div class="card-body">
                                                <h5 class="card-title"><?php echo $subject['subject_name']; ?></h5>
                                                <h6 class="card-subtitle mb-2 text-muted"><?php echo $subject['subject_code']; ?></h6>
                                                <span class="badge bg-warning">৪র্থ বিষয়</span>
                                            </div>
                                        </div>
                                    </div>
                            <?php
                                }
                            } else {
                                echo '<div class="col-12"><div class="alert alert-info">কোন ৪র্থ বিষয় নির্বাচন করা হয়নি</div></div>';
                            }
                            ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Action Buttons -->
                <div class="row mt-4 mb-4">
                    <div class="col-md-12">
                        <a href="student_subject_selection.php?id=<?php echo $student['student_id']; ?>" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>বিষয় নির্বাচন পরিবর্তন করুন
                        </a>
                        <a href="view_student.php?id=<?php echo $studentDbId; ?>" class="btn btn-info ms-2">
                            <i class="fas fa-user me-2"></i>শিক্ষার্থীর প্রোফাইল দেখুন
                        </a>
                        <a href="printable_subject_selection.php?id=<?php echo $student['student_id']; ?>" class="btn btn-secondary ms-2" target="_blank">
                            <i class="fas fa-print me-2"></i>প্রিন্ট করুন
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>