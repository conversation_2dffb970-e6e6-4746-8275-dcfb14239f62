<?php
session_start();
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Initialize variables
$payments = [];
$errorMessage = '';
$searchTerm = $_GET['search'] ?? '';

try {
    // Check if fee_payments table exists
    $tableCheck = $conn->query("SHOW TABLES LIKE 'fee_payments'");
    
    if ($tableCheck->num_rows == 0) {
        $errorMessage = 'fee_payments টেবিল পাওয়া যায়নি। প্রথমে সেটআপ চালান।';
    } else {
        // Enhanced query with proper JOIN to get student info
        if (!empty($searchTerm)) {
            $query = "SELECT fp.*, f.fee_type, s.first_name, s.last_name, s.roll_no, s.student_id, c.class_name,
                      CONCAT(s.first_name, ' ', s.last_name) as full_name
                      FROM fee_payments fp
                      LEFT JOIN fees f ON fp.fee_id = f.id
                      LEFT JOIN students s ON f.student_id = s.id
                      LEFT JOIN classes c ON s.class_id = c.id
                      WHERE (fp.receipt_no LIKE ? OR s.first_name LIKE ? OR s.last_name LIKE ? OR s.roll_no LIKE ? OR s.student_id LIKE ?)
                      ORDER BY fp.created_at DESC LIMIT 20";
            $stmt = $conn->prepare($query);
            $searchParam = "%$searchTerm%";
            $stmt->bind_param("sssss", $searchParam, $searchParam, $searchParam, $searchParam, $searchParam);
            $stmt->execute();
            $result = $stmt->get_result();
        } else {
            $query = "SELECT fp.*, f.fee_type, s.first_name, s.last_name, s.roll_no, s.student_id, c.class_name,
                      CONCAT(s.first_name, ' ', s.last_name) as full_name
                      FROM fee_payments fp
                      LEFT JOIN fees f ON fp.fee_id = f.id
                      LEFT JOIN students s ON f.student_id = s.id
                      LEFT JOIN classes c ON s.class_id = c.id
                      ORDER BY fp.created_at DESC LIMIT 20";
            $result = $conn->query($query);
        }

        if ($result) {
            $payments = $result->fetch_all(MYSQLI_ASSOC);

            // Set default values for missing data
            foreach ($payments as &$payment) {
                $payment['student_name'] = $payment['full_name'] ?? 'Unknown Student';
                $payment['fee_type'] = $payment['fee_type'] ?? 'Unknown Fee';
                $payment['roll_no'] = $payment['roll_no'] ?? $payment['student_id'] ?? 'N/A';
                $payment['class_name'] = $payment['class_name'] ?? 'N/A';
            }
        }
    }
} catch (Exception $e) {
    $errorMessage = 'ডাটাবেস এরর: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পেমেন্ট তালিকা</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', Arial, sans-serif;
            background-color: #f8f9fa;
        }
        .payment-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 20px;
            border-left: 4px solid #007bff;
        }
        .receipt-badge {
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .amount-display {
            font-size: 24px;
            font-weight: bold;
            color: #28a745;
        }
        .payment-method-badge {
            background: #17a2b8;
            color: white;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-list me-2"></i>পেমেন্ট তালিকা</h2>
                    <div>
                        <a href="fix_database.php" class="btn btn-warning me-2">
                            <i class="fas fa-wrench me-2"></i>ডাটাবেস ঠিক করুন
                        </a>
                        <a href="fee_management.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>ফিরে যান
                        </a>
                    </div>
                </div>

                <?php if (!empty($errorMessage)): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?= htmlspecialchars($errorMessage) ?>
                    <br><small><a href="fix_database.php" class="alert-link">ডাটাবেস ঠিক করুন</a></small>
                </div>
                <?php endif; ?>

                <!-- Search -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-8">
                                <input type="text" class="form-control" name="search"
                                       value="<?= htmlspecialchars($searchTerm) ?>"
                                       placeholder="নাম, রোল নং, রিসিপ্ট নং দিয়ে খুঁজুন...">
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>খুঁজুন
                                </button>
                                <a href="payment_list.php" class="btn btn-outline-secondary ms-1">
                                    <i class="fas fa-refresh"></i>
                                </a>
                                <a href="?debug=1" class="btn btn-outline-info ms-1">
                                    <i class="fas fa-bug"></i>
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Debug Info -->
                <?php if (isset($_GET['debug'])): ?>
                <div class="alert alert-info">
                    <h6>ডিবাগ তথ্য:</h6>
                    <p><strong>মোট পেমেন্ট:</strong> <?= count($payments) ?></p>
                    <?php if (!empty($payments)): ?>
                    <p><strong>প্রথম পেমেন্টের ডেটা:</strong></p>
                    <pre><?= print_r($payments[0], true) ?></pre>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <!-- Payments -->
                <div class="row">
                    <?php if (empty($payments)): ?>
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-receipt fa-3x mb-3 text-muted"></i>
                            <h4>কোন পেমেন্ট পাওয়া যায়নি</h4>
                            <?php if (empty($errorMessage)): ?>
                            <p>প্রথমে <a href="fix_database.php">ডাটাবেস ঠিক করুন</a> এবং নমুনা ডেটা যোগ করুন</p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php else: ?>
                    <?php foreach ($payments as $payment): ?>
                    <div class="col-md-6 col-lg-4">
                        <div class="payment-card">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span class="receipt-badge"><?= htmlspecialchars($payment['receipt_no'] ?? 'N/A') ?></span>
                                <small class="text-muted"><?= date('d/m/Y', strtotime($payment['payment_date'])) ?></small>
                            </div>

                            <div class="mb-2">
                                <h6 class="mb-1">
                                    <i class="fas fa-user me-2"></i><?= htmlspecialchars($payment['student_name']) ?>
                                </h6>
                                <?php if (!empty($payment['roll_no']) && $payment['roll_no'] !== 'N/A'): ?>
                                <small class="text-muted">রোল: <?= htmlspecialchars($payment['roll_no']) ?></small>
                                <?php endif; ?>
                                <?php if (!empty($payment['class_name']) && $payment['class_name'] !== 'N/A'): ?>
                                <small class="text-muted"> | ক্লাস: <?= htmlspecialchars($payment['class_name']) ?></small>
                                <?php endif; ?>
                            </div>

                            <div class="mb-2">
                                <strong>ফি ধরন:</strong>
                                <span class="text-info"><?= htmlspecialchars($payment['fee_type']) ?></span>
                            </div>

                            <div class="mb-2">
                                <strong>পরিমাণ:</strong>
                                <span class="amount-display">৳<?= number_format($payment['amount'], 2) ?></span>
                            </div>

                            <div class="mb-2">
                                <strong>পেমেন্ট মাধ্যম:</strong>
                                <span class="payment-method-badge">
                                    <?= $payment['payment_method'] === 'cash' ? 'নগদ' : htmlspecialchars($payment['payment_method']) ?>
                                </span>
                            </div>
                            
                            <?php if (!empty($payment['notes'])): ?>
                            <div class="mb-3">
                                <strong>নোট:</strong>
                                <small class="text-muted"><?= htmlspecialchars($payment['notes']) ?></small>
                            </div>
                            <?php endif; ?>
                            
                            <div class="d-grid gap-2">
                                <a href="receipt_view.php?receipt_no=<?= urlencode($payment['receipt_no']) ?>" 
                                   target="_blank" class="btn btn-success">
                                    <i class="fas fa-receipt me-2"></i>রিসিপ্ট দেখুন
                                </a>
                                <a href="receipt_final.php?receipt_no=<?= urlencode($payment['receipt_no']) ?>" 
                                   target="_blank" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-print me-2"></i>প্রিন্ট ভার্সন
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <!-- Stats -->
                <?php if (!empty($payments)): ?>
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-primary"><?= count($payments) ?></h3>
                                <p class="mb-0">পেমেন্ট পাওয়া গেছে</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-success">৳<?= number_format(array_sum(array_column($payments, 'amount')), 2) ?></h3>
                                <p class="mb-0">মোট পরিমাণ</p>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Quick Actions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-tools me-2"></i>দ্রুত অ্যাকশন</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <a href="fix_database.php" class="btn btn-warning w-100 mb-2">
                                            <i class="fas fa-database me-2"></i>ডাটাবেস ঠিক করুন
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="debug_system.php" class="btn btn-info w-100 mb-2">
                                            <i class="fas fa-bug me-2"></i>সিস্টেম ডিবাগ
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="setup_sample_data.php" class="btn btn-success w-100 mb-2">
                                            <i class="fas fa-plus me-2"></i>নমুনা ডেটা
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="fee_management.php" class="btn btn-primary w-100 mb-2">
                                            <i class="fas fa-money-bill me-2"></i>ফি ম্যানেজমেন্ট
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
