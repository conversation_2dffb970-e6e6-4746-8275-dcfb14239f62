<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if class_groups table exists and get data
$showSetup = false; // Force show cards
$classGroups = [];

// Add default groups if database fails
$classGroups = [
    [
        'id' => 1,
        'group_name' => 'প্রাথমিক নিম্ন (ক্লাস ১-২)',
        'group_code' => 'PRIMARY_LOWER',
        'description' => 'ক্লাস ১ ও ২ এর জন্য পরীক্ষা ব্যবস্থাপনা',
        'min_class' => 1,
        'max_class' => 2
    ],
    [
        'id' => 2,
        'group_name' => 'প্রাথমিক উচ্চ (ক্লাস ৩-৫)',
        'group_code' => 'PRIMARY_UPPER',
        'description' => 'ক্লাস ৩, ৪ ও ৫ এর জন্য পরীক্ষা ব্যবস্থাপনা',
        'min_class' => 3,
        'max_class' => 5
    ],
    [
        'id' => 3,
        'group_name' => 'জুনিয়র (ক্লাস ৬-৮)',
        'group_code' => 'JUNIOR',
        'description' => 'ক্লাস ৬, ৭ ও ৮ এর জন্য পরীক্ষা ব্যবস্থাপনা',
        'min_class' => 6,
        'max_class' => 8
    ],
    [
        'id' => 4,
        'group_name' => 'মাধ্যমিক (ক্লাস ৯-১০)',
        'group_code' => 'SECONDARY',
        'description' => 'ক্লাস ৯ ও ১০ এর জন্য পরীক্ষা ব্যবস্থাপনা',
        'min_class' => 9,
        'max_class' => 10
    ],
    [
        'id' => 5,
        'group_name' => 'উচ্চ মাধ্যমিক (ক্লাস ১১-১২)',
        'group_code' => 'HIGHER_SECONDARY',
        'description' => 'ক্লাস ১১ ও ১২ এর জন্য পরীক্ষা ব্যবস্থাপনা',
        'min_class' => 11,
        'max_class' => 12
    ]
];

try {
    $tableCheck = $conn->query("SHOW TABLES LIKE 'class_groups'");
    if ($tableCheck && $tableCheck->num_rows > 0) {
        // Table exists, check for data
        $groupsQuery = "SELECT * FROM class_groups ORDER BY min_class";
        $groupsResult = $conn->query($groupsQuery);

        if ($groupsResult && $groupsResult->num_rows > 0) {
            $classGroups = []; // Clear default data
            while ($row = $groupsResult->fetch_assoc()) {
                $classGroups[] = $row;
            }
        }
    }
} catch (Exception $e) {
    // Use default data
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ক্লাস ভিত্তিক পরীক্ষা ড্যাশবোর্ড - স্কুল ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: #f8f9fa;
        }
        .page-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 40px 0;
            margin-bottom: 40px;
            border-radius: 0 0 30px 30px;
        }
        .class-group-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .class-group-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        .card-header-custom {
            padding: 25px;
            color: white;
            border: none;
        }
        .primary-lower { background: linear-gradient(135deg, #FF6B6B, #FF8E8E); }
        .primary-upper { background: linear-gradient(135deg, #4ECDC4, #44A08D); }
        .junior { background: linear-gradient(135deg, #45B7D1, #96C93D); }
        .secondary { background: linear-gradient(135deg, #667eea, #764ba2); }
        .higher-secondary { background: linear-gradient(135deg, #f093fb, #f5576c); }
        
        .btn-access {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-access:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            color: white;
            transform: scale(1.05);
        }
        .setup-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 bg-dark text-white min-vh-100">
                <div class="p-3">
                    <h5>মেনু</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link text-white" href="dashboard.php">
                                <i class="fas fa-home me-2"></i>ড্যাশবোর্ড
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white active" href="#">
                                <i class="fas fa-layer-group me-2"></i>ক্লাস ভিত্তিক পরীক্ষা
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" href="exam_management.php">
                                <i class="fas fa-clipboard-list me-2"></i>সাধারণ পরীক্ষা
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <!-- Page Header -->
                <div class="page-header">
                    <div class="container">
                        <div class="row align-items-center">
                            <div class="col">
                                <h1 class="mb-3">ক্লাস ভিত্তিক পরীক্ষা ব্যবস্থাপনা</h1>
                                <p class="mb-0 opacity-75 fs-5">বিভিন্ন ক্লাস গ্রুপের জন্য আলাদা পরীক্ষা পরিচালনা করুন</p>
                            </div>
                            <div class="col-auto">
                                <a href="dashboard.php" class="btn btn-light btn-lg">
                                    <i class="fas fa-arrow-left me-2"></i>মূল ড্যাশবোর্ড
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="container">
                    <?php if ($showSetup): ?>
                        <!-- Setup Required -->
                        <div class="setup-card">
                            <div class="text-center">
                                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                                <h3 class="text-muted mb-3">সেটআপ প্রয়োজন</h3>
                                <p class="text-muted mb-4">ক্লাস ভিত্তিক পরীক্ষা ব্যবস্থাপনা ব্যবহার করার জন্য প্রথমে নিচের ধাপগুলো সম্পন্ন করুন:</p>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="card h-100">
                                            <div class="card-body text-center">
                                                <i class="fas fa-database fa-2x text-primary mb-3"></i>
                                                <h5>ধাপ ১</h5>
                                                <p>ক্লাস গ্রুপ টেবিল তৈরি করুন</p>
                                                <a href="create_class_groups_table.php" class="btn btn-primary">
                                                    <i class="fas fa-plus me-2"></i>টেবিল তৈরি করুন
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="card h-100">
                                            <div class="card-body text-center">
                                                <i class="fas fa-table fa-2x text-success mb-3"></i>
                                                <h5>ধাপ ২</h5>
                                                <p>পরীক্ষা টেবিল তৈরি করুন</p>
                                                <a href="create_class_based_exam_tables.php" class="btn btn-success">
                                                    <i class="fas fa-plus me-2"></i>পরীক্ষা টেবিল তৈরি করুন
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <!-- Class Groups Display -->
                        <div class="row">
                            <?php 
                            $cardClasses = ['primary-lower', 'primary-upper', 'junior', 'secondary', 'higher-secondary'];
                            $icons = ['fas fa-baby', 'fas fa-graduation-cap', 'fas fa-book', 'fas fa-user-graduate', 'fas fa-university'];
                            $pageLinks = [
                                'PRIMARY_LOWER' => 'class_exam_primary_lower_1_2.php',
                                'PRIMARY_UPPER' => 'class_exam_primary_upper_3_5.php',
                                'JUNIOR' => 'class_exam_junior_6_8.php',
                                'SECONDARY' => 'class_exam_secondary_9_10.php',
                                'HIGHER_SECONDARY' => 'class_exam_higher_secondary_11_12.php'
                            ];
                            
                            foreach ($classGroups as $index => $group):
                                $cardClass = $cardClasses[$index % count($cardClasses)];
                                $icon = $icons[$index % count($icons)];
                                $pageLink = $pageLinks[$group['group_code']] ?? '#';
                            ?>
                                <div class="col-lg-6 col-xl-4">
                                    <div class="card class-group-card">
                                        <div class="card-header-custom <?php echo $cardClass; ?>">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h4 class="mb-2">
                                                        <i class="<?php echo $icon; ?> me-2"></i>
                                                        <?php echo htmlspecialchars($group['group_name']); ?>
                                                    </h4>
                                                    <p class="mb-3 opacity-75">
                                                        <?php echo htmlspecialchars($group['description']); ?>
                                                    </p>
                                                    <a href="<?php echo $pageLink; ?>" class="btn btn-access">
                                                        <i class="fas fa-arrow-right me-2"></i>পরীক্ষা পরিচালনা
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php $conn->close(); ?>
