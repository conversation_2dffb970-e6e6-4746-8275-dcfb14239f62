<?php
/**
 * Font Update Script for Admin Pages
 * This script updates all PHP files in the admin directory to use the global-head.php include
 * for consistent font styling across all admin pages.
 */

// Define the directory to scan
$directory = __DIR__;

// Get all PHP files in the directory and subdirectories
$files = new RecursiveIteratorIterator(
    new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS),
    RecursiveIteratorIterator::SELF_FIRST,
    RecursiveIteratorIterator::CATCH_GET_CHILD
);

// Files to exclude from processing
$exclude_files = [
    'update_fonts.php',
    'dbh.inc.php',
    'functions.php',
    'global-head.php'
];

// Counter for updated files
$updated_files = 0;

// Process each PHP file
foreach ($files as $file) {
    if ($file->isFile() && $file->getExtension() === 'php') {
        $filename = $file->getBasename();
        $filepath = $file->getPathname();
        
        // Skip excluded files
        if (in_array($filename, $exclude_files)) {
            continue;
        }
        
        // Skip files in the includes directory
        if (strpos($filepath, 'includes' . DIRECTORY_SEPARATOR) !== false) {
            continue;
        }
        
        // Read the file content
        $content = file_get_contents($filepath);
        
        // Check if the file has a head section
        if (strpos($content, '<head>') !== false) {
            // Replace the head section with the global head include
            $pattern = '/<head>(.*?)<title>(.*?)<\/title>/s';
            $replacement = '<head>
    <?php include \'includes/global-head.php\'; ?>
    <title>$2</title>';
            
            $new_content = preg_replace($pattern, $replacement, $content);
            
            // Remove any duplicate CSS or font includes
            $new_content = preg_replace('/<link.*?fonts.googleapis.com.*?>/s', '', $new_content);
            $new_content = preg_replace('/<link.*?fonts.css.*?>/s', '', $new_content);
            $new_content = preg_replace('/<link.*?style.css.*?>/s', '', $new_content);
            
            // Write the updated content back to the file
            if ($new_content !== $content) {
                file_put_contents($filepath, $new_content);
                $updated_files++;
                echo "Updated: $filename<br>";
            }
        }
    }
}

echo "<h3>Font Update Complete for Admin Pages</h3>";
echo "Updated $updated_files files.<br>";
echo "<a href='dashboard.php'>Return to Dashboard</a>";
?>
