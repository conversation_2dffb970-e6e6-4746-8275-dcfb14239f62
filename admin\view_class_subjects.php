<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    echo '<div class="alert alert-danger">অননুমোদিত প্রবেশ!</div>';
    exit();
}

require_once '../includes/dbh.inc.php';

try {
    // Check if class_subjects table exists
    $tableExistsQuery = "SHOW TABLES LIKE 'class_subjects'";
    $tableExists = $conn->query($tableExistsQuery)->num_rows > 0;
    
    if (!$tableExists) {
        echo '<div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                এখনো কোন ক্লাসের জন্য বিষয় কনফিগার করা হয়নি।
              </div>';
        exit();
    }

    // Get class subjects configuration
    $query = "SELECT cs.*, c.class_name, d.department_name, s.subject_name, s.subject_code
              FROM class_subjects cs
              JOIN classes c ON cs.class_id = c.id
              LEFT JOIN departments d ON cs.department_id = d.id
              JOIN subjects s ON cs.subject_id = s.id
              ORDER BY c.class_name, d.department_name, cs.subject_type, s.subject_name";
    
    $result = $conn->query($query);
    
    if (!$result || $result->num_rows === 0) {
        echo '<div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                এখনো কোন ক্লাসের জন্য বিষয় কনফিগার করা হয়নি।
              </div>';
        exit();
    }

    // Group results by class and department
    $classSubjects = [];
    while ($row = $result->fetch_assoc()) {
        $classKey = $row['class_name'];
        $deptKey = $row['department_name'] ?: 'সাধারণ';
        
        if (!isset($classSubjects[$classKey])) {
            $classSubjects[$classKey] = [];
        }
        
        if (!isset($classSubjects[$classKey][$deptKey])) {
            $classSubjects[$classKey][$deptKey] = [
                'required' => [],
                'optional' => [],
                'fourth' => []
            ];
        }
        
        $classSubjects[$classKey][$deptKey][$row['subject_type']][] = [
            'subject_name' => $row['subject_name'],
            'subject_code' => $row['subject_code']
        ];
    }

    // Display the configuration
    echo '<div class="container-fluid">';
    
    foreach ($classSubjects as $className => $departments) {
        echo '<div class="card mb-4">';
        echo '<div class="card-header bg-primary text-white">';
        echo '<h5 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>' . htmlspecialchars($className) . '</h5>';
        echo '</div>';
        echo '<div class="card-body">';
        
        foreach ($departments as $deptName => $subjects) {
            echo '<div class="mb-4">';
            echo '<h6 class="text-info"><i class="fas fa-building me-2"></i>' . htmlspecialchars($deptName) . '</h6>';
            
            // Required subjects
            if (!empty($subjects['required'])) {
                echo '<div class="mb-3">';
                echo '<strong class="text-success"><i class="fas fa-star me-1"></i>আবশ্যিক বিষয়:</strong><br>';
                foreach ($subjects['required'] as $subject) {
                    echo '<span class="badge bg-success me-1 mb-1">' . htmlspecialchars($subject['subject_code']) . ' - ' . htmlspecialchars($subject['subject_name']) . '</span>';
                }
                echo '</div>';
            }
            
            // Optional subjects
            if (!empty($subjects['optional'])) {
                echo '<div class="mb-3">';
                echo '<strong class="text-info"><i class="fas fa-circle me-1"></i>ঐচ্ছিক বিষয়:</strong><br>';
                foreach ($subjects['optional'] as $subject) {
                    echo '<span class="badge bg-info me-1 mb-1">' . htmlspecialchars($subject['subject_code']) . ' - ' . htmlspecialchars($subject['subject_name']) . '</span>';
                }
                echo '</div>';
            }
            
            // Fourth subjects
            if (!empty($subjects['fourth'])) {
                echo '<div class="mb-3">';
                echo '<strong class="text-warning"><i class="fas fa-plus me-1"></i>চতুর্থ বিষয়:</strong><br>';
                foreach ($subjects['fourth'] as $subject) {
                    echo '<span class="badge bg-warning me-1 mb-1">' . htmlspecialchars($subject['subject_code']) . ' - ' . htmlspecialchars($subject['subject_name']) . '</span>';
                }
                echo '</div>';
            }
            
            echo '<hr>';
            echo '</div>';
        }
        
        echo '</div>';
        echo '</div>';
    }
    
    echo '</div>';

} catch (Exception $e) {
    echo '<div class="alert alert-danger">
            <i class="fas fa-exclamation-circle me-2"></i>
            ডেটা লোড করতে সমস্যা হয়েছে: ' . htmlspecialchars($e->getMessage()) . '
          </div>';
}
?>
