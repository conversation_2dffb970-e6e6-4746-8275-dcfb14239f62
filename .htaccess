DirectoryIndex index.html index.php

# Disable output buffering and compression to prevent title bar buffering
<IfModule mod_deflate.c>
    SetEnv no-gzip 1
    SetEnv dont-vary 1
</IfModule>

# PHP Configuration to prevent buffering
<IfModule mod_php.c>
    php_flag output_buffering Off
    php_flag implicit_flush On
    php_flag zlib.output_compression Off
</IfModule>

RewriteEngine On

# Redirect to HTTPS (if using SSL)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Block access to hidden files
<FilesMatch "^\.">
    Order allow,deny
    <PERSON><PERSON> from all
</FilesMatch>

# Protect against common attacks and prevent buffering
<IfModule mod_headers.c>
    Header set X-Content-Type-Options "nosniff"
    Header set X-XSS-Protection "1; mode=block"
    Header set X-Frame-Options "SAMEORIGIN"

    # Prevent caching and buffering for PHP files
    <FilesMatch "\.php$">
        Header always set Cache-Control "no-store, no-cache, must-revalidate, max-age=0"
        Header always set Pragma "no-cache"
        Header always set Expires "0"
        Header unset Content-Encoding
        Header unset Vary
    </FilesMatch>
</IfModule>