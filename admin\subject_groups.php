<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Initialize messages
$successMessage = '';
$errorMessage = '';

// Check for success message from redirect
if (isset($_GET['success'])) {
    $successMessage = "বিষয়ের ধরন সফলভাবে আপডেট করা হয়েছে!";
}

// Check for error message from redirect
if (isset($_GET['error'])) {
    $errorMessage = urldecode($_GET['error']);
}

// Create subject_groups table if it doesn't exist
$tableQuery = "CREATE TABLE IF NOT EXISTS subject_groups (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    subject_id INT(11) NOT NULL,
    group_id INT(11) NOT NULL,
    subject_type ENUM('required', 'optional', 'fourth') DEFAULT 'optional',
    is_applicable TINYINT(1) DEFAULT 1,
    category_id INT(11) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";
$conn->query($tableQuery);

// Check if category_id column exists
$checkColumnQuery = "SHOW COLUMNS FROM subject_groups LIKE 'category_id'";
$columnExists = $conn->query($checkColumnQuery)->num_rows > 0;

if (!$columnExists) {
    // Add category_id column
    $alterQuery = "ALTER TABLE subject_groups ADD COLUMN category_id INT(11) DEFAULT 1 AFTER is_applicable";
    $conn->query($alterQuery);
}

// Check if UNIQUE KEY exists and remove it
$checkUniqueKeyQuery = "SHOW CREATE TABLE subject_groups";
$result = $conn->query($checkUniqueKeyQuery);
if ($result && $result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $createTableStatement = $row['Create Table'];

    if (strpos($createTableStatement, 'UNIQUE KEY') !== false) {
        // Drop the UNIQUE KEY constraint
        $alterQuery = "ALTER TABLE subject_groups DROP INDEX subject_id";
        $conn->query($alterQuery);
    }
}

// Check if is_applicable column exists
$checkColumnQuery = "SHOW COLUMNS FROM subject_groups LIKE 'is_applicable'";
$columnExists = $conn->query($checkColumnQuery)->num_rows > 0;

if (!$columnExists) {
    // Add is_applicable column
    $alterQuery = "ALTER TABLE subject_groups ADD COLUMN is_applicable TINYINT(1) DEFAULT 1 AFTER subject_type";
    $conn->query($alterQuery);
}

// Create groups table if it doesn't exist
$tableQuery = "CREATE TABLE IF NOT EXISTS groups (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    group_name VARCHAR(255) NOT NULL,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";
$conn->query($tableQuery);

// Check if group_code column exists
$checkColumnQuery = "SHOW COLUMNS FROM groups LIKE 'group_code'";
$columnExists = $conn->query($checkColumnQuery)->num_rows > 0;

if ($columnExists) {
    // Alter the table to remove the group_code column
    $alterQuery = "ALTER TABLE groups DROP COLUMN group_code";
    $conn->query($alterQuery);
}

// Insert default groups if they don't exist
$defaultGroups = [
    'বিজ্ঞান',
    'মানবিক',
    'ব্যবসায়',
    'সাধারণ'
];

// Remove old default groups if they exist
$oldGroupsToRemove = ['বাণিজ্য', 'সকল'];
foreach ($oldGroupsToRemove as $oldGroupName) {
    $checkQuery = "SELECT id FROM groups WHERE group_name = '$oldGroupName'";
    $result = $conn->query($checkQuery);
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $oldGroupId = $row['id'];

        // Delete from subject_groups
        $deleteSubjectGroupsQuery = "DELETE FROM subject_groups WHERE group_id = $oldGroupId";
        $conn->query($deleteSubjectGroupsQuery);

        // Delete the group
        $deleteGroupQuery = "DELETE FROM groups WHERE id = $oldGroupId";
        $conn->query($deleteGroupQuery);
    }
}

// Insert new default groups if they don't exist
foreach ($defaultGroups as $groupName) {
    $checkQuery = "SELECT COUNT(*) as count FROM groups WHERE group_name = '$groupName'";
    $result = $conn->query($checkQuery);
    $row = $result->fetch_assoc();

    if ($row['count'] == 0) {
        $insertQuery = "INSERT INTO groups (group_name) VALUES ('$groupName')";
        $conn->query($insertQuery);
    }
}

// Handle form submission for updating subject types
if (isset($_POST['update_subject_types'])) {
    // First, set all subjects as not applicable
    $resetQuery = "UPDATE subject_groups SET is_applicable = 0";
    $conn->query($resetQuery);

    // Get all subject and group combinations
    if (isset($_POST['subject_type'])) {
        foreach ($_POST['subject_type'] as $subjectId => $groups) {
            foreach ($groups as $groupId => $categories) {
                // Check if this subject is applicable for this group
                $isApplicable = isset($_POST['is_applicable'][$subjectId][$groupId]) ? 1 : 0;

                if ($isApplicable) {
                    // Handle multiple categories
                    if (is_array($categories)) {
                        // Delete existing records for this subject-group combination
                        $deleteQuery = "DELETE FROM subject_groups
                                      WHERE subject_id = $subjectId AND group_id = $groupId";
                        $conn->query($deleteQuery);

                        // Insert new records for each category
                        foreach ($categories as $categoryId => $type) {
                            $insertQuery = "INSERT INTO subject_groups
                                          (subject_id, group_id, subject_type, is_applicable, category_id)
                                          VALUES ($subjectId, $groupId, '$type', $isApplicable, $categoryId)";
                            $conn->query($insertQuery);
                        }
                    } else {
                        // Single category (default behavior)
                        $type = $categories;

                        // Check if record exists
                        $checkQuery = "SELECT COUNT(*) as count FROM subject_groups
                                      WHERE subject_id = $subjectId AND group_id = $groupId AND category_id = 1";
                        $result = $conn->query($checkQuery);
                        $row = $result->fetch_assoc();

                        if ($row['count'] > 0) {
                            // Update existing record
                            $updateQuery = "UPDATE subject_groups
                                           SET subject_type = '$type', is_applicable = $isApplicable
                                           WHERE subject_id = $subjectId AND group_id = $groupId AND category_id = 1";
                            $conn->query($updateQuery);
                        } else {
                            // Insert new record
                            $insertQuery = "INSERT INTO subject_groups
                                          (subject_id, group_id, subject_type, is_applicable, category_id)
                                          VALUES ($subjectId, $groupId, '$type', $isApplicable, 1)";
                            $conn->query($insertQuery);
                        }
                    }
                }
            }
        }
    }

    // Handle adding new categories
    if (isset($_POST['add_category'])) {
        foreach ($_POST['add_category'] as $subjectId => $groups) {
            foreach ($groups as $groupId => $value) {
                if ($value == 1) {
                    // Get the highest category_id for this subject-group combination
                    $maxCategoryQuery = "SELECT MAX(category_id) as max_category FROM subject_groups
                                        WHERE subject_id = $subjectId AND group_id = $groupId";
                    $result = $conn->query($maxCategoryQuery);
                    $row = $result->fetch_assoc();
                    $newCategoryId = ($row['max_category'] ?? 0) + 1;

                    // Insert new category with default type 'optional'
                    $insertQuery = "INSERT INTO subject_groups
                                  (subject_id, group_id, subject_type, is_applicable, category_id)
                                  VALUES ($subjectId, $groupId, 'optional', 1, $newCategoryId)";
                    $conn->query($insertQuery);
                }
            }
        }
    }

    header("Location: subject_groups.php?success=1");
    exit();
}

// Get all subjects
$subjectsQuery = "SELECT * FROM subjects WHERE is_active = 1 ORDER BY subject_name";
$subjects = $conn->query($subjectsQuery);

// Get all groups
$groupsQuery = "SELECT * FROM groups WHERE is_active = 1 ORDER BY id";
$groups = $conn->query($groupsQuery);

// Get existing subject-group mappings
$mappingsQuery = "SELECT * FROM subject_groups ORDER BY subject_id, group_id, category_id";
$mappingsResult = $conn->query($mappingsQuery);
$mappings = [];
$categoryCount = [];

if ($mappingsResult && $mappingsResult->num_rows > 0) {
    while ($mapping = $mappingsResult->fetch_assoc()) {
        // Store mapping by category_id
        $mappings[$mapping['subject_id']][$mapping['group_id']][$mapping['category_id']] = [
            'subject_type' => $mapping['subject_type'],
            'is_applicable' => $mapping['is_applicable']
        ];

        // Count categories for each subject-group combination
        if (!isset($categoryCount[$mapping['subject_id']][$mapping['group_id']])) {
            $categoryCount[$mapping['subject_id']][$mapping['group_id']] = 0;
        }
        $categoryCount[$mapping['subject_id']][$mapping['group_id']]++;
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>গ্রুপ অনুযায়ী বিষয়ের ধরন - কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <!-- Additional Google Fonts for modern look -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --accent-color: #4895ef;
            --success-color: #4cc9f0;
            --warning-color: #f72585;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --border-radius: 12px;
            --box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Hind Siliguri', 'Noto Sans Bengali', sans-serif;
            background-color: #f0f2f5;
            color: var(--dark-color);
            line-height: 1.6;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Noto Sans Bengali', 'Hind Siliguri', sans-serif;
            font-weight: 600;
        }

        .sidebar {
            background: linear-gradient(135deg, #3a0ca3 0%, #4361ee 100%);
            color: white;
            min-height: 100vh;
            padding-top: 20px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.85);
            padding: 12px 20px;
            margin: 5px 10px;
            border-radius: 10px;
            transition: var(--transition);
            font-weight: 500;
        }

        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateX(5px);
        }

        .sidebar .nav-link.active {
            color: white;
            background: linear-gradient(90deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .sidebar .nav-link i {
            margin-right: 12px;
            font-size: 1.1rem;
            width: 24px;
            text-align: center;
        }

        .main-content {
            padding: 30px;
            background-color: #f0f2f5;
            min-height: 100vh;
        }

        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 30px;
            overflow: hidden;
            transition: var(--transition);
        }

        .card:hover {
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
            transform: translateY(-5px);
        }

        .card-header {
            border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
            font-weight: 600;
            padding: 1.2rem 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .card-body {
            padding: 1.5rem;
        }

        .btn {
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 500;
            transition: var(--transition);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
        }

        .btn-primary {
            background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
            border: none;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f72585 0%, #b5179e 100%);
            border: none;
            color: white;
        }

        .btn-warning:hover {
            color: white;
        }

        .table {
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
        }

        .table th {
            font-weight: 600;
            background-color: #f8f9fa;
            padding: 15px;
            font-size: 0.95rem;
        }

        .table td {
            padding: 15px;
            vertical-align: middle;
        }

        .table-bordered {
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .table-bordered th, .table-bordered td {
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .badge {
            padding: 8px 12px;
            font-weight: 500;
            border-radius: 8px;
        }

        .form-check-input {
            width: 1.2em;
            height: 1.2em;
            cursor: pointer;
        }

        .form-switch .form-check-input {
            width: 2.5em;
            height: 1.4em;
        }

        .subject-type-selector {
            width: 100%;
            padding: 10px 12px;
            border-radius: 10px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            font-size: 0.95rem;
            transition: var(--transition);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .subject-type-selector:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
        }

        .subject-type-selector option {
            padding: 10px;
        }

        .required-type {
            background: linear-gradient(135deg, #cfe2ff 0%, #e2eafc 100%);
            color: #0a58ca;
            font-weight: 500;
        }

        .optional-type {
            background: linear-gradient(135deg, #d1e7dd 0%, #e9f5db 100%);
            color: #0f5132;
            font-weight: 500;
        }

        .fourth-type {
            background: linear-gradient(135deg, #fff3cd 0%, #fefae0 100%);
            color: #664d03;
            font-weight: 500;
        }

        .alert {
            border-radius: var(--border-radius);
            padding: 1rem 1.5rem;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
            border: none;
        }

        .alert-info {
            background: linear-gradient(135deg, #4cc9f0 0%, #4895ef 100%);
            color: white;
        }

        .alert-success {
            background: linear-gradient(135deg, #57cc99 0%, #38b000 100%);
            color: white;
        }

        .category-item {
            position: relative;
            padding-right: 40px;
            margin-bottom: 15px;
        }

        .remove-category-btn {
            position: absolute;
            right: 0;
            top: 5px;
            padding: 4px 8px;
            font-size: 0.8rem;
            border-radius: 8px;
            background: linear-gradient(135deg, #f72585 0%, #b5179e 100%);
            border: none;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            transition: var(--transition);
        }

        .remove-category-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .add-category-btn {
            font-size: 0.9rem;
            padding: 8px 15px;
            background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
            border: none;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: var(--transition);
        }

        .add-category-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4 mt-2">
                    <h3 class="text-white">অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="subjects.php">
                            <i class="fas fa-book-open"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sessions.php">
                            <i class="fas fa-calendar-alt"></i> সেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2 class="mb-2">গ্রুপ অনুযায়ী বিষয়ের ধরন</h2>
                        <p class="text-muted">বিভিন্ন গ্রুপের জন্য বিষয়ের ধরন (আবশ্যিক/ঐচ্ছিক/৪র্থ) নির্ধারণ করুন</p>
                    </div>
                    <div class="col-auto d-flex align-items-center">
                        <a href="subjects.php" class="btn btn-primary me-2">
                            <i class="fas fa-arrow-left me-2"></i>বিষয় তালিকায় ফিরে যান
                        </a>
                    </div>
                </div>

                <div class="alert alert-info mb-4 animate__animated animate__fadeIn">
                    <div class="d-flex align-items-center">
                        <div class="feature-icon me-3 bg-white rounded-circle p-2">
                            <i class="fas fa-info-circle fa-2x text-primary"></i>
                        </div>
                        <div>
                            <h5 class="fw-bold mb-1">নতুন ফিচার: একাধিক ক্যাটাগরি</h5>
                            <p class="mb-0">এখন একই বিভাগের জন্য একাধিক ক্যাটাগরি যোগ করতে পারবেন। একটি বিষয়কে একই গ্রুপে একাধিক ধরনের (আবশ্যিক/ঐচ্ছিক/৪র্থ) হিসেবে সেট করতে "নতুন ক্যাটাগরি" বাটনে ক্লিক করুন।</p>
                        </div>
                    </div>
                </div>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i><?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Subject Groups Form -->
                <div class="card mb-4">
                    <div class="card-header bg-gradient">
                        <div class="d-flex align-items-center">
                            <div class="header-icon me-3 bg-white rounded-circle p-2">
                                <i class="fas fa-layer-group fa-lg text-primary"></i>
                            </div>
                            <h5 class="card-title mb-0 fw-bold">গ্রুপ অনুযায়ী বিষয়ের ধরন নির্ধারণ করুন</h5>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if ($subjects && $subjects->num_rows > 0 && $groups && $groups->num_rows > 0): ?>
                            <form method="POST" action="subject_groups.php">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>বিষয়</th>
                                                <?php while ($group = $groups->fetch_assoc()): ?>
                                                    <th class="text-center">
                                                        <?php echo htmlspecialchars($group['group_name']); ?>
                                                        <div class="small text-muted">
                                                            (টগল করে প্রযোজ্য/অপ্রযোজ্য করুন)
                                                        </div>
                                                    </th>
                                                <?php endwhile; ?>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            // Reset groups result pointer
                                            $groups->data_seek(0);

                                            // Loop through subjects
                                            while ($subject = $subjects->fetch_assoc()):
                                            ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($subject['subject_name']); ?></strong>
                                                        <br>
                                                        <small class="text-muted"><?php echo htmlspecialchars($subject['subject_code']); ?></small>
                                                    </td>
                                                    <?php
                                                    // Reset groups result pointer for each subject
                                                    $groups->data_seek(0);

                                                    // Loop through groups for this subject
                                                    while ($group = $groups->fetch_assoc()):
                                                        // Default type is optional - this is now handled per category
                                                    ?>
                                                        <td class="text-center">
                                                            <?php
                                                            $isApplicable = isset($mappings[$subject['id']][$group['id']]) ? true : false;
                                                            if ($isApplicable) {
                                                                // Check if at least one category is applicable
                                                                $anyApplicable = false;
                                                                foreach ($mappings[$subject['id']][$group['id']] as $catId => $catData) {
                                                                    if ($catData['is_applicable'] == 1) {
                                                                        $anyApplicable = true;
                                                                        break;
                                                                    }
                                                                }
                                                                $isApplicable = $anyApplicable;
                                                            }
                                                            ?>
                                                            <div class="d-flex flex-column align-items-center">
                                                                <div class="form-check form-switch mb-2">
                                                                    <input class="form-check-input subject-applicable" type="checkbox"
                                                                           name="is_applicable[<?php echo $subject['id']; ?>][<?php echo $group['id']; ?>]"
                                                                           id="applicable_<?php echo $subject['id']; ?>_<?php echo $group['id']; ?>"
                                                                           data-subject-id="<?php echo $subject['id']; ?>"
                                                                           data-group-id="<?php echo $group['id']; ?>"
                                                                           <?php echo $isApplicable ? 'checked' : ''; ?>>
                                                                </div>

                                                                <?php if ($isApplicable): ?>
                                                                    <div class="category-container" id="category_container_<?php echo $subject['id']; ?>_<?php echo $group['id']; ?>">
                                                                        <?php
                                                                        // Display all categories for this subject-group combination
                                                                        if (isset($mappings[$subject['id']][$group['id']])) {
                                                                            foreach ($mappings[$subject['id']][$group['id']] as $categoryId => $categoryData) {
                                                                                if ($categoryData['is_applicable'] == 1) {
                                                                                    $currentType = $categoryData['subject_type'];
                                                                                    ?>
                                                                                    <div class="category-item mb-2">
                                                                                        <select name="subject_type[<?php echo $subject['id']; ?>][<?php echo $group['id']; ?>][<?php echo $categoryId; ?>]"
                                                                                                class="form-select form-select-sm subject-type-selector <?php echo $currentType; ?>-type">
                                                                                            <option value="required" <?php echo ($currentType == 'required') ? 'selected' : ''; ?>>
                                                                                                আবশ্যিক
                                                                                            </option>
                                                                                            <option value="optional" <?php echo ($currentType == 'optional') ? 'selected' : ''; ?>>
                                                                                                ঐচ্ছিক
                                                                                            </option>
                                                                                            <option value="fourth" <?php echo ($currentType == 'fourth') ? 'selected' : ''; ?>>
                                                                                                ৪র্থ
                                                                                            </option>
                                                                                        </select>
                                                                                        <?php if ($categoryId > 1): ?>
                                                                                        <button type="button" class="btn btn-sm btn-danger remove-category-btn mt-1"
                                                                                                data-subject-id="<?php echo $subject['id']; ?>"
                                                                                                data-group-id="<?php echo $group['id']; ?>"
                                                                                                data-category-id="<?php echo $categoryId; ?>">
                                                                                            <i class="fas fa-times"></i>
                                                                                        </button>
                                                                                        <?php endif; ?>
                                                                                    </div>
                                                                                    <?php
                                                                                }
                                                                            }
                                                                        } else {
                                                                            // Default category if none exists
                                                                            $currentType = 'optional';
                                                                            ?>
                                                                            <div class="category-item mb-2">
                                                                                <select name="subject_type[<?php echo $subject['id']; ?>][<?php echo $group['id']; ?>]"
                                                                                        class="form-select form-select-sm subject-type-selector <?php echo $currentType; ?>-type">
                                                                                    <option value="required">আবশ্যিক</option>
                                                                                    <option value="optional" selected>ঐচ্ছিক</option>
                                                                                    <option value="fourth">৪র্থ</option>
                                                                                </select>
                                                                            </div>
                                                                            <?php
                                                                        }
                                                                        ?>
                                                                    </div>

                                                                    <!-- Add Category Button -->
                                                                    <button type="button" class="btn btn-sm btn-primary add-category-btn mt-2"
                                                                            data-subject-id="<?php echo $subject['id']; ?>"
                                                                            data-group-id="<?php echo $group['id']; ?>">
                                                                        <i class="fas fa-plus"></i> নতুন ক্যাটাগরি
                                                                    </button>
                                                                    <input type="hidden" name="add_category[<?php echo $subject['id']; ?>][<?php echo $group['id']; ?>]"
                                                                           id="add_category_<?php echo $subject['id']; ?>_<?php echo $group['id']; ?>" value="0">
                                                                <?php endif; ?>
                                                            </div>
                                                        </td>
                                                    <?php endwhile; ?>
                                                </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-end mt-5">
                                    <button type="button" id="saveButton" class="btn btn-warning btn-lg px-5 animate__animated animate__pulse animate__infinite animate__slower">
                                        <i class="fas fa-save me-2"></i>সংরক্ষণ করুন
                                    </button>
                                    <button type="submit" name="update_subject_types" id="actualSubmitButton" class="d-none">সংরক্ষণ</button>
                                </div>
                            </form>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>কোন বিষয় বা গ্রুপ পাওয়া যায়নি। প্রথমে বিষয় এবং গ্রুপ যোগ করুন।
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add animation to cards
            document.querySelectorAll('.card').forEach((card, index) => {
                card.classList.add('animate__animated', 'animate__fadeInUp');
                card.style.animationDelay = `${index * 0.1}s`;
            });

            // Add animation to table rows
            document.querySelectorAll('tbody tr').forEach((row, index) => {
                row.classList.add('animate__animated', 'animate__fadeIn');
                row.style.animationDelay = `${0.3 + (index * 0.05)}s`;
            });

            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert-dismissible');
                alerts.forEach(function(alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);

            // Handle save button click with confirmation
            document.getElementById('saveButton').addEventListener('click', function() {
                Swal.fire({
                    title: 'আপনি কি নিশ্চিত?',
                    text: 'সকল পরিবর্তন সংরক্ষণ করতে চান?',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#f72585',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'হ্যাঁ, সংরক্ষণ করুন!',
                    cancelButtonText: 'না, বাতিল করুন',
                    background: '#fff',
                    backdrop: `rgba(0,0,123,0.4)`,
                    showClass: {
                        popup: 'animate__animated animate__fadeInDown'
                    },
                    hideClass: {
                        popup: 'animate__animated animate__fadeOutUp'
                    }
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Show loading state
                        Swal.fire({
                            title: 'সংরক্ষণ করা হচ্ছে...',
                            html: 'অনুগ্রহ করে অপেক্ষা করুন...',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });

                        // Submit the form
                        document.getElementById('actualSubmitButton').click();
                    }
                });
            });

            // Change select background color based on selection
            const typeSelectors = document.querySelectorAll('.subject-type-selector');
            typeSelectors.forEach(function(selector) {
                // Set initial class
                updateSelectClass(selector);

                // Update class on change
                selector.addEventListener('change', function() {
                    updateSelectClass(this);
                });
            });

            // Handle checkbox changes
            const applicableCheckboxes = document.querySelectorAll('.subject-applicable');
            applicableCheckboxes.forEach(function(checkbox) {
                // Update on change
                checkbox.addEventListener('change', function() {
                    const subjectId = this.dataset.subjectId;
                    const groupId = this.dataset.groupId;
                    const cell = this.closest('td');
                    const container = this.closest('div.d-flex');

                    // Get or create category container
                    let categoryContainer = container.querySelector('.category-container');
                    if (!categoryContainer) {
                        categoryContainer = document.createElement('div');
                        categoryContainer.className = 'category-container';
                        categoryContainer.id = `category_container_${subjectId}_${groupId}`;
                        container.appendChild(categoryContainer);
                    }

                    // Clear the container
                    categoryContainer.innerHTML = '';

                    // If checked, add a new category item
                    if (this.checked) {
                        // Add default category
                        const categoryItem = createCategoryItem(subjectId, groupId, 1, 'optional');
                        categoryContainer.appendChild(categoryItem);

                        // Add the "Add Category" button
                        const addButton = document.createElement('button');
                        addButton.type = 'button';
                        addButton.className = 'btn btn-sm btn-primary add-category-btn mt-2';
                        addButton.dataset.subjectId = subjectId;
                        addButton.dataset.groupId = groupId;
                        addButton.innerHTML = '<i class="fas fa-plus"></i> নতুন ক্যাটাগরি';
                        addButton.addEventListener('click', handleAddCategory);
                        container.appendChild(addButton);

                        // Add hidden input for tracking new categories
                        const hiddenInput = document.createElement('input');
                        hiddenInput.type = 'hidden';
                        hiddenInput.name = `add_category[${subjectId}][${groupId}]`;
                        hiddenInput.id = `add_category_${subjectId}_${groupId}`;
                        hiddenInput.value = '0';
                        container.appendChild(hiddenInput);
                    } else {
                        // Remove any add button and hidden input
                        const addButton = container.querySelector('.add-category-btn');
                        if (addButton) container.removeChild(addButton);

                        const hiddenInput = container.querySelector(`input[name="add_category[${subjectId}][${groupId}]"]`);
                        if (hiddenInput) container.removeChild(hiddenInput);
                    }
                });
            });

            // Handle "Add Category" button clicks
            const addCategoryButtons = document.querySelectorAll('.add-category-btn');
            addCategoryButtons.forEach(button => {
                button.addEventListener('click', handleAddCategory);
            });

            // Handle "Remove Category" button clicks
            document.addEventListener('click', function(e) {
                if (e.target.closest('.remove-category-btn')) {
                    const button = e.target.closest('.remove-category-btn');
                    const categoryItem = button.closest('.category-item');

                    if (categoryItem) {
                        // Add animation before removing
                        categoryItem.classList.add('animate__animated', 'animate__fadeOutUp');

                        // Wait for animation to complete before removing
                        categoryItem.addEventListener('animationend', function() {
                            categoryItem.remove();

                            // Show a toast notification
                            Swal.fire({
                                position: 'top-end',
                                icon: 'info',
                                title: 'ক্যাটাগরি মুছে ফেলা হয়েছে',
                                showConfirmButton: false,
                                timer: 1500,
                                toast: true
                            });
                        });
                    }
                }
            });

            function handleAddCategory(e) {
                const button = e.currentTarget;
                const subjectId = button.dataset.subjectId;
                const groupId = button.dataset.groupId;
                const container = document.getElementById(`category_container_${subjectId}_${groupId}`);

                if (container) {
                    // Count existing categories to determine the new category ID
                    const existingCategories = container.querySelectorAll('.category-item');
                    const newCategoryId = existingCategories.length + 1;

                    // Create and add new category item
                    const categoryItem = createCategoryItem(subjectId, groupId, newCategoryId, 'optional', true);
                    categoryItem.classList.add('animate__animated', 'animate__fadeInDown');
                    container.appendChild(categoryItem);

                    // Set the hidden input to indicate a new category should be added
                    const hiddenInput = document.getElementById(`add_category_${subjectId}_${groupId}`);
                    if (hiddenInput) {
                        hiddenInput.value = '1';
                    }

                    // Show a toast notification
                    Swal.fire({
                        position: 'top-end',
                        icon: 'success',
                        title: 'নতুন ক্যাটাগরি যোগ করা হয়েছে',
                        showConfirmButton: false,
                        timer: 1500,
                        toast: true
                    });
                }
            }

            function createCategoryItem(subjectId, groupId, categoryId, defaultType, showRemoveButton = false) {
                const div = document.createElement('div');
                div.className = 'category-item mb-2';

                // Create select element
                const select = document.createElement('select');
                select.className = `form-select form-select-sm subject-type-selector ${defaultType}-type`;
                select.name = `subject_type[${subjectId}][${groupId}][${categoryId}]`;

                // Add options
                const types = [
                    { value: 'required', text: 'আবশ্যিক' },
                    { value: 'optional', text: 'ঐচ্ছিক' },
                    { value: 'fourth', text: '৪র্থ' }
                ];

                types.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.value;
                    option.textContent = type.text;
                    if (type.value === defaultType) {
                        option.selected = true;
                    }
                    select.appendChild(option);
                });

                // Add change event to update class
                select.addEventListener('change', function() {
                    updateSelectClass(this);
                });

                div.appendChild(select);

                // Add remove button for non-default categories
                if (showRemoveButton || categoryId > 1) {
                    const removeButton = document.createElement('button');
                    removeButton.type = 'button';
                    removeButton.className = 'btn btn-sm btn-danger remove-category-btn mt-1';
                    removeButton.dataset.subjectId = subjectId;
                    removeButton.dataset.groupId = groupId;
                    removeButton.dataset.categoryId = categoryId;
                    removeButton.innerHTML = '<i class="fas fa-times"></i>';
                    div.appendChild(removeButton);
                }

                return div;
            }

            function updateSelectClass(select) {
                // Remove all type classes
                select.classList.remove('required-type', 'optional-type', 'fourth-type');

                // Add class based on selected value
                select.classList.add(select.value + '-type');
            }
        });
    </script>
</body>
</html>
