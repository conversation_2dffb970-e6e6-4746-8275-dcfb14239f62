<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Function to update sidebar in a file
function updateSidebar($filePath) {
    // Read the file content
    $content = file_get_contents($filePath);
    
    // Check if the file already has the server setup menu
    if (strpos($content, 'সার্ভার সেটআপ') !== false) {
        return true; // Already updated
    }
    
    // Find the database backup menu item and add server setup menu after it
    $pattern = '/<li class="nav-item">\s*<a class="nav-link[^>]*" href="database_backup\.php">\s*<i class="fas fa-database[^>]*><\/i>\s*ডাটাবেজ ব্যাকআপ\s*<\/a>\s*<\/li>/s';
    
    $replacement = '<li class="nav-item">
            <a class="nav-link<?php echo ($currentPage == \'database_backup.php\') ? \' active\' : \'\'; ?>" href="database_backup.php">
                <i class="fas fa-database me-2"></i> ডাটাবেজ ব্যাকআপ
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link<?php echo ($currentPage == \'server_setup.php\') ? \' active\' : \'\'; ?>" href="server_setup.php">
                <i class="fas fa-server me-2"></i> সার্ভার সেটআপ
            </a>
        </li>';
    
    // Replace the pattern
    $updatedContent = preg_replace($pattern, $replacement, $content);
    
    // Save the updated content
    return file_put_contents($filePath, $updatedContent);
}

// Get all PHP files in the admin directory
$adminFiles = glob('*.php');
$updatedFiles = [];
$failedFiles = [];

// Update each file
foreach ($adminFiles as $file) {
    if ($file === 'update_sidebar.php') {
        continue; // Skip this file
    }
    
    if (updateSidebar($file)) {
        $updatedFiles[] = $file;
    } else {
        $failedFiles[] = $file;
    }
}

// Display results
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>সাইডবার আপডেট - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container mt-5">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">সাইডবার আপডেট</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    সাইডবারে সার্ভার সেটআপ মেনু যুক্ত করা হয়েছে।
                </div>
                
                <h6>আপডেট করা ফাইল:</h6>
                <ul>
                    <?php foreach ($updatedFiles as $file): ?>
                        <li><?php echo $file; ?></li>
                    <?php endforeach; ?>
                </ul>
                
                <?php if (!empty($failedFiles)): ?>
                    <h6>আপডেট করতে ব্যর্থ ফাইল:</h6>
                    <ul>
                        <?php foreach ($failedFiles as $file): ?>
                            <li><?php echo $file; ?></li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
                
                <div class="mt-4">
                    <a href="dashboard.php" class="btn btn-primary">
                        <i class="fas fa-arrow-left me-2"></i>ড্যাশবোর্ডে ফিরে যান
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
