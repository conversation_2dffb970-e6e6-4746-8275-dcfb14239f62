<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$successMessages = [];
$errorMessages = [];

// Create department_subject_types table if it doesn't exist
$tableQuery = "CREATE TABLE IF NOT EXISTS department_subject_types (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    department_id INT(11) NOT NULL,
    subject_id INT(11) NOT NULL,
    subject_type ENUM('required', 'optional', 'fourth') NOT NULL DEFAULT 'optional',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY(department_id, subject_id),
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
    FOREIG<PERSON> KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
)";

if ($conn->query($tableQuery)) {
    $successMessages[] = "বিভাগ অনুযায়ী বিষয়ের ধরন টেবিল সফলভাবে তৈরি করা হয়েছে।";
} else {
    $errorMessages[] = "টেবিল তৈরি করতে সমস্যা হয়েছে: " . $conn->error;
}

// Check if table exists and has data
$checkDataQuery = "SELECT COUNT(*) as count FROM department_subject_types";
$result = $conn->query($checkDataQuery);
$hasData = false;

if ($result) {
    $hasData = $result->fetch_assoc()['count'] > 0;
}

// Get departments and subjects for the form
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Check if subject_departments table exists
$checkSubjectDeptTable = $conn->query("SHOW TABLES LIKE 'subject_departments'");
$subjectDeptTableExists = $checkSubjectDeptTable->num_rows > 0;

// Handle form submission
if (isset($_POST['save_types'])) {
    $departmentId = $_POST['department_id'] ?? 0;
    $subjectTypes = $_POST['subject_type'] ?? [];

    if (empty($departmentId) || !is_numeric($departmentId)) {
        $errorMessages[] = "দয়া করে একটি বৈধ বিভাগ নির্বাচন করুন।";
    } else {
        // Begin transaction
        $conn->begin_transaction();

        try {
            // First delete existing entries for this department
            $deleteQuery = "DELETE FROM department_subject_types WHERE department_id = ?";
            $stmt = $conn->prepare($deleteQuery);
            $stmt->bind_param("i", $departmentId);
            $stmt->execute();

            // Insert new entries
            $insertQuery = "INSERT INTO department_subject_types (department_id, subject_id, subject_type) VALUES (?, ?, ?)";
            $stmt = $conn->prepare($insertQuery);

            foreach ($subjectTypes as $subjectId => $type) {
                $stmt->bind_param("iis", $departmentId, $subjectId, $type);
                $stmt->execute();
            }

            $conn->commit();
            $successMessages[] = "বিষয়ের ধরন সফলভাবে সংরক্ষণ করা হয়েছে।";
        } catch (Exception $e) {
            $conn->rollback();
            $errorMessages[] = "বিষয়ের ধরন সংরক্ষণ করতে সমস্যা হয়েছে: " . $e->getMessage();
        }
    }
}

// Get parameters
$selectedDepartment = $_GET['department_id'] ?? ($_POST['department_id'] ?? 0);
$selectedSubject = $_GET['subject_id'] ?? 0;

// Get existing subject types for a department if selected
$existingTypes = [];
$departmentSubjects = [];

if (!empty($selectedDepartment) && is_numeric($selectedDepartment)) {
    // Get existing subject types
    $typesQuery = "SELECT dst.subject_id, dst.subject_type
                  FROM department_subject_types dst
                  WHERE dst.department_id = ?";
    $stmt = $conn->prepare($typesQuery);
    $stmt->bind_param("i", $selectedDepartment);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $existingTypes[$row['subject_id']] = $row['subject_type'];
    }

    // Get subjects for this department
    if ($subjectDeptTableExists) {
        // Get subjects linked to this department through subject_departments table
        $subjectsQuery = "SELECT s.*
                         FROM subjects s
                         JOIN subject_departments sd ON s.id = sd.subject_id
                         WHERE sd.department_id = ? AND s.is_active = 1
                         ORDER BY s.subject_name";
        $stmt = $conn->prepare($subjectsQuery);
        $stmt->bind_param("i", $selectedDepartment);
    } else {
        // Fallback to all subjects if subject_departments table doesn't exist
        $subjectsQuery = "SELECT * FROM subjects WHERE is_active = 1 ORDER BY subject_name";
        $stmt = $conn->prepare($subjectsQuery);
    }

    $stmt->execute();
    $subjects = $stmt->get_result();
}

// If a specific subject is selected, get its details
$selectedSubjectInfo = null;
if (!empty($selectedSubject) && is_numeric($selectedSubject)) {
    $subjectQuery = "SELECT s.*, d.department_name
                    FROM subjects s
                    LEFT JOIN departments d ON s.department_id = d.id
                    WHERE s.id = ?";
    $stmt = $conn->prepare($subjectQuery);
    $stmt->bind_param("i", $selectedSubject);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $selectedSubjectInfo = $result->fetch_assoc();
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>বিভাগ অনুযায়ী বিষয়ের ধরন - কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .subject-card {
            transition: all 0.3s ease;
            border-left: 4px solid #6c757d;
        }
        .subject-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .subject-card.required {
            border-left-color: #0d6efd;
        }
        .subject-card.optional {
            border-left-color: #198754;
        }
        .subject-card.fourth {
            border-left-color: #dc3545;
        }
        .type-badge {
            position: absolute;
            top: 10px;
            right: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="departments.php">
                            <i class="fas fa-building me-2"></i> বিভাগ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subject_categories.php">
                            <i class="fas fa-tags me-2"></i> বিষয় ক্যাটাগরি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sessions.php">
                            <i class="fas fa-calendar-alt me-2"></i> সেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>বিভাগ অনুযায়ী বিষয়ের ধরন</h2>
                        <p class="text-muted">প্রতিটি বিভাগের জন্য বিষয়ের ধরন (আবশ্যিক/ঐচ্ছিক/৪র্থ) নির্ধারণ করুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="subjects.php" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>বিষয় পৃষ্ঠায় ফিরে যান
                        </a>
                    </div>
                </div>

                <?php if (!empty($successMessages)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <h5 class="alert-heading"><i class="fas fa-check-circle me-2"></i>সফল!</h5>
                        <ul class="mb-0">
                            <?php foreach ($successMessages as $message): ?>
                                <li><?php echo $message; ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($errorMessages)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <h5 class="alert-heading"><i class="fas fa-exclamation-circle me-2"></i>ত্রুটি!</h5>
                        <ul class="mb-0">
                            <?php foreach ($errorMessages as $message): ?>
                                <li><?php echo $message; ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if ($selectedSubjectInfo): ?>
                    <div class="alert alert-info">
                        <div class="d-flex align-items-center">
                            <div>
                                <h5 class="alert-heading mb-1"><i class="fas fa-book-open me-2"></i><?php echo htmlspecialchars($selectedSubjectInfo['subject_name']); ?></h5>
                                <p class="mb-0">বিষয় কোড: <strong><?php echo htmlspecialchars($selectedSubjectInfo['subject_code']); ?></strong> |
                                বিভাগ: <strong><?php echo htmlspecialchars($selectedSubjectInfo['department_name'] ?? 'N/A'); ?></strong></p>
                                <p class="mt-2 mb-0">নিম্নলিখিত বিভাগগুলির জন্য এই বিষয়ের ধরন নির্ধারণ করুন</p>
                            </div>
                            <div class="ms-auto">
                                <a href="edit_subject.php?id=<?php echo $selectedSubject; ?>" class="btn btn-outline-primary">
                                    <i class="fas fa-arrow-left me-2"></i>বিষয়ে ফিরে যান
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Department Selection -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">বিভাগ নির্বাচন করুন</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="create_department_subject_types.php" class="row g-3">
                            <?php if ($selectedSubjectInfo): ?>
                                <input type="hidden" name="subject_id" value="<?php echo $selectedSubject; ?>">
                            <?php endif; ?>

                            <div class="col-md-8">
                                <select name="department_id" class="form-select" required>
                                    <option value="">বিভাগ নির্বাচন করুন</option>
                                    <?php if ($departments && $departments->num_rows > 0): ?>
                                        <?php
                                        // Reset the departments result pointer
                                        $departments->data_seek(0);
                                        while ($dept = $departments->fetch_assoc()):
                                        ?>
                                            <option value="<?php echo $dept['id']; ?>" <?php echo ($selectedDepartment == $dept['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($dept['department_name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-2"></i>বিভাগ অনুযায়ী দেখুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <?php if (!empty($selectedDepartment) && is_numeric($selectedDepartment)): ?>
                    <!-- Subject Type Selection -->
                    <form method="POST" action="create_department_subject_types.php">
                        <input type="hidden" name="department_id" value="<?php echo $selectedDepartment; ?>">

                        <div class="card mb-4">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">বিষয়ের ধরন নির্ধারণ করুন</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php
                                    if (isset($subjects) && $subjects && $subjects->num_rows > 0):
                                        // If a specific subject is selected, only show that subject
                                        if ($selectedSubjectInfo) {
                                            $currentType = $existingTypes[$selectedSubject] ?? 'optional';
                                            ?>
                                            <div class="col-md-6 mx-auto mb-3">
                                                <div class="card subject-card <?php echo $currentType; ?> h-100">
                                                    <div class="card-body">
                                                        <span class="type-badge badge bg-<?php echo ($currentType == 'required') ? 'primary' : (($currentType == 'optional') ? 'success' : 'danger'); ?>">
                                                            <?php echo ($currentType == 'required') ? 'আবশ্যিক' : (($currentType == 'optional') ? 'ঐচ্ছিক' : '৪র্থ'); ?>
                                                        </span>
                                                        <h5 class="card-title"><?php echo htmlspecialchars($selectedSubjectInfo['subject_name']); ?></h5>
                                                        <p class="text-muted"><?php echo htmlspecialchars($selectedSubjectInfo['subject_code']); ?></p>

                                                        <div class="mt-3">
                                                            <label class="form-label">বিষয়ের ধরন:</label>
                                                            <select name="subject_type[<?php echo $selectedSubject; ?>]" class="form-select subject-type-select" data-subject-id="<?php echo $selectedSubject; ?>">
                                                                <option value="required" <?php echo ($currentType == 'required') ? 'selected' : ''; ?>>আবশ্যিক</option>
                                                                <option value="optional" <?php echo ($currentType == 'optional') ? 'selected' : ''; ?>>ঐচ্ছিক</option>
                                                                <option value="fourth" <?php echo ($currentType == 'fourth') ? 'selected' : ''; ?>>৪র্থ বিষয়</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php
                                        } else {
                                            // Show subjects for this department
                                            while ($subject = $subjects->fetch_assoc()):
                                                $currentType = $existingTypes[$subject['id']] ?? 'optional';
                                            ?>
                                            <div class="col-md-4 mb-3">
                                                <div class="card subject-card <?php echo $currentType; ?> h-100">
                                                    <div class="card-body">
                                                        <span class="type-badge badge bg-<?php echo ($currentType == 'required') ? 'primary' : (($currentType == 'optional') ? 'success' : 'danger'); ?>">
                                                            <?php echo ($currentType == 'required') ? 'আবশ্যিক' : (($currentType == 'optional') ? 'ঐচ্ছিক' : '৪র্থ'); ?>
                                                        </span>
                                                        <h5 class="card-title"><?php echo htmlspecialchars($subject['subject_name']); ?></h5>
                                                        <p class="text-muted"><?php echo htmlspecialchars($subject['subject_code']); ?></p>

                                                        <div class="mt-3">
                                                            <label class="form-label">বিষয়ের ধরন:</label>
                                                            <select name="subject_type[<?php echo $subject['id']; ?>]" class="form-select subject-type-select" data-subject-id="<?php echo $subject['id']; ?>">
                                                                <option value="required" <?php echo ($currentType == 'required') ? 'selected' : ''; ?>>আবশ্যিক</option>
                                                                <option value="optional" <?php echo ($currentType == 'optional') ? 'selected' : ''; ?>>ঐচ্ছিক</option>
                                                                <option value="fourth" <?php echo ($currentType == 'fourth') ? 'selected' : ''; ?>>৪র্থ বিষয়</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php
                                            endwhile;
                                        }
                                    else:
                                    ?>
                                        <div class="col-12">
                                            <div class="alert alert-info">
                                                <i class="fas fa-info-circle me-2"></i>এই বিভাগের জন্য কোন বিষয় পাওয়া যায়নি।
                                                <div class="mt-3">
                                                    <a href="add_subject_to_department.php?department_id=<?php echo $selectedDepartment; ?>" class="btn btn-primary btn-sm">
                                                        <i class="fas fa-plus-circle me-2"></i>এই বিভাগে বিষয় যোগ করুন
                                                    </a>
                                                    <a href="subjects.php" class="btn btn-outline-secondary btn-sm ms-2">
                                                        <i class="fas fa-list me-2"></i>সকল বিষয় দেখুন
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="card-footer">
                                <button type="submit" name="save_types" class="btn btn-success">
                                    <i class="fas fa-save me-2"></i>বিষয়ের ধরন সংরক্ষণ করুন
                                </button>
                            </div>
                        </div>
                    </form>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>দয়া করে বিষয়ের ধরন নির্ধারণ করতে একটি বিভাগ নির্বাচন করুন।
                    </div>
                <?php endif; ?>

                <!-- Instructions -->
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i>নির্দেশনা</h5>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li><strong>আবশ্যিক বিষয়:</strong> শিক্ষার্থীদের জন্য বাধ্যতামূলক বিষয়সমূহ</li>
                            <li><strong>ঐচ্ছিক বিষয়:</strong> শিক্ষার্থীরা নির্বাচন করতে পারে এমন বিষয়সমূহ</li>
                            <li><strong>৪র্থ বিষয়:</strong> শিক্ষার্থীরা ৪র্থ বিষয় হিসেবে নির্বাচন করতে পারে এমন বিষয়সমূহ</li>
                        </ul>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>একটি বিষয় বিভিন্ন বিভাগের জন্য ভিন্ন ভিন্ন ধরনের হতে পারে। উদাহরণস্বরূপ, গণিত বিজ্ঞান বিভাগের জন্য আবশ্যিক কিন্তু কলা বিভাগের জন্য ঐচ্ছিক হতে পারে।
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Update card class when subject type changes
        document.addEventListener('DOMContentLoaded', function() {
            const typeSelects = document.querySelectorAll('.subject-type-select');

            typeSelects.forEach(select => {
                select.addEventListener('change', function() {
                    const subjectId = this.getAttribute('data-subject-id');
                    const card = this.closest('.subject-card');

                    // Remove existing type classes
                    card.classList.remove('required', 'optional', 'fourth');

                    // Add new type class
                    card.classList.add(this.value);

                    // Update badge
                    const badge = card.querySelector('.type-badge');
                    badge.classList.remove('bg-primary', 'bg-success', 'bg-danger');

                    if (this.value === 'required') {
                        badge.classList.add('bg-primary');
                        badge.textContent = 'আবশ্যিক';
                    } else if (this.value === 'optional') {
                        badge.classList.add('bg-success');
                        badge.textContent = 'ঐচ্ছিক';
                    } else {
                        badge.classList.add('bg-danger');
                        badge.textContent = '৪র্থ';
                    }
                });
            });
        });
    </script>
</body>
</html>
