<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';
require_once '../includes/Encryption.php';

// Create backup directory if it doesn't exist
$backupDir = '../backups';
if (!file_exists($backupDir)) {
    mkdir($backupDir, 0777, true);
}

// Function to create database backup
function backupDatabase($conn, $backupDir) {
    global $dbname;

    // Create encryption object
    $encryption = new Encryption($_SESSION['userId'] . '_' . gethostname());

    $tables = array();
    $result = $conn->query("SHOW TABLES");

    while ($row = $result->fetch_row()) {
        $tables[] = $row[0];
    }

    $tempFile = $backupDir . '/temp_' . time() . '.sql';
    $backupFile = $backupDir . '/' . $dbname . '_' . date("Y-m-d_H-i-s") . '.enc';
    $output = "-- Database Backup for $dbname - " . date("Y-m-d H:i:s") . "\n";
    $output .= "-- This backup is encrypted and can only be restored on this computer\n\n";

    foreach ($tables as $table) {
        try {
            // Check if table exists
            $tableExistsQuery = "SHOW TABLES LIKE '$table'";
            $tableExists = $conn->query($tableExistsQuery);

            if ($tableExists && $tableExists->num_rows > 0) {
                // Get table structure
                $result = $conn->query("SHOW CREATE TABLE `$table`");
                if ($result && $row = $result->fetch_row()) {
                    $output .= "\n\n" . $row[1] . ";\n\n";

                    // Get table data
                    $dataResult = $conn->query("SELECT * FROM `$table`");
                    if ($dataResult) {
                        $numFields = $dataResult->field_count;

                        while ($row = $dataResult->fetch_row()) {
                            $output .= "INSERT INTO `$table` VALUES(";

                            for ($i = 0; $i < $numFields; $i++) {
                                if (isset($row[$i])) {
                                    // Escape special characters
                                    $row[$i] = str_replace("\n", "\\n", addslashes($row[$i]));
                                    $output .= '"' . $row[$i] . '"';
                                } else {
                                    $output .= 'NULL';
                                }

                                if ($i < ($numFields - 1)) {
                                    $output .= ',';
                                }
                            }

                            $output .= ");\n";
                        }
                    }

                    $output .= "\n\n";
                }
            }
        } catch (Exception $e) {
            // Skip this table and continue with others
            continue;
        }
    }

    // Save the SQL to a temporary file
    if (file_put_contents($tempFile, $output)) {
        // Encrypt the file
        if ($encryption->encryptFile($tempFile, $backupFile, true)) {
            // Delete the temporary file
            unlink($tempFile);
            return basename($backupFile);
        } else {
            // Delete the temporary file
            unlink($tempFile);
            return false;
        }
    } else {
        return false;
    }
}

// Function to restore database from backup
function restoreDatabase($conn, $backupFile) {
    // Check if this is an encrypted backup
    $isEncrypted = (pathinfo($backupFile, PATHINFO_EXTENSION) === 'enc');

    if ($isEncrypted) {
        // Create encryption object
        $encryption = new Encryption($_SESSION['userId'] . '_' . gethostname());

        // Create a temporary file for the decrypted content
        $tempFile = dirname($backupFile) . '/temp_restore_' . time() . '.sql';

        // Decrypt the file
        if (!$encryption->decryptFile($backupFile, $tempFile, true)) {
            return "ব্যাকআপ ফাইল ডিক্রিপ্ট করতে ব্যর্থ। এই ব্যাকআপ সম্ভবত অন্য কম্পিউটারে তৈরি করা হয়েছিল।";
        }

        // Use the decrypted file
        $sql = file_get_contents($tempFile);

        // Delete the temporary file
        unlink($tempFile);
    } else {
        // Regular SQL backup
        $sql = file_get_contents($backupFile);
    }

    // Split SQL by semicolon
    $queries = explode(';', $sql);

    // Begin transaction
    $conn->begin_transaction();

    try {
        foreach ($queries as $query) {
            $query = trim($query);

            if (!empty($query) && !preg_match('/^--/', $query)) { // Skip comments
                try {
                    $conn->query($query);
                } catch (mysqli_sql_exception $e) {
                    // Log the error but continue with other queries
                    error_log("Error executing query: " . $e->getMessage());
                }
            }
        }

        // Commit transaction
        $conn->commit();
        return true;
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        return $e->getMessage();
    }
}

// Function to get list of backup files
function getBackupFiles($backupDir) {
    $files = array();

    if (is_dir($backupDir)) {
        $dirHandle = opendir($backupDir);

        while (($file = readdir($dirHandle)) !== false) {
            $extension = pathinfo($file, PATHINFO_EXTENSION);
            if ($extension === 'sql' || $extension === 'enc') {
                $files[] = $file;
            }
        }

        closedir($dirHandle);
        rsort($files); // Sort by newest first
    }

    return $files;
}

// Handle backup creation
if (isset($_POST['create_backup'])) {
    $backupFile = backupDatabase($conn, $backupDir);

    if ($backupFile) {
        $successMessage = "ব্যাকআপ সফলভাবে তৈরি করা হয়েছে: $backupFile";
    } else {
        $errorMessage = "ব্যাকআপ তৈরি করতে সমস্যা হয়েছে।";
    }
}

// Handle backup restoration
if (isset($_POST['restore_backup']) && isset($_POST['backup_file'])) {
    $backupFile = $backupDir . '/' . $_POST['backup_file'];

    if (file_exists($backupFile)) {
        $result = restoreDatabase($conn, $backupFile);

        if ($result === true) {
            $successMessage = "ডাটাবেজ সফলভাবে পুনরুদ্ধার করা হয়েছে।";
        } else {
            $errorMessage = "ডাটাবেজ পুনরুদ্ধার করতে সমস্যা হয়েছে: $result";
        }
    } else {
        $errorMessage = "ব্যাকআপ ফাইল পাওয়া যায়নি।";
    }
}

// Handle backup deletion
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $backupFile = $backupDir . '/' . $_GET['delete'];

    if (file_exists($backupFile) && unlink($backupFile)) {
        $successMessage = "ব্যাকআপ ফাইল সফলভাবে মুছে ফেলা হয়েছে।";
    } else {
        $errorMessage = "ব্যাকআপ ফাইল মুছতে সমস্যা হয়েছে।";
    }
}

// Handle backup download
if (isset($_GET['download']) && !empty($_GET['download'])) {
    $backupFile = $backupDir . '/' . $_GET['download'];

    if (file_exists($backupFile)) {
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . basename($backupFile) . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        header('Content-Length: ' . filesize($backupFile));
        readfile($backupFile);
        exit;
    } else {
        $errorMessage = "ব্যাকআপ ফাইল পাওয়া যায়নি।";
    }
}

// Get list of backup files
$backupFiles = getBackupFiles($backupDir);

// Check if auto backup is enabled
$autoBackupEnabled = false;
$autoBackupInterval = 'daily';

$configFile = '../config/backup_config.php';
if (file_exists($configFile)) {
    include $configFile;
    $autoBackupEnabled = isset($config['auto_backup_enabled']) ? $config['auto_backup_enabled'] : false;
    $autoBackupInterval = isset($config['auto_backup_interval']) ? $config['auto_backup_interval'] : 'daily';
}

// Handle auto backup settings
if (isset($_POST['save_settings'])) {
    $autoBackupEnabled = isset($_POST['auto_backup_enabled']);
    $autoBackupInterval = $_POST['auto_backup_interval'];

    // Create config directory if it doesn't exist
    $configDir = '../config';
    if (!file_exists($configDir)) {
        mkdir($configDir, 0777, true);
    }

    // Save settings to config file
    $config = array(
        'auto_backup_enabled' => $autoBackupEnabled,
        'auto_backup_interval' => $autoBackupInterval,
        'last_backup_time' => time()
    );

    $configContent = "<?php\n\$config = " . var_export($config, true) . ";\n?>";

    if (file_put_contents($configFile, $configContent)) {
        $successMessage = "সেটিংস সফলভাবে সংরক্ষণ করা হয়েছে।";
    } else {
        $errorMessage = "সেটিংস সংরক্ষণ করতে সমস্যা হয়েছে।";
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>ডাটাবেজ ব্যাকআপ - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include_once '../includes/admin_sidebar.php'; ?>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>ডাটাবেজ ব্যাকআপ ম্যানেজমেন্ট</h2>
                        <p class="text-muted">ডাটাবেজ ব্যাকআপ নিন, পুনরুদ্ধার করুন এবং ম্যানেজ করুন</p>
                    </div>
                </div>

                <?php if (isset($errorMessage)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $errorMessage; ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($successMessage)): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo $successMessage; ?>
                    </div>
                <?php endif; ?>

                <!-- Nav Tabs -->
                <ul class="nav nav-tabs mb-4" id="myTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="backup-tab" data-bs-toggle="tab" data-bs-target="#backup" type="button" role="tab" aria-controls="backup" aria-selected="true">
                            <i class="fas fa-download me-2"></i>ব্যাকআপ
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="restore-tab" data-bs-toggle="tab" data-bs-target="#restore" type="button" role="tab" aria-controls="restore" aria-selected="false">
                            <i class="fas fa-upload me-2"></i>পুনরুদ্ধার
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab" aria-controls="settings" aria-selected="false">
                            <i class="fas fa-cog me-2"></i>সেটিংস
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link" href="portable_backup.php">
                            <i class="fas fa-exchange-alt me-2"></i>পোর্টেবল ব্যাকআপ
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link" href="database_transfer.php">
                            <i class="fas fa-sync-alt me-2"></i>ডাটাবেজ ট্রান্সফার
                        </a>
                    </li>
                </ul>

                <div class="tab-content" id="myTabContent">
                    <!-- Backup Tab -->
                    <div class="tab-pane fade show active" id="backup" role="tabpanel" aria-labelledby="backup-tab">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">ডাটাবেজ ব্যাকআপ নিন</h5>
                            </div>
                            <div class="card-body">
                                <p>বর্তমান ডাটাবেজের একটি ব্যাকআপ তৈরি করুন। এটি আপনার সমস্ত টেবিল এবং ডাটা সংরক্ষণ করবে।</p>
                                <form method="POST" action="database_backup.php">
                                    <button type="submit" name="create_backup" class="btn btn-primary">
                                        <i class="fas fa-download me-2"></i>ব্যাকআপ তৈরি করুন
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Restore Tab -->
                    <div class="tab-pane fade" id="restore" role="tabpanel" aria-labelledby="restore-tab">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">ব্যাকআপ থেকে পুনরুদ্ধার করুন</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>সতর্কতা: ডাটাবেজ পুনরুদ্ধার করলে বর্তমান সমস্ত ডাটা মুছে যাবে এবং ব্যাকআপ ফাইল থেকে ডাটা পুনরায় লোড করা হবে।
                                </div>

                                <?php if (empty($backupFiles)): ?>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>কোন ব্যাকআপ ফাইল পাওয়া যায়নি। দয়া করে প্রথমে একটি ব্যাকআপ তৈরি করুন।
                                    </div>
                                <?php else: ?>
                                    <form method="POST" action="database_backup.php" onsubmit="return confirm('আপনি কি নিশ্চিত যে আপনি ডাটাবেজ পুনরুদ্ধার করতে চান? বর্তমান সমস্ত ডাটা মুছে যাবে।');">
                                        <div class="mb-3">
                                            <label for="backup_file" class="form-label">ব্যাকআপ ফাইল নির্বাচন করুন</label>
                                            <select class="form-select" id="backup_file" name="backup_file" required>
                                                <?php foreach ($backupFiles as $file): ?>
                                                    <option value="<?php echo $file; ?>">
                                                        <?php echo $file; ?> (<?php echo date("F j, Y, g:i a", filemtime($backupDir . '/' . $file)); ?>)
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <button type="submit" name="restore_backup" class="btn btn-warning">
                                            <i class="fas fa-upload me-2"></i>ডাটাবেজ পুনরুদ্ধার করুন
                                        </button>
                                    </form>
                                <?php endif; ?>

                                <hr>

                                <h5>ব্যাকআপ ফাইল তালিকা</h5>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>ফাইল নাম</th>
                                                <th>তারিখ</th>
                                                <th>সাইজ</th>
                                                <th>ধরন</th>
                                                <th>অ্যাকশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (empty($backupFiles)): ?>
                                                <tr>
                                                    <td colspan="5" class="text-center">কোন ব্যাকআপ ফাইল পাওয়া যায়নি</td>
                                                </tr>
                                            <?php else: ?>
                                                <?php foreach ($backupFiles as $file): ?>
                                                    <?php $isEncrypted = (pathinfo($file, PATHINFO_EXTENSION) === 'enc'); ?>
                                                    <tr>
                                                        <td><?php echo $file; ?></td>
                                                        <td><?php echo date("F j, Y, g:i a", filemtime($backupDir . '/' . $file)); ?></td>
                                                        <td><?php echo round(filesize($backupDir . '/' . $file) / 1024, 2); ?> KB</td>
                                                        <td>
                                                            <?php if ($isEncrypted): ?>
                                                                <span class="badge bg-success">এনক্রিপ্টেড</span>
                                                                <i class="fas fa-lock text-success" title="এই ব্যাকআপ ফাইলটি এনক্রিপ্ট করা আছে এবং কেবল এই কম্পিউটারেই পুনরুদ্ধার করা যাবে"></i>
                                                            <?php else: ?>
                                                                <span class="badge bg-warning">সাধারণ</span>
                                                                <i class="fas fa-unlock text-warning" title="এই ব্যাকআপ ফাইলটি এনক্রিপ্ট করা নেই এবং যে কোন কম্পিউটারে পুনরুদ্ধার করা যাবে"></i>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <a href="database_backup.php?download=<?php echo $file; ?>" class="btn btn-sm btn-primary">
                                                                <i class="fas fa-download"></i> ডাউনলোড
                                                            </a>
                                                            <a href="database_backup.php?delete=<?php echo $file; ?>" class="btn btn-sm btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই ব্যাকআপ ফাইলটি মুছতে চান?');">
                                                                <i class="fas fa-trash"></i> মুছুন
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings Tab -->
                    <div class="tab-pane fade" id="settings" role="tabpanel" aria-labelledby="settings-tab">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">অটো ব্যাকআপ সেটিংস</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="database_backup.php">
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="auto_backup_enabled" name="auto_backup_enabled" <?php echo $autoBackupEnabled ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="auto_backup_enabled">অটোমেটিক ব্যাকআপ সক্রিয় করুন</label>
                                    </div>

                                    <div class="mb-3">
                                        <label for="auto_backup_interval" class="form-label">ব্যাকআপ ইন্টারভাল</label>
                                        <select class="form-select" id="auto_backup_interval" name="auto_backup_interval">
                                            <option value="daily" <?php echo $autoBackupInterval === 'daily' ? 'selected' : ''; ?>>দৈনিক</option>
                                            <option value="weekly" <?php echo $autoBackupInterval === 'weekly' ? 'selected' : ''; ?>>সাপ্তাহিক</option>
                                            <option value="monthly" <?php echo $autoBackupInterval === 'monthly' ? 'selected' : ''; ?>>মাসিক</option>
                                        </select>
                                    </div>

                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="encrypt_backup" name="encrypt_backup" checked disabled>
                                        <label class="form-check-label" for="encrypt_backup">ব্যাকআপ এনক্রিপ্ট করুন (সুপারিশকৃত)</label>
                                        <div class="form-text text-muted">এনক্রিপ্টেড ব্যাকআপ ফাইলগুলি কেবল এই কম্পিউটারেই পুনরুদ্ধার করা যাবে, যা আপনার ডাটা সুরক্ষিত রাখবে।</div>
                                    </div>

                                    <button type="submit" name="save_settings" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>সেটিংস সংরক্ষণ করুন
                                    </button>
                                </form>

                                <hr>

                                <div class="alert alert-info">
                                    <h5><i class="fas fa-info-circle me-2"></i>অটো ব্যাকআপ কিভাবে কাজ করে</h5>
                                    <p>অটো ব্যাকআপ সক্রিয় করলে, সিস্টেম নিয়মিত ব্যাকআপ নেবে। এটি কাজ করার জন্য:</p>
                                    <ol>
                                        <li>অ্যাডমিন প্যানেলে লগইন করা প্রয়োজন</li>
                                        <li>নির্ধারিত সময় অনুযায়ী ব্যাকআপ নেওয়া হবে</li>
                                        <li>ব্যাকআপ ফাইলগুলি <code>backups</code> ফোল্ডারে সংরক্ষিত হবে</li>
                                    </ol>
                                    <p>আরও নিরাপত্তার জন্য, নিয়মিত ম্যানুয়ালি ব্যাকআপ নিন এবং ব্যাকআপ ফাইলগুলি আপনার কম্পিউটারে ডাউনলোড করে রাখুন।</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
