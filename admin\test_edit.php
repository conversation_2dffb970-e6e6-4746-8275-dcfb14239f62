<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Simple test page
echo "<h1>Test Edit Page</h1>";
echo "<p>This is a test page to check if PHP is working correctly.</p>";

// Get student ID from URL
if (isset($_GET['id'])) {
    $studentId = $_GET['id'];
    echo "<p>Student ID: " . $studentId . "</p>";
    
    // Try to get student data
    $studentQuery = "SELECT * FROM students WHERE id = ?";
    $stmt = $conn->prepare($studentQuery);
    $stmt->bind_param("i", $studentId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $student = $result->fetch_assoc();
        echo "<p>Student found: " . $student['first_name'] . " " . $student['last_name'] . "</p>";
    } else {
        echo "<p>No student found with ID: " . $studentId . "</p>";
    }
} else {
    echo "<p>No ID provided</p>";
}
?>

<p><a href="students.php">Back to Students</a></p>
