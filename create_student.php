<?php
require_once 'includes/dbh.inc.php';

echo "<h2>Creating Student User</h2>";

// Create a student user
$username = "student";
$password = password_hash("password123", PASSWORD_DEFAULT);
$userType = "student";

// First check if user already exists
$check = $conn->query("SELECT * FROM users WHERE username = '$username' AND user_type = '$userType'");

if ($check->num_rows > 0) {
    echo "<p>Student user already exists.</p>";
    $row = $check->fetch_assoc();
    $userId = $row['id'];
} else {
    // Insert into users table
    $insertUser = "INSERT INTO users (username, password, user_type) VALUES ('$username', '$password', '$userType')";
    
    if ($conn->query($insertUser)) {
        $userId = $conn->insert_id;
        echo "<p>Student user created successfully with ID: $userId</p>";
    } else {
        echo "<p>Error creating user: " . $conn->error . "</p>";
        exit;
    }
}

// Check if student record exists
$checkStudent = $conn->query("SELECT * FROM students WHERE user_id = $userId");

if ($checkStudent->num_rows > 0) {
    echo "<p>Student record already exists.</p>";
} else {
    // Create student record
    $studentId = 'STU' . date('Y') . str_pad($userId, 4, '0', STR_PAD_LEFT);
    $insertStudent = "INSERT INTO students (student_id, first_name, last_name, gender, user_id) 
                      VALUES ('$studentId', 'Test', 'Student', 'male', $userId)";
    
    if ($conn->query($insertStudent)) {
        echo "<p>Student record created with ID: $studentId</p>";
    } else {
        echo "<p>Error creating student record: " . $conn->error . "</p>";
    }
}

echo "<h3>Login Details:</h3>";
echo "<p>Username: student</p>";
echo "<p>Password: password123</p>";
echo "<p>User Type: student</p>";

echo "<p><a href='index.php'>Go to Login Page</a></p>";
?> 