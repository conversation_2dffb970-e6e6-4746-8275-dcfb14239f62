document.addEventListener('deviceready', onDeviceReady, false);

// Website URL to load
const websiteUrl = 'http://yourwebsite.com/zfaw';
let webView = null;

function onDeviceReady() {
    console.log('Device is ready');
    
    // Check network connection
    checkConnection();
    
    // Listen for online/offline events
    document.addEventListener('online', onOnline, false);
    document.addEventListener('offline', onOffline, false);
    
    // Add retry button listener
    document.getElementById('retry-button').addEventListener('click', retryConnection);
}

function checkConnection() {
    console.log('Checking connection...');
    
    const networkState = navigator.connection.type;
    const states = {};
    states[Connection.UNKNOWN] = 'Unknown connection';
    states[Connection.ETHERNET] = 'Ethernet connection';
    states[Connection.WIFI] = 'WiFi connection';
    states[Connection.CELL_2G] = 'Cell 2G connection';
    states[Connection.CELL_3G] = 'Cell 3G connection';
    states[Connection.CELL_4G] = 'Cell 4G connection';
    states[Connection.CELL] = 'Cell generic connection';
    states[Connection.NONE] = 'No network connection';
    
    console.log('Connection type: ' + states[networkState]);
    
    if (networkState !== Connection.NONE) {
        // Create WebView
        loadWebsite();
    } else {
        // Show offline message
        showOfflineMessage();
    }
}

function loadWebsite() {
    console.log('Loading website...');
    
    // Hide offline message if it's visible
    document.getElementById('offline-message').style.display = 'none';
    
    // Show loader
    document.getElementById('loader').style.display = 'flex';
    
    // Create InAppBrowser
    webView = cordova.InAppBrowser.open(websiteUrl, '_self', 'location=no,hidden=yes');
    
    webView.addEventListener('loadstop', function() {
        console.log('Website loaded');
        webView.show();
        document.getElementById('loader').style.display = 'none';
    });
    
    webView.addEventListener('loaderror', function(err) {
        console.error('Error loading website:', err.message);
        webView.close();
        showOfflineMessage();
    });
}

function showOfflineMessage() {
    console.log('Showing offline message');
    document.getElementById('loader').style.display = 'none';
    document.getElementById('offline-message').style.display = 'block';
}

function onOnline() {
    console.log('Device is online');
    loadWebsite();
}

function onOffline() {
    console.log('Device is offline');
    if (webView) {
        webView.close();
        webView = null;
    }
    showOfflineMessage();
}

function retryConnection() {
    console.log('Retrying connection...');
    checkConnection();
} 