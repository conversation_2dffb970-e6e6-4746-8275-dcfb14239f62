<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get class group info for Junior (Classes 6-8)
$groupCode = 'JUNIOR';
$groupQuery = "SELECT * FROM class_groups WHERE group_code = '$groupCode' AND is_active = 1";
$groupResult = $conn->query($groupQuery);
$groupInfo = $groupResult ? $groupResult->fetch_assoc() : null;

if (!$groupInfo) {
    header("Location: class_based_exam_dashboard.php");
    exit();
}

// Get classes for this group (6-8)
$classesQuery = "SELECT * FROM classes WHERE class_name IN ('৬', '৭', '৮', '6', '7', '8') ORDER BY class_name";
$classesResult = $conn->query($classesQuery);

// Get subjects for these classes
$subjectsQuery = "SELECT DISTINCT s.* FROM subjects s 
                  INNER JOIN classes c ON s.class_id = c.id 
                  WHERE c.class_name IN ('৬', '৭', '৮', '6', '7', '8') 
                  ORDER BY s.subject_name";
$subjectsResult = $conn->query($subjectsQuery);

// Get exams from junior table
$examTableName = "exams_junior";
$examsQuery = "SELECT * FROM $examTableName ORDER BY exam_date DESC LIMIT 10";
$examsResult = $conn->query($examsQuery);

// Handle form submission for creating new exam
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_exam'])) {
    $examName = $_POST['exam_name'];
    $examType = $_POST['exam_type'];
    $classId = $_POST['class_id'];
    $subjectId = $_POST['subject_id'];
    $examDate = $_POST['exam_date'];
    $startTime = $_POST['start_time'];
    $endTime = $_POST['end_time'];
    $totalMarks = $_POST['total_marks'];
    $passingMarks = $_POST['passing_marks'];
    $instructions = $_POST['instructions'];
    $createdBy = $_SESSION['userId'];
    
    $insertQuery = "INSERT INTO $examTableName (exam_name, exam_type, class_id, subject_id, exam_date, start_time, end_time, total_marks, passing_marks, instructions, created_by) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($insertQuery);
    $stmt->bind_param("ssiisssiisi", $examName, $examType, $classId, $subjectId, $examDate, $startTime, $endTime, $totalMarks, $passingMarks, $instructions, $createdBy);
    
    if ($stmt->execute()) {
        $successMessage = "পরীক্ষা সফলভাবে তৈরি হয়েছে!";
        // Refresh exams list
        $examsResult = $conn->query($examsQuery);
    } else {
        $errorMessage = "পরীক্ষা তৈরি করতে সমস্যা হয়েছে: " . $conn->error;
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ক্লাস ৬-৮ পরীক্ষা ব্যবস্থাপনা - স্কুল ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/hind-siliguri.css">

    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: #f8f9fa;
        }
        .page-header {
            background: linear-gradient(135deg, #45B7D1, #96C93D);
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
            border-radius: 0 0 20px 20px;
        }
        .exam-card {
            border-radius: 10px;
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .exam-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.2);
        }
        .form-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .btn-primary-custom {
            background: linear-gradient(135deg, #45B7D1, #96C93D);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
        }
        .btn-primary-custom:hover {
            background: linear-gradient(135deg, #3A9BC1, #85B82D);
        }
        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #45B7D1;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 bg-dark text-white min-vh-100">
                <div class="p-3">
                    <h5>মেনু</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link text-white" href="dashboard.php">
                                <i class="fas fa-home me-2"></i>ড্যাশবোর্ড
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" href="class_based_exam_dashboard.php">
                                <i class="fas fa-layer-group me-2"></i>ক্লাস ভিত্তিক পরীক্ষা
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white active" href="#">
                                <i class="fas fa-book me-2"></i>ক্লাস ৬-৮ পরীক্ষা
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <!-- Page Header -->
                <div class="page-header">
                    <div class="container">
                        <div class="row align-items-center">
                            <div class="col">
                                <h1 class="mb-2">ক্লাস ৬-৮ পরীক্ষা ব্যবস্থাপনা</h1>
                                <p class="mb-0 opacity-75">জুনিয়র শ্রেণীর পরীক্ষা পরিচালনা</p>
                            </div>
                            <div class="col-auto">
                                <a href="class_based_exam_dashboard.php" class="btn btn-light">
                                    <i class="fas fa-arrow-left me-2"></i>ফিরে যান
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="container">
                    <!-- Success/Error Messages -->
                    <?php if (isset($successMessage)): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i><?php echo $successMessage; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($errorMessage)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i><?php echo $errorMessage; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Statistics Row -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number">
                                    <?php 
                                    $totalExamsQuery = "SELECT COUNT(*) as count FROM $examTableName";
                                    $totalExamsResult = $conn->query($totalExamsQuery);
                                    echo $totalExamsResult ? $totalExamsResult->fetch_assoc()['count'] : 0;
                                    ?>
                                </div>
                                <div class="text-muted">মোট পরীক্ষা</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number text-success">
                                    <?php 
                                    $upcomingExamsQuery = "SELECT COUNT(*) as count FROM $examTableName WHERE exam_date >= CURDATE()";
                                    $upcomingExamsResult = $conn->query($upcomingExamsQuery);
                                    echo $upcomingExamsResult ? $upcomingExamsResult->fetch_assoc()['count'] : 0;
                                    ?>
                                </div>
                                <div class="text-muted">আসন্ন পরীক্ষা</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number text-info">
                                    <?php 
                                    $completedExamsQuery = "SELECT COUNT(*) as count FROM $examTableName WHERE exam_date < CURDATE()";
                                    $completedExamsResult = $conn->query($completedExamsQuery);
                                    echo $completedExamsResult ? $completedExamsResult->fetch_assoc()['count'] : 0;
                                    ?>
                                </div>
                                <div class="text-muted">সম্পন্ন পরীক্ষা</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number text-warning">
                                    <?php 
                                    $todayExamsQuery = "SELECT COUNT(*) as count FROM $examTableName WHERE exam_date = CURDATE()";
                                    $todayExamsResult = $conn->query($todayExamsQuery);
                                    echo $todayExamsResult ? $todayExamsResult->fetch_assoc()['count'] : 0;
                                    ?>
                                </div>
                                <div class="text-muted">আজকের পরীক্ষা</div>
                            </div>
                        </div>
                    </div>

                    <!-- Create New Exam Form -->
                    <div class="form-section">
                        <h3 class="mb-4"><i class="fas fa-plus-circle me-2"></i>নতুন পরীক্ষা তৈরি করুন</h3>
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="exam_name" class="form-label">পরীক্ষার নাম *</label>
                                        <input type="text" class="form-control" id="exam_name" name="exam_name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="exam_type" class="form-label">পরীক্ষার ধরন *</label>
                                        <select class="form-select" id="exam_type" name="exam_type" required>
                                            <option value="">নির্বাচন করুন</option>
                                            <option value="সাপ্তাহিক পরীক্ষা">সাপ্তাহিক পরীক্ষা</option>
                                            <option value="মাসিক পরীক্ষা">মাসিক পরীক্ষা</option>
                                            <option value="ত্রৈমাসিক পরীক্ষা">ত্রৈমাসিক পরীক্ষা</option>
                                            <option value="অর্ধবার্ষিক পরীক্ষা">অর্ধবার্ষিক পরীক্ষা</option>
                                            <option value="বার্ষিক পরীক্ষা">বার্ষিক পরীক্ষা</option>
                                            <option value="নির্বাচনী পরীক্ষা">নির্বাচনী পরীক্ষা</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="class_id" class="form-label">ক্লাস *</label>
                                        <select class="form-select" id="class_id" name="class_id" required>
                                            <option value="">নির্বাচন করুন</option>
                                            <?php if ($classesResult && $classesResult->num_rows > 0): ?>
                                                <?php while ($class = $classesResult->fetch_assoc()): ?>
                                                    <option value="<?php echo $class['id']; ?>">
                                                        ক্লাস <?php echo htmlspecialchars($class['class_name']); ?>
                                                    </option>
                                                <?php endwhile; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="subject_id" class="form-label">বিষয় *</label>
                                        <select class="form-select" id="subject_id" name="subject_id" required>
                                            <option value="">নির্বাচন করুন</option>
                                            <?php if ($subjectsResult && $subjectsResult->num_rows > 0): ?>
                                                <?php while ($subject = $subjectsResult->fetch_assoc()): ?>
                                                    <option value="<?php echo $subject['id']; ?>">
                                                        <?php echo htmlspecialchars($subject['subject_name']); ?>
                                                    </option>
                                                <?php endwhile; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="exam_date" class="form-label">পরীক্ষার তারিখ *</label>
                                        <input type="date" class="form-control" id="exam_date" name="exam_date" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="start_time" class="form-label">শুরুর সময়</label>
                                        <input type="time" class="form-control" id="start_time" name="start_time">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="end_time" class="form-label">শেষের সময়</label>
                                        <input type="time" class="form-control" id="end_time" name="end_time">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="total_marks" class="form-label">মোট নম্বর *</label>
                                        <input type="number" class="form-control" id="total_marks" name="total_marks" value="100" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="passing_marks" class="form-label">পাশের নম্বর *</label>
                                        <input type="number" class="form-control" id="passing_marks" name="passing_marks" value="33" required>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="instructions" class="form-label">নির্দেশনা</label>
                                <textarea class="form-control" id="instructions" name="instructions" rows="3" placeholder="পরীক্ষার জন্য বিশেষ নির্দেশনা লিখুন..."></textarea>
                            </div>

                            <button type="submit" name="create_exam" class="btn btn-primary-custom">
                                <i class="fas fa-save me-2"></i>পরীক্ষা তৈরি করুন
                            </button>
                        </form>
                    </div>

                    <!-- Recent Exams List -->
                    <div class="form-section">
                        <h3 class="mb-4"><i class="fas fa-list me-2"></i>সাম্প্রতিক পরীক্ষাসমূহ</h3>

                        <?php if ($examsResult && $examsResult->num_rows > 0): ?>
                            <div class="row">
                                <?php while ($exam = $examsResult->fetch_assoc()): ?>
                                    <div class="col-md-6 col-lg-4">
                                        <div class="card exam-card">
                                            <div class="card-body">
                                                <h5 class="card-title"><?php echo htmlspecialchars($exam['exam_name']); ?></h5>
                                                <p class="card-text">
                                                    <small class="text-muted">
                                                        <i class="fas fa-calendar me-1"></i>
                                                        <?php echo date('d/m/Y', strtotime($exam['exam_date'])); ?>
                                                    </small><br>
                                                    <small class="text-muted">
                                                        <i class="fas fa-tag me-1"></i>
                                                        <?php echo htmlspecialchars($exam['exam_type']); ?>
                                                    </small><br>
                                                    <small class="text-muted">
                                                        <i class="fas fa-star me-1"></i>
                                                        <?php echo $exam['total_marks']; ?> নম্বর
                                                    </small>
                                                </p>
                                                <div class="d-flex gap-2">
                                                    <a href="exam_details.php?id=<?php echo $exam['id']; ?>&table=<?php echo $examTableName; ?>"
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye me-1"></i>দেখুন
                                                    </a>
                                                    <a href="exam_edit.php?id=<?php echo $exam['id']; ?>&table=<?php echo $examTableName; ?>"
                                                       class="btn btn-sm btn-outline-warning">
                                                        <i class="fas fa-edit me-1"></i>সম্পাদনা
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endwhile; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">এখনো কোন পরীক্ষা তৈরি করা হয়নি</h5>
                                <p class="text-muted">উপরের ফর্ম ব্যবহার করে প্রথম পরীক্ষা তৈরি করুন।</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Set minimum date to today
        document.getElementById('exam_date').min = new Date().toISOString().split('T')[0];

        // Auto-calculate passing marks based on total marks
        document.getElementById('total_marks').addEventListener('input', function() {
            const totalMarks = parseInt(this.value);
            if (totalMarks > 0) {
                const passingMarks = Math.round(totalMarks * 0.33);
                document.getElementById('passing_marks').value = passingMarks;
            }
        });
    </script>
</body>
</html>

<?php $conn->close(); ?>
