<?php
require_once 'includes/dbh.inc.php';

$query = "CREATE TABLE IF NOT EXISTS school_settings (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    school_name VARCHAR(255) NOT NULL,
    school_address TEXT,
    school_phone VARCHAR(50),
    school_email VARCHAR(100),
    logo_path VARCHAR(255) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($query)) {
    echo "school_settings table created successfully.";
    
    // Check if there's already a record
    $checkQuery = "SELECT COUNT(*) as count FROM school_settings";
    $result = $conn->query($checkQuery);
    $row = $result->fetch_assoc();
    
    if ($row['count'] == 0) {
        // Insert default record
        $insertQuery = "INSERT INTO school_settings (school_name, school_address, school_phone, school_email) 
                       VALUES ('Default School Name', 'Default School Address', '************', '<EMAIL>')";
        if ($conn->query($insertQuery)) {
            echo " Default settings added.";
        }
    }
} else {
    echo "Error creating school_settings table: " . $conn->error;
}
?> 