<?php
// <PERSON>rip<PERSON> to help fix PHP.ini settings for buffering issues
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>";
echo "<html><head><title>PHP.ini Buffer Fix</title></head><body>";
echo "<h1>🔧 PHP.ini Buffer Fix Instructions</h1>";

$php_ini_path = php_ini_loaded_file();
echo "<h2>📁 PHP.ini Location:</h2>";
echo "<p><strong>File:</strong> <code>" . $php_ini_path . "</code></p>";

echo "<h2>🛠️ Manual Fix Instructions:</h2>";
echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; border-left: 4px solid #007bff;'>";
echo "<h3>Step 1: Open XAMPP Control Panel</h3>";
echo "<p>1. Open XAMPP Control Panel</p>";
echo "<p>2. Click 'Config' button next to Apache</p>";
echo "<p>3. Select 'PHP (php.ini)'</p>";

echo "<h3>Step 2: Find and Modify These Settings:</h3>";
echo "<div style='background: #000; color: #00ff00; padding: 15px; border-radius: 5px; font-family: monospace;'>";
echo "; Find these lines and change them:<br>";
echo "output_buffering = Off<br>";
echo "implicit_flush = On<br>";
echo "zlib.output_compression = Off<br>";
echo "<br>";
echo "; If the lines don't exist, add them to the file<br>";
echo "</div>";

echo "<h3>Step 3: Save and Restart Apache</h3>";
echo "<p>1. Save the php.ini file</p>";
echo "<p>2. Go back to XAMPP Control Panel</p>";
echo "<p>3. Stop Apache</p>";
echo "<p>4. Start Apache again</p>";
echo "</div>";

echo "<h2>🚀 Alternative: Automatic Fix (Advanced Users)</h2>";
echo "<div style='background: #fff3cd; padding: 20px; border-radius: 5px; border-left: 4px solid #ffc107;'>";
echo "<p><strong>Warning:</strong> This will attempt to modify your php.ini file automatically.</p>";

if (isset($_POST['auto_fix'])) {
    echo "<h3>🔄 Attempting Automatic Fix...</h3>";
    
    if (is_writable($php_ini_path)) {
        $ini_content = file_get_contents($php_ini_path);
        
        // Backup original file
        $backup_path = $php_ini_path . '.backup.' . date('Y-m-d-H-i-s');
        file_put_contents($backup_path, $ini_content);
        echo "<p>✅ Backup created: <code>$backup_path</code></p>";
        
        // Modify settings
        $modifications = [
            'output_buffering' => 'Off',
            'implicit_flush' => 'On',
            'zlib.output_compression' => 'Off'
        ];
        
        foreach ($modifications as $setting => $value) {
            // Check if setting exists
            if (preg_match("/^$setting\s*=/m", $ini_content)) {
                // Replace existing setting
                $ini_content = preg_replace("/^$setting\s*=.*$/m", "$setting = $value", $ini_content);
                echo "<p>✅ Modified: $setting = $value</p>";
            } else {
                // Add new setting
                $ini_content .= "\n; Added by ZFAW Buffer Fix\n$setting = $value\n";
                echo "<p>✅ Added: $setting = $value</p>";
            }
        }
        
        // Write modified content
        if (file_put_contents($php_ini_path, $ini_content)) {
            echo "<p style='color: green;'><strong>✅ php.ini modified successfully!</strong></p>";
            echo "<p style='color: red;'><strong>⚠️ You MUST restart Apache now!</strong></p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to write to php.ini</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ php.ini file is not writable. Please use manual method.</p>";
    }
} else {
    echo "<form method='post'>";
    echo "<button type='submit' name='auto_fix' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>🚨 Auto-Fix php.ini (Use with caution!)</button>";
    echo "</form>";
}

echo "</div>";

echo "<h2>📋 Current Settings Check:</h2>";
echo "<table border='1' cellpadding='5' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Setting</th><th>Current Value</th><th>Recommended</th><th>Status</th></tr>";

$settings_check = [
    'output_buffering' => ['current' => ini_get('output_buffering'), 'recommended' => 'Off'],
    'implicit_flush' => ['current' => ini_get('implicit_flush'), 'recommended' => 'On'],
    'zlib.output_compression' => ['current' => ini_get('zlib.output_compression'), 'recommended' => 'Off']
];

foreach ($settings_check as $setting => $values) {
    $current = $values['current'];
    $recommended = $values['recommended'];
    
    // Convert boolean values to readable format
    if ($current === '1' || $current === 1) $current = 'On';
    if ($current === '' || $current === 0) $current = 'Off';
    
    $is_correct = ($current == $recommended);
    $status_color = $is_correct ? 'green' : 'red';
    $status_text = $is_correct ? '✅ Correct' : '❌ Needs Fix';
    
    echo "<tr>";
    echo "<td><strong>$setting</strong></td>";
    echo "<td>$current</td>";
    echo "<td>$recommended</td>";
    echo "<td style='color: $status_color;'>$status_text</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h2>🔄 After Making Changes:</h2>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
echo "<ol>";
echo "<li>Restart Apache in XAMPP Control Panel</li>";
echo "<li><a href='check-php-config.php' style='color: #007bff;'>Re-run the configuration check</a></li>";
echo "<li><a href='minimal-index.php' style='color: #007bff;'>Test the minimal page</a></li>";
echo "<li><a href='index.php' style='color: #007bff;'>Test the main page</a></li>";
echo "</ol>";
echo "</div>";

echo "<div style='margin: 20px 0;'>";
echo "<a href='check-php-config.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Check Config Again</a>";
echo "<a href='minimal-index.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Test Minimal Page</a>";
echo "<a href='index.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Main Page</a>";
echo "</div>";

echo "</body></html>";
?>
