<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Create events table if it doesn't exist
$tableQuery = "CREATE TABLE IF NOT EXISTS events (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    event_date DATE NOT NULL,
    event_time TIME,
    venue VARCHAR(255),
    organizer VARCHAR(255),
    status ENUM('upcoming', 'ongoing', 'completed', 'canceled') DEFAULT 'upcoming',
    created_by INT(11),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

$conn->query($tableQuery);

// Process form submission for adding new event
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] == 'add') {
        $title = $conn->real_escape_string($_POST['title']);
        $description = $conn->real_escape_string($_POST['description']);
        $event_date = $conn->real_escape_string($_POST['event_date']);
        $event_time = $conn->real_escape_string($_POST['event_time']);
        $venue = $conn->real_escape_string($_POST['venue']);
        $organizer = $conn->real_escape_string($_POST['organizer']);
        $status = $conn->real_escape_string($_POST['status']);
        $created_by = $_SESSION['userId'];

        $insertQuery = "INSERT INTO events (title, description, event_date, event_time, venue, organizer, status, created_by) 
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($insertQuery);
        $stmt->bind_param("sssssssi", $title, $description, $event_date, $event_time, $venue, $organizer, $status, $created_by);
        
        if ($stmt->execute()) {
            $success_message = "ইভেন্ট সফলভাবে যোগ করা হয়েছে।";
        } else {
            $error_message = "ইভেন্ট যোগ করতে সমস্যা হয়েছে: " . $conn->error;
        }
    }
    // Process form submission for editing event
    else if (isset($_POST['action']) && $_POST['action'] == 'edit') {
        $id = intval($_POST['id']);
        $title = $conn->real_escape_string($_POST['title']);
        $description = $conn->real_escape_string($_POST['description']);
        $event_date = $conn->real_escape_string($_POST['event_date']);
        $event_time = $conn->real_escape_string($_POST['event_time']);
        $venue = $conn->real_escape_string($_POST['venue']);
        $organizer = $conn->real_escape_string($_POST['organizer']);
        $status = $conn->real_escape_string($_POST['status']);

        $updateQuery = "UPDATE events SET 
                      title = ?, 
                      description = ?, 
                      event_date = ?, 
                      event_time = ?, 
                      venue = ?, 
                      organizer = ?, 
                      status = ? 
                      WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("sssssssi", $title, $description, $event_date, $event_time, $venue, $organizer, $status, $id);
        
        if ($stmt->execute()) {
            $success_message = "ইভেন্ট সফলভাবে আপডেট করা হয়েছে।";
        } else {
            $error_message = "ইভেন্ট আপডেট করতে সমস্যা হয়েছে: " . $conn->error;
        }
    }
    // Process form submission for deleting event
    else if (isset($_POST['action']) && $_POST['action'] == 'delete') {
        $id = intval($_POST['id']);

        $deleteQuery = "DELETE FROM events WHERE id = ?";
        $stmt = $conn->prepare($deleteQuery);
        $stmt->bind_param("i", $id);
        
        if ($stmt->execute()) {
            $success_message = "ইভেন্ট সফলভাবে মুছে ফেলা হয়েছে।";
        } else {
            $error_message = "ইভেন্ট মুছতে সমস্যা হয়েছে: " . $conn->error;
        }
    }
}

// Get event by id for editing
$edit_id = isset($_GET['edit']) ? intval($_GET['edit']) : 0;
$event_to_edit = null;

if ($edit_id > 0) {
    $editQuery = "SELECT * FROM events WHERE id = ?";
    $stmt = $conn->prepare($editQuery);
    $stmt->bind_param("i", $edit_id);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result->num_rows > 0) {
        $event_to_edit = $result->fetch_assoc();
    }
}

// Get all events
$query = "SELECT * FROM events ORDER BY event_date DESC";
$result = $conn->query($query);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>ইভেন্ট ব্যবস্থাপনা - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar (assuming sidebar is included) -->
            <?php include('includes/sidebar.php'); ?>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">ইভেন্ট ব্যবস্থাপনা</h1>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addEventModal">
                        <i class="fas fa-plus me-1"></i> নতুন ইভেন্ট যোগ করুন
                    </button>
                </div>
                
                <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <!-- Events List -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-calendar-day me-2"></i>সকল ইভেন্ট</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($result && $result->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th width="5%">#</th>
                                            <th width="15%">শিরোনাম</th>
                                            <th width="25%">বিবরণ</th>
                                            <th width="10%">তারিখ</th>
                                            <th width="10%">সময়</th>
                                            <th width="15%">স্থান</th>
                                            <th width="10%">অবস্থা</th>
                                            <th width="10%">কার্যক্রম</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php 
                                        $i = 1;
                                        while ($event = $result->fetch_assoc()): 
                                        ?>
                                        <tr>
                                            <td><?php echo $i++; ?></td>
                                            <td><?php echo htmlspecialchars($event['title']); ?></td>
                                            <td><?php echo mb_substr(htmlspecialchars($event['description']), 0, 100) . '...'; ?></td>
                                            <td><?php echo date('d-m-Y', strtotime($event['event_date'])); ?></td>
                                            <td><?php echo $event['event_time']; ?></td>
                                            <td><?php echo htmlspecialchars($event['venue']); ?></td>
                                            <td>
                                                <?php 
                                                $status_class = '';
                                                switch($event['status']) {
                                                    case 'upcoming':
                                                        $status_class = 'badge bg-primary';
                                                        $status_text = 'আসন্ন';
                                                        break;
                                                    case 'ongoing':
                                                        $status_class = 'badge bg-success';
                                                        $status_text = 'চলমান';
                                                        break;
                                                    case 'completed':
                                                        $status_class = 'badge bg-secondary';
                                                        $status_text = 'সম্পন্ন';
                                                        break;
                                                    case 'canceled':
                                                        $status_class = 'badge bg-danger';
                                                        $status_text = 'বাতিল';
                                                        break;
                                                    default:
                                                        $status_class = 'badge bg-info';
                                                        $status_text = $event['status'];
                                                }
                                                ?>
                                                <span class="<?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                            </td>
                                            <td>
                                                <a href="?edit=<?php echo $event['id']; ?>" class="btn btn-sm btn-warning">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#deleteEventModal" 
                                                        data-id="<?php echo $event['id']; ?>">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> কোন ইভেন্ট পাওয়া যায়নি। নতুন ইভেন্ট যোগ করুন।
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add Event Modal -->
    <div class="modal fade" id="addEventModal" tabindex="-1" aria-labelledby="addEventModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addEventModalLabel">নতুন ইভেন্ট যোগ করুন</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">
                        
                        <div class="mb-3">
                            <label for="title" class="form-label">শিরোনাম</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">বিবরণ</label>
                            <textarea class="form-control" id="description" name="description" rows="4" required></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="event_date" class="form-label">ইভেন্টের তারিখ</label>
                                <input type="date" class="form-control" id="event_date" name="event_date" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="event_time" class="form-label">ইভেন্টের সময়</label>
                                <input type="time" class="form-control" id="event_time" name="event_time">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="venue" class="form-label">স্থান</label>
                            <input type="text" class="form-control" id="venue" name="venue" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="organizer" class="form-label">আয়োজক</label>
                            <input type="text" class="form-control" id="organizer" name="organizer">
                        </div>
                        
                        <div class="mb-3">
                            <label for="status" class="form-label">অবস্থা</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="upcoming">আসন্ন</option>
                                <option value="ongoing">চলমান</option>
                                <option value="completed">সম্পন্ন</option>
                                <option value="canceled">বাতিল</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                        <button type="submit" class="btn btn-primary">সংরক্ষণ করুন</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Event Modal -->
    <?php if ($event_to_edit): ?>
    <div class="modal fade" id="editEventModal" tabindex="-1" aria-labelledby="editEventModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editEventModalLabel">ইভেন্ট সম্পাদনা</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="id" value="<?php echo $event_to_edit['id']; ?>">
                        
                        <div class="mb-3">
                            <label for="edit_title" class="form-label">শিরোনাম</label>
                            <input type="text" class="form-control" id="edit_title" name="title" 
                                value="<?php echo htmlspecialchars($event_to_edit['title']); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="edit_description" class="form-label">বিবরণ</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="4" required>
                                <?php echo htmlspecialchars($event_to_edit['description']); ?>
                            </textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="edit_event_date" class="form-label">ইভেন্টের তারিখ</label>
                                <input type="date" class="form-control" id="edit_event_date" name="event_date"
                                    value="<?php echo $event_to_edit['event_date']; ?>" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="edit_event_time" class="form-label">ইভেন্টের সময়</label>
                                <input type="time" class="form-control" id="edit_event_time" name="event_time"
                                    value="<?php echo $event_to_edit['event_time']; ?>">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="edit_venue" class="form-label">স্থান</label>
                            <input type="text" class="form-control" id="edit_venue" name="venue"
                                value="<?php echo htmlspecialchars($event_to_edit['venue']); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="edit_organizer" class="form-label">আয়োজক</label>
                            <input type="text" class="form-control" id="edit_organizer" name="organizer"
                                value="<?php echo htmlspecialchars($event_to_edit['organizer']); ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="edit_status" class="form-label">অবস্থা</label>
                            <select class="form-select" id="edit_status" name="status" required>
                                <option value="upcoming" <?php echo $event_to_edit['status'] == 'upcoming' ? 'selected' : ''; ?>>আসন্ন</option>
                                <option value="ongoing" <?php echo $event_to_edit['status'] == 'ongoing' ? 'selected' : ''; ?>>চলমান</option>
                                <option value="completed" <?php echo $event_to_edit['status'] == 'completed' ? 'selected' : ''; ?>>সম্পন্ন</option>
                                <option value="canceled" <?php echo $event_to_edit['status'] == 'canceled' ? 'selected' : ''; ?>>বাতিল</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                        <button type="submit" class="btn btn-primary">সংরক্ষণ করুন</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Delete Event Modal -->
    <div class="modal fade" id="deleteEventModal" tabindex="-1" aria-labelledby="deleteEventModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteEventModalLabel">ইভেন্ট মুছে ফেলুন</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>আপনি কি নিশ্চিত যে আপনি এই ইভেন্টটি মুছে ফেলতে চান?</p>
                </div>
                <div class="modal-footer">
                    <form method="POST" action="">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="delete_id" id="delete_id" value="">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                        <button type="submit" class="btn btn-danger">মুছে ফেলুন</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // Auto-open edit modal if edit parameter is present
        <?php if ($event_to_edit): ?>
        document.addEventListener('DOMContentLoaded', function() {
            var editModal = new bootstrap.Modal(document.getElementById('editEventModal'));
            editModal.show();
        });
        <?php endif; ?>
        
        // Set delete ID in modal
        document.addEventListener('DOMContentLoaded', function() {
            const deleteEventModal = document.getElementById('deleteEventModal');
            if (deleteEventModal) {
                deleteEventModal.addEventListener('show.bs.modal', function(event) {
                    const button = event.relatedTarget;
                    const id = button.getAttribute('data-id');
                    document.getElementById('delete_id').value = id;
                });
            }
        });
    </script>
</body>
</html> 