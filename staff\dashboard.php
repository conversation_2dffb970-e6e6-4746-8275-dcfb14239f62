<?php
session_start();

// Check if user is logged in and is a staff
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'staff') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get staff information
$userId = $_SESSION['userId'];
$sql = "SELECT * FROM staff WHERE user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();
$staff = $result->fetch_assoc();

// Get recent students
$recentStudentsQuery = "SELECT s.student_id, s.first_name, s.last_name, s.department, s.admission_date
                      FROM students s
                      ORDER BY s.id DESC
                      LIMIT 5";
$recentStudents = $conn->query($recentStudentsQuery);



// Get department statistics
$deptStatsQuery = "SELECT department, COUNT(*) as count
                  FROM students
                  GROUP BY department
                  ORDER BY count DESC
                  LIMIT 5";
$deptStats = $conn->query($deptStatsQuery);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>Staff Dashboard - College Management System</title>


    <style>
        body, html, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, select, textarea, label, li, th, td {
            font-family: 'Hind Siliguri', sans-serif;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>Staff Panel</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user me-2"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> Students
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-file-pdf me-2"></i> Reports
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Staff Information -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card profile-header">
                            <div class="row g-0">
                                <div class="col-md-2 text-center">
                                    <img src="https://via.placeholder.com/150" alt="Staff" class="profile-img">
                                </div>
                                <div class="col-md-10 profile-info">
                                    <div class="card-body">
                                        <h2><?php echo $staff['first_name'] . ' ' . $staff['last_name']; ?></h2>
                                        <p><strong>Staff ID:</strong> <?php echo $staff['staff_id']; ?></p>
                                        <p><strong>Role:</strong> <?php echo $staff['role']; ?></p>
                                        <p><strong>Email:</strong> <?php echo $staff['email']; ?></p>
                                        <p><strong>Phone:</strong> <?php echo $staff['phone']; ?></p>
                                        <p><strong>Joining Date:</strong> <?php echo date('d M Y', strtotime($staff['joining_date'])); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Stats Cards -->


                <div class="row mt-4">
                    <!-- Recent Students -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">Recent Students</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Name</th>
                                                <th>Department</th>
                                                <th>Join Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($recentStudents && $recentStudents->num_rows > 0): ?>
                                                <?php while ($student = $recentStudents->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo $student['student_id']; ?></td>
                                                        <td><?php echo $student['first_name'] . ' ' . $student['last_name']; ?></td>
                                                        <td><?php echo $student['department']; ?></td>
                                                        <td><?php echo date('d M Y', strtotime($student['admission_date'])); ?></td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="4" class="text-center">No students found</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <a href="students.php" class="btn btn-primary btn-sm mt-3">View All Students</a>
                            </div>
                        </div>
                    </div>

                    <!-- Department Stats -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">Department Statistics</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Department</th>
                                                <th>Student Count</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($deptStats && $deptStats->num_rows > 0): ?>
                                                <?php while ($dept = $deptStats->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo $dept['department']; ?></td>
                                                        <td><?php echo $dept['count']; ?></td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="2" class="text-center">No department data found</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Quick Actions -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0">Quick Actions</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">

                                    <a href="add_student.php" class="btn btn-outline-warning">
                                        <i class="fas fa-user-plus me-2"></i> Add Student
                                    </a>
                                    <a href="generate_id_cards.php" class="btn btn-outline-danger">
                                        <i class="fas fa-id-card me-2"></i> Generate ID Cards
                                    </a>
                                    <a href="general_reports.php" class="btn btn-outline-dark">
                                        <i class="fas fa-file-pdf me-2"></i> General Reports
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>