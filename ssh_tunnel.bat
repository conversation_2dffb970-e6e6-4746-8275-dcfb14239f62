@echo off
echo ===================================================
echo        SSH টানেল সেটআপ (localhost.run)
echo ===================================================
echo.

echo SSH টানেল সেটআপ করা হচ্ছে...
echo.

REM Check if SSH is available
where ssh >nul 2>&1
if %errorlevel% neq 0 (
    echo SSH কমান্ড পাওয়া যায়নি।
    echo Windows 10 বা উচ্চতর সংস্করণে SSH ইনস্টল করা থাকে।
    echo পুরানো Windows সংস্করণে, আপনাকে OpenSSH ইনস্টল করতে হবে।
    echo.
    echo যে কোন কী চাপুন বন্ধ করতে...
    pause > nul
    exit
)

echo SSH টানেল স্টার্ট করা হচ্ছে...
echo টানেল স্টার্ট হলে, আপনি একটি URL দেখতে পাবেন যা দিয়ে আপনার প্রজেক্ট অ্যাক্সেস করতে পারবেন।
echo.
echo টানেল বন্ধ করতে, এই উইন্ডো বন্ধ করুন।
echo.
echo দয়া করে অপেক্ষা করুন...
echo.

REM Start SSH tunnel using localhost.run
ssh -R 80:localhost:80 localhost.run

echo.
echo টানেল বন্ধ হয়েছে।
echo.
echo যে কোন কী চাপুন বন্ধ করতে...
pause > nul
