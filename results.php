<?php
session_start();
require_once 'includes/dbh.inc.php';

// Initialize variables
$studentId = '';
$examId = '';
$classId = '';
$sessionId = '';
$searchPerformed = false;
$studentResults = null;
$studentInfo = null;
$errorMessage = '';
$successMessage = '';

// Get classes for dropdown
$classesQuery = "SELECT id, class_name FROM classes ORDER BY class_name";
$classes = $conn->query($classesQuery);

// Get sessions for dropdown
$sessionsQuery = "SELECT id, session_name FROM sessions ORDER BY session_name DESC";
$sessions = $conn->query($sessionsQuery);

// Get exams for dropdown
$examsQuery = "SELECT id, exam_name FROM exams ORDER BY exam_date DESC";
$exams = $conn->query($examsQuery);

// Handle search form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['search_results'])) {
    $searchPerformed = true;
    $studentId = $_POST['student_id'] ?? '';
    $examId = $_POST['exam_id'] ?? '';
    $classId = $_POST['class_id'] ?? '';
    $sessionId = $_POST['session_id'] ?? '';

    // Validate student ID (required)
    if (empty($studentId)) {
        $errorMessage = "শিক্ষার্থী আইডি প্রয়োজন। অনুগ্রহ করে শিক্ষার্থী আইডি দিন।";
    } else {
        // Get student information
        $studentInfoQuery = "SELECT s.*, c.class_name, d.department_name, ss.session_name
                           FROM students s
                           LEFT JOIN classes c ON s.class_id = c.id
                           LEFT JOIN departments d ON s.department_id = d.id
                           LEFT JOIN sessions ss ON s.session_id = ss.id
                           WHERE s.student_id = ?";
        $stmt = $conn->prepare($studentInfoQuery);
        $stmt->bind_param("s", $studentId);
        $stmt->execute();
        $studentInfo = $stmt->get_result()->fetch_assoc();

        if (!$studentInfo) {
            $errorMessage = "এই আইডি দিয়ে কোন শিক্ষার্থী পাওয়া যায়নি।";
        } else {
            // Build query for results
            $resultsQuery = "SELECT r.id, r.marks_obtained, r.grade,
                           e.exam_name, e.total_marks, e.exam_date
                           FROM results r
                           JOIN students s ON r.student_id = s.id
                           JOIN exams e ON r.exam_id = e.id
                           WHERE s.student_id = ?";

            $params = [$studentId];
            $types = "s";

            if (!empty($examId)) {
                $resultsQuery .= " AND e.id = ?";
                $params[] = $examId;
                $types .= "i";
            }

            if (!empty($classId)) {
                $resultsQuery .= " AND s.class_id = ?";
                $params[] = $classId;
                $types .= "i";
            }

            if (!empty($sessionId)) {
                $resultsQuery .= " AND s.session_id = ?";
                $params[] = $sessionId;
                $types .= "i";
            }

            $resultsQuery .= " ORDER BY e.exam_date DESC";

            $stmt = $conn->prepare($resultsQuery);
            $stmt->bind_param($types, ...$params);
            $stmt->execute();
            $studentResults = $stmt->get_result();

            if ($studentResults->num_rows === 0) {
                $errorMessage = "নির্বাচিত মাপদণ্ড অনুযায়ী কোন ফলাফল পাওয়া যায়নি।";
            } else {
                $successMessage = "ফলাফল সফলভাবে পাওয়া গেছে!";
            }
        }
    }
}

// Set page title
$page_title = "পরীক্ষার ফলাফল";
$school_name = "নিশাত এডুকেশন সেন্টার";
$school_address = "চুয়াডাঙ্গা, বাংলাদেশ";
$school_logo = "img/logo.jpg";
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title><?php echo $page_title; ?> - <?php echo $school_name; ?></title>

    <!-- Bootstrap CSS -->
    

    <!-- Custom Fonts -->
    

    <style>
        :root {
            --primary-color: #006A4E; /* Deep Green */
            --secondary-color: #00563B; /* Darker Green */
            --accent-color: #F39C12; /* Amber/Gold */
            --dark-color: #2C3E50; /* Dark Blue-Gray */
            --light-color: #F5F5F5; /* Off-White */
            --text-color: #333333; /* Dark Gray */
            --light-text: #FFFFFF; /* White */
            --highlight-color: #E74C3C; /* Red Accent */
            --soft-color: #E3F2FD; /* Soft Blue */
        }

        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: var(--light-color);
            color: var(--text-color);
        }

        /* Header Styles */
        .header-top {
            background-color: white;
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .school-logo {
            width: 80px;
            height: 80px;
            object-fit: contain;
        }

        .school-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .school-subtitle {
            color: var(--text-color);
            font-weight: 400;
        }

        /* Navigation Styles */
        .main-nav {
            background-color: var(--primary-color);
            padding: 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .main-nav .nav {
            display: flex;
            flex-wrap: nowrap;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .main-nav .nav-link {
            color: rgba(255,255,255,0.9);
            padding: 15px;
            font-weight: 500;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .main-nav .nav-link:hover {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }

        .main-nav .nav-link.active {
            color: white;
            border-bottom-color: var(--accent-color);
            background-color: rgba(255,255,255,0.1);
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 50px 0;
            margin-bottom: 30px;
        }

        .hero-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .hero-text {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .hero-img {
            max-width: 100%;
            height: auto;
        }

        /* Card Styles */
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            overflow: hidden;
            margin-bottom: 25px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            padding: 15px 20px;
        }

        .card-body {
            padding: 20px;
        }

        /* Tab Styles */
        .nav-tabs .nav-link {
            color: var(--text-color);
            border: none;
            padding: 10px 15px;
            border-radius: 0;
            font-weight: 500;
        }

        .nav-tabs .nav-link.active {
            color: var(--primary-color);
            background-color: transparent;
            border-bottom: 2px solid var(--primary-color);
        }

        /* Detail Item Styles */
        .detail-item {
            padding: 10px 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            transition: transform 0.2s ease;
        }

        .detail-item i {
            color: var(--primary-color);
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        /* Form Control Styles */
        .form-control, .form-select {
            border-radius: 5px;
            padding: 10px 15px;
            border: 1px solid rgba(0,0,0,0.1);
        }

        .form-control:focus, .form-select:focus {
            box-shadow: 0 0 0 0.25rem rgba(0, 106, 78, 0.25);
            border-color: var(--primary-color);
        }

        /* Table Styles */
        .table {
            border-collapse: separate;
            border-spacing: 0;
        }

        .table th {
            background-color: rgba(0, 106, 78, 0.05);
            color: var(--primary-color);
            font-weight: 600;
        }

        .table td, .table th {
            padding: 12px 15px;
            vertical-align: middle;
        }

        /* Badge Styles */
        .badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: 500;
        }

        /* Profile Placeholder */
        .profile-placeholder {
            width: 120px;
            height: 120px;
            background-color: #e9ecef;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #adb5bd;
        }

        /* Back to top button */
        .back-to-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: none;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .back-to-top:hover {
            background-color: var(--secondary-color);
            transform: translateY(-3px);
        }

        /* Footer Styles */
        .footer {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 50px 0 0;
            margin-top: 50px;
        }

        .footer h5 {
            font-weight: 600;
            margin-bottom: 20px;
            position: relative;
            padding-bottom: 10px;
        }

        .footer h5:after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 50px;
            height: 2px;
            background-color: var(--accent-color);
        }

        .footer-links {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .footer-links li {
            padding: 8px 0;
            transition: all 0.3s ease;
        }

        .footer-links li:hover {
            transform: translateX(5px);
        }

        .footer-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .footer-links a:hover {
            color: white;
        }

        .social-icons a {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            margin-right: 10px;
            transition: all 0.3s ease;
        }

        .social-icons a:hover {
            background-color: var(--accent-color);
            transform: translateY(-3px);
        }

        .copyright {
            background-color: rgba(0, 0, 0, 0.1);
            padding: 15px 0;
            margin-top: 30px;
        }

        /* Print Styles */
        @media print {
            .container {
                width: 100%;
                max-width: 100%;
            }

            .card {
                border: none !important;
                box-shadow: none !important;
            }

            .card-header {
                background-color: #f8f9fa !important;
                color: #000 !important;
                border-bottom: 1px solid #dee2e6 !important;
            }

            .btn, form, .alert, .accordion, .nav-tabs, .back-to-top, .hero-section, .main-nav, .header-top, .footer {
                display: none !important;
            }

            .badge {
                border: 1px solid #000 !important;
            }

            .badge.bg-success {
                background-color: #fff !important;
                color: #000 !important;
            }

            .badge.bg-danger {
                background-color: #fff !important;
                color: #000 !important;
            }

            .profile-details {
                page-break-inside: avoid;
            }

            .table {
                border-collapse: collapse !important;
            }

            .table td, .table th {
                background-color: #fff !important;
                border: 1px solid #dee2e6 !important;
            }
        }
    </style>
</head>
<body>
    <!-- Header Top with Logo and Title -->
    <div class="header-top">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-2 text-center text-md-start mb-3 mb-md-0">
                    <img src="<?php echo $school_logo; ?>" alt="School Logo" class="school-logo" onerror="this.src='https://via.placeholder.com/80?text=ZFAW'">
                </div>
                <div class="col-md-10 text-center text-md-start">
                    <h1 class="school-title mb-1"><?php echo $school_name; ?></h1>
                    <h2 class="school-subtitle fs-5"><?php echo $school_address; ?></h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Navigation -->
    <nav class="main-nav">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <ul class="nav">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php"><i class="fas fa-home me-1"></i> হোম</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="subjects.php"><i class="fas fa-book me-1"></i> বিষয়সমূহ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="teachers.php"><i class="fas fa-chalkboard-teacher me-1"></i> শিক্ষকবৃন্দ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="students.php"><i class="fas fa-user-graduate me-1"></i> শিক্ষার্থীবৃন্দ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="notices.php"><i class="fas fa-bullhorn me-1"></i> নোটিশ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="gb_members.php"><i class="fas fa-users me-1"></i> পরিচালনা বোর্ড</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="personal_sms.php"><i class="fas fa-sms me-1"></i> পার্সোনাল এসএমএস</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="results.php"><i class="fas fa-chart-bar me-1"></i> ফলাফল</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="hero-title">পরীক্ষার ফলাফল</h1>
                <p class="hero-text">শিক্ষার্থীর আইডি দিয়ে পরীক্ষার ফলাফল দেখুন। আপনার সকল পরীক্ষার ফলাফল এখানে সংরক্ষিত আছে।</p>
                <a href="index.php" class="btn btn-lg" style="background-color: #00a65a; color: white; font-weight: 500; box-shadow: 0 4px 15px rgba(0,0,0,0.2); transition: all 0.3s ease;">হোমপেজে ফিরুন <i class="fas fa-arrow-right ms-2"></i></a>
            </div>
            <div class="col-lg-6 text-center">
                <img src="img/result-illustration.png" alt="Result Illustration" class="img-fluid rounded hero-img" style="max-width: 300px; border: 5px solid white; box-shadow: 0 5px 15px rgba(0,0,0,0.2);" onerror="this.src='https://via.placeholder.com/300x200?text=Results'">
            </div>
        </div>
    </div>
</section>

<!-- Alert Messages -->
<section class="container mt-4">
    <?php if (!empty($successMessage)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i> <?php echo $successMessage; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (!empty($errorMessage)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i> <?php echo $errorMessage; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
</section>

<!-- Main Content Section -->
<section class="container mt-4 mb-4">
    <div class="row">
        <!-- Left Column - Search Form -->
        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-header bg-white p-3">
                    <h5 class="mb-0"><i class="fas fa-search me-2"></i>ফলাফল খুঁজুন</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <div class="mb-3">
                            <label for="student_id" class="form-label">শিক্ষার্থী আইডি <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                                <input type="text" name="student_id" id="student_id" class="form-control" placeholder="শিক্ষার্থী আইডি দিন" value="<?php echo htmlspecialchars($studentId); ?>" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="exam_id" class="form-label">পরীক্ষা</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-book"></i></span>
                                <select name="exam_id" id="exam_id" class="form-select">
                                    <option value="">সকল পরীক্ষা</option>
                                    <?php if ($exams && $exams->num_rows > 0): ?>
                                        <?php while ($exam = $exams->fetch_assoc()): ?>
                                            <option value="<?php echo $exam['id']; ?>" <?php echo ($examId == $exam['id']) ? 'selected' : ''; ?>>
                                                <?php echo $exam['exam_name']; ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="class_id" class="form-label">শ্রেণী</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-graduation-cap"></i></span>
                                <select name="class_id" id="class_id" class="form-select">
                                    <option value="">সকল শ্রেণী</option>
                                    <?php if ($classes && $classes->num_rows > 0): ?>
                                        <?php while ($class = $classes->fetch_assoc()): ?>
                                            <option value="<?php echo $class['id']; ?>" <?php echo ($classId == $class['id']) ? 'selected' : ''; ?>>
                                                <?php echo $class['class_name']; ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="session_id" class="form-label">সেশন</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                                <select name="session_id" id="session_id" class="form-select">
                                    <option value="">সকল সেশন</option>
                                    <?php if ($sessions && $sessions->num_rows > 0): ?>
                                        <?php while ($session = $sessions->fetch_assoc()): ?>
                                            <option value="<?php echo $session['id']; ?>" <?php echo ($sessionId == $session['id']) ? 'selected' : ''; ?>>
                                                <?php echo $session['session_name']; ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>
                        <div class="d-grid">
                            <button type="submit" name="search_results" class="btn" style="background-color: #00a65a; color: white; font-weight: 500; box-shadow: 0 4px 15px rgba(0,0,0,0.1); transition: all 0.3s ease;">
                                <i class="fas fa-search me-2"></i> ফলাফল খুঁজুন
                            </button>
                        </div>
                        <div class="d-grid mt-2">
                            <a href="results.php" class="btn btn-light" style="font-weight: 500; box-shadow: 0 4px 15px rgba(0,0,0,0.05); transition: all 0.3s ease;">
                                <i class="fas fa-sync-alt me-2"></i> রিসেট করুন
                            </a>
                        </div>
                    </form>

                    <div class="mt-4">
                        <h6 class="mb-3"><i class="fas fa-info-circle text-primary me-2"></i>ফলাফল দেখার নিয়মাবলী</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item border-0 ps-0"><i class="fas fa-check-circle text-success me-2"></i>শিক্ষার্থী আইডি দিন (বাধ্যতামূলক)</li>
                            <li class="list-group-item border-0 ps-0"><i class="fas fa-check-circle text-success me-2"></i>নির্দিষ্ট পরীক্ষার ফলাফল দেখতে পরীক্ষা নির্বাচন করুন</li>
                            <li class="list-group-item border-0 ps-0"><i class="fas fa-check-circle text-success me-2"></i>নির্দিষ্ট শ্রেণীর ফলাফল দেখতে শ্রেণী নির্বাচন করুন</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column - Results Display -->
        <div class="col-md-8">
            <?php if ($searchPerformed && $studentInfo && $studentResults && $studentResults->num_rows > 0): ?>
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-white p-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-user-graduate me-2"></i>শিক্ষার্থীর তথ্য</h5>
                            <button class="btn btn-sm" style="background-color: #00a65a; color: white;" onclick="window.print()">
                                <i class="fas fa-print me-1"></i> প্রিন্ট
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 text-center mb-3 mb-md-0">
                                <?php if (isset($studentInfo['profile_photo']) && !empty($studentInfo['profile_photo'])): ?>
                                    <img src="<?php echo $studentInfo['profile_photo']; ?>" alt="Profile Photo" class="img-fluid rounded-circle" style="width: 100px; height: 100px; object-fit: cover; border: 3px solid #00a65a;" onerror="this.src='https://via.placeholder.com/100?text=Student'">
                                <?php else: ?>
                                    <div class="profile-placeholder mx-auto" style="width: 100px; height: 100px; background-color: #e9ecef; border-radius: 50%; display: flex; align-items: center; justify-content: center; border: 3px solid #00a65a;">
                                        <i class="fas fa-user-graduate" style="font-size: 40px; color: #adb5bd;"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-9">
                                <h4 class="mb-2"><?php echo htmlspecialchars($studentInfo['first_name'] . ' ' . $studentInfo['last_name']); ?></h4>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p class="mb-1"><i class="fas fa-id-card text-primary me-2"></i><strong>শিক্ষার্থী আইডি:</strong> <?php echo htmlspecialchars($studentInfo['student_id']); ?></p>
                                        <p class="mb-1"><i class="fas fa-sort-numeric-up text-primary me-2"></i><strong>রোল নম্বর:</strong> <?php echo htmlspecialchars($studentInfo['roll_number'] ?? 'N/A'); ?></p>
                                        <p class="mb-1"><i class="fas fa-graduation-cap text-primary me-2"></i><strong>শ্রেণী:</strong> <?php echo htmlspecialchars($studentInfo['class_name'] ?? 'N/A'); ?></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="mb-1"><i class="fas fa-building text-primary me-2"></i><strong>বিভাগ:</strong> <?php echo htmlspecialchars($studentInfo['department_name'] ?? 'N/A'); ?></p>
                                        <p class="mb-1"><i class="fas fa-calendar-alt text-primary me-2"></i><strong>সেশন:</strong> <?php echo htmlspecialchars($studentInfo['session_name'] ?? 'N/A'); ?></p>
                                        <?php if (isset($studentInfo['phone']) && !empty($studentInfo['phone'])): ?>
                                            <p class="mb-1"><i class="fas fa-phone text-primary me-2"></i><strong>ফোন:</strong> <?php echo htmlspecialchars($studentInfo['phone']); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card shadow-sm">
                    <div class="card-header bg-white p-3">
                        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>পরীক্ষার ফলাফল</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>পরীক্ষা</th>
                                        <th>তারিখ</th>
                                        <th>মোট মার্কস</th>
                                        <th>প্রাপ্ত মার্কস</th>
                                        <th>শতকরা (%)</th>
                                        <th>গ্রেড</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    // Reset the pointer to the beginning of the result set
                                    $studentResults->data_seek(0);
                                    while ($result = $studentResults->fetch_assoc()):
                                    ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($result['exam_name']); ?></td>
                                            <td><?php echo date('d F Y', strtotime($result['exam_date'])); ?></td>
                                            <td><?php echo htmlspecialchars($result['total_marks']); ?></td>
                                            <td><?php echo htmlspecialchars($result['marks_obtained']); ?></td>
                                            <td>
                                                <?php
                                                $percentage = ($result['marks_obtained'] / $result['total_marks']) * 100;
                                                echo number_format($percentage, 2) . '%';
                                                ?>
                                            </td>
                                            <td>
                                                <span class="badge <?php echo ($result['grade'] == 'F') ? 'bg-danger' : 'bg-success'; ?>">
                                                    <?php echo $result['grade']; ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <?php if ($searchPerformed): ?>
                    <div class="card shadow-sm">
                        <div class="card-body text-center p-5">
                            <i class="fas fa-search fa-4x mb-3 text-muted"></i>
                            <h4>কোন ফলাফল পাওয়া যায়নি</h4>
                            <p class="text-muted">আপনার অনুসন্ধান মাপদণ্ড অনুযায়ী কোন ফলাফল পাওয়া যায়নি। অনুগ্রহ করে আবার চেষ্টা করুন।</p>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="card shadow-sm">
                        <div class="card-body text-center p-5">
                            <i class="fas fa-info-circle fa-4x mb-3 text-primary"></i>
                            <h4>ফলাফল দেখতে অনুসন্ধান করুন</h4>
                            <p class="text-muted">আপনার ফলাফল দেখতে বাম পাশের ফর্মে শিক্ষার্থী আইডি দিন এবং অন্যান্য ফিল্টার অপশন নির্বাচন করুন।</p>
                            <div class="mt-4">
                                <h5 class="mb-3">প্রায়শই জিজ্ঞাসিত প্রশ্ন</h5>
                                <div class="accordion" id="resultFAQ">
                                    <div class="accordion-item border-0 mb-2">
                                        <h2 class="accordion-header" id="headingOne">
                                            <button class="accordion-button collapsed shadow-sm" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="false" aria-controls="collapseOne">
                                                আমার শিক্ষার্থী আইডি কোথায় পাব?
                                            </button>
                                        </h2>
                                        <div id="collapseOne" class="accordion-collapse collapse" aria-labelledby="headingOne" data-bs-parent="#resultFAQ">
                                            <div class="accordion-body">
                                                শিক্ষার্থী আইডি আপনার শিক্ষার্থী আইডি কার্ডে পাওয়া যাবে। আপনি আপনার শ্রেণী শিক্ষক বা অফিস থেকেও জানতে পারেন।
                                            </div>
                                        </div>
                                    </div>
                                    <div class="accordion-item border-0">
                                        <h2 class="accordion-header" id="headingTwo">
                                            <button class="accordion-button collapsed shadow-sm" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                                ফলাফল কখন প্রকাশিত হয়?
                                            </button>
                                        </h2>
                                        <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#resultFAQ">
                                            <div class="accordion-body">
                                                সাধারণত পরীক্ষা শেষ হওয়ার ১৫-৩০ দিনের মধ্যে ফলাফল প্রকাশ করা হয়। নির্দিষ্ট তারিখ জানতে অফিসে যোগাযোগ করুন।
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</section>
</div>

<!-- Back to top button -->
<a href="#" class="back-to-top">
    <i class="fas fa-arrow-up"></i>
</a>

<style>
    @media print {
        .container {
            width: 100%;
            max-width: 100%;
        }

        .card {
            border: none !important;
            box-shadow: none !important;
        }

        .card-header {
            background-color: #f8f9fa !important;
            color: #000 !important;
            border-bottom: 1px solid #dee2e6 !important;
        }

        .btn, form, .alert, .accordion, .nav-tabs, .back-to-top, .hero-section, .main-nav, .header-top, .footer {
            display: none !important;
        }

        .badge {
            border: 1px solid #000 !important;
        }

        .badge.bg-success {
            background-color: #fff !important;
            color: #000 !important;
        }

        .badge.bg-danger {
            background-color: #fff !important;
            color: #000 !important;
        }

        .profile-details {
            page-break-inside: avoid;
        }

        .table {
            border-collapse: collapse !important;
        }

        .table td, .table th {
            background-color: #fff !important;
            border: 1px solid #dee2e6 !important;
        }
    }
</style>

<!-- Footer Section -->
<footer class="footer">
    <div class="container">
        <div class="row">
            <div class="col-md-4 mb-4 mb-md-0">
                <h5>আমাদের সম্পর্কে</h5>
                <p class="text-light opacity-75">নিশাত এডুকেশন সেন্টার একটি আধুনিক শিক্ষা প্রতিষ্ঠান যা উচ্চমানের শিক্ষা প্রদানের জন্য প্রতিশ্রুতিবদ্ধ।</p>
                <div class="social-icons mt-3">
                    <a href="#"><i class="fab fa-facebook-f"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                    <a href="#"><i class="fab fa-youtube"></i></a>
                </div>
            </div>
            <div class="col-md-4 mb-4 mb-md-0">
                <h5>দ্রুত লিঙ্ক</h5>
                <ul class="footer-links">
                    <li><a href="index.php"><i class="fas fa-angle-right me-2"></i> হোম</a></li>
                    <li><a href="about.php"><i class="fas fa-angle-right me-2"></i> আমাদের সম্পর্কে</a></li>
                    <li><a href="teachers.php"><i class="fas fa-angle-right me-2"></i> শিক্ষকবৃন্দ</a></li>
                    <li><a href="notices.php"><i class="fas fa-angle-right me-2"></i> নোটিশ</a></li>
                    <li><a href="contact.php"><i class="fas fa-angle-right me-2"></i> যোগাযোগ</a></li>
                </ul>
            </div>
            <div class="col-md-4">
                <h5>যোগাযোগ করুন</h5>
                <ul class="footer-links">
                    <li><i class="fas fa-map-marker-alt me-2"></i> চুয়াডাঙ্গা, বাংলাদেশ</li>
                    <li><i class="fas fa-phone me-2"></i> +880 1234-567890</li>
                    <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                    <li><i class="fas fa-clock me-2"></i> সকাল ৯টা - বিকাল ৫টা</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="copyright text-center">
        <div class="container">
            <p class="mb-0">&copy; <?php echo date('Y'); ?> নিশাত এডুকেশন সেন্টার - সর্বসত্ত্ব সংরক্ষিত</p>
        </div>
    </div>
</footer>

<!-- Custom JavaScript -->
<script>
    // Back to top button
    window.addEventListener('scroll', function() {
        var backToTopBtn = document.querySelector('.back-to-top');
        if (backToTopBtn) {
            if (window.pageYOffset > 300) {
                backToTopBtn.style.display = 'flex';
                backToTopBtn.style.justifyContent = 'center';
                backToTopBtn.style.alignItems = 'center';
            } else {
                backToTopBtn.style.display = 'none';
            }
        }
    });

    // Add hover effect to detail items
    document.addEventListener('DOMContentLoaded', function() {
        const detailItems = document.querySelectorAll('.detail-item');
        detailItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(5px)';
            });

            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0)';
            });
        });
    });
</script>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
