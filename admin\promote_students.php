<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';
require_once '../includes/functions.php';

// Initialize variables
$successMessage = $errorMessage = '';
$sourceClassId = $destinationClassId = $sessionId = $departmentId = 0;
$students = [];

// Get all departments
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Get all classes
$classesQuery = "SELECT * FROM classes ORDER BY class_name";
$classes = $conn->query($classesQuery);

// Get all sessions
$sessionsQuery = "SELECT * FROM sessions ORDER BY session_name DESC";
$sessions = $conn->query($sessionsQuery);

// Handle form submission for loading students
if (isset($_POST['load_students'])) {
    $sourceClassId = $_POST['source_class_id'];
    $destinationClassId = $_POST['destination_class_id'];
    $sessionId = $_POST['session_id'];
    $departmentId = isset($_POST['department_id']) ? $_POST['department_id'] : 0;

    // Validate input
    if (empty($sourceClassId) || empty($destinationClassId) || empty($sessionId)) {
        $errorMessage = "উৎস ক্লাস, গন্তব্য ক্লাস এবং সেশন অবশ্যই নির্বাচন করতে হবে!";
    } else {
        // Get students from source class
        $studentsQuery = "SELECT s.*, c.class_name, d.department_name
                         FROM students s
                         JOIN classes c ON s.class_id = c.id
                         LEFT JOIN departments d ON s.department_id = d.id
                         WHERE s.class_id = ?
                         ORDER BY s.roll_number, s.first_name, s.last_name";
        $stmt = $conn->prepare($studentsQuery);
        $stmt->bind_param("i", $sourceClassId);
        $stmt->execute();
        $result = $stmt->get_result();

        while ($row = $result->fetch_assoc()) {
            $students[] = $row;
        }

        if (count($students) === 0) {
            $errorMessage = "নির্বাচিত ক্লাসে কোন শিক্ষার্থী পাওয়া যায়নি!";
        }
    }
}

// Handle form submission for promoting students
if (isset($_POST['promote_students'])) {
    $sourceClassId = $_POST['source_class_id'];
    $destinationClassId = $_POST['destination_class_id'];
    $sessionId = $_POST['session_id'];
    $departmentId = isset($_POST['department_id']) ? $_POST['department_id'] : 0;
    $selectedStudents = isset($_POST['selected_students']) ? $_POST['selected_students'] : [];
    $newRollNumbers = isset($_POST['new_roll_number']) ? $_POST['new_roll_number'] : [];

    // Validate input
    if (empty($sourceClassId) || empty($destinationClassId) || empty($sessionId)) {
        $errorMessage = "উৎস ক্লাস, গন্তব্য ক্লাস এবং সেশন অবশ্যই নির্বাচন করতে হবে!";
    } elseif (empty($selectedStudents)) {
        $errorMessage = "অন্তত একজন শিক্ষার্থী নির্বাচন করতে হবে!";
    } else {
        // Start transaction
        $conn->begin_transaction();

        try {
            // Update each selected student
            if ($departmentId > 0) {
                $updateQuery = "UPDATE students SET class_id = ?, roll_number = ?, department_id = ? WHERE id = ?";
            } else {
                $updateQuery = "UPDATE students SET class_id = ?, roll_number = ? WHERE id = ?";
            }
            $stmt = $conn->prepare($updateQuery);

            $promotedCount = 0;
            foreach ($selectedStudents as $studentId) {
                $newRollNumber = isset($newRollNumbers[$studentId]) ? $newRollNumbers[$studentId] : '';

                if ($departmentId > 0) {
                    $stmt->bind_param("isii", $destinationClassId, $newRollNumber, $departmentId, $studentId);
                } else {
                    $stmt->bind_param("isi", $destinationClassId, $newRollNumber, $studentId);
                }

                $stmt->execute();
                $promotedCount++;
            }

            // Commit transaction
            $conn->commit();
            $successMessage = "$promotedCount জন শিক্ষার্থী সফলভাবে উন্নীত করা হয়েছে!";

            // Reload students list
            $studentsQuery = "SELECT s.*, c.class_name, d.department_name
                             FROM students s
                             JOIN classes c ON s.class_id = c.id
                             LEFT JOIN departments d ON s.department_id = d.id
                             WHERE s.class_id = ?
                             ORDER BY s.roll_number, s.first_name, s.last_name";
            $stmt = $conn->prepare($studentsQuery);
            $stmt->bind_param("i", $sourceClassId);
            $stmt->execute();
            $result = $stmt->get_result();

            $students = [];
            while ($row = $result->fetch_assoc()) {
                $students[] = $row;
            }

        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            $errorMessage = "শিক্ষার্থী উন্নীত করতে সমস্যা: " . $e->getMessage();
        }
    }
}

// Get class names for display
$sourceClassName = '';
$destinationClassName = '';

if ($sourceClassId > 0) {
    $classQuery = "SELECT class_name FROM classes WHERE id = ?";
    $stmt = $conn->prepare($classQuery);
    $stmt->bind_param("i", $sourceClassId);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($row = $result->fetch_assoc()) {
        $sourceClassName = $row['class_name'];
    }
}

if ($destinationClassId > 0) {
    $classQuery = "SELECT class_name FROM classes WHERE id = ?";
    $stmt = $conn->prepare($classQuery);
    $stmt->bind_param("i", $destinationClassId);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($row = $result->fetch_assoc()) {
        $destinationClassName = $row['class_name'];
    }
}

// Get session name for display
$sessionName = '';
if ($sessionId > 0) {
    $sessionQuery = "SELECT session_name FROM sessions WHERE id = ?";
    $stmt = $conn->prepare($sessionQuery);
    $stmt->bind_param("i", $sessionId);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($row = $result->fetch_assoc()) {
        $sessionName = $row['session_name'];
    }
}

// Get department name for display
$departmentName = '';
if ($departmentId > 0) {
    $departmentQuery = "SELECT department_name FROM departments WHERE id = ?";
    $stmt = $conn->prepare($departmentQuery);
    $stmt->bind_param("i", $departmentId);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($row = $result->fetch_assoc()) {
        $departmentName = $row['department_name'];
    }
}

// Set current page for sidebar highlighting
$currentPage = 'promote_students.php';
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>শিক্ষার্থী উন্নীতকরণ - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    
    <style>
        .promotion-header {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .student-list {
            max-height: 600px;
            overflow-y: auto;
        }
        .promotion-actions {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        .select-all-container {
            margin-bottom: 15px;
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>শিক্ষার্থী উন্নীতকরণ</h2>
                        <p class="text-muted">শিক্ষার্থীদের এক ক্লাস থেকে অন্য ক্লাসে উন্নীত করুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="students.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>শিক্ষার্থী তালিকায় ফিরে যান
                        </a>
                    </div>
                </div>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Promotion Form -->
                <div class="card promotion-header">
                    <div class="card-body">
                        <form method="post" action="">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="source_class_id" class="form-label">উৎস ক্লাস</label>
                                    <select class="form-select" id="source_class_id" name="source_class_id" required>
                                        <option value="">ক্লাস নির্বাচন করুন</option>
                                        <?php while ($class = $classes->fetch_assoc()): ?>
                                            <option value="<?php echo $class['id']; ?>" <?php echo ($sourceClassId == $class['id']) ? 'selected' : ''; ?>>
                                                <?php echo $class['class_name']; ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="destination_class_id" class="form-label">গন্তব্য ক্লাস</label>
                                    <select class="form-select" id="destination_class_id" name="destination_class_id" required>
                                        <option value="">ক্লাস নির্বাচন করুন</option>
                                        <?php
                                        // Reset the classes result pointer
                                        $classes->data_seek(0);
                                        while ($class = $classes->fetch_assoc()):
                                        ?>
                                            <option value="<?php echo $class['id']; ?>" <?php echo ($destinationClassId == $class['id']) ? 'selected' : ''; ?>>
                                                <?php echo $class['class_name']; ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="session_id" class="form-label">সেশন</label>
                                    <select class="form-select" id="session_id" name="session_id" required>
                                        <option value="">সেশন নির্বাচন করুন</option>
                                        <?php while ($session = $sessions->fetch_assoc()): ?>
                                            <option value="<?php echo $session['id']; ?>" <?php echo ($sessionId == $session['id']) ? 'selected' : ''; ?>>
                                                <?php echo $session['session_name']; ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="department_id" class="form-label">বিভাগ</label>
                                    <select class="form-select" id="department_id" name="department_id">
                                        <option value="">বিভাগ নির্বাচন করুন</option>
                                        <?php
                                        // Reset the departments result pointer
                                        if ($departments) {
                                            $departments->data_seek(0);
                                            while ($dept = $departments->fetch_assoc()):
                                        ?>
                                            <option value="<?php echo $dept['id']; ?>" <?php echo ($departmentId == $dept['id']) ? 'selected' : ''; ?>>
                                                <?php echo $dept['department_name']; ?>
                                            </option>
                                        <?php
                                            endwhile;
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>
                            <div class="text-center">
                                <button type="submit" name="load_students" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>শিক্ষার্থী খুঁজুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <?php if (count($students) > 0): ?>
                    <!-- Student List -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <?php echo $sourceClassName; ?> থেকে <?php echo $destinationClassName; ?> এ উন্নীতকরণ
                                    <span class="badge bg-primary ms-2"><?php echo count($students); ?> জন শিক্ষার্থী</span>
                                </h5>
                                <div class="select-all-container">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="select_all">
                                        <label class="form-check-label" for="select_all">সবাইকে নির্বাচন করুন</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body student-list">
                            <form method="post" action="" id="promotion_form">
                                <input type="hidden" name="source_class_id" value="<?php echo $sourceClassId; ?>">
                                <input type="hidden" name="destination_class_id" value="<?php echo $destinationClassId; ?>">
                                <input type="hidden" name="session_id" value="<?php echo $sessionId; ?>">
                                <input type="hidden" name="department_id" value="<?php echo $departmentId; ?>">

                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th width="5%">নির্বাচন</th>
                                            <th width="10%">রোল নম্বর</th>
                                            <th width="15%">শিক্ষার্থী আইডি</th>
                                            <th width="20%">নাম</th>
                                            <th width="15%">ফোন</th>
                                            <th width="15%">বর্তমান বিভাগ</th>
                                            <th width="20%">নতুন রোল নম্বর</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($students as $student): ?>
                                            <tr>
                                                <td>
                                                    <div class="form-check">
                                                        <input class="form-check-input student-checkbox" type="checkbox" name="selected_students[]" value="<?php echo $student['id']; ?>">
                                                    </div>
                                                </td>
                                                <td><?php echo $student['roll_number'] ?? 'N/A'; ?></td>
                                                <td><?php echo $student['student_id']; ?></td>
                                                <td><?php echo $student['first_name'] . ' ' . $student['last_name']; ?></td>
                                                <td><?php echo $student['phone'] ?? 'N/A'; ?></td>
                                                <td><?php echo $student['department_name'] ?? 'N/A'; ?></td>
                                                <td>
                                                    <input type="text" class="form-control" name="new_roll_number[<?php echo $student['id']; ?>]" value="<?php echo $student['roll_number']; ?>">
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>

                                <div class="promotion-actions">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <p class="mb-0"><strong>নির্বাচিত শিক্ষার্থী:</strong> <span id="selected_count">0</span></p>
                                        </div>
                                        <div>
                                            <button type="submit" name="promote_students" class="btn btn-success" id="promote_button" disabled>
                                                <i class="fas fa-arrow-up me-2"></i>নির্বাচিত শিক্ষার্থীদের উন্নীত করুন
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endif; ?>

            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Select all checkbox functionality
            $('#select_all').change(function() {
                $('.student-checkbox').prop('checked', $(this).prop('checked'));
                updateSelectedCount();
            });

            // Individual checkbox change
            $('.student-checkbox').change(function() {
                updateSelectedCount();
            });

            // Update selected count
            function updateSelectedCount() {
                var count = $('.student-checkbox:checked').length;
                $('#selected_count').text(count);

                // Enable/disable promote button
                if (count > 0) {
                    $('#promote_button').prop('disabled', false);
                } else {
                    $('#promote_button').prop('disabled', true);
                }

                // Update select all checkbox
                if (count === $('.student-checkbox').length) {
                    $('#select_all').prop('checked', true);
                } else {
                    $('#select_all').prop('checked', false);
                }
            }
        });
    </script>
</body>
</html>
