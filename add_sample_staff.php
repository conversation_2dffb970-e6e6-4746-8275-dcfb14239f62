<?php
// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "college_management";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Create a staff user first
$staffUserSql = "INSERT INTO users (username, password, user_type) 
                VALUES ('staff', ?, 'staff')";
$hashedPassword = password_hash('staff123', PASSWORD_DEFAULT);

$stmt = $conn->prepare($staffUserSql);
$stmt->bind_param("s", $hashedPassword);

if ($stmt->execute()) {
    $userId = $conn->insert_id;
    echo "Staff user created successfully.<br>";
    
    // Make sure we have at least one department
    $checkDeptSql = "SELECT id FROM departments LIMIT 1";
    $deptResult = $conn->query($checkDeptSql);
    
    $deptId = null;
    if ($deptResult->num_rows == 0) {
        // Create a department
        $createDeptSql = "INSERT INTO departments (department_name, description) 
                        VALUES ('Administration', 'Administration Department')";
        if ($conn->query($createDeptSql)) {
            $deptId = $conn->insert_id;
            echo "Department created successfully.<br>";
        }
    } else {
        $deptRow = $deptResult->fetch_assoc();
        $deptId = $deptRow['id'];
    }
    
    // Now create the staff member
    $staffSql = "INSERT INTO staff (staff_id, first_name, last_name, email, phone, 
                gender, joining_date, department_id, user_id) 
                VALUES ('STF001', 'Nasrin', 'Akter', '<EMAIL>', '01812345678', 
                'female', CURDATE(), ?, ?)";
                
    $stmt = $conn->prepare($staffSql);
    $stmt->bind_param("ii", $deptId, $userId);
    
    if ($stmt->execute()) {
        echo "Sample staff member added successfully.<br>";
        echo "<p>Staff login credentials:</p>";
        echo "<p>Username: staff</p>";
        echo "<p>Password: staff123</p>";
        echo "<p><a href='admin/dashboard.php'>Go to Admin Dashboard</a></p>";
    } else {
        echo "Error adding staff: " . $stmt->error;
    }
    
} else {
    echo "Error creating staff user: " . $stmt->error;
}

$conn->close();
?> 