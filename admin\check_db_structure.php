<?php
require_once '../includes/dbh.inc.php';

echo "<h1>Database Structure Check</h1>";

// Check if students table exists
$studentsTableCheck = $conn->query("SHOW TABLES LIKE 'students'");
if ($studentsTableCheck->num_rows > 0) {
    echo "<h2>Students Table Structure</h2>";
    $studentsStructure = $conn->query("DESCRIBE students");
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $studentsStructure->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>Students table does not exist!</p>";
}

// Check if exams table exists
$examsTableCheck = $conn->query("SHOW TABLES LIKE 'exams'");
if ($examsTableCheck->num_rows > 0) {
    echo "<h2>Exams Table Structure</h2>";
    $examsStructure = $conn->query("DESCRIBE exams");
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $examsStructure->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>Exams table does not exist!</p>";
}

// Check if results table exists
$resultsTableCheck = $conn->query("SHOW TABLES LIKE 'results'");
if ($resultsTableCheck->num_rows > 0) {
    echo "<h2>Results Table Structure</h2>";
    $resultsStructure = $conn->query("DESCRIBE results");
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $resultsStructure->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>Results table does not exist!</p>";
}
?>
