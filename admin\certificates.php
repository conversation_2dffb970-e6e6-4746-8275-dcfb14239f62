<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get certificate types for dropdowns
$certificateTypes = ["appreciation" => "প্রশংসা পত্র", "achievement" => "কৃতিত্ব সনদ", "participation" => "অংশগ্রহন সনদ", "completion" => "সমাপ্তি সনদ"];

// Get list of students for dropdowns
$studentsQuery = $conn->query("SELECT id, student_id, first_name, last_name FROM students ORDER BY first_name");
$studentsList = [];
while ($row = $studentsQuery->fetch_assoc()) {
    $studentsList[$row['id']] = $row['student_id'] . ' - ' . $row['first_name'] . ' ' . $row['last_name'];
}

$errorMessage = '';
$successMessage = '';

// Process certificate generation
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Handle the form submission for adding a certificate
    if (isset($_POST['action']) && $_POST['action'] === 'add') {
        $student_id = $_POST['student_id'];
        $title = $_POST['title'];
        $description = $_POST['description'];
        $certificate_date = $_POST['certificate_date'];
        $issued_by = $_POST['issued_by'];
        $certificate_type = $_POST['certificate_type'];

        // Validate inputs
        if (empty($student_id) || empty($title) || empty($description) || empty($certificate_date) || empty($issued_by) || empty($certificate_type)) {
            $errorMessage = 'সকল প্রয়োজনীয় ক্ষেত্র পূরণ করুন।';
        } else {
            // Insert certificate
            $stmt = $conn->prepare("INSERT INTO certificates (student_id, title, description, certificate_date, issued_by, certificate_type) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->bind_param("isssss", $student_id, $title, $description, $certificate_date, $issued_by, $certificate_type);

            if ($stmt->execute()) {
                $successMessage = 'সার্টিফিকেট সফলভাবে তৈরী করা হয়েছে।';
            } else {
                $errorMessage = 'সার্টিফিকেট তৈরী করতে সমস্যা হয়েছে: ' . $conn->error;
            }

            $stmt->close();
        }
    }

    // Handle certificate deletion
    else if (isset($_POST['action']) && $_POST['action'] === 'delete' && isset($_POST['certificate_id'])) {
        $certificate_id = $_POST['certificate_id'];

        $stmt = $conn->prepare("DELETE FROM certificates WHERE id = ?");
        $stmt->bind_param("i", $certificate_id);

        if ($stmt->execute()) {
            $successMessage = 'সার্টিফিকেট সফলভাবে মুছে ফেলা হয়েছে।';
        } else {
            $errorMessage = 'সার্টিফিকেট মুছতে সমস্যা হয়েছে: ' . $conn->error;
        }

        $stmt->close();
    }
}

// Check if certificates table exists
$tableExists = false;
$tablesResult = $conn->query("SHOW TABLES LIKE 'certificates'");
if ($tablesResult && $tablesResult->num_rows > 0) {
    $tableExists = true;
}

// Create certificates table if it doesn't exist
if (!$tableExists) {
    $createTableSQL = "CREATE TABLE certificates (
        id INT(11) NOT NULL AUTO_INCREMENT,
        student_id INT(11) NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT NOT NULL,
        certificate_date DATE NOT NULL,
        issued_by VARCHAR(255) NOT NULL,
        certificate_type VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
    )";

    if ($conn->query($createTableSQL)) {
        $successMessage = 'সার্টিফিকেট টেবিল সফলভাবে তৈরী করা হয়েছে।';
        $tableExists = true;
    } else {
        $errorMessage = 'সার্টিফিকেট টেবিল তৈরী করতে সমস্যা হয়েছে: ' . $conn->error;
    }
}

// Get all certificates with student names
$certificates = false;
if ($tableExists) {
    $certificatesQuery = "SELECT c.*, s.student_id, s.first_name, s.last_name
                         FROM certificates c
                         JOIN students s ON c.student_id = s.id
                         ORDER BY c.created_at DESC";
    $certificates = $conn->query($certificatesQuery);
}

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>শিক্ষার্থীর প্রশংসা পত্র - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .certificate-preview {
            border: 2px solid #ddd;
            padding: 20px;
            margin-bottom: 20px;
            position: relative;
            background-color: #f9f9f9;
        }
        .certificate-preview h3 {
            color: #007bff;
            text-align: center;
            margin-bottom: 15px;
            font-family: 'Arial', sans-serif;
        }
        .certificate-content {
            text-align: center;
            margin-bottom: 20px;
        }
        .certificate-footer {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }
        .certificate-watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-30deg);
            opacity: 0.07;
            font-size: 7rem;
            z-index: 0;
            color: #000;
            white-space: nowrap;
        }
        .certificate-body {
            position: relative;
            z-index: 1;
        }
        .search-filters {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .certificate-actions {
            text-align: center;
            margin-top: 15px;
        }
        .modal-xl {
            max-width: 1100px;
        }
        #certificateForm {
            max-width: 800px;
            margin: 0 auto;
        }
        .certificate-template {
            border: 1px solid #ddd;
            padding: 20px;
            margin-top: 15px;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            border-radius: 5px;
        }
        .template-preview {
            background-color: #fff;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> কোর্স
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="departments.php">
                            <i class="fas fa-building me-2"></i> বিভাগ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subject_categories.php">
                            <i class="fas fa-tags me-2"></i> বিষয় ক্যাটাগরি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="certificates.php">
                            <i class="fas fa-certificate me-2"></i> সার্টিফিকেট
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="advanced_certificate.php">
                            <i class="fas fa-certificate me-2"></i> উন্নত সার্টিফিকেট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="simple_certificate.php">
                            <i class="fas fa-file-alt me-2"></i> সাধারণ সার্টিফিকেট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="upload_watermark.php">
                            <i class="fas fa-image me-2"></i> ওয়াটারমার্ক আপলোড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="remove_bengali_certificates.php">
                            <i class="fas fa-trash me-2"></i> বাংলা সার্টিফিকেট মুছুন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি ম্যানেজমেন্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="notices.php">
                            <i class="fas fa-bullhorn me-2"></i> নোটিশ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>শিক্ষার্থীর প্রশংসা পত্র</h2>
                        <p class="text-muted">শিক্ষার্থীর জন্য সার্টিফিকেট তৈরী করুন এবং ব্যবস্থাপনা করুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="advanced_certificate.php" class="btn btn-success me-2">
                            <i class="fas fa-certificate me-2"></i>উন্নত সার্টিফিকেট সিস্টেম
                        </a>
                        <a href="simple_certificate.php" class="btn btn-info me-2">
                            <i class="fas fa-file-alt me-2"></i>সাধারণ সার্টিফিকেট
                        </a>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCertificateModal">
                            <i class="fas fa-plus-circle me-2"></i>নতুন সার্টিফিকেট তৈরী করুন
                        </button>
                    </div>
                </div>

                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Search and filter section -->
                <div class="search-filters mb-4">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="searchStudent" class="form-label">শিক্ষার্থী খুঁজুন:</label>
                            <input type="text" class="form-control" id="searchStudent" placeholder="শিক্ষার্থীর নাম বা আইডি দিয়ে খুঁজুন">
                        </div>
                        <div class="col-md-4">
                            <label for="filterCertificateType" class="form-label">সার্টিফিকেটের ধরন:</label>
                            <select class="form-select" id="filterCertificateType">
                                <option value="">সকল ধরন</option>
                                <?php foreach ($certificateTypes as $typeValue => $typeText): ?>
                                    <option value="<?= $typeValue ?>"><?= $typeText ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="filterDate" class="form-label">তারিখ অনুযায়ী:</label>
                            <input type="month" class="form-control" id="filterDate">
                        </div>
                    </div>
                </div>

                <!-- Certificate List -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">সার্টিফিকেটসমূহ</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="certificatesTable">
                                <thead>
                                    <tr>
                                        <th>শিক্ষার্থীর আইডি</th>
                                        <th>শিক্ষার্থীর নাম</th>
                                        <th>সার্টিফিকেটের নাম</th>
                                        <th>ধরন</th>
                                        <th>ইস্যু তারিখ</th>
                                        <th>ইস্যু করেছেন</th>
                                        <th>অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!$tableExists): ?>
                                        <tr>
                                            <td colspan="7" class="text-center">সার্টিফিকেট টেবিল এখনো তৈরী করা হয়নি। উপরে "নতুন সার্টিফিকেট তৈরী করুন" বাটনে ক্লিক করে একটি সার্টিফিকেট তৈরী করুন।</td>
                                        </tr>
                                    <?php elseif ($certificates && $certificates->num_rows > 0): ?>
                                        <?php while ($certificate = $certificates->fetch_assoc()): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($certificate['student_id']); ?></td>
                                                <td><?php echo htmlspecialchars($certificate['first_name'] . ' ' . $certificate['last_name']); ?></td>
                                                <td><?php echo htmlspecialchars($certificate['title']); ?></td>
                                                <td>
                                                    <?php
                                                        echo isset($certificateTypes[$certificate['certificate_type']])
                                                            ? $certificateTypes[$certificate['certificate_type']]
                                                            : $certificate['certificate_type'];
                                                    ?>
                                                </td>
                                                <td><?php echo date('d/m/Y', strtotime($certificate['certificate_date'])); ?></td>
                                                <td><?php echo htmlspecialchars($certificate['issued_by']); ?></td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button type="button" class="btn btn-sm btn-primary view-certificate"
                                                                data-id="<?php echo $certificate['id']; ?>"
                                                                data-title="<?php echo htmlspecialchars($certificate['title']); ?>"
                                                                data-description="<?php echo htmlspecialchars($certificate['description']); ?>"
                                                                data-date="<?php echo date('d/m/Y', strtotime($certificate['certificate_date'])); ?>"
                                                                data-issued-by="<?php echo htmlspecialchars($certificate['issued_by']); ?>"
                                                                data-student-name="<?php echo htmlspecialchars($certificate['first_name'] . ' ' . $certificate['last_name']); ?>"
                                                                data-certificate-type="<?php echo $certificate['certificate_type']; ?>">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-success print-certificate"
                                                                data-id="<?php echo $certificate['id']; ?>">
                                                            <i class="fas fa-print"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-danger delete-certificate"
                                                                data-id="<?php echo $certificate['id']; ?>"
                                                                data-student-name="<?php echo htmlspecialchars($certificate['first_name'] . ' ' . $certificate['last_name']); ?>"
                                                                data-title="<?php echo htmlspecialchars($certificate['title']); ?>">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="7" class="text-center">কোন সার্টিফিকেট পাওয়া যায়নি</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Certificate Modal -->
    <div class="modal fade" id="addCertificateModal" tabindex="-1" aria-labelledby="addCertificateModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="addCertificateModalLabel">নতুন সার্টিফিকেট তৈরী করুন</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="certificateForm" method="POST" action="certificates.php">
                        <input type="hidden" name="action" value="add">

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="student_id" class="form-label">শিক্ষার্থী নির্বাচন করুন*</label>
                                <select class="form-select" id="student_id" name="student_id" required>
                                    <option value="">শিক্ষার্থী নির্বাচন করুন</option>
                                    <?php foreach ($studentsList as $id => $name): ?>
                                        <option value="<?php echo $id; ?>"><?php echo htmlspecialchars($name); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="certificate_type" class="form-label">সার্টিফিকেটের ধরন*</label>
                                <select class="form-select" id="certificate_type" name="certificate_type" required>
                                    <?php foreach ($certificateTypes as $typeValue => $typeText): ?>
                                        <option value="<?= $typeValue ?>" <?= $typeValue === 'appreciation' ? 'selected' : '' ?>><?= $typeText ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="title" class="form-label">সার্টিফিকেটের শিরোনাম*</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">বিবরণ*</label>
                            <textarea class="form-control" id="description" name="description" rows="4" required></textarea>
                            <div class="form-text">সার্টিফিকেটের মূল টেক্সট এবং শিক্ষার্থীর অর্জন সম্পর্কে বিস্তারিত লিখুন।</div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="certificate_date" class="form-label">সার্টিফিকেটের তারিখ*</label>
                                <input type="date" class="form-control" id="certificate_date" name="certificate_date" value="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="issued_by" class="form-label">ইস্যুকারী*</label>
                                <input type="text" class="form-control" id="issued_by" name="issued_by" placeholder="প্রধান শিক্ষক / অধ্যক্ষ / বিভাগীয় প্রধান" required>
                            </div>
                        </div>

                        <div class="certificate-template">
                            <h5 class="mb-3">সার্টিফিকেট টেমপ্লেট প্রিভিউ</h5>
                            <div class="template-preview">
                                <div id="certificatePreview" class="certificate-preview">
                                    <div class="certificate-watermark">ZFAW</div>
                                    <div class="certificate-body">
                                        <h3 id="previewTitle">প্রশংসা সার্টিফিকেট</h3>
                                        <div class="certificate-content">
                                            <h4 id="previewStudentName">শিক্ষার্থীর নাম</h4>
                                            <p class="mt-4" id="previewDescription">সার্টিফিকেটের বিবরণ এখানে প্রদর্শিত হবে।</p>
                                        </div>
                                        <div class="certificate-footer">
                                            <div class="certificate-date" id="previewDate"><?php echo date('d/m/Y'); ?></div>
                                            <div class="certificate-signature" id="previewIssuedBy">ইস্যুকারীর নাম</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল করুন</button>
                            <button type="submit" class="btn btn-primary">সার্টিফিকেট তৈরী করুন</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- View Certificate Modal -->
    <div class="modal fade" id="viewCertificateModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">সার্টিফিকেট প্রিভিউ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="certificate-preview">
                        <div class="certificate-watermark">ZFAW</div>
                        <div class="certificate-body">
                            <h3 id="modalTitle">প্রশংসা সার্টিফিকেট</h3>
                            <div class="certificate-content">
                                <h4 id="modalStudentName">শিক্ষার্থীর নাম</h4>
                                <p class="mt-4" id="modalDescription"></p>
                            </div>
                            <div class="certificate-footer">
                                <div class="certificate-date" id="modalDate"></div>
                                <div class="certificate-signature" id="modalIssuedBy"></div>
                            </div>
                        </div>
                    </div>
                    <div class="certificate-actions">
                        <button class="btn btn-success" id="modalPrintBtn">
                            <i class="fas fa-print me-2"></i>প্রিন্ট করুন
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Certificate Modal -->
    <div class="modal fade" id="deleteCertificateModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">সার্টিফিকেট মুছুন</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>আপনি কি নিশ্চিত যে আপনি "<span id="deleteStudentName"></span>" এর "<span id="deleteCertificateTitle"></span>" সার্টিফিকেট মুছতে চান?</p>
                    <p class="text-danger"><strong>সতর্কতা:</strong> এই সার্টিফিকেট স্থায়ীভাবে মুছে ফেলা হবে।</p>
                </div>
                <div class="modal-footer">
                    <form method="POST" action="certificates.php">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="certificate_id" id="deleteCertificateId">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল করুন</button>
                        <button type="submit" class="btn btn-danger">মুছে ফেলুন</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Print Certificate Iframe (hidden) -->
    <iframe id="printFrame" style="display:none;"></iframe>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="js/certificates.js"></script>
