<?php
session_start();

// Check if user is logged in as admin or teacher
if (!isset($_SESSION['userId']) || ($_SESSION['userType'] !== 'admin' && $_SESSION['userType'] !== 'teacher')) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Authentication required']);
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if department_id is set
if (!isset($_GET['department_id']) || empty($_GET['department_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Department ID is required']);
    exit();
}

$departmentId = intval($_GET['department_id']);

// Get classes for the selected department
$stmt = $conn->prepare("SELECT id, class_name FROM classes WHERE department_id = ? ORDER BY class_name");
$stmt->bind_param("i", $departmentId);
$stmt->execute();
$result = $stmt->get_result();

$classes = [];
while ($row = $result->fetch_assoc()) {
    $classes[] = [
        'id' => $row['id'],
        'class_name' => $row['class_name']
    ];
}

// Return data as JSON
header('Content-Type: application/json');
echo json_encode($classes);
exit();
?> 