<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Include heading functions if not already included
if (!function_exists('has_institution_logo')) {
    require_once '../includes/heading_functions.php';
}

// Get parameters from URL
$examId = isset($_GET['exam_id']) ? intval($_GET['exam_id']) : 0;
$classId = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;
$departmentId = isset($_GET['department_id']) ? intval($_GET['department_id']) : 0;
$sessionId = isset($_GET['session_id']) ? intval($_GET['session_id']) : 0;
$searchTerm = isset($_GET['search']) ? trim($_GET['search']) : '';
$sortBy = isset($_GET['sort_by']) ? $_GET['sort_by'] : 'roll_number';
$studentId = isset($_GET['student_id']) ? intval($_GET['student_id']) : 0;

// Check if institution logo exists
$hasLogo = false;
$logoUrl = '';
$logoPath = '../uploads/signatures/institution_logo.png';
if (file_exists($logoPath)) {
    $hasLogo = true;
    $logoUrl = $logoPath . '?v=' . time(); // Add timestamp to prevent caching
}

// If no logo exists, create a default logo path
$defaultLogoPath = '../assets/images/default_logo.png';
if (!$hasLogo && !file_exists($defaultLogoPath)) {
    // Create directory if it doesn't exist
    if (!file_exists('../assets/images')) {
        mkdir('../assets/images', 0777, true);
    }

    // Use a placeholder image
    $defaultLogoUrl = 'https://via.placeholder.com/80x80?text=ZFAW';
    file_put_contents($defaultLogoPath, file_get_contents($defaultLogoUrl));
}

// Initialize variables
$selectedExam = null;
$students = null;
$subjectsByDate = [];

// Get all classes, departments, and sessions for dropdowns
$classes = $conn->query("SELECT * FROM classes ORDER BY class_name");
$departments = $conn->query("SELECT * FROM departments ORDER BY department_name");
$sessions = $conn->query("SELECT * FROM sessions ORDER BY session_name DESC");

// Get students for dropdown if exam is selected
$studentsForDropdown = null;
if ($examId > 0) {
    $studentsDropdownQuery = "SELECT s.id, s.first_name, s.last_name, s.roll_number, s.student_id, c.class_name, d.department_name
                             FROM students s
                             LEFT JOIN classes c ON s.class_id = c.id
                             LEFT JOIN departments d ON s.department_id = d.id
                             WHERE 1=1";

    // Add filters based on exam
    if (!empty($selectedExam['class_id'])) {
        $studentsDropdownQuery .= " AND s.class_id = " . intval($selectedExam['class_id']);
    }

    if (!empty($selectedExam['department_id'])) {
        $studentsDropdownQuery .= " AND s.department_id = " . intval($selectedExam['department_id']);
    }

    if (!empty($selectedExam['session_id'])) {
        $studentsDropdownQuery .= " AND s.session_id = " . intval($selectedExam['session_id']);
    }

    $studentsDropdownQuery .= " ORDER BY s.roll_number, s.first_name, s.last_name";
    $studentsForDropdown = $conn->query($studentsDropdownQuery);
}

if ($examId > 0) {
    // Get exam details
    $examQuery = "SELECT e.*, c.class_name, d.department_name, s.session_name
                 FROM exams e
                 LEFT JOIN classes c ON e.class_id = c.id
                 LEFT JOIN departments d ON e.department_id = d.id
                 LEFT JOIN sessions s ON e.session_id = s.id
                 WHERE e.id = ?";
    $stmt = $conn->prepare($examQuery);
    $stmt->bind_param("i", $examId);
    $stmt->execute();
    $selectedExam = $stmt->get_result()->fetch_assoc();

    if ($selectedExam) {
        // Get exam subjects with routine information
        $subjectsQuery = "SELECT esr.*, s.subject_name, s.subject_code, er.exam_date, er.start_time, er.end_time, er.room_no
                         FROM exam_subject_relations esr
                         JOIN subjects s ON esr.subject_id = s.id
                         LEFT JOIN exam_routine er ON esr.exam_id = er.exam_id AND esr.subject_id = er.subject_id
                         WHERE esr.exam_id = ?
                         ORDER BY er.exam_date, s.subject_name";
        $stmt = $conn->prepare($subjectsQuery);
        $stmt->bind_param("i", $examId);
        $stmt->execute();
        $examSubjects = $stmt->get_result();

        // Group subjects by date
        $subjectsByDate = [];
        while ($subject = $examSubjects->fetch_assoc()) {
            $date = !empty($subject['exam_date']) ? $subject['exam_date'] : 'unscheduled';
            if (!isset($subjectsByDate[$date])) {
                $subjectsByDate[$date] = [];
            }
            $subjectsByDate[$date][] = $subject;
        }

        // Sort dates
        ksort($subjectsByDate);

        // Get students
        $studentsQuery = "SELECT s.*, c.class_name, d.department_name, ss.session_name,
                         GROUP_CONCAT(DISTINCT sub.id) as subject_ids,
                         GROUP_CONCAT(DISTINCT sub.subject_name) as subject_names,
                         GROUP_CONCAT(DISTINCT sub.subject_code) as subject_codes
                         FROM students s
                         LEFT JOIN classes c ON s.class_id = c.id
                         LEFT JOIN departments d ON s.department_id = d.id
                         LEFT JOIN sessions ss ON s.session_id = ss.id
                         LEFT JOIN student_subjects ss_rel ON s.id = ss_rel.student_id
                         LEFT JOIN subjects sub ON ss_rel.subject_id = sub.id
                         WHERE 1=1";

        // Add filters based on exam and user selection
        if (!empty($selectedExam['class_id']) && $classId == 0) {
            $studentsQuery .= " AND s.class_id = " . intval($selectedExam['class_id']);
        } elseif ($classId > 0) {
            $studentsQuery .= " AND s.class_id = " . $classId;
        }

        if (!empty($selectedExam['department_id']) && $departmentId == 0) {
            $studentsQuery .= " AND s.department_id = " . intval($selectedExam['department_id']);
        } elseif ($departmentId > 0) {
            $studentsQuery .= " AND s.department_id = " . $departmentId;
        }

        if (!empty($selectedExam['session_id']) && $sessionId == 0) {
            $studentsQuery .= " AND s.session_id = " . intval($selectedExam['session_id']);
        } elseif ($sessionId > 0) {
            $studentsQuery .= " AND s.session_id = " . $sessionId;
        }

        // Add search term filter
        if (!empty($searchTerm)) {
            $studentsQuery .= " AND (s.first_name LIKE '%" . $conn->real_escape_string($searchTerm) . "%'
                               OR s.last_name LIKE '%" . $conn->real_escape_string($searchTerm) . "%'
                               OR s.roll_number LIKE '%" . $conn->real_escape_string($searchTerm) . "%'
                               OR s.student_id LIKE '%" . $conn->real_escape_string($searchTerm) . "%')";
        }

        // Add specific student filter
        if ($studentId > 0) {
            $studentsQuery .= " AND s.id = " . $studentId;
        }

        // Add sorting
        $studentsQuery .= " GROUP BY s.id";

        switch ($sortBy) {
            case 'name':
                $studentsQuery .= " ORDER BY s.first_name, s.last_name";
                break;
            case 'department':
                $studentsQuery .= " ORDER BY d.department_name, s.roll_number";
                break;
            case 'roll_number':
            default:
                $studentsQuery .= " ORDER BY s.roll_number, s.first_name, s.last_name";
                break;
        }
        $students = $conn->query($studentsQuery);
    }
}

// Get all exams for the dropdown
$examsQuery = "SELECT e.*, c.class_name, d.department_name, s.session_name
               FROM exams e
               LEFT JOIN classes c ON e.class_id = c.id
               LEFT JOIN departments d ON e.department_id = d.id
               LEFT JOIN sessions s ON e.session_id = s.id
               ORDER BY e.exam_date DESC, e.exam_name";
$exams = $conn->query($examsQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শিক্ষার্থী পরীক্ষা হাজিরা পত্র (অর্ধেক পেজ)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Hind Siliguri', Arial, sans-serif;
            background-color: #f8f9fa;
        }

        /* Print styles for half page */
        @media print {
            body * {
                visibility: hidden;
            }
            .print-section, .print-section * {
                visibility: visible;
            }
            .print-section {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                max-width: 100%;
                font-size: 10px;
                padding: 0;
                margin: 0;
                box-sizing: border-box;
                background-color: white !important;
                color: black !important;
            }
            .no-print {
                display: none !important;
            }
            .page-break {
                page-break-after: always;
            }

            /* Ensure page fits on A4 paper - Half page */
            @page {
                size: A4 portrait;
                margin: 0.3cm;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            /* Half page specific styles */
            .attendance-card {
                height: 13.5cm !important; /* Half of A4 height minus margins */
                max-height: 13.5cm !important;
                overflow: hidden !important;
                box-shadow: none !important;
                border: 1px solid #000 !important;
                margin-bottom: 0.5cm !important;
                page-break-inside: avoid !important;
            }

            /* Compact header for half page */
            .attendance-header {
                margin-bottom: 8px !important;
                padding-bottom: 6px !important;
            }

            .attendance-header h3 {
                font-size: 14px !important;
                margin: 0 0 3px 0 !important;
            }

            .attendance-header h4 {
                font-size: 12px !important;
                margin: 0 0 4px 0 !important;
            }

            .header-logo img {
                max-width: 50px !important;
                max-height: 50px !important;
            }

            /* Compact student info */
            .student-info {
                margin: 8px 0 !important;
                padding: 6px 8px !important;
            }

            .student-info-row {
                margin-bottom: 4px !important;
            }

            .student-info-label {
                width: 60px !important;
                font-size: 9px !important;
            }

            .student-info-value {
                padding: 2px 4px !important;
                min-height: 16px !important;
                font-size: 9px !important;
            }

            /* Compact table */
            .attendance-table {
                font-size: 8px !important;
                margin-top: 8px !important;
            }

            .attendance-table th,
            .attendance-table td {
                padding: 2px 1px !important;
                height: 20px !important;
                border: 1px solid #000 !important;
                font-size: 8px !important;
            }

            .attendance-table th {
                font-size: 8px !important;
                background-color: #f0f0f0 !important;
            }

            /* Compact signature area */
            .signature-area {
                margin-top: 8px !important;
            }

            .signature-line {
                font-size: 8px !important;
                padding-top: 3px !important;
            }

            /* Two cards per page - better control */
            .page-break {
                page-break-after: always !important;
                height: 0 !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            /* Ensure proper spacing between cards */
            .attendance-card + .attendance-card {
                margin-top: 0.5cm !important;
            }

            /* Force page break after every second card */
            .attendance-card:nth-child(2n) {
                margin-bottom: 0 !important;
            }
        }

        /* Screen styles remain similar but more compact */
        .attendance-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 15px;
            margin-bottom: 20px;
            max-width: 190mm;
            margin-left: auto;
            margin-right: auto;
            height: auto;
            max-height: 14cm; /* Limit height for half page */
        }

        .attendance-header {
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 2px solid #3498db;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .header-content {
            display: grid;
            grid-template-columns: 20% 60% 20%;
            width: 100%;
            align-items: center;
        }

        .header-logo {
            text-align: left;
            padding: 5px;
        }

        .header-logo img {
            max-width: 60px;
            max-height: 60px;
            border-radius: 5px;
        }

        .header-text {
            text-align: center;
        }

        .header-right {
            text-align: right;
        }

        .attendance-header h3 {
            margin: 0 0 3px 0;
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }

        .attendance-header h4 {
            margin: 0 0 5px 0;
            font-size: 14px;
            font-weight: bold;
            color: #3498db;
        }

        .student-info {
            margin: 10px 0;
            background-color: #f8f9fa;
            padding: 8px 10px;
            border-radius: 6px;
            border-left: 3px solid #3498db;
        }

        .student-info-row {
            display: flex;
            margin-bottom: 6px;
            align-items: center;
        }

        .student-info-row:last-child {
            margin-bottom: 0;
        }

        .student-info-label {
            width: 70px;
            font-weight: bold;
            color: #2c3e50;
            font-size: 12px;
        }

        .student-info-value {
            flex: 1;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 4px 6px;
            min-height: 18px;
            background-color: white;
            font-size: 12px;
        }

        .attendance-table {
            border-collapse: collapse;
            width: 100%;
            table-layout: fixed;
            border: 2px solid #3498db;
            margin-top: 10px;
            font-size: 10px;
        }

        .attendance-table th, .attendance-table td {
            border: 1px solid #3498db;
            padding: 3px 2px;
            text-align: center;
            vertical-align: middle;
            height: 24px;
        }

        .attendance-table th {
            font-weight: bold;
            font-size: 10px;
            background-color: #3498db;
            color: white;
            text-transform: uppercase;
        }

        .attendance-table td {
            font-size: 9px;
        }

        .attendance-table tr:nth-child(even) {
            background-color: #f0f7fc;
        }

        .signature-area {
            margin-top: 12px;
            display: flex;
            justify-content: space-between;
            padding: 0 8px;
        }

        .signature-box {
            width: 30%;
            text-align: center;
        }

        .signature-line {
            border-top: 1px solid #3498db;
            padding-top: 4px;
            font-weight: bold;
            color: #2c3e50;
            font-size: 10px;
        }

        .print-btn {
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            font-weight: bold;
            transition: background-color 0.3s;
        }

        .print-btn:hover {
            background-color: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12 p-4">
                <div class="d-flex justify-content-between align-items-center mb-4 no-print">
                    <h2 class="text-primary"><i class="fas fa-clipboard-check me-2"></i>শিক্ষার্থী পরীক্ষা হাজিরা পত্র (অর্ধেক পেজ)</h2>
                    <div>
                        <?php if ($examId > 0): ?>
                            <a href="student_exam_attendance.php?<?php echo http_build_query($_GET); ?>" class="btn btn-info me-2">
                                <i class="fas fa-expand me-2"></i>ফুল পেজ ভার্সন
                            </a>
                        <?php endif; ?>
                        <button onclick="window.print();" class="print-btn">
                            <i class="fas fa-print me-2"></i>প্রিন্ট করুন
                        </button>
                    </div>
                </div>

                <div class="card mb-4 no-print" style="border-radius: 10px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);">
                    <div class="card-header bg-primary text-white" style="border-radius: 10px 10px 0 0;">
                        <h5 class="mb-0"><i class="fas fa-search me-2"></i>উন্নত অনুসন্ধান</h5>
                    </div>
                    <div class="card-body">
                        <form action="" method="get" class="row g-3">
                            <!-- পরীক্ষা নির্বাচন -->
                            <div class="col-md-6">
                                <label for="exam_id" class="form-label fw-bold">পরীক্ষা <span class="text-danger">*</span></label>
                                <select name="exam_id" id="exam_id" class="form-select shadow-sm" required>
                                    <option value="">পরীক্ষা নির্বাচন করুন</option>
                                    <?php while ($exam = $exams->fetch_assoc()): ?>
                                        <option value="<?php echo $exam['id']; ?>" <?php echo ($examId == $exam['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($exam['exam_name'] . ' - ' . $exam['exam_type'] .
                                                  ' (' . ($exam['class_name'] ?? 'সকল শ্রেণী') .
                                                  ', ' . ($exam['department_name'] ?? 'সকল বিভাগ') .
                                                  ', ' . ($exam['session_name'] ?? 'N/A') . ')'); ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>

                            <!-- শ্রেণী নির্বাচন -->
                            <div class="col-md-6">
                                <label for="class_id" class="form-label fw-bold">শ্রেণী</label>
                                <select name="class_id" id="class_id" class="form-select shadow-sm">
                                    <option value="0">সকল শ্রেণী</option>
                                    <?php
                                    if ($classes) {
                                        $classes->data_seek(0);
                                        while ($class = $classes->fetch_assoc()):
                                    ?>
                                        <option value="<?php echo $class['id']; ?>" <?php echo ($classId == $class['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($class['class_name']); ?>
                                        </option>
                                    <?php
                                        endwhile;
                                    }
                                    ?>
                                </select>
                            </div>

                            <!-- বিভাগ নির্বাচন -->
                            <div class="col-md-6">
                                <label for="department_id" class="form-label fw-bold">বিভাগ</label>
                                <select name="department_id" id="department_id" class="form-select shadow-sm">
                                    <option value="0">সকল বিভাগ</option>
                                    <?php
                                    if ($departments) {
                                        $departments->data_seek(0);
                                        while ($department = $departments->fetch_assoc()):
                                    ?>
                                        <option value="<?php echo $department['id']; ?>" <?php echo ($departmentId == $department['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($department['department_name']); ?>
                                        </option>
                                    <?php
                                        endwhile;
                                    }
                                    ?>
                                </select>
                            </div>

                            <!-- সেশন নির্বাচন -->
                            <div class="col-md-6">
                                <label for="session_id" class="form-label fw-bold">সেশন</label>
                                <select name="session_id" id="session_id" class="form-select shadow-sm">
                                    <option value="0">সকল সেশন</option>
                                    <?php
                                    if ($sessions) {
                                        $sessions->data_seek(0);
                                        while ($session = $sessions->fetch_assoc()):
                                    ?>
                                        <option value="<?php echo $session['id']; ?>" <?php echo ($sessionId == $session['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($session['session_name']); ?>
                                        </option>
                                    <?php
                                        endwhile;
                                    }
                                    ?>
                                </select>
                            </div>

                            <!-- শিক্ষার্থী অনুসন্ধান -->
                            <div class="col-md-6">
                                <label for="search" class="form-label fw-bold">শিক্ষার্থী অনুসন্ধান</label>
                                <input type="text" name="search" id="search" class="form-control shadow-sm"
                                       placeholder="নাম, রোল, বা আইডি দিয়ে খুঁজুন"
                                       value="<?php echo htmlspecialchars($searchTerm); ?>">
                            </div>

                            <!-- শিক্ষার্থী নির্বাচন -->
                            <div class="col-md-6">
                                <label for="student_id" class="form-label fw-bold">শিক্ষার্থী নির্বাচন</label>
                                <select name="student_id" id="student_id" class="form-select shadow-sm">
                                    <option value="0">সকল শিক্ষার্থী</option>
                                    <?php
                                    if ($studentsForDropdown && $examId > 0) {
                                        while ($studentOption = $studentsForDropdown->fetch_assoc()):
                                    ?>
                                        <option value="<?php echo $studentOption['id']; ?>" <?php echo ($studentId == $studentOption['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($studentOption['roll_number'] . ' - ' . $studentOption['first_name'] . ' ' . $studentOption['last_name'] .
                                                  ' (' . $studentOption['student_id'] . ')'); ?>
                                        </option>
                                    <?php
                                        endwhile;
                                    }
                                    ?>
                                </select>
                            </div>

                            <!-- সাজানোর বিকল্প -->
                            <div class="col-md-6">
                                <label for="sort_by" class="form-label fw-bold">সাজানোর বিকল্প</label>
                                <select name="sort_by" id="sort_by" class="form-select shadow-sm">
                                    <option value="roll_number" <?php echo ($sortBy == 'roll_number') ? 'selected' : ''; ?>>রোল অনুযায়ী</option>
                                    <option value="name" <?php echo ($sortBy == 'name') ? 'selected' : ''; ?>>নাম অনুযায়ী</option>
                                    <option value="department" <?php echo ($sortBy == 'department') ? 'selected' : ''; ?>>বিভাগ অনুযায়ী</option>
                                </select>
                            </div>

                            <!-- অনুসন্ধান বাটন -->
                            <div class="col-md-12 mt-4">
                                <button type="submit" class="print-btn">
                                    <i class="fas fa-search me-2"></i>অনুসন্ধান করুন
                                </button>

                                <?php if ($examId > 0): ?>
                                <a href="student_exam_attendance_half.php" class="btn btn-outline-secondary ms-2">
                                    <i class="fas fa-redo me-2"></i>রিসেট করুন
                                </a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>

                <?php if ($examId > 0): ?>
                    <!-- ফিল্টার সারাংশ -->
                    <div class="card mb-4 no-print" style="border-radius: 10px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);">
                        <div class="card-body py-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="badge bg-primary me-2">
                                        <i class="fas fa-filter me-1"></i> ফিল্টার:
                                    </span>

                                    <?php if ($selectedExam): ?>
                                        <span class="badge bg-info me-2">
                                            <i class="fas fa-file-alt me-1"></i> পরীক্ষা: <?php echo htmlspecialchars($selectedExam['exam_name'] . ' - ' . $selectedExam['exam_type']); ?>
                                        </span>
                                    <?php endif; ?>

                                    <?php if ($classId > 0): ?>
                                        <span class="badge bg-secondary me-2">
                                            <i class="fas fa-graduation-cap me-1"></i> শ্রেণী:
                                            <?php
                                                $classes->data_seek(0);
                                                while ($class = $classes->fetch_assoc()) {
                                                    if ($class['id'] == $classId) {
                                                        echo htmlspecialchars($class['class_name']);
                                                        break;
                                                    }
                                                }
                                            ?>
                                        </span>
                                    <?php endif; ?>

                                    <?php if ($departmentId > 0): ?>
                                        <span class="badge bg-secondary me-2">
                                            <i class="fas fa-building me-1"></i> বিভাগ:
                                            <?php
                                                $departments->data_seek(0);
                                                while ($department = $departments->fetch_assoc()) {
                                                    if ($department['id'] == $departmentId) {
                                                        echo htmlspecialchars($department['department_name']);
                                                        break;
                                                    }
                                                }
                                            ?>
                                        </span>
                                    <?php endif; ?>

                                    <?php if ($sessionId > 0): ?>
                                        <span class="badge bg-secondary me-2">
                                            <i class="fas fa-calendar-alt me-1"></i> সেশন:
                                            <?php
                                                $sessions->data_seek(0);
                                                while ($session = $sessions->fetch_assoc()) {
                                                    if ($session['id'] == $sessionId) {
                                                        echo htmlspecialchars($session['session_name']);
                                                        break;
                                                    }
                                                }
                                            ?>
                                        </span>
                                    <?php endif; ?>

                                    <?php if (!empty($searchTerm)): ?>
                                        <span class="badge bg-warning text-dark me-2">
                                            <i class="fas fa-search me-1"></i> অনুসন্ধান: <?php echo htmlspecialchars($searchTerm); ?>
                                        </span>
                                    <?php endif; ?>

                                    <?php if ($studentId > 0 && $studentsForDropdown): ?>
                                        <span class="badge bg-secondary me-2">
                                            <i class="fas fa-user-graduate me-1"></i> শিক্ষার্থী:
                                            <?php
                                                $studentsForDropdown->data_seek(0);
                                                while ($studentOption = $studentsForDropdown->fetch_assoc()) {
                                                    if ($studentOption['id'] == $studentId) {
                                                        echo htmlspecialchars($studentOption['roll_number'] . ' - ' . $studentOption['first_name'] . ' ' . $studentOption['last_name']);
                                                        break;
                                                    }
                                                }
                                            ?>
                                        </span>
                                    <?php endif; ?>

                                    <span class="badge bg-dark me-2">
                                        <i class="fas fa-sort me-1"></i> সাজানো:
                                        <?php
                                            switch ($sortBy) {
                                                case 'name':
                                                    echo 'নাম অনুযায়ী';
                                                    break;
                                                case 'department':
                                                    echo 'বিভাগ অনুযায়ী';
                                                    break;
                                                case 'roll_number':
                                                default:
                                                    echo 'রোল অনুযায়ী';
                                                    break;
                                            }
                                        ?>
                                    </span>
                                </div>

                                <?php if ($students && $students->num_rows > 0): ?>
                                    <span class="badge bg-success">
                                        <i class="fas fa-users me-1"></i> মোট শিক্ষার্থী: <?php echo $students->num_rows; ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($students && $students->num_rows > 0): ?>
                    <div class="print-section">
                        <?php
                        // Translate day names to Bengali
                        $dayTranslations = [
                            'Monday' => 'সোমবার',
                            'Tuesday' => 'মঙ্গলবার',
                            'Wednesday' => 'বুধবার',
                            'Thursday' => 'বৃহস্পতিবার',
                            'Friday' => 'শুক্রবার',
                            'Saturday' => 'শনিবার',
                            'Sunday' => 'রবিবার'
                        ];

                        // Prepare all subjects with dates
                        $allSubjects = [];

                        // Add scheduled subjects
                        foreach ($subjectsByDate as $date => $subjects) {
                            if ($date === 'unscheduled') continue;

                            foreach ($subjects as $subject) {
                                $formattedDate = date('d/m/Y', strtotime($date));
                                $dayName = date('l', strtotime($date));
                                $bengaliDayName = $dayTranslations[$dayName] ?? $dayName;

                                $subject['formatted_date'] = $formattedDate;
                                $subject['day_name'] = $bengaliDayName;
                                $allSubjects[] = $subject;
                            }
                        }

                        // Add unscheduled subjects
                        if (isset($subjectsByDate['unscheduled']) && !empty($subjectsByDate['unscheduled'])) {
                            foreach ($subjectsByDate['unscheduled'] as $subject) {
                                $subject['formatted_date'] = '';
                                $subject['day_name'] = '';
                                $allSubjects[] = $subject;
                            }
                        }

                        // Create a page for each student - but show 2 per page
                        $students->data_seek(0); // Reset the result pointer
                        $studentCount = 0;
                        while ($student = $students->fetch_assoc()):
                            // Get student's subjects
                            $studentSubjectIds = !empty($student['subject_ids']) ? explode(',', $student['subject_ids']) : [];

                            // Filter subjects for this student
                            $studentSubjects = [];
                            foreach ($allSubjects as $subject) {
                                if (empty($studentSubjectIds) || in_array($subject['subject_id'], $studentSubjectIds)) {
                                    $studentSubjects[] = $subject;
                                }
                            }

                            // Skip if student has no subjects
                            if (empty($studentSubjects)) continue;

                            $studentCount++;
                        ?>
                            <div class="attendance-card">
                                <div class="attendance-header">
                                    <div class="header-content">
                                        <div class="header-logo">
                                            <?php if ($hasLogo): ?>
                                                <img src="<?php echo $logoUrl; ?>" alt="প্রতিষ্ঠানের লোগো">
                                            <?php else: ?>
                                                <img src="<?php echo $defaultLogoPath; ?>" alt="ডিফল্ট লোগো">
                                            <?php endif; ?>
                                        </div>
                                        <div class="header-text">
                                            <h3><?php echo htmlspecialchars($_SESSION['institutionName'] ?? 'স্কুল ম্যানেজমেন্ট সিস্টেম'); ?></h3>
                                            <h4><?php echo htmlspecialchars($selectedExam['exam_name'] . ' - ' . $selectedExam['exam_type']); ?> হাজিরা পত্র</h4>
                                        </div>
                                        <div class="header-right">
                                            <!-- ডান দিকের কলাম, ভবিষ্যতে প্রয়োজন হলে ব্যবহার করা যাবে -->
                                        </div>
                                    </div>
                                </div>

                                <div class="student-info">
                                    <div class="student-info-row">
                                        <div class="student-info-label">শ্রেণী:</div>
                                        <div class="student-info-value"><?php echo htmlspecialchars($student['class_name'] ?? 'সকল শ্রেণী'); ?></div>
                                        <div class="student-info-label" style="margin-left: 15px;">সেশন:</div>
                                        <div class="student-info-value"><?php echo htmlspecialchars($student['session_name'] ?? 'N/A'); ?></div>
                                    </div>
                                    <div class="student-info-row">
                                        <div class="student-info-label">বিভাগ:</div>
                                        <div class="student-info-value"><?php echo htmlspecialchars($student['department_name'] ?? 'সকল বিভাগ'); ?></div>
                                        <div class="student-info-label" style="margin-left: 15px;">রোল:</div>
                                        <div class="student-info-value"><?php echo htmlspecialchars($student['roll_number'] ?? 'N/A'); ?></div>
                                    </div>
                                    <div class="student-info-row">
                                        <div class="student-info-label">নাম:</div>
                                        <div class="student-info-value" style="flex: 3;"><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></div>
                                    </div>
                                    <div class="student-info-row">
                                        <div class="student-info-label">আইডি:</div>
                                        <div class="student-info-value"><?php echo htmlspecialchars($student['student_id']); ?></div>
                                    </div>
                                </div>

                                <?php
                                // Show limited subjects to fit in half page
                                $maxSubjectsPerCard = 8; // Limit subjects to fit in half page
                                $displaySubjects = array_slice($studentSubjects, 0, $maxSubjectsPerCard);
                                ?>
                                    <table class="attendance-table" border="1" cellspacing="0" cellpadding="2" style="border-collapse: collapse; border: 1px solid black;">
                                        <thead>
                                            <tr style="border: 1px solid black;">
                                                <th width="8%" style="border: 1px solid black;">ক্রম</th>
                                                <th width="15%" style="border: 1px solid black;">তারিখ</th>
                                                <th width="25%" style="border: 1px solid black;">বিষয়</th>
                                                <th width="12%" style="border: 1px solid black;">সময়</th>
                                                <th width="10%" style="border: 1px solid black;">কক্ষ</th>
                                                <th width="15%" style="border: 1px solid black;">শিক্ষার্থীর স্বাক্ষর</th>
                                                <th width="15%" style="border: 1px solid black;">মন্তব্য</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            $counter = 1;

                                            foreach ($displaySubjects as $subject):
                                            ?>
                                                <tr style="border: 1px solid black;">
                                                    <td style="border: 1px solid black;"><?php echo $counter++; ?></td>
                                                    <td style="border: 1px solid black;">
                                                        <?php echo $subject['formatted_date']; ?>
                                                        <?php if (!empty($subject['day_name'])): ?>
                                                            <br><small>(<?php echo $subject['day_name']; ?>)</small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td class="text-start" style="border: 1px solid black;">
                                                        <span class="subject-name">
                                                            <?php echo htmlspecialchars($subject['subject_name']); ?>
                                                            <?php if (!empty($subject['subject_code'])): ?>
                                                                (<?php echo htmlspecialchars($subject['subject_code']); ?>)
                                                            <?php endif; ?>
                                                        </span>
                                                    </td>
                                                    <td style="border: 1px solid black;">
                                                        <?php if (!empty($subject['start_time']) && !empty($subject['end_time'])): ?>
                                                            <?php echo date('h:i A', strtotime($subject['start_time'])) . '<br>-<br>' . date('h:i A', strtotime($subject['end_time'])); ?>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td style="border: 1px solid black;">
                                                        <?php if (!empty($subject['room_no'])): ?>
                                                            <?php echo htmlspecialchars($subject['room_no']); ?>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td style="border: 1px solid black;"></td>
                                                    <td style="border: 1px solid black;"></td>
                                                </tr>
                                            <?php endforeach; ?>

                                            <?php
                                            // Add empty rows to fill the table if needed
                                            $emptyRows = max(0, 6 - count($displaySubjects)); // Minimum 6 rows for half page
                                            for ($i = 0; $i < $emptyRows; $i++):
                                            ?>
                                                <tr style="border: 1px solid black;">
                                                    <td style="border: 1px solid black;"><?php echo $counter++; ?></td>
                                                    <td style="border: 1px solid black;"></td>
                                                    <td style="border: 1px solid black;"></td>
                                                    <td style="border: 1px solid black;"></td>
                                                    <td style="border: 1px solid black;"></td>
                                                    <td style="border: 1px solid black;"></td>
                                                    <td style="border: 1px solid black;"></td>
                                                </tr>
                                            <?php endfor; ?>
                                        </tbody>
                                    </table>

                                    <!-- Signature area -->
                                    <div class="signature-area">
                                        <div class="signature-box">
                                            <div class="signature-line">শিক্ষার্থীর স্বাক্ষর</div>
                                        </div>
                                        <div class="signature-box">
                                            <div class="signature-line">পরীক্ষা নিয়ন্ত্রক</div>
                                        </div>
                                        <div class="signature-box">
                                            <div class="signature-line">প্রধান শিক্ষক</div>
                                        </div>
                                    </div>
                            </div>

                            <?php
                            // Add page break after every 2 students
                            if ($studentCount % 2 == 0):
                            ?>
                                <div class="page-break"></div>
                            <?php endif; ?>

                        <?php endwhile; ?>
                    </div>
                <?php elseif ($examId > 0): ?>
                    <div class="alert alert-warning text-center">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        নির্বাচিত ফিল্টার অনুযায়ী কোনো শিক্ষার্থী পাওয়া যায়নি।
                    </div>
                <?php else: ?>
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle me-2"></i>
                        হাজিরা পত্র দেখতে একটি পরীক্ষা নির্বাচন করুন।
                    </div>
                <?php endif; ?>

            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
