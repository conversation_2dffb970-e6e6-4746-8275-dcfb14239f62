<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Database Connection Test</h1>";

// Connection parameters from your dbh.inc.php
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "zfaw";

// Test 1: Try connecting without specifying a database
echo "<h2>Test 1: Basic Connection to MySQL Server</h2>";
try {
    $conn1 = new mysqli($servername, $username, $password);
    if ($conn1->connect_error) {
        echo "<p style='color:red'>Connection to MySQL server failed: " . $conn1->connect_error . "</p>";
    } else {
        echo "<p style='color:green'>Connection to MySQL server successful!</p>";
        $conn1->close();
    }
} catch (Exception $e) {
    echo "<p style='color:red'>Exception: " . $e->getMessage() . "</p>";
}

// Test 2: Try connecting with the database name
echo "<h2>Test 2: Connection to zfaw Database</h2>";
try {
    $conn2 = new mysqli($servername, $username, $password, $dbname);
    if ($conn2->connect_error) {
        echo "<p style='color:red'>Connection to zfaw database failed: " . $conn2->connect_error . "</p>";
    } else {
        echo "<p style='color:green'>Connection to zfaw database successful!</p>";
        
        // Check if users table exists
        $result = $conn2->query("SHOW TABLES LIKE 'users'");
        if ($result->num_rows > 0) {
            echo "<p style='color:green'>users table exists in zfaw database</p>";
        } else {
            echo "<p style='color:red'>users table does NOT exist in zfaw database</p>";
        }
        
        $conn2->close();
    }
} catch (Exception $e) {
    echo "<p style='color:red'>Exception: " . $e->getMessage() . "</p>";
}

// Test 3: Try connecting to 127.0.0.1 instead of localhost
echo "<h2>Test 3: Connection using 127.0.0.1 instead of localhost</h2>";
try {
    $conn3 = new mysqli("127.0.0.1", $username, $password, $dbname);
    if ($conn3->connect_error) {
        echo "<p style='color:red'>Connection to 127.0.0.1 failed: " . $conn3->connect_error . "</p>";
    } else {
        echo "<p style='color:green'>Connection to 127.0.0.1 successful!</p>";
        $conn3->close();
    }
} catch (Exception $e) {
    echo "<p style='color:red'>Exception: " . $e->getMessage() . "</p>";
}

// Test 4: Check MySQL socket and port
echo "<h2>Test 4: MySQL Configuration Info</h2>";
try {
    $conn4 = @new mysqli($servername, $username, $password);
    if (!$conn4->connect_error) {
        $result = $conn4->query("SHOW VARIABLES LIKE 'port'");
        if ($row = $result->fetch_assoc()) {
            echo "<p>MySQL port: " . $row['Value'] . "</p>";
        }
        
        $result = $conn4->query("SHOW VARIABLES LIKE 'socket'");
        if ($row = $result->fetch_assoc()) {
            echo "<p>MySQL socket: " . $row['Value'] . "</p>";
        }
        
        $result = $conn4->query("SHOW VARIABLES LIKE 'hostname'");
        if ($row = $result->fetch_assoc()) {
            echo "<p>MySQL hostname: " . $row['Value'] . "</p>";
        }
        
        $conn4->close();
    } else {
        echo "<p style='color:red'>Could not get MySQL configuration info</p>";
    }
} catch (Exception $e) {
    echo "<p style='color:red'>Exception: " . $e->getMessage() . "</p>";
}

// Test 5: List all available databases
echo "<h2>Test 5: Available Databases</h2>";
try {
    $conn5 = @new mysqli($servername, $username, $password);
    if (!$conn5->connect_error) {
        $result = $conn5->query("SHOW DATABASES");
        if ($result->num_rows > 0) {
            echo "<ul>";
            while ($row = $result->fetch_assoc()) {
                echo "<li>" . $row['Database'] . "</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>No databases found</p>";
        }
        $conn5->close();
    } else {
        echo "<p style='color:red'>Could not list databases</p>";
    }
} catch (Exception $e) {
    echo "<p style='color:red'>Exception: " . $e->getMessage() . "</p>";
}

echo "<h2>Recommendations</h2>";
echo "<p>If you're having connection issues:</p>";
echo "<ol>";
echo "<li>Make sure MySQL service is running in XAMPP Control Panel</li>";
echo "<li>Check if the database 'zfaw' exists</li>";
echo "<li>Try using '127.0.0.1' instead of 'localhost' in your connection string</li>";
echo "<li>Check if there's a firewall blocking the connection</li>";
echo "<li>Verify MySQL is listening on the default port (3306)</li>";
echo "</ol>";

echo "<p><a href='database/setup.php'>Run Database Setup</a> (if database tables don't exist)</p>";
?>
