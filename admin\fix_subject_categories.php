<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$successMessage = '';
$errorMessage = '';
$fixedCount = 0;

// Handle fix request
if (isset($_POST['fix_categories'])) {
    try {
        $conn->begin_transaction();
        
        // Get all student subjects that are currently 'optional'
        $query = "SELECT ss.id, ss.student_id, ss.subject_id, s.department_id as student_dept_id
                  FROM student_subjects ss
                  JOIN students s ON ss.student_id = s.id
                  WHERE ss.category = 'optional'";
        
        $result = $conn->query($query);
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $student_subject_id = $row['id'];
                $student_id = $row['student_id'];
                $subject_id = $row['subject_id'];
                $student_dept_id = $row['student_dept_id'];
                
                $correct_category = 'optional'; // default
                
                // Check department_subject_types table for correct category
                if ($student_dept_id) {
                    $dept_query = "SELECT subject_type FROM department_subject_types 
                                   WHERE department_id = ? AND subject_id = ?";
                    $dept_stmt = $conn->prepare($dept_query);
                    $dept_stmt->bind_param("ii", $student_dept_id, $subject_id);
                    $dept_stmt->execute();
                    $dept_result = $dept_stmt->get_result();
                    
                    if ($dept_result->num_rows > 0) {
                        $dept_data = $dept_result->fetch_assoc();
                        $correct_category = $dept_data['subject_type'];
                    }
                }
                
                // If still optional, check subjects table
                if ($correct_category === 'optional') {
                    $subject_query = "SELECT category FROM subjects WHERE id = ?";
                    $subject_stmt = $conn->prepare($subject_query);
                    $subject_stmt->bind_param("i", $subject_id);
                    $subject_stmt->execute();
                    $subject_result = $subject_stmt->get_result();
                    
                    if ($subject_result->num_rows > 0) {
                        $subject_data = $subject_result->fetch_assoc();
                        $correct_category = $subject_data['category'] ?? 'optional';
                    }
                }
                
                // Update if category is different
                if ($correct_category !== 'optional') {
                    $update_query = "UPDATE student_subjects SET category = ? WHERE id = ?";
                    $update_stmt = $conn->prepare($update_query);
                    $update_stmt->bind_param("si", $correct_category, $student_subject_id);
                    
                    if ($update_stmt->execute()) {
                        $fixedCount++;
                    }
                }
            }
        }
        
        $conn->commit();
        $successMessage = "$fixedCount টি বিষয়ের ক্যাটাগরি সঠিকভাবে আপডেট করা হয়েছে।";
        
    } catch (Exception $e) {
        $conn->rollback();
        $errorMessage = "ত্রুটি: " . $e->getMessage();
    }
}

// Get statistics
$stats_query = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN category = 'required' THEN 1 ELSE 0 END) as required_count,
                    SUM(CASE WHEN category = 'optional' THEN 1 ELSE 0 END) as optional_count,
                    SUM(CASE WHEN category = 'fourth' THEN 1 ELSE 0 END) as fourth_count
                FROM student_subjects";
$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বিষয় ক্যাটাগরি ঠিক করুন - অ্যাডমিন প্যানেল</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/admin-style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Include Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">🔧 বিষয় ক্যাটাগরি ঠিক করুন</h1>
                    <div>
                        <a href="dashboard.php" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-1"></i> ড্যাশবোর্ডে ফিরুন
                        </a>
                    </div>
                </div>

                <!-- Success/Error Messages -->
                <?php if ($successMessage): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i><?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($errorMessage): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Current Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary"><?php echo $stats['total']; ?></h5>
                                <p class="card-text">মোট বিষয় নির্বাচন</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success"><?php echo $stats['required_count']; ?></h5>
                                <p class="card-text">আবশ্যিক বিষয়</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-warning"><?php echo $stats['optional_count']; ?></h5>
                                <p class="card-text">ঐচ্ছিক বিষয়</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-info"><?php echo $stats['fourth_count']; ?></h5>
                                <p class="card-text">চতুর্থ বিষয়</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Fix Categories Form -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-tools me-2"></i>বিষয় ক্যাটাগরি ঠিক করুন</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>এই টুলটি কী করে:</strong>
                            <ul class="mb-0 mt-2">
                                <li>সব 'optional' হিসেবে চিহ্নিত বিষয়গুলো চেক করে</li>
                                <li>শিক্ষার্থীর বিভাগ অনুযায়ী সঠিক ক্যাটাগরি নির্ধারণ করে</li>
                                <li>department_subject_types টেবিল থেকে সঠিক তথ্য নেয়</li>
                                <li>প্রয়োজনে subjects টেবিল থেকে ফলব্যাক ক্যাটাগরি নেয়</li>
                            </ul>
                        </div>
                        
                        <form method="POST" onsubmit="return confirm('আপনি কি নিশ্চিত যে বিষয় ক্যাটাগরিগুলো ঠিক করতে চান?');">
                            <button type="submit" name="fix_categories" class="btn btn-warning">
                                <i class="fas fa-wrench me-2"></i>বিষয় ক্যাটাগরি ঠিক করুন
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-lightbulb me-2"></i>ভবিষ্যতে এই সমস্যা এড়ানোর উপায়</h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li><strong>department_subject_types টেবিল সেটআপ করুন:</strong> প্রতিটি বিভাগের জন্য বিষয়গুলোর সঠিক ধরন নির্ধারণ করুন</li>
                            <li><strong>subjects টেবিলে সঠিক category সেট করুন:</strong> প্রতিটি বিষয়ের জন্য সঠিক ক্যাটাগরি নির্ধারণ করুন</li>
                            <li><strong>CSV আপলোডের আগে যাচাই করুন:</strong> আপলোড করার আগে নিশ্চিত হন যে সব সেটআপ সঠিক</li>
                        </ol>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
