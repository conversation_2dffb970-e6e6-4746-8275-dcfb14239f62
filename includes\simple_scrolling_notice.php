<!-- Simple Scrolling Notice Section -->
<div style="background-color: #f5f5f5; padding: 0; border-top: 1px solid #dee2e6; border-bottom: 1px solid #dee2e6; box-shadow: 0 4px 6px rgba(0,0,0,0.1); overflow: hidden; margin-bottom: 0; height: 60px;">
    <div class="container" style="height: 100%; padding-top: 5px;">
        <marquee behavior="scroll" scrollamount="5" direction="left" onmouseover="this.stop();" onmouseout="this.start();" style="color: #006A4E; font-weight: 600; font-size: 22px; line-height: 50px; height: 50px;">
            <i class="fas fa-bullhorn me-2"></i> <strong>সর্বশেষ নোটিশ:</strong>
            <?php
            // Safe notice display with error handling
            try {
                // Include database connection file if not already included
                if (!function_exists('ensure_connection')) {
                    require_once 'dbh.inc.php';
                }

                // Ensure we have a valid connection
                $conn = ensure_connection();

                // Check if notices table exists
                $latest_notice_query = "SHOW TABLES LIKE 'notices'";
                $latest_notice_result = $conn->query($latest_notice_query);

                if ($latest_notice_result && $latest_notice_result->num_rows > 0) {
                    // Get latest notice with a timeout limit
                    $sql = "SELECT * FROM notices ORDER BY date DESC LIMIT 1";
                    $result = $conn->query($sql);

                    if ($result && $result->num_rows > 0) {
                        $row = $result->fetch_assoc();
                        echo htmlspecialchars($row['title']) . ' - ' . htmlspecialchars(substr($row['content'], 0, 150)) .
                             (strlen($row['content']) > 150 ? '...' : '');
                    } else {
                        echo "বর্তমানে কোন নোটিশ নেই।";
                    }
                } else {
                    echo "বর্তমানে কোন নোটিশ নেই।";
                }
            } catch (Exception $e) {
                // Silently handle the error and show a generic message
                echo "নোটিশ লোড করতে সমস্যা হচ্ছে। পরে আবার চেষ্টা করুন।";
                // Optionally log the error
                error_log('Notice Error: ' . $e->getMessage());
            }
            ?>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <i class="fas fa-calendar-alt me-2"></i> <strong>আজকের তারিখ:</strong> <?php echo date('d/m/Y'); ?>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <i class="fas fa-graduation-cap me-2"></i> <strong>ভর্তি চলছে:</strong> নতুন শিক্ষার্থীদের জন্য ভর্তি চলছে। আজই যোগাযোগ করুন।
        </marquee>
    </div>
</div>
