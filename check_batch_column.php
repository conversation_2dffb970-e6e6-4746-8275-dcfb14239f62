<?php
// Database Connection
require_once 'includes/dbh.inc.php';

// Query to check if batch column exists
$query = "SHOW COLUMNS FROM students LIKE 'batch'";
$result = $conn->query($query);

echo "Checking for 'batch' column in students table:\n\n";

if ($result && $result->num_rows > 0) {
    echo "SUCCESS: The 'batch' column exists in the students table.\n\n";
    
    // Show column details
    $column_info = $result->fetch_assoc();
    echo "Field Name: " . $column_info['Field'] . "\n";
    echo "Type: " . $column_info['Type'] . "\n";
    echo "Null: " . $column_info['Null'] . "\n";
    echo "Key: " . $column_info['Key'] . "\n";
    echo "Default: " . ($column_info['Default'] === NULL ? 'NULL' : $column_info['Default']) . "\n";
    echo "Extra: " . $column_info['Extra'] . "\n";
} else {
    echo "ERROR: The 'batch' column does not exist in the students table.\n";
    echo "Error message: " . $conn->error . "\n";
    
    // Let's check if the students table exists
    $table_check = $conn->query("SHOW TABLES LIKE 'students'");
    if ($table_check->num_rows > 0) {
        echo "The 'students' table exists.\n";
        
        // Show existing columns
        echo "\nExisting columns in the students table:\n";
        $columns_result = $conn->query("SHOW COLUMNS FROM students");
        while ($column = $columns_result->fetch_assoc()) {
            echo "- " . $column['Field'] . " (" . $column['Type'] . ")\n";
        }
    } else {
        echo "The 'students' table does not exist.\n";
    }
}

// Close connection
$conn->close();
?> 