<?php
session_start();
require_once '../includes/dbh.inc.php';
require_once '../includes/bkash_config.php';
require_once '../includes/bkash_functions.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Check if fee_id is provided
if (!isset($_GET['fee_id'])) {
    $_SESSION['error'] = 'ফি আইডি প্রদান করা হয়নি!';
    header('Location: fees.php');
    exit();
}

$feeId = intval($_GET['fee_id']);

// Get fee details
$feeQuery = "SELECT f.*, s.first_name, s.last_name, s.student_id as roll, c.class_name 
             FROM fees f
             JOIN students s ON f.student_id = s.id
             JOIN classes c ON s.class_id = c.id
             WHERE f.id = ?";
$stmt = $conn->prepare($feeQuery);
$stmt->bind_param('i', $feeId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    $_SESSION['error'] = 'ফি রেকর্ড খুঁজে পাওয়া যায়নি!';
    header('Location: fees.php');
    exit();
}

$fee = $result->fetch_assoc();
$dueAmount = $fee['amount'] - $fee['paid'];

// Include header
include_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Include sidebar -->
        <?php include_once 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">বিকাশ পেমেন্ট</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="fees.php" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> ফিরে যান
                    </a>
                </div>
            </div>
            
            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger">
                    <?= $_SESSION['error'] ?>
                    <?php unset($_SESSION['error']); ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success">
                    <?= $_SESSION['success'] ?>
                    <?php unset($_SESSION['success']); ?>
                </div>
            <?php endif; ?>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">শিক্ষার্থী এবং ফি তথ্য</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-bordered">
                                <tr>
                                    <th width="30%">শিক্ষার্থীর নাম</th>
                                    <td><?= $fee['first_name'] . ' ' . $fee['last_name'] ?></td>
                                </tr>
                                <tr>
                                    <th>রোল নম্বর</th>
                                    <td><?= $fee['roll'] ?></td>
                                </tr>
                                <tr>
                                    <th>শ্রেণী</th>
                                    <td><?= $fee['class_name'] ?></td>
                                </tr>
                                <tr>
                                    <th>ফি টাইপ</th>
                                    <td><?= $fee['fee_type'] ?></td>
                                </tr>
                                <tr>
                                    <th>মোট পরিমাণ</th>
                                    <td>৳ <?= number_format($fee['amount'], 2) ?></td>
                                </tr>
                                <tr>
                                    <th>পরিশোধিত</th>
                                    <td>৳ <?= number_format($fee['paid'], 2) ?></td>
                                </tr>
                                <tr>
                                    <th>বাকি</th>
                                    <td>৳ <?= number_format($dueAmount, 2) ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">বিকাশ পেমেন্ট</h5>
                        </div>
                        <div class="card-body">
                            <form id="bkashPaymentForm" method="POST" action="bkash_create_payment.php">
                                <input type="hidden" name="fee_id" value="<?= $feeId ?>">
                                <input type="hidden" name="student_name" value="<?= $fee['first_name'] . ' ' . $fee['last_name'] ?>">
                                <input type="hidden" name="fee_type" value="<?= $fee['fee_type'] ?>">
                                
                                <div class="mb-3">
                                    <label for="payment_amount" class="form-label">পেমেন্ট পরিমাণ</label>
                                    <input type="number" class="form-control" id="payment_amount" name="payment_amount" 
                                           min="1" max="<?= $dueAmount ?>" step="0.01" value="<?= $dueAmount ?>" required>
                                    <small class="text-muted">সর্বোচ্চ পরিমাণ: ৳ <?= number_format($dueAmount, 2) ?></small>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="payer_reference" class="form-label">পেয়ার রেফারেন্স (ঐচ্ছিক)</label>
                                    <input type="text" class="form-control" id="payer_reference" name="payer_reference" 
                                           placeholder="পেয়ার রেফারেন্স">
                                    <small class="text-muted">বিকাশ অ্যাকাউন্ট নম্বর বা অন্য কোন রেফারেন্স</small>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-money-bill-wave me-2"></i> বিকাশ দিয়ে পেমেন্ট করুন
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    document.getElementById('bkashPaymentForm').addEventListener('submit', function(e) {
        const amount = parseFloat(document.getElementById('payment_amount').value);
        const maxAmount = parseFloat(<?= $dueAmount ?>);
        
        if (amount <= 0) {
            e.preventDefault();
            alert('পেমেন্ট পরিমাণ শূন্যের চেয়ে বেশি হতে হবে!');
        } else if (amount > maxAmount) {
            e.preventDefault();
            alert('পেমেন্ট পরিমাণ বাকি পরিমাণের চেয়ে বেশি হতে পারবে না!');
        }
    });
</script>

<?php
// Include footer
include_once 'includes/footer.php';
?>
