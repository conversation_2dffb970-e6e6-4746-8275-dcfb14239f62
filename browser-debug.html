<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>নিশাত এডুকেশন সেন্টার - Browser Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .debug-panel {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin: 10px 0;
        }
        
        .status-good { color: green; font-weight: bold; }
        .status-bad { color: red; font-weight: bold; }
        .status-warn { color: orange; font-weight: bold; }
        
        .console {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .critical {
            background: #ffebee;
            border-left: 4px solid #f44336;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔍 Browser-Level Buffer Debug</h1>
    
    <div class="critical">
        <h3>🚨 Critical Test</h3>
        <p><strong>Title Bar Status:</strong> <span id="title-status">Checking...</span></p>
        <p><strong>Current Title:</strong> <span id="current-title">Loading...</span></p>
        <p><strong>Expected Title:</strong> নিশাত এডুকেশন সেন্টার - Browser Debug</p>
    </div>
    
    <div class="debug-panel">
        <h3>📊 Real-time Monitoring</h3>
        <div id="monitoring-area">
            <p>Starting monitoring...</p>
        </div>
    </div>
    
    <div class="debug-panel">
        <h3>🌐 Network Analysis</h3>
        <button onclick="testNetworkSpeed()">Test Network Speed</button>
        <button onclick="testResourceLoading()">Test Resource Loading</button>
        <button onclick="testAjaxRequests()">Test AJAX</button>
        <div id="network-results"></div>
    </div>
    
    <div class="debug-panel">
        <h3>🧠 Browser Information</h3>
        <div id="browser-info"></div>
    </div>
    
    <div class="debug-panel">
        <h3>📝 Debug Console</h3>
        <div class="console" id="debug-console"></div>
        <button onclick="clearConsole()">Clear Console</button>
        <button onclick="exportLogs()">Export Logs</button>
    </div>
    
    <div class="debug-panel">
        <h3>🔧 Manual Tests</h3>
        <button onclick="testTitleChange()">Test Title Change</button>
        <button onclick="testPageReload()">Test Page Reload</button>
        <button onclick="testNewWindow()">Open New Window</button>
        <button onclick="testIncognito()">Test Incognito (Manual)</button>
    </div>

    <script>
        let logCount = 0;
        let titleChangeCount = 0;
        let isBuffering = false;
        
        // Logging function
        function log(message, type = 'info') {
            const console = document.getElementById('debug-console');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            
            let color = '#00ff00';
            if (type === 'error') color = '#ff0000';
            if (type === 'warn') color = '#ffff00';
            if (type === 'critical') color = '#ff00ff';
            
            entry.style.color = color;
            entry.innerHTML = `[${timestamp}] ${message}`;
            console.appendChild(entry);
            console.scrollTop = console.scrollHeight;
            
            logCount++;
            
            // Also log to browser console
            console.log(`[ZFAW Debug] ${message}`);
        }
        
        // Title monitoring
        function monitorTitle() {
            const expectedTitle = 'নিশাত এডুকেশন সেন্টার - Browser Debug';
            const currentTitle = document.title;
            
            document.getElementById('current-title').textContent = currentTitle;
            
            if (currentTitle === expectedTitle) {
                document.getElementById('title-status').innerHTML = '<span class="status-good">✅ CORRECT</span>';
                if (isBuffering) {
                    log('Title buffering STOPPED', 'info');
                    isBuffering = false;
                }
            } else {
                document.getElementById('title-status').innerHTML = '<span class="status-bad">❌ BUFFERING/WRONG</span>';
                if (!isBuffering) {
                    log('Title buffering DETECTED!', 'critical');
                    isBuffering = true;
                }
            }
            
            titleChangeCount++;
        }
        
        // Browser info
        function updateBrowserInfo() {
            const info = document.getElementById('browser-info');
            info.innerHTML = `
                <table border="1" cellpadding="5" style="border-collapse: collapse; width: 100%;">
                    <tr><th>Property</th><th>Value</th></tr>
                    <tr><td>User Agent</td><td>${navigator.userAgent}</td></tr>
                    <tr><td>Browser</td><td>${navigator.appName}</td></tr>
                    <tr><td>Version</td><td>${navigator.appVersion}</td></tr>
                    <tr><td>Platform</td><td>${navigator.platform}</td></tr>
                    <tr><td>Language</td><td>${navigator.language}</td></tr>
                    <tr><td>Online</td><td>${navigator.onLine ? 'Yes' : 'No'}</td></tr>
                    <tr><td>Cookies Enabled</td><td>${navigator.cookieEnabled ? 'Yes' : 'No'}</td></tr>
                    <tr><td>Screen Resolution</td><td>${screen.width}x${screen.height}</td></tr>
                    <tr><td>Window Size</td><td>${window.innerWidth}x${window.innerHeight}</td></tr>
                    <tr><td>Connection</td><td>${navigator.connection ? navigator.connection.effectiveType : 'Unknown'}</td></tr>
                </table>
            `;
        }
        
        // Network tests
        function testNetworkSpeed() {
            log('Testing network speed...', 'info');
            const startTime = performance.now();
            
            fetch('ultra-minimal.html?' + Math.random())
                .then(response => {
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    log(`Network speed test: ${duration.toFixed(2)}ms`, 'info');
                    
                    document.getElementById('network-results').innerHTML += 
                        `<p>✅ Network Speed: ${duration.toFixed(2)}ms</p>`;
                })
                .catch(error => {
                    log(`Network speed test failed: ${error}`, 'error');
                    document.getElementById('network-results').innerHTML += 
                        `<p>❌ Network Error: ${error}</p>`;
                });
        }
        
        function testResourceLoading() {
            log('Testing resource loading...', 'info');
            
            // Test image loading
            const img = new Image();
            img.onload = () => log('Image loading: SUCCESS', 'info');
            img.onerror = () => log('Image loading: FAILED', 'error');
            img.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
            
            // Test CSS loading
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'data:text/css,body{}';
            link.onload = () => log('CSS loading: SUCCESS', 'info');
            link.onerror = () => log('CSS loading: FAILED', 'error');
            document.head.appendChild(link);
        }
        
        function testAjaxRequests() {
            log('Testing AJAX requests...', 'info');
            
            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'ultra-minimal.html?' + Math.random());
            xhr.onreadystatechange = function() {
                log(`AJAX State: ${xhr.readyState}, Status: ${xhr.status}`, 'info');
            };
            xhr.onload = () => log('AJAX request: SUCCESS', 'info');
            xhr.onerror = () => log('AJAX request: FAILED', 'error');
            xhr.send();
        }
        
        // Manual tests
        function testTitleChange() {
            log('Testing title change...', 'info');
            const originalTitle = document.title;
            document.title = 'TEST TITLE - ' + Math.random();
            
            setTimeout(() => {
                document.title = originalTitle;
                log('Title change test completed', 'info');
            }, 2000);
        }
        
        function testPageReload() {
            if (confirm('This will reload the page. Continue?')) {
                log('Reloading page...', 'info');
                location.reload();
            }
        }
        
        function testNewWindow() {
            log('Opening new window...', 'info');
            window.open('ultra-minimal.html', '_blank', 'width=600,height=400');
        }
        
        function testIncognito() {
            alert('Please manually open this page in an incognito/private window and compare the behavior.');
        }
        
        function clearConsole() {
            document.getElementById('debug-console').innerHTML = '';
            logCount = 0;
        }
        
        function exportLogs() {
            const logs = document.getElementById('debug-console').innerText;
            const blob = new Blob([logs], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'zfaw-debug-logs.txt';
            a.click();
            URL.revokeObjectURL(url);
        }
        
        // Real-time monitoring
        function updateMonitoring() {
            const area = document.getElementById('monitoring-area');
            area.innerHTML = `
                <p><strong>Monitoring Time:</strong> ${new Date().toLocaleString()}</p>
                <p><strong>Title Checks:</strong> ${titleChangeCount}</p>
                <p><strong>Log Entries:</strong> ${logCount}</p>
                <p><strong>Page Load Time:</strong> ${(performance.now() / 1000).toFixed(2)} seconds</p>
                <p><strong>Memory Usage:</strong> ${(performance.memory ? (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2) + ' MB' : 'Unknown')}</p>
                <p><strong>Buffering Status:</strong> ${isBuffering ? '❌ BUFFERING' : '✅ NORMAL'}</p>
            `;
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('Browser debug tool initialized', 'info');
            updateBrowserInfo();
            
            // Start monitoring
            setInterval(monitorTitle, 500);
            setInterval(updateMonitoring, 1000);
            
            // Initial checks
            monitorTitle();
            updateMonitoring();
            
            log('All monitoring systems active', 'info');
        });
        
        // Detect page visibility changes
        document.addEventListener('visibilitychange', function() {
            log(`Page visibility changed: ${document.hidden ? 'hidden' : 'visible'}`, 'info');
        });
        
        // Detect focus changes
        window.addEventListener('focus', () => log('Window gained focus', 'info'));
        window.addEventListener('blur', () => log('Window lost focus', 'info'));
        
        // Detect errors
        window.addEventListener('error', function(e) {
            log(`JavaScript Error: ${e.message} at ${e.filename}:${e.lineno}`, 'error');
        });
        
        // Performance monitoring
        window.addEventListener('load', function() {
            log(`Page fully loaded in ${(performance.now() / 1000).toFixed(2)} seconds`, 'info');
        });
        
    </script>
</body>
</html>
