<?php
// Check table structure
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database connection
$servername = "127.0.0.1";
$username = "root";
$password = "";
$dbname = "zfaw";

$conn = new mysqli($servername, $username, $password, $dbname);

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Table Structure Check</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>

<h1>🔍 Database Table Structure Check</h1>

<?php
$tables = ['students', 'sessions', 'classes', 'fees'];

foreach ($tables as $table) {
    echo "<h2>📋 Table: $table</h2>";
    
    // Check if table exists
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result && $result->num_rows > 0) {
        echo "<p class='success'>✅ Table exists</p>";
        
        // Show table structure
        $structure = $conn->query("DESCRIBE $table");
        if ($structure) {
            echo "<table>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
            while ($row = $structure->fetch_assoc()) {
                echo "<tr>";
                echo "<td><strong>" . $row['Field'] . "</strong></td>";
                echo "<td>" . $row['Type'] . "</td>";
                echo "<td>" . $row['Null'] . "</td>";
                echo "<td>" . $row['Key'] . "</td>";
                echo "<td>" . ($row['Default'] ? $row['Default'] : 'NULL') . "</td>";
                echo "<td>" . $row['Extra'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Show sample data
            $sampleData = $conn->query("SELECT * FROM $table LIMIT 3");
            if ($sampleData && $sampleData->num_rows > 0) {
                echo "<h3>📊 Sample Data (First 3 rows)</h3>";
                echo "<table>";
                
                // Get column names
                $fields = $sampleData->fetch_fields();
                echo "<tr>";
                foreach ($fields as $field) {
                    echo "<th>" . $field->name . "</th>";
                }
                echo "</tr>";
                
                // Reset pointer and show data
                $sampleData->data_seek(0);
                while ($row = $sampleData->fetch_assoc()) {
                    echo "<tr>";
                    foreach ($row as $value) {
                        echo "<td>" . ($value ? htmlspecialchars($value) : 'NULL') . "</td>";
                    }
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p class='warning'>⚠️ No data in table</p>";
            }
        }
    } else {
        echo "<p class='error'>❌ Table does not exist</p>";
    }
    
    echo "<hr>";
}

// Check for common column name issues
echo "<h2>🔧 Column Name Analysis</h2>";

$studentColumns = $conn->query("SHOW COLUMNS FROM students");
if ($studentColumns) {
    echo "<h3>Students Table Columns:</h3>";
    echo "<ul>";
    $hasName = false;
    $hasFirstName = false;
    $hasLastName = false;
    $hasRollNumber = false;
    $hasStudentId = false;
    
    while ($col = $studentColumns->fetch_assoc()) {
        $colName = $col['Field'];
        echo "<li><strong>$colName</strong> (" . $col['Type'] . ")</li>";
        
        if ($colName === 'name') $hasName = true;
        if ($colName === 'first_name') $hasFirstName = true;
        if ($colName === 'last_name') $hasLastName = true;
        if ($colName === 'roll_number') $hasRollNumber = true;
        if ($colName === 'student_id') $hasStudentId = true;
    }
    echo "</ul>";
    
    echo "<h3>🔍 Analysis:</h3>";
    echo "<ul>";
    if ($hasName) {
        echo "<li class='success'>✅ Has 'name' column</li>";
    } else if ($hasFirstName && $hasLastName) {
        echo "<li class='success'>✅ Has 'first_name' and 'last_name' columns</li>";
    } else {
        echo "<li class='error'>❌ No name columns found</li>";
    }
    
    if ($hasRollNumber) {
        echo "<li class='success'>✅ Has 'roll_number' column</li>";
    } else if ($hasStudentId) {
        echo "<li class='warning'>⚠️ Has 'student_id' column (can be used as roll)</li>";
    } else {
        echo "<li class='error'>❌ No roll/student_id column found</li>";
    }
    echo "</ul>";
}

// Suggest correct queries
echo "<h2>💡 Suggested Queries for Your Database</h2>";

echo "<h3>For Students:</h3>";
if ($hasFirstName && $hasLastName) {
    echo "<code>SELECT *, CONCAT(first_name, ' ', last_name) as full_name FROM students ORDER BY first_name, last_name</code>";
} else if ($hasName) {
    echo "<code>SELECT * FROM students ORDER BY name</code>";
} else {
    echo "<p class='error'>Cannot determine correct student query</p>";
}

echo "<h3>For Student Display:</h3>";
if ($hasFirstName && $hasLastName) {
    if ($hasRollNumber) {
        echo "<code>\$studentName = \$student['first_name'] . ' ' . \$student['last_name'];<br>";
        echo "\$rollNumber = \$student['roll_number'];</code>";
    } else if ($hasStudentId) {
        echo "<code>\$studentName = \$student['first_name'] . ' ' . \$student['last_name'];<br>";
        echo "\$rollNumber = \$student['student_id'];</code>";
    }
}

?>

<h2>🔗 Navigation</h2>
<p>
    <a href="minimal-fee-test.php?bypass=test">Minimal Fee Test</a> | 
    <a href="super-simple-test.php">Super Simple Test</a> | 
    <a href="fee_management.php">Fee Management</a>
</p>

</body>
</html>
