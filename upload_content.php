<?php
// Initialize variables
$message = '';
$error = '';

// Load current values from index.php
$index_content = file_get_contents('index.php');

// Extract current values using regular expressions
preg_match('/\$school_name = "([^"]*)";/', $index_content, $school_name_match);
preg_match('/\$school_address = "([^"]*)";/', $index_content, $school_address_match);
preg_match('/\$principal_name = "([^"]*)";/', $index_content, $principal_name_match);
preg_match('/\$principal_title = "([^"]*)";/', $index_content, $principal_title_match);
preg_match('/\$principal_message = "([^"]*)";/', $index_content, $principal_message_match);
preg_match('/\$chairman_name = "([^"]*)";/', $index_content, $chairman_name_match);
preg_match('/\$chairman_title = "([^"]*)";/', $index_content, $chairman_title_match);
preg_match('/\$chairman_message = "([^"]*)";/', $index_content, $chairman_message_match);

// Set current values or defaults
$current_school_name = isset($school_name_match[1]) ? $school_name_match[1] : "নিশাত এডুকেশন সেন্টার";
$current_school_address = isset($school_address_match[1]) ? $school_address_match[1] : "ঢাকা, বাংলাদেশ";
$current_principal_name = isset($principal_name_match[1]) ? $principal_name_match[1] : "প্রফেসর মোঃ আবদুল করিম";
$current_principal_title = isset($principal_title_match[1]) ? $principal_title_match[1] : "অধ্যক্ষ";
$current_principal_message = isset($principal_message_match[1]) ? $principal_message_match[1] : "শিক্ষা হল জাতির মেরুদণ্ড। আমাদের শিক্ষা প্রতিষ্ঠানে আমরা শিক্ষার্থীদের শুধু বইয়ের জ্ঞান নয়, জীবনের জন্য প্রয়োজনীয় সকল দক্ষতা শেখাতে প্রতিশ্রুতিবদ্ধ। আমাদের লক্ষ্য হল প্রতিটি শিক্ষার্থীকে দেশ ও জাতির জন্য একজন যোগ্য নাগরিক হিসেবে গড়ে তোলা।";
$current_chairman_name = isset($chairman_name_match[1]) ? $chairman_name_match[1] : "জনাব মোঃ রফিকুল ইসলাম";
$current_chairman_title = isset($chairman_title_match[1]) ? $chairman_title_match[1] : "সভাপতি";
$current_chairman_message = isset($chairman_message_match[1]) ? $chairman_message_match[1] : "আমাদের শিক্ষা প্রতিষ্ঠান সর্বদা উন্নত মানের শিক্ষা প্রদানে নিবেদিত। আমরা বিশ্বাস করি, শিক্ষার্থীদের সামগ্রিক বিকাশের মাধ্যমেই একটি সমৃদ্ধ জাতি গঠন সম্ভব। আমাদের প্রতিষ্ঠানে আধুনিক শিক্ষা পদ্ধতি ও প্রযুক্তির সমন্বয়ে শিক্ষার্থীদের ভবিষ্যৎ চ্যালেঞ্জ মোকাবেলার জন্য প্রস্তুত করা হয়।";

// Check if form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {

    // Create img directory if it doesn't exist
    if (!file_exists('img')) {
        mkdir('img', 0777, true);
    }

    // Process school information
    $school_name = $_POST['school_name'] ?? $current_school_name;
    $school_address = $_POST['school_address'] ?? $current_school_address;

    // Process logo upload
    if (isset($_FILES['school_logo']) && $_FILES['school_logo']['error'] == 0) {
        $allowed = ['jpg', 'jpeg', 'png', 'gif'];
        $filename = $_FILES['school_logo']['name'];
        $filetype = pathinfo($filename, PATHINFO_EXTENSION);

        if (in_array(strtolower($filetype), $allowed)) {
            $new_filename = 'logo.' . $filetype;
            if (move_uploaded_file($_FILES['school_logo']['tmp_name'], 'img/' . $new_filename)) {
                $message .= "প্রতিষ্ঠানের লোগো সফলভাবে আপলোড হয়েছে।<br>";

                // Update index.php with new logo path
                $logo_path = "img/" . $new_filename;
            } else {
                $error .= "লোগো আপলোড করতে সমস্যা হয়েছে।<br>";
            }
        } else {
            $error .= "লোগোর জন্য শুধুমাত্র JPG, JPEG, PNG এবং GIF ফাইল গ্রহণযোগ্য।<br>";
        }
    }

    // Process principal photo upload
    if (isset($_FILES['principal_photo']) && $_FILES['principal_photo']['error'] == 0) {
        $allowed = ['jpg', 'jpeg', 'png', 'gif'];
        $filename = $_FILES['principal_photo']['name'];
        $filetype = pathinfo($filename, PATHINFO_EXTENSION);

        if (in_array(strtolower($filetype), $allowed)) {
            $new_filename = 'principal.' . $filetype;
            if (move_uploaded_file($_FILES['principal_photo']['tmp_name'], 'img/' . $new_filename)) {
                $message .= "অধ্যক্ষের ছবি সফলভাবে আপলোড হয়েছে।<br>";

                // Update index.php with new principal photo path
                $principal_photo_path = "img/" . $new_filename;
            } else {
                $error .= "অধ্যক্ষের ছবি আপলোড করতে সমস্যা হয়েছে।<br>";
            }
        } else {
            $error .= "অধ্যক্ষের ছবির জন্য শুধুমাত্র JPG, JPEG, PNG এবং GIF ফাইল গ্রহণযোগ্য।<br>";
        }
    }

    // Process principal information
    $principal_name = $_POST['principal_name'] ?? $current_principal_name;
    $principal_title = $_POST['principal_title'] ?? $current_principal_title;
    $principal_message = $_POST['principal_message'] ?? $current_principal_message;

    // Process chairman photo upload
    if (isset($_FILES['chairman_photo']) && $_FILES['chairman_photo']['error'] == 0) {
        $allowed = ['jpg', 'jpeg', 'png', 'gif'];
        $filename = $_FILES['chairman_photo']['name'];
        $filetype = pathinfo($filename, PATHINFO_EXTENSION);

        if (in_array(strtolower($filetype), $allowed)) {
            $new_filename = 'chairman.' . $filetype;
            if (move_uploaded_file($_FILES['chairman_photo']['tmp_name'], 'img/' . $new_filename)) {
                $message .= "সভাপতির ছবি সফলভাবে আপলোড হয়েছে।<br>";

                // Update index.php with new chairman photo path
                $chairman_photo_path = "img/" . $new_filename;
            } else {
                $error .= "সভাপতির ছবি আপলোড করতে সমস্যা হয়েছে।<br>";
            }
        } else {
            $error .= "সভাপতির ছবির জন্য শুধুমাত্র JPG, JPEG, PNG এবং GIF ফাইল গ্রহণযোগ্য।<br>";
        }
    }

    // Process chairman information
    $chairman_name = $_POST['chairman_name'] ?? $current_chairman_name;
    $chairman_title = $_POST['chairman_title'] ?? $current_chairman_title;
    $chairman_message = $_POST['chairman_message'] ?? $current_chairman_message;

    // Process hero image upload
    if (isset($_FILES['hero_image']) && $_FILES['hero_image']['error'] == 0) {
        $allowed = ['jpg', 'jpeg', 'png', 'gif'];
        $filename = $_FILES['hero_image']['name'];
        $filetype = pathinfo($filename, PATHINFO_EXTENSION);

        if (in_array(strtolower($filetype), $allowed)) {
            $new_filename = 'hero-image.' . $filetype;
            if (move_uploaded_file($_FILES['hero_image']['tmp_name'], 'img/' . $new_filename)) {
                $message .= "হিরো সেকশনের ছবি সফলভাবে আপলোড হয়েছে।<br>";
            } else {
                $error .= "হিরো সেকশনের ছবি আপলোড করতে সমস্যা হয়েছে।<br>";
            }
        } else {
            $error .= "হিরো সেকশনের ছবির জন্য শুধুমাত্র JPG, JPEG, PNG এবং GIF ফাইল গ্রহণযোগ্য।<br>";
        }
    }

    // Process about image upload
    if (isset($_FILES['about_image']) && $_FILES['about_image']['error'] == 0) {
        $allowed = ['jpg', 'jpeg', 'png', 'gif'];
        $filename = $_FILES['about_image']['name'];
        $filetype = pathinfo($filename, PATHINFO_EXTENSION);

        if (in_array(strtolower($filetype), $allowed)) {
            $new_filename = 'about-image.' . $filetype;
            if (move_uploaded_file($_FILES['about_image']['tmp_name'], 'img/' . $new_filename)) {
                $message .= "আমাদের সম্পর্কে সেকশনের ছবি সফলভাবে আপলোড হয়েছে।<br>";
            } else {
                $error .= "আমাদের সম্পর্কে সেকশনের ছবি আপলোড করতে সমস্যা হয়েছে।<br>";
            }
        } else {
            $error .= "আমাদের সম্পর্কে সেকশনের ছবির জন্য শুধুমাত্র JPG, JPEG, PNG এবং GIF ফাইল গ্রহণযোগ্য।<br>";
        }
    }

    // Update index.php with new content
    // Always update text fields, even if no images are uploaded
    $index_file = file_get_contents('index.php');

    // Update school information
    $index_file = preg_replace('/\$school_name = "[^"]*";/', '$school_name = "' . addslashes($school_name) . '";', $index_file);
    $index_file = preg_replace('/\$school_address = "[^"]*";/', '$school_address = "' . addslashes($school_address) . '";', $index_file);

    // Update principal information
    $index_file = preg_replace('/\$principal_name = "[^"]*";/', '$principal_name = "' . addslashes($principal_name) . '";', $index_file);
    $index_file = preg_replace('/\$principal_title = "[^"]*";/', '$principal_title = "' . addslashes($principal_title) . '";', $index_file);
    $index_file = preg_replace('/\$principal_message = "[^"]*";/', '$principal_message = "' . addslashes($principal_message) . '";', $index_file);

    // Update chairman information
    $index_file = preg_replace('/\$chairman_name = "[^"]*";/', '$chairman_name = "' . addslashes($chairman_name) . '";', $index_file);
    $index_file = preg_replace('/\$chairman_title = "[^"]*";/', '$chairman_title = "' . addslashes($chairman_title) . '";', $index_file);
    $index_file = preg_replace('/\$chairman_message = "[^"]*";/', '$chairman_message = "' . addslashes($chairman_message) . '";', $index_file);

    // Update image paths if uploaded
    if (isset($logo_path)) {
        $index_file = preg_replace('/\$school_logo = "[^"]*";/', '$school_logo = "' . $logo_path . '";', $index_file);
    }
    if (isset($principal_photo_path)) {
        $index_file = preg_replace('/\$principal_photo = "[^"]*";/', '$principal_photo = "' . $principal_photo_path . '";', $index_file);
    }
    if (isset($chairman_photo_path)) {
        $index_file = preg_replace('/\$chairman_photo = "[^"]*";/', '$chairman_photo = "' . $chairman_photo_path . '";', $index_file);
    }

    // Save the updated index.php file
    file_put_contents('index.php', $index_file);

    // Add success message
    if (empty($message)) {
        $message = "প্রতিষ্ঠানের তথ্য সফলভাবে আপডেট করা হয়েছে।<br>";
    } else {
        $message .= "সকল তথ্য সফলভাবে আপডেট করা হয়েছে।<br>";
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>প্রতিষ্ঠানের তথ্য আপলোড</title>

    <!-- Bootstrap CSS -->
    

    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: #f8f9fa;
        }

        .form-container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-top: 30px;
            margin-bottom: 30px;
        }

        .section-title {
            border-left: 4px solid #006A4E;
            padding-left: 10px;
            margin-bottom: 20px;
            color: #006A4E;
        }

        .form-label {
            font-weight: 500;
        }

        .preview-image {
            max-width: 150px;
            max-height: 150px;
            border-radius: 5px;
            margin-top: 10px;
            border: 1px solid #ddd;
        }

        .btn-primary {
            background-color: #006A4E;
            border-color: #006A4E;
        }

        .btn-primary:hover {
            background-color: #00563B;
            border-color: #00563B;
        }

        .alert {
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="form-container">
                    <h2 class="text-center mb-4">প্রতিষ্ঠানের তথ্য আপলোড করুন</h2>

                    <?php if (!empty($message)): ?>
                        <div class="alert alert-success">
                            <?php echo $message; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger">
                            <?php echo $error; ?>
                        </div>
                    <?php endif; ?>

                    <form action="" method="POST" enctype="multipart/form-data">
                        <!-- School Information Section -->
                        <div class="mb-4">
                            <h4 class="section-title">প্রতিষ্ঠানের তথ্য</h4>

                            <div class="mb-3">
                                <label for="school_name" class="form-label">প্রতিষ্ঠানের নাম</label>
                                <input type="text" class="form-control" id="school_name" name="school_name" value="<?php echo htmlspecialchars($current_school_name); ?>">
                            </div>

                            <div class="mb-3">
                                <label for="school_address" class="form-label">প্রতিষ্ঠানের ঠিকানা</label>
                                <input type="text" class="form-control" id="school_address" name="school_address" value="<?php echo htmlspecialchars($current_school_address); ?>">
                            </div>

                            <div class="mb-3">
                                <label for="school_logo" class="form-label">প্রতিষ্ঠানের লোগো (80x80 পিক্সেল সাইজ সুপারিশ করা হয়)</label>
                                <input type="file" class="form-control" id="school_logo" name="school_logo">
                                <div class="mt-2">
                                    <img id="logo_preview" class="preview-image" src="#" alt="লোগো প্রিভিউ" style="display: none;">
                                </div>
                            </div>
                        </div>

                        <!-- Principal Information Section -->
                        <div class="mb-4">
                            <h4 class="section-title">অধ্যক্ষের তথ্য</h4>

                            <div class="mb-3">
                                <label for="principal_name" class="form-label">অধ্যক্ষের নাম</label>
                                <input type="text" class="form-control" id="principal_name" name="principal_name" value="<?php echo htmlspecialchars($current_principal_name); ?>">
                            </div>

                            <div class="mb-3">
                                <label for="principal_title" class="form-label">অধ্যক্ষের পদবি</label>
                                <input type="text" class="form-control" id="principal_title" name="principal_title" value="<?php echo htmlspecialchars($current_principal_title); ?>">
                            </div>

                            <div class="mb-3">
                                <label for="principal_message" class="form-label">অধ্যক্ষের বাণী</label>
                                <textarea class="form-control" id="principal_message" name="principal_message" rows="4"><?php echo htmlspecialchars($current_principal_message); ?></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="principal_photo" class="form-label">অধ্যক্ষের ছবি (150x150 পিক্সেল সাইজ সুপারিশ করা হয়)</label>
                                <input type="file" class="form-control" id="principal_photo" name="principal_photo">
                                <div class="mt-2">
                                    <img id="principal_preview" class="preview-image" src="#" alt="অধ্যক্ষের ছবি প্রিভিউ" style="display: none;">
                                </div>
                            </div>
                        </div>

                        <!-- Chairman Information Section -->
                        <div class="mb-4">
                            <h4 class="section-title">সভাপতির তথ্য</h4>

                            <div class="mb-3">
                                <label for="chairman_name" class="form-label">সভাপতির নাম</label>
                                <input type="text" class="form-control" id="chairman_name" name="chairman_name" value="<?php echo htmlspecialchars($current_chairman_name); ?>">
                            </div>

                            <div class="mb-3">
                                <label for="chairman_title" class="form-label">সভাপতির পদবি</label>
                                <input type="text" class="form-control" id="chairman_title" name="chairman_title" value="<?php echo htmlspecialchars($current_chairman_title); ?>">
                            </div>

                            <div class="mb-3">
                                <label for="chairman_message" class="form-label">সভাপতির বাণী</label>
                                <textarea class="form-control" id="chairman_message" name="chairman_message" rows="4"><?php echo htmlspecialchars($current_chairman_message); ?></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="chairman_photo" class="form-label">সভাপতির ছবি (150x150 পিক্সেল সাইজ সুপারিশ করা হয়)</label>
                                <input type="file" class="form-control" id="chairman_photo" name="chairman_photo">
                                <div class="mt-2">
                                    <img id="chairman_preview" class="preview-image" src="#" alt="সভাপতির ছবি প্রিভিউ" style="display: none;">
                                </div>
                            </div>
                        </div>

                        <!-- Additional Images Section -->
                        <div class="mb-4">
                            <h4 class="section-title">অতিরিক্ত ছবিসমূহ</h4>

                            <div class="mb-3">
                                <label for="hero_image" class="form-label">হিরো সেকশনের ছবি (600x400 পিক্সেল সাইজ সুপারিশ করা হয়)</label>
                                <input type="file" class="form-control" id="hero_image" name="hero_image">
                                <div class="mt-2">
                                    <img id="hero_preview" class="preview-image" src="#" alt="হিরো ছবি প্রিভিউ" style="display: none;">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="about_image" class="form-label">আমাদের সম্পর্কে সেকশনের ছবি (600x400 পিক্সেল সাইজ সুপারিশ করা হয়)</label>
                                <input type="file" class="form-control" id="about_image" name="about_image">
                                <div class="mt-2">
                                    <img id="about_preview" class="preview-image" src="#" alt="আমাদের সম্পর্কে ছবি প্রিভিউ" style="display: none;">
                                </div>
                            </div>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg px-5">সংরক্ষণ করুন</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Image Preview Script -->
    <script>
        // Function to show image preview
        function showImagePreview(input, previewId) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();

                reader.onload = function(e) {
                    document.getElementById(previewId).src = e.target.result;
                    document.getElementById(previewId).style.display = 'block';
                }

                reader.readAsDataURL(input.files[0]);
            }
        }

        // Set up event listeners for all file inputs
        document.getElementById('school_logo').addEventListener('change', function() {
            showImagePreview(this, 'logo_preview');
        });

        document.getElementById('principal_photo').addEventListener('change', function() {
            showImagePreview(this, 'principal_preview');
        });

        document.getElementById('chairman_photo').addEventListener('change', function() {
            showImagePreview(this, 'chairman_preview');
        });

        document.getElementById('hero_image').addEventListener('change', function() {
            showImagePreview(this, 'hero_preview');
        });

        document.getElementById('about_image').addEventListener('change', function() {
            showImagePreview(this, 'about_preview');
        });
    </script>
</body>
</html>
