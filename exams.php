<?php
session_start();
require_once 'includes/dbh.inc.php';

// Get all active exams with class information
$examsQuery = "SELECT e.*, c.class_name
              FROM exams e
              LEFT JOIN classes c ON e.class_id = c.id
              WHERE e.status = 'active' OR e.status IS NULL OR e.status = ''
              ORDER BY e.exam_date ASC";
$exams = $conn->query($examsQuery);

// Get exam types for filter
$examTypesQuery = "SELECT DISTINCT exam_type FROM exams WHERE exam_type IS NOT NULL AND exam_type != ''";
$examTypes = $conn->query($examTypesQuery);

// Get classes for filter (prevent duplicates)
$classesQuery = "SELECT DISTINCT id, class_name FROM classes ORDER BY class_name";
$classes = $conn->query($classesQuery);

// Handle filters
$filterApplied = false;
$filteredExams = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['filter_exams'])) {
    $filterApplied = true;
    $filterClass = $_POST['class_id'] ?? '';
    $filterType = $_POST['exam_type'] ?? '';
    $filterDate = $_POST['exam_date'] ?? '';

    $filteredExamsQuery = "SELECT e.*, c.class_name
                          FROM exams e
                          LEFT JOIN classes c ON e.class_id = c.id
                          WHERE (e.status = 'active' OR e.status IS NULL OR e.status = '')";

    if (!empty($filterClass)) {
        $filteredExamsQuery .= " AND e.class_id = '$filterClass'";
    }

    if (!empty($filterType)) {
        $filteredExamsQuery .= " AND e.exam_type = '$filterType'";
    }

    if (!empty($filterDate)) {
        $filteredExamsQuery .= " AND DATE(e.exam_date) = '$filterDate'";
    }

    $filteredExamsQuery .= " ORDER BY e.exam_date ASC";
    $filteredExams = $conn->query($filteredExamsQuery);
}

// Function to get days remaining
function getDaysRemaining($examDate) {
    $today = new DateTime();
    $examDay = new DateTime($examDate);
    $interval = $today->diff($examDay);
    return $interval->days;
}

// Count exams by status
$upcomingCount = 0;
$ongoingCount = 0;
$completedCount = 0;
$totalCount = 0;

if ($exams && $exams->num_rows > 0) {
    $totalCount = $exams->num_rows;

    // Reset the pointer
    $exams->data_seek(0);

    // Count upcoming, ongoing, and completed exams
    $today = date('Y-m-d');
    while ($exam = $exams->fetch_assoc()) {
        $examDate = $exam['exam_date'];
        $examEndDate = !empty($exam['end_date']) ? $exam['end_date'] : $examDate;

        if ($examDate > $today) {
            $upcomingCount++;
        } elseif ($examEndDate < $today) {
            $completedCount++;
        } else {
            $ongoingCount++;
        }
    }

    // Reset the pointer again
    $exams->data_seek(0);
}

// Set page title
$page_title = "পরীক্ষা";
$school_name = "নিশাত এডুকেশন সেন্টার";
$school_address = "চুয়াডাঙ্গা, বাংলাদেশ";
$school_logo = "img/logo.jpg";
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title><?php echo $page_title; ?> - <?php echo $school_name; ?></title>

    <!-- Bootstrap CSS -->
    

    <!-- Custom Fonts CSS -->
    

    <!-- Custom CSS -->
    

    <style>
        :root {
            --primary-color: #006A4E; /* Deep Green */
            --secondary-color: #00563B; /* Darker Green */
            --accent-color: #F39C12; /* Amber/Gold */
            --dark-color: #2C3E50; /* Dark Blue-Gray */
            --light-color: #F5F5F5; /* Off-White */
            --text-color: #333333; /* Dark Gray */
            --light-text: #FFFFFF; /* White */
            --highlight-color: #E74C3C; /* Red Accent */
            --soft-color: #E3F2FD; /* Soft Blue */
        }

        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: var(--light-color);
            color: var(--text-color);
        }

        /* Header Styles */
        .header-top {
            background-color: white;
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .school-logo {
            width: 80px;
            height: 80px;
            object-fit: contain;
        }

        .school-title {
            color: var(--primary-color);
            font-weight: 700;
            font-size: 1.8rem;
        }

        .school-subtitle {
            color: var(--dark-color);
            font-weight: 500;
        }

        /* Navigation Styles */
        .main-nav {
            background-color: var(--primary-color);
            padding: 0;
        }

        .main-nav .nav-link {
            color: var(--light-text);
            font-weight: 500;
            padding: 15px 20px;
            transition: all 0.3s;
            border-radius: 0;
        }

        .main-nav .nav-link:hover,
        .main-nav .nav-link.active {
            background-color: var(--secondary-color);
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 60px 0;
            margin-bottom: 30px;
        }

        .hero-title {
            font-weight: 700;
            margin-bottom: 20px;
            color: white;
        }

        .hero-text {
            font-size: 1.1rem;
            margin-bottom: 30px;
            opacity: 0.9;
            color: white;
        }

        /* Card Styles */
        .card {
            border: none;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            height: 100%;
            background-color: white;
            margin-bottom: 20px;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .card-header {
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
            padding: 15px 20px;
            border: none;
        }

        .card-header.warning-header {
            background-color: var(--accent-color);
            color: var(--dark-color);
        }

        .card-body {
            padding: 25px;
        }

        /* Exam Card Styles */
        .exam-card {
            transition: all 0.3s ease;
            border-left: 5px solid #006A4E;
        }

        .exam-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .exam-date {
            font-size: 0.9rem;
            color: #6c757d;
        }

        .text-danger {
            color: #dc3545 !important;
            font-weight: bold;
        }

        /* Footer Styles */
        .footer {
            background-color: var(--dark-color);
            color: white;
            padding: 40px 0 20px;
            margin-top: 50px;
        }

        .footer h5 {
            color: var(--light-color);
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--accent-color);
            display: inline-block;
        }

        .footer-links {
            list-style: none;
            padding: 0;
        }

        .footer-links li {
            margin-bottom: 10px;
        }

        .footer-links a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s;
        }

        .footer-links a:hover {
            color: white;
            padding-left: 5px;
        }

        .social-icons a {
            display: inline-block;
            width: 36px;
            height: 36px;
            background-color: rgba(255,255,255,0.1);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 36px;
            margin-right: 10px;
            transition: all 0.3s;
        }

        .social-icons a:hover {
            background-color: var(--accent-color);
            transform: translateY(-3px);
        }

        .copyright {
            background-color: rgba(0,0,0,0.2);
            padding: 15px 0;
            margin-top: 30px;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .school-title {
                font-size: 1.4rem;
            }

            .school-subtitle {
                font-size: 1rem;
            }

            .hero-section {
                padding: 40px 0;
            }

            .hero-img {
                margin-top: 30px;
            }
        }
    </style>
</head>
<body>
    <!-- Header Top with Logo and Title -->
    <div class="header-top">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-2 text-center text-md-start mb-3 mb-md-0">
                    <img src="<?php echo $school_logo; ?>" alt="School Logo" class="school-logo" onerror="this.src='https://via.placeholder.com/80?text=ZFAW'">
                </div>
                <div class="col-md-10 text-center text-md-start">
                    <h1 class="school-title mb-1"><?php echo $school_name; ?></h1>
                    <h2 class="school-subtitle fs-5"><?php echo $school_address; ?></h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Navigation -->
    <nav class="main-nav">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <ul class="nav">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php"><i class="fas fa-home me-1"></i> হোম</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="subjects.php"><i class="fas fa-book me-1"></i> বিষয়সমূহ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="teachers.php"><i class="fas fa-chalkboard-teacher me-1"></i> শিক্ষকবৃন্দ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="students.php"><i class="fas fa-user-graduate me-1"></i> শিক্ষার্থীবৃন্দ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="notices.php"><i class="fas fa-bullhorn me-1"></i> নোটিশ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="assignments.php"><i class="fas fa-tasks me-1"></i> অ্যাসাইনমেন্ট</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="exams.php"><i class="fas fa-file-alt me-1"></i> পরীক্ষা</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="results.php"><i class="fas fa-chart-bar me-1"></i> ফলাফল</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

<div class="container mt-4 mb-5">
    <div class="row">
        <div class="col-md-12">
            <h2 class="text-center mb-4">পরীক্ষা সময়সূচী</h2>
            <p class="text-center text-muted mb-4">আমাদের প্রতিষ্ঠানের আসন্ন এবং চলমান পরীক্ষাসমূহের সময়সূচী</p>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">পরীক্ষা ফিল্টার</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="" class="row g-3">
                        <div class="col-md-4">
                            <label for="class_id" class="form-label">শ্রেণী</label>
                            <select name="class_id" id="class_id" class="form-select">
                                <option value="">সকল শ্রেণী</option>
                                <?php if ($classes && $classes->num_rows > 0): ?>
                                    <?php
                                    $seenClasses = [];
                                    $classes->data_seek(0); // Reset pointer
                                    while ($class = $classes->fetch_assoc()):
                                        // Prevent duplicate class names
                                        if (!in_array($class['class_name'], $seenClasses)):
                                            $seenClasses[] = $class['class_name'];
                                    ?>
                                        <option value="<?php echo $class['id']; ?>"><?php echo $class['class_name']; ?></option>
                                    <?php
                                        endif;
                                    endwhile;
                                    ?>
                                <?php endif; ?>
                            </select>
                        </div>

                        <div class="col-md-4">
                            <label for="exam_type" class="form-label">পরীক্ষার ধরন</label>
                            <select name="exam_type" id="exam_type" class="form-select">
                                <option value="">সকল ধরন</option>
                                <?php if ($examTypes && $examTypes->num_rows > 0): ?>
                                    <?php while ($type = $examTypes->fetch_assoc()): ?>
                                        <option value="<?php echo $type['exam_type']; ?>"><?php echo $type['exam_type']; ?></option>
                                    <?php endwhile; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="exam_date" class="form-label">তারিখ</label>
                            <input type="date" name="exam_date" id="exam_date" class="form-control">
                        </div>
                        <div class="col-12 text-center">
                            <button type="submit" name="filter_exams" class="btn btn-primary">
                                <i class="fas fa-filter me-2"></i> ফিল্টার করুন
                            </button>
                            <a href="exams.php" class="btn btn-secondary ms-2">
                                <i class="fas fa-sync-alt me-2"></i> রিসেট করুন
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Exams List -->
    <div class="row">
        <?php
        $examsList = $filterApplied ? $filteredExams : $exams;
        if ($examsList && $examsList->num_rows > 0):
        ?>
            <?php
            $currentDate = null;
            while ($exam = $examsList->fetch_assoc()):
                $examDate = date('Y-m-d', strtotime($exam['exam_date']));

                // Group by date
                if ($currentDate !== $examDate) {
                    $currentDate = $examDate;
                    echo '<div class="col-12 mt-4 mb-3">';
                    echo '<h4 class="date-header">' . date('d F Y (l)', strtotime($exam['exam_date'])) . '</h4>';
                    echo '</div>';
                }
            ?>
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card exam-card h-100">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><?php echo htmlspecialchars($exam['exam_name']); ?></h5>
                        </div>
                        <div class="card-body">
                            <div class="exam-details">
                                <?php if (!empty($exam['class_name'])): ?>
                                <p><i class="fas fa-graduation-cap me-2"></i> <strong>শ্রেণী:</strong> <?php echo htmlspecialchars($exam['class_name']); ?></p>
                                <?php endif; ?>

                                <?php if (!empty($exam['exam_type'])): ?>
                                <p><i class="fas fa-tag me-2"></i> <strong>পরীক্ষার ধরন:</strong> <?php echo htmlspecialchars($exam['exam_type']); ?></p>
                                <?php endif; ?>

                                <p><i class="fas fa-calendar-alt me-2"></i> <strong>তারিখ:</strong> <?php echo date('d F Y', strtotime($exam['exam_date'])); ?></p>

                                <?php if (!empty($exam['start_time']) && !empty($exam['end_time'])): ?>
                                <p><i class="fas fa-clock me-2"></i> <strong>সময়:</strong> <?php echo date('h:i A', strtotime($exam['start_time'])); ?> - <?php echo date('h:i A', strtotime($exam['end_time'])); ?></p>
                                <?php endif; ?>

                                <?php if (!empty($exam['total_marks'])): ?>
                                <p><i class="fas fa-star me-2"></i> <strong>মোট নম্বর:</strong> <?php echo htmlspecialchars($exam['total_marks']); ?></p>
                                <?php endif; ?>

                                <?php if (!empty($exam['passing_marks'])): ?>
                                <p><i class="fas fa-check-circle me-2"></i> <strong>পাস নম্বর:</strong> <?php echo htmlspecialchars($exam['passing_marks']); ?></p>
                                <?php endif; ?>
                            </div>

                            <?php
                            // Calculate days remaining
                            $daysRemaining = getDaysRemaining($exam['exam_date']);
                            $isPast = strtotime($exam['exam_date']) < strtotime('today');
                            $badgeClass = $isPast ? 'bg-secondary' : ($daysRemaining <= 7 ? 'bg-danger' : ($daysRemaining <= 30 ? 'bg-warning text-dark' : 'bg-success'));
                            $badgeText = $isPast ? 'সম্পন্ন হয়েছে' : ($daysRemaining == 0 ? 'আজ' : ($daysRemaining . ' দিন বাকি'));
                            ?>

                            <div class="text-center mt-3">
                                <span class="badge <?php echo $badgeClass; ?> p-2">
                                    <i class="fas <?php echo $isPast ? 'fa-check' : 'fa-hourglass-half'; ?> me-1"></i>
                                    <?php echo $badgeText; ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endwhile; ?>
        <?php else: ?>
            <div class="col-12">
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i> কোন পরীক্ষা পাওয়া যায়নি। অনুগ্রহ করে পরে আবার চেক করুন।
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Additional Information -->
    <div class="row mt-5">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">পরীক্ষা সম্পর্কিত তথ্য</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle me-2"></i> পরীক্ষার নিয়মাবলী:</h6>
                            <ul>
                                <li>পরীক্ষা শুরুর ৩০ মিনিট আগে পরীক্ষা কেন্দ্রে উপস্থিত থাকতে হবে।</li>
                                <li>প্রবেশপত্র ছাড়া কোন শিক্ষার্থীকে পরীক্ষায় অংশগ্রহণ করতে দেওয়া হবে না।</li>
                                <li>মোবাইল ফোন, ক্যালকুলেটর (অনুমতি ছাড়া) এবং অন্যান্য ইলেকট্রনিক ডিভাইস নিষিদ্ধ।</li>
                                <li>পরীক্ষার হলে কোন ধরনের অসদাচরণ করলে তাৎক্ষণিকভাবে বহিষ্কার করা হবে।</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-question-circle me-2"></i> প্রায়শই জিজ্ঞাসিত প্রশ্ন:</h6>
                            <div class="accordion" id="examFAQ">
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingOne">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="false" aria-controls="collapseOne">
                                            পরীক্ষার ফলাফল কখন প্রকাশ করা হবে?
                                        </button>
                                    </h2>
                                    <div id="collapseOne" class="accordion-collapse collapse" aria-labelledby="headingOne" data-bs-parent="#examFAQ">
                                        <div class="accordion-body">
                                            সাধারণত পরীক্ষা শেষ হওয়ার ১৫-৩০ দিনের মধ্যে ফলাফল প্রকাশ করা হয়। নির্দিষ্ট তারিখ জানতে অফিসে যোগাযোগ করুন।
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingTwo">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                            পরীক্ষার সময়সূচীতে পরিবর্তন হতে পারে?
                                        </button>
                                    </h2>
                                    <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#examFAQ">
                                        <div class="accordion-body">
                                            হ্যাঁ, অপ্রত্যাশিত পরিস্থিতিতে পরীক্ষার সময়সূচীতে পরিবর্তন হতে পারে। এ ব্যাপারে নিয়মিত নোটিশ বোর্ড এবং ওয়েবসাইট চেক করুন।
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingThree">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                            পরীক্ষার প্রস্তুতি কিভাবে নিতে হবে?
                                        </button>
                                    </h2>
                                    <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#examFAQ">
                                        <div class="accordion-body">
                                            নিয়মিত ক্লাস করুন, সিলেবাস অনুযায়ী পড়াশুনা করুন, মডেল টেস্ট দিন এবং শিক্ষকদের পরামর্শ অনুসরণ করুন। প্রয়োজনে অতিরিক্ত ক্লাস নিতে পারেন।
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .date-header {
        position: relative;
        padding-bottom: 10px;
        margin-bottom: 20px;
        color: #006A4E;
        border-bottom: 1px solid #dee2e6;
    }

    .date-header:after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 100px;
        height: 3px;
        background-color: #006A4E;
    }

    .exam-card {
        transition: all 0.3s ease;
        border-left: 5px solid #006A4E;
    }

    .exam-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .exam-details p {
        margin-bottom: 8px;
    }

    .exam-card .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }
</style>

<?php
// Include footer
include_once 'includes/footer.php';
?>
