<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Function to calculate GPA based on grade
function calculateGPA($grade) {
    switch ($grade) {
        case 'A+': return 5.00;
        case 'A': return 4.00;
        case 'A-': return 3.50;
        case 'B': return 3.00;
        case 'C': return 2.00;
        case 'D': return 1.00;
        case 'F':
        default: return 0.00;
    }
}

// Get parameters
$examId = isset($_GET['exam_id']) ? intval($_GET['exam_id']) : 0;
$studentId = isset($_GET['student_id']) ? intval($_GET['student_id']) : 0;

// Redirect if parameters are missing
if ($examId == 0 || $studentId == 0) {
    header("Location: generate_marksheet.php");
    exit();
}

// Get student details
$studentInfo = null;
$studentQuery = "SELECT s.*, c.class_name, d.department_name, ss.session_name, s.profile_photo
                FROM students s
                LEFT JOIN classes c ON s.class_id = c.id
                LEFT JOIN departments d ON s.department_id = d.id
                LEFT JOIN sessions ss ON s.session_id = ss.id
                WHERE s.id = ?";
$stmt = $conn->prepare($studentQuery);
$stmt->bind_param("i", $studentId);
$stmt->execute();
$studentInfo = $stmt->get_result()->fetch_assoc();

// Get exam details
$examInfo = null;
$examQuery = "SELECT e.*, c.class_name, d.department_name, s.session_name
             FROM exams e
             LEFT JOIN classes c ON e.class_id = c.id
             LEFT JOIN departments d ON e.department_id = d.id
             LEFT JOIN sessions s ON e.session_id = s.id
             WHERE e.id = ?";
$stmt = $conn->prepare($examQuery);
$stmt->bind_param("i", $examId);
$stmt->execute();
$examInfo = $stmt->get_result()->fetch_assoc();

// Get results
$results = null;
if ($studentId > 0 && $examId > 0) {
    // Get student's selected subjects and check which one is the 4th subject
    $studentSubjectsQuery = "SELECT ss.*, s.subject_name, s.subject_code, s.is_fourth_subject
                           FROM student_subjects ss
                           JOIN subjects s ON ss.subject_id = s.id
                           WHERE ss.student_id = ?";
    $stmtSubjects = $conn->prepare($studentSubjectsQuery);
    $stmtSubjects->bind_param("i", $studentId);
    $stmtSubjects->execute();
    $studentSubjects = $stmtSubjects->get_result();

    // Create a map of subject_id to is_fourth_subject
    $fourthSubjectMap = [];
    while ($subj = $studentSubjects->fetch_assoc()) {
        if (isset($subj['is_fourth_subject']) && $subj['is_fourth_subject'] == 1) {
            $fourthSubjectMap[$subj['subject_id']] = true;
        }
    }

    // Get results with subject info
    $resultsQuery = "SELECT r.*, s.subject_name, s.subject_code
                    FROM results r
                    JOIN subjects s ON r.subject_id = s.id
                    WHERE r.student_id = ? AND r.exam_id = ?
                    ORDER BY s.subject_name";

    $stmt = $conn->prepare($resultsQuery);
    $stmt->bind_param("ii", $studentId, $examId);
    $stmt->execute();
    $results = $stmt->get_result();

    // Add is_fourth_subject flag to results
    $allResults = [];
    while ($row = $results->fetch_assoc()) {
        $row['is_fourth_subject'] = isset($fourthSubjectMap[$row['subject_id']]) ? 1 : 0;
        $allResults[] = $row;
    }

    // Sort results to put 4th subject at the end
    usort($allResults, function($a, $b) {
        if ($a['is_fourth_subject'] != $b['is_fourth_subject']) {
            return $a['is_fourth_subject'] - $b['is_fourth_subject'];
        }
        return strcmp($a['subject_name'], $b['subject_name']);
    });

    // Store results in a variable
    $results = $allResults;
}

// Redirect if no data found
if (!$studentInfo || !$examInfo || !$results || count($results) == 0) {
    header("Location: generate_marksheet.php?error=nodata");
    exit();
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>মার্কশীট - <?php echo htmlspecialchars($studentInfo['first_name'] . ' ' . $studentInfo['last_name']); ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/hind-siliguri.css">
    <style>
        body {
            font-family: 'Hind Siliguri', 'Noto Sans Bengali', sans-serif;
            background-color: #fff;
            color: #333;
            line-height: 1.6;
        }
        * {
            font-family: 'Hind Siliguri', 'Noto Sans Bengali', sans-serif;
        }
        .marksheet {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px auto;
            max-width: 800px;
            background-color: #fff;
        }
        .marksheet-header {
            margin-bottom: 20px;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header-left {
            width: 20%;
            text-align: center;
        }
        .header-center {
            width: 60%;
            text-align: center;
        }
        .header-right {
            width: 20%;
            text-align: center;
        }
        .institution-logo {
            max-width: 100px;
            max-height: 100px;
        }
        .student-photo {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border: 3px solid #ddd;
            border-radius: 50%;
            overflow: hidden;
        }
        .marksheet-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .marksheet-subtitle {
            font-size: 18px;
            margin-bottom: 5px;
        }
        .student-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .student-info-left, .student-info-right {
            width: 48%;
        }
        .info-row {
            margin-bottom: 10px;
        }
        .info-label {
            font-weight: bold;
            display: inline-block;
            width: 120px;
        }
        .marks-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border: 1px solid #aaa;
        }
        .marks-table th, .marks-table td {
            border: 1px solid #aaa;
            padding: 8px;
            text-align: center;
        }
        .marks-table th {
            background-color: #f2f2f2;
        }
        .marks-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .result-summary {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        .result-summary-left, .result-summary-right {
            width: 48%;
        }
        .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 50px;
        }
        .signature-box {
            text-align: center;
            width: 30%;
        }
        .signature-line {
            border-top: 1px solid #333;
            margin-top: 40px;
            padding-top: 5px;
        }
        .gpa-highlight {
            font-weight: bold;
            color: #007bff;
            font-size: 1.1em;
        }
        .grade-cell {
            font-weight: bold;
        }
        .grade-A\+ {
            color: #28a745;
        }
        .grade-A {
            color: #28a745;
        }
        .grade-A- {
            color: #28a745;
        }
        .grade-B {
            color: #17a2b8;
        }
        .grade-C {
            color: #ffc107;
        }
        .grade-D {
            color: #fd7e14;
        }
        .grade-F {
            color: #dc3545;
        }
        @media print {
            body {
                background-color: #fff;
                margin: 0;
                padding: 0;
            }
            .marksheet {
                border: none;
                padding: 0;
                margin: 0;
                max-width: 100%;
                width: 210mm;
                height: 297mm;
                page-break-after: always;
            }
            .no-print {
                display: none !important;
            }
            /* Reduce font sizes for better fit */
            .marksheet-title {
                font-size: 20px;
            }
            .marksheet-subtitle {
                font-size: 16px;
            }
            .info-row {
                margin-bottom: 5px;
            }
            .marks-table {
                border: 1px solid #aaa !important;
            }
            .marks-table th, .marks-table td {
                padding: 4px;
                font-size: 12px;
                border: 1px solid #aaa !important;
                background-color: transparent !important;
            }
            .marks-table tr:nth-child(even) {
                background-color: #f9f9f9 !important;
            }
            .signature-section {
                margin-top: 20px;
            }
            .signature-line {
                margin-top: 20px;
            }
            /* Ensure the page doesn't scale */
            @page {
                size: A4;
                margin: 10mm;
            }
        }
    </style>
</head>
<body onload="window.print()">
    <div class="marksheet">
        <div class="marksheet-header">
            <div class="header-left">
                <?php
                // Check if institution logo exists
                $logoPath = '../uploads/institution_logo.png';
                $defaultLogoPath = '../assets/images/default_logo.png';
                $defaultLogoSvgPath = '../assets/images/default_logo.svg';

                if (file_exists($logoPath)) {
                    echo '<img src="' . $logoPath . '" alt="Institution Logo" class="institution-logo">';
                } else if (file_exists($defaultLogoPath)) {
                    echo '<img src="' . $defaultLogoPath . '" alt="Default Logo" class="institution-logo">';
                } else if (file_exists($defaultLogoSvgPath)) {
                    echo '<img src="' . $defaultLogoSvgPath . '" alt="Default Logo" class="institution-logo">';
                } else {
                    echo '<div style="width:100px;height:100px;background:#f8f9fa;display:flex;align-items:center;justify-content:center;border:1px solid #ddd;"><i class="fas fa-university" style="font-size:40px;color:#6c757d;"></i></div>';
                }
                ?>
            </div>
            <div class="header-center">
                <div class="marksheet-title">স্কুল ম্যানেজমেন্ট সিস্টেম</div>
                <div class="marksheet-subtitle">মার্কশীট</div>
                <div class="marksheet-subtitle"><?php echo htmlspecialchars($examInfo['exam_name']); ?></div>
            </div>
            <div class="header-right">
                <div class="photo-container">
                    <?php
                    // Check if student has a profile photo in database
                    if (!empty($studentInfo['profile_photo']) && file_exists('../' . $studentInfo['profile_photo'])) {
                        echo '<img src="../' . $studentInfo['profile_photo'] . '" alt="Student Photo" class="student-photo">';
                    } else {
                        // Fallback to old method
                        $photoPath = '../uploads/students/' . $studentInfo['student_id'] . '.jpg';
                        $defaultPhotoPath = '../assets/images/default_student.png';
                        $defaultPhotoSvgPath = '../assets/images/default_student.svg';

                        if (file_exists($photoPath)) {
                            echo '<img src="' . $photoPath . '" alt="Student Photo" class="student-photo">';
                        } else if (file_exists($defaultPhotoPath)) {
                            echo '<img src="' . $defaultPhotoPath . '" alt="Default Student" class="student-photo">';
                        } else if (file_exists($defaultPhotoSvgPath)) {
                            echo '<img src="' . $defaultPhotoSvgPath . '" alt="Default Student" class="student-photo">';
                        } else {
                            echo '<div style="width:120px;height:120px;background:#f8f9fa;display:flex;align-items:center;justify-content:center;border:3px solid #ddd;border-radius:50%;"><i class="fas fa-user" style="font-size:40px;color:#6c757d;"></i></div>';
                        }
                    }
                    ?>
                </div>
            </div>
        </div>

        <div class="student-info">
            <div class="student-info-left">
                <div class="info-row">
                    <span class="info-label">নাম:</span>
                    <span><?php echo htmlspecialchars($studentInfo['first_name'] . ' ' . $studentInfo['last_name']); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">রোল নম্বর:</span>
                    <span><?php echo htmlspecialchars($studentInfo['roll_number']); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">শিক্ষার্থী আইডি:</span>
                    <span><?php echo htmlspecialchars($studentInfo['student_id']); ?></span>
                </div>
            </div>
            <div class="student-info-right">
                <div class="info-row">
                    <span class="info-label">শ্রেণী:</span>
                    <span><?php echo htmlspecialchars($studentInfo['class_name'] ?? 'N/A'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">বিভাগ:</span>
                    <span><?php echo htmlspecialchars($studentInfo['department_name'] ?? 'N/A'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">সেশন:</span>
                    <span><?php echo htmlspecialchars($studentInfo['session_name'] ?? 'N/A'); ?></span>
                </div>
            </div>
        </div>

        <table class="marks-table">
            <thead>
                <tr>
                    <th>ক্রম</th>
                    <th>বিষয়</th>
                    <th>বিষয় কোড</th>
                    <th>সিকিউ</th>
                    <th>এমসিকিউ</th>
                    <th>প্র্যাকটিক্যাল</th>
                    <th>পূর্ণ মার্কস</th>
                    <th>প্রাপ্ত মার্কস</th>
                    <th>শতকরা</th>
                    <th>গ্রেড</th>
                    <th>জিপিএ</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $totalMarks = 0;
                $totalObtained = 0;
                $totalGPA = 0;
                $subjectCount = 0;
                $mainSubjects = [];
                $fourthSubject = null;
                $i = 1;

                // Process all subjects
                foreach ($results as $result):
                    $totalMarks += $result['total_marks'];
                    $totalObtained += $result['marks_obtained'];
                    $subjectPercentage = ($result['total_marks'] > 0) ? ($result['marks_obtained'] / $result['total_marks']) * 100 : 0;
                    $subjectGPA = calculateGPA($result['grade']);

                    // Store subject data for GPA calculation
                    $subjectData = [
                        'name' => $result['subject_name'],
                        'code' => $result['subject_code'],
                        'gpa' => $subjectGPA,
                        'is_fourth_subject' => isset($result['is_fourth_subject']) && $result['is_fourth_subject'] == 1
                    ];

                    // Check if this is marked as a 4th subject
                    if (isset($result['is_fourth_subject']) && $result['is_fourth_subject'] == 1) {
                        $fourthSubject = $subjectData;
                    } else {
                        $mainSubjects[] = $subjectData;
                    }

                    $subjectCount++;
                ?>
                <tr<?php echo (isset($result['is_fourth_subject']) && $result['is_fourth_subject'] == 1) ? ' class="table-info"' : ''; ?>>
                    <td><?php echo $i++; ?></td>
                    <td>
                        <?php echo htmlspecialchars($result['subject_name']); ?>
                        <?php if (isset($result['is_fourth_subject']) && $result['is_fourth_subject'] == 1): ?>
                            <span class="badge bg-info">৪র্থ বিষয়</span>
                        <?php endif; ?>
                    </td>
                    <td><?php echo htmlspecialchars($result['subject_code'] ?? 'N/A'); ?></td>
                    <td><?php echo isset($result['cq_marks']) && $result['cq_marks'] > 0 ? intval($result['cq_marks']) : ''; ?></td>
                    <td><?php echo isset($result['mcq_marks']) && $result['mcq_marks'] > 0 ? intval($result['mcq_marks']) : ''; ?></td>
                    <td><?php echo isset($result['practical_marks']) && $result['practical_marks'] > 0 ? intval($result['practical_marks']) : ''; ?></td>
                    <td><?php echo $result['total_marks'] > 0 ? intval($result['total_marks']) : ''; ?></td>
                    <td><?php echo $result['marks_obtained'] > 0 ? intval($result['marks_obtained']) : ''; ?></td>
                    <td><?php echo $subjectPercentage > 0 ? intval($subjectPercentage) . '%' : ''; ?></td>
                    <td class="grade-cell grade-<?php echo $result['grade']; ?>"><?php echo $result['grade']; ?></td>
                    <td class="gpa-highlight"><?php echo $subjectGPA > 0 ? number_format($subjectGPA, 2) : ''; ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
            <tfoot>
                <tr>
                    <th colspan="6">মোট</th>
                    <th><?php echo $totalMarks > 0 ? intval($totalMarks) : ''; ?></th>
                    <th><?php echo $totalObtained > 0 ? intval($totalObtained) : ''; ?></th>
                    <th>
                        <?php
                        $percentage = ($totalMarks > 0) ? ($totalObtained / $totalMarks) * 100 : 0;
                        echo $percentage > 0 ? intval($percentage) . '%' : '';
                        ?>
                    </th>
                    <th class="grade-cell">
                        <?php
                        $finalGrade = '';
                        if ($percentage >= 80) $finalGrade = 'A+';
                        elseif ($percentage >= 70) $finalGrade = 'A';
                        elseif ($percentage >= 60) $finalGrade = 'A-';
                        elseif ($percentage >= 50) $finalGrade = 'B';
                        elseif ($percentage >= 40) $finalGrade = 'C';
                        elseif ($percentage >= 33) $finalGrade = 'D';
                        else $finalGrade = 'F';
                        ?>
                        <span class="grade-<?php echo $finalGrade; ?>"><?php echo $finalGrade; ?></span>
                    </th>
                    <th class="gpa-highlight">
                        <?php
                        // Calculate GPA according to Bangladesh Education Board rules
                        $mainSubjectCount = count($mainSubjects);
                        $mainSubjectsGPA = 0;
                        $hasFailed = false;

                        // Check if any main subject has F grade
                        foreach ($mainSubjects as $subject) {
                            if ($subject['gpa'] == 0) {
                                $hasFailed = true;
                                break;
                            }
                            $mainSubjectsGPA += $subject['gpa'];
                        }

                        // If any main subject has F grade, final GPA is 0
                        if ($hasFailed) {
                            $baseGPA = 0;
                            $finalGPA = 0;
                            $finalGrade = 'F';
                        } else {
                            // Calculate base GPA (without 4th subject)
                            $baseGPA = ($mainSubjectCount > 0) ? $mainSubjectsGPA / $mainSubjectCount : 0;

                            // Add bonus from 4th subject if applicable
                            $bonusGPA = 0;
                            $finalGPA = $baseGPA;

                            if ($fourthSubject && $fourthSubject['gpa'] > 2.0) {
                                $bonusGPA = $fourthSubject['gpa'] - 2.0;
                                $finalGPA = $baseGPA + $bonusGPA;
                            }

                            // Cap GPA at 5.0
                            $finalGPA = min($finalGPA, 5.0);
                        }

                        // Store for later use
                        $averageGPA = $finalGPA;

                        echo $finalGPA > 0 ? number_format($finalGPA, 2) : '';
                        ?>
                    </th>
                </tr>
            </tfoot>
        </table>

        <div class="result-summary">
            <div class="result-summary-left">
                <div class="info-row">
                    <span class="info-label">মোট মার্কস:</span>
                    <span><?php echo $totalMarks > 0 ? intval($totalMarks) : ''; ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">প্রাপ্ত মার্কস:</span>
                    <span><?php echo $totalObtained > 0 ? intval($totalObtained) : ''; ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">শতকরা হার:</span>
                    <span><?php echo $percentage > 0 ? intval($percentage) . '%' : ''; ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">গ্রেড:</span>
                    <span class="grade-cell grade-<?php echo $finalGrade; ?>">
                        <?php echo $finalGrade; ?>
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">জিপিএ:</span>
                    <span class="gpa-highlight"><?php echo $averageGPA > 0 ? number_format($averageGPA, 2) : ''; ?></span>
                </div>
            </div>
            <div class="result-summary-right">
                <div class="info-row">
                    <span class="info-label">পরীক্ষার তারিখ:</span>
                    <span><?php echo date('d/m/Y', strtotime($examInfo['exam_date'])); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">ফলাফল প্রকাশের তারিখ:</span>
                    <span><?php echo date('d/m/Y'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">ফলাফল:</span>
                    <span class="<?php echo ($hasFailed) ? 'text-danger fw-bold' : 'text-success fw-bold'; ?>">
                        <?php echo ($hasFailed) ? 'অনুত্তীর্ণ' : 'উত্তীর্ণ'; ?>
                    </span>
                </div>

                <?php if ($fourthSubject && $fourthSubject['gpa'] > 2.0): ?>
                <div class="info-row mt-3">
                    <span class="info-label">৪র্থ বিষয়:</span>
                    <span><?php echo htmlspecialchars($fourthSubject['name']); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">৪র্থ বিষয় GPA:</span>
                    <span><?php echo $fourthSubject['gpa'] > 0 ? number_format($fourthSubject['gpa'], 2) : ''; ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">বোনাস পয়েন্ট:</span>
                    <span class="text-success"><?php echo $bonusGPA > 0 ? '+' . number_format($bonusGPA, 2) : ''; ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="mt-4 mb-4 p-3 border-top">
            <h6 class="mb-2">জিপিএ ক্যালকুলেশন পদ্ধতি:</h6>
            <ul class="small">
                <li>৪র্থ বিষয় ছাড়া অন্য কোন বিষয়ে F গ্রেড পেলে সামগ্রিক ফলাফল F এবং জিপিএ 0.00 হবে।</li>
                <li>৬টি মূল বিষয়ের জিপিএ যোগ করে ৬ দিয়ে ভাগ করে গড় জিপিএ নির্ধারণ করা হয়।</li>
                <li>৪র্থ বিষয়ে যদি জিপিএ ২.০০ এর বেশি হয়, তাহলে অতিরিক্ত পয়েন্ট (৪র্থ বিষয়ের জিপিএ - ২.০০) গড় জিপিএর সাথে যোগ করা হয়।</li>
                <li>৪র্থ বিষয়ে যদি জিপিএ ২.০০ এর কম হয়, তাহলে কোন অতিরিক্ত পয়েন্ট যোগ করা হয় না।</li>
                <li>সর্বোচ্চ জিপিএ ৫.০০ এর বেশি হতে পারে না।</li>
            </ul>
        </div>

        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-line">শিক্ষার্থীর স্বাক্ষর</div>
            </div>
            <div class="signature-box">
                <div class="signature-line">শ্রেণী শিক্ষকের স্বাক্ষর</div>
            </div>
            <div class="signature-box">
                <div class="signature-line">প্রধান শিক্ষকের স্বাক্ষর</div>
            </div>
        </div>
    </div>

    <div class="text-center mt-4 mb-4 no-print">
        <a href="generate_marksheet.php" class="btn btn-primary">ফিরে যান</a>
        <button onclick="window.print()" class="btn btn-success ms-2">প্রিন্ট করুন</button>
        <a href="http://localhost/zfaw/admin/exams.php" class="btn btn-outline-primary ms-2">পরীক্ষা তালিকা</a>
    </div>
</body>
</html>
