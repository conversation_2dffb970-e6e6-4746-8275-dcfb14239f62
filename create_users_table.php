<?php
// Database connection
require_once 'includes/dbh.inc.php';

echo "<h2>Creating Users Table</h2>";

// Create users table
$usersTableQuery = "CREATE TABLE IF NOT EXISTS users (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    user_type ENUM('admin', 'teacher', 'student', 'staff') NOT NULL,
    email VARCHAR(100) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active'
)";

if ($conn->query($usersTableQuery)) {
    echo "<p>Users table created successfully!</p>";
    
    // Check if admin user exists
    $checkAdmin = $conn->query("SELECT * FROM users WHERE username='admin' AND user_type='admin'");
    
    if ($checkAdmin->num_rows == 0) {
        // Create default admin user
        $adminPassword = password_hash("admin123", PASSWORD_DEFAULT);
        $insertAdmin = $conn->prepare("INSERT INTO users (username, password, user_type, email) VALUES (?, ?, 'admin', '<EMAIL>')");
        $insertAdmin->bind_param("ss", $adminUsername, $adminPassword);
        
        $adminUsername = "admin";
        
        if ($insertAdmin->execute()) {
            echo "<p>Default admin user created successfully!</p>";
            echo "<p>Username: admin<br>Password: admin123</p>";
            echo "<p><strong>Important:</strong> Please change this password immediately after logging in.</p>";
        } else {
            echo "<p>Error creating default admin user: " . $insertAdmin->error . "</p>";
        }
        
        $insertAdmin->close();
    } else {
        echo "<p>Admin user already exists.</p>";
    }
} else {
    echo "<p>Error creating users table: " . $conn->error . "</p>";
}

echo "<p><a href='index.php'>Return to homepage</a></p>";

$conn->close();
?>
