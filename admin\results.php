<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get filter values from GET request
$class_id = isset($_GET['class_id']) ? $_GET['class_id'] : '';
$exam_id = isset($_GET['exam_id']) ? $_GET['exam_id'] : '';
$student_id = isset($_GET['student_id']) ? $_GET['student_id'] : '';
$session_id = isset($_GET['session_id']) ? $_GET['session_id'] : '';
$department_id = isset($_GET['department_id']) ? $_GET['department_id'] : '';
$per_page = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 10; // Default 10 items per page
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1; // Default to page 1

// Build query for results with filters
$resultsQuery = "SELECT r.id, r.marks_obtained, r.grade, e.exam_name, e.total_marks,
                s.first_name, s.last_name, s.student_id as student_code, c.class_name,
                d.department_name, ss.session_name
                FROM results r
                JOIN students s ON r.student_id = s.id
                JOIN exams e ON r.exam_id = e.id
                LEFT JOIN classes c ON s.class_id = c.id
                LEFT JOIN departments d ON s.department_id = d.id
                LEFT JOIN sessions ss ON s.session_id = ss.id
                WHERE 1=1";

$queryParams = [];

if (!empty($class_id)) {
    $resultsQuery .= " AND s.class_id = ?";
    $queryParams[] = $class_id;
}

if (!empty($exam_id)) {
    $resultsQuery .= " AND r.exam_id = ?";
    $queryParams[] = $exam_id;
}

if (!empty($student_id)) {
    $resultsQuery .= " AND r.student_id = ?";
    $queryParams[] = $student_id;
}

if (!empty($session_id)) {
    $resultsQuery .= " AND s.session_id = ?";
    $queryParams[] = $session_id;
}

if (!empty($department_id)) {
    $resultsQuery .= " AND s.department_id = ?";
    $queryParams[] = $department_id;
}

// Count total results for pagination
$countQuery = str_replace("SELECT r.id, r.marks_obtained, r.grade, e.exam_name, e.total_marks,
                s.first_name, s.last_name, s.student_id as student_code, c.class_name", "SELECT COUNT(*) as total", $resultsQuery);

$countStmt = $conn->prepare($countQuery);

if (!empty($queryParams)) {
    $types = str_repeat("i", count($queryParams));
    $countStmt->bind_param($types, ...$queryParams);
}

$countStmt->execute();
$countResult = $countStmt->get_result();
$totalRows = $countResult->fetch_assoc()['total'];

// Calculate pagination
$totalPages = ceil($totalRows / $per_page);
$offset = ($page - 1) * $per_page;

// Add pagination to the query
$resultsQuery .= " ORDER BY r.id DESC LIMIT ?, ?";
$queryParams[] = $offset;
$queryParams[] = $per_page;

// Prepare and execute the query
$stmt = $conn->prepare($resultsQuery);

if (!empty($queryParams)) {
    $types = str_repeat("i", count($queryParams));
    $stmt->bind_param($types, ...$queryParams);
}

$stmt->execute();
$results = $stmt->get_result();

// Get all classes for filter dropdown
$classesQuery = "SELECT * FROM classes ORDER BY class_name";
$classes = $conn->query($classesQuery);

// Get all exams for filter dropdown
$examsQuery = "SELECT * FROM exams ORDER BY exam_name";
$exams = $conn->query($examsQuery);

// Get all students for filter dropdown
$studentsQuery = "SELECT id, student_id as student_code, first_name, last_name FROM students ORDER BY first_name, last_name";
$students = $conn->query($studentsQuery);

// Get all sessions for filter dropdown
$sessionsQuery = "SELECT * FROM sessions ORDER BY session_name";
$sessions = $conn->query($sessionsQuery);

// Get all departments for filter dropdown
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Check if created_at column exists in results table and add it if needed
$check_created_column = "SHOW COLUMNS FROM results LIKE 'created_at'";
$created_column_result = $conn->query($check_created_column);
if ($created_column_result->num_rows == 0) {
    // Column doesn't exist, add it
    $add_created_column = "ALTER TABLE results ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP";
    $conn->query($add_created_column);
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>শিক্ষার্থী ফলাফল - স্কুল ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col-md-8">
                        <h2>শিক্ষার্থী ফলাফল</h2>
                        <p class="text-muted">সকল শিক্ষার্থীদের পরীক্ষার ফলাফল দেখুন</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <!-- Result management page has been removed -->
                    </div>
                </div>

                <!-- Filter Form -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">ফলাফল ফিল্টার করুন</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="session_id" class="form-label">সেশন</label>
                                    <select name="session_id" id="session_id" class="form-select">
                                        <option value="">সকল সেশন</option>
                                        <?php if ($sessions && $sessions->num_rows > 0): ?>
                                            <?php while ($session = $sessions->fetch_assoc()): ?>
                                                <option value="<?php echo $session['id']; ?>" <?php echo ($session_id == $session['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $session['session_name']; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="department_id" class="form-label">বিভাগ</label>
                                    <select name="department_id" id="department_id" class="form-select">
                                        <option value="">সকল বিভাগ</option>
                                        <?php if ($departments && $departments->num_rows > 0): ?>
                                            <?php while ($department = $departments->fetch_assoc()): ?>
                                                <option value="<?php echo $department['id']; ?>" <?php echo ($department_id == $department['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $department['department_name']; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="class_id" class="form-label">ক্লাস</label>
                                    <select name="class_id" id="class_id" class="form-select">
                                        <option value="">সকল ক্লাস</option>
                                        <?php if ($classes && $classes->num_rows > 0): ?>
                                            <?php while ($class = $classes->fetch_assoc()): ?>
                                                <option value="<?php echo $class['id']; ?>" <?php echo ($class_id == $class['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $class['class_name']; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="exam_id" class="form-label">পরীক্ষা</label>
                                    <select name="exam_id" id="exam_id" class="form-select">
                                        <option value="">সকল পরীক্ষা</option>
                                        <?php if ($exams && $exams->num_rows > 0): ?>
                                            <?php while ($exam = $exams->fetch_assoc()): ?>
                                                <option value="<?php echo $exam['id']; ?>" <?php echo ($exam_id == $exam['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $exam['exam_name']; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="student_id" class="form-label">শিক্ষার্থী</label>
                                    <select name="student_id" id="student_id" class="form-select">
                                        <option value="">সকল শিক্ষার্থী</option>
                                        <?php if ($students && $students->num_rows > 0): ?>
                                            <?php while ($student = $students->fetch_assoc()): ?>
                                                <option value="<?php echo $student['id']; ?>" <?php echo ($student_id == $student['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $student['first_name'] . ' ' . $student['last_name'] . ' (' . $student['student_code'] . ')'; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="per_page" class="form-label">প্রতি পৃষ্ঠায় দেখানো হবে</label>
                                    <select name="per_page" id="per_page" class="form-select">
                                        <option value="5" <?php echo ($per_page == 5) ? 'selected' : ''; ?>>৫ জন</option>
                                        <option value="10" <?php echo ($per_page == 10) ? 'selected' : ''; ?>>১০ জন</option>
                                        <option value="15" <?php echo ($per_page == 15) ? 'selected' : ''; ?>>১৫ জন</option>
                                        <option value="20" <?php echo ($per_page == 20) ? 'selected' : ''; ?>>২০ জন</option>
                                        <option value="25" <?php echo ($per_page == 25) ? 'selected' : ''; ?>>২৫ জন</option>
                                        <option value="50" <?php echo ($per_page == 50) ? 'selected' : ''; ?>>৫০ জন</option>
                                        <option value="100" <?php echo ($per_page == 100) ? 'selected' : ''; ?>>১০০ জন</option>
                                    </select>
                                </div>
                            </div>
                            <div class="d-flex">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-filter me-2"></i>ফিল্টার করুন
                                </button>
                                <a href="results.php" class="btn btn-secondary">
                                    <i class="fas fa-sync-alt me-2"></i>রিসেট
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Results Table -->
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">ফলাফল তালিকা</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>শিক্ষার্থী</th>
                                        <th>আইডি</th>
                                        <th>সেশন</th>
                                        <th>বিভাগ</th>
                                        <th>ক্লাস</th>
                                        <th>পরীক্ষা</th>
                                        <th>মোট মার্কস</th>
                                        <th>প্রাপ্ত মার্কস</th>
                                        <th>শতকরা (%)</th>
                                        <th>গ্রেড</th>
                                        <th>একশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($results && $results->num_rows > 0): ?>
                                        <?php while ($result = $results->fetch_assoc()): ?>
                                            <tr>
                                                <td><?php echo $result['first_name'] . ' ' . $result['last_name']; ?></td>
                                                <td><?php echo $result['student_code']; ?></td>
                                                <td><?php echo $result['session_name'] ?? 'N/A'; ?></td>
                                                <td><?php echo $result['department_name'] ?? 'N/A'; ?></td>
                                                <td><?php echo $result['class_name']; ?></td>
                                                <td><?php echo $result['exam_name']; ?></td>
                                                <td><?php echo $result['total_marks']; ?></td>
                                                <td><?php echo $result['marks_obtained']; ?></td>
                                                <td>
                                                    <?php
                                                    $percentage = ($result['marks_obtained'] / $result['total_marks']) * 100;
                                                    echo number_format($percentage, 2) . '%';
                                                    ?>
                                                </td>
                                                <td>
                                                    <span class="badge <?php echo ($result['grade'] == 'F') ? 'bg-danger' : 'bg-success'; ?>">
                                                        <?php echo $result['grade']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="view_student.php?id=<?php echo $result['id']; ?>" class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="11" class="text-center">কোন ফলাফল পাওয়া যায়নি</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <div>
                                <p class="mb-0">মোট <?php echo $totalRows; ?> জন শিক্ষার্থীর মধ্যে <?php echo ($page-1)*$per_page+1; ?> থেকে <?php echo min($page*$per_page, $totalRows); ?> পর্যন্ত দেখানো হচ্ছে</p>
                            </div>
                            <nav aria-label="Page navigation">
                                <ul class="pagination mb-0">
                                    <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?session_id=<?php echo $session_id; ?>&department_id=<?php echo $department_id; ?>&class_id=<?php echo $class_id; ?>&exam_id=<?php echo $exam_id; ?>&student_id=<?php echo $student_id; ?>&per_page=<?php echo $per_page; ?>&page=<?php echo $page-1; ?>" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    <?php endif; ?>

                                    <?php
                                    // Show limited page numbers with ellipsis
                                    $startPage = max(1, $page - 2);
                                    $endPage = min($totalPages, $page + 2);

                                    // Always show first page
                                    if ($startPage > 1) {
                                        echo '<li class="page-item"><a class="page-link" href="?session_id=' . $session_id . '&department_id=' . $department_id . '&class_id=' . $class_id . '&exam_id=' . $exam_id . '&student_id=' . $student_id . '&per_page=' . $per_page . '&page=1">১</a></li>';
                                        if ($startPage > 2) {
                                            echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                                        }
                                    }

                                    // Display page numbers
                                    for ($i = $startPage; $i <= $endPage; $i++) {
                                        echo '<li class="page-item ' . ($page == $i ? 'active' : '') . '">
                                            <a class="page-link" href="?session_id=' . $session_id . '&department_id=' . $department_id . '&class_id=' . $class_id . '&exam_id=' . $exam_id . '&student_id=' . $student_id . '&per_page=' . $per_page . '&page=' . $i . '">' . $i . '</a>
                                        </li>';
                                    }

                                    // Always show last page
                                    if ($endPage < $totalPages) {
                                        if ($endPage < $totalPages - 1) {
                                            echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                                        }
                                        echo '<li class="page-item"><a class="page-link" href="?session_id=' . $session_id . '&department_id=' . $department_id . '&class_id=' . $class_id . '&exam_id=' . $exam_id . '&student_id=' . $student_id . '&per_page=' . $per_page . '&page=' . $totalPages . '">' . $totalPages . '</a></li>';
                                    }
                                    ?>

                                    <?php if ($page < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?session_id=<?php echo $session_id; ?>&department_id=<?php echo $department_id; ?>&class_id=<?php echo $class_id; ?>&exam_id=<?php echo $exam_id; ?>&student_id=<?php echo $student_id; ?>&per_page=<?php echo $per_page; ?>&page=<?php echo $page+1; ?>" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-submit form when dropdown changes
            document.getElementById('session_id').addEventListener('change', function() {
                document.querySelector('form').submit();
            });

            document.getElementById('department_id').addEventListener('change', function() {
                document.querySelector('form').submit();
            });

            document.getElementById('class_id').addEventListener('change', function() {
                document.querySelector('form').submit();
            });

            document.getElementById('exam_id').addEventListener('change', function() {
                document.querySelector('form').submit();
            });

            document.getElementById('per_page').addEventListener('change', function() {
                document.querySelector('form').submit();
            });
        });
    </script>
</body>
</html>