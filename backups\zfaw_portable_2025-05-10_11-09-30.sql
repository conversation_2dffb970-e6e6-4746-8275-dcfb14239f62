-- Portable Database Backup for zfaw - 2025-05-10 11:09:30
-- This backup can be used on any computer



CREATE TABLE `bkash_payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fee_id` int(11) NOT NULL,
  `payment_id` varchar(100) NOT NULL,
  `trx_id` varchar(100) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status` varchar(50) NOT NULL,
  `payer_reference` varchar(100) DEFAULT NULL,
  `payment_date` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `classes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `class_name` varchar(50) NOT NULL,
  `department_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `department_id` (`department_id`),
  CONSTRAINT `classes_ibfk_1` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `classes` VALUES("1","একাদশ",NULL,"2025-05-04 21:59:04");
INSERT INTO `classes` VALUES("2","দ্বাদশ",NULL,"2025-05-04 21:59:13");




CREATE TABLE `committee_members` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `position` varchar(255) NOT NULL,
  `details` text DEFAULT NULL,
  `photo` varchar(255) DEFAULT NULL,
  `priority` int(11) DEFAULT 100,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `committee_members` VALUES("1","মাননীয় অধ্যক্ষ","সভাপতি","অভিজ্ঞ শিক্ষাবিদ এবং প্রশাসক, ২০১০ সাল থেকে সংস্থার সাথে যুক্ত।",NULL,"1","2025-05-04 22:14:12");
INSERT INTO `committee_members` VALUES("2","মাননীয় সচিব","সদস্য সচিব","অভিজ্ঞ প্রশাসক এবং শিক্ষাবিদ, ২০১২ সাল থেকে সংস্থার সাথে যুক্ত।",NULL,"2","2025-05-04 22:14:12");
INSERT INTO `committee_members` VALUES("3","মোঃ আব্দুল কাদের","সদস্য","বিশিষ্ট ব্যবসায়ী এবং সমাজসেবক, ২০১৪ সাল থেকে সংস্থার সাথে যুক্ত।",NULL,"3","2025-05-04 22:14:12");




CREATE TABLE `department_subject_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `department_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `subject_type` enum('required','optional','fourth') NOT NULL DEFAULT 'optional',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `department_id` (`department_id`,`subject_id`),
  KEY `subject_id` (`subject_id`),
  CONSTRAINT `department_subject_types_ibfk_1` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE CASCADE,
  CONSTRAINT `department_subject_types_ibfk_2` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=281 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `department_subject_types` VALUES("264","5","22","required","2025-05-05 23:03:42");
INSERT INTO `department_subject_types` VALUES("265","5","23","required","2025-05-05 23:03:42");
INSERT INTO `department_subject_types` VALUES("266","5","24","required","2025-05-05 23:03:42");
INSERT INTO `department_subject_types` VALUES("267","5","25","required","2025-05-05 23:03:42");
INSERT INTO `department_subject_types` VALUES("268","5","26","required","2025-05-05 23:03:42");
INSERT INTO `department_subject_types` VALUES("269","5","27","required","2025-05-05 23:03:42");
INSERT INTO `department_subject_types` VALUES("270","5","28","required","2025-05-05 23:03:42");
INSERT INTO `department_subject_types` VALUES("271","5","29","required","2025-05-05 23:03:42");
INSERT INTO `department_subject_types` VALUES("272","5","30","required","2025-05-05 23:03:42");
INSERT INTO `department_subject_types` VALUES("273","5","31","required","2025-05-05 23:03:42");
INSERT INTO `department_subject_types` VALUES("274","5","32","required","2025-05-05 23:03:42");
INSERT INTO `department_subject_types` VALUES("275","5","33","required","2025-05-05 23:03:43");
INSERT INTO `department_subject_types` VALUES("276","5","34","required","2025-05-05 23:03:43");
INSERT INTO `department_subject_types` VALUES("277","5","35","required","2025-05-05 23:03:43");
INSERT INTO `department_subject_types` VALUES("278","5","36","required","2025-05-05 23:03:43");
INSERT INTO `department_subject_types` VALUES("279","5","37","required","2025-05-05 23:03:43");
INSERT INTO `department_subject_types` VALUES("280","5","38","required","2025-05-05 23:03:43");




CREATE TABLE `departments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `department_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `department_code` varchar(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `department_name` (`department_name`),
  UNIQUE KEY `department_code` (`department_code`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `departments` VALUES("1","সাধারন","সাধারন","2025-05-04 18:16:51","সা-০");
INSERT INTO `departments` VALUES("2","বিজ্ঞান","বিজ্ঞান","2025-05-04 21:59:32","বি-১");
INSERT INTO `departments` VALUES("5","মানবিক","মানবিক","2025-05-04 22:05:42","মা-২");
INSERT INTO `departments` VALUES("11","ব্যবসায়","ব্যবসায়","2025-05-06 00:04:38","ব্য-৩");




CREATE TABLE `detailed_results` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `exam_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `cq_marks` decimal(5,2) DEFAULT 0.00,
  `mcq_marks` decimal(5,2) DEFAULT 0.00,
  `practical_marks` decimal(5,2) DEFAULT 0.00,
  `total_obtained` decimal(5,2) DEFAULT 0.00,
  `grade` varchar(5) NOT NULL,
  `is_passed` tinyint(1) DEFAULT 1,
  `cq_passed` tinyint(1) DEFAULT 1,
  `mcq_passed` tinyint(1) DEFAULT 1,
  `practical_passed` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `student_id` (`student_id`,`exam_id`,`subject_id`),
  KEY `idx_student_id` (`student_id`),
  KEY `idx_exam_id` (`exam_id`),
  KEY `idx_subject_id` (`subject_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `detailed_results` VALUES("1","2","3","22","30.00","20.00","0.00","50.00","B","1","1","1","1","2025-05-08 23:58:55","2025-05-08 23:59:59");
INSERT INTO `detailed_results` VALUES("2","1","3","22","30.00","21.00","0.00","51.00","B","1","1","1","1","2025-05-08 23:58:55","2025-05-08 23:59:59");
INSERT INTO `detailed_results` VALUES("3","3","3","22","12.00","23.00","0.00","35.00","F","0","0","1","1","2025-05-08 23:59:59","2025-05-09 00:19:28");
INSERT INTO `detailed_results` VALUES("4","4","3","22","25.00","14.00","0.00","39.00","D","1","1","1","1","2025-05-08 23:59:59","2025-05-08 23:59:59");
INSERT INTO `detailed_results` VALUES("5","4","3","30","41.00","20.00","0.00","61.00","A-","1","1","1","1","2025-05-09 00:07:25","2025-05-09 00:07:25");




CREATE TABLE `exam_departments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `exam_id` int(11) NOT NULL,
  `department_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `exam_id` (`exam_id`,`department_id`),
  KEY `department_id` (`department_id`),
  CONSTRAINT `exam_departments_ibfk_1` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`id`) ON DELETE CASCADE,
  CONSTRAINT `exam_departments_ibfk_2` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `exam_routine` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `exam_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `exam_date` date NOT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `room_no` varchar(50) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_exam_subject_date` (`exam_id`,`subject_id`,`exam_date`)
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `exam_routine` VALUES("20","4","30","2025-05-19","10:00:00","13:00:00","","","2025-05-09 11:59:15","2025-05-09 11:59:15");
INSERT INTO `exam_routine` VALUES("21","4","22","2025-05-20","10:00:00","13:00:00","","","2025-05-09 11:59:15","2025-05-09 11:59:15");
INSERT INTO `exam_routine` VALUES("22","4","26","2025-05-19","10:00:00","13:00:00","","","2025-05-09 11:59:15","2025-05-09 11:59:15");
INSERT INTO `exam_routine` VALUES("23","4","31","2025-05-19","10:00:00","13:00:00","","","2025-05-09 11:59:15","2025-05-09 11:59:15");
INSERT INTO `exam_routine` VALUES("24","4","24","2025-05-26","10:00:00","13:00:00","","","2025-05-09 11:59:15","2025-05-09 11:59:15");
INSERT INTO `exam_routine` VALUES("25","4","29","2025-05-26","10:00:00","13:00:00","","","2025-05-09 11:59:15","2025-05-09 11:59:15");
INSERT INTO `exam_routine` VALUES("26","4","40","2025-05-28","10:00:00","13:00:00","","","2025-05-09 11:59:15","2025-05-09 11:59:15");
INSERT INTO `exam_routine` VALUES("27","4","34","2025-05-21","10:00:00","13:00:00","","","2025-05-09 11:59:15","2025-05-09 11:59:15");
INSERT INTO `exam_routine` VALUES("28","4","28","2025-05-26","10:00:00","13:00:00","","","2025-05-09 11:59:15","2025-05-09 11:59:15");
INSERT INTO `exam_routine` VALUES("29","4","36","2025-05-30","10:00:00","13:00:00","","","2025-05-09 11:59:15","2025-05-09 11:59:15");
INSERT INTO `exam_routine` VALUES("30","4","35","2025-05-27","10:00:00","13:00:00","","","2025-05-09 11:59:15","2025-05-09 11:59:15");
INSERT INTO `exam_routine` VALUES("31","4","37","2025-05-22","10:00:00","13:00:00","","","2025-05-09 11:59:15","2025-05-09 11:59:15");
INSERT INTO `exam_routine` VALUES("32","4","39","2025-05-29","10:00:00","13:00:00","","","2025-05-09 11:59:15","2025-05-09 11:59:15");
INSERT INTO `exam_routine` VALUES("33","4","25","2025-05-22","10:00:00","13:00:00","","","2025-05-09 11:59:15","2025-05-09 11:59:15");
INSERT INTO `exam_routine` VALUES("34","4","23","2025-05-23","10:00:00","13:00:00","","","2025-05-09 11:59:15","2025-05-09 11:59:15");
INSERT INTO `exam_routine` VALUES("35","4","33","2025-05-23","10:00:00","13:00:00","","","2025-05-09 11:59:15","2025-05-09 11:59:15");
INSERT INTO `exam_routine` VALUES("36","4","32","2025-05-23","10:00:00","13:00:00","","","2025-05-09 11:59:15","2025-05-09 11:59:15");
INSERT INTO `exam_routine` VALUES("37","4","27","2025-05-28","10:00:00","13:00:00","","","2025-05-09 11:59:15","2025-05-09 11:59:15");
INSERT INTO `exam_routine` VALUES("38","4","38","2025-05-22","10:00:00","13:00:00","","","2025-05-09 11:59:15","2025-05-09 11:59:15");




CREATE TABLE `exam_subject_relations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `exam_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `total_marks` int(11) DEFAULT 100,
  `passing_marks` int(11) DEFAULT 33,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `exam_id` (`exam_id`,`subject_id`),
  KEY `subject_id` (`subject_id`),
  CONSTRAINT `exam_subject_relations_ibfk_1` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`id`) ON DELETE CASCADE,
  CONSTRAINT `exam_subject_relations_ibfk_2` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=75 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `exam_subject_relations` VALUES("56","4","30","100","33","2025-05-09 11:18:41");
INSERT INTO `exam_subject_relations` VALUES("57","4","22","100","33","2025-05-09 11:18:41");
INSERT INTO `exam_subject_relations` VALUES("58","4","26","100","33","2025-05-09 11:18:41");
INSERT INTO `exam_subject_relations` VALUES("59","4","31","100","33","2025-05-09 11:18:41");
INSERT INTO `exam_subject_relations` VALUES("60","4","24","100","33","2025-05-09 11:18:41");
INSERT INTO `exam_subject_relations` VALUES("61","4","29","100","33","2025-05-09 11:18:41");
INSERT INTO `exam_subject_relations` VALUES("62","4","40","100","33","2025-05-09 11:18:41");
INSERT INTO `exam_subject_relations` VALUES("63","4","34","100","33","2025-05-09 11:18:41");
INSERT INTO `exam_subject_relations` VALUES("64","4","28","100","33","2025-05-09 11:18:41");
INSERT INTO `exam_subject_relations` VALUES("65","4","36","100","33","2025-05-09 11:18:41");
INSERT INTO `exam_subject_relations` VALUES("66","4","35","100","33","2025-05-09 11:18:41");
INSERT INTO `exam_subject_relations` VALUES("67","4","37","100","33","2025-05-09 11:18:41");
INSERT INTO `exam_subject_relations` VALUES("68","4","39","100","33","2025-05-09 11:18:41");
INSERT INTO `exam_subject_relations` VALUES("69","4","25","100","33","2025-05-09 11:18:41");
INSERT INTO `exam_subject_relations` VALUES("70","4","23","100","33","2025-05-09 11:18:41");
INSERT INTO `exam_subject_relations` VALUES("71","4","33","100","33","2025-05-09 11:18:41");
INSERT INTO `exam_subject_relations` VALUES("72","4","32","100","33","2025-05-09 11:18:41");
INSERT INTO `exam_subject_relations` VALUES("73","4","27","100","33","2025-05-09 11:18:41");
INSERT INTO `exam_subject_relations` VALUES("74","4","38","100","33","2025-05-09 11:18:41");




CREATE TABLE `exam_subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `exam_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `total_marks` int(11) NOT NULL DEFAULT 100,
  `passing_marks` int(11) DEFAULT 33,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `exam_id` (`exam_id`,`subject_id`),
  KEY `subject_id` (`subject_id`),
  CONSTRAINT `exam_subjects_ibfk_1` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`id`) ON DELETE CASCADE,
  CONSTRAINT `exam_subjects_ibfk_2` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `exam_type_subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `exam_type_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `class_id` int(11) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `exam_type_id` (`exam_type_id`,`subject_id`,`class_id`,`department_id`),
  KEY `subject_id` (`subject_id`),
  KEY `class_id` (`class_id`),
  KEY `department_id` (`department_id`),
  CONSTRAINT `exam_type_subjects_ibfk_1` FOREIGN KEY (`exam_type_id`) REFERENCES `exam_types` (`id`) ON DELETE CASCADE,
  CONSTRAINT `exam_type_subjects_ibfk_2` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE,
  CONSTRAINT `exam_type_subjects_ibfk_3` FOREIGN KEY (`class_id`) REFERENCES `classes` (`id`) ON DELETE SET NULL,
  CONSTRAINT `exam_type_subjects_ibfk_4` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `exam_type_subjects` VALUES("1","2","22","1",NULL,"2025-05-09 09:53:00");
INSERT INTO `exam_type_subjects` VALUES("2","2","23","1",NULL,"2025-05-09 09:53:00");
INSERT INTO `exam_type_subjects` VALUES("3","2","24","1",NULL,"2025-05-09 09:53:00");
INSERT INTO `exam_type_subjects` VALUES("4","2","25","1",NULL,"2025-05-09 09:53:00");
INSERT INTO `exam_type_subjects` VALUES("5","2","26","1",NULL,"2025-05-09 09:53:00");
INSERT INTO `exam_type_subjects` VALUES("6","2","27","1",NULL,"2025-05-09 09:53:00");
INSERT INTO `exam_type_subjects` VALUES("7","2","28","1",NULL,"2025-05-09 09:53:00");
INSERT INTO `exam_type_subjects` VALUES("8","2","29","1",NULL,"2025-05-09 09:53:00");
INSERT INTO `exam_type_subjects` VALUES("9","2","30","1",NULL,"2025-05-09 09:53:00");
INSERT INTO `exam_type_subjects` VALUES("10","2","31","1",NULL,"2025-05-09 09:53:00");
INSERT INTO `exam_type_subjects` VALUES("11","2","32","1",NULL,"2025-05-09 09:53:00");
INSERT INTO `exam_type_subjects` VALUES("12","2","33","1",NULL,"2025-05-09 09:53:00");
INSERT INTO `exam_type_subjects` VALUES("13","2","34","1",NULL,"2025-05-09 09:53:00");
INSERT INTO `exam_type_subjects` VALUES("14","2","35","1",NULL,"2025-05-09 09:53:00");
INSERT INTO `exam_type_subjects` VALUES("15","2","36","1",NULL,"2025-05-09 09:53:00");
INSERT INTO `exam_type_subjects` VALUES("16","2","37","1",NULL,"2025-05-09 09:53:00");
INSERT INTO `exam_type_subjects` VALUES("17","2","38","1",NULL,"2025-05-09 09:53:00");
INSERT INTO `exam_type_subjects` VALUES("18","2","39","1",NULL,"2025-05-09 09:53:00");
INSERT INTO `exam_type_subjects` VALUES("19","2","40","1",NULL,"2025-05-09 09:53:00");




CREATE TABLE `exam_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `exam_types` VALUES("1","অর্ধ-বার্ষিক পরীক্ষা","শিক্ষাবর্ষের মধ্যে অনুষ্ঠিত পরীক্ষা","1","2025-05-08 22:51:42");
INSERT INTO `exam_types` VALUES("2","বার্ষিক পরীক্ষা","শিক্ষাবর্ষের শেষে অনুষ্ঠিত পরীক্ষা","1","2025-05-08 22:51:42");
INSERT INTO `exam_types` VALUES("3","নির্বাচনী পরীক্ষা","পাবলিক পরীক্ষার প্রস্তুতি হিসেবে অনুষ্ঠিত পরীক্ষা","1","2025-05-08 22:51:42");
INSERT INTO `exam_types` VALUES("4","প্রাক-নির্বাচনী পরীক্ষা","নির্বাচনী পরীক্ষার আগে অনুষ্ঠিত পরীক্ষা","1","2025-05-08 22:51:42");




CREATE TABLE `exams` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `exam_name` varchar(255) NOT NULL,
  `exam_type` varchar(50) DEFAULT NULL,
  `subject_id` int(11) DEFAULT NULL,
  `class_id` int(11) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `session_id` int(11) DEFAULT NULL,
  `exam_date` date NOT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `total_marks` int(11) DEFAULT 100,
  `passing_marks` int(11) DEFAULT 33,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `exams` VALUES("4","বার্ষিক পরীক্ষা","বার্ষিক পরীক্ষা","30","1",NULL,"1","2025-05-19","10:00:00","13:00:00","100","33","1","2025-05-09 10:30:18","2025-05-09 10:42:37","1");




CREATE TABLE `fee_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `fee_categories` VALUES("1","মাসিক ফি","মাসিক বেতন","2025-05-07 07:44:22","2025-05-07 08:22:26");
INSERT INTO `fee_categories` VALUES("2","অর্ধ-বার্ষিক ফি","অর্ধ-বার্ষিক পরীক্ষার ফি","2025-05-07 07:44:22","2025-05-07 08:23:13");
INSERT INTO `fee_categories` VALUES("3","ভর্তি ফি","ভর্তি ফি","2025-05-07 07:44:22","2025-05-07 07:44:22");
INSERT INTO `fee_categories` VALUES("4","সেশন ফি","সেশন ফি","2025-05-07 07:44:22","2025-05-07 07:44:22");
INSERT INTO `fee_categories` VALUES("5","অন্যান্য ফি","অন্যান্য ফি","2025-05-07 07:44:22","2025-05-07 07:44:22");
INSERT INTO `fee_categories` VALUES("6","বার্ষিক ফি","বার্ষিক পরীক্ষার ফি","2025-05-07 08:24:11","2025-05-07 08:24:11");




CREATE TABLE `fee_map_class` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fee_type_id` int(11) NOT NULL,
  `class_id` int(11) NOT NULL,
  `academic_year` varchar(20) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `fee_type_id` (`fee_type_id`),
  KEY `class_id` (`class_id`),
  CONSTRAINT `fee_map_class_ibfk_1` FOREIGN KEY (`fee_type_id`) REFERENCES `fee_types` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fee_map_class_ibfk_2` FOREIGN KEY (`class_id`) REFERENCES `classes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `fee_map_session` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fee_type_id` int(11) NOT NULL,
  `session_name` varchar(50) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `fee_type_id` (`fee_type_id`),
  CONSTRAINT `fee_map_session_ibfk_1` FOREIGN KEY (`fee_type_id`) REFERENCES `fee_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `fee_map_student` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fee_type_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `fee_type_id` (`fee_type_id`),
  KEY `student_id` (`student_id`),
  CONSTRAINT `fee_map_student_ibfk_1` FOREIGN KEY (`fee_type_id`) REFERENCES `fee_types` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fee_map_student_ibfk_2` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `fee_payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `fee_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_date` date NOT NULL,
  `payment_method` varchar(50) NOT NULL,
  `receipt_no` varchar(50) DEFAULT NULL,
  `transaction_id` varchar(100) DEFAULT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'Pending',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `payment_amount` decimal(10,2) GENERATED ALWAYS AS (`amount`) STORED,
  PRIMARY KEY (`id`),
  KEY `fee_id` (`fee_id`),
  CONSTRAINT `fee_payments_ibfk_1` FOREIGN KEY (`fee_id`) REFERENCES `fees` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `fee_payments` VALUES("1","0","36","450.00","2025-05-07","cash","",NULL,"Pending","","2025-05-07 14:03:09","2025-05-07 14:03:09","450.00");
INSERT INTO `fee_payments` VALUES("2","0","37","300.00","2025-05-07","cash","",NULL,"Pending","","2025-05-07 21:02:27","2025-05-07 21:02:27","300.00");
INSERT INTO `fee_payments` VALUES("3","0","41","150.00","2025-05-07","bkash","",NULL,"Pending","","2025-05-07 21:07:52","2025-05-07 21:07:52","150.00");
INSERT INTO `fee_payments` VALUES("4","0","38","450.00","2025-05-08","cash","RCPT-20250508-7546",NULL,"Pending","","2025-05-08 06:43:45","2025-05-08 06:43:45","450.00");
INSERT INTO `fee_payments` VALUES("5","0","42","150.00","2025-05-08","cash","RCPT-20250508-7546",NULL,"Pending","","2025-05-08 06:43:45","2025-05-08 06:43:45","150.00");




CREATE TABLE `fee_sms_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `student_name` varchar(100) NOT NULL,
  `student_phone` varchar(20) NOT NULL,
  `fee_amount` decimal(10,2) NOT NULL,
  `message` text NOT NULL,
  `status` varchar(20) NOT NULL,
  `sent_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `fee_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_recurring` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `amount` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `fee_types` VALUES("1","অর্ধ বার্ষিক পরীক্ষা","অর্ধ বার্ষিক পরীক্ষা","0","2025-05-07 08:48:27","450.00");
INSERT INTO `fee_types` VALUES("2","বার্ষিক পরীক্ষা ফিস","বার্ষিক পরীক্ষা ফিস","0","2025-05-07 08:49:26","450.00");
INSERT INTO `fee_types` VALUES("3","মাসিক বেতন","মাসিক বেতন","1","2025-05-07 21:06:42","150.00");




CREATE TABLE `fees` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `fee_type` varchar(100) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `class_id` int(11) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `session_id` int(11) DEFAULT NULL,
  `is_recurring` tinyint(1) DEFAULT 0,
  `recurring_interval` varchar(20) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `paid` decimal(10,2) DEFAULT 0.00,
  `due_date` date NOT NULL,
  `payment_status` enum('due','partial','paid','overpaid') DEFAULT 'due',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `notes` text DEFAULT NULL,
  `payment_date` date DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  KEY `student_id` (`student_id`),
  CONSTRAINT `fees_ibfk_2` FOREIGN KEY (`category_id`) REFERENCES `fee_categories` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fees_ibfk_3` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=44 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `fees` VALUES("36","3","1","","বার্ষিক পরীক্ষা ফিস","450.00",NULL,NULL,NULL,"0",NULL,NULL,"450.00","2025-05-07","paid","2025-05-07 14:01:24","2025-05-07 14:03:09",NULL,"2025-05-07");
INSERT INTO `fees` VALUES("37","4","1","","বার্ষিক পরীক্ষা ফিস","450.00",NULL,NULL,NULL,"0",NULL,NULL,"300.00","2025-05-07","partial","2025-05-07 14:01:24","2025-05-07 21:02:27",NULL,"2025-05-07");
INSERT INTO `fees` VALUES("38","2","1","","বার্ষিক পরীক্ষা ফিস","450.00",NULL,NULL,NULL,"0",NULL,NULL,"450.00","2025-05-07","paid","2025-05-07 14:01:24","2025-05-08 06:43:45",NULL,"2025-05-08");
INSERT INTO `fees` VALUES("39","1","1","","বার্ষিক পরীক্ষা ফিস","450.00",NULL,NULL,NULL,"0",NULL,NULL,"0.00","2025-05-07","due","2025-05-07 14:01:24","2025-05-07 14:01:24",NULL,NULL);
INSERT INTO `fees` VALUES("40","3","1","","মাসিক বেতন","150.00",NULL,NULL,NULL,"0",NULL,NULL,"0.00","2025-05-07","due","2025-05-07 21:07:27","2025-05-07 21:07:27",NULL,NULL);
INSERT INTO `fees` VALUES("41","4","1","","মাসিক বেতন","150.00",NULL,NULL,NULL,"0",NULL,NULL,"150.00","2025-05-07","paid","2025-05-07 21:07:27","2025-05-07 21:07:52",NULL,"2025-05-07");
INSERT INTO `fees` VALUES("42","2","1","","মাসিক বেতন","150.00",NULL,NULL,NULL,"0",NULL,NULL,"150.00","2025-05-07","paid","2025-05-07 21:07:27","2025-05-08 06:43:45",NULL,"2025-05-08");
INSERT INTO `fees` VALUES("43","1","1","","মাসিক বেতন","150.00",NULL,NULL,NULL,"0",NULL,NULL,"0.00","2025-05-07","due","2025-05-07 21:07:27","2025-05-07 21:07:27",NULL,NULL);




CREATE TABLE `governing_board_members` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `position` varchar(100) NOT NULL,
  `address` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `photo` varchar(255) DEFAULT NULL,
  `bio` text DEFAULT NULL,
  `display_order` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





CREATE TABLE `groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_name` varchar(255) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `groups` VALUES("1","বিজ্ঞান","1","2025-05-05 07:10:43");
INSERT INTO `groups` VALUES("2","মানবিক","1","2025-05-05 07:10:43");
INSERT INTO `groups` VALUES("11","ব্যবসায়","1","2025-05-05 19:48:24");
INSERT INTO `groups` VALUES("12","সাধারণ","1","2025-05-05 19:48:24");




CREATE TABLE `notices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `date` date NOT NULL,
  `added_by` varchar(50) DEFAULT NULL,
  `attachment_path` varchar(255) DEFAULT NULL,
  `attachment_type` varchar(10) DEFAULT NULL,
  `target_audience` enum('all','students','teachers','staff') NOT NULL DEFAULT 'all',
  `expiry_date` date NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `notices` VALUES("1","স্বাগতম আমাদের কলেজ ম্যানেজমেন্ট সিস্টেমে","এই সিস্টেমটি ব্যবহার করে আপনি সহজেই আপনার কলেজের সকল কার্যক্রম পরিচালনা করতে পারবেন।","2025-05-04",NULL,NULL,NULL,"all","2025-06-03","2025-05-04 18:31:41","2025-05-04 18:31:41");




CREATE TABLE `passing_marks_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` int(11) DEFAULT NULL,
  `min_percentage` decimal(5,2) NOT NULL,
  `max_percentage` decimal(5,2) NOT NULL,
  `passing_mark` decimal(5,2) NOT NULL,
  `cq_passing_percent` decimal(5,2) NOT NULL DEFAULT 33.00,
  `mcq_passing_percent` decimal(5,2) NOT NULL DEFAULT 33.00,
  `practical_passing_percent` decimal(5,2) NOT NULL DEFAULT 33.00,
  `grade` varchar(5) NOT NULL,
  `grade_point` decimal(3,2) NOT NULL,
  `description` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `subject_id` (`subject_id`,`min_percentage`,`max_percentage`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `passing_marks_config` VALUES("1",NULL,"80.00","100.00","80.00","40.00","40.00","40.00","A+","5.00","শ্রেষ্ঠত্ব (Excellence)","2025-05-04 22:13:55","2025-05-04 22:13:55");
INSERT INTO `passing_marks_config` VALUES("2",NULL,"70.00","79.99","70.00","35.00","35.00","35.00","A","4.00","অতি উত্তম (Very Good)","2025-05-04 22:13:55","2025-05-04 22:13:55");
INSERT INTO `passing_marks_config` VALUES("3",NULL,"60.00","69.99","60.00","33.00","33.00","33.00","A-","3.50","উত্তম (Good)","2025-05-04 22:13:55","2025-05-04 22:13:55");
INSERT INTO `passing_marks_config` VALUES("4",NULL,"50.00","59.99","50.00","33.00","33.00","33.00","B","3.00","ভালো (Satisfactory)","2025-05-04 22:13:55","2025-05-04 22:13:55");
INSERT INTO `passing_marks_config` VALUES("5",NULL,"40.00","49.99","40.00","33.00","33.00","33.00","C","2.00","মোটামুটি (Average)","2025-05-04 22:13:55","2025-05-04 22:13:55");
INSERT INTO `passing_marks_config` VALUES("6",NULL,"33.00","39.99","33.00","33.00","33.00","33.00","D","1.00","নিম্নমান (Poor)","2025-05-04 22:13:55","2025-05-04 22:13:55");
INSERT INTO `passing_marks_config` VALUES("7",NULL,"0.00","32.99","0.00","0.00","0.00","0.00","F","0.00","অকৃতকার্য (Fail)","2025-05-04 22:13:55","2025-05-04 22:13:55");




CREATE TABLE `payment_receipts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `payment_date` date NOT NULL,
  `payment_method` varchar(50) DEFAULT 'cash',
  `total_amount` decimal(10,2) NOT NULL,
  `receipt_no` varchar(50) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `student_id` (`student_id`),
  CONSTRAINT `payment_receipts_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `results` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `subject_id` int(11) DEFAULT NULL,
  `exam_id` int(11) NOT NULL,
  `marks_obtained` float NOT NULL,
  `total_marks` float NOT NULL DEFAULT 100,
  `grade` varchar(5) NOT NULL,
  `date` date DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `cq_marks` decimal(10,2) DEFAULT 0.00,
  `mcq_marks` decimal(10,2) DEFAULT 0.00,
  `practical_marks` decimal(10,2) DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_student_id` (`student_id`),
  KEY `idx_exam_id` (`exam_id`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `results` VALUES("1","4","30","4","50","100","B","2025-05-10","1","","40.00","10.00","0.00","2025-05-10 06:25:39","2025-05-10 06:31:43");
INSERT INTO `results` VALUES("2","1","22","4","65","100","A-","2025-05-10","1","","40.00","25.00","0.00","2025-05-10 06:32:41","2025-05-10 06:32:41");
INSERT INTO `results` VALUES("3","4","22","4","38","100","D","2025-05-10","1","","23.00","15.00","0.00","2025-05-10 06:32:41","2025-05-10 06:32:41");
INSERT INTO `results` VALUES("4","2","22","4","32","100","F","2025-05-10","1","","22.00","10.00","0.00","2025-05-10 06:32:41","2025-05-10 06:32:41");
INSERT INTO `results` VALUES("5","3","22","4","54","100","B","2025-05-10","1","","33.00","21.00","0.00","2025-05-10 06:32:41","2025-05-10 06:32:41");
INSERT INTO `results` VALUES("6","3","26","4","84","100","A+","2025-05-10","1","","44.00","20.00","20.00","2025-05-10 06:33:00","2025-05-10 06:33:00");
INSERT INTO `results` VALUES("7","4","31","4","32","100","F","2025-05-10","1","","22.00","10.00","0.00","2025-05-10 06:33:24","2025-05-10 06:33:24");
INSERT INTO `results` VALUES("8","3","24","4","68","100","A-","2025-05-10","1","","33.00","15.00","20.00","2025-05-10 06:33:45","2025-05-10 06:33:45");
INSERT INTO `results` VALUES("9","1","29","4","50","100","B","2025-05-10","1","","25.00","25.00","0.00","2025-05-10 06:34:05","2025-05-10 06:34:05");
INSERT INTO `results` VALUES("10","2","29","4","63","100","A-","2025-05-10","1","","43.00","20.00","0.00","2025-05-10 06:34:05","2025-05-10 06:34:05");
INSERT INTO `results` VALUES("11","1","34","4","33","100","D","2025-05-10","1","","33.00","0.00","0.00","2025-05-10 06:37:28","2025-05-10 06:37:28");
INSERT INTO `results` VALUES("12","4","34","4","50","100","B","2025-05-10","1","","50.00","0.00","0.00","2025-05-10 06:37:28","2025-05-10 06:37:28");
INSERT INTO `results` VALUES("13","2","34","4","26","100","F","2025-05-10","1","","26.00","0.00","0.00","2025-05-10 06:37:28","2025-05-10 06:37:28");
INSERT INTO `results` VALUES("14","3","34","4","40","100","C","2025-05-10","1","","40.00","0.00","0.00","2025-05-10 06:37:28","2025-05-10 06:37:28");
INSERT INTO `results` VALUES("15","4","28","4","69","100","A-","2025-05-10","1","","54.00","15.00","0.00","2025-05-10 06:37:43","2025-05-10 06:37:43");
INSERT INTO `results` VALUES("16","1","35","4","53","100","B","2025-05-10","1","","21.00","12.00","20.00","2025-05-10 06:38:30","2025-05-10 06:38:30");
INSERT INTO `results` VALUES("17","4","35","4","67","100","A-","2025-05-10","1","","25.00","22.00","20.00","2025-05-10 06:38:30","2025-05-10 06:38:30");
INSERT INTO `results` VALUES("18","2","35","4","60","100","A-","2025-05-10","1","","35.00","15.00","10.00","2025-05-10 06:38:30","2025-05-10 06:38:30");
INSERT INTO `results` VALUES("19","3","35","4","65","100","A-","2025-05-10","1","","23.00","20.00","22.00","2025-05-10 06:38:30","2025-05-10 06:38:30");
INSERT INTO `results` VALUES("20","1","37","4","49","100","C","2025-05-10","1","","29.00","20.00","0.00","2025-05-10 06:38:56","2025-05-10 06:38:56");
INSERT INTO `results` VALUES("21","2","37","4","59","100","B","2025-05-10","1","","35.00","24.00","0.00","2025-05-10 06:38:56","2025-05-10 06:38:56");
INSERT INTO `results` VALUES("22","3","25","4","82","100","A+","2025-05-10","1","","50.00","12.00","20.00","2025-05-10 06:39:21","2025-05-10 06:39:21");
INSERT INTO `results` VALUES("23","3","23","4","64","100","A-","2025-05-10","1","","29.00","15.00","20.00","2025-05-10 06:39:37","2025-05-10 06:39:37");
INSERT INTO `results` VALUES("24","1","32","4","58","100","B","2025-05-10","1","","43.00","15.00","0.00","2025-05-10 06:40:09","2025-05-10 06:40:09");
INSERT INTO `results` VALUES("25","2","32","4","72","100","A","2025-05-10","1","","51.00","21.00","0.00","2025-05-10 06:40:09","2025-05-10 06:40:09");
INSERT INTO `results` VALUES("26","4","27","4","83","100","A+","2025-05-10","1","","42.00","21.00","20.00","2025-05-10 06:40:25","2025-05-10 06:40:25");
INSERT INTO `results` VALUES("27","1","38","4","74","100","A","2025-05-10","1","","54.00","20.00","0.00","2025-05-10 06:40:53","2025-05-10 06:40:53");
INSERT INTO `results` VALUES("28","2","38","4","82","100","A+","2025-05-10","1","","60.00","22.00","0.00","2025-05-10 06:40:53","2025-05-10 06:40:53");




CREATE TABLE `school_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `school_name` varchar(255) NOT NULL,
  `school_address` text DEFAULT NULL,
  `school_phone` varchar(50) DEFAULT NULL,
  `school_email` varchar(100) DEFAULT NULL,
  `logo_path` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `school_settings` VALUES("1","","","2025-05-07 06:06:41","2025-05-07 06:34:52","Default School Name","Default School Address","************","<EMAIL>","uploads/logos/1746578092_logo1.jpg");




CREATE TABLE `sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_name` varchar(50) NOT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `is_current` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `session_name` (`session_name`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `sessions` VALUES("1","2025-26","2025-05-01","2026-05-12","0","2025-05-04 22:08:26");
INSERT INTO `sessions` VALUES("2","2026-27","2026-05-04","2027-12-31","0","2025-05-04 22:09:14");




CREATE TABLE `staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` varchar(20) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `gender` enum('male','female','other') NOT NULL,
  `dob` date DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(50) DEFAULT NULL,
  `state` varchar(50) DEFAULT NULL,
  `postal_code` varchar(20) DEFAULT NULL,
  `country` varchar(50) DEFAULT 'Bangladesh',
  `joining_date` date DEFAULT NULL,
  `profile_photo` varchar(255) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `designation` varchar(100) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `role` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `staff_id` (`staff_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `staff` VALUES("1","STF-001","Jamal","Hossain","<EMAIL>","01912345678","male",NULL,NULL,NULL,NULL,NULL,"Bangladesh","2025-05-04",NULL,"1","Office Assistant",NULL,"2025-05-04 18:23:13",NULL);




CREATE TABLE `student_subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `category` varchar(20) NOT NULL DEFAULT 'optional',
  `session_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=55 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `student_subjects` VALUES("14","3","22","required","2","2025-05-05 23:37:49");
INSERT INTO `student_subjects` VALUES("15","3","24","required","2","2025-05-05 23:37:49");
INSERT INTO `student_subjects` VALUES("16","3","34","required","2","2025-05-05 23:37:49");
INSERT INTO `student_subjects` VALUES("17","3","35","required","2","2025-05-05 23:37:49");
INSERT INTO `student_subjects` VALUES("18","3","23","required","2","2025-05-05 23:37:49");
INSERT INTO `student_subjects` VALUES("19","3","26","optional","2","2025-05-05 23:37:49");
INSERT INTO `student_subjects` VALUES("20","3","25","fourth","2","2025-05-05 23:37:49");
INSERT INTO `student_subjects` VALUES("34","1","22","required","2","2025-05-06 17:39:45");
INSERT INTO `student_subjects` VALUES("35","1","34","required","2","2025-05-06 17:39:45");
INSERT INTO `student_subjects` VALUES("36","1","35","required","2","2025-05-06 17:39:45");
INSERT INTO `student_subjects` VALUES("37","1","29","optional","2","2025-05-06 17:39:45");
INSERT INTO `student_subjects` VALUES("38","1","37","optional","2","2025-05-06 17:39:45");
INSERT INTO `student_subjects` VALUES("39","1","32","optional","2","2025-05-06 17:39:45");
INSERT INTO `student_subjects` VALUES("40","1","38","fourth","2","2025-05-06 17:39:45");
INSERT INTO `student_subjects` VALUES("41","4","30","required","2","2025-05-08 05:59:41");
INSERT INTO `student_subjects` VALUES("42","4","22","required","2","2025-05-08 05:59:41");
INSERT INTO `student_subjects` VALUES("43","4","34","required","2","2025-05-08 05:59:41");
INSERT INTO `student_subjects` VALUES("44","4","28","required","2","2025-05-08 05:59:41");
INSERT INTO `student_subjects` VALUES("45","4","35","required","2","2025-05-08 05:59:41");
INSERT INTO `student_subjects` VALUES("46","4","31","optional","2","2025-05-08 05:59:41");
INSERT INTO `student_subjects` VALUES("47","4","27","fourth","2","2025-05-08 05:59:41");
INSERT INTO `student_subjects` VALUES("48","2","22","required","2","2025-05-08 06:00:20");
INSERT INTO `student_subjects` VALUES("49","2","34","required","2","2025-05-08 06:00:20");
INSERT INTO `student_subjects` VALUES("50","2","35","required","2","2025-05-08 06:00:21");
INSERT INTO `student_subjects` VALUES("51","2","29","optional","2","2025-05-08 06:00:21");
INSERT INTO `student_subjects` VALUES("52","2","37","optional","2","2025-05-08 06:00:21");
INSERT INTO `student_subjects` VALUES("53","2","32","optional","2","2025-05-08 06:00:21");
INSERT INTO `student_subjects` VALUES("54","2","38","fourth","2","2025-05-08 06:00:21");




CREATE TABLE `students` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` varchar(20) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `gender` enum('male','female','other') NOT NULL,
  `dob` date DEFAULT NULL,
  `address` text DEFAULT NULL,
  `class_id` int(11) DEFAULT NULL,
  `session_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `admission_date` date DEFAULT NULL,
  `profile_photo` varchar(255) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `batch` varchar(20) DEFAULT NULL,
  `group_name` varchar(50) DEFAULT NULL,
  `role` varchar(50) DEFAULT NULL,
  `roll_number` varchar(20) DEFAULT NULL,
  `guardian_name` varchar(100) DEFAULT NULL,
  `guardian_relation` varchar(50) DEFAULT NULL,
  `guardian_phone` varchar(20) DEFAULT NULL,
  `guardian_email` varchar(100) DEFAULT NULL,
  `guardian_address` text DEFAULT NULL,
  `guardian_occupation` varchar(100) DEFAULT NULL,
  `father_name` varchar(100) DEFAULT NULL,
  `father_phone` varchar(20) DEFAULT NULL,
  `father_email` varchar(100) DEFAULT NULL,
  `father_occupation` varchar(100) DEFAULT NULL,
  `father_income` varchar(50) DEFAULT NULL,
  `mother_name` varchar(100) DEFAULT NULL,
  `mother_phone` varchar(20) DEFAULT NULL,
  `mother_email` varchar(100) DEFAULT NULL,
  `mother_occupation` varchar(100) DEFAULT NULL,
  `mother_income` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `student_id` (`student_id`),
  KEY `class_id` (`class_id`),
  KEY `session_id` (`session_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `students_ibfk_1` FOREIGN KEY (`class_id`) REFERENCES `classes` (`id`) ON DELETE SET NULL,
  CONSTRAINT `students_ibfk_2` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`id`) ON DELETE SET NULL,
  CONSTRAINT `students_ibfk_3` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `students` VALUES("1","STD-601523","Tanvir","Rahman",NULL,"01711000111","male","2000-05-05","Damurhuda, Chuadanga","1","1","5","2025-05-05 09:20:27","2025-05-05","uploads/profile_photos/68182e7bcc166.jpg","5","2526",NULL,"Regular Student","252601",NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO `students` VALUES("2","STD-443687","Noyon","Tara","<EMAIL>","01977861762","female","1999-05-05","Chuadanga","1","1","6","2025-05-05 09:22:06","0000-00-00","uploads/students/1746853436_3 abu hasem.jpg","5","2526",NULL,"regular","252602",NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO `students` VALUES("3","STD-310268","ALI","RAHMAN","<EMAIL>","01717861762","male","2005-05-05","Chuadanga","1","1","7","2025-05-05 23:36:31","2025-05-05","uploads/profile_photos/6818f71f51b29.jpg","2","2025",NULL,"Regular Student","252604",NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO `students` VALUES("4","STD-833027","KUMARI","RANI","<EMAIL>","01711000111","female","2004-05-05","চুয়াডাঙ্গা বাংলাদেশ","1","1","8","2025-05-05 23:40:57","0000-00-00","uploads/students/1746853388_ico.jpeg","11","2526",NULL,"","252602",NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);




CREATE TABLE `subject_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `category_name` (`category_name`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `subject_categories` VALUES("1","required","আবশ্যিক বিষয়সমূহ","2025-05-04 20:58:37");
INSERT INTO `subject_categories` VALUES("2","optional","ঐচ্ছিক বিষয়সমূহ","2025-05-04 20:58:37");
INSERT INTO `subject_categories` VALUES("3","fourth","৪র্থ বিষয়","2025-05-04 20:58:37");




CREATE TABLE `subject_departments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` int(11) NOT NULL,
  `department_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `subject_id` (`subject_id`,`department_id`)
) ENGINE=InnoDB AUTO_INCREMENT=199 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `subject_departments` VALUES("1","1","1");
INSERT INTO `subject_departments` VALUES("62","2","1");
INSERT INTO `subject_departments` VALUES("2","2","2");
INSERT INTO `subject_departments` VALUES("42","2","5");
INSERT INTO `subject_departments` VALUES("22","2","6");
INSERT INTO `subject_departments` VALUES("63","3","1");
INSERT INTO `subject_departments` VALUES("3","3","2");
INSERT INTO `subject_departments` VALUES("43","3","5");
INSERT INTO `subject_departments` VALUES("23","3","6");
INSERT INTO `subject_departments` VALUES("64","4","1");
INSERT INTO `subject_departments` VALUES("4","4","2");
INSERT INTO `subject_departments` VALUES("44","4","5");
INSERT INTO `subject_departments` VALUES("24","4","6");
INSERT INTO `subject_departments` VALUES("65","5","1");
INSERT INTO `subject_departments` VALUES("5","5","2");
INSERT INTO `subject_departments` VALUES("45","5","5");
INSERT INTO `subject_departments` VALUES("25","5","6");
INSERT INTO `subject_departments` VALUES("66","6","1");
INSERT INTO `subject_departments` VALUES("6","6","2");
INSERT INTO `subject_departments` VALUES("46","6","5");
INSERT INTO `subject_departments` VALUES("26","6","6");
INSERT INTO `subject_departments` VALUES("67","7","1");
INSERT INTO `subject_departments` VALUES("7","7","2");
INSERT INTO `subject_departments` VALUES("47","7","5");
INSERT INTO `subject_departments` VALUES("27","7","6");
INSERT INTO `subject_departments` VALUES("68","8","1");
INSERT INTO `subject_departments` VALUES("8","8","2");
INSERT INTO `subject_departments` VALUES("48","8","5");
INSERT INTO `subject_departments` VALUES("28","8","6");
INSERT INTO `subject_departments` VALUES("69","9","1");
INSERT INTO `subject_departments` VALUES("9","9","2");
INSERT INTO `subject_departments` VALUES("49","9","5");
INSERT INTO `subject_departments` VALUES("29","9","6");
INSERT INTO `subject_departments` VALUES("70","10","1");
INSERT INTO `subject_departments` VALUES("10","10","2");
INSERT INTO `subject_departments` VALUES("50","10","5");
INSERT INTO `subject_departments` VALUES("30","10","6");
INSERT INTO `subject_departments` VALUES("71","11","1");
INSERT INTO `subject_departments` VALUES("11","11","2");
INSERT INTO `subject_departments` VALUES("51","11","5");
INSERT INTO `subject_departments` VALUES("31","11","6");
INSERT INTO `subject_departments` VALUES("72","12","1");
INSERT INTO `subject_departments` VALUES("12","12","2");
INSERT INTO `subject_departments` VALUES("52","12","5");
INSERT INTO `subject_departments` VALUES("32","12","6");
INSERT INTO `subject_departments` VALUES("73","13","1");
INSERT INTO `subject_departments` VALUES("13","13","2");
INSERT INTO `subject_departments` VALUES("53","13","5");
INSERT INTO `subject_departments` VALUES("33","13","6");
INSERT INTO `subject_departments` VALUES("74","14","1");
INSERT INTO `subject_departments` VALUES("14","14","2");
INSERT INTO `subject_departments` VALUES("54","14","5");
INSERT INTO `subject_departments` VALUES("34","14","6");
INSERT INTO `subject_departments` VALUES("75","15","1");
INSERT INTO `subject_departments` VALUES("15","15","2");
INSERT INTO `subject_departments` VALUES("55","15","5");
INSERT INTO `subject_departments` VALUES("35","15","6");
INSERT INTO `subject_departments` VALUES("76","16","1");
INSERT INTO `subject_departments` VALUES("16","16","2");
INSERT INTO `subject_departments` VALUES("56","16","5");
INSERT INTO `subject_departments` VALUES("36","16","6");
INSERT INTO `subject_departments` VALUES("77","17","1");
INSERT INTO `subject_departments` VALUES("17","17","2");
INSERT INTO `subject_departments` VALUES("57","17","5");
INSERT INTO `subject_departments` VALUES("37","17","6");
INSERT INTO `subject_departments` VALUES("78","18","1");
INSERT INTO `subject_departments` VALUES("18","18","2");
INSERT INTO `subject_departments` VALUES("58","18","5");
INSERT INTO `subject_departments` VALUES("38","18","6");
INSERT INTO `subject_departments` VALUES("79","19","1");
INSERT INTO `subject_departments` VALUES("19","19","2");
INSERT INTO `subject_departments` VALUES("59","19","5");
INSERT INTO `subject_departments` VALUES("39","19","6");
INSERT INTO `subject_departments` VALUES("80","20","1");
INSERT INTO `subject_departments` VALUES("20","20","2");
INSERT INTO `subject_departments` VALUES("60","20","5");
INSERT INTO `subject_departments` VALUES("40","20","6");
INSERT INTO `subject_departments` VALUES("81","21","1");
INSERT INTO `subject_departments` VALUES("21","21","2");
INSERT INTO `subject_departments` VALUES("61","21","5");
INSERT INTO `subject_departments` VALUES("41","21","6");
INSERT INTO `subject_departments` VALUES("182","22","5");
INSERT INTO `subject_departments` VALUES("183","23","5");
INSERT INTO `subject_departments` VALUES("184","24","5");
INSERT INTO `subject_departments` VALUES("185","25","5");
INSERT INTO `subject_departments` VALUES("186","26","5");
INSERT INTO `subject_departments` VALUES("187","27","5");
INSERT INTO `subject_departments` VALUES("188","28","5");
INSERT INTO `subject_departments` VALUES("189","29","5");
INSERT INTO `subject_departments` VALUES("190","30","5");
INSERT INTO `subject_departments` VALUES("191","31","5");
INSERT INTO `subject_departments` VALUES("192","32","5");
INSERT INTO `subject_departments` VALUES("193","33","5");
INSERT INTO `subject_departments` VALUES("194","34","5");
INSERT INTO `subject_departments` VALUES("195","35","5");
INSERT INTO `subject_departments` VALUES("196","36","5");
INSERT INTO `subject_departments` VALUES("197","37","5");
INSERT INTO `subject_departments` VALUES("198","38","5");




CREATE TABLE `subject_exam_pattern` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` int(11) NOT NULL,
  `has_cq` tinyint(1) DEFAULT 1,
  `has_mcq` tinyint(1) DEFAULT 1,
  `has_practical` tinyint(1) DEFAULT 0,
  `cq_marks` decimal(5,2) DEFAULT 70.00,
  `mcq_marks` decimal(5,2) DEFAULT 30.00,
  `practical_marks` decimal(5,2) DEFAULT 0.00,
  `total_marks` decimal(5,2) DEFAULT 100.00,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `subject_id` (`subject_id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `subject_exam_pattern` VALUES("1","30","1","1","0","70.00","30.00","0.00","100.00","1","2025-05-08 21:30:37","2025-05-08 21:30:37");
INSERT INTO `subject_exam_pattern` VALUES("2","22","1","1","0","70.00","30.00","0.00","100.00","1","2025-05-08 21:31:25","2025-05-08 21:31:25");
INSERT INTO `subject_exam_pattern` VALUES("3","26","1","1","1","50.00","25.00","25.00","100.00","1","2025-05-08 21:58:27","2025-05-08 21:58:27");
INSERT INTO `subject_exam_pattern` VALUES("4","31","1","1","0","70.00","30.00","0.00","100.00","1","2025-05-08 21:58:42","2025-05-08 21:58:42");
INSERT INTO `subject_exam_pattern` VALUES("5","24","1","1","1","50.00","25.00","25.00","100.00","1","2025-05-08 21:59:38","2025-05-08 21:59:38");
INSERT INTO `subject_exam_pattern` VALUES("6","29","1","1","0","70.00","30.00","0.00","100.00","1","2025-05-08 21:59:49","2025-05-08 21:59:49");
INSERT INTO `subject_exam_pattern` VALUES("7","40","1","1","0","70.00","30.00","0.00","100.00","1","2025-05-08 21:59:57","2025-05-08 21:59:57");
INSERT INTO `subject_exam_pattern` VALUES("8","34","1","0","0","100.00","0.00","0.00","100.00","1","2025-05-08 22:00:09","2025-05-08 22:00:09");
INSERT INTO `subject_exam_pattern` VALUES("9","28","1","1","0","70.00","30.00","0.00","100.00","1","2025-05-08 22:00:25","2025-05-08 22:00:25");
INSERT INTO `subject_exam_pattern` VALUES("10","36","1","1","0","70.00","30.00","0.00","100.00","1","2025-05-08 22:00:42","2025-05-08 22:00:42");
INSERT INTO `subject_exam_pattern` VALUES("11","35","1","1","1","50.00","25.00","25.00","100.00","1","2025-05-08 22:01:01","2025-05-08 22:01:01");
INSERT INTO `subject_exam_pattern` VALUES("12","37","1","1","0","70.00","30.00","0.00","100.00","1","2025-05-08 22:01:12","2025-05-08 22:01:12");
INSERT INTO `subject_exam_pattern` VALUES("13","39","1","1","0","70.00","30.00","0.00","100.00","1","2025-05-08 22:01:20","2025-05-08 22:01:20");
INSERT INTO `subject_exam_pattern` VALUES("14","25","1","1","1","50.00","25.00","25.00","100.00","1","2025-05-08 22:01:44","2025-05-08 22:01:44");
INSERT INTO `subject_exam_pattern` VALUES("15","23","1","1","1","50.00","25.00","25.00","100.00","1","2025-05-08 22:02:02","2025-05-08 22:02:02");
INSERT INTO `subject_exam_pattern` VALUES("16","33","1","1","0","70.00","30.00","0.00","100.00","1","2025-05-08 22:02:10","2025-05-08 22:02:10");
INSERT INTO `subject_exam_pattern` VALUES("17","32","1","1","0","70.00","30.00","0.00","100.00","1","2025-05-08 22:02:22","2025-05-08 22:02:22");
INSERT INTO `subject_exam_pattern` VALUES("18","27","1","1","1","50.00","25.00","25.00","100.00","1","2025-05-08 22:02:44","2025-05-08 22:02:44");
INSERT INTO `subject_exam_pattern` VALUES("19","38","1","1","0","70.00","30.00","0.00","100.00","1","2025-05-08 22:03:00","2025-05-08 22:03:00");




CREATE TABLE `subject_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` int(11) NOT NULL,
  `group_id` int(11) NOT NULL,
  `subject_type` enum('required','optional','fourth') NOT NULL DEFAULT 'optional',
  `is_applicable` tinyint(1) DEFAULT 1,
  `category_id` int(11) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `subject_id` (`subject_id`),
  KEY `group_id` (`group_id`),
  CONSTRAINT `subject_groups_ibfk_1` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE,
  CONSTRAINT `subject_groups_ibfk_2` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=254 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `subject_groups` VALUES("202","30","11","required","1","1","2025-05-08 23:11:27");
INSERT INTO `subject_groups` VALUES("203","22","1","required","1","1","2025-05-08 23:11:27");
INSERT INTO `subject_groups` VALUES("204","22","2","required","1","1","2025-05-08 23:11:27");
INSERT INTO `subject_groups` VALUES("205","22","11","required","1","1","2025-05-08 23:11:27");
INSERT INTO `subject_groups` VALUES("206","26","1","optional","1","1","2025-05-08 23:11:27");
INSERT INTO `subject_groups` VALUES("207","26","1","fourth","1","2","2025-05-08 23:11:27");
INSERT INTO `subject_groups` VALUES("208","26","1","optional","1","3","2025-05-08 23:11:27");
INSERT INTO `subject_groups` VALUES("209","31","11","optional","1","1","2025-05-08 23:11:27");
INSERT INTO `subject_groups` VALUES("210","31","11","fourth","1","2","2025-05-08 23:11:27");
INSERT INTO `subject_groups` VALUES("211","31","11","optional","1","3","2025-05-08 23:11:27");
INSERT INTO `subject_groups` VALUES("212","24","1","required","1","1","2025-05-08 23:11:27");
INSERT INTO `subject_groups` VALUES("213","29","2","optional","1","1","2025-05-08 23:11:27");
INSERT INTO `subject_groups` VALUES("214","29","2","fourth","1","2","2025-05-08 23:11:27");
INSERT INTO `subject_groups` VALUES("215","29","2","optional","1","3","2025-05-08 23:11:27");
INSERT INTO `subject_groups` VALUES("216","40","1","fourth","1","1","2025-05-08 23:11:27");
INSERT INTO `subject_groups` VALUES("217","40","2","optional","1","1","2025-05-08 23:11:28");
INSERT INTO `subject_groups` VALUES("218","40","2","fourth","1","2","2025-05-08 23:11:28");
INSERT INTO `subject_groups` VALUES("219","40","2","optional","1","3","2025-05-08 23:11:28");
INSERT INTO `subject_groups` VALUES("220","40","11","optional","1","1","2025-05-08 23:11:28");
INSERT INTO `subject_groups` VALUES("221","40","11","fourth","1","2","2025-05-08 23:11:28");
INSERT INTO `subject_groups` VALUES("222","40","11","optional","1","3","2025-05-08 23:11:28");
INSERT INTO `subject_groups` VALUES("223","34","1","required","1","1","2025-05-08 23:11:28");
INSERT INTO `subject_groups` VALUES("224","34","2","required","1","1","2025-05-08 23:11:28");
INSERT INTO `subject_groups` VALUES("225","34","11","required","1","1","2025-05-08 23:11:28");
INSERT INTO `subject_groups` VALUES("226","28","11","required","1","1","2025-05-08 23:11:28");
INSERT INTO `subject_groups` VALUES("227","36","2","optional","1","1","2025-05-08 23:11:28");
INSERT INTO `subject_groups` VALUES("228","36","2","fourth","1","2","2025-05-08 23:11:28");
INSERT INTO `subject_groups` VALUES("229","36","2","optional","1","3","2025-05-08 23:11:28");
INSERT INTO `subject_groups` VALUES("230","35","1","required","1","1","2025-05-08 23:11:28");
INSERT INTO `subject_groups` VALUES("231","35","2","required","1","1","2025-05-08 23:11:28");
INSERT INTO `subject_groups` VALUES("232","35","11","required","1","1","2025-05-08 23:11:28");
INSERT INTO `subject_groups` VALUES("233","37","2","optional","1","1","2025-05-08 23:11:28");
INSERT INTO `subject_groups` VALUES("234","37","2","fourth","1","2","2025-05-08 23:11:28");
INSERT INTO `subject_groups` VALUES("235","37","2","optional","1","3","2025-05-08 23:11:28");
INSERT INTO `subject_groups` VALUES("236","39","2","optional","1","1","2025-05-08 23:11:28");
INSERT INTO `subject_groups` VALUES("237","39","2","fourth","1","2","2025-05-08 23:11:28");
INSERT INTO `subject_groups` VALUES("238","39","2","optional","1","3","2025-05-08 23:11:29");
INSERT INTO `subject_groups` VALUES("239","25","1","optional","1","1","2025-05-08 23:11:29");
INSERT INTO `subject_groups` VALUES("240","25","1","fourth","1","2","2025-05-08 23:11:29");
INSERT INTO `subject_groups` VALUES("241","25","1","optional","1","3","2025-05-08 23:11:29");
INSERT INTO `subject_groups` VALUES("242","23","1","required","1","1","2025-05-08 23:11:29");
INSERT INTO `subject_groups` VALUES("243","33","2","optional","1","1","2025-05-08 23:11:29");
INSERT INTO `subject_groups` VALUES("244","33","2","fourth","1","2","2025-05-08 23:11:29");
INSERT INTO `subject_groups` VALUES("245","33","2","optional","1","3","2025-05-08 23:11:29");
INSERT INTO `subject_groups` VALUES("246","32","2","optional","1","1","2025-05-08 23:11:29");
INSERT INTO `subject_groups` VALUES("247","32","2","fourth","1","2","2025-05-08 23:11:29");
INSERT INTO `subject_groups` VALUES("248","32","2","optional","1","3","2025-05-08 23:11:29");
INSERT INTO `subject_groups` VALUES("249","27","1","fourth","1","1","2025-05-08 23:11:29");
INSERT INTO `subject_groups` VALUES("250","27","2","fourth","1","1","2025-05-08 23:11:29");
INSERT INTO `subject_groups` VALUES("251","27","11","optional","1","1","2025-05-08 23:11:29");
INSERT INTO `subject_groups` VALUES("252","27","11","fourth","1","2","2025-05-08 23:11:29");
INSERT INTO `subject_groups` VALUES("253","38","2","fourth","1","1","2025-05-08 23:11:29");




CREATE TABLE `subject_marks_distribution` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` int(11) NOT NULL,
  `cq_marks` decimal(5,2) DEFAULT 70.00,
  `mcq_marks` decimal(5,2) DEFAULT 30.00,
  `practical_marks` decimal(5,2) DEFAULT 0.00,
  `total_marks` decimal(5,2) DEFAULT 100.00,
  `min_cq_pass_marks` decimal(5,2) DEFAULT 23.10,
  `min_mcq_pass_marks` decimal(5,2) DEFAULT 9.90,
  `min_practical_pass_marks` decimal(5,2) DEFAULT 0.00,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `subject_id` (`subject_id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `subject_marks_distribution` VALUES("1","26","50.00","25.00","25.00","100.00","16.50","8.25","8.25","1","2025-05-08 17:43:36","2025-05-09 00:11:44");
INSERT INTO `subject_marks_distribution` VALUES("2","30","70.00","30.00","0.00","100.00","23.10","9.90","0.00","1","2025-05-08 17:43:47","2025-05-08 17:43:47");
INSERT INTO `subject_marks_distribution` VALUES("3","22","70.00","30.00","0.00","100.00","23.10","9.90","0.00","1","2025-05-08 17:44:10","2025-05-08 17:44:10");
INSERT INTO `subject_marks_distribution` VALUES("4","31","70.00","30.00","0.00","100.00","23.10","9.90","0.00","1","2025-05-08 17:44:18","2025-05-08 17:44:18");
INSERT INTO `subject_marks_distribution` VALUES("5","24","50.00","25.00","25.00","100.00","16.50","8.25","8.25","1","2025-05-08 17:44:40","2025-05-09 00:11:44");
INSERT INTO `subject_marks_distribution` VALUES("6","29","70.00","30.00","0.00","100.00","23.10","9.90","0.00","1","2025-05-08 17:44:51","2025-05-08 17:44:51");
INSERT INTO `subject_marks_distribution` VALUES("7","40","70.00","30.00","0.00","100.00","23.10","9.90","0.00","1","2025-05-08 17:45:01","2025-05-08 17:45:01");
INSERT INTO `subject_marks_distribution` VALUES("8","34","100.00","0.00","0.00","100.00","33.00","0.00","0.00","1","2025-05-08 17:57:18","2025-05-09 00:11:44");
INSERT INTO `subject_marks_distribution` VALUES("9","28","70.00","30.00","0.00","100.00","23.10","9.90","0.00","1","2025-05-08 17:57:44","2025-05-08 17:57:44");
INSERT INTO `subject_marks_distribution` VALUES("10","36","70.00","30.00","0.00","100.00","23.10","9.90","0.00","1","2025-05-08 17:57:58","2025-05-08 17:57:58");
INSERT INTO `subject_marks_distribution` VALUES("11","35","50.00","25.00","25.00","100.00","16.50","8.25","8.25","1","2025-05-08 17:58:32","2025-05-09 00:11:44");
INSERT INTO `subject_marks_distribution` VALUES("12","37","70.00","30.00","0.00","100.00","23.10","9.90","0.00","1","2025-05-08 17:58:52","2025-05-08 17:58:52");
INSERT INTO `subject_marks_distribution` VALUES("13","39","70.00","30.00","0.00","100.00","23.10","9.90","0.00","1","2025-05-08 17:59:06","2025-05-08 17:59:06");
INSERT INTO `subject_marks_distribution` VALUES("14","25","50.00","25.00","25.00","100.00","16.50","8.25","8.25","1","2025-05-08 17:59:25","2025-05-09 00:11:44");
INSERT INTO `subject_marks_distribution` VALUES("15","23","50.00","25.00","25.00","100.00","16.50","8.25","8.25","1","2025-05-08 17:59:47","2025-05-09 00:11:44");
INSERT INTO `subject_marks_distribution` VALUES("16","33","70.00","30.00","0.00","100.00","23.10","9.90","0.00","1","2025-05-08 17:59:58","2025-05-08 17:59:58");
INSERT INTO `subject_marks_distribution` VALUES("17","27","50.00","25.00","25.00","100.00","16.50","8.25","8.25","1","2025-05-08 18:00:16","2025-05-09 00:11:44");
INSERT INTO `subject_marks_distribution` VALUES("18","38","70.00","30.00","0.00","100.00","23.10","9.90","0.00","1","2025-05-08 18:00:28","2025-05-08 18:00:28");
INSERT INTO `subject_marks_distribution` VALUES("19","32","70.00","30.00","0.00","100.00","23.10","9.90","0.00","1","2025-05-08 18:01:35","2025-05-08 18:01:35");




CREATE TABLE `subject_minimum_pass` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` int(11) NOT NULL,
  `cq_min_marks` float DEFAULT 0,
  `mcq_min_marks` float DEFAULT 0,
  `practical_min_marks` float DEFAULT 0,
  `total_min_marks` float DEFAULT 33,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `subject_id` (`subject_id`),
  KEY `subject_id_2` (`subject_id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `subject_minimum_pass` VALUES("1","30","23","10","0","33","1","2025-05-08 22:04:05","2025-05-08 22:04:05");
INSERT INTO `subject_minimum_pass` VALUES("2","22","23","10","0","33","1","2025-05-08 22:04:16","2025-05-08 22:04:16");
INSERT INTO `subject_minimum_pass` VALUES("3","26","17","8","8","33","1","2025-05-08 22:04:28","2025-05-08 22:04:28");
INSERT INTO `subject_minimum_pass` VALUES("4","31","23","10","0","33","1","2025-05-08 22:04:39","2025-05-08 22:04:39");
INSERT INTO `subject_minimum_pass` VALUES("5","24","17","8","8","33","1","2025-05-08 22:04:54","2025-05-08 22:04:54");
INSERT INTO `subject_minimum_pass` VALUES("6","29","23","10","0","33","1","2025-05-08 22:05:08","2025-05-08 22:05:08");
INSERT INTO `subject_minimum_pass` VALUES("7","40","23","10","0","33","1","2025-05-08 22:05:17","2025-05-08 22:05:17");
INSERT INTO `subject_minimum_pass` VALUES("8","34","33","0","0","33","1","2025-05-08 22:05:24","2025-05-08 22:05:24");
INSERT INTO `subject_minimum_pass` VALUES("9","28","23","10","0","33","1","2025-05-08 22:05:32","2025-05-08 22:05:32");
INSERT INTO `subject_minimum_pass` VALUES("10","35","17","8","8","33","1","2025-05-08 22:05:45","2025-05-08 22:05:45");
INSERT INTO `subject_minimum_pass` VALUES("11","37","23","10","0","33","1","2025-05-08 22:06:23","2025-05-08 22:06:23");
INSERT INTO `subject_minimum_pass` VALUES("12","39","23","10","0","33","1","2025-05-08 22:06:35","2025-05-08 22:06:35");
INSERT INTO `subject_minimum_pass` VALUES("13","25","17","8","8","33","1","2025-05-08 22:06:47","2025-05-08 22:06:47");
INSERT INTO `subject_minimum_pass` VALUES("14","23","17","8","8","33","1","2025-05-08 22:07:02","2025-05-08 22:07:02");
INSERT INTO `subject_minimum_pass` VALUES("15","33","23","10","0","33","1","2025-05-08 22:07:10","2025-05-08 22:07:10");
INSERT INTO `subject_minimum_pass` VALUES("16","32","23","10","0","33","1","2025-05-08 22:07:20","2025-05-08 22:07:20");
INSERT INTO `subject_minimum_pass` VALUES("17","27","17","8","8","33","1","2025-05-08 22:07:28","2025-05-08 22:07:28");
INSERT INTO `subject_minimum_pass` VALUES("18","38","23","10","0","33","1","2025-05-08 22:07:35","2025-05-08 22:07:35");




CREATE TABLE `subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_name` varchar(100) NOT NULL,
  `subject_code` varchar(20) NOT NULL,
  `category` varchar(255) DEFAULT 'required',
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `is_fourth_subject` tinyint(1) DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `subjects` VALUES("22","BANGLA","101","required",NULL,"1","2025-05-05 18:50:47","0");
INSERT INTO `subjects` VALUES("23","PHYSICS","174-175","required",NULL,"1","2025-05-05 19:54:30","0");
INSERT INTO `subjects` VALUES("24","CHEMISTRY","176-177","required",NULL,"1","2025-05-05 19:54:44","0");
INSERT INTO `subjects` VALUES("25","MATH","265-266","required",NULL,"1","2025-05-05 19:54:58","0");
INSERT INTO `subjects` VALUES("26","BIOLOGY","178-179","required",NULL,"1","2025-05-05 19:55:20","0");
INSERT INTO `subjects` VALUES("27","STATISTICS","129-130","required",NULL,"1","2025-05-05 19:55:39","0");
INSERT INTO `subjects` VALUES("28","FBI","277-278","required",NULL,"1","2025-05-05 19:56:06","0");
INSERT INTO `subjects` VALUES("29","CIVICS","269-270","required",NULL,"1","2025-05-05 21:51:22","0");
INSERT INTO `subjects` VALUES("30","ACCOUNTING","253-254","required",NULL,"1","2025-05-05 22:17:02","0");
INSERT INTO `subjects` VALUES("31","BOM","286-287","required",NULL,"1","2025-05-05 22:17:21","0");
INSERT INTO `subjects` VALUES("32","SOCIOLOGY","117-118","required",NULL,"1","2025-05-05 22:17:32","0");
INSERT INTO `subjects` VALUES("33","SOCIAL WORK","271-272","required",NULL,"1","2025-05-05 22:17:51","0");
INSERT INTO `subjects` VALUES("34","ENGLISH","107-108","required",NULL,"1","2025-05-05 22:18:13","0");
INSERT INTO `subjects` VALUES("35","ICT","275","required",NULL,"1","2025-05-05 22:18:30","0");
INSERT INTO `subjects` VALUES("36","HISTORY","304-305","required",NULL,"1","2025-05-05 22:18:45","0");
INSERT INTO `subjects` VALUES("37","ISLAMIC HISTORY","267-268","required",NULL,"1","2025-05-05 22:19:17","0");
INSERT INTO `subjects` VALUES("38","STUDY OF ISLAM","249-250","required",NULL,"1","2025-05-05 22:19:33","0");
INSERT INTO `subjects` VALUES("39","LOGIC","121-122","required",NULL,"1","2025-05-06 16:17:13","0");
INSERT INTO `subjects` VALUES("40","ECONOMICS","109-110","required",NULL,"1","2025-05-06 16:17:29","0");




CREATE TABLE `teacher_subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `teacher_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `session_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `teacher_id` (`teacher_id`,`subject_id`,`session_id`),
  KEY `subject_id` (`subject_id`),
  KEY `session_id` (`session_id`),
  CONSTRAINT `teacher_subjects_ibfk_1` FOREIGN KEY (`teacher_id`) REFERENCES `teachers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `teacher_subjects_ibfk_2` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE,
  CONSTRAINT `teacher_subjects_ibfk_3` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `teachers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `teacher_id` varchar(20) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `gender` enum('male','female','other') NOT NULL,
  `dob` date DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(50) DEFAULT NULL,
  `state` varchar(50) DEFAULT NULL,
  `postal_code` varchar(20) DEFAULT NULL,
  `country` varchar(50) DEFAULT 'Bangladesh',
  `joining_date` date DEFAULT NULL,
  `profile_photo` varchar(255) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `subject` varchar(100) DEFAULT NULL,
  `designation` varchar(100) DEFAULT NULL,
  `username` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `teacher_id` (`teacher_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `user_type` enum('admin','teacher','student','staff') NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `role` varchar(20) NOT NULL DEFAULT 'student',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `users` VALUES("1","admin","$2y$10$C6EA2xrSbOEX9fKd9sR/Tucy6f8rmK4/eu2WzdPwgk6JSpfamJRXi","admin","<EMAIL>","2025-05-04 17:57:58","student");
INSERT INTO `users` VALUES("5","tanvir","$2y$10$srJMXXivx3yu/3vNY/lvhuO4NLLTHmFVYO2DdL/FuqyOkFtq/mxw6","admin",NULL,"2025-05-05 09:20:27","student");
INSERT INTO `users` VALUES("6","nayan","$2y$10$0qS7lG.CoSUbOQuGT1bGY.an/NuJO59xU0fkhiznv6GIkThJpI0WG","admin",NULL,"2025-05-05 09:22:06","student");
INSERT INTO `users` VALUES("7","ali","$2y$10$cz9veGg8Qlh1uJLSkicH9uufzBPILimPW5Hb3iEDuWTKRpy094QZG","admin",NULL,"2025-05-05 23:36:31","student");
INSERT INTO `users` VALUES("8","rani","$2y$10$cgT.phXuTC86lh9cUlMr6OhWuhHdWs5Fug9zvoVMOoq85YSuIhbTm","admin",NULL,"2025-05-05 23:40:57","student");


