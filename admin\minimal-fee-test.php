<?php
// Minimal fee test with error handling
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session FIRST before any output
session_start();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Minimal Fee Test</title>
    <style>body{font-family:Arial;margin:20px;} .error{color:red;} .success{color:green;}</style>
</head>
<body>

<h1>🧪 Minimal Fee Test</h1>

<?php
try {
    echo "<p class='success'>✅ Session started</p>";
    
    // Check if user is logged in
    $bypassLogin = isset($_GET['bypass']) && $_GET['bypass'] === 'test';

    if (!isset($_SESSION['userId']) && !$bypassLogin) {
        echo "<p class='error'>❌ User not logged in.</p>";
        echo "<p><a href='../login.php'>Please login first</a> OR <a href='?bypass=test'>Bypass for testing</a></p>";
        echo "</body></html>";
        exit;
    }

    if (isset($_SESSION['userType']) && $_SESSION['userType'] !== 'admin' && !$bypassLogin) {
        echo "<p class='error'>❌ Access denied. Admin access required.</p>";
        echo "<p><a href='?bypass=test'>Bypass for testing</a></p>";
        echo "</body></html>";
        exit;
    }

    if ($bypassLogin) {
        echo "<p class='success'>⚠️ Login bypassed for testing</p>";
        // Set fake session for testing
        $_SESSION['userId'] = 999;
        $_SESSION['userType'] = 'admin';
        $_SESSION['username'] = 'test_admin';
    } else {
        echo "<p class='success'>✅ User authenticated: " . $_SESSION['username'] . " (Admin)</p>";
    }
    
    // Connect to database
    $servername = "127.0.0.1";
    $username = "root";
    $password = "";
    $dbname = "zfaw";
    
    $conn = new mysqli($servername, $username, $password, $dbname);
    
    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }
    
    echo "<p class='success'>✅ Database connected</p>";
    
    // Check if tables exist
    $tables = ['students', 'sessions', 'classes', 'fees'];
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo "<p class='success'>✅ Table '$table' exists</p>";
        } else {
            echo "<p class='error'>❌ Table '$table' not found</p>";
        }
    }
    
    // Count records
    $counts = [];
    foreach ($tables as $table) {
        $result = $conn->query("SELECT COUNT(*) as count FROM $table");
        if ($result) {
            $count = $result->fetch_assoc()['count'];
            $counts[$table] = $count;
            echo "<p>📊 $table: $count records</p>";
        }
    }
    
    // Handle form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_fee'])) {
        echo "<h2>🔄 Processing Fee Addition...</h2>";
        
        $studentId = intval($_POST['student_id']);
        $feeType = $_POST['fee_type'];
        $amount = floatval($_POST['amount']);
        $dueDate = $_POST['due_date'];
        
        echo "<p>Student ID: $studentId</p>";
        echo "<p>Fee Type: $feeType</p>";
        echo "<p>Amount: $amount</p>";
        echo "<p>Due Date: $dueDate</p>";
        
        if ($studentId > 0 && !empty($feeType) && $amount > 0 && !empty($dueDate)) {
            // Get student info
            $studentQuery = "SELECT * FROM students WHERE id = $studentId";
            $studentResult = $conn->query($studentQuery);
            
            if ($studentResult && $studentResult->num_rows > 0) {
                $student = $studentResult->fetch_assoc();
                $studentName = $student['first_name'] . ' ' . $student['last_name'];
                echo "<p class='success'>✅ Student found: " . $studentName . "</p>";
                
                // Insert fee
                $sql = "INSERT INTO fees (student_id, session_id, class_id, fee_type, amount, due_date, status, created_at) 
                        VALUES ($studentId, " . $student['session_id'] . ", " . $student['class_id'] . ", '$feeType', $amount, '$dueDate', 'unpaid', NOW())";
                
                echo "<p>SQL Query: $sql</p>";
                
                if ($conn->query($sql)) {
                    echo "<p class='success'>✅ Fee added successfully! Fee ID: " . $conn->insert_id . "</p>";
                } else {
                    echo "<p class='error'>❌ Error adding fee: " . $conn->error . "</p>";
                }
            } else {
                echo "<p class='error'>❌ Student not found</p>";
            }
        } else {
            echo "<p class='error'>❌ Invalid form data</p>";
        }
    }
    
    // Show form if we have students
    if ($counts['students'] > 0) {
        echo "<h2>📝 Add Fee Form</h2>";
        echo "<form method='post'>";
        
        // Get students (check for correct column names)
        $studentsQuery = "SELECT * FROM students ORDER BY first_name, last_name LIMIT 10";
        $studentsResult = $conn->query($studentsQuery);
        
        echo "<p><label>Student:</label><br>";
        echo "<select name='student_id' required>";
        echo "<option value=''>Choose Student</option>";
        while ($student = $studentsResult->fetch_assoc()) {
            $studentName = $student['first_name'] . ' ' . $student['last_name'];
            $rollNumber = isset($student['roll_number']) ? $student['roll_number'] : (isset($student['student_id']) ? $student['student_id'] : 'N/A');
            echo "<option value='" . $student['id'] . "'>" . $studentName . " (Roll: " . $rollNumber . ")</option>";
        }
        echo "</select></p>";
        
        echo "<p><label>Fee Type:</label><br>";
        echo "<select name='fee_type' required>";
        echo "<option value=''>Choose Fee Type</option>";
        echo "<option value='tuition'>Tuition Fee</option>";
        echo "<option value='admission'>Admission Fee</option>";
        echo "<option value='exam'>Exam Fee</option>";
        echo "</select></p>";
        
        echo "<p><label>Amount:</label><br>";
        echo "<input type='number' name='amount' value='1000' step='0.01' required></p>";
        
        echo "<p><label>Due Date:</label><br>";
        echo "<input type='date' name='due_date' value='" . date('Y-m-d', strtotime('+30 days')) . "' required></p>";
        
        echo "<p><button type='submit' name='add_fee'>Add Fee</button></p>";
        echo "</form>";
    } else {
        echo "<p class='error'>❌ No students found. Please add students first.</p>";
    }
    
    // Show recent fees
    if ($counts['fees'] > 0) {
        echo "<h2>📋 Recent Fees</h2>";
        $feesQuery = "SELECT f.*, s.name as student_name FROM fees f LEFT JOIN students s ON f.student_id = s.id ORDER BY f.id DESC LIMIT 5";
        $feesResult = $conn->query($feesQuery);
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Student</th><th>Fee Type</th><th>Amount</th><th>Due Date</th><th>Status</th></tr>";
        while ($fee = $feesResult->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $fee['id'] . "</td>";
            echo "<td>" . (isset($fee['student_name']) ? $fee['student_name'] : 'N/A') . "</td>";
            echo "<td>" . $fee['fee_type'] . "</td>";
            echo "<td>" . $fee['amount'] . "</td>";
            echo "<td>" . $fee['due_date'] . "</td>";
            echo "<td>" . $fee['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<h2>🔗 Navigation</h2>";
echo "<a href='fee_management.php'>Fee Management</a> | ";
echo "<a href='dashboard.php'>Dashboard</a> | ";
echo "<a href='simple-test.php'>Simple Test</a>";

echo "</body></html>";
?>
