<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['userId'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if student_roles table exists
$tableCheck = $conn->query("SHOW TABLES LIKE 'student_roles'");
if ($tableCheck->num_rows == 0) {
    // Create the table
    $createTable = "CREATE TABLE student_roles (
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        role_name VARCHAR(100) NOT NULL,
        role_name_bn VARCHAR(100) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $conn->query($createTable);
    
    // Add default roles
    $defaultRoles = [
        ['Class Captain', 'ক্লাস ক্যাপ্টেন', 'Class captain role'],
        ['Class Representative', 'ক্লাস প্রতিনিধি', 'Class representative role'],
        ['Class Monitor', 'ক্লাস মনিটর', 'Class monitor role'],
        ['Student Council Member', 'ছাত্র সংসদ সদস্য', 'Student council member role'],
        ['Regular Student', 'সাধারণ শিক্ষার্থী', 'Regular student role']
    ];
    
    $insertStmt = $conn->prepare("INSERT INTO student_roles (role_name, role_name_bn, description) VALUES (?, ?, ?)");
    foreach ($defaultRoles as $role) {
        $insertStmt->bind_param("sss", $role[0], $role[1], $role[2]);
        $insertStmt->execute();
    }
}

// Get all roles
$roles = $conn->query("SELECT * FROM student_roles ORDER BY role_name");
$rolesList = [];

if ($roles && $roles->num_rows > 0) {
    while ($role = $roles->fetch_assoc()) {
        $rolesList[] = [
            'id' => $role['id'],
            'role_name' => $role['role_name'],
            'role_name_bn' => $role['role_name_bn'],
            'description' => $role['description']
        ];
    }
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode([
    'success' => true,
    'roles' => $rolesList
]);
?>
