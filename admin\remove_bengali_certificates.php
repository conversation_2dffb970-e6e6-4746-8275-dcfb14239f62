<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$errorMessage = '';
$successMessage = '';
$deletedCount = 0;

// Process deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_delete'])) {
    // Delete Bengali certificates from database
    $stmt = $conn->prepare("DELETE FROM certificates WHERE certificate_type = 'bengali'");
    
    if ($stmt->execute()) {
        $deletedCount = $stmt->affected_rows;
        $successMessage = "সফলভাবে {$deletedCount}টি বাংলা সার্টিফিকেট মুছে ফেলা হয়েছে।";
    } else {
        $errorMessage = "সার্টিফিকেট মুছতে সমস্যা হয়েছে: " . $conn->error;
    }
    
    $stmt->close();
}

// Count Bengali certificates
$bengaliCount = 0;
$result = $conn->query("SELECT COUNT(*) as count FROM certificates WHERE certificate_type = 'bengali'");
if ($result && $result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $bengaliCount = $row['count'];
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>বাংলা সার্টিফিকেট মুছুন - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="certificates.php">
                            <i class="fas fa-certificate me-2"></i> সার্টিফিকেট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="simple_certificate.php">
                            <i class="fas fa-file-alt me-2"></i> সাধারণ সার্টিফিকেট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="upload_watermark.php">
                            <i class="fas fa-image me-2"></i> ওয়াটারমার্ক আপলোড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="remove_bengali_certificates.php">
                            <i class="fas fa-trash me-2"></i> বাংলা সার্টিফিকেট মুছুন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>বাংলা সার্টিফিকেট মুছুন</h2>
                        <p class="text-muted">সিস্টেম থেকে সমস্ত বাংলা সার্টিফিকেট মুছে ফেলুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="certificates.php" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>সার্টিফিকেট পেজে ফিরে যান
                        </a>
                    </div>
                </div>

                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-body">
                        <?php if ($bengaliCount > 0): ?>
                            <div class="alert alert-warning">
                                <h4 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i>সতর্কতা!</h4>
                                <p>ডাটাবেসে <strong><?php echo $bengaliCount; ?></strong>টি বাংলা সার্টিফিকেট রয়েছে। এই অপারেশন সমস্ত বাংলা সার্টিফিকেট স্থায়ীভাবে মুছে ফেলবে।</p>
                                <hr>
                                <p class="mb-0">এই অপারেশন অপরিবর্তনীয়। একবার মুছে ফেললে, এই ডেটা পুনরুদ্ধার করা যাবে না।</p>
                            </div>
                            
                            <form method="POST" action="remove_bengali_certificates.php" onsubmit="return confirm('আপনি কি নিশ্চিত যে আপনি সমস্ত বাংলা সার্টিফিকেট মুছতে চান?');">
                                <input type="hidden" name="confirm_delete" value="1">
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash me-2"></i>সমস্ত বাংলা সার্টিফিকেট মুছুন
                                </button>
                            </form>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>ডাটাবেসে কোন বাংলা সার্টিফিকেট নেই।
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">বাংলা সার্টিফিকেট ফাইল মুছুন</h5>
                    </div>
                    <div class="card-body">
                        <p>নিম্নলিখিত ফাইলগুলি মুছে ফেলা হবে:</p>
                        <ul>
                            <li><code>D:\xampp\htdocs\zfaw\admin\bengali_certificate.php</code></li>
                            <li><code>D:\xampp\htdocs\zfaw\admin\certificate_templates\bengali_certificate.php</code></li>
                        </ul>
                        
                        <form method="POST" action="delete_bengali_files.php" onsubmit="return confirm('আপনি কি নিশ্চিত যে আপনি বাংলা সার্টিফিকেট ফাইলগুলি মুছতে চান?');">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>বাংলা সার্টিফিকেট ফাইলগুলি মুছুন
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
