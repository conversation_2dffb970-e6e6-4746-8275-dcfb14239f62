<?php
// COMPLETE RESPONSE FIX - Stop tab loading icon
header('Content-Type: text/html; charset=UTF-8');
header('Cache-Control: no-cache, must-revalidate');
header('Connection: close');

// Start output buffering to calculate content length
ob_start();

// Simple static page with no database connections
$school_name = "নিশাত এডুকেশন সেন্টার";
$school_address = "চুয়াডাঙ্গা, বাংলাদেশ";
$school_logo = "img/logo.jpg";
$principal_name = " মোঃ কামাল উদ্দীন";
$principal_title = "অধ্যক্ষ";
$principal_message = "শিক্ষা হল জাতির মেরুদণ্ড। আমাদের শিক্ষা প্রতিষ্ঠানে আমরা শিক্ষার্থীদের শুধু বইয়ের জ্ঞান নয়, জীবনের জন্য প্রয়োজনীয় সকল দক্ষতা শেখাতে প্রতিশ্রুতিবদ্ধ। আমাদের লক্ষ্য হল প্রতিটি শিক্ষার্থীকে দেশ ও জাতির জন্য একজন যোগ্য নাগরিক হিসেবে গড়ে তোলা।";
$principal_photo = "img/principal.jpg";

$chairman_name = "জনাব মোঃ রফিকুল ইসলাম তনু";
$chairman_title = "সভাপতি";
$chairman_message = "আমাদের শিক্ষা প্রতিষ্ঠান সর্বদা উন্নত মানের শিক্ষা প্রদানে নিবেদিত। আমরা বিশ্বাস করি, শিক্ষার্থীদের সামগ্রিক বিকাশের মাধ্যমেই একটি সমৃদ্ধ জাতি গঠন সম্ভব। আমাদের প্রতিষ্ঠানে আধুনিক শিক্ষা পদ্ধতি ও প্রযুক্তির সমন্বয়ে শিক্ষার্থীদের ভবিষ্যৎ চ্যালেঞ্জ মোকাবেলার জন্য প্রস্তুত করা হয়।";
$chairman_photo = "img/chairman.jpg";
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title><?php echo $school_name; ?></title>

    <!-- Additional meta tags to prevent title bar buffering -->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="generator" content="ZFAW School Management System">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

    <style>
        :root {
            --primary-color: #006A4E; /* Deep Green */
            --secondary-color: #00563B; /* Darker Green */
            --accent-color: #F39C12; /* Amber/Gold */
            --dark-color: #2C3E50; /* Dark Blue-Gray */
            --light-color: #F5F5F5; /* Off-White */
            --text-color: #333333; /* Dark Gray */
            --light-text: #FFFFFF; /* White */
            --highlight-color: #E74C3C; /* Red Accent */
            --soft-color: #E3F2FD; /* Soft Blue */
        }

        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: var(--light-color);
            color: var(--text-color);
        }

        /* Header Styles */
        .header-top {
            background-color: white;
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .school-logo {
            width: 80px;
            height: 80px;
            object-fit: contain;
        }

        .school-title {
            color: var(--primary-color);
            font-weight: 700;
            font-size: 1.8rem;
        }

        .school-subtitle {
            color: var(--dark-color);
            font-weight: 500;
        }

        /* Navigation Styles */
        .main-nav {
            background-color: var(--primary-color);
            padding: 0;
        }

        .main-nav .nav-link {
            color: var(--light-text);
            font-weight: 500;
            padding: 15px 20px;
            transition: all 0.3s;
            border-radius: 0;
        }

        .main-nav .nav-link:hover,
        .main-nav .nav-link.active {
            background-color: var(--secondary-color);
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 60px 0;
            margin-bottom: 30px;
        }

        .hero-title {
            font-weight: 700;
            margin-bottom: 20px;
            color: white;
        }

        .hero-text {
            font-size: 1.1rem;
            margin-bottom: 30px;
            opacity: 0.9;
            color: white;
        }

        .hero-btn {
            background-color: var(--accent-color);
            border: none;
            padding: 10px 25px;
            font-weight: 500;
            transition: all 0.3s;
            color: var(--dark-color);
        }

        .hero-btn:hover {
            background-color: #E67E22;
            transform: translateY(-2px);
            color: var(--dark-color);
        }

        /* Card Styles - Optimized to prevent buffering */
        .card {
            border: none;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            height: 100%;
            background-color: white;
            /* Removed transitions that cause buffering */
        }

        .card:hover {
            /* Simplified hover effect */
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        .card-header {
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
            padding: 15px 20px;
        }

        .card-img-top {
            height: 180px;
            object-fit: cover;
        }

        /* Feature Box - Optimized */
        .feature-box {
            text-align: center;
            padding: 30px 20px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            height: 100%;
            border-top: 4px solid var(--accent-color);
            /* Removed transition that causes buffering */
        }

        .feature-box:hover {
            /* Simplified hover effect */
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        .feature-icon {
            font-size: 2.5rem;
            color: var(--accent-color);
            margin-bottom: 20px;
        }

        /* Footer Styles */
        .footer {
            background-color: var(--dark-color);
            color: white;
            padding: 40px 0 20px;
            margin-top: 50px;
        }

        .footer h5 {
            color: var(--light-color);
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--accent-color);
            display: inline-block;
        }

        .footer-links {
            list-style: none;
            padding: 0;
        }

        .footer-links li {
            margin-bottom: 10px;
        }

        .footer-links a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s;
        }

        .footer-links a:hover {
            color: white;
            padding-left: 5px;
        }

        .social-icons a {
            display: inline-block;
            width: 36px;
            height: 36px;
            background-color: rgba(255,255,255,0.1);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 36px;
            margin-right: 10px;
            transition: all 0.3s;
        }

        .social-icons a:hover {
            background-color: var(--accent-color);
            transform: translateY(-3px);
        }

        .copyright {
            background-color: rgba(0,0,0,0.2);
            padding: 15px 0;
            margin-top: 30px;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .school-title {
                font-size: 1.4rem;
            }

            .school-subtitle {
                font-size: 1rem;
            }

            .hero-section {
                padding: 40px 0;
            }

            .hero-img {
                margin-top: 30px;
            }
        }
    </style>
</head>
<body>
    <!-- Header Top with Logo and Title -->
    <div class="header-top">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-2 text-center text-md-start mb-3 mb-md-0">
                    <img src="<?php echo $school_logo; ?>" alt="School Logo" class="school-logo" onerror="this.src='https://via.placeholder.com/80?text=ZFAW'">
                </div>
                <div class="col-md-10 text-center text-md-start">
                    <h1 class="school-title mb-1"><?php echo $school_name; ?></h1>
                    <h2 class="school-subtitle fs-5"><?php echo $school_address; ?></h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Navigation -->
    <nav class="main-nav">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <ul class="nav">
                        <li class="nav-item">
                            <a class="nav-link active" href="index.php"><i class="fas fa-home me-1"></i> হোম</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="subjects.php"><i class="fas fa-book me-1"></i> বিষয়সমূহ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="teachers.php"><i class="fas fa-chalkboard-teacher me-1"></i> শিক্ষকবৃন্দ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="students.php"><i class="fas fa-user-graduate me-1"></i> শিক্ষার্থীবৃন্দ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="notices.php"><i class="fas fa-bullhorn me-1"></i> নোটিশ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="gb_members.php"><i class="fas fa-users me-1"></i> পরিচালনা বোর্ড</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="personal_sms.php"><i class="fas fa-sms me-1"></i> পার্সোনাল এসএমএস</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php"><i class="fas fa-sign-in-alt me-1"></i> লগইন</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Scrolling Notice Section -->
    <div style="background-color: #f5f5f5; border-top: 1px solid #dee2e6; border-bottom: 1px solid #dee2e6; padding: 15px 0; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
        <div class="container">
            <div class="scrolling-notice-container" style="overflow: hidden; white-space: nowrap; position: relative; height: 30px;">
                <div class="scrolling-notice-content" style="display: inline-block; color: #006A4E; font-size: 18px; font-weight: 500;">
                    <i class="fas fa-bullhorn"></i> <strong>সর্বশেষ নোটিশ:</strong> <a href="notices.php" style="color: #006A4E; text-decoration: none; border-bottom: 1px dotted #006A4E;" onmouseover="this.style.color='#00563B'; this.style.borderBottom='1px solid #00563B';" onmouseout="this.style.color='#006A4E'; this.style.borderBottom='1px dotted #006A4E';">এসএসসি পরীক্ষা ২০২৫ এর নির্দিষ্ট বিষয়ের সময়সূচী Latest Notice/SSC Corner-এ দেখা যাচ্ছে</a>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <i class="fas fa-calendar-alt"></i> <strong>আজকের তারিখ:</strong> <?php echo date('d/m/Y, l'); ?>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <i class="fas fa-graduation-cap"></i> <strong>ভর্তি চলছে:</strong> <a href="admission.php" style="color: #006A4E; text-decoration: none; border-bottom: 1px dotted #006A4E;" onmouseover="this.style.color='#00563B'; this.style.borderBottom='1px solid #00563B';" onmouseout="this.style.color='#006A4E'; this.style.borderBottom='1px dotted #006A4E';">নতুন শিক্ষার্থীদের জন্য ভর্তি চলছে। আজই যোগাযোগ করুন।</a>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <i class="fas fa-bell"></i> <strong>নতুন ঘোষণা:</strong> <a href="notices.php" style="color: #006A4E; text-decoration: none; border-bottom: 1px dotted #006A4E;">শীতকালীন ছুটির তালিকা প্রকাশিত হয়েছে</a>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <i class="fas fa-star"></i> <strong>বিশেষ সুবিধা:</strong> অনলাইন ক্লাস এবং ডিজিটাল লাইব্রেরি সুবিধা উপলব্ধ
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <i class="fas fa-trophy"></i> <strong>সাফল্যের খবর:</strong> <a href="results.php" style="color: #006A4E; text-decoration: none; border-bottom: 1px dotted #006A4E;">আমাদের শিক্ষার্থীরা জেএসসি ও এসএসসি পরীক্ষায় ৯৮% পাস করেছে</a>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <i class="fas fa-clock"></i> <strong>সময়সূচী:</strong> সকাল ৮:০০ টা থেকে দুপুর ২:০০ টা পর্যন্ত ক্লাস চলবে
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <i class="fas fa-phone"></i> <strong>যোগাযোগ:</strong> জরুরি প্রয়োজনে কল করুন +৮৮০১৭১৭৮৬১৭৬২
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Enhanced Scrolling Notice Styles */
        .scrolling-notice-container {
            background: linear-gradient(90deg,
                rgba(245,245,245,1) 0%,
                rgba(245,245,245,0.8) 10%,
                rgba(245,245,245,0) 20%,
                rgba(245,245,245,0) 80%,
                rgba(245,245,245,0.8) 90%,
                rgba(245,245,245,1) 100%);
        }

        @keyframes scroll-left {
            0% {
                transform: translateX(100vw);
            }
            100% {
                transform: translateX(-100%);
            }
        }

        .scrolling-notice-content {
            animation: scroll-left 60s linear infinite;
            will-change: transform;
            padding-left: 50px;
            padding-right: 50px;
        }

        .scrolling-notice-container:hover .scrolling-notice-content {
            animation-play-state: paused;
            cursor: pointer;
        }

        /* Add blinking effect for important notices */
        .scrolling-notice-content i.fa-bullhorn {
            animation: blink 2s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }

        /* Responsive scrolling speed */
        @media (max-width: 768px) {
            .scrolling-notice-content {
                animation-duration: 45s;
                font-size: 16px !important;
            }
        }

        /* Smooth scrolling for better performance */
        @media (prefers-reduced-motion: reduce) {
            .scrolling-notice-content {
                animation: none;
                transform: translateX(0);
                text-align: center;
            }
        }

        /* Add hover effects for links in scrolling text */
        .scrolling-notice-content a {
            transition: all 0.3s ease;
        }

        .scrolling-notice-content a:hover {
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
            transform: scale(1.05);
        }
    </style>

    <!-- Messages Section -->
    <section class="container mt-4 mb-4">
        <div class="row">
            <!-- Principal's Message -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header text-white" style="background-color: #00a65a;">
                        <h5 class="mb-0" style="color: #ffffff;"><?php echo $principal_title; ?>-এর বাণী</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 text-center mb-3 mb-md-0">
                                <img src="<?php echo $principal_photo; ?>" alt="<?php echo $principal_name; ?>" class="img-fluid rounded-circle" style="width: 150px; height: 150px; object-fit: cover;" onerror="this.src='https://via.placeholder.com/150?text=Principal'">
                                <h6 class="mt-2 mb-0"><?php echo $principal_name; ?></h6>
                                <small class="text-muted"><?php echo $principal_title; ?></small>
                            </div>
                            <div class="col-md-8">
                                <p class="card-text"><?php echo $principal_message; ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chairman's Message -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header text-white" style="background-color: #00a65a;">
                        <h5 class="mb-0" style="color: #ffffff;"><?php echo $chairman_title; ?>-এর বাণী</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 text-center mb-3 mb-md-0">
                                <img src="<?php echo $chairman_photo; ?>" alt="<?php echo $chairman_name; ?>" class="img-fluid rounded-circle" style="width: 150px; height: 150px; object-fit: cover;" onerror="this.src='https://via.placeholder.com/150?text=Chairman'">
                                <h6 class="mt-2 mb-0"><?php echo $chairman_name; ?></h6>
                                <small class="text-muted"><?php echo $chairman_title; ?></small>
                            </div>
                            <div class="col-md-8">
                                <p class="card-text"><?php echo $chairman_message; ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="hero-title" style="color: #2196F3;"><?php echo $school_name; ?></h1>
                    <p class="hero-text" style="color: white;">আমাদের শিক্ষা প্রতিষ্ঠানে আপনাকে স্বাগতম। আমরা শিক্ষার্থীদের সর্বোচ্চ মানের শিক্ষা প্রদানে প্রতিশ্রুতিবদ্ধ। আমাদের লক্ষ্য হল প্রতিটি শিক্ষার্থীকে দেশ ও জাতির জন্য একজন যোগ্য নাগরিক হিসেবে গড়ে তোলা।</p>
                    <a href="about.php" class="btn btn-lg" style="background-color: #00a65a; color: white; font-weight: 500; box-shadow: 0 4px 15px rgba(0,0,0,0.2); transition: all 0.3s ease;">আরও জানুন <i class="fas fa-arrow-right ms-2"></i></a>
                </div>
                <div class="col-lg-6">
                    <img src="img/hero-image.jpg" alt="Education" class="img-fluid rounded hero-img" onerror="this.src='https://via.placeholder.com/600x400?text=Education'">
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="container mt-5">
        <div class="text-center mb-5">
            <h2 class="fw-bold">আমাদের বৈশিষ্ট্যসমূহ</h2>
            <p class="text-muted">ZFAW স্কুল ম্যানেজমেন্ট সিস্টেমের প্রধান বৈশিষ্ট্যসমূহ</p>
        </div>

        <div class="row g-4">
            <div class="col-md-4">
                <div class="feature-box">
                    <div class="feature-icon">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                    <h4>শিক্ষার্থী ব্যবস্থাপনা</h4>
                    <p>শিক্ষার্থীদের তথ্য সংরক্ষণ, হাজিরা, ফলাফল এবং অন্যান্য কার্যক্রম সহজেই পরিচালনা করুন।</p>
                </div>
            </div>

            <div class="col-md-4">
                <div class="feature-box">
                    <div class="feature-icon">
                        <i class="fas fa-chalkboard-teacher"></i>
                    </div>
                    <h4>শিক্ষক ব্যবস্থাপনা</h4>
                    <p>শিক্ষকদের তথ্য, ক্লাস রুটিন, বেতন এবং অন্যান্য কার্যক্রম সহজেই পরিচালনা করুন।</p>
                </div>
            </div>

            <div class="col-md-4">
                <div class="feature-box">
                    <div class="feature-icon">
                        <i class="fas fa-book"></i>
                    </div>
                    <h4>বিষয় ব্যবস্থাপনা</h4>
                    <p>বিভিন্ন শ্রেণির বিষয়সমূহ, সিলেবাস এবং পাঠ্যক্রম সহজেই পরিচালনা করুন।</p>
                </div>
            </div>

            <div class="col-md-4">
                <div class="feature-box">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h4>ফলাফল ব্যবস্থাপনা</h4>
                    <p>পরীক্ষার ফলাফল প্রস্তুত, প্রকাশ এবং বিশ্লেষণ সহজেই করুন।</p>
                </div>
            </div>

            <div class="col-md-4">
                <div class="feature-box">
                    <div class="feature-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <h4>ফি ব্যবস্থাপনা</h4>
                    <p>শিক্ষার্থীদের বেতন, ফি সংগ্রহ এবং হিসাব সহজেই পরিচালনা করুন।</p>
                </div>
            </div>

            <div class="col-md-4">
                <div class="feature-box">
                    <div class="feature-icon">
                        <i class="fas fa-id-card"></i>
                    </div>
                    <h4>আইডি কার্ড ও সার্টিফিকেট</h4>
                    <p>শিক্ষার্থীদের আইডি কার্ড এবং সার্টিফিকেট সহজেই তৈরি করুন।</p>
                </div>
            </div>

        </div>
    </section>

    <!-- Latest Notices Section -->
    <section class="container mt-5">
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center text-white" style="background-color: #00a65a;">
                        <h5 class="mb-0" style="color: #ffffff;">সর্বশেষ নোটিশ</h5>
                        <a href="notices.php" class="btn btn-sm btn-outline-light">সকল নোটিশ</a>
                    </div>
                    <div class="card-body">
                        <div class="list-group">
                            <div class="alert alert-info">নোটিশ দেখতে <a href="notices.php" class="alert-link">এখানে ক্লিক করুন</a>।</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header text-white" style="background-color: #00a65a;">
                        <h5 class="mb-0" style="color: #ffffff;">দ্রুত লিঙ্ক</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group">
                            <a href="login.php" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-sign-in-alt me-2"></i> লগইন</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            <a href="subjects.php" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-book me-2"></i> বিষয়সমূহ</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            <a href="teachers.php" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষকবৃন্দ</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            <a href="students.php" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-user-graduate me-2"></i> শিক্ষার্থীবৃন্দ</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            <a href="result.php" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-chart-line me-2"></i> ফলাফল</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            <a href="gb_members.php" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-users me-2"></i> পরিচালনা বোর্ড</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="container mt-5">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <img src="img/about-image.jpg" alt="About Us" class="img-fluid rounded" onerror="this.src='https://via.placeholder.com/600x400?text=About+Us'">
            </div>
            <div class="col-lg-6">
                <h2 class="fw-bold mb-4">আমাদের সম্পর্কে</h2>
                <p>ZFAW স্কুল ম্যানেজমেন্ট সিস্টেম হল একটি সম্পূর্ণ শিক্ষা প্রতিষ্ঠান ব্যবস্থাপনা সমাধান। এটি শিক্ষার্থী, শিক্ষক, কোর্স, ফলাফল, ফি এবং অন্যান্য সকল কার্যক্রম সহজে পরিচালনা করতে সাহায্য করে।</p>
                <p>আমাদের সিস্টেম ব্যবহার করে আপনি আপনার শিক্ষা প্রতিষ্ঠানের সকল কার্যক্রম সহজে এবং দক্ষতার সাথে পরিচালনা করতে পারবেন।</p>
                <a href="about.php" class="btn mt-3" style="background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%); color: white; font-weight: 500; box-shadow: 0 4px 15px rgba(0,0,0,0.2); transition: all 0.3s ease;">আরও জানুন <i class="fas fa-arrow-right ms-2"></i></a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer mt-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5>ZFAW শিক্ষালয়</h5>
                    <p>উচ্চমানের শিক্ষা প্রদানের মাধ্যমে শিক্ষার্থীদের ভবিষ্যৎ গড়তে সাহায্য করা আমাদের লক্ষ্য।</p>
                    <div class="social-icons mt-3">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>

                <div class="col-lg-4 mb-4">
                    <h5>লিঙ্কসমূহ</h5>
                    <ul class="footer-links">
                        <li><a href="index.php"><i class="fas fa-chevron-right me-2"></i> হোম</a></li>
                        <li><a href="about.php"><i class="fas fa-chevron-right me-2"></i> আমাদের সম্পর্কে</a></li>
                        <li><a href="subjects.php"><i class="fas fa-chevron-right me-2"></i> বিষয়সমূহ</a></li>
                        <li><a href="teachers.php"><i class="fas fa-chevron-right me-2"></i> শিক্ষকবৃন্দ</a></li>
                        <li><a href="students.php"><i class="fas fa-chevron-right me-2"></i> শিক্ষার্থীবৃন্দ</a></li>
                        <li><a href="login.php"><i class="fas fa-chevron-right me-2"></i> লগইন</a></li>
                    </ul>
                </div>

                <div class="col-lg-4 mb-4">
                    <h5>যোগাযোগ, মোঃ আসফ উদ্দৌলাহ্ (ঝন্টু)</h5>
                    <ul class="footer-links">
                        <li><i class="fas fa-map-marker-alt me-2"></i> <?php echo $school_address; ?></li>
                        <li><i class="fas fa-phone me-2"></i> +৮৮০১৭১৭৮৬১৭৬২, +৮৮০১৯৭৭৮৬১৭৬২,</li>
                        <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="copyright text-center">
            <div class="container">
                <p class="mb-0">&copy; <?php echo date('Y'); ?> আসফ উদ্দৌল্লাহ্. সর্বস্বত্ব সংরক্ষিত।</p>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <a href="#" class="btn back-to-top position-fixed bottom-0 end-0 m-4 rounded-circle" style="width: 45px; height: 45px; line-height: 45px; display: none; background-color: var(--accent-color); color: var(--dark-color);">
        <i class="fas fa-arrow-up"></i>
    </a>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Enhanced JavaScript with Scrolling Notice Features -->
    <script>
        // Set title once and never change it
        document.title = 'নিশাত এডুকেশন সেন্টার';

        // Simple back to top button without intervals
        window.addEventListener('scroll', function() {
            var backToTopBtn = document.querySelector('.back-to-top');
            if (backToTopBtn) {
                if (window.pageYOffset > 300) {
                    backToTopBtn.style.display = 'flex';
                    backToTopBtn.style.justifyContent = 'center';
                    backToTopBtn.style.alignItems = 'center';
                } else {
                    backToTopBtn.style.display = 'none';
                }
            }
        });

        // Enhanced scrolling notice functionality
        document.addEventListener('DOMContentLoaded', function() {
            const scrollingNotice = document.querySelector('.scrolling-notice-content');
            const noticeContainer = document.querySelector('.scrolling-notice-container');

            if (scrollingNotice && noticeContainer) {
                // Add click to pause/resume functionality
                noticeContainer.addEventListener('click', function() {
                    const isAnimationPaused = scrollingNotice.style.animationPlayState === 'paused';
                    scrollingNotice.style.animationPlayState = isAnimationPaused ? 'running' : 'paused';

                    // Visual feedback
                    noticeContainer.style.backgroundColor = isAnimationPaused ? '#f5f5f5' : '#e8f5e8';

                    // Reset background after 1 second
                    setTimeout(() => {
                        noticeContainer.style.backgroundColor = '#f5f5f5';
                    }, 1000);
                });

                // Add keyboard accessibility
                noticeContainer.setAttribute('tabindex', '0');
                noticeContainer.setAttribute('role', 'button');
                noticeContainer.setAttribute('aria-label', 'Click to pause/resume scrolling notice');

                noticeContainer.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        noticeContainer.click();
                    }
                });

                // Add tooltip
                noticeContainer.title = 'ক্লিক করুন স্ক্রোলিং থামাতে/চালু করতে';
            }
        });
    </script>
</body>
</html>
<?php
// Complete the response properly to stop tab loading icon
$content = ob_get_contents();
$content_length = strlen($content);
ob_end_clean();

// Send proper headers
header('Content-Length: ' . $content_length);

// Output content
echo $content;

// Ensure complete response
if (function_exists('fastcgi_finish_request')) {
    fastcgi_finish_request();
} else {
    flush();
}

// Explicitly exit to prevent any additional output
exit();
?>
