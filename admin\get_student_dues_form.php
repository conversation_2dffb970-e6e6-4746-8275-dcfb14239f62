<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Get student ID from request
$studentId = isset($_GET['student_id']) ? intval($_GET['student_id']) : 0;

// Get filter parameters - simplified to only include session, class, department, student, and date
$fromDate = isset($_GET['from_date']) ? $_GET['from_date'] : '';
$toDate = isset($_GET['to_date']) ? $_GET['to_date'] : '';
$sessionId = isset($_GET['session_id']) ? intval($_GET['session_id']) : 0;
$classId = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;
$departmentId = isset($_GET['department_id']) ? intval($_GET['department_id']) : 0;

// Debug: Log received parameters
echo '<script>
console.log("Received filter parameters:");
console.log("from_date: ' . $fromDate . '");
console.log("to_date: ' . $toDate . '");
console.log("session_id: ' . $sessionId . '");
console.log("class_id: ' . $classId . '");
console.log("department_id: ' . $departmentId . '");
console.log("GET params:", ' . json_encode($_GET) . ');
</script>';

// Basic validation
if ($studentId <= 0) {
    echo '<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i> অবৈধ শিক্ষার্থী আইডি</div>';
    exit;
}

// Check database connection
if (!isset($conn) || $conn->connect_error) {
    echo '<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i> ডাটাবেস কানেকশন উপলব্ধ নেই</div>';
    exit;
}

// Add custom CSS for modern design with Hind Siliguri font
echo '<style>
    @import url("https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap");

    * {
        font-family: "Hind Siliguri", sans-serif;
    }

    .avatar {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80px;
        height: 80px;
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .avatar-lg {
        width: 90px;
        height: 90px;
    }

    .avatar i {
        transition: all 0.3s ease;
    }

    .avatar:hover i {
        transform: scale(1.1);
    }

    .rounded-3 {
        border-radius: 0.75rem !important;
    }

    .rounded-4 {
        border-radius: 1rem !important;
    }

    .rounded-circle {
        border-radius: 50% !important;
    }

    .shadow-sm {
        box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05) !important;
    }

    .shadow-hover {
        transition: all 0.3s ease;
    }

    .shadow-hover:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1) !important;
        transform: translateY(-2px);
    }

    .bg-opacity-10 {
        --bs-bg-opacity: 0.1;
    }

    .bg-opacity-20 {
        --bs-bg-opacity: 0.2;
    }

    .gap-3 {
        gap: 1rem !important;
    }

    .table {
        --bs-table-hover-bg: rgba(0, 0, 0, 0.02);
        border-collapse: separate;
        border-spacing: 0;
        margin-bottom: 0;
    }

    .table-container {
        border-radius: 1rem;
        overflow: hidden;
        box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
    }

    .table-container:hover {
        box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.12);
    }

    .table th {
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.8rem;
        letter-spacing: 0.5px;
        padding: 1rem 0.75rem;
        background-color: #f8f9fa;
        border-bottom: 2px solid #e9ecef;
    }

    .table td {
        padding: 1rem 0.75rem;
        vertical-align: middle;
        border-bottom: 1px solid #f1f1f1;
    }

    .table tr:last-child td {
        border-bottom: none;
    }

    .table tr {
        transition: all 0.2s ease;
    }

    .table tr:hover {
        background-color: rgba(0, 123, 255, 0.03);
    }

    .badge {
        font-weight: 500;
        letter-spacing: 0.5px;
        padding: 0.5rem 1rem;
        border-radius: 50rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .badge:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .bg-danger {
        background-color: #ff6b6b !important;
    }

    .bg-warning {
        background-color: #feca57 !important;
    }

    .bg-success {
        background-color: #1dd1a1 !important;
    }

    .bg-primary {
        background-color: #54a0ff !important;
    }

    .bg-info {
        background-color: #00d2d3 !important;
    }

    .text-danger {
        color: #ff6b6b !important;
    }

    .text-warning {
        color: #ff9f43 !important;
    }

    .text-success {
        color: #10ac84 !important;
    }

    .text-primary {
        color: #2e86de !important;
    }

    .text-info {
        color: #01a3a4 !important;
    }

    .card {
        border: none;
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-3px);
        box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1) !important;
    }

    .form-check-input {
        width: 1.2rem;
        height: 1.2rem;
        cursor: pointer;
    }

    .form-check-input:checked {
        background-color: #2e86de;
        border-color: #2e86de;
    }

    .form-check-label {
        cursor: pointer;
    }

    .form-switch .form-check-input {
        width: 2.5rem;
        height: 1.25rem;
    }

    .fw-medium {
        font-weight: 500 !important;
    }

    .fw-semibold {
        font-weight: 600 !important;
    }

    .alert {
        border: none;
        border-radius: 0.75rem;
    }

    .due-amount {
        font-weight: 700;
        color: #ff6b6b;
    }

    .fee-type {
        font-weight: 500;
        color: #2e86de;
    }

    .overdue-indicator {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        background-color: rgba(255, 107, 107, 0.1);
        border-radius: 0.25rem;
        font-size: 0.75rem;
        color: #ff6b6b;
        margin-top: 0.25rem;
    }

    .student-info-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 1rem;
    }

    .summary-card {
        background: linear-gradient(135deg, #f1f9ff 0%, #dcf0ff 100%);
        border-radius: 1rem;
    }
</style>';

try {
    // Get student information
    $studentQuery = "SELECT s.*, c.class_name, ss.session_name
                    FROM students s
                    LEFT JOIN classes c ON s.class_id = c.id
                    LEFT JOIN sessions ss ON s.session_id = ss.id
                    WHERE s.id = ?";

    $stmt = $conn->prepare($studentQuery);
    if (!$stmt) {
        throw new Exception("Prepare failed: " . $conn->error);
    }

    $stmt->bind_param('i', $studentId);
    $stmt->execute();
    $studentResult = $stmt->get_result();

    if ($studentResult->num_rows === 0) {
        echo '<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i> শিক্ষার্থী পাওয়া যায়নি</div>';
        exit;
    }

    $student = $studentResult->fetch_assoc();

    // Get all due fees for the student with filters
    $feesQuery = "SELECT f.*, c.class_name, ss.session_name, d.department_name
                 FROM fees f
                 LEFT JOIN students s ON f.student_id = s.id
                 LEFT JOIN classes c ON s.class_id = c.id
                 LEFT JOIN sessions ss ON s.session_id = ss.id
                 LEFT JOIN departments d ON s.department_id = d.id
                 WHERE f.student_id = ? AND (f.payment_status = 'due' OR f.payment_status = 'partial')";

    $params = [$studentId];
    $types = "i";

    // Apply simplified filters
    if (!empty($fromDate)) {
        $feesQuery .= " AND f.due_date >= ?";
        $params[] = $fromDate;
        $types .= "s";
    }

    if (!empty($toDate)) {
        $feesQuery .= " AND f.due_date <= ?";
        $params[] = $toDate;
        $types .= "s";
    }

    if ($sessionId > 0) {
        $feesQuery .= " AND s.session_id = ?";
        $params[] = $sessionId;
        $types .= "i";
    }

    if ($classId > 0) {
        $feesQuery .= " AND s.class_id = ?";
        $params[] = $classId;
        $types .= "i";
    }

    if ($departmentId > 0) {
        $feesQuery .= " AND s.department_id = ?";
        $params[] = $departmentId;
        $types .= "i";
    }

    // Default sorting by due date
    $feesQuery .= " ORDER BY f.due_date ASC";

    // Debug: Log the final query and parameters
    echo '<script>
    console.log("Final SQL query: ' . str_replace('"', '\"', $feesQuery) . '");
    console.log("Parameter types: ' . $types . '");
    console.log("Parameters:", ' . json_encode($params) . ');
    </script>';

    $stmt = $conn->prepare($feesQuery);
    if (!$stmt) {
        throw new Exception("Prepare fees query failed: " . $conn->error);
    }

    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $feesResult = $stmt->get_result();

    $fees = [];
    $totalDue = 0;

    while ($fee = $feesResult->fetch_assoc()) {
        $dueAmount = $fee['amount'] - $fee['paid'];
        $totalDue += $dueAmount;
        $fees[] = $fee;
    }

    // Display student info in a beautiful modern card
    echo '<div class="card shadow-hover student-info-card mb-4 rounded-4">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0 me-4">
                        <div class="avatar avatar-lg bg-primary bg-opacity-20 text-primary rounded-circle shadow-sm">
                            <i class="fas fa-user-graduate fa-2x"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <h3 class="mb-2 fw-semibold">' . htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) . '</h3>
                        <div class="d-flex flex-wrap gap-3 mb-2">
                            <div class="badge bg-info bg-opacity-10 text-info px-3 py-2">
                                <i class="fas fa-id-card me-1"></i> ' . htmlspecialchars($student['student_id']) . '
                            </div>
                            <div class="badge bg-success bg-opacity-10 text-success px-3 py-2">
                                <i class="fas fa-graduation-cap me-1"></i> ' . htmlspecialchars($student['class_name'] ?? 'N/A') . '
                            </div>
                            <div class="badge bg-primary bg-opacity-10 text-primary px-3 py-2">
                                <i class="fas fa-calendar-alt me-1"></i> ' . htmlspecialchars($student['session_name'] ?? 'N/A') . '
                            </div>
                        </div>
                        <div class="text-muted small">
                            <i class="fas fa-info-circle me-1"></i>
                            এই শিক্ষার্থীর মোট ' . count($fees) . 'টি বকেয়া ফি রয়েছে, মোট পরিমাণ:
                            <span class="fw-semibold text-danger">৳' . number_format($totalDue, 2) . '</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>';

    // Simplified Filter Section
    echo '<div class="card mb-4 rounded-4 shadow-sm">
            <div class="card-header bg-light p-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-filter me-2 text-primary"></i> ফিল্টার অপশন
                    </h6>
                    <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#advancedSearchCollapse" aria-expanded="false" aria-controls="advancedSearchCollapse" id="toggleAdvancedSearch">
                        <i class="fas fa-chevron-down" id="advancedSearchIcon"></i>
                    </button>
                </div>
            </div>
            <div class="collapse" id="advancedSearchCollapse">
                <div class="card-body p-4">
                    <form id="duesFilterForm" method="GET" action="">
                        <input type="hidden" name="student_id" value="' . $studentId . '">
                        <div class="row g-3">
                            <!-- Session Filter -->
                            <div class="col-md-6 col-lg-4">
                                <label class="form-label">সেশন</label>
                                <select class="form-select" name="session_id" id="sessionFilter">
                                    <option value="0">সকল সেশন</option>';

                                    // Get sessions
                                    $sessionsQuery = "SELECT id, session_name FROM sessions ORDER BY session_name";
                                    $sessionsResult = $conn->query($sessionsQuery);

                                    while ($session = $sessionsResult->fetch_assoc()) {
                                        $selected = ($sessionId == $session['id']) ? 'selected' : '';
                                        echo '<option value="' . $session['id'] . '" ' . $selected . '>' . htmlspecialchars($session['session_name']) . '</option>';
                                    }

                            echo '</select>
                            </div>

                            <!-- Class Filter -->
                            <div class="col-md-6 col-lg-4">
                                <label class="form-label">ক্লাস</label>
                                <select class="form-select" name="class_id" id="classFilter">
                                    <option value="0">সকল ক্লাস</option>';

                                    // Get classes
                                    $classesQuery = "SELECT id, class_name FROM classes ORDER BY class_name";
                                    $classesResult = $conn->query($classesQuery);

                                    while ($class = $classesResult->fetch_assoc()) {
                                        $selected = ($classId == $class['id']) ? 'selected' : '';
                                        echo '<option value="' . $class['id'] . '" ' . $selected . '>' . htmlspecialchars($class['class_name']) . '</option>';
                                    }

                            echo '</select>
                            </div>

                            <!-- Department Filter -->
                            <div class="col-md-6 col-lg-4">
                                <label class="form-label">বিভাগ</label>
                                <select class="form-select" name="department_id" id="departmentFilter">
                                    <option value="0">সকল বিভাগ</option>';

                                    // Get departments
                                    $departmentsQuery = "SELECT id, department_name FROM departments ORDER BY department_name";
                                    $departmentsResult = $conn->query($departmentsQuery);

                                    while ($department = $departmentsResult->fetch_assoc()) {
                                        $selected = ($departmentId == $department['id']) ? 'selected' : '';
                                        echo '<option value="' . $department['id'] . '" ' . $selected . '>' . htmlspecialchars($department['department_name']) . '</option>';
                                    }

                            echo '</select>
                            </div>

                            <!-- Due Date Range -->
                            <div class="col-md-6 col-lg-4">
                                <label class="form-label">বকেয়া তারিখ থেকে</label>
                                <input type="date" class="form-control" name="from_date" id="fromDateFilter" value="' . htmlspecialchars($fromDate) . '">
                            </div>

                            <div class="col-md-6 col-lg-4">
                                <label class="form-label">বকেয়া তারিখ পর্যন্ত</label>
                                <input type="date" class="form-control" name="to_date" id="toDateFilter" value="' . htmlspecialchars($toDate) . '">
                            </div>

                            <!-- Action Buttons -->
                            <div class="col-12 mt-4 d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-1"></i> ফিল্টার করুন
                                </button>
                                <button type="button" class="btn btn-outline-secondary" id="resetFiltersBtn">
                                    <i class="fas fa-undo me-1"></i> রিসেট করুন
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>';

    if (count($fees) === 0) {
        echo '<div class="alert shadow-sm border-0 rounded-4 p-4" style="background: linear-gradient(135deg, #fff9c4 0%, #fffde7 100%);">
                <div class="d-flex align-items-center">
                    <div class="me-4">
                        <div class="rounded-circle bg-warning bg-opacity-20 p-3 shadow-sm">
                            <i class="fas fa-check-circle fa-2x text-warning"></i>
                        </div>
                    </div>
                    <div>
                        <h4 class="mb-2 fw-semibold text-warning">কোন বকেয়া ফি নেই</h4>
                        <p class="mb-0 text-muted">এই শিক্ষার্থীর সকল ফি পরিশোধ করা হয়েছে। কোন বকেয়া ফি রেকর্ড পাওয়া যায়নি।</p>
                    </div>
                </div>
              </div>';
    } else {
        // Beautiful summary card before table
        echo '<div class="card shadow-hover summary-card mb-4 rounded-4">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-7">
                            <div class="d-flex align-items-center">
                                <div class="rounded-circle bg-primary bg-opacity-20 p-3 me-3 shadow-sm">
                                    <i class="fas fa-file-invoice-dollar fa-2x text-primary"></i>
                                </div>
                                <div>
                                    <h4 class="mb-1 fw-semibold">মোট বকেয়া: <span class="text-primary">' . count($fees) . 'টি ফি</span></h4>
                                    <p class="mb-0 text-muted">মোট বকেয়া পরিমাণ: <span class="fw-semibold text-danger">৳' . number_format($totalDue, 2) . '</span></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-5 text-md-end mt-3 mt-md-0">
                            <p class="text-muted mb-2">পরিশোধ করতে চাওয়া ফি নির্বাচন করুন</p>
                            <div class="form-check form-switch d-inline-block">
                                <input class="form-check-input" type="checkbox" id="select_all_dues_table" role="switch">
                                <label class="form-check-label fw-medium" for="select_all_dues_table">
                                    <i class="fas fa-check-square me-1"></i> সবগুলো নির্বাচন করুন
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>';

        // Create beautiful modern dues table
        echo '<div class="table-container">
            <table class="table table-hover align-middle bg-white">
                <thead>
                    <tr>
                        <th width="60" class="ps-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="select_all_dues_table_header">
                            </div>
                        </th>
                        <th>ফি ধরন</th>
                        <th class="text-end">মোট</th>
                        <th class="text-end">পরিশোধিত</th>
                        <th class="text-end">বকেয়া</th>
                        <th class="text-center">তারিখ</th>
                        <th class="text-center pe-4">স্ট্যাটাস</th>
                    </tr>
                </thead>
                <tbody>';

        foreach ($fees as $index => $fee) {
            $statusClass = $fee['payment_status'] === 'due' ? 'bg-danger' : 'bg-warning';
            $statusText = $fee['payment_status'] === 'due' ? 'বকেয়া' : 'আংশিক';
            $dueAmount = $fee['amount'] - $fee['paid'];
            $dueDateFormatted = date('d/m/Y', strtotime($fee['due_date']));
            $isOverdue = strtotime($fee['due_date']) < strtotime('today');
            $rowClass = $isOverdue ? 'table-danger bg-opacity-10' : '';

            echo '<tr class="' . $rowClass . '">
                <td class="ps-4">
                    <div class="form-check">
                        <input class="form-check-input due-fee-checkbox" type="checkbox" name="fee_ids[]" value="' . $fee['id'] . '" data-index="' . $index . '" data-amount="' . $fee['amount'] . '" data-paid="' . $fee['paid'] . '" data-due="' . $dueAmount . '" onchange="updateSelectedDues()">
                        <input type="hidden" name="payment_amounts[' . $index . ']" value="' . $dueAmount . '" class="payment-amount-input">
                    </div>
                </td>
                <td>
                    <div class="fee-type">' . htmlspecialchars($fee['fee_type']) . '</div>
                    ' . ($isOverdue ? '<div class="overdue-indicator"><i class="fas fa-exclamation-circle me-1"></i>সময়সীমা অতিক্রান্ত</div>' : '') . '
                </td>
                <td class="text-end fw-medium">৳ ' . number_format($fee['amount'], 2) . '</td>
                <td class="text-end text-muted">৳ ' . number_format($fee['paid'], 2) . '</td>
                <td class="text-end due-amount">৳ ' . number_format($dueAmount, 2) . '</td>
                <td class="text-center">
                    <div class="badge bg-light text-dark px-3 py-2 shadow-sm">
                        <i class="far fa-calendar-alt me-1"></i> ' . $dueDateFormatted . '
                    </div>
                </td>
                <td class="text-center pe-4">
                    <span class="badge ' . $statusClass . ' px-3 py-2">' . $statusText . '</span>
                </td>
            </tr>';

            // Also add hidden inputs to parent form
            echo '<script>
                (function() {
                    try {
                        const parentForm = window.parent.document.getElementById("bulkPaymentForm");
                        if (parentForm) {
                            // Create hidden input for fee_ids
                            let hiddenInput = document.createElement("input");
                            hiddenInput.type = "hidden";
                            hiddenInput.name = "fee_ids[]";
                            hiddenInput.value = "' . $fee['id'] . '";
                            hiddenInput.className = "dynamic-fee-input fee-id-' . $fee['id'] . '";
                            hiddenInput.disabled = true; // Initially disabled
                            parentForm.appendChild(hiddenInput);

                            // Create hidden input for payment_amounts
                            let amountInput = document.createElement("input");
                            amountInput.type = "hidden";
                            amountInput.name = "payment_amounts[' . $index . ']";
                            amountInput.value = "' . ($fee['amount'] - $fee['paid']) . '";
                            amountInput.className = "dynamic-amount-input amount-id-' . $fee['id'] . '";
                            amountInput.disabled = true; // Initially disabled
                            parentForm.appendChild(amountInput);
                        }
                    } catch (e) {
                        console.error("Error adding hidden inputs to parent form:", e);
                    }
                })();
            </script>';
        }

        echo '</tbody>
            </table>
        </div>';

        // Add JavaScript for handling checkboxes
        echo '<script>
            document.addEventListener("DOMContentLoaded", function() {
                console.log("DOM fully loaded");

                // Get elements
                const selectAllCheckbox = document.getElementById("select_all_dues_table");
                const selectAllHeaderCheckbox = document.getElementById("select_all_dues_table_header");
                const dueCheckboxes = document.querySelectorAll(".due-fee-checkbox");

                // Function to toggle all checkboxes
                function toggleAllCheckboxes(checked) {
                    console.log("Toggling all checkboxes:", checked);
                    dueCheckboxes.forEach(checkbox => {
                        checkbox.checked = checked;
                    });

                    // Keep both "select all" checkboxes in sync
                    if (selectAllCheckbox) selectAllCheckbox.checked = checked;
                    if (selectAllHeaderCheckbox) selectAllHeaderCheckbox.checked = checked;

                    updateParentWindow();
                }

                // Add event listener to main "select all" checkbox (in card)
                if (selectAllCheckbox) {
                    console.log("Main select all checkbox found");
                    selectAllCheckbox.addEventListener("change", function() {
                        toggleAllCheckboxes(this.checked);
                    });
                }

                // Add event listener to header "select all" checkbox (in table header)
                if (selectAllHeaderCheckbox) {
                    console.log("Header select all checkbox found");
                    selectAllHeaderCheckbox.addEventListener("change", function() {
                        toggleAllCheckboxes(this.checked);
                    });
                }

                // Add event listeners to individual checkboxes
                dueCheckboxes.forEach(checkbox => {
                    checkbox.addEventListener("change", function() {
                        console.log("Checkbox changed:", this.checked);
                        updateParentWindow();

                        // Check if all checkboxes are checked
                        const allChecked = Array.from(dueCheckboxes).every(cb => cb.checked);
                        if (selectAllCheckbox) {
                            selectAllCheckbox.checked = allChecked;
                        }
                    });
                });

                // Handle "select all" button in parent window
                try {
                    const parentSelectAllBtn = window.parent.document.getElementById("select_all_dues");
                    if (parentSelectAllBtn) {
                        console.log("Parent select all button found");
                        parentSelectAllBtn.disabled = dueCheckboxes.length === 0;

                        // Remove any existing event listeners
                        const newBtn = parentSelectAllBtn.cloneNode(true);
                        parentSelectAllBtn.parentNode.replaceChild(newBtn, parentSelectAllBtn);

                        // Add new event listener
                        newBtn.addEventListener("click", function() {
                            console.log("Parent select all button clicked");
                            if (selectAllCheckbox) {
                                selectAllCheckbox.checked = true;
                                dueCheckboxes.forEach(checkbox => {
                                    checkbox.checked = true;
                                });
                                updateParentWindow();
                            }
                        });
                    }
                } catch (e) {
                    console.error("Error setting up parent button:", e);
                }

                // Initial update
                updateParentWindow();
            });

            // Function to update parent window elements
            function updateParentWindow() {
                try {
                    console.log("Updating parent window");

                    // Calculate selected fees and total amount
                    const selectedCheckboxes = document.querySelectorAll(".due-fee-checkbox:checked");
                    const count = selectedCheckboxes.length;
                    let totalAmount = 0;

                    // Get all dynamic inputs in parent form
                    const parent = window.parent.document;
                    const dynamicFeeInputs = parent.querySelectorAll(".dynamic-fee-input");
                    const dynamicAmountInputs = parent.querySelectorAll(".dynamic-amount-input");

                    // First disable all dynamic inputs
                    dynamicFeeInputs.forEach(input => {
                        input.disabled = true;
                    });
                    dynamicAmountInputs.forEach(input => {
                        input.disabled = true;
                    });

                    // Enable only selected fee inputs
                    selectedCheckboxes.forEach(checkbox => {
                        const feeId = checkbox.value;
                        const amount = parseFloat(checkbox.getAttribute("data-due") || 0);
                        totalAmount += amount;

                        // Enable corresponding hidden inputs in parent form
                        const feeInput = parent.querySelector(`.fee-id-${feeId}`);
                        const amountInput = parent.querySelector(`.amount-id-${feeId}`);

                        if (feeInput) {
                            feeInput.disabled = false;
                            console.log(`Enabled fee input for ID ${feeId}`);
                        }

                        if (amountInput) {
                            amountInput.disabled = false;
                            console.log(`Enabled amount input for ID ${feeId}`);
                        }
                    });

                    console.log("Selected count:", count, "Total amount:", totalAmount);

                    // Update count and amount displays
                    const countElement = parent.getElementById("selected_fees_count");
                    const amountElement = parent.getElementById("total_due_amount");

                    if (countElement) countElement.textContent = count;
                    if (amountElement) amountElement.textContent = totalAmount.toFixed(2);

                    // Enable/disable submit button
                    const submitButton = parent.getElementById("bulk_payment_submit");
                    if (submitButton) {
                        submitButton.disabled = count === 0;
                        console.log("Submit button disabled:", count === 0);
                    }

                    // Show/hide payment details
                    const paymentDetails = parent.getElementById("payment_details_container");
                    if (paymentDetails) {
                        paymentDetails.style.display = count > 0 ? "block" : "none";
                    }

                    // Update partial payment amount
                    const partialAmount = parent.getElementById("partial_payment_amount");
                    if (partialAmount) {
                        partialAmount.max = totalAmount;
                        partialAmount.value = totalAmount;
                    }
                } catch (e) {
                    console.error("Error updating parent window:", e);
                }
            }
        </script>';

        // Show payment details container
        echo '<script>
            // Show payment details container
            if (document.getElementById("payment_details_container")) {
                document.getElementById("payment_details_container").style.display = "block";
            }

            // Add form submission handler to parent window
            try {
                const parentForm = window.parent.document.getElementById("student_dues_payment_form");
                if (parentForm) {
                    // Remove any existing event listeners
                    const newForm = parentForm.cloneNode(true);
                    parentForm.parentNode.replaceChild(newForm, parentForm);

                    // Add new event listener
                    newForm.addEventListener("submit", function(e) {
                        e.preventDefault();

                        // Get selected fees
                        const selectedCheckboxes = document.querySelectorAll(".due-fee-checkbox:checked");
                        if (selectedCheckboxes.length === 0) {
                            alert("কমপক্ষে একটি বকেয়া নির্বাচন করুন!");
                            return;
                        }

                        // Get form data
                        const formData = new FormData(this);
                        formData.append("action", "process_dues_payment");

                        // Debug form data
                        console.log("Form data being submitted:");
                        for (let pair of formData.entries()) {
                            console.log(pair[0] + ": " + pair[1]);
                        }

                        // Disable submit button and show loading
                        const submitBtn = this.querySelector("button[type=submit]");
                        if (submitBtn) {
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = \'<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> প্রক্রিয়াকরণ হচ্ছে...\';
                        }

                        // Send form data to server with simple path
                        fetch("ajax_handler.php", {
                            method: "POST",
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.status === "success") {
                                // Show success message
                                alert(data.message || "পেমেন্ট সফলভাবে যোগ করা হয়েছে!");

                                // Redirect to receipt page if available
                                if (data.receipt_id) {
                                    window.parent.location.href = "student_payment_receipt.php?student_id=' . $studentId . '&payment_ids=" + data.receipt_id;
                                } else {
                                    // Reload page
                                    window.parent.location.reload();
                                }
                            } else {
                                // Show error message
                                alert(data.message || "পেমেন্ট যোগ করতে সমস্যা হয়েছে!");

                                // Re-enable submit button
                                if (submitBtn) {
                                    submitBtn.disabled = false;
                                    submitBtn.innerHTML = \'<i class="fas fa-save me-1"></i> পেমেন্ট যোগ করুন\';
                                }
                            }
                        })
                        .catch(error => {
                            console.error("Error processing payment:", error);
                            alert("পেমেন্ট যোগ করতে সমস্যা হয়েছে!");

                            // Re-enable submit button
                            if (submitBtn) {
                                submitBtn.disabled = false;
                                submitBtn.innerHTML = \'<i class="fas fa-save me-1"></i> পেমেন্ট যোগ করুন\';
                            }
                        });
                    });
                }
            } catch (e) {
                console.error("Error setting up form submission:", e);
            }
        </script>';
    }

} catch (Exception $e) {
    echo '<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i> ডাটাবেস এরর: ' . $e->getMessage() . '</div>';
}

// Add JavaScript for advanced search functionality
echo '<script>
document.addEventListener("DOMContentLoaded", function() {
    // Toggle advanced search section
    const toggleAdvancedSearch = document.getElementById("toggleAdvancedSearch");
    const advancedSearchIcon = document.getElementById("advancedSearchIcon");
    const advancedSearchCollapse = document.getElementById("advancedSearchCollapse");

    // Check if any filter is applied - simplified
    const hasActiveFilters = ' . (
        !empty($fromDate) ||
        !empty($toDate) ||
        $sessionId > 0 ||
        $classId > 0 ||
        $departmentId > 0 ? 'true' : 'false'
    ) . ';

    // Show advanced search section if filters are applied
    if (hasActiveFilters) {
        // Use setTimeout to ensure the DOM is fully loaded
        setTimeout(() => {
            try {
                const bsCollapse = new bootstrap.Collapse(advancedSearchCollapse, {
                    toggle: true
                });

                if (advancedSearchIcon) {
                    advancedSearchIcon.classList.remove("fa-chevron-down");
                    advancedSearchIcon.classList.add("fa-chevron-up");
                }

                // Add active filter badge
                const filterBadge = document.createElement("span");
                filterBadge.className = "badge bg-primary ms-2";
                filterBadge.textContent = "ফিল্টার সক্রিয়";

                const filterHeading = document.querySelector(".card-header h6");
                if (filterHeading) {
                    filterHeading.appendChild(filterBadge);
                }
            } catch (e) {
                console.error("Error showing filter section:", e);
            }
        }, 100);
    }

    // Toggle icon when advanced search is expanded/collapsed
    if (advancedSearchCollapse) {
        advancedSearchCollapse.addEventListener("show.bs.collapse", function() {
            if (advancedSearchIcon) {
                advancedSearchIcon.classList.remove("fa-chevron-down");
                advancedSearchIcon.classList.add("fa-chevron-up");
            }
        });

        advancedSearchCollapse.addEventListener("hide.bs.collapse", function() {
            if (advancedSearchIcon) {
                advancedSearchIcon.classList.remove("fa-chevron-up");
                advancedSearchIcon.classList.add("fa-chevron-down");
            }
        });
    }

    // Reset filters button
    const resetFiltersBtn = document.getElementById("resetFiltersBtn");
    if (resetFiltersBtn) {
        resetFiltersBtn.addEventListener("click", function() {
            console.log("Reset filters button clicked");

            // Clear all filter inputs except student_id
            const form = document.getElementById("duesFilterForm");
            const inputs = form.querySelectorAll("input:not([name=\'student_id\']), select");

            inputs.forEach(input => {
                if (input.tagName === "SELECT") {
                    input.selectedIndex = 0;
                    console.log("Reset select:", input.name);
                } else if (input.type === "date" || input.type === "number") {
                    input.value = "";
                    console.log("Reset input:", input.name);
                }
            });

            // Submit the form
            console.log("Submitting form with reset filters");
            form.submit();
        });
    }

    // Add filter badge if filters are applied
    if (hasActiveFilters) {
        const filterCount = document.createElement("span");
        filterCount.className = "badge bg-primary ms-2";
        filterCount.textContent = "ফিল্টার সক্রিয়";

        const filterHeading = document.querySelector(".card-header h6");
        if (filterHeading) {
            filterHeading.appendChild(filterCount);
        }
    }
});
</script>';
?>
