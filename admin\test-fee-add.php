<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    die('Unauthorized access');
}

$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_add_fee'])) {
    $studentId = intval($_POST['student_id']);
    $feeType = $_POST['fee_type'];
    $amount = floatval($_POST['amount']);
    $dueDate = $_POST['due_date'];
    $sessionId = intval($_POST['session_id']);
    $classId = intval($_POST['class_id']);
    
    echo "<h3>🔍 Debug Info:</h3>";
    echo "<ul>";
    echo "<li>Student ID: $studentId</li>";
    echo "<li>Fee Type: $feeType</li>";
    echo "<li>Amount: $amount</li>";
    echo "<li>Due Date: $dueDate</li>";
    echo "<li>Session ID: $sessionId</li>";
    echo "<li>Class ID: $classId</li>";
    echo "</ul>";
    
    if ($studentId > 0 && !empty($feeType) && $amount > 0) {
        // Insert fee directly
        $sql = "INSERT INTO fees (student_id, session_id, class_id, fee_type, amount, due_date, status, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, 'unpaid', NOW())";
        
        $stmt = $conn->prepare($sql);
        if ($stmt) {
            $stmt->bind_param("iiisds", $studentId, $sessionId, $classId, $feeType, $amount, $dueDate);
            
            if ($stmt->execute()) {
                $message = "✅ Fee added successfully! Fee ID: " . $conn->insert_id;
                $messageType = 'success';
            } else {
                $message = "❌ Error adding fee: " . $stmt->error;
                $messageType = 'error';
            }
            $stmt->close();
        } else {
            $message = "❌ Error preparing statement: " . $conn->error;
            $messageType = 'error';
        }
    } else {
        $message = "❌ Invalid form data";
        $messageType = 'error';
    }
}

// Get students for dropdown
$studentsQuery = "SELECT s.*, c.class_name, sess.session_name 
                  FROM students s 
                  LEFT JOIN classes c ON s.class_id = c.id 
                  LEFT JOIN sessions sess ON s.session_id = sess.id 
                  ORDER BY s.name LIMIT 20";
$studentsResult = $conn->query($studentsQuery);

// Get sessions
$sessionsQuery = "SELECT * FROM sessions ORDER BY id DESC";
$sessionsResult = $conn->query($sessionsQuery);

// Get classes
$classesQuery = "SELECT * FROM classes ORDER BY class_name";
$classesResult = $conn->query($classesQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <title>Test Fee Addition</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .message { padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>

<h1>🧪 Test Fee Addition</h1>

<?php if ($message): ?>
    <div class="message <?php echo $messageType; ?>">
        <?php echo $message; ?>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-md-6">
        <h2>Direct Fee Addition Test</h2>
        <form method="post" action="">
            <div class="mb-3">
                <label class="form-label">Select Student:</label>
                <select name="student_id" class="form-control" required>
                    <option value="">Choose Student</option>
                    <?php if ($studentsResult && $studentsResult->num_rows > 0): ?>
                        <?php while ($student = $studentsResult->fetch_assoc()): ?>
                            <option value="<?php echo $student['id']; ?>">
                                <?php echo htmlspecialchars($student['name']); ?> 
                                (Roll: <?php echo $student['roll_number']; ?>) 
                                - <?php echo isset($student['class_name']) ? $student['class_name'] : 'No Class'; ?>
                            </option>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <option value="">No students found</option>
                    <?php endif; ?>
                </select>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Session:</label>
                <select name="session_id" class="form-control" required>
                    <option value="">Choose Session</option>
                    <?php if ($sessionsResult && $sessionsResult->num_rows > 0): ?>
                        <?php while ($session = $sessionsResult->fetch_assoc()): ?>
                            <option value="<?php echo $session['id']; ?>">
                                <?php echo htmlspecialchars($session['session_name']); ?>
                            </option>
                        <?php endwhile; ?>
                    <?php endif; ?>
                </select>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Class:</label>
                <select name="class_id" class="form-control" required>
                    <option value="">Choose Class</option>
                    <?php if ($classesResult && $classesResult->num_rows > 0): ?>
                        <?php while ($class = $classesResult->fetch_assoc()): ?>
                            <option value="<?php echo $class['id']; ?>">
                                <?php echo htmlspecialchars($class['class_name']); ?>
                            </option>
                        <?php endwhile; ?>
                    <?php endif; ?>
                </select>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Fee Type:</label>
                <select name="fee_type" class="form-control" required>
                    <option value="">Choose Fee Type</option>
                    <option value="tuition">Tuition Fee</option>
                    <option value="admission">Admission Fee</option>
                    <option value="exam">Exam Fee</option>
                    <option value="library">Library Fee</option>
                    <option value="transport">Transport Fee</option>
                    <option value="other">Other</option>
                </select>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Amount:</label>
                <input type="number" name="amount" class="form-control" value="1000" step="0.01" required>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Due Date:</label>
                <input type="date" name="due_date" class="form-control" value="<?php echo date('Y-m-d', strtotime('+30 days')); ?>" required>
            </div>
            
            <button type="submit" name="test_add_fee" class="btn btn-primary">
                🧪 Test Add Fee
            </button>
        </form>
    </div>
    
    <div class="col-md-6">
        <h2>📊 Database Status</h2>
        <ul>
            <li><strong>Students:</strong> <?php 
                $count = $conn->query("SELECT COUNT(*) as count FROM students")->fetch_assoc()['count'];
                echo $count;
            ?></li>
            <li><strong>Sessions:</strong> <?php 
                $count = $conn->query("SELECT COUNT(*) as count FROM sessions")->fetch_assoc()['count'];
                echo $count;
            ?></li>
            <li><strong>Classes:</strong> <?php 
                $count = $conn->query("SELECT COUNT(*) as count FROM classes")->fetch_assoc()['count'];
                echo $count;
            ?></li>
            <li><strong>Existing Fees:</strong> <?php 
                $count = $conn->query("SELECT COUNT(*) as count FROM fees")->fetch_assoc()['count'];
                echo $count;
            ?></li>
        </ul>
        
        <h3>🔗 Quick Links</h3>
        <a href="check-database.php" class="btn btn-info">Database Check</a><br><br>
        <a href="fee_management.php" class="btn btn-success">Fee Management</a><br><br>
        <a href="debug-fee.php" class="btn btn-warning">Debug Tool</a>
    </div>
</div>

</body>
</html>
