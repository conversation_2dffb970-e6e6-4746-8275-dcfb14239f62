<?php
// Database Connection
require_once 'includes/dbh.inc.php';

echo "<h1>Notices Page Fix Tool</h1>";

// Check if the session protection in admin/notices.php is preventing access
echo "<h3>Step 1: Analyzing admin/notices.php</h3>";

$noticesFile = "admin/notices.php";
if (file_exists($noticesFile)) {
    echo "<p style='color:green;'>The admin/notices.php file exists.</p>";
    
    // Read the first 20 lines of the file to check session validation
    $fileContent = file($noticesFile, FILE_IGNORE_NEW_LINES);
    $sessionCheckLines = array_slice($fileContent, 0, 20);
    
    echo "<p>Examining session check in notices.php:</p>";
    echo "<pre style='background-color: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
    foreach ($sessionCheckLines as $line) {
        echo htmlspecialchars($line) . "\n";
    }
    echo "</pre>";
    
    // Look for session check code
    $sessionCheckFound = false;
    foreach ($sessionCheckLines as $line) {
        if (strpos($line, 'userType') !== false && strpos($line, 'admin') !== false) {
            $sessionCheckFound = true;
            echo "<p style='color:orange;'>Session validation for admin found in the file. This might be preventing access.</p>";
            break;
        }
    }
    
    if (!$sessionCheckFound) {
        echo "<p>No strict admin validation found in the first 20 lines.</p>";
    }
} else {
    echo "<p style='color:red;'>The admin/notices.php file does not exist!</p>";
}

// Create a simple notices table if it doesn't exist
echo "<h3>Step 2: Checking/Creating Notices Table</h3>";

$sql = "SHOW TABLES LIKE 'notices'";
$result = $conn->query($sql);

if ($result->num_rows == 0) {
    // Table doesn't exist, create it
    $sql = "CREATE TABLE notices (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        date DATE NOT NULL,
        added_by VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "<p style='color:green;'>Notices table created successfully!</p>";
        
        // Add sample notice
        $sql = "INSERT INTO notices (title, content, date, added_by) VALUES 
        ('Welcome to College Management System', 'This is a sample notice. You can add more notices from the admin panel.', CURDATE(), 'admin')";
        
        if ($conn->query($sql) === TRUE) {
            echo "<p style='color:green;'>Sample notice added successfully!</p>";
        } else {
            echo "<p style='color:red;'>Error adding sample notice: " . $conn->error . "</p>";
        }
    } else {
        echo "<p style='color:red;'>Error creating notices table: " . $conn->error . "</p>";
    }
} else {
    echo "<p style='color:green;'>Notices table already exists.</p>";
}

// Create a temporary access file
echo "<h3>Step 3: Creating Direct Notice Access Page</h3>";

$directAccessFile = "temp_notices.php";
$content = '<?php
// This is a temporary direct access file for notices
require_once "includes/dbh.inc.php";

echo "<h1>All Notices</h1>";

$sql = "SELECT * FROM notices ORDER BY date DESC";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    echo "<table border=\'1\' cellpadding=\'10\' style=\'border-collapse: collapse; width: 100%;\'>";
    echo "<tr><th>Title</th><th>Date</th><th>Content</th></tr>";
    
    while($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row["title"]) . "</td>";
        echo "<td>" . $row["date"] . "</td>";
        echo "<td>" . nl2br(htmlspecialchars($row["content"])) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>No notices found</p>";
}

echo "<p><a href=\'index.php\'>Back to Home</a></p>";
?>';

file_put_contents($directAccessFile, $content);
echo "<p style='color:green;'>Created a temporary notices access page at: <a href='temp_notices.php'>temp_notices.php</a></p>";

// Create a simple bypass page for admin access
$adminBypassFile = "admin_notices_bypass.php";
$bypassContent = '<?php
session_start();
// This script temporarily sets admin session to access notices
$_SESSION["userId"] = 1;
$_SESSION["username"] = "admin";
$_SESSION["userType"] = "admin";
$_SESSION["lastActivity"] = time();

echo "<h2>Admin Session Created</h2>";
echo "<p>You now have a temporary admin session to access the notices page.</p>";
echo "<p>Try accessing: <a href=\'admin/notices.php\' target=\'_blank\'>admin/notices.php</a></p>";
echo "<p>When finished: <a href=\'clear_session.php\'>Clear Session</a></p>";
?>';

file_put_contents($adminBypassFile, $bypassContent);
echo "<p style='color:green;'>Created a temporary admin bypass page at: <a href='admin_notices_bypass.php'>admin_notices_bypass.php</a></p>";

// Provide overall solution
echo "<h3>Recommended Solution:</h3>";
echo "<ol>";
echo "<li>First try the regular login through <a href='index.php'>index.php</a> with admin credentials.</li>";
echo "<li>If that fails, you can use the temporary bypass: <a href='admin_notices_bypass.php'>admin_notices_bypass.php</a></li>";
echo "<li>If both fail, use the direct access page: <a href='temp_notices.php'>temp_notices.php</a></li>";
echo "<li>Check the database connection and notices table structure: <a href='check_notices_table.php'>check_notices_table.php</a></li>";
echo "</ol>";

$conn->close();
?> 