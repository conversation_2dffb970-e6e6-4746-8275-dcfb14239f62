<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get parameters
$subject_id = isset($_GET['subject_id']) ? intval($_GET['subject_id']) : 0;
$class_id = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01');
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');

// Get classes for dropdown
$classesQuery = "SELECT id, class_name FROM classes ORDER BY class_name";
$classesResult = $conn->query($classesQuery);
$classes = [];
if ($classesResult && $classesResult->num_rows > 0) {
    while ($row = $classesResult->fetch_assoc()) {
        $classes[$row['id']] = $row['class_name'];
    }
}

// Get subjects for dropdown
$subjectsQuery = "SELECT id, subject_name FROM subjects ORDER BY subject_name";
$subjectsResult = $conn->query($subjectsQuery);
$subjects = [];
if ($subjectsResult && $subjectsResult->num_rows > 0) {
    while ($row = $subjectsResult->fetch_assoc()) {
        $subjects[$row['id']] = $row['subject_name'];
    }
}

// Get attendance data
$attendance_data = [];
$students = [];
$subject_details = null;
$class_details = null;
$overall_stats = [];

if ($subject_id > 0) {
    // Get subject details
    $subjectQuery = "SELECT * FROM subjects WHERE id = ?";
    $stmt = $conn->prepare($subjectQuery);
    $stmt->bind_param("i", $subject_id);
    $stmt->execute();
    $subject_details = $stmt->get_result()->fetch_assoc();
    
    // Get class details if specified
    if ($class_id > 0) {
        $classQuery = "SELECT * FROM classes WHERE id = ?";
        $stmt = $conn->prepare($classQuery);
        $stmt->bind_param("i", $class_id);
        $stmt->execute();
        $class_details = $stmt->get_result()->fetch_assoc();
    }
    
    // Build attendance query
    $attendanceQuery = "SELECT a.*, s.student_id as student_code, s.first_name, s.last_name, s.roll_number, c.class_name 
                       FROM attendance a 
                       JOIN students s ON a.student_id = s.id 
                       JOIN classes c ON a.class_id = c.id 
                       WHERE a.subject_id = ? AND a.date BETWEEN ? AND ?";
    
    $params = [$subject_id, $start_date, $end_date];
    $types = "iss";
    
    if ($class_id > 0) {
        $attendanceQuery .= " AND a.class_id = ?";
        $params[] = $class_id;
        $types .= "i";
    }
    
    $attendanceQuery .= " ORDER BY c.class_name, s.roll_number, a.date";
    
    $stmt = $conn->prepare($attendanceQuery);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $attendanceResult = $stmt->get_result();
    
    while ($row = $attendanceResult->fetch_assoc()) {
        $student_key = $row['student_id'];
        
        if (!isset($students[$student_key])) {
            $students[$student_key] = [
                'student_code' => $row['student_code'],
                'first_name' => $row['first_name'],
                'last_name' => $row['last_name'],
                'roll_number' => $row['roll_number'],
                'class_name' => $row['class_name'],
                'present' => 0,
                'absent' => 0,
                'late' => 0,
                'excused' => 0,
                'total' => 0
            ];
        }
        
        $students[$student_key][$row['status']]++;
        $students[$student_key]['total']++;
        
        $attendance_data[] = $row;
    }
    
    // Calculate overall statistics
    $total_present = 0;
    $total_absent = 0;
    $total_late = 0;
    $total_excused = 0;
    $total_records = 0;
    
    foreach ($students as $student) {
        $total_present += $student['present'];
        $total_absent += $student['absent'];
        $total_late += $student['late'];
        $total_excused += $student['excused'];
        $total_records += $student['total'];
    }
    
    $overall_stats = [
        'total_students' => count($students),
        'present' => $total_present,
        'absent' => $total_absent,
        'late' => $total_late,
        'excused' => $total_excused,
        'total_records' => $total_records,
        'avg_attendance' => $total_records > 0 ? round(($total_present + $total_late) / $total_records * 100, 2) : 0
    ];
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>বিষয়ভিত্তিক উপস্থিতি রিপোর্ট - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .status-present { background-color: #d4edda; color: #155724; }
        .status-absent { background-color: #f8d7da; color: #721c24; }
        .status-late { background-color: #fff3cd; color: #856404; }
        .status-excused { background-color: #d1ecf1; color: #0c5460; }
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .stats-card {
            border-left: 4px solid #007bff;
        }
        .stats-good { color: #28a745; }
        .stats-warning { color: #ffc107; }
        .stats-danger { color: #dc3545; }
        
        @media print {
            .no-print { display: none !important; }
            .table { font-size: 12px; }
        }
    </style>
    <link rel="stylesheet" href="css/hind-siliguri.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include('includes/sidebar.php'); ?>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">বিষয়ভিত্তিক উপস্থিতি রিপোর্ট</h1>
                    <div class="no-print">
                        <button onclick="window.print()" class="btn btn-primary">
                            <i class="fas fa-print me-1"></i> প্রিন্ট করুন
                        </button>
                    </div>
                </div>
                
                <!-- Filter Form -->
                <div class="card mb-4 no-print">
                    <div class="card-header">
                        <h5><i class="fas fa-filter me-2"></i>রিপোর্ট ফিল্টার</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="" class="row g-3">
                            <div class="col-md-3">
                                <label for="subject_id" class="form-label">বিষয়</label>
                                <select class="form-select" id="subject_id" name="subject_id" required>
                                    <option value="">বিষয় নির্বাচন করুন</option>
                                    <?php foreach ($subjects as $id => $name): ?>
                                    <option value="<?php echo $id; ?>" <?php echo ($subject_id == $id) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($name); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label for="class_id" class="form-label">ক্লাস (ঐচ্ছিক)</label>
                                <select class="form-select" id="class_id" name="class_id">
                                    <option value="0">সকল ক্লাস</option>
                                    <?php foreach ($classes as $id => $name): ?>
                                    <option value="<?php echo $id; ?>" <?php echo ($class_id == $id) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($name); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label for="start_date" class="form-label">শুরুর তারিখ</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $start_date; ?>" required>
                            </div>
                            
                            <div class="col-md-3">
                                <label for="end_date" class="form-label">শেষ তারিখ</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $end_date; ?>" required>
                            </div>
                            
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i> রিপোর্ট দেখুন
                                </button>
                                <a href="attendance.php" class="btn btn-secondary ms-2">
                                    <i class="fas fa-arrow-left me-1"></i> ফিরে যান
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <?php if ($subject_details): ?>
                <!-- Report Header -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-book me-2"></i>রিপোর্ট বিবরণ</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>বিষয়:</strong> <?php echo htmlspecialchars($subject_details['subject_name']); ?></p>
                                <p><strong>বিষয় কোড:</strong> <?php echo htmlspecialchars($subject_details['subject_code'] ?? 'N/A'); ?></p>
                                <?php if ($class_details): ?>
                                <p><strong>ক্লাস:</strong> <?php echo htmlspecialchars($class_details['class_name']); ?></p>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6">
                                <p><strong>সময়কাল:</strong> <?php echo date('d/m/Y', strtotime($start_date)) . ' - ' . date('d/m/Y', strtotime($end_date)); ?></p>
                                <p><strong>মোট শিক্ষার্থী:</strong> <?php echo $overall_stats['total_students']; ?></p>
                                <p><strong>মোট রেকর্ড:</strong> <?php echo $overall_stats['total_records']; ?></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Overall Statistics -->
                <div class="card mb-4 stats-card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-pie me-2"></i>সামগ্রিক পরিসংখ্যান</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-2">
                                <div class="card bg-success bg-opacity-10">
                                    <div class="card-body">
                                        <h4 class="text-success"><?php echo $overall_stats['present']; ?></h4>
                                        <p class="mb-0">উপস্থিত</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-danger bg-opacity-10">
                                    <div class="card-body">
                                        <h4 class="text-danger"><?php echo $overall_stats['absent']; ?></h4>
                                        <p class="mb-0">অনুপস্থিত</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-warning bg-opacity-10">
                                    <div class="card-body">
                                        <h4 class="text-warning"><?php echo $overall_stats['late']; ?></h4>
                                        <p class="mb-0">দেরি</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-info bg-opacity-10">
                                    <div class="card-body">
                                        <h4 class="text-info"><?php echo $overall_stats['excused']; ?></h4>
                                        <p class="mb-0">ছুটি</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-secondary bg-opacity-10">
                                    <div class="card-body">
                                        <h4 class="text-secondary"><?php echo $overall_stats['total_records']; ?></h4>
                                        <p class="mb-0">মোট রেকর্ড</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-primary bg-opacity-10">
                                    <div class="card-body">
                                        <?php 
                                        $percentage_class = '';
                                        if ($overall_stats['avg_attendance'] >= 80) $percentage_class = 'stats-good';
                                        elseif ($overall_stats['avg_attendance'] >= 60) $percentage_class = 'stats-warning';
                                        else $percentage_class = 'stats-danger';
                                        ?>
                                        <h4 class="<?php echo $percentage_class; ?>"><?php echo $overall_stats['avg_attendance']; ?>%</h4>
                                        <p class="mb-0">গড় উপস্থিতি</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Student-wise Statistics -->
                <?php if (!empty($students)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-users me-2"></i>শিক্ষার্থীভিত্তিক পরিসংখ্যান</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>ক্লাস</th>
                                        <th>রোল</th>
                                        <th>নাম</th>
                                        <th>উপস্থিত</th>
                                        <th>অনুপস্থিত</th>
                                        <th>দেরি</th>
                                        <th>ছুটি</th>
                                        <th>মোট</th>
                                        <th>উপস্থিতির হার</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($students as $student): 
                                        $percentage = $student['total'] > 0 ? round(($student['present'] + $student['late']) / $student['total'] * 100, 2) : 0;
                                        $percentage_class = '';
                                        if ($percentage >= 80) $percentage_class = 'stats-good';
                                        elseif ($percentage >= 60) $percentage_class = 'stats-warning';
                                        else $percentage_class = 'stats-danger';
                                    ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($student['class_name']); ?></td>
                                        <td><?php echo htmlspecialchars($student['roll_number']); ?></td>
                                        <td><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></td>
                                        <td class="text-center"><?php echo $student['present']; ?></td>
                                        <td class="text-center"><?php echo $student['absent']; ?></td>
                                        <td class="text-center"><?php echo $student['late']; ?></td>
                                        <td class="text-center"><?php echo $student['excused']; ?></td>
                                        <td class="text-center"><?php echo $student['total']; ?></td>
                                        <td class="text-center <?php echo $percentage_class; ?>">
                                            <strong><?php echo $percentage; ?>%</strong>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Detailed Attendance Records -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-list me-2"></i>বিস্তারিত উপস্থিতি রেকর্ড</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover table-sm">
                                <thead class="table-light">
                                    <tr>
                                        <th>তারিখ</th>
                                        <th>ক্লাস</th>
                                        <th>রোল</th>
                                        <th>নাম</th>
                                        <th>অবস্থা</th>
                                        <th>মন্তব্য</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($attendance_data as $record): 
                                        $status_text = '';
                                        $status_class = '';
                                        switch ($record['status']) {
                                            case 'present':
                                                $status_text = 'উপস্থিত';
                                                $status_class = 'status-present';
                                                break;
                                            case 'absent':
                                                $status_text = 'অনুপস্থিত';
                                                $status_class = 'status-absent';
                                                break;
                                            case 'late':
                                                $status_text = 'দেরি';
                                                $status_class = 'status-late';
                                                break;
                                            case 'excused':
                                                $status_text = 'ছুটি';
                                                $status_class = 'status-excused';
                                                break;
                                        }
                                    ?>
                                    <tr>
                                        <td><?php echo date('d/m/Y', strtotime($record['date'])); ?></td>
                                        <td><?php echo htmlspecialchars($record['class_name']); ?></td>
                                        <td><?php echo htmlspecialchars($record['roll_number']); ?></td>
                                        <td><?php echo htmlspecialchars($record['first_name'] . ' ' . $record['last_name']); ?></td>
                                        <td>
                                            <span class="status-badge <?php echo $status_class; ?>">
                                                <?php echo $status_text; ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($record['remarks'] ?? ''); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php elseif ($subject_id > 0): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i> নির্বাচিত বিষয়ের কোন উপস্থিতি রেকর্ড পাওয়া যায়নি।
                </div>
                <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> রিপোর্ট দেখতে একটি বিষয় নির্বাচন করুন।
                </div>
                <?php endif; ?>
                
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
