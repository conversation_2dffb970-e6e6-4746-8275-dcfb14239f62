<?php
session_start();
require_once '../includes/dbh.inc.php';
require_once '../includes/bkash_config.php';
require_once '../includes/bkash_functions.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Create bKash payments table if it doesn't exist
createBkashPaymentsTable($conn);

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$itemsPerPage = 10;
$offset = ($page - 1) * $itemsPerPage;

// Search and filter parameters
$searchTerm = isset($_GET['search']) ? $_GET['search'] : '';
$statusFilter = isset($_GET['status']) ? $_GET['status'] : '';
$dateFrom = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$dateTo = isset($_GET['date_to']) ? $_GET['date_to'] : '';

// Build query conditions
$whereClause = "";
$params = [];
$types = "";

if (!empty($searchTerm)) {
    $whereClause .= " WHERE (s.student_id LIKE ? OR s.first_name LIKE ? OR s.last_name LIKE ? OR bp.trx_id LIKE ?)";
    $searchParam = "%$searchTerm%";
    $params = [$searchParam, $searchParam, $searchParam, $searchParam];
    $types = "ssss";
}

if (!empty($statusFilter)) {
    $whereClause = empty($whereClause) ? " WHERE bp.status = ?" : $whereClause . " AND bp.status = ?";
    $params[] = $statusFilter;
    $types .= "s";
}

if (!empty($dateFrom)) {
    $whereClause = empty($whereClause) ? " WHERE DATE(bp.payment_date) >= ?" : $whereClause . " AND DATE(bp.payment_date) >= ?";
    $params[] = $dateFrom;
    $types .= "s";
}

if (!empty($dateTo)) {
    $whereClause = empty($whereClause) ? " WHERE DATE(bp.payment_date) <= ?" : $whereClause . " AND DATE(bp.payment_date) <= ?";
    $params[] = $dateTo;
    $types .= "s";
}

// Get total count for pagination
$countQuery = "SELECT COUNT(*) as total FROM bkash_payments bp
              LEFT JOIN fees f ON bp.fee_id = f.id
              LEFT JOIN students s ON f.student_id = s.id" . $whereClause;
$stmt = $conn->prepare($countQuery);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$totalResult = $stmt->get_result();
$totalRows = $totalResult->fetch_assoc()['total'];
$totalPages = ceil($totalRows / $itemsPerPage);

// Get payments for current page
$paymentsQuery = "SELECT bp.*, f.fee_type, s.first_name, s.last_name, s.student_id as roll, c.class_name
                 FROM bkash_payments bp
                 LEFT JOIN fees f ON bp.fee_id = f.id
                 LEFT JOIN students s ON f.student_id = s.id
                 LEFT JOIN classes c ON s.class_id = c.id" . 
                 $whereClause . 
                 " ORDER BY bp.payment_date DESC
                 LIMIT ? OFFSET ?";

$params[] = $itemsPerPage;
$params[] = $offset;
$types .= "ii";

$stmt = $conn->prepare($paymentsQuery);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$payments = $stmt->get_result();

// Get payment statistics
$statsQuery = "SELECT 
                COUNT(*) as total_payments,
                SUM(amount) as total_amount,
                COUNT(CASE WHEN status = 'Completed' THEN 1 END) as completed_payments,
                SUM(CASE WHEN status = 'Completed' THEN amount ELSE 0 END) as completed_amount,
                COUNT(CASE WHEN status != 'Completed' THEN 1 END) as failed_payments
              FROM bkash_payments";
$statsResult = $conn->query($statsQuery);
$stats = $statsResult->fetch_assoc();

// Include header
include_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Include sidebar -->
        <?php include_once 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-list-alt me-2"></i> বিকাশ পেমেন্ট তালিকা</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="bkash_dashboard.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-tachometer-alt me-1"></i> ড্যাশবোর্ড
                        </a>
                        <a href="bkash_payment_form.php" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-plus-circle me-1"></i> নতুন পেমেন্ট
                        </a>
                    </div>
                </div>
            </div>
            
            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i> <?= $_SESSION['success'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>
            
            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i> <?= $_SESSION['error'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>
            
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card border-primary h-100">
                        <div class="card-body text-center">
                            <div class="display-4 text-primary mb-2">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <h5 class="card-title">মোট পেমেন্ট</h5>
                            <h2 class="display-6 text-primary"><?= $stats['total_payments'] ?? 0 ?></h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card border-success h-100">
                        <div class="card-body text-center">
                            <div class="display-4 text-success mb-2">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <h5 class="card-title">সফল পেমেন্ট</h5>
                            <h2 class="display-6 text-success"><?= $stats['completed_payments'] ?? 0 ?></h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card border-danger h-100">
                        <div class="card-body text-center">
                            <div class="display-4 text-danger mb-2">
                                <i class="fas fa-times-circle"></i>
                            </div>
                            <h5 class="card-title">ব্যর্থ পেমেন্ট</h5>
                            <h2 class="display-6 text-danger"><?= $stats['failed_payments'] ?? 0 ?></h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card border-info h-100">
                        <div class="card-body text-center">
                            <div class="display-4 text-info mb-2">
                                <i class="fas fa-hand-holding-usd"></i>
                            </div>
                            <h5 class="card-title">মোট পরিমাণ</h5>
                            <h2 class="display-6 text-info">৳ <?= number_format($stats['completed_amount'] ?? 0, 2) ?></h2>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Search and Filter -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-search me-2"></i> অনুসন্ধান এবং ফিল্টার</h5>
                </div>
                <div class="card-body">
                    <form action="" method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="search" class="form-label">অনুসন্ধান</label>
                            <input type="text" class="form-control" id="search" name="search" placeholder="শিক্ষার্থী আইডি, নাম বা ট্রানজেকশন আইডি" value="<?= htmlspecialchars($searchTerm) ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">স্ট্যাটাস</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">সকল স্ট্যাটাস</option>
                                <option value="Completed" <?= $statusFilter === 'Completed' ? 'selected' : '' ?>>সফল</option>
                                <option value="Initiated" <?= $statusFilter === 'Initiated' ? 'selected' : '' ?>>প্রক্রিয়াধীন</option>
                                <option value="Failed" <?= $statusFilter === 'Failed' ? 'selected' : '' ?>>ব্যর্থ</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">তারিখ থেকে</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="<?= htmlspecialchars($dateFrom) ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">তারিখ পর্যন্ত</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="<?= htmlspecialchars($dateTo) ?>">
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <div class="d-grid gap-2 w-100">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i> অনুসন্ধান
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Payments Table -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0"><i class="fas fa-list me-2"></i> বিকাশ পেমেন্ট তালিকা</h5>
                    <a href="bkash_payment_export.php<?= !empty($_SERVER['QUERY_STRING']) ? '?' . $_SERVER['QUERY_STRING'] : '' ?>" class="btn btn-sm btn-light">
                        <i class="fas fa-file-export me-1"></i> এক্সপোর্ট
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-light">
                                <tr>
                                    <th>আইডি</th>
                                    <th>শিক্ষার্থী</th>
                                    <th>ফি টাইপ</th>
                                    <th>পরিমাণ</th>
                                    <th>ট্রানজেকশন আইডি</th>
                                    <th>তারিখ</th>
                                    <th>স্ট্যাটাস</th>
                                    <th>অ্যাকশন</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($payments && $payments->num_rows > 0): ?>
                                    <?php while ($payment = $payments->fetch_assoc()): ?>
                                        <tr>
                                            <td><?= $payment['id'] ?></td>
                                            <td>
                                                <?php if (!empty($payment['first_name'])): ?>
                                                    <?= htmlspecialchars($payment['first_name'] . ' ' . $payment['last_name']) ?> 
                                                    (<?= $payment['roll'] ?>)
                                                <?php else: ?>
                                                    <span class="text-muted">শিক্ষার্থী পাওয়া যায়নি</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= htmlspecialchars($payment['fee_type'] ?? 'N/A') ?></td>
                                            <td>৳ <?= number_format($payment['amount'], 2) ?></td>
                                            <td>
                                                <?php if (!empty($payment['trx_id'])): ?>
                                                    <?= $payment['trx_id'] ?>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">N/A</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= date('d/m/Y H:i', strtotime($payment['payment_date'])) ?></td>
                                            <td>
                                                <?php if ($payment['status'] === 'Completed'): ?>
                                                    <span class="badge bg-success">সফল</span>
                                                <?php elseif ($payment['status'] === 'Initiated'): ?>
                                                    <span class="badge bg-warning text-dark">প্রক্রিয়াধীন</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">ব্যর্থ</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="bkash_payment_details.php?id=<?= $payment['id'] ?>" class="btn btn-info" title="বিস্তারিত দেখুন">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <?php if ($payment['status'] === 'Initiated'): ?>
                                                        <a href="bkash_check_status.php?id=<?= $payment['id'] ?>" class="btn btn-warning" title="স্ট্যাটাস চেক করুন">
                                                            <i class="fas fa-sync-alt"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if ($payment['status'] !== 'Completed'): ?>
                                                        <a href="bkash_payment.php?fee_id=<?= $payment['fee_id'] ?>" class="btn btn-success" title="আবার চেষ্টা করুন">
                                                            <i class="fas fa-redo"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="8" class="text-center">কোন পেমেন্ট রেকর্ড পাওয়া যায়নি</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                <li class="page-item <?= ($page <= 1) ? 'disabled' : '' ?>">
                                    <a class="page-link" href="?page=<?= $page - 1 ?>&search=<?= urlencode($searchTerm) ?>&status=<?= urlencode($statusFilter) ?>&date_from=<?= urlencode($dateFrom) ?>&date_to=<?= urlencode($dateTo) ?>">আগের</a>
                                </li>
                                
                                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <li class="page-item <?= ($page == $i) ? 'active' : '' ?>">
                                        <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($searchTerm) ?>&status=<?= urlencode($statusFilter) ?>&date_from=<?= urlencode($dateFrom) ?>&date_to=<?= urlencode($dateTo) ?>"><?= $i ?></a>
                                    </li>
                                <?php endfor; ?>
                                
                                <li class="page-item <?= ($page >= $totalPages) ? 'disabled' : '' ?>">
                                    <a class="page-link" href="?page=<?= $page + 1 ?>&search=<?= urlencode($searchTerm) ?>&status=<?= urlencode($statusFilter) ?>&date_from=<?= urlencode($dateFrom) ?>&date_to=<?= urlencode($dateTo) ?>">পরের</a>
                                </li>
                            </ul>
                        </nav>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Help Section -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-question-circle me-2"></i> সাহায্য</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle me-2"></i> বিকাশ পেমেন্ট স্ট্যাটাস</h6>
                            <ul>
                                <li><strong>সফল:</strong> পেমেন্ট সফলভাবে সম্পন্ন হয়েছে</li>
                                <li><strong>প্রক্রিয়াধীন:</strong> পেমেন্ট প্রক্রিয়াধীন আছে</li>
                                <li><strong>ব্যর্থ:</strong> পেমেন্ট ব্যর্থ হয়েছে</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-cogs me-2"></i> অ্যাকশন বাটন</h6>
                            <ul>
                                <li><i class="fas fa-eye"></i> <strong>বিস্তারিত দেখুন:</strong> পেমেন্টের বিস্তারিত তথ্য দেখুন</li>
                                <li><i class="fas fa-sync-alt"></i> <strong>স্ট্যাটাস চেক করুন:</strong> বিকাশ API থেকে পেমেন্ট স্ট্যাটাস আপডেট করুন</li>
                                <li><i class="fas fa-redo"></i> <strong>আবার চেষ্টা করুন:</strong> ব্যর্থ পেমেন্ট আবার চেষ্টা করুন</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<?php
// Include footer
include_once 'includes/footer.php';
?>
