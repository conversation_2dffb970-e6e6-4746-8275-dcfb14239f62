<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Initialize variables
$students = [];
$errorMessage = '';
$successMessage = '';

// Check if student_ids parameter is provided
if (!isset($_GET['student_ids']) || empty($_GET['student_ids'])) {
    header("Location: advanced_certificate.php");
    exit();
}

// Get student IDs from URL
$studentIds = explode(',', $_GET['student_ids']);

// Validate student IDs
if (empty($studentIds)) {
    header("Location: advanced_certificate.php");
    exit();
}

// Get student data
$placeholders = str_repeat('?,', count($studentIds) - 1) . '?';
$types = str_repeat('i', count($studentIds));

$query = "SELECT s.*, d.department_name, c.class_name, ss.session_name
          FROM students s
          LEFT JOIN departments d ON s.department_id = d.id
          LEFT JOIN classes c ON s.class_id = c.id
          LEFT JOIN sessions ss ON s.session_id = ss.id
          WHERE s.id IN ($placeholders)
          ORDER BY s.first_name, s.last_name";

$stmt = $conn->prepare($query);
$stmt->bind_param($types, ...$studentIds);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $students[] = $row;
    }
} else {
    header("Location: advanced_certificate.php");
    exit();
}

// Check if certificate_settings table exists
$tableCheck = $conn->query("SHOW TABLES LIKE 'certificate_settings'");
if ($tableCheck->num_rows == 0) {
    // Create certificate_settings table if it doesn't exist
    $createTableSQL = "CREATE TABLE certificate_settings (
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        header_text VARCHAR(255) DEFAULT 'Certificate of Achievement',
        footer_text VARCHAR(255) DEFAULT 'This certificate is awarded for outstanding performance',
        signature_text VARCHAR(255) DEFAULT 'Principal',
        logo_path VARCHAR(255) DEFAULT NULL,
        watermark_path VARCHAR(255) DEFAULT NULL,
        border_color VARCHAR(20) DEFAULT '#000000',
        header_color VARCHAR(20) DEFAULT '#000000',
        institution_name VARCHAR(255) DEFAULT 'College Management System',
        institution_address VARCHAR(255) DEFAULT 'Address, City, Country',
        established_year VARCHAR(20) DEFAULT '2000',
        seal_color VARCHAR(20) DEFAULT '#800000',
        seal_opacity DECIMAL(3,2) DEFAULT 0.7,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $conn->query($createTableSQL);

    // Insert default settings
    $insertSQL = "INSERT INTO certificate_settings (id, header_text, footer_text, signature_text, border_color, header_color, institution_name, institution_address, established_year, seal_color, seal_opacity)
                 VALUES (1, 'Certificate of Achievement', 'This certificate is awarded for outstanding performance', 'Principal', '#000000', '#000000', 'College Management System', 'Address, City, Country', '2000', '#800000', 0.7)";
    $conn->query($insertSQL);
} else {
    // Check if new columns exist, add them if they don't
    $columnsToAdd = [
        "institution_name" => "VARCHAR(255) DEFAULT 'College Management System'",
        "institution_address" => "VARCHAR(255) DEFAULT 'Address, City, Country'",
        "established_year" => "VARCHAR(20) DEFAULT '2000'",
        "seal_color" => "VARCHAR(20) DEFAULT '#800000'",
        "seal_opacity" => "DECIMAL(3,2) DEFAULT 0.7"
    ];

    foreach ($columnsToAdd as $column => $definition) {
        $columnCheck = $conn->query("SHOW COLUMNS FROM certificate_settings LIKE '$column'");
        if ($columnCheck->num_rows == 0) {
            $conn->query("ALTER TABLE certificate_settings ADD COLUMN $column $definition");
        }
    }
}

// Get certificate settings
$certificateSettings = [];
$settingsQuery = $conn->query("SELECT * FROM certificate_settings WHERE id = 1");
if ($settingsQuery && $settingsQuery->num_rows > 0) {
    $certificateSettings = $settingsQuery->fetch_assoc();
} else {
    // Insert default settings if no settings found
    $insertSQL = "INSERT INTO certificate_settings (id, header_text, footer_text, signature_text, border_color, header_color)
                 VALUES (1, 'Certificate of Achievement', 'This certificate is awarded for outstanding performance', 'Principal', '#000000', '#000000')";
    $conn->query($insertSQL);
    $settingsQuery = $conn->query("SELECT * FROM certificate_settings WHERE id = 1");
    $certificateSettings = $settingsQuery->fetch_assoc();
}

// Process certificate generation
if (isset($_POST['generate_certificates'])) {
    $headerText = $_POST['header_text'];
    $footerText = $_POST['footer_text'];
    $signatureText = $_POST['signature_text'];
    $borderColor = $_POST['border_color'];
    $headerColor = $_POST['header_color'];

    // Get seal customization settings
    $institutionName = isset($_POST['institution_name']) ? $_POST['institution_name'] : 'College Management System';
    $institutionAddress = isset($_POST['institution_address']) ? $_POST['institution_address'] : 'Address, City, Country';
    $establishedYear = isset($_POST['established_year']) ? $_POST['established_year'] : '2000';
    $sealColor = isset($_POST['seal_color']) ? $_POST['seal_color'] : '#800000';
    $sealOpacity = isset($_POST['seal_opacity']) ? $_POST['seal_opacity'] : '0.7';

    // Update certificate settings
    $updateQuery = "UPDATE certificate_settings SET
                    header_text = ?,
                    footer_text = ?,
                    signature_text = ?,
                    border_color = ?,
                    header_color = ?,
                    institution_name = ?,
                    institution_address = ?,
                    established_year = ?,
                    seal_color = ?,
                    seal_opacity = ?
                    WHERE id = 1";
    $updateStmt = $conn->prepare($updateQuery);
    $updateStmt->bind_param("sssssssssd",
        $headerText,
        $footerText,
        $signatureText,
        $borderColor,
        $headerColor,
        $institutionName,
        $institutionAddress,
        $establishedYear,
        $sealColor,
        $sealOpacity
    );
    $updateStmt->execute();

    // Create upload directories if they don't exist
    $uploadDirs = ['uploads', 'uploads/logos', 'uploads/watermarks'];
    foreach ($uploadDirs as $dir) {
        if (!file_exists($dir)) {
            if (!mkdir($dir, 0777, true)) {
                $errorMessage = "ডিরেক্টরি তৈরি করতে সমস্যা হয়েছে: " . $dir;
            }
        }
    }

    // Handle logo upload
    if (isset($_FILES['logo']) && $_FILES['logo']['error'] == 0) {
        $logoName = time() . '_' . $_FILES['logo']['name'];
        $logoPath = 'uploads/logos/' . $logoName;

        if (move_uploaded_file($_FILES['logo']['tmp_name'], $logoPath)) {
            $conn->query("UPDATE certificate_settings SET logo_path = '$logoPath' WHERE id = 1");
            $certificateSettings['logo_path'] = $logoPath;
        } else {
            $errorMessage = "লোগো আপলোড করতে সমস্যা হয়েছে।";
        }
    }

    // Handle watermark upload
    if (isset($_FILES['watermark']) && $_FILES['watermark']['error'] == 0) {
        $watermarkName = time() . '_' . $_FILES['watermark']['name'];
        $watermarkPath = 'uploads/watermarks/' . $watermarkName;

        if (move_uploaded_file($_FILES['watermark']['tmp_name'], $watermarkPath)) {
            $conn->query("UPDATE certificate_settings SET watermark_path = '$watermarkPath' WHERE id = 1");
            $certificateSettings['watermark_path'] = $watermarkPath;
        } else {
            $errorMessage = "ওয়াটারমার্ক আপলোড করতে সমস্যা হয়েছে।";
        }
    }

    // Only redirect if there are no errors
    if (empty($errorMessage)) {
        // Redirect to print certificates
        $studentIdsParam = implode(',', $studentIds);
        header("Location: print_certificates.php?student_ids=" . $studentIdsParam);
        exit();
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>সার্টিফিকেট তৈরি করুন - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    
    

    <style>

        .certificate-preview {
            border: 1px solid #ddd;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 10px;
            background-color: #fff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            font-family: 'Hind Siliguri', sans-serif;
        }
        .certificate-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #000;
        }
        .certificate-body {
            text-align: center;
            margin-bottom: 20px;
        }
        .certificate-footer {
            text-align: center;
            margin-top: 40px;
        }
        .signature-line {
            width: 200px;
            border-top: 1px solid #000;
            margin: 10px auto;
        }
        .student-list {
            max-height: 300px;
            overflow-y: auto;
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="certificates.php">
                            <i class="fas fa-certificate me-2"></i> সার্টিফিকেট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>সার্টিফিকেট তৈরি করুন</h2>
                        <p class="text-muted">নির্বাচিত শিক্ষার্থীদের জন্য সার্টিফিকেট কাস্টমাইজ করুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="advanced_certificate.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>ফিরে যান
                        </a>
                    </div>
                </div>

                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- Certificate Settings Form -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="fas fa-cog me-2"></i>সার্টিফিকেট সেটিংস</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="header_text" class="form-label">হেডার টেক্সট</label>
                                        <input type="text" class="form-control" id="header_text" name="header_text" value="<?php echo htmlspecialchars($certificateSettings['header_text'] ?? 'Certificate of Achievement'); ?>" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="footer_text" class="form-label">ফুটার টেক্সট</label>
                                        <input type="text" class="form-control" id="footer_text" name="footer_text" value="<?php echo htmlspecialchars($certificateSettings['footer_text'] ?? 'This certificate is awarded for outstanding performance'); ?>" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="signature_text" class="form-label">স্বাক্ষর টেক্সট</label>
                                        <input type="text" class="form-control" id="signature_text" name="signature_text" value="<?php echo htmlspecialchars($certificateSettings['signature_text'] ?? 'Principal'); ?>" required>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="border_color" class="form-label">বর্ডার কালার</label>
                                            <input type="color" class="form-control form-control-color w-100" id="border_color" name="border_color" value="<?php echo htmlspecialchars($certificateSettings['border_color'] ?? '#000000'); ?>">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="header_color" class="form-label">হেডার কালার</label>
                                            <input type="color" class="form-control form-control-color w-100" id="header_color" name="header_color" value="<?php echo htmlspecialchars($certificateSettings['header_color'] ?? '#000000'); ?>">
                                        </div>
                                    </div>

                                    <h5 class="mt-4 mb-3">সিল কাস্টমাইজেশন</h5>

                                    <div class="mb-3">
                                        <label for="institution_name" class="form-label">প্রতিষ্ঠানের নাম</label>
                                        <input type="text" class="form-control" id="institution_name" name="institution_name" value="<?php echo htmlspecialchars($certificateSettings['institution_name'] ?? 'College Management System'); ?>">
                                    </div>

                                    <div class="mb-3">
                                        <label for="institution_address" class="form-label">প্রতিষ্ঠানের ঠিকানা</label>
                                        <input type="text" class="form-control" id="institution_address" name="institution_address" value="<?php echo htmlspecialchars($certificateSettings['institution_address'] ?? 'Address, City, Country'); ?>">
                                    </div>

                                    <div class="mb-3">
                                        <label for="established_year" class="form-label">প্রতিষ্ঠার সাল</label>
                                        <input type="text" class="form-control" id="established_year" name="established_year" value="<?php echo htmlspecialchars($certificateSettings['established_year'] ?? '2000'); ?>">
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="seal_color" class="form-label">সিল কালার</label>
                                            <input type="color" class="form-control form-control-color w-100" id="seal_color" name="seal_color" value="<?php echo htmlspecialchars($certificateSettings['seal_color'] ?? '#800000'); ?>">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="seal_opacity" class="form-label">সিল অপাসিটি</label>
                                            <input type="range" class="form-range" id="seal_opacity" name="seal_opacity" min="0.1" max="1.0" step="0.1" value="<?php echo htmlspecialchars($certificateSettings['seal_opacity'] ?? '0.7'); ?>">
                                            <output for="seal_opacity" id="seal_opacity_output"><?php echo htmlspecialchars($certificateSettings['seal_opacity'] ?? '0.7'); ?></output>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="logo" class="form-label">লোগো আপলোড</label>
                                        <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                                        <?php if (!empty($certificateSettings['logo_path'])): ?>
                                            <div class="mt-2">
                                                <img src="<?php echo htmlspecialchars($certificateSettings['logo_path']); ?>" alt="Current Logo" style="max-height: 50px;">
                                                <small class="text-muted ms-2">বর্তমান লোগো</small>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="mb-3">
                                        <label for="watermark" class="form-label">ওয়াটারমার্ক আপলোড</label>
                                        <input type="file" class="form-control" id="watermark" name="watermark" accept="image/*">
                                        <?php if (!empty($certificateSettings['watermark_path'])): ?>
                                            <div class="mt-2">
                                                <img src="<?php echo htmlspecialchars($certificateSettings['watermark_path']); ?>" alt="Current Watermark" style="max-height: 50px;">
                                                <small class="text-muted ms-2">বর্তমান ওয়াটারমার্ক</small>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-center mt-4">
                                        <button type="submit" name="generate_certificates" class="btn btn-primary btn-lg">
                                            <i class="fas fa-certificate me-2"></i>সার্টিফিকেট তৈরি করুন
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Selected Students -->
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="fas fa-users me-2"></i>নির্বাচিত শিক্ষার্থী (<?php echo count($students); ?>)</h5>
                            </div>
                            <div class="card-body student-list">
                                <ul class="list-group">
                                    <?php foreach ($students as $student): ?>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></strong>
                                                <br>
                                                <small class="text-muted">আইডি: <?php echo htmlspecialchars($student['student_id']); ?>, রোল: <?php echo htmlspecialchars($student['roll_number']); ?></small>
                                            </div>
                                            <span class="badge bg-primary rounded-pill"><?php echo htmlspecialchars($student['class_name'] ?? 'N/A'); ?></span>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Certificate Preview -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="fas fa-eye me-2"></i>সার্টিফিকেট প্রিভিউ</h5>
                            </div>
                            <div class="card-body">
                                <div class="certificate-preview" id="certificate-preview" style="border: 5px solid <?php echo htmlspecialchars($certificateSettings['border_color'] ?? '#000000'); ?>; padding: 20px; position: relative; background-color: white; text-align: center;">
                                    <div style="text-align: center; margin-bottom: 15px; padding-bottom: 10px; border-bottom: 2px solid #f0f0f0;">
                                        <?php if (!empty($certificateSettings['logo_path'])): ?>
                                            <img src="<?php echo htmlspecialchars($certificateSettings['logo_path']); ?>" alt="Logo" style="max-height: 60px; margin-bottom: 10px;">
                                        <?php endif; ?>
                                        <h2 id="preview-header" style="color: <?php echo htmlspecialchars($certificateSettings['header_color'] ?? '#000000'); ?>; font-size: 24px; font-weight: bold; margin: 10px 0; text-transform: uppercase; letter-spacing: 1px;">
                                            <?php echo htmlspecialchars($certificateSettings['header_text'] ?? 'Certificate of Achievement'); ?>
                                        </h2>
                                    </div>
                                    <div style="padding: 10px 20px; text-align: center;">
                                        <p style="font-style: italic; margin-bottom: 10px; text-align: center;">এই মর্মে প্রত্যয়ন করা যাচ্ছে যে,</p>
                                        <h3 style="font-size: 22px; font-weight: bold; margin: 10px 0; color: #333; text-align: center;"><strong>Student Name</strong></h3>

                                        <div style="margin: 15px auto; font-size: 14px; line-height: 1.6; text-align: justify;">
                                            <p style="margin: 0;">
                                                পিতা Sample Father ও মাতা Sample Mother এর সন্তান, শিক্ষার্থী আইডি <strong>12345</strong>, রোল নম্বর <strong>101</strong>, <strong>Sample Class</strong> শ্রেণীর <strong>Sample Department</strong> বিভাগের <strong>2023-2024</strong> সেশনের একজন নিয়মিত শিক্ষার্থী।

                                                <span id="preview-footer" style="display: inline;"><?php echo htmlspecialchars($certificateSettings['footer_text'] ?? 'This certificate is awarded for outstanding performance'); ?></span>
                                            </p>
                                        </div>

                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: flex-end; margin-top: 20px; padding-top: 10px; border-top: 2px solid #f0f0f0;">
                                        <div style="text-align: left; font-style: italic; flex: 1;">
                                            <p>তারিখ: <?php echo date('d/m/Y'); ?></p>
                                        </div>

                                        <div style="text-align: center; flex: 1; display: flex; justify-content: center; align-items: center;">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 80 80" style="opacity: <?php echo $certificateSettings['seal_opacity'] ?? '0.7'; ?>;">
                                                <defs><path id="circle-text-path-preview" d="M 40,40 m -30,0 a 30,30 0 1,1 60,0 a 30,30 0 1,1 -60,0" /></defs>
                                                <circle cx="40" cy="40" r="38" fill="none" stroke="<?php echo $certificateSettings['seal_color'] ?? $certificateSettings['border_color'] ?? '#000000'; ?>" stroke-width="1.5" stroke-dasharray="4,4" />
                                                <circle cx="40" cy="40" r="35" fill="none" stroke="<?php echo $certificateSettings['seal_color'] ?? $certificateSettings['border_color'] ?? '#000000'; ?>" stroke-width="1" />
                                                <circle cx="40" cy="40" r="30" fill="none" stroke="<?php echo $certificateSettings['seal_color'] ?? $certificateSettings['border_color'] ?? '#000000'; ?>" stroke-width="0.5" />
                                                <text><textPath href="#circle-text-path-preview" startOffset="50%" text-anchor="middle" fill="<?php echo $certificateSettings['seal_color'] ?? $certificateSettings['border_color'] ?? '#000000'; ?>" font-size="5" font-family="Arial"><?php echo htmlspecialchars($certificateSettings['institution_name'] ?? 'College Management System'); ?></textPath></text>
                                                <text x="40" y="35" text-anchor="middle" fill="<?php echo $certificateSettings['seal_color'] ?? $certificateSettings['border_color'] ?? '#000000'; ?>" font-size="4" font-family="Arial"><?php echo htmlspecialchars($certificateSettings['institution_address'] ?? 'Address, City, Country'); ?></text>
                                                <text x="40" y="42" text-anchor="middle" fill="<?php echo $certificateSettings['seal_color'] ?? $certificateSettings['border_color'] ?? '#000000'; ?>" font-size="6" font-weight="bold" font-family="Arial">OFFICIAL SEAL</text>
                                                <text x="40" y="50" text-anchor="middle" fill="<?php echo $certificateSettings['seal_color'] ?? $certificateSettings['border_color'] ?? '#000000'; ?>" font-size="4" font-family="Arial">Established: <?php echo htmlspecialchars($certificateSettings['established_year'] ?? '2000'); ?></text>
                                                <text x="40" y="57" text-anchor="middle" fill="<?php echo $certificateSettings['seal_color'] ?? $certificateSettings['border_color'] ?? '#000000'; ?>" font-size="4" font-family="Arial"><?php echo date('Y'); ?></text>


                                            </svg>
                                        </div>

                                        <div style="text-align: right; flex: 1;">
                                            <div style="width: 120px; border-top: 1px solid #000; margin: 5px 0 3px auto;"></div>
                                            <p id="preview-signature" style="margin: 0;"><?php echo htmlspecialchars($certificateSettings['signature_text'] ?? 'Principal'); ?></p>
                                        </div>
                                    </div>


                                </div>
                                <div class="text-center mt-3">
                                    <p class="text-muted">এটি শুধুমাত্র একটি প্রিভিউ। আসল সার্টিফিকেট আলাদা দেখাতে পারে।</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        // Update certificate preview when form fields change
        document.getElementById('header_text').addEventListener('input', function() {
            document.getElementById('preview-header').textContent = this.value;
        });

        document.getElementById('footer_text').addEventListener('input', function() {
            document.getElementById('preview-footer').textContent = this.value;
        });

        document.getElementById('signature_text').addEventListener('input', function() {
            document.getElementById('preview-signature').textContent = this.value;
        });

        document.getElementById('border_color').addEventListener('input', function() {
            document.getElementById('certificate-preview').style.border = '5px solid ' + this.value;
        });

        document.getElementById('header_color').addEventListener('input', function() {
            document.getElementById('preview-header').style.color = this.value;
        });

        // Set initial border color
        document.getElementById('certificate-preview').style.border = '5px solid <?php echo htmlspecialchars($certificateSettings['border_color'] ?? '#000000'); ?>';

        // Seal customization preview updates
        if (document.getElementById('institution_name')) {
            document.getElementById('institution_name').addEventListener('input', updateSealPreview);
        }

        if (document.getElementById('institution_address')) {
            document.getElementById('institution_address').addEventListener('input', updateSealPreview);
        }

        if (document.getElementById('established_year')) {
            document.getElementById('established_year').addEventListener('input', updateSealPreview);
        }

        if (document.getElementById('seal_color')) {
            document.getElementById('seal_color').addEventListener('input', updateSealPreview);
        }

        if (document.getElementById('seal_opacity')) {
            document.getElementById('seal_opacity').addEventListener('input', function() {
                document.getElementById('seal_opacity_output').textContent = this.value;
                updateSealPreview();
            });
        }

        function updateSealPreview() {
            // This is a simplified version - in reality, we'd need to regenerate the SVG
            // For now, we'll just show that changes are being made
            const sealPreviewElement = document.querySelector('.certificate-preview div[style*="background-image: url"]');
            if (sealPreviewElement) {
                // Update opacity
                if (document.getElementById('seal_opacity')) {
                    sealPreviewElement.style.opacity = document.getElementById('seal_opacity').value;
                }
            }
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
</body>
</html>
