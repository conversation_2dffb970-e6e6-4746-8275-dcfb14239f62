<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Initialize variables
$successMessage = '';
$errorMessage = '';
$group = null;

// Check if group ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header("Location: groups.php");
    exit();
}

$group_id = $_GET['id'];

// Handle form submission
if (isset($_POST['update_group'])) {
    $group_name = $_POST['group_name'];
    $group_code = $_POST['group_code'];
    $description = $_POST['description'] ?? '';
    $is_active = isset($_POST['is_active']) ? 1 : 0;

    // Validate input
    if (empty($group_name) || empty($group_code)) {
        $errorMessage = "গ্রুপের নাম এবং কোড অবশ্যই পূরণ করতে হবে!";
    } else {
        // Check if group code already exists for other groups
        $checkQuery = "SELECT * FROM groups WHERE group_code = ? AND id != ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param("si", $group_code, $group_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $errorMessage = "এই গ্রুপ কোড ইতিমধ্যে অন্য গ্রুপের জন্য ব্যবহৃত হয়েছে!";
        } else {
            // Update group
            $updateQuery = "UPDATE groups SET 
                           group_name = ?, 
                           group_code = ?, 
                           description = ?, 
                           is_active = ?
                           WHERE id = ?";
            $stmt = $conn->prepare($updateQuery);
            $stmt->bind_param("sssii", $group_name, $group_code, $description, $is_active, $group_id);
            
            if ($stmt->execute()) {
                $successMessage = "গ্রুপ সফলভাবে আপডেট করা হয়েছে!";
            } else {
                $errorMessage = "গ্রুপ আপডেট করতে সমস্যা হয়েছে: " . $conn->error;
            }
        }
    }
}

// Get group details
$groupQuery = "SELECT * FROM groups WHERE id = ?";
$stmt = $conn->prepare($groupQuery);
$stmt->bind_param("i", $group_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header("Location: groups.php");
    exit();
}

$group = $result->fetch_assoc();

// Get subject count for this group
$subjectCountQuery = "SELECT COUNT(*) as total FROM subject_groups WHERE group_id = ?";
$stmt = $conn->prepare($subjectCountQuery);
$stmt->bind_param("i", $group_id);
$stmt->execute();
$subjectCountResult = $stmt->get_result();
$subjectCount = $subjectCountResult->fetch_assoc()['total'];

// Get subjects for this group
$subjectsQuery = "SELECT s.id, s.subject_name, s.subject_code, sg.subject_type
                 FROM subject_groups sg
                 JOIN subjects s ON sg.subject_id = s.id
                 WHERE sg.group_id = ?
                 ORDER BY s.subject_name
                 LIMIT 10";
$stmt = $conn->prepare($subjectsQuery);
$stmt->bind_param("i", $group_id);
$stmt->execute();
$subjects = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>গ্রুপ সম্পাদনা - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .card-hover {
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .info-card {
            border-left: 4px solid;
            transition: all 0.3s ease;
        }
        .info-card.active {
            border-left-color: #198754;
        }
        .info-card.inactive {
            border-left-color: #dc3545;
        }
        .subject-badge {
            display: inline-block;
            margin: 2px;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.85rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="groups.php">
                            <i class="fas fa-layer-group me-2"></i> গ্রুপ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sessions.php">
                            <i class="fas fa-calendar-alt me-2"></i> সেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>গ্রুপ সম্পাদনা</h2>
                        <p class="text-muted">গ্রুপের তথ্য সম্পাদনা এবং সংশ্লিষ্ট তথ্য দেখুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="groups.php" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>গ্রুপ তালিকায় ফিরে যান
                        </a>
                    </div>
                </div>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i><?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- Group Information -->
                    <div class="col-md-4 mb-4">
                        <div class="card info-card <?php echo $group['is_active'] ? 'active' : 'inactive'; ?> h-100">
                            <div class="card-header <?php echo $group['is_active'] ? 'bg-success' : 'bg-danger'; ?> text-white">
                                <h5 class="card-title mb-0">গ্রুপ তথ্য</h5>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-4">
                                    <div class="display-1 mb-3">
                                        <i class="fas fa-layer-group text-primary"></i>
                                    </div>
                                    <h4><?php echo htmlspecialchars($group['group_name']); ?></h4>
                                    <p class="text-muted"><?php echo htmlspecialchars($group['group_code']); ?></p>
                                    <span class="badge <?php echo $group['is_active'] ? 'bg-success' : 'bg-danger'; ?> px-3 py-2">
                                        <?php echo $group['is_active'] ? 'সক্রিয়' : 'নিষ্ক্রিয়'; ?>
                                    </span>
                                </div>
                                
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-book-open me-2 text-primary"></i> বিষয়:</span>
                                        <span class="fw-bold"><?php echo $subjectCount; ?> টি</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-calendar-alt me-2 text-warning"></i> তৈরি:</span>
                                        <span class="fw-bold"><?php echo date('d/m/Y', strtotime($group['created_at'])); ?></span>
                                    </li>
                                </ul>
                                
                                <?php if ($subjects && $subjects->num_rows > 0): ?>
                                <div class="mt-4">
                                    <h6 class="mb-3"><i class="fas fa-book-open me-2"></i>সংযুক্ত বিষয়সমূহ:</h6>
                                    <div>
                                        <?php while ($subject = $subjects->fetch_assoc()): 
                                            $badgeClass = 'bg-secondary';
                                            if ($subject['subject_type'] == 'required') {
                                                $badgeClass = 'bg-primary';
                                            } elseif ($subject['subject_type'] == 'optional') {
                                                $badgeClass = 'bg-success';
                                            } elseif ($subject['subject_type'] == 'fourth') {
                                                $badgeClass = 'bg-warning text-dark';
                                            }
                                        ?>
                                            <span class="subject-badge <?php echo $badgeClass; ?>">
                                                <?php echo htmlspecialchars($subject['subject_name']); ?>
                                            </span>
                                        <?php endwhile; ?>
                                        
                                        <?php if ($subjectCount > 10): ?>
                                            <div class="mt-2 text-center">
                                                <small class="text-muted">আরও <?php echo $subjectCount - 10; ?> টি বিষয় আছে</small>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                            <div class="card-footer">
                                <div class="d-grid gap-2">
                                    <a href="subject_groups.php?group_id=<?php echo $group_id; ?>" class="btn btn-outline-primary">
                                        <i class="fas fa-link me-2"></i>বিষয় সংযোগ করুন
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Edit Form -->
                    <div class="col-md-8 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">গ্রুপ সম্পাদনা করুন</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="edit_group.php?id=<?php echo $group_id; ?>">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="group_name" class="form-label">গ্রুপের নাম <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="group_name" name="group_name" value="<?php echo htmlspecialchars($group['group_name']); ?>" required>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="group_code" class="form-label">গ্রুপ কোড <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="group_code" name="group_code" value="<?php echo htmlspecialchars($group['group_code']); ?>" required>
                                        </div>
                                        <div class="col-md-12">
                                            <label for="is_active" class="form-label d-block">অবস্থা</label>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" <?php echo $group['is_active'] ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="is_active">সক্রিয়</label>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <label for="description" class="form-label">বিবরণ</label>
                                            <textarea class="form-control" id="description" name="description" rows="4"><?php echo htmlspecialchars($group['description'] ?? ''); ?></textarea>
                                        </div>
                                        <div class="col-12 mt-4">
                                            <button type="submit" name="update_group" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i>আপডেট করুন
                                            </button>
                                            <a href="groups.php" class="btn btn-secondary ms-2">
                                                <i class="fas fa-times me-2"></i>বাতিল করুন
                                            </a>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Related Information -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">গ্রুপ সম্পর্কিত তথ্য</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title"><i class="fas fa-info-circle me-2 text-primary"></i>গ্রুপ সম্পর্কে</h5>
                                        <p><?php echo !empty($group['description']) ? htmlspecialchars($group['description']) : 'কোন বিবরণ নেই'; ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title"><i class="fas fa-link me-2 text-success"></i>বিষয় সংযোগ</h5>
                                        <p>এই গ্রুপের সাথে বিষয় সংযোগ করতে নিচের বাটনে ক্লিক করুন।</p>
                                        <a href="subject_groups.php?group_id=<?php echo $group_id; ?>" class="btn btn-outline-success">
                                            <i class="fas fa-link me-2"></i>বিষয় সংযোগ করুন
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-dismiss alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert-dismissible');
                alerts.forEach(function(alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);
        });
    </script>
</body>
</html>
