<?php
session_start();

// Check if user is logged in
// Modify the session check to match the actual session variable used in your system
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';
// require_once 'includes/functions.php';  // Commented out until we verify this file exists

// Define minimal functions if needed
if (!function_exists('sanitize_input')) {
    function sanitize_input($data) {
        global $conn;
        return $conn->real_escape_string(trim($data));
    }
}

// Create uploads directory if it doesn't exist
$uploadDir = '../uploads/logos/';
if (!file_exists($uploadDir)) {
    mkdir($uploadDir, 0777, true);
}

$currentPage = basename($_SERVER['PHP_SELF']);

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Handle system reset
    if (isset($_POST['reset_system']) && isset($_POST['reset_confirmation']) && $_POST['reset_confirmation'] === 'RESET') {
        try {
            // Start transaction
            $conn->begin_transaction();

            // Get all tables in the database
            $tables = [];
            $tablesResult = $conn->query("SHOW TABLES");
            while ($row = $tablesResult->fetch_row()) {
                $tables[] = $row[0];
            }

            // Disable foreign key checks temporarily
            $conn->query("SET FOREIGN_KEY_CHECKS = 0");

            // Truncate all tables except admin users
            foreach ($tables as $table) {
                // Skip admin users table to maintain access
                if ($table !== 'users') {
                    $conn->query("TRUNCATE TABLE `$table`");
                }
            }

            // Re-enable foreign key checks
            $conn->query("SET FOREIGN_KEY_CHECKS = 1");

            // Commit transaction
            $conn->commit();

            // Set success message
            $success_message = "সিস্টেম সফলভাবে রিসেট করা হয়েছে। সকল ডাটা মুছে ফেলা হয়েছে।";

            // Redirect to refresh the page after reset
            header("Location: settings.php?reset=success");
            exit();
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            $error_message = "সিস্টেম রিসেট করতে সমস্যা: " . $e->getMessage();
        }
    }

    if (isset($_POST['update_settings'])) {
        // First, ensure the table exists
        $tableExists = $conn->query("SHOW TABLES LIKE 'school_settings'");

        if ($tableExists->num_rows == 0) {
            // Create the table if it doesn't exist
            $createTableSQL = "CREATE TABLE school_settings (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                school_name VARCHAR(255) NOT NULL,
                school_address TEXT,
                school_phone VARCHAR(50),
                school_email VARCHAR(100),
                logo_path VARCHAR(255)
            )";

            if (!$conn->query($createTableSQL)) {
                $error_message = "Error creating table: " . $conn->error;
                // Don't proceed if we can't create the table
                goto form_processing_end;
            }
        } else {
            // Check if required columns exist and add them if they don't
            $requiredColumns = [
                'school_name' => 'VARCHAR(255) NOT NULL',
                'school_address' => 'TEXT',
                'school_phone' => 'VARCHAR(50)',
                'school_email' => 'VARCHAR(100)',
                'logo_path' => 'VARCHAR(255)'
            ];

            foreach ($requiredColumns as $column => $type) {
                $columnExists = $conn->query("SHOW COLUMNS FROM school_settings LIKE '$column'");

                if ($columnExists->num_rows == 0) {
                    $addColumnSQL = "ALTER TABLE school_settings ADD COLUMN $column $type";
                    if (!$conn->query($addColumnSQL)) {
                        $error_message = "Error adding column $column: " . $conn->error;
                        // Continue anyway, we'll try to work with what we have
                    }
                }
            }
        }

        // Example settings update functionality
        $school_name = $conn->real_escape_string($_POST['school_name']);
        $school_address = $conn->real_escape_string($_POST['school_address']);
        $school_phone = $conn->real_escape_string($_POST['school_phone']);
        $school_email = $conn->real_escape_string($_POST['school_email']);

        // Handle logo upload
        $logo_path = isset($settings['logo_path']) ? $settings['logo_path'] : '';

        if (!empty($_FILES['school_logo']['name'])) {
            $file_name = time() . '_' . $_FILES['school_logo']['name'];
            $file_tmp = $_FILES['school_logo']['tmp_name'];
            $file_type = $_FILES['school_logo']['type'];
            $file_ext = strtolower(pathinfo($_FILES['school_logo']['name'], PATHINFO_EXTENSION));

            $allowed_extensions = array("jpg", "jpeg", "png", "gif");

            if (in_array($file_ext, $allowed_extensions)) {
                $upload_path = $uploadDir . $file_name;

                if (move_uploaded_file($file_tmp, $upload_path)) {
                    // Delete old logo if exists
                    if (!empty($logo_path) && file_exists('../' . $logo_path)) {
                        unlink('../' . $logo_path);
                    }

                    $logo_path = 'uploads/logos/' . $file_name;
                } else {
                    $error_message = "Error uploading logo";
                }
            } else {
                $error_message = "Invalid file format. Only JPG, JPEG, PNG and GIF are allowed.";
            }
        }

        // Check if record exists
        $checkQuery = "SELECT COUNT(*) as count FROM school_settings";
        $checkResult = $conn->query($checkQuery);
        $row = $checkResult->fetch_assoc();

        if ($row['count'] == 0) {
            // Insert new record
            $query = "INSERT INTO school_settings (school_name, school_address, school_phone, school_email, logo_path)
                      VALUES ('$school_name', '$school_address', '$school_phone', '$school_email', '$logo_path')";
        } else {
            // Update existing record
            $query = "UPDATE school_settings SET
                      school_name = '$school_name',
                      school_address = '$school_address',
                      school_phone = '$school_phone',
                      school_email = '$school_email',
                      logo_path = '$logo_path'
                      WHERE id = 1";
        }

        if ($conn->query($query)) {
            $success_message = "Settings updated successfully!";
        } else {
            $error_message = "Error updating settings: " . $conn->error;
        }
    }

    form_processing_end: // Label for goto statement
}

// Check if school_settings table exists
$tableExists = $conn->query("SHOW TABLES LIKE 'school_settings'");

if ($tableExists->num_rows == 0) {
    // Create the table if it doesn't exist
    $createTableSQL = "CREATE TABLE school_settings (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        school_name VARCHAR(255) NOT NULL,
        school_address TEXT,
        school_phone VARCHAR(50),
        school_email VARCHAR(100),
        logo_path VARCHAR(255)
    )";

    if (!$conn->query($createTableSQL)) {
        $error_message = "Error creating table: " . $conn->error;
    }
} else {
    // Check if required columns exist and add them if they don't
    $requiredColumns = [
        'school_name' => 'VARCHAR(255) NOT NULL',
        'school_address' => 'TEXT',
        'school_phone' => 'VARCHAR(50)',
        'school_email' => 'VARCHAR(100)',
        'logo_path' => 'VARCHAR(255)'
    ];

    foreach ($requiredColumns as $column => $type) {
        $columnExists = $conn->query("SHOW COLUMNS FROM school_settings LIKE '$column'");

        if ($columnExists->num_rows == 0) {
            $addColumnSQL = "ALTER TABLE school_settings ADD COLUMN $column $type";
            $conn->query($addColumnSQL);
        }
    }
}

// Fetch current settings
$query = "SELECT * FROM school_settings LIMIT 1";
$result = $conn->query($query);

if ($result && $result->num_rows > 0) {
    $settings = $result->fetch_assoc();
} else {
    // Create default settings if none exist
    $insertQuery = "INSERT INTO school_settings (school_name, school_address, school_phone, school_email)
                   VALUES ('আপনার প্রতিষ্ঠানের নাম', 'আপনার প্রতিষ্ঠানের ঠিকানা', '০১৭১২-৩৪৫৬৭৮', '<EMAIL>')";
    if ($conn->query($insertQuery)) {
        $settings = [
            'school_name' => 'আপনার প্রতিষ্ঠানের নাম',
            'school_address' => 'আপনার প্রতিষ্ঠানের ঠিকানা',
            'school_phone' => '০১৭১২-৩৪৫৬৭৮',
            'school_email' => '<EMAIL>',
            'logo_path' => ''
        ];
    } else {
        $error_message = "সেটিংস তৈরি করতে সমস্যা: " . $conn->error;
        $settings = [
            'school_name' => '',
            'school_address' => '',
            'school_phone' => '',
            'school_email' => '',
            'logo_path' => ''
        ];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>সেটিংস | অ্যাডমিন প্যানেল</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <!-- Hind Siliguri Font CSS -->
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: #f8f9fa;
        }
        .logo-preview {
            max-width: 200px;
            max-height: 200px;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 5px;
            margin-top: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .logo-placeholder {
            width: 200px;
            height: 200px;
            border: 2px dashed #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #aaa;
            margin-top: 10px;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 30px;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            padding: 15px 20px;
            font-weight: 600;
            border-radius: 10px 10px 0 0 !important;
        }
        .card-body {
            padding: 25px;
        }
        .form-control, .form-select {
            border-radius: 8px;
            padding: 10px 15px;
            border: 1px solid #ddd;
            transition: all 0.3s;
        }
        .form-control:focus, .form-select:focus {
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.1);
            border-color: #86b7fe;
        }
        .btn-primary {
            background-color: #0d6efd;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s;
        }
        .btn-primary:hover {
            background-color: #0b5ed7;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .form-label {
            font-weight: 500;
            margin-bottom: 8px;
            color: #333;
        }
        .alert {
            border-radius: 8px;
            padding: 15px 20px;
        }
        main {
            padding-top: 20px;
            padding-bottom: 40px;
        }
        .sidebar .nav-link {
            border-radius: 8px;
            margin-bottom: 5px;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
        }
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.2);
        }

        /* System Reset Styles */
        .reset-confirmation-steps .step {
            transition: all 0.3s ease;
        }

        .reset-confirmation-steps .alert {
            margin-bottom: 20px;
        }

        .reset-confirmation-steps .alert-heading {
            color: #721c24;
            font-weight: 600;
        }

        .reset-confirmation-steps ul {
            margin-bottom: 15px;
        }

        .reset-confirmation-steps ul li {
            margin-bottom: 5px;
        }

        #resetSystemBtn {
            transition: all 0.3s ease;
        }

        #resetSystemBtn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        }

        #resetConfirmModal .modal-content {
            border: none;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        #resetConfirmModal .modal-header {
            border-radius: 10px 10px 0 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        #resetConfirmModal .btn-danger {
            background-color: #dc3545;
            border: none;
            transition: all 0.3s ease;
        }

        #resetConfirmModal .btn-danger:hover:not(:disabled) {
            background-color: #c82333;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        }

        #resetConfirmModal .btn-danger:disabled {
            background-color: #dc3545;
            opacity: 0.65;
        }

        #reset_confirmation.is-invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
        }

        /* Animation for steps */
        .reset-confirmation-steps .step:not(.d-none) {
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php
            if (file_exists('includes/sidebar.php')) {
                include 'includes/sidebar.php';
            } else {
                echo '<div class="col-md-3 col-lg-2 sidebar bg-dark text-light p-3">
                    <h4><i class="fas fa-user-shield me-2"></i>অ্যাডমিন মেনু</h4>
                    <ul class="nav flex-column mt-3">
                        <li class="nav-item">
                            <a class="nav-link text-light" href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>ড্যাশবোর্ড</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-light" href="students.php"><i class="fas fa-user-graduate me-2"></i>শিক্ষার্থী</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-light active" href="settings.php"><i class="fas fa-cog me-2"></i>সেটিংস</a>
                        </li>
                    </ul>
                </div>';
            }
            ?>

            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-4 border-bottom">
                    <h1 class="h2">সেটিংস</h1>
                </div>

                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle me-2"></i><?php echo str_replace("Settings updated successfully!", "সেটিংস সফলভাবে আপডেট করা হয়েছে!", $success_message); ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo str_replace("Error updating settings:", "সেটিংস আপডেট করতে সমস্যা:", $error_message); ?>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cog me-2"></i>প্রতিষ্ঠানের সেটিংস</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="" enctype="multipart/form-data">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <label for="school_name" class="form-label">প্রতিষ্ঠানের নাম</label>
                                        <input type="text" class="form-control" id="school_name" name="school_name" value="<?php echo $settings['school_name']; ?>" required>
                                    </div>
                                    <div class="mb-4">
                                        <label for="school_address" class="form-label">প্রতিষ্ঠানের ঠিকানা</label>
                                        <textarea class="form-control" id="school_address" name="school_address" rows="3" required><?php echo $settings['school_address']; ?></textarea>
                                    </div>
                                    <div class="mb-4">
                                        <label for="school_phone" class="form-label">ফোন নম্বর</label>
                                        <input type="text" class="form-control" id="school_phone" name="school_phone" value="<?php echo $settings['school_phone']; ?>">
                                    </div>
                                    <div class="mb-4">
                                        <label for="school_email" class="form-label">ইমেইল ঠিকানা</label>
                                        <input type="email" class="form-control" id="school_email" name="school_email" value="<?php echo $settings['school_email']; ?>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <label for="school_logo" class="form-label">প্রতিষ্ঠানের লোগো</label>
                                        <input type="file" class="form-control" id="school_logo" name="school_logo" accept="image/*" onchange="previewLogo(this)">
                                        <small class="form-text text-muted">সুপারিশকৃত আকার: 200x200 পিক্সেল। সমর্থিত ফরম্যাট: JPG, PNG, GIF।</small>

                                        <?php if (!empty($settings['logo_path']) && file_exists('../' . $settings['logo_path'])): ?>
                                            <div class="mt-3">
                                                <img src="../<?php echo $settings['logo_path']; ?>" alt="প্রতিষ্ঠানের লোগো" class="logo-preview" id="logoPreview">
                                            </div>
                                        <?php else: ?>
                                            <div class="logo-placeholder" id="logoPlaceholder">
                                                <i class="fas fa-image fa-3x"></i>
                                            </div>
                                            <img src="" alt="প্রতিষ্ঠানের লোগো" class="logo-preview d-none" id="logoPreview">
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" name="update_settings" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>সেটিংস সংরক্ষণ করুন
                            </button>
                        </form>
                    </div>
                </div>

                <!-- System Reset Card -->
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>সিস্টেম রিসেট</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h5 class="alert-heading"><i class="fas fa-exclamation-circle me-2"></i>সতর্কতা!</h5>
                            <p>এই অপশনটি সিস্টেমের সমস্ত ডাটা মুছে ফেলবে এবং সিস্টেমকে প্রাথমিক অবস্থায় ফিরিয়ে আনবে। এই প্রক্রিয়া অপরিবর্তনীয় এবং মুছে ফেলা ডাটা পুনরুদ্ধার করা যাবে না।</p>
                            <hr>
                            <p class="mb-0">শুধুমাত্র নতুন প্রতিষ্ঠানের জন্য সিস্টেম সেটআপ করার সময় এই অপশনটি ব্যবহার করুন।</p>
                        </div>

                        <button type="button" class="btn btn-danger" id="resetSystemBtn" data-bs-toggle="modal" data-bs-target="#resetConfirmModal">
                            <i class="fas fa-trash-alt me-2"></i>সিস্টেম রিসেট করুন
                        </button>
                    </div>
                </div>

                <!-- Reset Confirmation Modal -->
                <div class="modal fade" id="resetConfirmModal" tabindex="-1" aria-labelledby="resetConfirmModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header bg-danger text-white">
                                <h5 class="modal-title" id="resetConfirmModalLabel"><i class="fas fa-exclamation-triangle me-2"></i>সিস্টেম রিসেট নিশ্চিতকরণ</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form method="POST" action="" id="resetForm">
                                    <div class="reset-confirmation-steps">
                                        <div class="step" id="step1">
                                            <div class="alert alert-danger">
                                                <h5 class="alert-heading"><i class="fas fa-exclamation-circle me-2"></i>প্রথম সতর্কতা!</h5>
                                                <p>আপনি সিস্টেম রিসেট করতে যাচ্ছেন। এটি সমস্ত ডাটা মুছে ফেলবে, যার মধ্যে রয়েছে:</p>
                                                <ul>
                                                    <li>সমস্ত শিক্ষার্থী তথ্য</li>
                                                    <li>সমস্ত শ্রেণী এবং বিভাগ</li>
                                                    <li>সমস্ত ফি রেকর্ড</li>
                                                    <li>সমস্ত পরীক্ষা এবং ফলাফল</li>
                                                    <li>সমস্ত সেটিংস</li>
                                                </ul>
                                                <p>এই প্রক্রিয়া অপরিবর্তনীয়। আপনি কি এগিয়ে যেতে চান?</p>
                                            </div>
                                            <div class="d-flex justify-content-between">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল করুন</button>
                                                <button type="button" class="btn btn-danger" id="step1NextBtn">হ্যাঁ, এগিয়ে যান</button>
                                            </div>
                                        </div>

                                        <div class="step d-none" id="step2">
                                            <div class="alert alert-danger">
                                                <h5 class="alert-heading"><i class="fas fa-exclamation-circle me-2"></i>দ্বিতীয় সতর্কতা!</h5>
                                                <p>আপনি কি নিশ্চিত যে আপনি সিস্টেম রিসেট করতে চান?</p>
                                                <p>এই প্রক্রিয়া সম্পন্ন হলে, সমস্ত ডাটা স্থায়ীভাবে মুছে যাবে এবং পুনরুদ্ধার করা যাবে না।</p>
                                                <p>আপনি যদি ডাটা সংরক্ষণ করতে চান, তাহলে আগে ব্যাকআপ নিয়ে নিন।</p>
                                            </div>
                                            <div class="d-flex justify-content-between">
                                                <button type="button" class="btn btn-secondary" id="step2PrevBtn">পিছনে যান</button>
                                                <button type="button" class="btn btn-danger" id="step2NextBtn">হ্যাঁ, এগিয়ে যান</button>
                                            </div>
                                        </div>

                                        <div class="step d-none" id="step3">
                                            <div class="alert alert-danger">
                                                <h5 class="alert-heading"><i class="fas fa-exclamation-circle me-2"></i>চূড়ান্ত সতর্কতা!</h5>
                                                <p>এটি আপনার শেষ সুযোগ! সিস্টেম রিসেট করার পর, সমস্ত ডাটা স্থায়ীভাবে মুছে যাবে।</p>
                                                <p>নিশ্চিত করতে, নিচের ঘরে "RESET" লিখুন।</p>
                                            </div>
                                            <div class="mb-3">
                                                <label for="reset_confirmation" class="form-label">নিশ্চিতকরণ কোড:</label>
                                                <input type="text" class="form-control" id="reset_confirmation" name="reset_confirmation" placeholder="RESET" required>
                                                <div class="invalid-feedback">
                                                    সঠিক কোড লিখুন: RESET
                                                </div>
                                            </div>
                                            <div class="d-flex justify-content-between">
                                                <button type="button" class="btn btn-secondary" id="step3PrevBtn">পিছনে যান</button>
                                                <button type="submit" name="reset_system" class="btn btn-danger" id="finalResetBtn" disabled>
                                                    <i class="fas fa-trash-alt me-2"></i>সিস্টেম রিসেট করুন
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // লোগো প্রিভিউ ফাংশন
        function previewLogo(input) {
            const logoPreview = document.getElementById('logoPreview');
            const logoPlaceholder = document.getElementById('logoPlaceholder');

            if (input.files && input.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    logoPreview.src = e.target.result;
                    logoPreview.classList.remove('d-none');
                    if (logoPlaceholder) {
                        logoPlaceholder.classList.add('d-none');
                    }

                    // ফাইল নাম দেখানো
                    const fileName = input.files[0].name;
                    const fileSize = Math.round(input.files[0].size / 1024); // কিলোবাইটে রূপান্তর

                    // ফাইল তথ্য দেখানো
                    const fileInfo = document.createElement('div');
                    fileInfo.className = 'mt-2 small text-muted';
                    fileInfo.innerHTML = `<i class="fas fa-file-image me-1"></i> ${fileName} (${fileSize} KB)`;

                    // আগের ফাইল তথ্য মুছে ফেলা
                    const existingFileInfo = document.querySelector('.file-info');
                    if (existingFileInfo) {
                        existingFileInfo.remove();
                    }

                    fileInfo.classList.add('file-info');
                    input.parentNode.appendChild(fileInfo);
                }

                reader.readAsDataURL(input.files[0]);
            }
        }

        // সেটিংস ফর্ম সাবমিট করার আগে কনফার্মেশন
        document.querySelector('form[enctype="multipart/form-data"]').addEventListener('submit', function(e) {
            const formChanged = document.querySelector('form').querySelector('input, textarea').value !== '';

            if (formChanged) {
                const confirmed = confirm('আপনি কি নিশ্চিত যে আপনি সেটিংস আপডেট করতে চান?');
                if (!confirmed) {
                    e.preventDefault();
                }
            }
        });

        // সিস্টেম রিসেট ফাংশনালিটি
        document.addEventListener('DOMContentLoaded', function() {
            // রিসেট মডাল এলিমেন্টস
            const step1 = document.getElementById('step1');
            const step2 = document.getElementById('step2');
            const step3 = document.getElementById('step3');
            const step1NextBtn = document.getElementById('step1NextBtn');
            const step2PrevBtn = document.getElementById('step2PrevBtn');
            const step2NextBtn = document.getElementById('step2NextBtn');
            const step3PrevBtn = document.getElementById('step3PrevBtn');
            const resetConfirmationInput = document.getElementById('reset_confirmation');
            const finalResetBtn = document.getElementById('finalResetBtn');
            const resetForm = document.getElementById('resetForm');
            const resetModal = document.getElementById('resetConfirmModal');

            // রিসেট মডাল খোলার সময় সব স্টেপ রিসেট করা
            resetModal.addEventListener('show.bs.modal', function() {
                // সব স্টেপ রিসেট করা
                step1.classList.remove('d-none');
                step2.classList.add('d-none');
                step3.classList.add('d-none');

                // ইনপুট ফিল্ড খালি করা
                if (resetConfirmationInput) {
                    resetConfirmationInput.value = '';
                    resetConfirmationInput.classList.remove('is-invalid');
                }

                // ফাইনাল বাটন ডিজেবল করা
                if (finalResetBtn) {
                    finalResetBtn.disabled = true;
                }
            });

            // স্টেপ 1 থেকে স্টেপ 2 এ যাওয়া
            if (step1NextBtn) {
                step1NextBtn.addEventListener('click', function() {
                    step1.classList.add('d-none');
                    step2.classList.remove('d-none');
                });
            }

            // স্টেপ 2 থেকে স্টেপ 1 এ ফিরে যাওয়া
            if (step2PrevBtn) {
                step2PrevBtn.addEventListener('click', function() {
                    step2.classList.add('d-none');
                    step1.classList.remove('d-none');
                });
            }

            // স্টেপ 2 থেকে স্টেপ 3 এ যাওয়া
            if (step2NextBtn) {
                step2NextBtn.addEventListener('click', function() {
                    step2.classList.add('d-none');
                    step3.classList.remove('d-none');
                });
            }

            // স্টেপ 3 থেকে স্টেপ 2 এ ফিরে যাওয়া
            if (step3PrevBtn) {
                step3PrevBtn.addEventListener('click', function() {
                    step3.classList.add('d-none');
                    step2.classList.remove('d-none');
                });
            }

            // কনফার্মেশন কোড চেক করা
            if (resetConfirmationInput) {
                resetConfirmationInput.addEventListener('input', function() {
                    if (this.value === 'RESET') {
                        this.classList.remove('is-invalid');
                        finalResetBtn.disabled = false;
                    } else {
                        this.classList.add('is-invalid');
                        finalResetBtn.disabled = true;
                    }
                });
            }

            // রিসেট ফর্ম সাবমিট করার আগে চূড়ান্ত কনফার্মেশন
            if (resetForm) {
                resetForm.addEventListener('submit', function(e) {
                    if (resetConfirmationInput.value !== 'RESET') {
                        e.preventDefault();
                        resetConfirmationInput.classList.add('is-invalid');
                        return false;
                    }

                    return true;
                });
            }

            // রিসেট সাকসেস মেসেজ চেক করা
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('reset') === 'success') {
                // সাকসেস মেসেজ দেখানো
                const successAlert = document.createElement('div');
                successAlert.className = 'alert alert-success alert-dismissible fade show';
                successAlert.innerHTML = `
                    <i class="fas fa-check-circle me-2"></i>সিস্টেম সফলভাবে রিসেট করা হয়েছে। সকল ডাটা মুছে ফেলা হয়েছে।
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;

                // পেজে মেসেজ যোগ করা
                const mainContent = document.querySelector('main');
                if (mainContent) {
                    mainContent.insertBefore(successAlert, mainContent.firstChild);

                    // URL থেকে প্যারামিটার মুছে ফেলা
                    window.history.replaceState({}, document.title, window.location.pathname);
                }
            }
        });
    </script>
</body>
</html>