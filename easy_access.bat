@echo off
echo ===================================================
echo        সহজ অ্যাক্সেস সেটআপ
echo ===================================================
echo.

echo XAMPP সার্ভার চেক করা হচ্ছে...
echo.

REM Check if XAMPP is running
tasklist /FI "IMAGENAME eq httpd.exe" 2>NUL | find /I /N "httpd.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo Apache সার্ভার চালু আছে।
) else (
    echo Apache সার্ভার চালু নেই। XAMPP স্টার্ট করা হচ্ছে...
    start "" "D:\xampp\xampp-control.exe"
    timeout /t 5 /nobreak > nul
    echo দয়া করে XAMPP কন্ট্রোল প্যানেলে Apache এবং MySQL স্টার্ট করুন।
    echo তারপর যে কোন কী চাপুন...
    pause > nul
)

echo.
echo আপনার প্রজেক্ট ওপেন করা হচ্ছে...
start http://localhost/zfaw

echo.
echo আপনার প্রজেক্ট এখন নিম্নলিখিত URL-এ অ্যাক্সেসযোগ্য:
echo - লোকাল: http://localhost/zfaw
echo.
echo যে কোন কী চাপুন বন্ধ করতে...
pause > nul
