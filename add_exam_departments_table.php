<?php
require_once 'includes/dbh.inc.php';

// Check if exam_departments table exists
$result = $conn->query("SHOW TABLES LIKE 'exam_departments'");
if ($result->num_rows == 0) {
    // Create exam_departments table
    $createTableQuery = "CREATE TABLE IF NOT EXISTS exam_departments (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        exam_id INT(11) NOT NULL,
        department_id INT(11) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (exam_id) REFERENCES exams(id) ON DELETE CASCADE,
        FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
        UNIQUE KEY (exam_id, department_id)
    )";
    
    if ($conn->query($createTableQuery)) {
        echo "exam_departments table created successfully!";
    } else {
        echo "Error creating exam_departments table: " . $conn->error;
    }
} else {
    echo "exam_departments table already exists.";
}
?>
