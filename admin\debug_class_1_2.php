<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Debug Class 1-2 Page</h2>";

// Test 1: Session
echo "<h3>1. Session Test</h3>";
session_start();
if (isset($_SESSION['userId'])) {
    echo "<p style='color: green;'>✅ Session active - User ID: " . $_SESSION['userId'] . "</p>";
    echo "<p style='color: green;'>✅ User Type: " . ($_SESSION['userType'] ?? 'Not set') . "</p>";
} else {
    echo "<p style='color: red;'>❌ No session found</p>";
}

// Test 2: Database connection
echo "<h3>2. Database Connection Test</h3>";
try {
    require_once '../includes/dbh.inc.php';
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    
    // Test query
    $testQuery = "SELECT COUNT(*) as count FROM classes";
    $result = $conn->query($testQuery);
    if ($result) {
        $row = $result->fetch_assoc();
        echo "<p style='color: green;'>✅ Database query successful - Classes count: " . $row['count'] . "</p>";
    } else {
        echo "<p style='color: red;'>❌ Database query failed: " . $conn->error . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
}

// Test 3: Check if classes exist
echo "<h3>3. Class 1-2 Data Test</h3>";
try {
    $classQuery = "SELECT id, class_name FROM classes WHERE class_name IN ('ONE', 'TWO', 'ক্লাস ১', 'ক্লাস ২', '১', '২', '1', '2')";
    $classResult = $conn->query($classQuery);
    
    if ($classResult && $classResult->num_rows > 0) {
        echo "<p style='color: green;'>✅ Class 1-2 data found:</p>";
        echo "<ul>";
        while ($class = $classResult->fetch_assoc()) {
            echo "<li>ID: " . $class['id'] . " - Name: " . htmlspecialchars($class['class_name']) . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: orange;'>⚠️ No class 1-2 data found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Class query failed: " . $e->getMessage() . "</p>";
}

// Test 4: Check students
echo "<h3>4. Students Test</h3>";
try {
    $studentQuery = "
        SELECT s.id, s.student_name, s.roll_number, c.class_name
        FROM students s
        JOIN classes c ON s.class_id = c.id
        WHERE c.class_name IN ('ONE', 'TWO', 'ক্লাস ১', 'ক্লাস ২', '১', '২', '1', '2')
        LIMIT 5
    ";
    $studentResult = $conn->query($studentQuery);
    
    if ($studentResult && $studentResult->num_rows > 0) {
        echo "<p style='color: green;'>✅ Class 1-2 students found:</p>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Name</th><th>Roll</th><th>Class</th></tr>";
        while ($student = $studentResult->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $student['id'] . "</td>";
            echo "<td>" . htmlspecialchars($student['student_name']) . "</td>";
            echo "<td>" . $student['roll_number'] . "</td>";
            echo "<td>" . htmlspecialchars($student['class_name']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ No class 1-2 students found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Student query failed: " . $e->getMessage() . "</p>";
}

// Test 5: Check if original file has syntax errors
echo "<h3>5. File Syntax Test</h3>";
$originalFile = 'class_exam_primary_lower_1_2.php';
if (file_exists($originalFile)) {
    echo "<p style='color: green;'>✅ Original file exists</p>";
    
    // Check file size
    $fileSize = filesize($originalFile);
    echo "<p>File size: " . number_format($fileSize) . " bytes</p>";
    
    // Try to include the file in a controlled way
    ob_start();
    $includeError = false;
    try {
        // Don't actually include, just check if it's readable
        $content = file_get_contents($originalFile, false, null, 0, 1000);
        if ($content !== false) {
            echo "<p style='color: green;'>✅ File is readable</p>";
        } else {
            echo "<p style='color: red;'>❌ File is not readable</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ File read error: " . $e->getMessage() . "</p>";
    }
    ob_end_clean();
} else {
    echo "<p style='color: red;'>❌ Original file not found</p>";
}

// Test 6: PHP version and extensions
echo "<h3>6. PHP Environment Test</h3>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>MySQL Extension: " . (extension_loaded('mysqli') ? '✅ Loaded' : '❌ Not loaded') . "</p>";
echo "<p>Session Extension: " . (extension_loaded('session') ? '✅ Loaded' : '❌ Not loaded') . "</p>";

// Test 7: Memory and execution
echo "<h3>7. System Resources</h3>";
echo "<p>Memory Limit: " . ini_get('memory_limit') . "</p>";
echo "<p>Max Execution Time: " . ini_get('max_execution_time') . " seconds</p>";
echo "<p>Current Memory Usage: " . number_format(memory_get_usage(true)) . " bytes</p>";

echo "<br><a href='class_exam_primary_lower_1_2.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Try Original Page</a>";
echo " <a href='dashboard.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>Dashboard</a>";

if (isset($conn)) {
    $conn->close();
}
?>
