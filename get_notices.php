<?php
// This file loads notices asynchronously to prevent hanging the main page

// Include database connection
require_once "includes/dbh.inc.php";

// Check if notices table exists and fetch notices
$notices_query = "SHOW TABLES LIKE 'notices'";
$notices_result = $conn->query($notices_query);

if ($notices_result && $notices_result->num_rows > 0) {
    $sql = "SELECT * FROM notices ORDER BY date DESC LIMIT 5";
    $result = $conn->query($sql);
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            echo '<a href="notice_details.php?id=' . $row['id'] . '" class="list-group-item list-group-item-action">';
            echo '<div class="d-flex w-100 justify-content-between">';
            echo '<h5 class="mb-1">' . $row['title'] . '</h5>';
            echo '<small>' . date('d M Y', strtotime($row['date'])) . '</small>';
            echo '</div>';
            echo '<p class="mb-1">' . substr(strip_tags($row['content']), 0, 100) . '...</p>';
            echo '</a>';
        }
    } else {
        echo '<div class="alert alert-info">কোন নোটিশ পাওয়া যায়নি।</div>';
    }
} else {
    echo '<div class="alert alert-info">নোটিশ সিস্টেম এখনও সেটআপ করা হয়নি।</div>';
}

// Close database connection
$conn->close();
?>
