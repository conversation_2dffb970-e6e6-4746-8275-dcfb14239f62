<?php
// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "college_management";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Check if classes table exists
$tableCheck = $conn->query("SHOW TABLES LIKE 'classes'");
if ($tableCheck->num_rows == 0) {
    // Create classes table
    $classesTableQuery = "CREATE TABLE IF NOT EXISTS classes (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        class_name VARCHAR(50) NOT NULL,
        department_id INT(11) NULL,
        section VARCHAR(20) DEFAULT 'A',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
    )";

    if ($conn->query($classesTableQuery)) {
        echo "Classes table created successfully!<br>";
    } else {
        echo "Error creating classes table: " . $conn->error . "<br>";
    }
}

// Check if any classes exist
$classCheckQuery = "SELECT id FROM classes LIMIT 1";
$classResult = $conn->query($classCheckQuery);

if ($classResult->num_rows == 0) {
    // Get a department
    $deptCheckQuery = "SELECT id FROM departments LIMIT 1";
    $deptResult = $conn->query($deptCheckQuery);
    
    $deptId = null;
    if ($deptResult->num_rows > 0) {
        $deptRow = $deptResult->fetch_assoc();
        $deptId = $deptRow['id'];
    } else {
        // Create a department
        $createDeptSql = "INSERT INTO departments (department_name, description) 
                        VALUES ('Science', 'Science Department')";
        if ($conn->query($createDeptSql)) {
            $deptId = $conn->insert_id;
            echo "Department created successfully.<br>";
        }
    }
    
    // Add sample classes
    $classes = [
        ['Class 9', 'Science'],
        ['Class 10', 'Science'],
        ['Class 11', 'Science'],
        ['Class 12', 'Science']
    ];
    
    foreach ($classes as $class) {
        $insertClassQuery = "INSERT INTO classes (class_name, section, department_id) 
                           VALUES ('{$class[0]}', '{$class[1]}', $deptId)";
                           
        if ($conn->query($insertClassQuery)) {
            echo "Class '{$class[0]} {$class[1]}' added successfully!<br>";
        } else {
            echo "Error adding class: " . $conn->error . "<br>";
        }
    }
} else {
    echo "Classes already exist.<br>";
}

echo "<p>Setup complete. <a href='create_exams_table.php'>Continue to create exams tables</a></p>";

$conn->close();
?> 