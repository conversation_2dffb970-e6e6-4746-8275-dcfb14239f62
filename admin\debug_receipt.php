<?php
session_start();
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

echo "<h2>ডাটাবেস ডিবাগিং - রিসিপ্ট তথ্য</h2>";

// Check which tables exist
echo "<h3>উপলব্ধ টেবিল:</h3>";
$tables = ['payments', 'fee_payments', 'fees', 'students', 'classes'];
foreach ($tables as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "<p style='color: green;'>✓ $table টেবিল পাওয়া গেছে</p>";
        
        // Show table structure
        $structure = $conn->query("DESCRIBE $table");
        echo "<details><summary>$table টেবিলের স্ট্রাকচার</summary>";
        echo "<table border='1'><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr>";
        while ($row = $structure->fetch_assoc()) {
            echo "<tr><td>{$row['Field']}</td><td>{$row['Type']}</td><td>{$row['Null']}</td><td>{$row['Key']}</td></tr>";
        }
        echo "</table></details>";
        
        // Show sample data
        $sampleData = $conn->query("SELECT * FROM $table LIMIT 3");
        if ($sampleData->num_rows > 0) {
            echo "<details><summary>$table টেবিলের নমুনা ডেটা</summary>";
            echo "<table border='1'>";
            $first = true;
            while ($row = $sampleData->fetch_assoc()) {
                if ($first) {
                    echo "<tr>";
                    foreach (array_keys($row) as $key) {
                        echo "<th>$key</th>";
                    }
                    echo "</tr>";
                    $first = false;
                }
                echo "<tr>";
                foreach ($row as $value) {
                    echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
                }
                echo "</tr>";
            }
            echo "</table></details>";
        }
    } else {
        echo "<p style='color: red;'>✗ $table টেবিল পাওয়া যায়নি</p>";
    }
}

// Check for recent receipts
echo "<h3>সাম্প্রতিক রিসিপ্ট:</h3>";

// Check payments table
$paymentsQuery = "SELECT p.receipt_no, p.amount, p.payment_date, f.fee_type, s.first_name, s.last_name, s.roll_no, c.class_name 
                  FROM payments p 
                  JOIN fees f ON p.fee_id = f.id 
                  JOIN students s ON f.student_id = s.id 
                  LEFT JOIN classes c ON s.class_id = c.id 
                  WHERE p.receipt_no IS NOT NULL 
                  ORDER BY p.created_at DESC LIMIT 5";

$result = $conn->query($paymentsQuery);
if ($result && $result->num_rows > 0) {
    echo "<h4>payments টেবিল থেকে:</h4>";
    echo "<table border='1'><tr><th>রিসিপ্ট নং</th><th>পরিমাণ</th><th>তারিখ</th><th>ফি ধরন</th><th>শিক্ষার্থী</th><th>রোল</th><th>ক্লাস</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td><a href='receipt_final.php?receipt_no=" . urlencode($row['receipt_no']) . "'>" . htmlspecialchars($row['receipt_no']) . "</a></td>";
        echo "<td>৳" . number_format($row['amount'], 2) . "</td>";
        echo "<td>" . date('d/m/Y', strtotime($row['payment_date'])) . "</td>";
        echo "<td>" . htmlspecialchars($row['fee_type']) . "</td>";
        echo "<td>" . htmlspecialchars($row['first_name'] . ' ' . $row['last_name']) . "</td>";
        echo "<td>" . htmlspecialchars($row['roll_no'] ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($row['class_name'] ?? 'N/A') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>payments টেবিলে কোন রিসিপ্ট পাওয়া যায়নি</p>";
}

// Check fee_payments table
$feePaymentsQuery = "SELECT fp.receipt_no, fp.amount, fp.payment_date, f.fee_type, s.first_name, s.last_name, s.roll_no, c.class_name 
                     FROM fee_payments fp 
                     JOIN fees f ON fp.fee_id = f.id 
                     JOIN students s ON f.student_id = s.id 
                     LEFT JOIN classes c ON s.class_id = c.id 
                     WHERE fp.receipt_no IS NOT NULL 
                     ORDER BY fp.created_at DESC LIMIT 5";

$result2 = $conn->query($feePaymentsQuery);
if ($result2 && $result2->num_rows > 0) {
    echo "<h4>fee_payments টেবিল থেকে:</h4>";
    echo "<table border='1'><tr><th>রিসিপ্ট নং</th><th>পরিমাণ</th><th>তারিখ</th><th>ফি ধরন</th><th>শিক্ষার্থী</th><th>রোল</th><th>ক্লাস</th></tr>";
    while ($row = $result2->fetch_assoc()) {
        echo "<tr>";
        echo "<td><a href='receipt_final.php?receipt_no=" . urlencode($row['receipt_no']) . "'>" . htmlspecialchars($row['receipt_no']) . "</a></td>";
        echo "<td>৳" . number_format($row['amount'], 2) . "</td>";
        echo "<td>" . date('d/m/Y', strtotime($row['payment_date'])) . "</td>";
        echo "<td>" . htmlspecialchars($row['fee_type']) . "</td>";
        echo "<td>" . htmlspecialchars($row['first_name'] . ' ' . $row['last_name']) . "</td>";
        echo "<td>" . htmlspecialchars($row['roll_no'] ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($row['class_name'] ?? 'N/A') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>fee_payments টেবিলে কোন রিসিপ্ট পাওয়া যায়নি</p>";
}

echo "<br><a href='fee_management.php'>ফি ম্যানেজমেন্টে ফিরে যান</a>";
?>
