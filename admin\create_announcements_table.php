<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Create announcements table
$tableQuery = "CREATE TABLE IF NOT EXISTS announcements (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    target_group ENUM('all', 'students', 'teachers', 'staff', 'admin') DEFAULT 'all',
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_by INT(11),
    expire_date DATE NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($tableQuery)) {
    echo "<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9;'>";
    echo "<h2 style='color: #28a745;'>সফল!</h2>";
    echo "<p>ঘোষণা টেবিল সফলভাবে তৈরি করা হয়েছে।</p>";
    echo "<p><a href='announcements.php' style='display: inline-block; padding: 10px 15px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px;'>ঘোষণা পেজে ফিরে যান</a></p>";
    echo "</div>";
} else {
    echo "<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9;'>";
    echo "<h2 style='color: #dc3545;'>ত্রুটি!</h2>";
    echo "<p>টেবিল তৈরি করতে সমস্যা হয়েছে: " . $conn->error . "</p>";
    echo "<p><a href='announcements.php' style='display: inline-block; padding: 10px 15px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px;'>ঘোষণা পেজে ফিরে যান</a></p>";
    echo "</div>";
}
?>
