<?php
require_once 'includes/dbh.inc.php';

// Check if classes table exists
$tableCheckQuery = "SHOW TABLES LIKE 'classes'";
$tableCheckResult = $conn->query($tableCheckQuery);
if (!$tableCheckResult || $tableCheckResult->num_rows == 0) {
    echo "Classes table doesn't exist!";
    exit;
}

// Count total classes
$countQuery = "SELECT COUNT(*) as count FROM classes";
$countResult = $conn->query($countQuery);
$countRow = $countResult->fetch_assoc();
echo "Total classes: " . $countRow['count'] . "\n\n";

// Show all classes
echo "All classes:\n";
$classesQuery = "SELECT * FROM classes ORDER BY class_name";
$classesResult = $conn->query($classesQuery);

if ($classesResult && $classesResult->num_rows > 0) {
    while ($class = $classesResult->fetch_assoc()) {
        echo "ID: " . $class['id'] . 
             ", Name: " . $class['class_name'] . 
             ", Department ID: " . ($class['department_id'] ?? 'NULL') . "\n";
    }
} else {
    echo "No classes found.\n";
}

// Check sessions table
echo "\nChecking sessions table:\n";
$tableCheckQuery = "SHOW TABLES LIKE 'sessions'";
$tableCheckResult = $conn->query($tableCheckQuery);
if (!$tableCheckResult || $tableCheckResult->num_rows == 0) {
    echo "Sessions table doesn't exist!";
    exit;
}

// Count total sessions
$countQuery = "SELECT COUNT(*) as count FROM sessions";
$countResult = $conn->query($countQuery);
$countRow = $countResult->fetch_assoc();
echo "Total sessions: " . $countRow['count'] . "\n\n";

// Show all sessions
echo "All sessions:\n";
$sessionsQuery = "SELECT * FROM sessions ORDER BY session_name";
$sessionsResult = $conn->query($sessionsQuery);

if ($sessionsResult && $sessionsResult->num_rows > 0) {
    while ($session = $sessionsResult->fetch_assoc()) {
        echo "ID: " . $session['id'] . 
             ", Name: " . $session['session_name'] . "\n";
    }
} else {
    echo "No sessions found.\n";
}
