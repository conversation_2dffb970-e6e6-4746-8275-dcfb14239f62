<?php
session_start();

// Check if user is logged in and is a teacher
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'teacher') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if student ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: students.php");
    exit();
}

$studentId = $_GET['id'];

// Get teacher information
$userId = $_SESSION['userId'];
$username = $_SESSION['username'] ?? '';

// First try to get teacher by user_id
$sql = "SELECT t.*, d.department_name 
        FROM teachers t
        LEFT JOIN departments d ON t.department_id = d.id
        WHERE t.user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();

// If no results, try to get by username
if ($result->num_rows === 0 && !empty($username)) {
    $sql = "SELECT t.*, d.department_name 
            FROM teachers t
            LEFT JOIN departments d ON t.department_id = d.id
            WHERE t.username = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
}

$teacher = $result->fetch_assoc();

// If still no teacher found, redirect to login
if (!$teacher) {
    header("Location: ../index.php");
    exit();
}

// Get student information
try {
    $studentQuery = "SELECT s.*, c.class_name, d.department_name
                    FROM students s
                    LEFT JOIN classes c ON s.class_id = c.id
                    LEFT JOIN departments d ON s.department_id = d.id
                    WHERE s.id = ?";
    $stmt = $conn->prepare($studentQuery);
    $stmt->bind_param("i", $studentId);
    $stmt->execute();
    $studentResult = $stmt->get_result();
    
    if ($studentResult->num_rows === 0) {
        header("Location: students.php");
        exit();
    }
    
    $student = $studentResult->fetch_assoc();
    
    // Check if student belongs to teacher's department
    if ($teacher['department_id'] != $student['department_id']) {
        // Allow access only if teacher has admin privileges or belongs to same department
        if ($_SESSION['userType'] !== 'admin') {
            header("Location: students.php");
            exit();
        }
    }
    
    // Get student's subjects
    $subjectsQuery = "SELECT ss.*, s.subject_name, s.subject_code
                     FROM student_subjects ss
                     JOIN subjects s ON ss.subject_id = s.id
                     WHERE ss.student_id = ?";
    $stmt = $conn->prepare($subjectsQuery);
    $stmt->bind_param("i", $student['id']);
    $stmt->execute();
    $subjects = $stmt->get_result();
    
    // Get student's attendance
    $attendanceQuery = "SELECT a.*, s.subject_name
                       FROM attendance a
                       LEFT JOIN subjects s ON a.subject_id = s.id
                       WHERE a.student_id = ?
                       ORDER BY a.date DESC
                       LIMIT 10";
    $stmt = $conn->prepare($attendanceQuery);
    $stmt->bind_param("i", $student['id']);
    $stmt->execute();
    $attendance = $stmt->get_result();
    
    // Get student's results
    $resultsQuery = "SELECT r.*, e.exam_name, s.subject_name
                    FROM results r
                    LEFT JOIN exams e ON r.exam_id = e.id
                    LEFT JOIN subjects s ON r.subject_id = s.id
                    WHERE r.student_id = ?
                    ORDER BY r.date DESC
                    LIMIT 10";
    $stmt = $conn->prepare($resultsQuery);
    $stmt->bind_param("i", $student['id']);
    $stmt->execute();
    $results = $stmt->get_result();
    
} catch (Exception $e) {
    // Handle error gracefully
    $error_msg = "শিক্ষার্থীর তথ্য লোড করতে সমস্যা হয়েছে: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include __DIR__ . '/includes/global-head.php'; ?>
    <title>Student Details - Teacher Panel</title>
    
    <style>
        body, html, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, select, textarea, label, li, th, td {
            font-family: 'Hind Siliguri', sans-serif;
        }
        
        .profile-header {
            background-color: #f8f9fa;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .student-img {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid #fff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
        }
        
        .nav-tabs .nav-link {
            color: #495057;
        }
        
        .nav-tabs .nav-link.active {
            color: #007bff;
            font-weight: bold;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        .attendance-present {
            background-color: #d4edda;
        }
        
        .attendance-absent {
            background-color: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>Teacher Panel</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user me-2"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> Students
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> Courses
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> Exams
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> Results
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">
                            <i class="fas fa-calendar-check me-2"></i> Attendance
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2>শিক্ষার্থী বিবরণ</h2>
                            <a href="students.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i> ফিরে যান
                            </a>
                        </div>
                        
                        <?php if (isset($error_msg)): ?>
                            <div class="alert alert-danger">
                                <?php echo $error_msg; ?>
                            </div>
                        <?php else: ?>
                            <!-- Student Profile -->
                            <div class="card profile-header mb-4">
                                <div class="row g-0">
                                    <div class="col-md-3 text-center">
                                        <?php if (!empty($student['profile_photo'])): ?>
                                            <img src="../uploads/student_photos/<?php echo $student['profile_photo']; ?>" alt="Student" class="student-img">
                                        <?php else: ?>
                                            <img src="https://via.placeholder.com/150" alt="Student" class="student-img">
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-md-9">
                                        <div class="card-body">
                                            <h3><?php echo $student['first_name'] . ' ' . $student['last_name']; ?></h3>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <p><strong>শিক্ষার্থী আইডি:</strong> <?php echo $student['student_id']; ?></p>
                                                    <p><strong>শ্রেণী:</strong> <?php echo $student['class_name'] ?? 'N/A'; ?></p>
                                                    <p><strong>বিভাগ:</strong> <?php echo $student['department_name'] ?? 'N/A'; ?></p>
                                                    <p><strong>ব্যাচ:</strong> <?php echo $student['batch'] ?? 'N/A'; ?></p>
                                                </div>
                                                <div class="col-md-6">
                                                    <p><strong>ইমেইল:</strong> <?php echo $student['email'] ?? 'N/A'; ?></p>
                                                    <p><strong>ফোন:</strong> <?php echo $student['phone'] ?? 'N/A'; ?></p>
                                                    <p><strong>জন্ম তারিখ:</strong> 
                                                        <?php 
                                                        echo !empty($student['date_of_birth']) 
                                                            ? date('d F, Y', strtotime($student['date_of_birth'])) 
                                                            : 'N/A'; 
                                                        ?>
                                                    </p>
                                                    <p><strong>ভর্তির তারিখ:</strong> 
                                                        <?php 
                                                        echo !empty($student['admission_date']) 
                                                            ? date('d F, Y', strtotime($student['admission_date'])) 
                                                            : 'N/A'; 
                                                        ?>
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Tabs for different information -->
                            <div class="card">
                                <div class="card-body">
                                    <ul class="nav nav-tabs" id="studentTabs" role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="subjects-tab" data-bs-toggle="tab" data-bs-target="#subjects" type="button" role="tab" aria-controls="subjects" aria-selected="true">বিষয়সমূহ</button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="attendance-tab" data-bs-toggle="tab" data-bs-target="#attendance" type="button" role="tab" aria-controls="attendance" aria-selected="false">উপস্থিতি</button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="results-tab" data-bs-toggle="tab" data-bs-target="#results" type="button" role="tab" aria-controls="results" aria-selected="false">ফলাফল</button>
                                        </li>
                                    </ul>
                                    
                                    <div class="tab-content p-3" id="studentTabsContent">
                                        <!-- Subjects Tab -->
                                        <div class="tab-pane fade show active" id="subjects" role="tabpanel" aria-labelledby="subjects-tab">
                                            <div class="table-responsive">
                                                <table class="table table-hover">
                                                    <thead>
                                                        <tr>
                                                            <th>বিষয় কোড</th>
                                                            <th>বিষয়ের নাম</th>
                                                            <th>স্ট্যাটাস</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php if (isset($subjects) && $subjects->num_rows > 0): ?>
                                                            <?php while ($subject = $subjects->fetch_assoc()): ?>
                                                                <tr>
                                                                    <td><?php echo $subject['subject_code']; ?></td>
                                                                    <td><?php echo $subject['subject_name']; ?></td>
                                                                    <td>
                                                                        <?php
                                                                            $statusClass = '';
                                                                            $statusText = '';
                                                                            
                                                                            if ($subject['status'] == 'active') {
                                                                                $statusClass = 'bg-success';
                                                                                $statusText = 'সক্রিয়';
                                                                            } else if ($subject['status'] == 'completed') {
                                                                                $statusClass = 'bg-primary';
                                                                                $statusText = 'সম্পন্ন';
                                                                            } else if ($subject['status'] == 'dropped') {
                                                                                $statusClass = 'bg-danger';
                                                                                $statusText = 'বাদ দেওয়া';
                                                                            } else {
                                                                                $statusClass = 'bg-secondary';
                                                                                $statusText = 'অজানা';
                                                                            }
                                                                        ?>
                                                                        <span class="badge <?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                                                    </td>
                                                                </tr>
                                                            <?php endwhile; ?>
                                                        <?php else: ?>
                                                            <tr>
                                                                <td colspan="3" class="text-center">কোন বিষয় খুঁজে পাওয়া যায়নি</td>
                                                            </tr>
                                                        <?php endif; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        
                                        <!-- Attendance Tab -->
                                        <div class="tab-pane fade" id="attendance" role="tabpanel" aria-labelledby="attendance-tab">
                                            <div class="table-responsive">
                                                <table class="table table-hover">
                                                    <thead>
                                                        <tr>
                                                            <th>তারিখ</th>
                                                            <th>বিষয়</th>
                                                            <th>স্ট্যাটাস</th>
                                                            <th>মন্তব্য</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php if (isset($attendance) && $attendance->num_rows > 0): ?>
                                                            <?php while ($attend = $attendance->fetch_assoc()): ?>
                                                                <tr class="<?php echo $attend['status'] == 'present' ? 'attendance-present' : 'attendance-absent'; ?>">
                                                                    <td><?php echo date('d M Y', strtotime($attend['date'])); ?></td>
                                                                    <td><?php echo $attend['subject_name'] ?? 'N/A'; ?></td>
                                                                    <td>
                                                                        <?php if ($attend['status'] == 'present'): ?>
                                                                            <span class="badge bg-success">উপস্থিত</span>
                                                                        <?php else: ?>
                                                                            <span class="badge bg-danger">অনুপস্থিত</span>
                                                                        <?php endif; ?>
                                                                    </td>
                                                                    <td><?php echo $attend['remarks'] ?? ''; ?></td>
                                                                </tr>
                                                            <?php endwhile; ?>
                                                        <?php else: ?>
                                                            <tr>
                                                                <td colspan="4" class="text-center">কোন উপস্থিতি রেকর্ড খুঁজে পাওয়া যায়নি</td>
                                                            </tr>
                                                        <?php endif; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        
                                        <!-- Results Tab -->
                                        <div class="tab-pane fade" id="results" role="tabpanel" aria-labelledby="results-tab">
                                            <div class="table-responsive">
                                                <table class="table table-hover">
                                                    <thead>
                                                        <tr>
                                                            <th>পরীক্ষা</th>
                                                            <th>বিষয়</th>
                                                            <th>প্রাপ্ত নম্বর</th>
                                                            <th>মোট নম্বর</th>
                                                            <th>গ্রেড</th>
                                                            <th>তারিখ</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php if (isset($results) && $results->num_rows > 0): ?>
                                                            <?php while ($result = $results->fetch_assoc()): ?>
                                                                <tr>
                                                                    <td><?php echo $result['exam_name'] ?? 'N/A'; ?></td>
                                                                    <td><?php echo $result['subject_name'] ?? 'N/A'; ?></td>
                                                                    <td><?php echo $result['marks_obtained']; ?></td>
                                                                    <td><?php echo $result['total_marks']; ?></td>
                                                                    <td><?php echo $result['grade'] ?? 'N/A'; ?></td>
                                                                    <td><?php echo date('d M Y', strtotime($result['date'])); ?></td>
                                                                </tr>
                                                            <?php endwhile; ?>
                                                        <?php else: ?>
                                                            <tr>
                                                                <td colspan="6" class="text-center">কোন ফলাফল খুঁজে পাওয়া যায়নি</td>
                                                            </tr>
                                                        <?php endif; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
