<?php
session_start();
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$receiptNo = $_GET['receipt_no'] ?? '';
$payment = null;
$errorMessage = '';

if (empty($receiptNo)) {
    header("Location: payment_search.php");
    exit();
}

try {
    // Step 1: Get payment info
    $paymentQuery = "SELECT * FROM fee_payments WHERE receipt_no = ? LIMIT 1";
    $stmt = $conn->prepare($paymentQuery);
    $stmt->bind_param("s", $receiptNo);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        $errorMessage = 'রিসিপ্ট পাওয়া যায়নি!';
    } else {
        $payment = $result->fetch_assoc();
        
        // Initialize default values
        $payment['fee_type'] = 'Unknown Fee';
        $payment['student_name'] = 'Unknown Student';
        $payment['roll_no'] = 'N/A';
        $payment['class_name'] = 'N/A';
        
        // Step 2: Get fee and student info
        try {
            $feeQuery = "SELECT fee_type, student_id FROM fees WHERE id = ? LIMIT 1";
            $feeStmt = $conn->prepare($feeQuery);
            $feeStmt->bind_param("i", $payment['fee_id']);
            $feeStmt->execute();
            $feeResult = $feeStmt->get_result();
            
            if ($feeResult->num_rows > 0) {
                $feeData = $feeResult->fetch_assoc();
                $payment['fee_type'] = $feeData['fee_type'];
                
                // Get student info
                $studentQuery = "SELECT first_name, last_name, roll_no, student_id, class_id FROM students WHERE id = ? LIMIT 1";
                $studentStmt = $conn->prepare($studentQuery);
                $studentStmt->bind_param("i", $feeData['student_id']);
                $studentStmt->execute();
                $studentResult = $studentStmt->get_result();
                
                if ($studentResult->num_rows > 0) {
                    $studentData = $studentResult->fetch_assoc();
                    $payment['student_name'] = trim($studentData['first_name'] . ' ' . $studentData['last_name']);
                    $payment['roll_no'] = $studentData['roll_no'] ?? $studentData['student_id'] ?? 'N/A';
                    
                    // Get class name
                    if (!empty($studentData['class_id'])) {
                        $classQuery = "SELECT class_name FROM classes WHERE id = ? LIMIT 1";
                        $classStmt = $conn->prepare($classQuery);
                        $classStmt->bind_param("i", $studentData['class_id']);
                        $classStmt->execute();
                        $classResult = $classStmt->get_result();
                        
                        if ($classResult->num_rows > 0) {
                            $classData = $classResult->fetch_assoc();
                            $payment['class_name'] = $classData['class_name'];
                        }
                    }
                }
            }
        } catch (Exception $e) {
            // Keep default values
        }
    }
} catch (Exception $e) {
    $errorMessage = 'ডাটাবেস এরর: ' . $e->getMessage();
}

if (!empty($errorMessage)) {
    echo "<div style='text-align: center; padding: 50px;'>";
    echo "<h3>সমস্যা!</h3>";
    echo "<p>" . htmlspecialchars($errorMessage) . "</p>";
    echo "<a href='payment_search.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ফিরে যান</a>";
    echo "</div>";
    exit();
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>প্রিন্ট রিসিপ্ট - <?= htmlspecialchars($receiptNo) ?></title>
    <style>
        body { 
            font-family: 'Hind Siliguri', Arial, sans-serif; 
            margin: 0;
            padding: 20px;
            background: white;
            color: #000;
        }
        .receipt-container { 
            max-width: 600px; 
            margin: 0 auto; 
            border: 2px solid #000;
            padding: 20px;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        .school-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .school-info {
            font-size: 14px;
            margin-bottom: 10px;
        }
        .receipt-title {
            font-size: 18px;
            font-weight: bold;
            background: #000;
            color: white;
            padding: 5px;
            margin-top: 10px;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .info-table td {
            padding: 8px;
            border-bottom: 1px solid #ccc;
        }
        .info-table .label {
            font-weight: bold;
            width: 40%;
        }
        .amount-section {
            text-align: center;
            border: 2px solid #000;
            padding: 15px;
            margin: 20px 0;
            background: #f9f9f9;
        }
        .amount-text {
            font-size: 14px;
            margin-bottom: 5px;
        }
        .amount-number {
            font-size: 28px;
            font-weight: bold;
        }
        .footer {
            text-align: center;
            border-top: 2px solid #000;
            padding-top: 15px;
            margin-top: 20px;
            font-size: 12px;
        }
        .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }
        .signature-box {
            text-align: center;
            width: 45%;
        }
        .signature-line {
            border-top: 1px solid #000;
            margin-top: 40px;
            padding-top: 5px;
        }
        @media print {
            body { margin: 0; padding: 10px; }
            .no-print { display: none !important; }
        }
        .print-button {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <button class="print-button no-print" onclick="window.print()">
        প্রিন্ট করুন
    </button>

    <div class="receipt-container">
        <!-- Header -->
        <div class="header">
            <div class="school-name">জাফর আহমদ ফতেহ আলী ওয়াকফ কলেজ</div>
            <div class="school-info">ঢাকা, বাংলাদেশ</div>
            <div class="school-info">ফোন: ০১৭১২৩৪৫৬৭৮</div>
            <div class="receipt-title">ফি পেমেন্ট রিসিপ্ট</div>
        </div>

        <!-- Receipt Info -->
        <table class="info-table">
            <tr>
                <td class="label">রিসিপ্ট নং:</td>
                <td><?= htmlspecialchars($payment['receipt_no']) ?></td>
            </tr>
            <tr>
                <td class="label">তারিখ:</td>
                <td><?= date('d/m/Y', strtotime($payment['payment_date'])) ?></td>
            </tr>
            <tr>
                <td class="label">পেমেন্ট মাধ্যম:</td>
                <td><?= $payment['payment_method'] === 'cash' ? 'নগদ' : htmlspecialchars($payment['payment_method']) ?></td>
            </tr>
        </table>

        <!-- Student Info -->
        <table class="info-table">
            <tr>
                <td class="label">শিক্ষার্থীর নাম:</td>
                <td><?= htmlspecialchars($payment['student_name']) ?></td>
            </tr>
            <?php if ($payment['roll_no'] !== 'N/A'): ?>
            <tr>
                <td class="label">রোল নং:</td>
                <td><?= htmlspecialchars($payment['roll_no']) ?></td>
            </tr>
            <?php endif; ?>
            <?php if ($payment['class_name'] !== 'N/A'): ?>
            <tr>
                <td class="label">শ্রেণী:</td>
                <td><?= htmlspecialchars($payment['class_name']) ?></td>
            </tr>
            <?php endif; ?>
            <tr>
                <td class="label">ফি ধরন:</td>
                <td><?= htmlspecialchars($payment['fee_type']) ?></td>
            </tr>
        </table>

        <!-- Amount -->
        <div class="amount-section">
            <div class="amount-text">পরিশোধিত পরিমাণ</div>
            <div class="amount-number">৳<?= number_format($payment['amount'], 2) ?></div>
        </div>

        <?php if (!empty($payment['notes'])): ?>
        <!-- Notes -->
        <table class="info-table">
            <tr>
                <td class="label">নোট:</td>
                <td><?= htmlspecialchars($payment['notes']) ?></td>
            </tr>
        </table>
        <?php endif; ?>

        <!-- Signature Section -->
        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-line">শিক্ষার্থীর স্বাক্ষর</div>
            </div>
            <div class="signature-box">
                <div class="signature-line">অনুমোদিত কর্মকর্তার স্বাক্ষর</div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>ধন্যবাদ!</strong></p>
            <p>এই রিসিপ্টটি সংরক্ষণ করুন</p>
            <p>প্রিন্ট তারিখ: <?= date('d/m/Y H:i:s') ?></p>
        </div>
    </div>

    <script>
        // Auto print when page loads (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
