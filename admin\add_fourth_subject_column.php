<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$success_message = '';
$error_message = '';

// Check if the column already exists
$checkColumnQuery = "SHOW COLUMNS FROM subjects LIKE 'is_fourth_subject'";
$columnExists = $conn->query($checkColumnQuery)->num_rows > 0;

if ($columnExists) {
    $success_message = "কলাম 'is_fourth_subject' ইতিমধ্যে বিদ্যমান আছে।";
} else {
    // Add the column if it doesn't exist
    $addColumnQuery = "ALTER TABLE subjects ADD COLUMN is_fourth_subject TINYINT(1) DEFAULT 0";

    if ($conn->query($addColumnQuery) === TRUE) {
        $success_message = "কলাম 'is_fourth_subject' সফলভাবে যোগ করা হয়েছে।";
    } else {
        $error_message = "কলা<PERSON> যোগ করতে সমস্যা হয়েছে: " . $conn->error;
    }
}

// Handle form submission to mark subjects as 4th subject
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_subjects'])) {
    // Reset all subjects to not be 4th subjects
    $resetQuery = "UPDATE subjects SET is_fourth_subject = 0";
    $conn->query($resetQuery);

    // Update selected subjects
    if (isset($_POST['fourth_subjects']) && is_array($_POST['fourth_subjects'])) {
        foreach ($_POST['fourth_subjects'] as $subjectId) {
            $subjectId = intval($subjectId);
            $updateQuery = "UPDATE subjects SET is_fourth_subject = 1 WHERE id = ?";
            $stmt = $conn->prepare($updateQuery);
            $stmt->bind_param("i", $subjectId);
            $stmt->execute();
        }
        $success_message = "৪র্থ বিষয় সফলভাবে আপডেট করা হয়েছে।";
    }
}

// Check if department_id column exists in subjects table
$checkDeptColumnQuery = "SHOW COLUMNS FROM subjects LIKE 'department_id'";
$deptColumnExists = $conn->query($checkDeptColumnQuery)->num_rows > 0;

// Get all subjects
if ($deptColumnExists) {
    $subjectsQuery = "SELECT s.*, d.department_name
                     FROM subjects s
                     LEFT JOIN departments d ON s.department_id = d.id
                     ORDER BY s.subject_name";
} else {
    $subjectsQuery = "SELECT s.*
                     FROM subjects s
                     ORDER BY s.subject_name";
}
$subjects = $conn->query($subjectsQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>৪র্থ বিষয় সেটিং - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <?php include 'includes/global-head.php'; ?>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-book me-2 text-primary"></i> ৪র্থ বিষয় সেটিং
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="subjects.php" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left me-1"></i> বিষয় তালিকা
                        </a>
                        <a href="exam_dashboard.php" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-th-large me-1"></i> পরীক্ষা ড্যাশবোর্ড
                        </a>
                    </div>
                </div>

                <!-- Exam Navigation Buttons -->
                <?php include 'exam_buttons.php'; ?>

                <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-1"></i> <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-1"></i> <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i> ৪র্থ বিষয় সম্পর্কে
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>৪র্থ বিষয় হল একটি বিশেষ বিষয় যা GPA ক্যালকুলেশনে বিশেষ নিয়মে গণনা করা হয়:</p>
                        <ul>
                            <li>৪র্থ বিষয়ে যদি GPA 2.00 এর বেশি হয়, তাহলে অতিরিক্ত পয়েন্ট (৪র্থ বিষয়ের GPA - 2.00) মূল GPA-এর সাথে যোগ করা হয়।</li>
                            <li>৪র্থ বিষয়ে যদি GPA 2.00 এর কম হয়, তাহলে কোন অতিরিক্ত পয়েন্ট যোগ হয় না।</li>
                        </ul>
                        <p>নিচের ফর্মে আপনি কোন বিষয়গুলি ৪র্থ বিষয় হিসেবে গণ্য হবে তা নির্ধারণ করতে পারেন।</p>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-edit me-2"></i> ৪র্থ বিষয় নির্ধারণ করুন
                        </h5>
                    </div>
                    <div class="card-body">
                        <form action="" method="POST">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>৪র্থ বিষয়?</th>
                                            <th>বিষয়ের নাম</th>
                                            <th>বিষয় কোড</th>
                                            <?php if ($deptColumnExists): ?>
                                            <th>বিভাগ</th>
                                            <?php endif; ?>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if ($subjects && $subjects->num_rows > 0): ?>
                                            <?php while ($subject = $subjects->fetch_assoc()): ?>
                                                <tr>
                                                    <td>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="fourth_subjects[]" value="<?php echo $subject['id']; ?>" id="subject_<?php echo $subject['id']; ?>" <?php echo (isset($subject['is_fourth_subject']) && $subject['is_fourth_subject'] == 1) ? 'checked' : ''; ?>>
                                                            <label class="form-check-label" for="subject_<?php echo $subject['id']; ?>">
                                                                ৪র্থ বিষয়
                                                            </label>
                                                        </div>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($subject['subject_name']); ?></td>
                                                    <td><?php echo htmlspecialchars($subject['subject_code'] ?? 'N/A'); ?></td>
                                                    <?php if ($deptColumnExists): ?>
                                                    <td><?php echo htmlspecialchars($subject['department_name'] ?? 'N/A'); ?></td>
                                                    <?php endif; ?>
                                                </tr>
                                            <?php endwhile; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="<?php echo $deptColumnExists ? '4' : '3'; ?>" class="text-center">কোন বিষয় পাওয়া যায়নি।</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>

                            <div class="mt-3">
                                <button type="submit" name="update_subjects" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i> সংরক্ষণ করুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
