<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Initialize messages
$successMessage = '';
$errorMessage = '';

// Find the Business group ID
$businessGroupQuery = "SELECT id FROM groups WHERE group_name = 'ব্যবসায়' LIMIT 1";
$businessGroupResult = $conn->query($businessGroupQuery);

if (!$businessGroupResult || $businessGroupResult->num_rows == 0) {
    // If not found, try broader search
    $businessGroupQuery = "SELECT id FROM groups WHERE group_name LIKE '%ব্যবসায়%' OR group_name LIKE '%বাবসায়%' OR group_name LIKE '%বানিজ্য%' OR group_name LIKE '%Business%' LIMIT 1";
    $businessGroupResult = $conn->query($businessGroupQuery);
}

if ($businessGroupResult && $businessGroupResult->num_rows > 0) {
    $businessGroupId = $businessGroupResult->fetch_assoc()['id'];

    // Check if this group has any subjects
    $checkSubjectsQuery = "SELECT COUNT(*) as count FROM subject_groups WHERE group_id = ? AND is_applicable = 1";
    $stmt = $conn->prepare($checkSubjectsQuery);
    $stmt->bind_param("i", $businessGroupId);
    $stmt->execute();
    $result = $stmt->get_result();
    $subjectCount = $result->fetch_assoc()['count'];

    if ($subjectCount == 0) {
        $infoMessage = "ব্যবসায় গ্রুপে কোন বিষয় যোগ করা হয়নি। নিচে থেকে বিষয় যোগ করুন।";
    }
} else {
    // If still not found, create the group
    $insertGroupQuery = "INSERT INTO groups (group_name) VALUES ('ব্যবসায়')";
    if ($conn->query($insertGroupQuery)) {
        $businessGroupId = $conn->insert_id;
        $successMessage = "ব্যবসায় গ্রুপ সফলভাবে তৈরি করা হয়েছে।";
    } else {
        $errorMessage = "ব্যবসায় গ্রুপ তৈরি করতে সমস্যা হয়েছে।";
        $businessGroupId = null;
    }

    // Also check if there's a department with this name
    $checkDeptQuery = "SELECT id FROM departments WHERE department_name = 'ব্যবসায়' LIMIT 1";
    $deptResult = $conn->query($checkDeptQuery);

    if (!$deptResult || $deptResult->num_rows == 0) {
        // Create the department if it doesn't exist
        $insertDeptQuery = "INSERT INTO departments (department_name) VALUES ('ব্যবসায়')";
        if ($conn->query($insertDeptQuery)) {
            $successMessage .= " ব্যবসায় বিভাগও সফলভাবে তৈরি করা হয়েছে।";
        }
    }
}

// Handle form submission
if (isset($_POST['configure_subjects']) && $businessGroupId) {
    // Get all selected subjects
    $selectedSubjects = isset($_POST['subjects']) ? $_POST['subjects'] : [];
    $subjectTypes = isset($_POST['subject_type']) ? $_POST['subject_type'] : [];

    // First, reset all subjects for this group
    $resetQuery = "UPDATE subject_groups SET is_applicable = 0 WHERE group_id = ?";
    $stmt = $conn->prepare($resetQuery);
    $stmt->bind_param("i", $businessGroupId);
    $stmt->execute();

    // Now set the selected subjects
    foreach ($selectedSubjects as $subjectId) {
        $type = isset($subjectTypes[$subjectId]) ? $subjectTypes[$subjectId] : 'optional';

        // Check if record exists
        $checkQuery = "SELECT COUNT(*) as count FROM subject_groups
                      WHERE subject_id = ? AND group_id = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param("ii", $subjectId, $businessGroupId);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();

        if ($row['count'] > 0) {
            // Update existing record
            $updateQuery = "UPDATE subject_groups
                           SET subject_type = ?, is_applicable = 1
                           WHERE subject_id = ? AND group_id = ?";
            $stmt = $conn->prepare($updateQuery);
            $stmt->bind_param("sii", $type, $subjectId, $businessGroupId);
            $stmt->execute();
        } else {
            // Insert new record
            $insertQuery = "INSERT INTO subject_groups
                          (subject_id, group_id, subject_type, is_applicable)
                          VALUES (?, ?, ?, 1)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param("iis", $subjectId, $businessGroupId, $type);
            $stmt->execute();
        }
    }

    $successMessage = "ব্যবসায় বিভাগের বিষয়সমূহ সফলভাবে কনফিগার করা হয়েছে।";
}

// Get all subjects
$subjectsQuery = "SELECT * FROM subjects WHERE is_active = 1 ORDER BY subject_name";
$subjects = $conn->query($subjectsQuery);

// Get currently configured subjects for business group
$configuredSubjectsQuery = "SELECT sg.subject_id, sg.subject_type, s.subject_name, s.subject_code
                           FROM subject_groups sg
                           JOIN subjects s ON sg.subject_id = s.id
                           WHERE sg.group_id = ? AND sg.is_applicable = 1";
$stmt = $conn->prepare($configuredSubjectsQuery);
$stmt->bind_param("i", $businessGroupId);
$stmt->execute();
$configuredSubjects = $stmt->get_result();

$configuredSubjectIds = [];
$configuredSubjectTypes = [];

if ($configuredSubjects && $configuredSubjects->num_rows > 0) {
    while ($subject = $configuredSubjects->fetch_assoc()) {
        $configuredSubjectIds[] = $subject['subject_id'];
        $configuredSubjectTypes[$subject['subject_id']] = $subject['subject_type'];
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>ব্যবসায় বিভাগের বিষয় কনফিগারেশন - কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --accent-color: #4895ef;
            --success-color: #4cc9f0;
            --warning-color: #f72585;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --border-radius: 12px;
            --box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Hind Siliguri', 'Noto Sans Bengali', sans-serif;
            background-color: #f0f2f5;
            color: var(--dark-color);
            line-height: 1.6;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Noto Sans Bengali', 'Hind Siliguri', sans-serif;
            font-weight: 600;
        }

        .sidebar {
            background: linear-gradient(135deg, #3a0ca3 0%, #4361ee 100%);
            color: white;
            min-height: 100vh;
            padding-top: 20px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.85);
            padding: 12px 20px;
            margin: 5px 10px;
            border-radius: 10px;
            transition: var(--transition);
            font-weight: 500;
        }

        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateX(5px);
        }

        .sidebar .nav-link.active {
            color: white;
            background: linear-gradient(90deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .sidebar .nav-link i {
            margin-right: 12px;
            font-size: 1.1rem;
            width: 24px;
            text-align: center;
        }

        .main-content {
            padding: 30px;
            background-color: #f0f2f5;
            min-height: 100vh;
        }

        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 30px;
            overflow: hidden;
            transition: var(--transition);
        }

        .card:hover {
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
            transform: translateY(-5px);
        }

        .card-header {
            border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
            font-weight: 600;
            padding: 1.2rem 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .card-body {
            padding: 1.5rem;
        }

        .btn {
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 500;
            transition: var(--transition);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
        }

        .btn-primary {
            background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
            border: none;
        }

        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            border: none;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f72585 0%, #b5179e 100%);
            border: none;
            color: white;
        }

        .subject-card {
            margin-bottom: 20px;
            transition: all 0.3s ease;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            overflow: hidden;
            height: 100%;
            border: 2px solid transparent;
        }

        .subject-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .subject-card.selected {
            border-color: #2ecc71;
            background-color: rgba(46, 204, 113, 0.05);
        }

        .subject-card .card-body {
            padding: 1.5rem;
        }

        .subject-card .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .subject-card .card-text {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .form-check-input {
            width: 1.2em;
            height: 1.2em;
            margin-top: 0.25em;
            cursor: pointer;
        }

        .form-check-label {
            padding-left: 0.5rem;
            cursor: pointer;
        }

        .form-select {
            padding: 12px;
            border-radius: 10px;
            border: 1px solid rgba(0,0,0,0.1);
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: var(--transition);
        }

        .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
        }

        .alert {
            border-radius: 12px;
            padding: 1rem 1.5rem;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
            border: none;
        }

        .alert-success {
            background: linear-gradient(135deg, #57cc99 0%, #38b000 100%);
            color: white;
        }

        .alert-danger {
            background: linear-gradient(135deg, #f72585 0%, #b5179e 100%);
            color: white;
        }

        .alert-info {
            background: linear-gradient(135deg, #4cc9f0 0%, #4895ef 100%);
            color: white;
        }

        .required-type {
            background-color: #cfe2ff;
            color: #0a58ca;
        }

        .optional-type {
            background-color: #d1e7dd;
            color: #0f5132;
        }

        .fourth-type {
            background-color: #fff3cd;
            color: #664d03;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4 mt-2">
                    <h3 class="text-white">অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="subjects.php">
                            <i class="fas fa-book-open"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sessions.php">
                            <i class="fas fa-calendar-alt"></i> সেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2 class="mb-2">ব্যবসায় বিভাগের বিষয় কনফিগারেশন</h2>
                        <p class="text-muted">ব্যবসায় বিভাগের জন্য বিষয়সমূহ নির্ধারণ করুন</p>
                    </div>
                    <div class="col-auto d-flex align-items-center">
                        <a href="subject_groups.php" class="btn btn-primary me-2">
                            <i class="fas fa-arrow-left me-2"></i>সকল গ্রুপের বিষয় সেটিং
                        </a>
                        <a href="student_subject_selection.php?id=STD-833027" class="btn btn-info">
                            <i class="fas fa-user-graduate me-2"></i>শিক্ষার্থী বিষয় নির্বাচন
                        </a>
                    </div>
                </div>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i><?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($infoMessage)): ?>
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <i class="fas fa-info-circle me-2"></i><?php echo $infoMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="alert alert-info mb-4">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-info-circle fa-2x"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">ব্যবসায় বিভাগের বিষয় কনফিগারেশন</h5>
                            <p class="mb-0">এই পেজে আপনি ব্যবসায় বিভাগের জন্য বিষয়সমূহ নির্ধারণ করতে পারবেন। প্রতিটি বিষয়ের পাশে চেকবক্স সিলেক্ট করে বিষয়টি যোগ করুন এবং ড্রপডাউন থেকে বিষয়ের ধরন (আবশ্যিক/ঐচ্ছিক/৪র্থ) নির্বাচন করুন।</p>
                        </div>
                    </div>
                </div>

                <!-- Configure Subjects Form -->
                <div class="card">
                    <div class="card-header bg-gradient">
                        <div class="d-flex align-items-center">
                            <div class="me-3 bg-white rounded-circle p-2">
                                <i class="fas fa-cog fa-lg text-primary"></i>
                            </div>
                            <h5 class="card-title mb-0">ব্যবসায় বিভাগের বিষয় নির্ধারণ করুন</h5>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if ($subjects && $subjects->num_rows > 0): ?>
                            <form method="POST" action="configure_business_subjects.php">
                                <div class="row">
                                    <?php while ($subject = $subjects->fetch_assoc()): ?>
                                        <div class="col-md-4 col-lg-3 mb-4">
                                            <div class="card subject-card h-100 <?php echo in_array($subject['id'], $configuredSubjectIds) ? 'selected' : ''; ?>">
                                                <div class="card-body">
                                                    <div class="form-check">
                                                        <input class="form-check-input subject-checkbox" type="checkbox"
                                                               name="subjects[]" value="<?php echo $subject['id']; ?>"
                                                               id="subject<?php echo $subject['id']; ?>"
                                                               <?php echo in_array($subject['id'], $configuredSubjectIds) ? 'checked' : ''; ?>>
                                                        <label class="form-check-label" for="subject<?php echo $subject['id']; ?>">
                                                            <h5 class="card-title"><?php echo $subject['subject_name']; ?></h5>
                                                            <p class="card-text"><?php echo $subject['subject_code']; ?></p>
                                                        </label>
                                                    </div>

                                                    <div class="mt-3">
                                                        <select name="subject_type[<?php echo $subject['id']; ?>]"
                                                                class="form-select form-select-sm subject-type-selector
                                                                <?php
                                                                    if (isset($configuredSubjectTypes[$subject['id']])) {
                                                                        echo $configuredSubjectTypes[$subject['id']] . '-type';
                                                                    } else {
                                                                        echo 'optional-type';
                                                                    }
                                                                ?>">
                                                            <option value="required" <?php echo (isset($configuredSubjectTypes[$subject['id']]) && $configuredSubjectTypes[$subject['id']] == 'required') ? 'selected' : ''; ?>>
                                                                আবশ্যিক
                                                            </option>
                                                            <option value="optional" <?php echo (!isset($configuredSubjectTypes[$subject['id']]) || $configuredSubjectTypes[$subject['id']] == 'optional') ? 'selected' : ''; ?>>
                                                                ঐচ্ছিক
                                                            </option>
                                                            <option value="fourth" <?php echo (isset($configuredSubjectTypes[$subject['id']]) && $configuredSubjectTypes[$subject['id']] == 'fourth') ? 'selected' : ''; ?>>
                                                                ৪র্থ
                                                            </option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endwhile; ?>
                                </div>

                                <div class="text-center mt-4">
                                    <button type="submit" name="configure_subjects" class="btn btn-success btn-lg px-5">
                                        <i class="fas fa-save me-2"></i>সংরক্ষণ করুন
                                    </button>
                                </div>
                            </form>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>কোন বিষয় পাওয়া যায়নি। প্রথমে বিষয় যোগ করুন।
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add animation to cards
            document.querySelectorAll('.card').forEach((card, index) => {
                card.classList.add('animate__animated', 'animate__fadeInUp');
                card.style.animationDelay = `${index * 0.05}s`;
            });

            // Handle checkbox changes
            const subjectCheckboxes = document.querySelectorAll('.subject-checkbox');
            subjectCheckboxes.forEach(function(checkbox) {
                checkbox.addEventListener('change', function() {
                    const card = this.closest('.subject-card');
                    if (this.checked) {
                        card.classList.add('selected');
                    } else {
                        card.classList.remove('selected');
                    }
                });
            });

            // Change select background color based on selection
            const typeSelectors = document.querySelectorAll('.subject-type-selector');
            typeSelectors.forEach(function(selector) {
                selector.addEventListener('change', function() {
                    // Remove all type classes
                    this.classList.remove('required-type', 'optional-type', 'fourth-type');
                    // Add class based on selected value
                    this.classList.add(this.value + '-type');
                });
            });

            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert-dismissible');
                alerts.forEach(function(alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);
        });
    </script>
</body>
</html>
