$(document).ready(function() {
    // Preview certificate as user fills the form
    $('#title, #description, #certificate_date, #issued_by, #student_id').on('input change', function() {
        updatePreview();
    });

    function updatePreview() {
        var title = $('#title').val() || 'প্রশংসা সার্টিফিকেট';
        var description = $('#description').val() || 'সার্টিফিকেটের বিবরণ এখানে প্রদর্শিত হবে।';
        var date = $('#certificate_date').val() ? new Date($('#certificate_date').val()).toLocaleDateString('bn-BD') : '';
        var issuedBy = $('#issued_by').val() || 'ইস্যুকারীর নাম';
        
        // Get selected student name
        var studentName = 'শিক্ষার্থীর নাম';
        if ($('#student_id').val()) {
            var selectedOption = $('#student_id option:selected');
            var studentInfo = selectedOption.text();
            var nameParts = studentInfo.split(' - ');
            if (nameParts.length > 1) {
                studentName = nameParts[1];
            }
        }
        
        $('#previewTitle').text(title);
        $('#previewStudentName').text(studentName);
        $('#previewDescription').text(description);
        $('#previewDate').text(date);
        $('#previewIssuedBy').text(issuedBy);
    }
    
    // Handle view certificate button
    $('.view-certificate').click(function() {
        var id = $(this).data('id');
        var title = $(this).data('title');
        var description = $(this).data('description');
        var date = $(this).data('date');
        var issuedBy = $(this).data('issued-by');
        var studentName = $(this).data('student-name');
        var certificateType = $(this).data('certificate-type');
        
        // Update modal content
        $('#modalTitle').text(title);
        $('#modalStudentName').text(studentName);
        $('#modalDescription').text(description);
        $('#modalDate').text(date);
        $('#modalIssuedBy').text(issuedBy);
        
        // Set the print button functionality
        $('#modalPrintBtn').data('id', id);
        
        // Show the modal
        $('#viewCertificateModal').modal('show');
    });
    
    // Handle print certificate button
    $('.print-certificate, #modalPrintBtn').click(function() {
        var id = $(this).data('id');
        
        // Get the certificate details
        var title = $('#modalTitle').text() || 'প্রশংসা সার্টিফিকেট';
        var studentName = $('#modalStudentName').text() || 'শিক্ষার্থীর নাম';
        var description = $('#modalDescription').text() || '';
        var date = $('#modalDate').text() || '';
        var issuedBy = $('#modalIssuedBy').text() || '';
        
        // Create the print content
        var printContent = '<!DOCTYPE html>' +
            '<html>' +
            '<head>' +
            '<title>Print Certificate</title>' +
            '<style>' +
            'body { font-family: Arial, sans-serif; margin: 0; padding: 0; }' +
            '.certificate-container { width: 800px; margin: 0 auto; padding: 40px; position: relative; border: 5px solid #007bff; }' +
            '.certificate-watermark { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%) rotate(-30deg); opacity: 0.07; font-size: 10rem; z-index: 0; color: #000; white-space: nowrap; }' +
            '.certificate-header { text-align: center; margin-bottom: 20px; border-bottom: 2px solid #007bff; padding-bottom: 10px; }' +
            '.certificate-title { color: #007bff; font-size: 36px; margin-bottom: 10px; }' +
            '.certificate-student { font-size: 24px; margin-bottom: 30px; text-align: center; }' +
            '.certificate-content { text-align: center; font-size: 18px; line-height: 1.6; margin: 30px 0; }' +
            '.certificate-footer { display: flex; justify-content: space-between; margin-top: 60px; border-top: 1px solid #ddd; padding-top: 20px; }' +
            '.certificate-date, .certificate-signature { font-size: 18px; }' +
            '.certificate-logo { text-align: center; margin-bottom: 20px; font-size: 24px; font-weight: bold; }' +
            '@media print { @page { size: A4 landscape; margin: 0; } body { margin: 1cm; } }' +
            '</style>' +
            '</head>' +
            '<body>' +
            '<div class="certificate-container">' +
            '<div class="certificate-watermark">ZFAW</div>' +
            '<div class="certificate-logo">ZFAW College Management System</div>' +
            '<div class="certificate-header">' +
            '<h1 class="certificate-title">' + title + '</h1>' +
            '</div>' +
            '<div class="certificate-student">' +
            '<h2>' + studentName + '</h2>' +
            '</div>' +
            '<div class="certificate-content">' + description + '</div>' +
            '<div class="certificate-footer">' +
            '<div class="certificate-date">' + date + '</div>' +
            '<div class="certificate-signature">' + issuedBy + '</div>' +
            '</div>' +
            '</div>' +
            '<script>' +
            'window.onload = function() { setTimeout(function() { window.print(); }, 500); };' +
            '</script>' +
            '</body>' +
            '</html>';
        
        var frame = document.getElementById('printFrame');
        frame.contentDocument.open();
        frame.contentDocument.write(printContent);
        frame.contentDocument.close();
    });
    
    // Handle delete certificate button
    $('.delete-certificate').click(function() {
        var id = $(this).data('id');
        var studentName = $(this).data('student-name');
        var title = $(this).data('title');
        
        $('#deleteCertificateId').val(id);
        $('#deleteStudentName').text(studentName);
        $('#deleteCertificateTitle').text(title);
        
        $('#deleteCertificateModal').modal('show');
    });
    
    // Filter certificates by student name or ID
    $('#searchStudent').on('input', function() {
        filterCertificates();
    });
    
    // Filter certificates by type
    $('#filterCertificateType').on('change', function() {
        filterCertificates();
    });
    
    // Filter certificates by date
    $('#filterDate').on('change', function() {
        filterCertificates();
    });
    
    // Filter function for certificates table
    function filterCertificates() {
        var searchTerm = $('#searchStudent').val().toLowerCase();
        var certificateType = $('#filterCertificateType').val();
        var dateFilter = $('#filterDate').val();
        
        $('#certificatesTable tbody tr').each(function() {
            var studentId = $(this).find('td:eq(0)').text().toLowerCase();
            var studentName = $(this).find('td:eq(1)').text().toLowerCase();
            var certType = $(this).find('td:eq(3)').text();
            var certDate = $(this).find('td:eq(4)').text();
            
            // Parse date for filtering
            var show = true;
            
            // Apply student filter
            if (searchTerm && !(studentId.includes(searchTerm) || studentName.includes(searchTerm))) {
                show = false;
            }
            
            // Apply certificate type filter
            if (certificateType && !certType.includes(certificateType)) {
                show = false;
            }
            
            // Apply date filter
            if (dateFilter && certDate) {
                var dateParts = certDate.split('/');
                if (dateParts.length === 3) {
                    var certYearMonth = dateParts[2] + '-' + (dateParts[1].length === 1 ? '0' + dateParts[1] : dateParts[1]);
                    if (certYearMonth !== dateFilter) {
                        show = false;
                    }
                }
            }
            
            $(this).toggle(show);
        });
    }
});
