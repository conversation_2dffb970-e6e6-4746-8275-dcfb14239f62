<?php
$file = file_get_contents('detailed_marks_entry.php');
preg_match_all('/function\s+getStudentsByClass/i', $file, $matches, PREG_OFFSET_CAPTURE);

foreach ($matches[0] as $index => $match) {
    $position = $match[1];
    
    // Count lines to get line number
    $lineNumber = substr_count(substr($file, 0, $position), "\n") + 1;
    
    echo "Found 'function getStudentsByClass' at position: $position (line $lineNumber)\n";
}
?> 