<?php
// Include database connection
require_once "includes/dbh.inc.php";

// Check if subjects table has data
$subjectsQuery = "SELECT COUNT(*) as count FROM subjects";
$result = $conn->query($subjectsQuery);
$subjectCount = $result->fetch_assoc()['count'];

if ($subjectCount < 5) {
    // Add some demo subjects
    $demoSubjects = [
        ['বাংলা', 'BAN101', 'required'],
        ['ইংরেজি', 'ENG101', 'required'],
        ['গণিত', 'MATH101', 'required'],
        ['বিজ্ঞান', 'SCI101', 'required'],
        ['ইতিহাস', 'HIST101', 'optional'],
        ['ভূগোল', 'GEO101', 'optional'],
        ['অর্থনীতি', 'ECON101', 'optional'],
        ['রসায়ন', 'CHEM101', 'optional'],
        ['পদার্থবিজ্ঞান', 'PHY101', 'fourth'],
        ['জীববিজ্ঞান', 'BIO101', 'fourth'],
        ['কম্পিউটার', 'CSE101', 'fourth'],
        ['ধর্ম', 'REL101', 'optional']
    ];
    
    $insertSubjectQuery = "INSERT INTO subjects (subject_name, subject_code, category, is_active) VALUES (?, ?, ?, 1)";
    $stmt = $conn->prepare($insertSubjectQuery);
    
    foreach ($demoSubjects as $subject) {
        $stmt->bind_param("sss", $subject[0], $subject[1], $subject[2]);
        $stmt->execute();
    }
    
    echo "<p>Added demo subjects.</p>";
}

// Get all departments
$departmentsQuery = "SELECT id FROM departments";
$departments = $conn->query($departmentsQuery);

if ($departments && $departments->num_rows > 0) {
    // Get all subjects
    $subjectsQuery = "SELECT id, category FROM subjects";
    $subjects = $conn->query($subjectsQuery);
    
    if ($subjects && $subjects->num_rows > 0) {
        $subjectsList = [];
        while ($subject = $subjects->fetch_assoc()) {
            $subjectsList[] = $subject;
        }
        
        // For each department, assign subjects
        while ($department = $departments->fetch_assoc()) {
            $departmentId = $department['id'];
            
            // First, check if department already has subjects
            $checkQuery = "SELECT COUNT(*) as count FROM subject_departments WHERE department_id = ?";
            $stmt = $conn->prepare($checkQuery);
            $stmt->bind_param("i", $departmentId);
            $stmt->execute();
            $result = $stmt->get_result();
            $count = $result->fetch_assoc()['count'];
            
            if ($count == 0) {
                // Assign all subjects to this department
                $insertQuery = "INSERT INTO subject_departments (department_id, subject_id) VALUES (?, ?)";
                $stmt = $conn->prepare($insertQuery);
                
                foreach ($subjectsList as $subject) {
                    $subjectId = $subject['id'];
                    $stmt->bind_param("ii", $departmentId, $subjectId);
                    $stmt->execute();
                }
                
                echo "<p>Assigned subjects to department ID: $departmentId</p>";
            }
            
            // Now check if department has subject types
            $checkTypesQuery = "SELECT COUNT(*) as count FROM department_subject_types WHERE department_id = ?";
            $stmt = $conn->prepare($checkTypesQuery);
            $stmt->bind_param("i", $departmentId);
            $stmt->execute();
            $result = $stmt->get_result();
            $count = $result->fetch_assoc()['count'];
            
            if ($count == 0) {
                // Assign subject types based on subject category
                $insertTypeQuery = "INSERT INTO department_subject_types (department_id, subject_id, subject_type) VALUES (?, ?, ?)";
                $stmt = $conn->prepare($insertTypeQuery);
                
                foreach ($subjectsList as $subject) {
                    $subjectId = $subject['id'];
                    $subjectType = $subject['category'];
                    $stmt->bind_param("iis", $departmentId, $subjectId, $subjectType);
                    $stmt->execute();
                }
                
                echo "<p>Assigned subject types to department ID: $departmentId</p>";
            }
        }
    }
}

echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
echo "<h3>Success!</h3>";
echo "<p>Demo subjects and mappings have been added.</p>";
echo "<p>You can now go to <a href='admin/student_subject_selection.php'>Student Subject Selection</a> page.</p>";
echo "</div>";

// Close connection
$conn->close();
?>
