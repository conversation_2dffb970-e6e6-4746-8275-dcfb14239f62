<?php
/**
 * Buffer Fix Configuration
 * This file helps prevent title bar buffering issues
 */

// Disable output buffering completely
ini_set('output_buffering', 'Off');
ini_set('implicit_flush', 'On');

// Turn off output compression
ini_set('zlib.output_compression', 'Off');

// Disable any automatic output handlers
if (function_exists('apache_setenv')) {
    apache_setenv('no-gzip', '1');
}

// Clear any existing output buffers
while (ob_get_level()) {
    ob_end_clean();
}

// Set proper headers to prevent caching and buffering
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');
header('Expires: 0');

// Flush output immediately
if (function_exists('fastcgi_finish_request')) {
    fastcgi_finish_request();
} else {
    flush();
}
?>
