<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Function to calculate GPA based on grade
function calculateGPA($grade) {
    switch ($grade) {
        case 'A+': return 5.00;
        case 'A': return 4.00;
        case 'A-': return 3.50;
        case 'B': return 3.00;
        case 'C': return 2.00;
        case 'D': return 1.00;
        case 'F':
        default: return 0.00;
    }
}

// Initialize variables
$examId = isset($_GET['exam_id']) ? intval($_GET['exam_id']) : 0;
$classId = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;
$departmentId = isset($_GET['department_id']) ? intval($_GET['department_id']) : 0;
$sessionId = isset($_GET['session_id']) ? intval($_GET['session_id']) : 0;
$perPage = isset($_GET['per_page']) ? intval($_GET['per_page']) : 20; // Default 20 students per page
$page = isset($_GET['page']) ? intval($_GET['page']) : 1; // Default to first page
$success_message = '';
$error_message = '';

// Get all exams for dropdown
$examsQuery = "SELECT e.*, c.class_name, d.department_name, s.session_name
               FROM exams e
               LEFT JOIN classes c ON e.class_id = c.id
               LEFT JOIN departments d ON e.department_id = d.id
               LEFT JOIN sessions s ON e.session_id = s.id
               ORDER BY e.exam_date DESC, e.exam_name";
$exams = $conn->query($examsQuery);

// Get all classes for dropdown
$classesQuery = "SELECT * FROM classes ORDER BY class_name";
$classes = $conn->query($classesQuery);

// Get all departments for dropdown
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Get all sessions for dropdown
$sessionsQuery = "SELECT * FROM sessions ORDER BY session_name";
$sessions = $conn->query($sessionsQuery);

// Get exam details if exam ID is provided
$examInfo = null;
if ($examId > 0) {
    $examQuery = "SELECT e.*, c.class_name, d.department_name, s.session_name
                 FROM exams e
                 LEFT JOIN classes c ON e.class_id = c.id
                 LEFT JOIN departments d ON e.department_id = d.id
                 LEFT JOIN sessions s ON e.session_id = s.id
                 WHERE e.id = ?";
    $stmt = $conn->prepare($examQuery);
    $stmt->bind_param("i", $examId);
    $stmt->execute();
    $examInfo = $stmt->get_result()->fetch_assoc();
}

// Get institution information
$institutionInfo = [];

// Check if settings table exists
$checkTableQuery = "SHOW TABLES LIKE 'settings'";
$tableExists = $conn->query($checkTableQuery)->num_rows > 0;

if ($tableExists) {
    $institutionQuery = "SELECT * FROM settings WHERE id = 1";
    $institutionResult = $conn->query($institutionQuery);
    if ($institutionResult && $institutionResult->num_rows > 0) {
        $institutionInfo = $institutionResult->fetch_assoc();
    }
}

// Get institution logo
$logoPath = '../uploads/institution_logo.png';
$defaultLogoPath = '../assets/images/default_logo.png';
$defaultLogoSvgPath = '../assets/images/default_logo.svg';

// Check if any of the logo paths exist
if (file_exists($logoPath)) {
    $logoToUse = $logoPath;
} else if (file_exists($defaultLogoPath)) {
    $logoToUse = $defaultLogoPath;
} else if (file_exists($defaultLogoSvgPath)) {
    $logoToUse = $defaultLogoSvgPath;
} else {
    // If no logo found, use a placeholder icon
    $logoToUse = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAFBUlEQVR4nO2dW4hVVRjHf2PjZTLNSxmZl4wKK8nKsB6kKAh6CCKih4IgEAp6CXqIXnoMfOjBBH3oQpRYkBQFRkGakCRF5gVTs7wlOo6XGcVxnGb6sPY5s8+cvc/e55y99tprn/8Pfpzz7e/ba63/d+199t5rfWuDYRiGYRiGYRiGYRiGYRiGYRiGYRiGYRiGYRiGYRiGYRhGI5gBvAP8DvQDe4HngbZGOlUmJgHvA4PAcM5rEPhAy5QGbcDnwBDFQQxf/cAGYGwjHC0DzwJ/UxxCXhwngVWNcLQMrMcvhLw4zgJr6u5pCVgKXCQ/gKIgAIaAh+rsbyl4k/gQsuI4B9xbV29LwFLgAskhhMVxCZhfR39LwVaSQwiL4wowv27eloAVuC/4pBDy4ugB7qiXw2XgU9KFkBXHIHBfnfwtBQ+QPYcUxXEVWFgfdzuHNuBVYA/QS/QXfVYcV4GH6+Jx5zAWeA04TXQIYXFcAx6ri9cdxDjgDdJPTGFxXAcerot3HUYb8BZwjvQhhMXxZD0c7kTG4yaVc2QLISuO5+rgb8cyHlcjOk/2EMLieLUOPnc0E4BtpJ+YwuJ4rw4+dzwTgR2kn5jC4thWB587nknALtKHEBbHjjr4XAomA7tJH0JYHN/WwedSMAXYQ/oQsuLoBSbWwedSMBXYR/oQsuI4DUyog8+lYBrwA+lDyIrjb2B6HXwuBdOBn0kfQlYcfwEz4jrZKvNIWZgJ/Er6ELLi6ANmxXWyVeaRsjAb+I30IWTFcQGYE9fJVplHysJc4HfSh5AVRz8wL66TrTKPlIX5wB+kDyErjkFgQVwnW2UeKQsLgT9JH0JWHEPAA3GdbJV5pCwsAo6SPoSsOIaBxXGdbJV5pCwsAY6RPoSsOK4DS+M62SrzSFlYChwnfQhZcdwAlsV1slXmkbKwDDhB+hCy4hgCVsR1slXmkbKwHDhJ+hCy4rgJrIzrZKvMI2VhJXCK9CFkxXETeDiuk60yj5SFVcBp0oeQFccN4JG4TrbKPFIWVgNnSB9CVhzXgEfjOtkq80hZWAOcJX0IWXFcBR6L62SrzCNl4XHgHOlDyIrjCvBEXCdbZR4pC08CF0gfQlYcl4Gn4jrZKvNIWXgauEj6ELLiuAQ8E9fJVplHysKzwD+kDyErjovAc3GdbJV5pCw8D/STPoSsOC4AL8R1slXmkbLwItBH+hCy4ugFXorrZKvMI2XhZdyGhLQhZMXRC7wS18lWmUfKwqu4/VNpQ8iKoxv3UxHDo5P0IWTFcR63g9kwbsNtQEgbQlYcZ4BNcZ1slXmkLGzGbUJIG0JWHH8Br8V1slXmkbLwOm7XbtoQsuI4BWyJ62SrzCNlYStuv1TaELLiOAlsjetkq8wjZWEb0EP6ELLiOAFsj+tkq8wjZWE7cJT0IWTF0Q18ENfJVplHysKHuP1SaUPIiuMY8FFcJ1tlHikLHwPHSR9CVhxHgU/iOtkq80hZ+BT3UxFpQ8iK4wjwWVwnW2UeKQufA3+TPoSsOA4DX8R1slXmkbLwJXCI9CFkxXEI+Cquk60yj5SFr4E/SB9CVhwHge/iOtkq80hZ+B44QPoQsuI4APwQ18lWmUfKwo+4/VJpQ8iKYz/wU1wnW2UeKQs/4/ZLpQ0hK459wJ64TrbKPFIW9gK/kD6ErDh+BvbFdbJV5pGysB+3XyptCFlx7MXtqTIMwzAMwzAMwzAMwzAMwzAMwzAMwzAMwzAMwzAMwzAMw2gF/gOOVYMBvETGXAAAAABJRU5ErkJggg==';
}

// Create default institution info if not available
if (empty($institutionInfo)) {
    $institutionInfo = [
        'institution_name' => 'School Management System',
        'institution_address' => 'Your School Address',
        'phone' => '',
        'email' => ''
    ];
}

// Get subjects for the selected exam
$subjects = [];
if ($examId > 0) {
    $subjectsQuery = "SELECT DISTINCT s.id, s.subject_name, s.subject_code, s.is_fourth_subject
                     FROM subjects s
                     JOIN results r ON s.id = r.subject_id
                     WHERE r.exam_id = ?
                     ORDER BY s.is_fourth_subject ASC, s.subject_name";
    $stmt = $conn->prepare($subjectsQuery);
    $stmt->bind_param("i", $examId);
    $stmt->execute();
    $subjectsResult = $stmt->get_result();

    while ($subject = $subjectsResult->fetch_assoc()) {
        $subjects[] = $subject;
    }
}

// Get students and their results
$students = [];
$totalStudents = 0;

if ($examId > 0) {
    // Base query for both count and data
    $baseQuery = "FROM students s
                 LEFT JOIN classes c ON s.class_id = c.id
                 LEFT JOIN departments d ON s.department_id = d.id
                 LEFT JOIN sessions ss ON s.session_id = ss.id
                 WHERE EXISTS (SELECT 1 FROM results r WHERE r.student_id = s.id AND r.exam_id = ?)";

    $params = [$examId];
    $types = "i";

    if ($classId > 0) {
        $baseQuery .= " AND s.class_id = ?";
        $params[] = $classId;
        $types .= "i";
    }

    if ($departmentId > 0) {
        $baseQuery .= " AND s.department_id = ?";
        $params[] = $departmentId;
        $types .= "i";
    }

    if ($sessionId > 0) {
        $baseQuery .= " AND s.session_id = ?";
        $params[] = $sessionId;
        $types .= "i";
    }

    // Get total count for pagination
    $countQuery = "SELECT COUNT(*) as total " . $baseQuery;
    $stmt = $conn->prepare($countQuery);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $totalStudents = $row['total'];

    // Calculate pagination
    $totalPages = ceil($totalStudents / $perPage);
    $offset = ($page - 1) * $perPage;

    // Get students with pagination
    $studentsQuery = "SELECT s.*, c.class_name, d.department_name, ss.session_name " . $baseQuery;
    $studentsQuery .= " ORDER BY s.roll_number, s.first_name, s.last_name";
    $studentsQuery .= " LIMIT ?, ?";

    // Add pagination parameters
    $params[] = $offset;
    $params[] = $perPage;
    $types .= "ii";

    $stmt = $conn->prepare($studentsQuery);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $studentsResult = $stmt->get_result();

    // Fetch all students
    while ($student = $studentsResult->fetch_assoc()) {
        $studentId = $student['id'];
        $student['results'] = [];
        $student['total_marks'] = 0;
        $student['total_obtained'] = 0;
        $student['gpa'] = 0;
        $student['grade'] = '';
        $student['has_failed'] = false;

        // Get results for this student
        $resultsQuery = "SELECT r.*, s.subject_name, s.subject_code, s.is_fourth_subject
                        FROM results r
                        JOIN subjects s ON r.subject_id = s.id
                        WHERE r.student_id = ? AND r.exam_id = ?";
        $stmt = $conn->prepare($resultsQuery);
        $stmt->bind_param("ii", $studentId, $examId);
        $stmt->execute();
        $resultsResult = $stmt->get_result();

        $mainSubjects = [];
        $fourthSubject = null;

        while ($result = $resultsResult->fetch_assoc()) {
            $student['results'][$result['subject_id']] = $result;
            $student['total_marks'] += $result['total_marks'];
            $student['total_obtained'] += $result['marks_obtained'];

            $subjectGPA = calculateGPA($result['grade']);

            // Check if this is a 4th subject
            if (isset($result['is_fourth_subject']) && $result['is_fourth_subject'] == 1) {
                $fourthSubject = [
                    'name' => $result['subject_name'],
                    'code' => $result['subject_code'],
                    'gpa' => $subjectGPA
                ];
            } else {
                $mainSubjects[] = [
                    'name' => $result['subject_name'],
                    'code' => $result['subject_code'],
                    'gpa' => $subjectGPA
                ];

                // Check if student failed in any main subject
                if ($subjectGPA == 0) {
                    $student['has_failed'] = true;
                }
            }
        }

        // Calculate GPA
        if ($student['has_failed']) {
            $student['gpa'] = 0;
            $student['grade'] = 'F';
        } else {
            // Calculate base GPA (without 4th subject)
            $mainSubjectCount = count($mainSubjects);
            $mainSubjectsGPA = 0;

            foreach ($mainSubjects as $subject) {
                $mainSubjectsGPA += $subject['gpa'];
            }

            $baseGPA = ($mainSubjectCount > 0) ? $mainSubjectsGPA / $mainSubjectCount : 0;
            $finalGPA = $baseGPA;

            // Add bonus from 4th subject if applicable
            if ($fourthSubject && $fourthSubject['gpa'] > 2.0) {
                $bonusGPA = $fourthSubject['gpa'] - 2.0;
                $finalGPA = $baseGPA + $bonusGPA;
            }

            // Cap GPA at 5.0
            $finalGPA = min($finalGPA, 5.0);
            $student['gpa'] = $finalGPA;

            // Determine grade based on GPA
            if ($finalGPA >= 5.0) $student['grade'] = 'A+';
            elseif ($finalGPA >= 4.0) $student['grade'] = 'A';
            elseif ($finalGPA >= 3.5) $student['grade'] = 'A-';
            elseif ($finalGPA >= 3.0) $student['grade'] = 'B';
            elseif ($finalGPA >= 2.0) $student['grade'] = 'C';
            elseif ($finalGPA >= 1.0) $student['grade'] = 'D';
            else $student['grade'] = 'F';
        }

        $students[] = $student;
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ট্যাবুলেশন শীট - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <?php include 'includes/global-head.php'; ?>
    <link rel="stylesheet" href="../css/hind-siliguri.css">
    <style>
        body {
            font-family: 'Hind Siliguri', 'Noto Sans Bengali', sans-serif;
        }
        .tabulation-sheet {
            margin-bottom: 20px;
        }
        .student-tabulation {
            page-break-inside: avoid;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 0 10px rgba(0,0,0,0.05);
        }
        .student-name-header {
            background-color: #f8f9fa;
            text-align: left;
            font-size: 16px;
            padding: 10px 15px;
        }
        .student-details {
            font-size: 12px;
            font-weight: normal;
            color: #666;
            margin-left: 15px;
        }
        .tabulation-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #aaa;
        }
        .tabulation-table th, .tabulation-table td {
            border: 1px solid #aaa;
            padding: 8px;
            text-align: center;
            font-size: 14px;
        }
        .tabulation-table th {
            background-color: #f2f2f2;
        }
        .tabulation-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .fourth-subject-header {
            background-color: #e8f4ff !important;
        }
        .fourth-subject {
            background-color: #e8f4ff !important;
        }
        .total-row {
            background-color: #f2f2f2 !important;
            font-weight: bold;
        }
        .text-start {
            text-align: left !important;
            font-weight: bold;
        }
        .grade-cell {
            font-weight: bold;
        }
        .grade-A\+ {
            color: #28a745;
        }
        .grade-A {
            color: #28a745;
        }
        .grade-A- {
            color: #28a745;
        }
        .grade-B {
            color: #17a2b8;
        }
        .grade-C {
            color: #ffc107;
        }
        .grade-D {
            color: #fd7e14;
        }
        .grade-F {
            color: #dc3545;
        }
        .print-header {
            display: none;
            width: 100%;
        }
        .institution-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .header-left {
            width: 100%;
            text-align: center;
        }
        .institution-logo {
            max-width: 100px;
            max-height: 100px;
        }
        .logo-container {
            width: 80px;
            margin-right: 20px;
        }
        .institution-info {
            flex: 1;
        }
        .institution-name {
            margin: 0 0 5px 0;
            font-size: 24px;
            font-weight: bold;
        }
        .institution-address, .institution-contact {
            margin: 0 0 5px 0;
            font-size: 14px;
        }
        .exam-info {
            border-top: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
            padding: 10px 0;
            margin-bottom: 15px;
        }
        .exam-title {
            margin: 0 0 5px 0;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
        }
        .exam-details {
            margin: 0;
            font-size: 14px;
            text-align: center;
        }
        @media print {
            .no-print,
            .main-content > *:not(.card:has(.tabulation-sheet)),
            .card-header,
            .card:has(.tabulation-sheet) > .card-body > *:not(.tabulation-sheet):not(.print-header),
            .sidebar,
            header,
            footer,
            nav {
                display: none !important;
            }
            body {
                background-color: #fff !important;
                margin: 0 !important;
                padding: 0 !important;
            }
            .container-fluid {
                width: 100% !important;
                padding: 0 !important;
                margin: 0 !important;
            }
            .main-content {
                width: 100% !important;
                padding: 0 !important;
                margin: 0 !important;
            }
            .card:has(.tabulation-sheet) {
                border: none !important;
                box-shadow: none !important;
                margin: 0 !important;
                padding: 0 !important;
            }
            .card-body {
                padding: 0 !important;
                margin: 0 !important;
                position: relative !important;
            }
            .tabulation-sheet {
                margin: 0 !important;
                padding: 0 !important;
            }
            .print-header {
                display: block !important;
                margin-bottom: 15px;
                width: 100%;
                position: relative;
                z-index: 1000;
            }
            .institution-header {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
            }
            .logo-container {
                width: 60px;
                margin-right: 15px;
            }
            .institution-logo {
                max-width: 100%;
                height: auto;
            }
            .institution-info {
                flex: 1;
            }
            .institution-name {
                margin: 0 0 3px 0;
                font-size: 18px;
                font-weight: bold;
            }
            .institution-address, .institution-contact {
                margin: 0 0 3px 0;
                font-size: 10px;
            }
            .exam-info {
                border-top: 1px solid #ddd;
                border-bottom: 1px solid #ddd;
                padding: 5px 0;
                margin-bottom: 10px;
            }
            .exam-title {
                margin: 0 0 3px 0;
                font-size: 14px;
                font-weight: bold;
                text-align: center;
            }
            .exam-details {
                margin: 0;
                font-size: 10px;
                text-align: center;
            }
            .student-tabulation {
                page-break-inside: avoid;
                break-inside: avoid;
                margin-bottom: 0;
                border: 1px solid #ddd !important;
                box-shadow: none;
            }
            .student-name-header {
                background-color: #f8f9fa !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                font-size: 14px !important;
                padding: 8px !important;
            }
            .student-details {
                font-size: 10px !important;
                margin-left: 10px !important;
            }
            .tabulation-table {
                border: 1px solid #aaa !important;
                font-size: 10px;
                margin-bottom: 0 !important;
                border-collapse: collapse !important;
                width: 100% !important;
            }
            .tabulation-table th, .tabulation-table td {
                border: 1px solid #aaa !important;
                padding: 1px 2px !important;
                font-size: 10px;
                vertical-align: middle !important;
                text-align: center !important;
                white-space: nowrap !important;
            }
            .fourth-subject-header {
                background-color: #e8f4ff !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            .fourth-subject {
                background-color: #e8f4ff !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            .total-row {
                background-color: #f2f2f2 !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            .tabulation-table th {
                background-color: #f2f2f2 !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            .col-md-9, .col-lg-10 {
                width: 100% !important;
                max-width: 100% !important;
                flex: 0 0 100% !important;
            }
            .row {
                margin: 0 !important;
                padding: 0 !important;
            }
            @page {
                size: portrait;
                margin: 5mm;
            }

            .landscape-mode {
                width: 100%;
                transform-origin: top left;
                transform: rotate(90deg) translate(0, -100%);
                position: absolute;
                top: 0;
                left: 0;
            }

            img, div[style*="background-image"] {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            td[style*="background-image"] {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-table me-2 text-primary"></i> ট্যাবুলেশন শীট
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="exam_dashboard.php" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left me-1"></i> পরীক্ষা ড্যাশবোর্ড
                        </a>
                        <a href="http://localhost/zfaw/admin/exams.php" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-list me-1"></i> পরীক্ষা তালিকা
                        </a>
                    </div>
                </div>

                <!-- Exam Navigation Buttons -->
                <?php include 'exam_buttons.php'; ?>

                <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-1"></i> <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-1"></i> <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <!-- Search and Filter Form -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-search me-2"></i> ট্যাবুলেশন শীট খুঁজুন
                        </h5>
                    </div>
                    <div class="card-body">
                        <form action="" method="GET" class="row g-3">
                            <div class="col-md-6">
                                <label for="exam_id" class="form-label">পরীক্ষা*</label>
                                <select class="form-select" id="exam_id" name="exam_id" required>
                                    <option value="">পরীক্ষা নির্বাচন করুন</option>
                                    <?php if ($exams && $exams->num_rows > 0): ?>
                                        <?php while ($exam = $exams->fetch_assoc()): ?>
                                            <option value="<?php echo $exam['id']; ?>" <?php echo ($examId == $exam['id']) ? 'selected' : ''; ?>>
                                                <?php
                                                    echo htmlspecialchars($exam['exam_name']);
                                                    if (!empty($exam['exam_type'])) {
                                                        echo ' (' . htmlspecialchars($exam['exam_type']) . ')';
                                                    }
                                                    if (!empty($exam['class_name'])) {
                                                        echo ' - ' . htmlspecialchars($exam['class_name']);
                                                    }
                                                    if (!empty($exam['session_name'])) {
                                                        echo ' - ' . htmlspecialchars($exam['session_name']);
                                                    }
                                                ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>

                            <div class="col-md-6">
                                <label for="class_id" class="form-label">শ্রেণী</label>
                                <select class="form-select" id="class_id" name="class_id">
                                    <option value="0">সকল শ্রেণী</option>
                                    <?php if ($classes && $classes->num_rows > 0): ?>
                                        <?php while ($class = $classes->fetch_assoc()): ?>
                                            <option value="<?php echo $class['id']; ?>" <?php echo ($classId == $class['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($class['class_name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>

                            <div class="col-md-6">
                                <label for="department_id" class="form-label">বিভাগ</label>
                                <select class="form-select" id="department_id" name="department_id">
                                    <option value="0">সকল বিভাগ</option>
                                    <?php if ($departments && $departments->num_rows > 0): ?>
                                        <?php while ($department = $departments->fetch_assoc()): ?>
                                            <option value="<?php echo $department['id']; ?>" <?php echo ($departmentId == $department['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($department['department_name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>

                            <div class="col-md-6">
                                <label for="session_id" class="form-label">সেশন</label>
                                <select class="form-select" id="session_id" name="session_id">
                                    <option value="0">সকল সেশন</option>
                                    <?php if ($sessions && $sessions->num_rows > 0): ?>
                                        <?php while ($session = $sessions->fetch_assoc()): ?>
                                            <option value="<?php echo $session['id']; ?>" <?php echo ($sessionId == $session['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($session['session_name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>

                            <div class="col-md-6">
                                <label for="per_page" class="form-label">প্রতি পৃষ্ঠায় শিক্ষার্থী সংখ্যা</label>
                                <select class="form-select" id="per_page" name="per_page">
                                    <option value="10" <?php echo ($perPage == 10) ? 'selected' : ''; ?>>১০</option>
                                    <option value="20" <?php echo ($perPage == 20) ? 'selected' : ''; ?>>২০</option>
                                    <option value="30" <?php echo ($perPage == 30) ? 'selected' : ''; ?>>৩০</option>
                                    <option value="50" <?php echo ($perPage == 50) ? 'selected' : ''; ?>>৫০</option>
                                    <option value="100" <?php echo ($perPage == 100) ? 'selected' : ''; ?>>১০০</option>
                                </select>
                            </div>

                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i> খুঁজুন
                                </button>
                                <a href="tabulation_sheet.php" class="btn btn-secondary">
                                    <i class="fas fa-redo me-2"></i> রিসেট
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Tabulation Sheet Display -->
                <?php if ($examId > 0 && !empty($students) && !empty($subjects)): ?>
                <div class="card mb-4">
                    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-table me-2"></i> ট্যাবুলেশন শীট
                            <span class="badge bg-light text-dark ms-2">মোট: <?php echo $totalStudents; ?></span>
                        </h5>
                        <div>
                            <div class="btn-group me-2">
                                <button type="button" class="btn btn-light btn-sm dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-print me-1"></i> প্রিন্ট করুন
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="printPage('portrait'); return false;">পোর্ট্রেট মোডে প্রিন্ট</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="printPage('landscape'); return false;">ল্যান্ডস্কেপ মোডে প্রিন্ট</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="print-header">
                            <table style="width: 100%; border: none; margin-bottom: 15px;">
                                <tr>
                                    <td style="width: 15%; text-align: center; vertical-align: middle;">
                                        <div class="header-left">
                                            <?php if (strpos($logoToUse, 'data:image') === 0): ?>
                                                <img src="<?php echo $logoToUse; ?>" alt="Institution Logo" class="institution-logo">
                                            <?php else: ?>
                                                <img src="<?php echo $logoToUse; ?>" alt="Institution Logo" class="institution-logo">
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td style="width: 85%; text-align: center; vertical-align: middle;">
                                        <h2 style="margin: 0 0 5px 0; font-size: 18px; font-weight: bold;"><?php echo htmlspecialchars($institutionInfo['institution_name'] ?? 'School Management System'); ?></h2>
                                        <p style="margin: 0 0 3px 0; font-size: 12px;"><?php echo htmlspecialchars($institutionInfo['institution_address'] ?? ''); ?></p>
                                        <p style="margin: 0 0 3px 0; font-size: 12px;">
                                            <?php if (!empty($institutionInfo['phone'])): ?>
                                            ফোন: <?php echo htmlspecialchars($institutionInfo['phone']); ?>
                                            <?php endif; ?>
                                            <?php if (!empty($institutionInfo['email'])): ?>
                                            | ইমেইল: <?php echo htmlspecialchars($institutionInfo['email']); ?>
                                            <?php endif; ?>
                                        </p>
                                    </td>
                                </tr>
                            </table>

                            <div style="border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; padding: 5px 0; margin-bottom: 10px; text-align: center;">
                                <h3 style="margin: 0 0 3px 0; font-size: 16px; font-weight: bold;"><?php echo htmlspecialchars($examInfo['exam_name']); ?> - ট্যাবুলেশন শীট</h3>
                                <p style="margin: 0; font-size: 12px;">
                                    <?php if (!empty($examInfo['class_name'])): ?>
                                    শ্রেণী: <?php echo htmlspecialchars($examInfo['class_name']); ?>
                                    <?php endif; ?>
                                    <?php if (!empty($examInfo['department_name'])): ?>
                                    | বিভাগ: <?php echo htmlspecialchars($examInfo['department_name']); ?>
                                    <?php endif; ?>
                                    <?php if (!empty($examInfo['session_name'])): ?>
                                    | সেশন: <?php echo htmlspecialchars($examInfo['session_name']); ?>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>

                        <div class="tabulation-sheet">
                            <?php
                            $i = ($page - 1) * $perPage + 1;
                            foreach ($students as $student):
                            ?>
                            <div class="student-tabulation mb-1">
                                <table class="tabulation-table">
                                    <thead>
                                        <tr>
                                            <th colspan="<?php echo count($student['results']) * 2 + 6; ?>" class="student-name-header">
                                                <?php echo $i++; ?>. <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>
                                                <span class="student-details">
                                                    রোল: <?php echo htmlspecialchars($student['roll_number']); ?> |
                                                    আইডি: <?php echo htmlspecialchars($student['student_id']); ?>
                                                    <?php if (!empty($student['class_name'])): ?>
                                                     | শ্রেণী: <?php echo htmlspecialchars($student['class_name']); ?>
                                                    <?php endif; ?>
                                                    <?php if (!empty($student['department_name'])): ?>
                                                     | বিভাগ: <?php echo htmlspecialchars($student['department_name']); ?>
                                                    <?php endif; ?>
                                                </span>
                                            </th>
                                        </tr>
                                        <tr>
                                            <th rowspan="2" style="min-width: 40px;">বিবরণ</th>
                                            <?php
                                            // Filter subjects to only show those the student has results for
                                            $mainSubjects = [];
                                            $fourthSubject = null;

                                            foreach ($subjects as $subject) {
                                                if (isset($student['results'][$subject['id']])) {
                                                    $isFourthSubject = isset($subject['is_fourth_subject']) && $subject['is_fourth_subject'] == 1;

                                                    if ($isFourthSubject) {
                                                        $fourthSubject = $subject;
                                                    } else {
                                                        $mainSubjects[] = $subject;
                                                    }
                                                }
                                            }

                                            // Display main subjects first
                                            foreach ($mainSubjects as $subject):
                                            ?>
                                            <th colspan="4" style="min-width: 80px;">
                                                <?php echo htmlspecialchars($subject['subject_name']); ?>
                                                <br>
                                                <small><?php echo htmlspecialchars($subject['subject_code']); ?></small>
                                            </th>
                                            <?php endforeach; ?>

                                            <!-- Display 4th subject at the end if exists -->
                                            <?php if ($fourthSubject): ?>
                                            <th colspan="4" class="fourth-subject-header" style="min-width: 80px;">
                                                <?php echo htmlspecialchars($fourthSubject['subject_name']); ?>
                                                <br><small>(৪র্থ বিষয়)</small>
                                                <br>
                                                <small><?php echo htmlspecialchars($fourthSubject['subject_code']); ?></small>
                                            </th>
                                            <?php endif; ?>
                                            <th rowspan="2" style="min-width: 30px;">মোট<br>মার্কস</th>
                                            <th rowspan="2" style="min-width: 30px;">শতকরা<br>(%)</th>
                                            <th rowspan="2" style="min-width: 30px;">জিপিএ</th>
                                            <th rowspan="2" style="min-width: 30px;">গ্রেড</th>
                                            <th rowspan="2" style="min-width: 40px;">ফলাফল</th>
                                        </tr>
                                        <tr>
                                            <?php foreach ($mainSubjects as $subject): ?>
                                            <th style="min-width: 20px;">সিকিউ</th>
                                            <th style="min-width: 20px;">এমসিকিউ</th>
                                            <th style="min-width: 20px;">প্র্যাক</th>
                                            <th style="min-width: 20px;">মোট</th>
                                            <?php endforeach; ?>

                                            <?php if ($fourthSubject): ?>
                                            <th style="min-width: 20px;">সিকিউ</th>
                                            <th style="min-width: 20px;">এমসিকিউ</th>
                                            <th style="min-width: 20px;">প্র্যাক</th>
                                            <th style="min-width: 20px;">মোট</th>
                                            <?php endif; ?>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Marks row -->
                                        <tr>
                                            <td class="text-center" style="min-width: 40px;">মার্কস</td>
                                            <?php
                                            // Display marks for main subjects
                                            foreach ($mainSubjects as $subject):
                                                $hasResult = isset($student['results'][$subject['id']]);
                                                $cqMarks = $hasResult && isset($student['results'][$subject['id']]['cq_marks']) && $student['results'][$subject['id']]['cq_marks'] > 0 ? intval($student['results'][$subject['id']]['cq_marks']) : '';
                                                $mcqMarks = $hasResult && isset($student['results'][$subject['id']]['mcq_marks']) && $student['results'][$subject['id']]['mcq_marks'] > 0 ? intval($student['results'][$subject['id']]['mcq_marks']) : '';
                                                $practicalMarks = $hasResult && isset($student['results'][$subject['id']]['practical_marks']) && $student['results'][$subject['id']]['practical_marks'] > 0 ? intval($student['results'][$subject['id']]['practical_marks']) : '';
                                                $marksObtained = $hasResult && $student['results'][$subject['id']]['marks_obtained'] > 0 ? intval($student['results'][$subject['id']]['marks_obtained']) : '';
                                            ?>
                                            <td style="min-width: 20px;"><?php echo $cqMarks; ?></td>
                                            <td style="min-width: 20px;"><?php echo $mcqMarks; ?></td>
                                            <td style="min-width: 20px;"><?php echo $practicalMarks; ?></td>
                                            <td style="min-width: 20px; font-weight: bold;"><?php echo $marksObtained; ?></td>
                                            <?php endforeach; ?>

                                            <!-- Display marks for 4th subject if exists -->
                                            <?php if ($fourthSubject):
                                                $hasResult = isset($student['results'][$fourthSubject['id']]);
                                                $cqMarks = $hasResult && isset($student['results'][$fourthSubject['id']]['cq_marks']) && $student['results'][$fourthSubject['id']]['cq_marks'] > 0 ? intval($student['results'][$fourthSubject['id']]['cq_marks']) : '';
                                                $mcqMarks = $hasResult && isset($student['results'][$fourthSubject['id']]['mcq_marks']) && $student['results'][$fourthSubject['id']]['mcq_marks'] > 0 ? intval($student['results'][$fourthSubject['id']]['mcq_marks']) : '';
                                                $practicalMarks = $hasResult && isset($student['results'][$fourthSubject['id']]['practical_marks']) && $student['results'][$fourthSubject['id']]['practical_marks'] > 0 ? intval($student['results'][$fourthSubject['id']]['practical_marks']) : '';
                                                $marksObtained = $hasResult && $student['results'][$fourthSubject['id']]['marks_obtained'] > 0 ? intval($student['results'][$fourthSubject['id']]['marks_obtained']) : '';
                                            ?>
                                            <td class="fourth-subject" style="min-width: 20px;"><?php echo $cqMarks; ?></td>
                                            <td class="fourth-subject" style="min-width: 20px;"><?php echo $mcqMarks; ?></td>
                                            <td class="fourth-subject" style="min-width: 20px;"><?php echo $practicalMarks; ?></td>
                                            <td class="fourth-subject" style="min-width: 20px; font-weight: bold;"><?php echo $marksObtained; ?></td>
                                            <?php endif; ?>
                                            <td style="min-width: 30px; font-weight: bold;"><?php echo $student['total_marks'] > 0 ? intval($student['total_marks']) : ''; ?></td>
                                            <td style="min-width: 30px; font-weight: bold;">
                                                <?php
                                                $percentage = ($student['total_marks'] > 0) ? ($student['total_obtained'] / $student['total_marks']) * 100 : 0;
                                                echo $percentage > 0 ? intval($percentage) . '%' : '';
                                                ?>
                                            </td>
                                            <td rowspan="2" style="min-width: 30px; font-weight: bold;"><?php echo $student['gpa'] > 0 ? number_format($student['gpa'], 2) : ''; ?></td>
                                            <td rowspan="2" class="grade-cell grade-<?php echo $student['grade']; ?>" style="min-width: 30px; font-weight: bold;"><?php echo $student['grade']; ?></td>
                                            <td rowspan="2" class="<?php echo $student['has_failed'] ? 'text-danger' : 'text-success'; ?> fw-bold" style="min-width: 40px;">
                                                <?php echo ($student['has_failed'] ? 'অনুত্তীর্ণ' : 'উত্তীর্ণ'); ?>
                                            </td>
                                        </tr>

                                        <!-- Grade row -->
                                        <tr>
                                            <td class="text-center" style="min-width: 40px;">গ্রেড</td>
                                            <?php
                                            // Display grades for main subjects
                                            foreach ($mainSubjects as $subject):
                                                $hasResult = isset($student['results'][$subject['id']]);
                                                $grade = $hasResult ? $student['results'][$subject['id']]['grade'] : '-';
                                                $subjectGPA = $hasResult ? calculateGPA($grade) : 0;
                                            ?>
                                            <td colspan="4" class="grade-cell grade-<?php echo $grade; ?>" style="min-width: 80px;">
                                                <?php echo $grade; ?>
                                                <small>(<?php echo number_format($subjectGPA, 2); ?>)</small>
                                            </td>
                                            <?php endforeach; ?>

                                            <!-- Display grade for 4th subject if exists -->
                                            <?php if ($fourthSubject):
                                                $hasResult = isset($student['results'][$fourthSubject['id']]);
                                                $grade = $hasResult ? $student['results'][$fourthSubject['id']]['grade'] : '-';
                                                $subjectGPA = $hasResult ? calculateGPA($grade) : 0;
                                            ?>
                                            <td colspan="4" class="grade-cell grade-<?php echo $grade; ?> fourth-subject" style="min-width: 80px;">
                                                <?php echo $grade; ?>
                                                <small>(<?php echo number_format($subjectGPA, 2); ?>)</small>
                                            </td>
                                            <?php endif; ?>
                                            <td style="min-width: 30px; font-weight: bold;"><?php echo intval($student['total_obtained']); ?></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <?php endforeach; ?>
                        </div>

                        <?php if ($totalPages > 1): ?>
                        <div class="d-flex justify-content-between align-items-center mt-3 no-print">
                            <div>
                                <span class="text-muted">দেখানো হচ্ছে <?php echo ($page - 1) * $perPage + 1; ?> থেকে <?php echo min($page * $perPage, $totalStudents); ?> (মোট <?php echo $totalStudents; ?>)</span>
                            </div>
                            <nav aria-label="Page navigation">
                                <ul class="pagination">
                                    <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?exam_id=<?php echo $examId; ?>&class_id=<?php echo $classId; ?>&department_id=<?php echo $departmentId; ?>&session_id=<?php echo $sessionId; ?>&per_page=<?php echo $perPage; ?>&page=1" aria-label="First">
                                            <span aria-hidden="true">&laquo;&laquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?exam_id=<?php echo $examId; ?>&class_id=<?php echo $classId; ?>&department_id=<?php echo $departmentId; ?>&session_id=<?php echo $sessionId; ?>&per_page=<?php echo $perPage; ?>&page=<?php echo $page - 1; ?>" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    <?php endif; ?>

                                    <?php
                                    $startPage = max(1, $page - 2);
                                    $endPage = min($totalPages, $page + 2);

                                    for ($i = $startPage; $i <= $endPage; $i++):
                                    ?>
                                    <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                        <a class="page-link" href="?exam_id=<?php echo $examId; ?>&class_id=<?php echo $classId; ?>&department_id=<?php echo $departmentId; ?>&session_id=<?php echo $sessionId; ?>&per_page=<?php echo $perPage; ?>&page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                    </li>
                                    <?php endfor; ?>

                                    <?php if ($page < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?exam_id=<?php echo $examId; ?>&class_id=<?php echo $classId; ?>&department_id=<?php echo $departmentId; ?>&session_id=<?php echo $sessionId; ?>&per_page=<?php echo $perPage; ?>&page=<?php echo $page + 1; ?>" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?exam_id=<?php echo $examId; ?>&class_id=<?php echo $classId; ?>&department_id=<?php echo $departmentId; ?>&session_id=<?php echo $sessionId; ?>&per_page=<?php echo $perPage; ?>&page=<?php echo $totalPages; ?>" aria-label="Last">
                                            <span aria-hidden="true">&raquo;&raquo;</span>
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php elseif ($examId > 0): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> নির্বাচিত পরীক্ষার জন্য কোন ফলাফল পাওয়া যায়নি।
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function printPage(orientation) {
            // Create a style element
            var style = document.createElement('style');
            var tabulationSheet = document.querySelector('.tabulation-sheet');

            // Set the print orientation
            if (orientation === 'landscape') {
                style.innerHTML = `
                    @page {
                        size: landscape;
                        margin: 5mm;
                    }

                    @media print {
                        .tabulation-table {
                            font-size: 8px !important;
                            border-collapse: collapse !important;
                            width: 100% !important;
                        }
                        .tabulation-table th, .tabulation-table td {
                            padding: 0px 1px !important;
                            border: 1px solid #aaa !important;
                            text-align: center !important;
                            white-space: nowrap !important;
                        }
                        .student-tabulation {
                            margin-bottom: 0 !important;
                            page-break-inside: avoid !important;
                        }
                    }
                `;
            } else {
                style.innerHTML = `
                    @page {
                        size: portrait;
                        margin: 5mm;
                    }

                    @media print {
                        .tabulation-table {
                            font-size: 8px !important;
                            border-collapse: collapse !important;
                            width: 100% !important;
                        }
                        .tabulation-table th, .tabulation-table td {
                            padding: 0px 1px !important;
                            border: 1px solid #aaa !important;
                            text-align: center !important;
                            white-space: nowrap !important;
                        }
                        .student-tabulation {
                            margin-bottom: 0 !important;
                            page-break-inside: avoid !important;
                        }
                    }
                `;
            }

            // Append the style to the head
            document.head.appendChild(style);

            // Print the page
            window.print();

            // Remove the style after printing
            setTimeout(function() {
                document.head.removeChild(style);
            }, 1000);
        }
    </script>
</body>
</html>
