<?php
/**
 * SMS Functions for ZFAW
 *
 * This file contains functions for sending SMS using different gateways
 */

/**
 * Send SMS using Grameenphone Enterprise API
 *
 * @param string $phoneNumber Recipient phone number (must be in format: 880XXXXXXXXXX)
 * @param string $message SMS message content
 * @return array Status of the SMS sending operation
 */
function sendSmsViaGp($phoneNumber, $message) {
    // Format phone number if needed (ensure it starts with 880)
    if (substr($phoneNumber, 0, 3) !== '880') {
        // If number starts with 0, replace it with 880
        if (substr($phoneNumber, 0, 1) === '0') {
            $phoneNumber = '88' . $phoneNumber;
        }
        // If number starts with +880, remove the +
        else if (substr($phoneNumber, 0, 4) === '+880') {
            $phoneNumber = substr($phoneNumber, 1);
        }
        // Otherwise, prepend 880
        else {
            $phoneNumber = '880' . $phoneNumber;
        }
    }

    // Get Grameenphone Enterprise API credentials from database
    $username = "YOUR_GP_USERNAME"; // Default value
    $password = "YOUR_GP_PASSWORD"; // Default value
    $apiCode = "YOUR_GP_API_CODE";  // Default value
    $cli = "YOUR_GP_CLI";           // Default value

    // Try to get credentials from database
    global $conn;
    if (isset($conn)) {
        $settingsQuery = "SELECT gp_username, gp_password, gp_api_code, gp_cli FROM fee_sms_settings LIMIT 1";
        $result = $conn->query($settingsQuery);

        if ($result && $result->num_rows > 0) {
            $settings = $result->fetch_assoc();

            if (!empty($settings['gp_username'])) {
                $username = $settings['gp_username'];
            }

            if (!empty($settings['gp_password'])) {
                $password = $settings['gp_password'];
            }

            if (!empty($settings['gp_api_code'])) {
                $apiCode = $settings['gp_api_code'];
            }

            if (!empty($settings['gp_cli'])) {
                $cli = $settings['gp_cli'];
            }
        }
    }

    // Prepare API URL and parameters
    $apiUrl = "https://gpcmp.grameenphone.com/gpcmpapi/messageplatform/controller.home";
    $params = [
        "username" => $username,
        "password" => $password,
        "apicode" => $apiCode,
        "msisdn" => $phoneNumber,
        "countrycode" => "880",
        "cli" => $cli,
        "messagetype" => "1", // 1 for text message
        "message" => $message,
        "messageid" => uniqid()
    ];

    // Initialize cURL session
    $ch = curl_init();

    // Set cURL options
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    // Execute cURL session and get response
    $response = curl_exec($ch);
    $error = curl_error($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    // Close cURL session
    curl_close($ch);

    // Check for errors
    if ($error) {
        return [
            "status" => "failed",
            "message" => "cURL Error: " . $error
        ];
    }

    // Parse response
    $responseData = json_decode($response, true);

    // Check if response is valid
    if ($httpCode == 200 && isset($responseData['success']) && $responseData['success'] === true) {
        return [
            "status" => "success",
            "message" => "SMS sent successfully",
            "response" => $responseData
        ];
    } else {
        return [
            "status" => "failed",
            "message" => "Failed to send SMS: " . ($responseData['message'] ?? 'Unknown error'),
            "response" => $responseData
        ];
    }
}

/**
 * Send SMS using SSL Wireless API
 *
 * @param string $phoneNumber Recipient phone number
 * @param string $message SMS message content
 * @return array Status of the SMS sending operation
 */
function sendSmsViaSslWireless($phoneNumber, $message) {
    // Format phone number if needed
    if (substr($phoneNumber, 0, 1) === '0') {
        $phoneNumber = '88' . $phoneNumber;
    }

    // SSL Wireless API credentials
    $apiToken = "YOUR_SSL_API_TOKEN";
    $sid = "YOUR_SSL_SID";
    $csmsId = uniqid();

    // Prepare API URL and parameters
    $sslSmsUrl = "https://smsplus.sslwireless.com/api/v3/send-sms";
    $data = [
        "api_token" => $apiToken,
        "sid" => $sid,
        "msisdn" => $phoneNumber,
        "sms" => $message,
        "csms_id" => $csmsId
    ];

    // Initialize cURL session
    $ch = curl_init();

    // Set cURL options
    curl_setopt($ch, CURLOPT_URL, $sslSmsUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    // Execute cURL session and get response
    $response = curl_exec($ch);
    $err = curl_error($ch);

    // Close cURL session
    curl_close($ch);

    // Check for errors
    if ($err) {
        return [
            "status" => "failed",
            "message" => "cURL Error: " . $err
        ];
    } else {
        $responseData = json_decode($response, true);

        if (isset($responseData['status']) && $responseData['status'] === 'SUCCESS') {
            return [
                "status" => "success",
                "message" => "SMS sent successfully",
                "response" => $responseData
            ];
        } else {
            return [
                "status" => "failed",
                "message" => "Failed to send SMS: " . ($responseData['error_message'] ?? 'Unknown error'),
                "response" => $responseData
            ];
        }
    }
}

/**
 * Send SMS using Banglalink Enterprise API
 *
 * @param string $phoneNumber Recipient phone number
 * @param string $message SMS message content
 * @return array Status of the SMS sending operation
 */
function sendSmsViaBanglalink($phoneNumber, $message) {
    // Format phone number if needed
    if (substr($phoneNumber, 0, 1) === '0') {
        $phoneNumber = '88' . $phoneNumber;
    }

    // Banglalink Enterprise API credentials
    $username = "YOUR_BANGLALINK_USERNAME";
    $password = "YOUR_BANGLALINK_PASSWORD";
    $apiKey = "YOUR_BANGLALINK_API_KEY";
    $senderId = "YOUR_BANGLALINK_SENDER_ID";

    // Prepare API URL and parameters
    $apiUrl = "https://api.banglalink.net/v1/sendSMS";
    $params = [
        "username" => $username,
        "password" => $password,
        "apiKey" => $apiKey,
        "senderId" => $senderId,
        "msisdn" => $phoneNumber,
        "message" => $message,
        "messagetype" => "text"
    ];

    // Initialize cURL session
    $ch = curl_init();

    // Set cURL options
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);

    // Execute cURL session and get response
    $response = curl_exec($ch);
    $error = curl_error($ch);

    // Close cURL session
    curl_close($ch);

    // Check for errors
    if ($error) {
        return [
            "status" => "failed",
            "message" => "cURL Error: " . $error
        ];
    }

    // Parse response
    $responseData = json_decode($response, true);

    // Check if response is valid
    if (isset($responseData['status']) && $responseData['status'] === 'success') {
        return [
            "status" => "success",
            "message" => "SMS sent successfully",
            "response" => $responseData
        ];
    } else {
        return [
            "status" => "failed",
            "message" => "Failed to send SMS: " . ($responseData['message'] ?? 'Unknown error'),
            "response" => $responseData
        ];
    }
}

/**
 * Send SMS using Robi Enterprise API
 *
 * @param string $phoneNumber Recipient phone number
 * @param string $message SMS message content
 * @return array Status of the SMS sending operation
 */
function sendSmsViaRobi($phoneNumber, $message) {
    // Format phone number if needed
    if (substr($phoneNumber, 0, 1) === '0') {
        $phoneNumber = '88' . $phoneNumber;
    }

    // Robi Enterprise API credentials
    $username = "YOUR_ROBI_USERNAME";
    $password = "YOUR_ROBI_PASSWORD";
    $masking = "YOUR_ROBI_MASKING"; // Sender ID

    // Prepare API URL and parameters
    $apiUrl = "https://api.robi.com.bd/SendSMS";
    $params = [
        "username" => $username,
        "password" => $password,
        "To" => $phoneNumber,
        "From" => $masking,
        "Message" => $message
    ];

    // Initialize cURL session
    $ch = curl_init();

    // Set cURL options
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    // Execute cURL session and get response
    $response = curl_exec($ch);
    $error = curl_error($ch);

    // Close cURL session
    curl_close($ch);

    // Check for errors
    if ($error) {
        return [
            "status" => "failed",
            "message" => "cURL Error: " . $error
        ];
    }

    // Parse response
    $responseData = json_decode($response, true);

    // Check if response is valid
    if (isset($responseData['Status']) && $responseData['Status'] === 'Success') {
        return [
            "status" => "success",
            "message" => "SMS sent successfully",
            "response" => $responseData
        ];
    } else {
        return [
            "status" => "failed",
            "message" => "Failed to send SMS: " . ($responseData['ErrorMessage'] ?? 'Unknown error'),
            "response" => $responseData
        ];
    }
}

/**
 * Send SMS using mTalk API
 *
 * @param string $phoneNumber Recipient phone number
 * @param string $message SMS message content
 * @return array Status of the SMS sending operation
 */
function sendSmsViaMtalk($phoneNumber, $message) {
    // Format phone number if needed
    if (substr($phoneNumber, 0, 1) === '0') {
        $phoneNumber = '88' . $phoneNumber;
    }

    // mTalk API credentials
    $apiKey = "YOUR_MTALK_API_KEY";
    $senderId = "YOUR_MTALK_SENDER_ID";

    // Prepare API URL and parameters
    $apiUrl = "https://api.mtalkz.com/v1/sms/send";
    $params = [
        "apikey" => $apiKey,
        "senderid" => $senderId,
        "number" => $phoneNumber,
        "message" => $message,
        "format" => "json"
    ];

    // Initialize cURL session
    $ch = curl_init();

    // Set cURL options
    curl_setopt($ch, CURLOPT_URL, $apiUrl . '?' . http_build_query($params));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    // Execute cURL session and get response
    $response = curl_exec($ch);
    $error = curl_error($ch);

    // Close cURL session
    curl_close($ch);

    // Check for errors
    if ($error) {
        return [
            "status" => "failed",
            "message" => "cURL Error: " . $error
        ];
    }

    // Parse response
    $responseData = json_decode($response, true);

    // Check if response is valid
    if (isset($responseData['status']) && $responseData['status'] === 'success') {
        return [
            "status" => "success",
            "message" => "SMS sent successfully",
            "response" => $responseData
        ];
    } else {
        return [
            "status" => "failed",
            "message" => "Failed to send SMS: " . ($responseData['message'] ?? 'Unknown error'),
            "response" => $responseData
        ];
    }
}

/**
 * Send SMS using Infobip API
 *
 * @param string $phoneNumber Recipient phone number
 * @param string $message SMS message content
 * @return array Status of the SMS sending operation
 */
function sendSmsViaInfobip($phoneNumber, $message) {
    // Format phone number if needed
    if (substr($phoneNumber, 0, 1) === '0') {
        $phoneNumber = '88' . $phoneNumber;
    }

    // Infobip API credentials
    $apiKey = "YOUR_INFOBIP_API_KEY";
    $baseUrl = "YOUR_INFOBIP_BASE_URL"; // e.g., https://xyz123.api.infobip.com
    $from = "YOUR_INFOBIP_SENDER_ID";

    // Prepare API URL and parameters
    $apiUrl = $baseUrl . "/sms/2/text/advanced";
    $payload = [
        "messages" => [
            [
                "from" => $from,
                "destinations" => [
                    ["to" => $phoneNumber]
                ],
                "text" => $message
            ]
        ]
    ];

    // Initialize cURL session
    $ch = curl_init();

    // Set cURL options
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Authorization: App " . $apiKey,
        "Content-Type: application/json",
        "Accept: application/json"
    ]);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    // Execute cURL session and get response
    $response = curl_exec($ch);
    $error = curl_error($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    // Close cURL session
    curl_close($ch);

    // Check for errors
    if ($error) {
        return [
            "status" => "failed",
            "message" => "cURL Error: " . $error
        ];
    }

    // Parse response
    $responseData = json_decode($response, true);

    // Check if response is valid
    if ($httpCode >= 200 && $httpCode < 300) {
        return [
            "status" => "success",
            "message" => "SMS sent successfully",
            "response" => $responseData
        ];
    } else {
        return [
            "status" => "failed",
            "message" => "Failed to send SMS: " . ($responseData['requestError']['serviceException']['text'] ?? 'Unknown error'),
            "response" => $responseData
        ];
    }
}
