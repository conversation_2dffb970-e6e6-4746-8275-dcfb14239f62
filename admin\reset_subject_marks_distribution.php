<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$success_message = "";
$error_message = "";

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reset_confirm'])) {
    // Drop the subject_marks_distribution table if it exists
    $dropTableQuery = "DROP TABLE IF EXISTS subject_marks_distribution";
    
    if ($conn->query($dropTableQuery)) {
        // Create the table with the new structure
        $createTableQuery = "CREATE TABLE subject_marks_distribution (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            subject_id INT(11) NOT NULL,
            cq_marks DECIMAL(5,2) DEFAULT 70.00,
            mcq_marks DECIMAL(5,2) DEFAULT 30.00,
            practical_marks DECIMAL(5,2) DEFAULT 0.00,
            total_marks DECIMAL(5,2) DEFAULT 100.00,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY (subject_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($createTableQuery)) {
            $success_message = "বিষয় মার্কস ডিস্ট্রিবিউশন টেবিল সফলভাবে রিসেট করা হয়েছে।";
        } else {
            $error_message = "টেবিল তৈরি করতে সমস্যা হয়েছে: " . $conn->error;
        }
    } else {
        $error_message = "টেবিল মুছতে সমস্যা হয়েছে: " . $conn->error;
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>রিসেট বিষয় মার্কস ডিস্ট্রিবিউশন</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-danger text-white">
                        <h3 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i> বিষয় মার্কস ডিস্ট্রিবিউশন রিসেট</h3>
                    </div>
                    <div class="card-body">
                        <?php if ($success_message): ?>
                            <div class="alert alert-success">
                                <h4 class="alert-heading">সফল!</h4>
                                <p><?php echo $success_message; ?></p>
                                <hr>
                                <p class="mb-0">এখন আপনি <a href="subject_marks_distribution.php" class="alert-link">বিষয় মার্কস ডিস্ট্রিবিউশন</a> পেজে ফিরে যেতে পারেন।</p>
                            </div>
                        <?php elseif ($error_message): ?>
                            <div class="alert alert-danger">
                                <h4 class="alert-heading">ত্রুটি!</h4>
                                <p><?php echo $error_message; ?></p>
                                <hr>
                                <p class="mb-0">আবার চেষ্টা করুন অথবা <a href="subject_marks_distribution.php" class="alert-link">বিষয় মার্কস ডিস্ট্রিবিউশন</a> পেজে ফিরে যান।</p>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <h4 class="alert-heading">সতর্কতা!</h4>
                                <p>আপনি বিষয় মার্কস ডিস্ট্রিবিউশন টেবিল রিসেট করতে যাচ্ছেন। এটি করলে সমস্ত ডাটা মুছে যাবে এবং পুনরুদ্ধার করা যাবে না।</p>
                                <hr>
                                <p class="mb-0">আপনি কি নিশ্চিত যে আপনি এটি করতে চান?</p>
                            </div>
                            
                            <form method="POST" action="">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="reset_confirm" name="reset_confirm" required>
                                    <label class="form-check-label" for="reset_confirm">
                                        হ্যাঁ, আমি নিশ্চিত যে আমি বিষয় মার্কস ডিস্ট্রিবিউশন টেবিল রিসেট করতে চাই।
                                    </label>
                                </div>
                                
                                <div class="d-flex justify-content-between">
                                    <a href="subject_marks_distribution.php" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-2"></i> ফিরে যান
                                    </a>
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-trash-alt me-2"></i> রিসেট করুন
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
