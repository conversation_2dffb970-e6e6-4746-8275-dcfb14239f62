<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Set session for testing
if (!isset($_SESSION['userId'])) {
    $_SESSION['userId'] = 1;
    $_SESSION['userType'] = 'admin';
}

require_once '../includes/dbh.inc.php';

echo "<h2>Debug Exam Creation Issue</h2>";

$examTableName = "exams_primary_lower";

// Test 1: Check if exam table exists
echo "<h3>1. Check Exam Table</h3>";
try {
    $tableCheck = $conn->query("SHOW TABLES LIKE '$examTableName'");
    if ($tableCheck && $tableCheck->num_rows > 0) {
        echo "<p style='color: green;'>✅ Table '$examTableName' exists</p>";
        
        // Show table structure
        $describeQuery = "DESCRIBE $examTableName";
        $describeResult = $conn->query($describeQuery);
        
        if ($describeResult) {
            echo "<h4>Table Structure:</h4>";
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
            while ($row = $describeResult->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['Field'] . "</td>";
                echo "<td>" . $row['Type'] . "</td>";
                echo "<td>" . $row['Null'] . "</td>";
                echo "<td>" . $row['Key'] . "</td>";
                echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
                echo "<td>" . $row['Extra'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p style='color: red;'>❌ Table '$examTableName' does not exist</p>";
        echo "<p>Creating table...</p>";
        
        // Create table
        $createTableQuery = "
            CREATE TABLE IF NOT EXISTS $examTableName (
                id INT AUTO_INCREMENT PRIMARY KEY,
                exam_name VARCHAR(255) NOT NULL,
                exam_type VARCHAR(100) NOT NULL,
                class_id INT,
                session VARCHAR(20),
                subject_id INT,
                exam_date DATE,
                start_time TIME,
                end_time TIME,
                total_marks INT DEFAULT 100,
                passing_marks INT DEFAULT 33,
                instructions TEXT,
                created_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ";
        
        if ($conn->query($createTableQuery)) {
            echo "<p style='color: green;'>✅ Table '$examTableName' created successfully</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to create table: " . $conn->error . "</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking table: " . $e->getMessage() . "</p>";
}

// Test 2: Test form submission
echo "<h3>2. Test Form Submission</h3>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_exam'])) {
    try {
        $examName = $_POST['exam_name'] ?? '';
        $examType = $_POST['exam_type'] ?? '';
        $classId = $_POST['class_id'] ?? '';
        $examSession = $_POST['exam_session'] ?? '';
        $subjectId = $_POST['subject_id'] ?? '';
        $examDate = $_POST['exam_date'] ?? '';
        $startTime = $_POST['start_time'] ?? '';
        $endTime = $_POST['end_time'] ?? '';
        $totalMarks = $_POST['total_marks'] ?? 100;
        $passingMarks = $_POST['passing_marks'] ?? 33;
        $instructions = $_POST['instructions'] ?? '';
        $createdBy = $_SESSION['userId'];

        echo "<h4>Received Data:</h4>";
        echo "<ul>";
        echo "<li>Exam Name: $examName</li>";
        echo "<li>Exam Type: $examType</li>";
        echo "<li>Class ID: $classId</li>";
        echo "<li>Session: $examSession</li>";
        echo "<li>Subject ID: $subjectId</li>";
        echo "<li>Exam Date: $examDate</li>";
        echo "<li>Start Time: $startTime</li>";
        echo "<li>End Time: $endTime</li>";
        echo "<li>Total Marks: $totalMarks</li>";
        echo "<li>Passing Marks: $passingMarks</li>";
        echo "<li>Created By: $createdBy</li>";
        echo "</ul>";

        if (!empty($examName) && !empty($examType) && !empty($examDate) && !empty($examSession)) {
            // Check if table exists
            $tableCheck = $conn->query("SHOW TABLES LIKE '$examTableName'");
            if ($tableCheck && $tableCheck->num_rows > 0) {
                $insertQuery = "INSERT INTO $examTableName (exam_name, exam_type, class_id, session, subject_id, exam_date, start_time, end_time, total_marks, passing_marks, instructions, created_by)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                $stmt = $conn->prepare($insertQuery);
                if ($stmt) {
                    $stmt->bind_param("ssissssiisi", $examName, $examType, $classId, $examSession, $subjectId, $examDate, $startTime, $endTime, $totalMarks, $passingMarks, $instructions, $createdBy);

                    if ($stmt->execute()) {
                        echo "<p style='color: green;'>✅ Exam created successfully!</p>";
                        echo "<p>Exam ID: " . $conn->insert_id . "</p>";
                    } else {
                        echo "<p style='color: red;'>❌ Failed to execute query: " . $stmt->error . "</p>";
                    }
                } else {
                    echo "<p style='color: red;'>❌ Failed to prepare statement: " . $conn->error . "</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ Table does not exist</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ Missing required fields</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>";
    }
}

// Test form
echo "<h3>3. Test Form</h3>";
?>

<form method="POST">
    <div style="margin-bottom: 10px;">
        <label>Exam Name:</label>
        <input type="text" name="exam_name" value="Test Exam" required>
    </div>
    <div style="margin-bottom: 10px;">
        <label>Exam Type:</label>
        <select name="exam_type" required>
            <option value="সাপ্তাহিক">সাপ্তাহিক</option>
            <option value="মাসিক">মাসিক</option>
        </select>
    </div>
    <div style="margin-bottom: 10px;">
        <label>Class ID:</label>
        <select name="class_id">
            <option value="">Select Class</option>
            <?php
            try {
                $classQuery = "SELECT id, class_name FROM classes WHERE class_name IN ('ONE', 'TWO') ORDER BY class_name";
                $classResult = $conn->query($classQuery);
                if ($classResult) {
                    while ($class = $classResult->fetch_assoc()) {
                        echo "<option value='{$class['id']}'>{$class['class_name']}</option>";
                    }
                }
            } catch (Exception $e) {
                echo "<option value='3'>ONE</option>";
                echo "<option value='4'>TWO</option>";
            }
            ?>
        </select>
    </div>
    <div style="margin-bottom: 10px;">
        <label>Session:</label>
        <select name="exam_session" required>
            <option value="2024-2025">2024-2025</option>
            <option value="2023-2024">2023-2024</option>
        </select>
    </div>
    <div style="margin-bottom: 10px;">
        <label>Exam Date:</label>
        <input type="date" name="exam_date" value="<?php echo date('Y-m-d'); ?>" required>
    </div>
    <div style="margin-bottom: 10px;">
        <label>Start Time:</label>
        <input type="time" name="start_time" value="10:00">
    </div>
    <div style="margin-bottom: 10px;">
        <label>End Time:</label>
        <input type="time" name="end_time" value="12:00">
    </div>
    <div style="margin-bottom: 10px;">
        <label>Total Marks:</label>
        <input type="number" name="total_marks" value="100">
    </div>
    <div style="margin-bottom: 10px;">
        <label>Passing Marks:</label>
        <input type="number" name="passing_marks" value="33">
    </div>
    <button type="submit" name="test_exam" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;">Test Create Exam</button>
</form>

<br><br>
<a href="class_exam_primary_lower_1_2.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Back to Main Page</a>

<?php
$conn->close();
?>
