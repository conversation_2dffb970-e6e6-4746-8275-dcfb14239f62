/* Ex<PERSON> CSS */
.exam-type-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
}

.exam-type-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.exam-type-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.exam-type-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.exam-type-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    font-size: 16px;
}

.exam-type-icon {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: white;
}

.exam-type-icon.cq {
    background-color: #4361ee;
}

.exam-type-icon.mcq {
    background-color: #2ecc71;
}

.exam-type-icon.practical {
    background-color: #3498db;
}

.exam-type-toggle {
    position: relative;
}

.exam-type-body {
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.exam-type-marks {
    display: flex;
    align-items: center;
    gap: 10px;
}

.marks-input-container {
    flex: 1;
    position: relative;
}

.marks-input {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.marks-input:focus {
    border-color: #4361ee;
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
    outline: none;
}

.marks-input:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
}

.marks-label {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    font-weight: 500;
}

.exam-type-info {
    font-size: 13px;
    color: #666;
    margin-top: 5px;
}

.exam-type-card.disabled {
    opacity: 0.6;
}

.exam-type-card.disabled .exam-type-body {
    display: none;
}

/* Progress bar styles */
.marks-distribution {
    margin-top: 20px;
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
}

.marks-distribution-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.marks-distribution-cards {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.distribution-card {
    flex: 1;
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.distribution-card-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 5px;
}

.distribution-card-value {
    font-size: 24px;
    font-weight: 700;
}

.distribution-card.cq .distribution-card-value {
    color: #4361ee;
}

.distribution-card.mcq .distribution-card-value {
    color: #2ecc71;
}

.distribution-card.practical .distribution-card-value {
    color: #3498db;
}

.marks-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.marks-total-label {
    font-weight: 600;
    color: #333;
}

.marks-total-value {
    font-weight: 700;
    padding: 5px 15px;
    border-radius: 20px;
    color: white;
}

.marks-progress {
    height: 15px;
    background-color: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.marks-progress-bar {
    height: 100%;
    float: left;
}

.marks-progress-bar.cq {
    background-color: #4361ee;
}

.marks-progress-bar.mcq {
    background-color: #2ecc71;
}

.marks-progress-bar.practical {
    background-color: #3498db;
}

.marks-info {
    font-size: 13px;
    color: #666;
}

/* Form switch custom styles */
.form-check-input:checked {
    background-color: #4361ee;
    border-color: #4361ee;
}

.form-check-input:focus {
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
    border-color: #4361ee;
}

/* Responsive styles */
@media (max-width: 768px) {
    .marks-distribution-cards {
        flex-direction: column;
    }
}
