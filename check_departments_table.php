<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'includes/dbh.inc.php';

echo "<h1>Checking Departments Table Structure</h1>";
echo "<pre>";

// Ensure connection is active
$conn = ensure_connection();

if ($conn) {
    echo "Database connection successful.\n\n";
    
    // Check if departments table exists
    $result = $conn->query("SHOW TABLES LIKE 'departments'");
    
    if ($result && $result->num_rows > 0) {
        echo "The 'departments' table exists.\n\n";
        
        // Get table structure
        $result = $conn->query("DESCRIBE departments");
        
        if ($result) {
            echo "Structure of 'departments' table:\n";
            echo "-----------------------------\n";
            
            while ($row = $result->fetch_assoc()) {
                echo "{$row['Field']} | {$row['Type']} | {$row['Key']} | {$row['Default']}\n";
            }
            
            echo "\n";
            
            // Get sample data
            $result = $conn->query("SELECT * FROM departments LIMIT 5");
            
            if ($result && $result->num_rows > 0) {
                echo "Sample data from 'departments' table:\n";
                echo "-----------------------------\n";
                
                $fields = array();
                $data = array();
                
                // Get field names
                $fields_info = $result->fetch_fields();
                foreach ($fields_info as $field) {
                    $fields[] = $field->name;
                }
                
                echo implode(" | ", $fields) . "\n";
                echo str_repeat("-", 50) . "\n";
                
                // Get data
                while ($row = $result->fetch_assoc()) {
                    echo implode(" | ", $row) . "\n";
                }
            } else {
                echo "No data found in the 'departments' table or error executing query.\n";
            }
        } else {
            echo "Error getting table structure: " . $conn->error . "\n";
        }
    } else {
        echo "The 'departments' table does not exist!\n";
        
        // Check what tables exist
        $result = $conn->query("SHOW TABLES");
        
        if ($result) {
            echo "\nAvailable tables in the database:\n";
            echo "-----------------------------\n";
            
            while ($row = $result->fetch_row()) {
                echo $row[0] . "\n";
            }
        }
    }
    
    $conn->close();
    echo "\nDatabase connection closed.";
} else {
    echo "Error: Could not establish database connection.";
}

echo "</pre>";
?>
