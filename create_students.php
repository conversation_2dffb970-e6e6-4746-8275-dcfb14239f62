<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Database connection parameters
$servername = "127.0.0.1";
$username = "root";
$password = "";
$dbname = "zfaw";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "<h1>Student Setup Script</h1>";
echo "<pre>";

// Create students table if it doesn't exist
$sql = "CREATE TABLE IF NOT EXISTS students (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id VARCHAR(20) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    roll_no VARCHAR(20) DEFAULT NULL,
    class_id INT(11) NOT NULL,
    session_id INT(11) NOT NULL,
    department_id INT(11) DEFAULT NULL,
    gender ENUM('male', 'female', 'other') DEFAULT NULL,
    dob DATE DEFAULT NULL,
    email VARCHAR(100) DEFAULT NULL,
    phone VARCHAR(20) DEFAULT NULL,
    address TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_id) REFERENCES classes(id),
    FOREIGN KEY (session_id) REFERENCES sessions(id),
    FOREIGN KEY (department_id) REFERENCES departments(id)
)";

if ($conn->query($sql) === TRUE) {
    echo "Table 'students' created or already exists\n";
} else {
    echo "Error creating students table: " . $conn->error . "\n";
    die();
}

// Check if students table is empty
$result = $conn->query("SELECT COUNT(*) as count FROM students");
$row = $result->fetch_assoc();
if ($row['count'] > 0) {
    echo "Students table already has {$row['count']} records. Skipping sample data insertion.\n";
} else {
    // Get session IDs
    $sessionResult = $conn->query("SELECT id, session_name FROM sessions ORDER BY id LIMIT 1");
    if ($sessionResult->num_rows == 0) {
        echo "No sessions found. Please run create_database.php first.\n";
        die();
    }
    $session = $sessionResult->fetch_assoc();
    $sessionId = $session['id'];
    echo "Using session: {$session['session_name']} (ID: $sessionId)\n";
    
    // Get class IDs
    $classResult = $conn->query("SELECT id, class_name FROM classes ORDER BY id");
    if ($classResult->num_rows == 0) {
        echo "No classes found. Please run create_database.php first.\n";
        die();
    }
    
    $classes = [];
    while ($class = $classResult->fetch_assoc()) {
        $classes[] = $class;
    }
    
    // Get department IDs
    $departmentResult = $conn->query("SELECT id, department_name FROM departments ORDER BY id");
    if ($departmentResult->num_rows == 0) {
        echo "No departments found. Please run create_database.php first.\n";
        die();
    }
    
    $departments = [];
    while ($department = $departmentResult->fetch_assoc()) {
        $departments[] = $department;
    }
    
    // Sample student data
    $students = [
        // Class 1, Department 1
        ['Karim', 'Ahmed', '101', 1, 1, 'male', '2005-05-15', '<EMAIL>', '01712345678'],
        ['Fatima', 'Begum', '102', 1, 1, 'female', '2005-06-20', '<EMAIL>', '01712345679'],
        ['Rahim', 'Khan', '103', 1, 1, 'male', '2005-07-10', '<EMAIL>', '01712345680'],
        
        // Class 1, Department 2
        ['Nusrat', 'Jahan', '104', 1, 2, 'female', '2005-08-05', '<EMAIL>', '01712345681'],
        ['Imran', 'Hossain', '105', 1, 2, 'male', '2005-09-12', '<EMAIL>', '01712345682'],
        
        // Class 2, Department 1
        ['Sadia', 'Islam', '201', 2, 1, 'female', '2004-04-25', '<EMAIL>', '01712345683'],
        ['Farhan', 'Ahmed', '202', 2, 1, 'male', '2004-05-30', '<EMAIL>', '01712345684'],
        
        // Class 2, Department 2
        ['Tasnim', 'Akter', '203', 2, 2, 'female', '2004-06-15', '<EMAIL>', '01712345685'],
        ['Sakib', 'Hassan', '204', 2, 2, 'male', '2004-07-20', '<EMAIL>', '01712345686'],
        
        // Class 3, Department 1
        ['Mim', 'Rahman', '301', 3, 1, 'female', '2003-03-10', '<EMAIL>', '01712345687'],
        ['Tanvir', 'Islam', '302', 3, 1, 'male', '2003-04-15', '<EMAIL>', '01712345688'],
        
        // Class 3, Department 2
        ['Riya', 'Chowdhury', '303', 3, 2, 'female', '2003-05-20', '<EMAIL>', '01712345689'],
        ['Fahim', 'Rahman', '304', 3, 2, 'male', '2003-06-25', '<EMAIL>', '01712345690']
    ];
    
    // Insert students
    $insertQuery = "INSERT INTO students (first_name, last_name, roll_no, class_id, department_id, session_id, gender, dob, email, phone, student_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($insertQuery);
    
    $insertedCount = 0;
    foreach ($students as $student) {
        // Generate a random 6-digit student ID
        $studentId = 'S' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
        
        $stmt->bind_param("sssiiissss", 
            $student[0], // first_name
            $student[1], // last_name
            $student[2], // roll_no
            $student[3], // class_id
            $student[4], // department_id
            $sessionId,  // session_id
            $student[5], // gender
            $student[6], // dob
            $student[7], // email
            $student[8], // phone
            $studentId   // student_id
        );
        
        if ($stmt->execute()) {
            $insertedCount++;
        } else {
            echo "Error inserting student: " . $stmt->error . "\n";
        }
    }
    
    echo "Inserted $insertedCount sample students\n";
}

// Display current data
echo "\nCurrent Students:\n";
$result = $conn->query("SELECT s.id, s.student_id, s.first_name, s.last_name, s.roll_no, c.class_name, d.department_name, ss.session_name 
                        FROM students s
                        JOIN classes c ON s.class_id = c.id
                        JOIN departments d ON s.department_id = d.id
                        JOIN sessions ss ON s.session_id = ss.id
                        ORDER BY s.id LIMIT 10");

if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        echo "ID: {$row['id']}, Student ID: {$row['student_id']}, Name: {$row['first_name']} {$row['last_name']}, Roll: {$row['roll_no']}, Class: {$row['class_name']}, Department: {$row['department_name']}, Session: {$row['session_name']}\n";
    }
    
    if ($result->num_rows == 10) {
        echo "... and more (showing first 10 records only)\n";
    }
} else {
    echo "No students found\n";
}

echo "</pre>";
echo "<p>Student setup completed. You can now <a href='admin/fee_assign.php'>go to the fee assign page</a>.</p>";

// Close connection
$conn->close();
?>
