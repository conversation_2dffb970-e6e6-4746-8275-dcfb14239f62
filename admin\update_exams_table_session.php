<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

echo "<!DOCTYPE html>
<html lang='bn'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Exams Table Session Update</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap' rel='stylesheet'>
    <style>
        body { font-family: 'Hind Siliguri', sans-serif; background: #f8f9fa; }
        .container { margin-top: 50px; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='card'>
            <div class='card-header bg-primary text-white'>
                <h4><i class='fas fa-database'></i> Exams Table Session Column Update</h4>
            </div>
            <div class='card-body'>";

try {
    $examTableName = "exams_primary_lower";
    
    // Check if exams table exists
    $checkTableQuery = "SHOW TABLES LIKE '$examTableName'";
    $checkTableResult = $conn->query($checkTableQuery);
    
    if ($checkTableResult && $checkTableResult->num_rows > 0) {
        echo "<p class='text-success'>✓ $examTableName table exists</p>";
        
        // Check if session column exists
        $checkColumnQuery = "SHOW COLUMNS FROM $examTableName LIKE 'session'";
        $checkResult = $conn->query($checkColumnQuery);
        
        if ($checkResult && $checkResult->num_rows > 0) {
            echo "<p class='text-info'>✓ Session column already exists in $examTableName table</p>";
        } else {
            // Add session column
            $addColumnQuery = "ALTER TABLE $examTableName ADD COLUMN session VARCHAR(20) DEFAULT NULL AFTER class_id";
            if ($conn->query($addColumnQuery)) {
                echo "<p class='text-success'>✓ Session column added to $examTableName table successfully</p>";
            } else {
                echo "<p class='text-danger'>✗ Failed to add session column: " . $conn->error . "</p>";
            }
        }
        
        // Update existing exams with current session
        $currentYear = date('Y');
        $nextYear = $currentYear + 1;
        $currentSession = "$currentYear-$nextYear";
        
        // Check if there are exams without session
        $checkExamsQuery = "SELECT COUNT(*) as count FROM $examTableName WHERE session IS NULL OR session = ''";
        $checkExamsResult = $conn->query($checkExamsQuery);
        
        if ($checkExamsResult) {
            $row = $checkExamsResult->fetch_assoc();
            $examsWithoutSession = $row['count'];
            
            if ($examsWithoutSession > 0) {
                // Update exams without session
                $updateQuery = "UPDATE $examTableName SET session = '$currentSession' WHERE session IS NULL OR session = ''";
                if ($conn->query($updateQuery)) {
                    echo "<p class='text-success'>✓ Updated $examsWithoutSession exams with current session ($currentSession)</p>";
                } else {
                    echo "<p class='text-danger'>✗ Failed to update exams session: " . $conn->error . "</p>";
                }
            } else {
                echo "<p class='text-info'>✓ All exams already have session assigned</p>";
            }
        }
        
        // Show current exams with sessions
        echo "<h5 class='mt-4'>Current Exams with Sessions:</h5>";
        $examsQuery = "SELECT * FROM $examTableName ORDER BY created_at DESC LIMIT 10";
        $examsResult = $conn->query($examsQuery);
        
        if ($examsResult && $examsResult->num_rows > 0) {
            echo "<div class='table-responsive'>
                    <table class='table table-striped table-sm'>
                        <thead class='table-primary'>
                            <tr>
                                <th>Exam Name</th>
                                <th>Type</th>
                                <th>Session</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>";
            
            while ($exam = $examsResult->fetch_assoc()) {
                $examName = $exam['exam_name'] ?? 'N/A';
                $examType = $exam['exam_type'] ?? 'N/A';
                $session = $exam['session'] ?? 'N/A';
                $examDate = $exam['exam_date'] ?? 'N/A';
                
                echo "<tr>
                        <td><strong>$examName</strong></td>
                        <td>$examType</td>
                        <td><span class='badge bg-info'>$session</span></td>
                        <td>$examDate</td>
                      </tr>";
            }
            
            echo "</tbody></table></div>";
        } else {
            echo "<p class='text-muted'>No exams found in $examTableName table</p>";
        }
        
        echo "<div class='alert alert-success mt-4'>
                <h5>Update Complete!</h5>
                <p>$examTableName table has been updated with session column.</p>
                <p>All existing exams have been assigned to current session: <strong>$currentSession</strong></p>
              </div>";
        
    } else {
        echo "<div class='alert alert-warning'>
                <h5>Table Not Found!</h5>
                <p>$examTableName table does not exist. Please run the database setup first.</p>
                <a href='setup_class_1_2_database.php' class='btn btn-warning'>Setup Database</a>
              </div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>
            <h5>Error!</h5>
            <p>Failed to update exams table: " . $e->getMessage() . "</p>
          </div>";
}

echo "        <div class='mt-4'>
                <a href='class_exam_primary_lower_1_2.php' class='btn btn-primary'>
                    <i class='fas fa-arrow-left'></i> Back to Class 1-2 Dashboard
                </a>
            </div>
        </div>
    </div>
</div>
</body>
</html>";

if (isset($conn)) $conn->close();
?>
