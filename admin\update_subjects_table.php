<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$successMessage = '';
$errorMessage = '';

// Handle table update
if (isset($_POST['update_table'])) {
    try {
        $conn->begin_transaction();
        
        // Check if category column exists
        $checkCategoryQuery = "SHOW COLUMNS FROM subjects LIKE 'category'";
        $categoryExists = $conn->query($checkCategoryQuery)->num_rows > 0;
        
        if (!$categoryExists) {
            $addCategoryQuery = "ALTER TABLE subjects ADD COLUMN category VARCHAR(255) DEFAULT 'optional' AFTER subject_code";
            $conn->query($addCategoryQuery);
        }
        
        // Check if description column exists
        $checkDescriptionQuery = "SHOW COLUMNS FROM subjects LIKE 'description'";
        $descriptionExists = $conn->query($checkDescriptionQuery)->num_rows > 0;
        
        if (!$descriptionExists) {
            $addDescriptionQuery = "ALTER TABLE subjects ADD COLUMN description TEXT AFTER category";
            $conn->query($addDescriptionQuery);
        }
        
        $conn->commit();
        $successMessage = "subjects টেবিল সফলভাবে আপডেট করা হয়েছে!";
        
    } catch (Exception $e) {
        $conn->rollback();
        $errorMessage = "টেবিল আপডেট করতে সমস্যা হয়েছে: " . $e->getMessage();
    }
}

// Get current table structure
$tableStructureQuery = "DESCRIBE subjects";
$tableStructure = $conn->query($tableStructureQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বিষয় টেবিল আপডেট - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/admin-style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Include Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">🔧 বিষয় টেবিল আপডেট</h1>
                    <div>
                        <a href="subjects.php" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-1"></i> বিষয় তালিকায় ফিরুন
                        </a>
                    </div>
                </div>

                <!-- Success/Error Messages -->
                <?php if ($successMessage): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i><?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($errorMessage): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Current Table Structure -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-table me-2"></i>বর্তমান টেবিল গঠন</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ফিল্ড</th>
                                        <th>ধরন</th>
                                        <th>Null</th>
                                        <th>Key</th>
                                        <th>Default</th>
                                        <th>Extra</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($column = $tableStructure->fetch_assoc()): ?>
                                        <tr>
                                            <td><strong><?php echo $column['Field']; ?></strong></td>
                                            <td><?php echo $column['Type']; ?></td>
                                            <td><?php echo $column['Null']; ?></td>
                                            <td><?php echo $column['Key']; ?></td>
                                            <td><?php echo $column['Default'] ?? 'NULL'; ?></td>
                                            <td><?php echo $column['Extra']; ?></td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Update Form -->
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-wrench me-2"></i>টেবিল আপডেট করুন</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>এই আপডেট কী করবে:</strong>
                            <ul class="mb-0 mt-2">
                                <li><code>category</code> column যুক্ত করবে (যদি না থাকে)</li>
                                <li><code>description</code> column যুক্ত করবে (যদি না থাকে)</li>
                                <li>বিদ্যমান ডেটা অক্ষত থাকবে</li>
                                <li>নতুন বিষয়ের জন্য category এবং description ফিল্ড ব্যবহার করা যাবে</li>
                            </ul>
                        </div>
                        
                        <form method="POST" onsubmit="return confirm('আপনি কি নিশ্চিত যে টেবিল আপডেট করতে চান?');">
                            <button type="submit" name="update_table" class="btn btn-warning">
                                <i class="fas fa-database me-2"></i>টেবিল আপডেট করুন
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-lightbulb me-2"></i>আপডেটের পরে করণীয়</h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li><strong>বিষয় তালিকা চেক করুন:</strong> subjects.php পেজে গিয়ে দেখুন নতুন column গুলো দেখাচ্ছে কিনা</li>
                            <li><strong>নতুন বিষয় যোগ করুন:</strong> category এবং description ফিল্ড সহ নতুন বিষয় যোগ করে টেস্ট করুন</li>
                            <li><strong>বিদ্যমান বিষয় এডিট করুন:</strong> পুরানো বিষয়গুলোতে সঠিক category সেট করুন</li>
                            <li><strong>CSV আপলোড টেস্ট করুন:</strong> নতুন category detection কাজ করছে কিনা দেখুন</li>
                        </ol>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
