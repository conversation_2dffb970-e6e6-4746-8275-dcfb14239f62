/**
 * Bulk Payment JavaScript
 * Handles the dynamic filtering, searching, and payment processing for the bulk payment modal
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const bulkPaymentModal = document.getElementById('bulkPaymentModal');
    const duesFilterForm = document.getElementById('duesFilterForm');
    const duesListContainer = document.getElementById('duesListContainer');
    const paymentFormSection = document.getElementById('paymentFormSection');
    const selectedDuesCount = document.getElementById('selected_dues_count');
    const totalDuesAmount = document.getElementById('total_dues_amount');
    const submitBulkPayment = document.getElementById('submitBulkPayment');
    const bulkPaymentForm = document.getElementById('bulkPaymentForm');
    
    // Filter elements
    const sessionSelect = document.getElementById('dues_session_id');
    const classSelect = document.getElementById('dues_class_id');
    const departmentSelect = document.getElementById('dues_department_id');
    const studentSearch = document.getElementById('dues_student_search');
    const feeTypeSelect = document.getElementById('dues_fee_type');
    const paymentStatusSelect = document.getElementById('dues_payment_status');
    const fromDateInput = document.getElementById('dues_from_date');
    const toDateInput = document.getElementById('dues_to_date');
    const minAmountInput = document.getElementById('dues_min_amount');
    const maxAmountInput = document.getElementById('dues_max_amount');
    const sortBySelect = document.getElementById('dues_sort_by');
    const perPageSelect = document.getElementById('dues_per_page');
    
    // Advanced filters toggle
    const advancedFiltersToggle = document.querySelector('[data-bs-toggle="collapse"][data-bs-target="#advancedDuesFilters"]');
    const advancedFiltersIcon = document.getElementById('advancedDuesFiltersIcon');
    
    if (advancedFiltersToggle && advancedFiltersIcon) {
        advancedFiltersToggle.addEventListener('click', function() {
            const isCollapsed = advancedFiltersIcon.classList.contains('fa-chevron-down');
            
            if (isCollapsed) {
                advancedFiltersIcon.classList.remove('fa-chevron-down');
                advancedFiltersIcon.classList.add('fa-chevron-up');
            } else {
                advancedFiltersIcon.classList.remove('fa-chevron-up');
                advancedFiltersIcon.classList.add('fa-chevron-down');
            }
        });
    }
    
    // Clear input buttons
    const clearStudentSearchBtn = document.querySelector('[data-target="dues_student_search"]');
    if (clearStudentSearchBtn) {
        clearStudentSearchBtn.addEventListener('click', function() {
            studentSearch.value = '';
        });
    }
    
    // Clear date range button
    const clearDateRangeBtn = document.querySelector('.clear-dues-date-range');
    if (clearDateRangeBtn) {
        clearDateRangeBtn.addEventListener('click', function() {
            fromDateInput.value = '';
            toDateInput.value = '';
        });
    }
    
    // Clear amount range button
    const clearAmountRangeBtn = document.querySelector('.clear-dues-amount-range');
    if (clearAmountRangeBtn) {
        clearAmountRangeBtn.addEventListener('click', function() {
            minAmountInput.value = '';
            maxAmountInput.value = '';
        });
    }
    
    // Reset filter form button
    const resetFilterBtn = document.getElementById('resetDuesFilterForm');
    if (resetFilterBtn) {
        resetFilterBtn.addEventListener('click', function() {
            duesFilterForm.reset();
            loadDuesList();
        });
    }
    
    // Initialize modal
    if (bulkPaymentModal) {
        bulkPaymentModal.addEventListener('shown.bs.modal', function() {
            // Load dues list when modal is shown
            loadDuesList();
        });
    }
    
    // Filter form submission
    if (duesFilterForm) {
        duesFilterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            loadDuesList();
        });
        
        // Add keyboard shortcut for search (Ctrl+Enter)
        duesFilterForm.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                loadDuesList();
            }
        });
    }
    
    // Bulk payment form submission
    if (bulkPaymentForm) {
        bulkPaymentForm.addEventListener('submit', function(e) {
            e.preventDefault();
            processBulkPayment();
        });
    }
    
    /**
     * Load dues list based on filter criteria
     */
    function loadDuesList() {
        // Show loading indicator
        duesListContainer.innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">বকেয়া তথ্য লোড হচ্ছে...</p>
            </div>
        `;
        
        // Hide payment form section
        paymentFormSection.style.display = 'none';
        
        // Get filter values
        const filters = {
            session_id: sessionSelect ? sessionSelect.value : '',
            class_id: classSelect ? classSelect.value : '',
            department_id: departmentSelect ? departmentSelect.value : '',
            student_search: studentSearch ? studentSearch.value : '',
            fee_type: feeTypeSelect ? feeTypeSelect.value : '',
            payment_status: paymentStatusSelect ? paymentStatusSelect.value : 'due',
            from_date: fromDateInput ? fromDateInput.value : '',
            to_date: toDateInput ? toDateInput.value : '',
            min_amount: minAmountInput ? minAmountInput.value : '',
            max_amount: maxAmountInput ? maxAmountInput.value : '',
            sort_by: sortBySelect ? sortBySelect.value : 'due_date_asc',
            per_page: perPageSelect ? perPageSelect.value : '10'
        };
        
        // Fetch dues data
        fetch('ajax/get_dues_list.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(filters)
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Render dues list
                renderDuesList(data.dues);
                
                // Show payment form section if dues are found
                if (data.dues && data.dues.length > 0) {
                    paymentFormSection.style.display = 'block';
                }
            } else {
                // Show error message
                duesListContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i> ${data.message || 'বকেয়া তথ্য লোড করতে সমস্যা হয়েছে'}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading dues:', error);
            duesListContainer.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i> বকেয়া তথ্য লোড করতে সমস্যা হয়েছে
                </div>
            `;
        });
    }
    
    /**
     * Render dues list
     */
    function renderDuesList(dues) {
        if (!dues || dues.length === 0) {
            duesListContainer.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> কোন বকেয়া পাওয়া যায়নি
                </div>
            `;
            return;
        }
        
        // Group dues by student
        const studentGroups = {};
        dues.forEach(due => {
            if (!studentGroups[due.student_id]) {
                studentGroups[due.student_id] = {
                    student_id: due.student_id,
                    student_name: due.student_name,
                    student_roll: due.student_roll,
                    class_name: due.class_name,
                    session_name: due.session_name,
                    department_name: due.department_name,
                    dues: []
                };
            }
            studentGroups[due.student_id].dues.push(due);
        });
        
        // Build HTML
        let html = '';
        
        // Add student groups
        Object.values(studentGroups).forEach(group => {
            html += `
                <div class="card mb-4 student-dues-card" data-student-id="${group.student_id}">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-0">${group.student_name} (${group.student_roll})</h6>
                            <small class="text-muted">${group.class_name || 'N/A'}, ${group.session_name || 'N/A'}, ${group.department_name || 'N/A'}</small>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input select-all-student-dues" type="checkbox" data-student-id="${group.student_id}">
                            <label class="form-check-label">সব নির্বাচন করুন</label>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="50" class="text-center">#</th>
                                        <th>ফি ধরন</th>
                                        <th class="text-end">মোট</th>
                                        <th class="text-end">পরিশোধিত</th>
                                        <th class="text-end">বকেয়া</th>
                                        <th class="text-center">তারিখ</th>
                                        <th class="text-center">স্ট্যাটাস</th>
                                        <th width="100" class="text-center">নির্বাচন</th>
                                    </tr>
                                </thead>
                                <tbody>
            `;
            
            // Add dues for this student
            group.dues.forEach((due, index) => {
                const dueAmount = parseFloat(due.amount) - parseFloat(due.paid);
                const dueDate = new Date(due.due_date);
                const isOverdue = dueDate < new Date() && due.payment_status !== 'paid';
                const statusClass = due.payment_status === 'due' ? 'danger' : (due.payment_status === 'partial' ? 'warning' : 'success');
                const statusText = due.payment_status === 'due' ? 'বকেয়া' : (due.payment_status === 'partial' ? 'আংশিক' : 'পরিশোধিত');
                
                html += `
                    <tr class="${isOverdue ? 'table-danger' : ''}">
                        <td class="text-center">${index + 1}</td>
                        <td>${due.fee_type}</td>
                        <td class="text-end">৳ ${parseFloat(due.amount).toFixed(2)}</td>
                        <td class="text-end">৳ ${parseFloat(due.paid).toFixed(2)}</td>
                        <td class="text-end fw-bold text-danger">৳ ${dueAmount.toFixed(2)}</td>
                        <td class="text-center">${dueDate.toLocaleDateString('en-GB')}</td>
                        <td class="text-center"><span class="badge bg-${statusClass}">${statusText}</span></td>
                        <td class="text-center">
                            <div class="form-check d-flex justify-content-center">
                                <input class="form-check-input due-checkbox" type="checkbox" 
                                    data-fee-id="${due.id}" 
                                    data-student-id="${due.student_id}"
                                    data-amount="${dueAmount.toFixed(2)}">
                            </div>
                        </td>
                    </tr>
                `;
            });
            
            html += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
        });
        
        // Update container
        duesListContainer.innerHTML = html;
        
        // Add event listeners to checkboxes
        addCheckboxEventListeners();
    }
    
    /**
     * Add event listeners to checkboxes
     */
    function addCheckboxEventListeners() {
        // Select all dues for a student
        const selectAllCheckboxes = document.querySelectorAll('.select-all-student-dues');
        selectAllCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const studentId = this.getAttribute('data-student-id');
                const dueCheckboxes = document.querySelectorAll(`.due-checkbox[data-student-id="${studentId}"]`);
                
                dueCheckboxes.forEach(dueCheckbox => {
                    dueCheckbox.checked = this.checked;
                });
                
                updateSelectedDues();
            });
        });
        
        // Individual due checkboxes
        const dueCheckboxes = document.querySelectorAll('.due-checkbox');
        dueCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateSelectedDues();
                
                // Update "select all" checkbox state
                const studentId = this.getAttribute('data-student-id');
                const allStudentDues = document.querySelectorAll(`.due-checkbox[data-student-id="${studentId}"]`);
                const checkedStudentDues = document.querySelectorAll(`.due-checkbox[data-student-id="${studentId}"]:checked`);
                const selectAllCheckbox = document.querySelector(`.select-all-student-dues[data-student-id="${studentId}"]`);
                
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = allStudentDues.length === checkedStudentDues.length;
                }
            });
        });
    }
    
    /**
     * Update selected dues count and total amount
     */
    function updateSelectedDues() {
        const selectedCheckboxes = document.querySelectorAll('.due-checkbox:checked');
        let totalAmount = 0;
        
        selectedCheckboxes.forEach(checkbox => {
            totalAmount += parseFloat(checkbox.getAttribute('data-amount') || 0);
        });
        
        // Update UI
        selectedDuesCount.textContent = selectedCheckboxes.length;
        totalDuesAmount.textContent = totalAmount.toFixed(2);
        
        // Enable/disable submit button
        submitBulkPayment.disabled = selectedCheckboxes.length === 0;
    }
    
    /**
     * Process bulk payment
     */
    function processBulkPayment() {
        // Get selected dues
        const selectedCheckboxes = document.querySelectorAll('.due-checkbox:checked');
        if (selectedCheckboxes.length === 0) {
            alert('কমপক্ষে একটি বকেয়া নির্বাচন করুন!');
            return;
        }
        
        // Get form data
        const paymentDate = document.getElementById('bulk_payment_date').value;
        const paymentMethod = document.getElementById('bulk_payment_method').value;
        const receiptNo = document.getElementById('bulk_receipt_no').value;
        const paymentNotes = document.getElementById('bulk_payment_notes').value;
        
        if (!paymentDate || !paymentMethod) {
            alert('সমস্ত প্রয়োজনীয় ফিল্ড পূরণ করুন!');
            return;
        }
        
        // Prepare payment data
        const feeIds = [];
        const paymentAmounts = [];
        const studentIds = [];
        
        selectedCheckboxes.forEach(checkbox => {
            feeIds.push(checkbox.getAttribute('data-fee-id'));
            paymentAmounts.push(checkbox.getAttribute('data-amount'));
            studentIds.push(checkbox.getAttribute('data-student-id'));
        });
        
        // Disable submit button and show loading
        submitBulkPayment.disabled = true;
        submitBulkPayment.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> প্রক্রিয়াকরণ হচ্ছে...';
        
        // Send payment data to server
        fetch('ajax_handler.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'process_bulk_payment',
                fee_ids: JSON.stringify(feeIds),
                payment_amounts: JSON.stringify(paymentAmounts),
                student_ids: JSON.stringify(studentIds),
                payment_date: paymentDate,
                payment_method: paymentMethod,
                receipt_no: receiptNo,
                payment_note: paymentNotes
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Show success message
                alert('পেমেন্ট সফলভাবে যোগ করা হয়েছে!');
                
                // Close modal
                const bsModal = bootstrap.Modal.getInstance(bulkPaymentModal);
                if (bsModal) {
                    bsModal.hide();
                }
                
                // Reload page to refresh data
                window.location.reload();
            } else {
                // Show error message
                alert('পেমেন্ট যোগ করতে সমস্যা: ' + (data.message || 'অজানা ত্রুটি'));
                
                // Re-enable submit button
                submitBulkPayment.disabled = false;
                submitBulkPayment.innerHTML = '<i class="fas fa-save me-1"></i> পেমেন্ট যোগ করুন';
            }
        })
        .catch(error => {
            console.error('Error processing payment:', error);
            alert('পেমেন্ট যোগ করতে সমস্যা: অজানা ত্রুটি');
            
            // Re-enable submit button
            submitBulkPayment.disabled = false;
            submitBulkPayment.innerHTML = '<i class="fas fa-save me-1"></i> পেমেন্ট যোগ করুন';
        });
    }
});
