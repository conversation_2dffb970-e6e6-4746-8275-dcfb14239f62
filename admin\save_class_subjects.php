<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

require_once '../includes/dbh.inc.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'message' => 'Invalid JSON data']);
    exit();
}

$classId = isset($input['class_id']) ? intval($input['class_id']) : 0;
$departmentId = isset($input['department_id']) ? intval($input['department_id']) : null;
$subjects = isset($input['subjects']) ? $input['subjects'] : [];

if (!$classId) {
    echo json_encode(['success' => false, 'message' => 'Class ID is required']);
    exit();
}

if (empty($subjects)) {
    echo json_encode(['success' => false, 'message' => 'At least one subject is required']);
    exit();
}

try {
    // Create class_subjects table if it doesn't exist
    $createTableQuery = "CREATE TABLE IF NOT EXISTS class_subjects (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        class_id INT(11) NOT NULL,
        department_id INT(11) DEFAULT NULL,
        subject_id INT(11) NOT NULL,
        subject_type ENUM('required', 'optional', 'fourth') DEFAULT 'optional',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_class_subject (class_id, department_id, subject_id),
        FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
        FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
    )";
    $conn->query($createTableQuery);

    // Begin transaction
    $conn->begin_transaction();

    // Delete existing assignments for this class and department
    $deleteQuery = "DELETE FROM class_subjects WHERE class_id = ?";
    $deleteParams = [$classId];
    $deleteTypes = "i";
    
    if ($departmentId) {
        $deleteQuery .= " AND department_id = ?";
        $deleteParams[] = $departmentId;
        $deleteTypes .= "i";
    } else {
        $deleteQuery .= " AND department_id IS NULL";
    }
    
    $deleteStmt = $conn->prepare($deleteQuery);
    $deleteStmt->bind_param($deleteTypes, ...$deleteParams);
    $deleteStmt->execute();

    // Insert new assignments
    $insertQuery = "INSERT INTO class_subjects (class_id, department_id, subject_id, subject_type) VALUES (?, ?, ?, ?)";
    $insertStmt = $conn->prepare($insertQuery);
    
    $successCount = 0;
    foreach ($subjects as $subject) {
        $subjectId = intval($subject['subject_id']);
        $subjectType = $subject['subject_type'];
        
        // Validate subject type
        if (!in_array($subjectType, ['required', 'optional', 'fourth'])) {
            $subjectType = 'optional';
        }
        
        $insertStmt->bind_param("iiis", $classId, $departmentId, $subjectId, $subjectType);
        if ($insertStmt->execute()) {
            $successCount++;
        }
    }

    // Commit transaction
    $conn->commit();

    echo json_encode([
        'success' => true,
        'message' => "Successfully saved $successCount subjects for the class",
        'saved_count' => $successCount
    ]);

} catch (Exception $e) {
    // Rollback transaction on error
    $conn->rollback();
    
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
