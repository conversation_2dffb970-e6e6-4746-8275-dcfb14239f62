<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Include database connection
require_once 'includes/dbh.inc.php';

// Function to log debug information
function logDebug($message) {
    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px; background-color: #f9f9f9;'>";
    echo "<pre>" . htmlspecialchars($message) . "</pre>";
    echo "</div>";
}

// Log PHP version and configuration
logDebug("PHP Version: " . phpversion());
logDebug("Max Execution Time: " . ini_get('max_execution_time'));
logDebug("Memory Limit: " . ini_get('memory_limit'));
logDebug("Post Max Size: " . ini_get('post_max_size'));

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    logDebug("Form submitted via POST");
    
    // Log all POST data
    logDebug("POST Data: " . print_r($_POST, true));
    
    // Get student ID
    $studentId = $_POST['student_id'] ?? '';
    logDebug("Student ID: " . $studentId);
    
    if (empty($studentId)) {
        logDebug("ERROR: Student ID is empty");
        die("Student ID is required");
    }
    
    // Get student data
    $studentQuery = "SELECT s.*, d.department_name
                     FROM students s
                     LEFT JOIN departments d ON s.department_id = d.id
                     WHERE s.student_id = ?";
    $stmt = $conn->prepare($studentQuery);
    $stmt->bind_param("s", $studentId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        logDebug("ERROR: Student not found with ID: " . $studentId);
        die("Student not found");
    }
    
    $student = $result->fetch_assoc();
    $studentDbId = $student['id'];
    logDebug("Student database ID: " . $studentDbId);
    
    // Get current session
    $currentSessionQuery = "SELECT id, session_name FROM sessions ORDER BY id DESC LIMIT 1";
    $currentSessionResult = $conn->query($currentSessionQuery);
    
    if (!$currentSessionResult || $currentSessionResult->num_rows === 0) {
        logDebug("ERROR: No session found");
        die("No session found");
    }
    
    $currentSession = $currentSessionResult->fetch_assoc();
    logDebug("Current session ID: " . $currentSession['id']);
    
    // Validate selection
    $requiredSubjectIds = isset($_POST['required_subjects']) ? $_POST['required_subjects'] : [];
    $optionalSubjectIds = isset($_POST['optional_subjects']) ? $_POST['optional_subjects'] : [];
    $fourthSubjectIds = isset($_POST['fourth_subjects']) ? $_POST['fourth_subjects'] : [];
    
    logDebug("Required subjects: " . print_r($requiredSubjectIds, true));
    logDebug("Optional subjects: " . print_r($optionalSubjectIds, true));
    logDebug("Fourth subjects: " . print_r($fourthSubjectIds, true));
    
    $totalSelected = count($requiredSubjectIds) + count($optionalSubjectIds) + count($fourthSubjectIds);
    logDebug("Total selected subjects: " . $totalSelected);
    
    if ($totalSelected < 1) {
        logDebug("ERROR: No subjects selected");
        die("At least one subject must be selected");
    }
    
    // Check student_subjects table structure
    $tableStructureQuery = "DESCRIBE student_subjects";
    $tableStructure = $conn->query($tableStructureQuery);
    
    if (!$tableStructure) {
        logDebug("ERROR: Could not check student_subjects table structure: " . $conn->error);
        die("Could not check student_subjects table structure: " . $conn->error);
    }
    
    logDebug("student_subjects table structure:");
    $columns = [];
    while ($column = $tableStructure->fetch_assoc()) {
        $columns[$column['Field']] = $column;
        logDebug("  " . $column['Field'] . " - " . $column['Type'] . " - " . $column['Null'] . " - " . $column['Key'] . " - " . $column['Default']);
    }
    
    // Start transaction
    $conn->begin_transaction();
    logDebug("Transaction started");
    
    try {
        // Delete existing selections
        $deleteQuery = "DELETE FROM student_subjects WHERE student_id = ?";
        $stmt = $conn->prepare($deleteQuery);
        
        if (!$stmt) {
            logDebug("ERROR: Failed to prepare delete statement: " . $conn->error);
            throw new Exception("Failed to prepare delete statement: " . $conn->error);
        }
        
        $stmt->bind_param("i", $studentDbId);
        $deleteResult = $stmt->execute();
        
        if (!$deleteResult) {
            logDebug("ERROR: Failed to delete existing selections: " . $stmt->error);
            throw new Exception("Failed to delete existing selections: " . $stmt->error);
        }
        
        logDebug("Deleted existing selections for student ID: " . $studentDbId);
        
        // Insert required subjects
        $insertQuery = "INSERT INTO student_subjects (student_id, subject_id, category, session_id) VALUES (?, ?, ?, ?)";
        $stmt = $conn->prepare($insertQuery);
        
        if (!$stmt) {
            logDebug("ERROR: Failed to prepare insert statement: " . $conn->error);
            throw new Exception("Failed to prepare insert statement: " . $conn->error);
        }
        
        // Insert required subjects
        foreach ($requiredSubjectIds as $subjectId) {
            $category = 'required';
            $stmt->bind_param("iisi", $studentDbId, $subjectId, $category, $currentSession['id']);
            $insertResult = $stmt->execute();
            
            if (!$insertResult) {
                logDebug("ERROR: Failed to insert required subject ID " . $subjectId . ": " . $stmt->error);
                throw new Exception("Failed to insert required subject: " . $stmt->error);
            }
            
            logDebug("Inserted required subject ID: " . $subjectId);
        }
        
        // Insert optional subjects
        foreach ($optionalSubjectIds as $subjectId) {
            $category = 'optional';
            $stmt->bind_param("iisi", $studentDbId, $subjectId, $category, $currentSession['id']);
            $insertResult = $stmt->execute();
            
            if (!$insertResult) {
                logDebug("ERROR: Failed to insert optional subject ID " . $subjectId . ": " . $stmt->error);
                throw new Exception("Failed to insert optional subject: " . $stmt->error);
            }
            
            logDebug("Inserted optional subject ID: " . $subjectId);
        }
        
        // Insert fourth subjects
        foreach ($fourthSubjectIds as $subjectId) {
            $category = 'fourth';
            $stmt->bind_param("iisi", $studentDbId, $subjectId, $category, $currentSession['id']);
            $insertResult = $stmt->execute();
            
            if (!$insertResult) {
                logDebug("ERROR: Failed to insert fourth subject ID " . $subjectId . ": " . $stmt->error);
                throw new Exception("Failed to insert fourth subject: " . $stmt->error);
            }
            
            logDebug("Inserted fourth subject ID: " . $subjectId);
        }
        
        // Commit transaction
        $conn->commit();
        logDebug("Transaction committed successfully");
        
        // Verify the subjects were saved
        $verifyQuery = "SELECT COUNT(*) as count FROM student_subjects WHERE student_id = ?";
        $stmt = $conn->prepare($verifyQuery);
        $stmt->bind_param("i", $studentDbId);
        $stmt->execute();
        $verifyResult = $stmt->get_result()->fetch_assoc();
        
        logDebug("Verification: Found " . $verifyResult['count'] . " subjects for student ID: " . $studentDbId);
        
        if ($verifyResult['count'] != $totalSelected) {
            logDebug("WARNING: Verification count doesn't match selected count");
        } else {
            logDebug("SUCCESS: All subjects saved successfully");
        }
        
        // Show success message
        echo "<div style='border: 1px solid #4CAF50; padding: 10px; margin: 20px 0; background-color: #E8F5E9; color: #2E7D32;'>";
        echo "<h3>Success!</h3>";
        echo "<p>Successfully saved " . $totalSelected . " subjects for student " . $student['first_name'] . " " . $student['last_name'] . " (" . $studentId . ")</p>";
        echo "</div>";
        
    } catch (Exception $e) {
        // Rollback on error
        $conn->rollback();
        logDebug("ERROR: Transaction rolled back: " . $e->getMessage());
        
        // Show error message
        echo "<div style='border: 1px solid #F44336; padding: 10px; margin: 20px 0; background-color: #FFEBEE; color: #C62828;'>";
        echo "<h3>Error!</h3>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "</div>";
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Form Submission</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Debug Form Submission</h1>
        
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Test Form</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="debug_form_submission.php">
                    <div class="mb-3">
                        <label for="student_id" class="form-label">Student ID</label>
                        <input type="text" class="form-control" id="student_id" name="student_id" value="STD-601523" required>
                    </div>
                    
                    <h4>Required Subjects</h4>
                    <div class="mb-3">
                        <?php
                        // Get subjects for the student's department
                        $studentQuery = "SELECT s.*, d.department_name, d.id as dept_id
                                         FROM students s
                                         LEFT JOIN departments d ON s.department_id = d.id
                                         WHERE s.student_id = 'STD-601523'";
                        $studentResult = $conn->query($studentQuery);
                        
                        if ($studentResult && $studentResult->num_rows > 0) {
                            $student = $studentResult->fetch_assoc();
                            $departmentId = $student['dept_id'];
                            
                            // Find the corresponding group ID
                            $groupId = null;
                            
                            // First try to find a group with the same name as the department
                            if ($student['department_name']) {
                                $groupQuery = "SELECT id FROM groups WHERE group_name = ?";
                                $stmt = $conn->prepare($groupQuery);
                                $stmt->bind_param("s", $student['department_name']);
                                $stmt->execute();
                                $groupResult = $stmt->get_result();
                                
                                if ($groupResult->num_rows > 0) {
                                    $groupId = $groupResult->fetch_assoc()['id'];
                                }
                            }
                            
                            // If no group found, use department ID as fallback
                            if (!$groupId) {
                                $groupId = $departmentId;
                            }
                            
                            // Get required subjects
                            $requiredSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code
                                                     FROM subjects s
                                                     JOIN subject_groups sg ON s.id = sg.subject_id
                                                     WHERE sg.group_id = ? AND s.is_active = 1
                                                     AND sg.subject_type = 'required' AND sg.is_applicable = 1
                                                     GROUP BY s.id
                                                     ORDER BY s.subject_name";
                            $stmt = $conn->prepare($requiredSubjectsQuery);
                            $stmt->bind_param("i", $groupId);
                            $stmt->execute();
                            $requiredSubjects = $stmt->get_result();
                            
                            if ($requiredSubjects && $requiredSubjects->num_rows > 0) {
                                while ($subject = $requiredSubjects->fetch_assoc()) {
                                    echo '<div class="form-check">';
                                    echo '<input class="form-check-input" type="checkbox" name="required_subjects[]" value="' . $subject['id'] . '" id="req' . $subject['id'] . '">';
                                    echo '<label class="form-check-label" for="req' . $subject['id'] . '">' . $subject['subject_name'] . ' (' . $subject['subject_code'] . ')</label>';
                                    echo '</div>';
                                }
                            } else {
                                echo '<p>No required subjects found for this student\'s department/group.</p>';
                            }
                            
                            // Get optional subjects
                            echo '<h4 class="mt-4">Optional Subjects</h4>';
                            $optionalSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code
                                                     FROM subjects s
                                                     JOIN subject_groups sg ON s.id = sg.subject_id
                                                     WHERE sg.group_id = ? AND s.is_active = 1
                                                     AND sg.subject_type = 'optional' AND sg.is_applicable = 1
                                                     GROUP BY s.id
                                                     ORDER BY s.subject_name";
                            $stmt = $conn->prepare($optionalSubjectsQuery);
                            $stmt->bind_param("i", $groupId);
                            $stmt->execute();
                            $optionalSubjects = $stmt->get_result();
                            
                            if ($optionalSubjects && $optionalSubjects->num_rows > 0) {
                                while ($subject = $optionalSubjects->fetch_assoc()) {
                                    echo '<div class="form-check">';
                                    echo '<input class="form-check-input" type="checkbox" name="optional_subjects[]" value="' . $subject['id'] . '" id="opt' . $subject['id'] . '">';
                                    echo '<label class="form-check-label" for="opt' . $subject['id'] . '">' . $subject['subject_name'] . ' (' . $subject['subject_code'] . ')</label>';
                                    echo '</div>';
                                }
                            } else {
                                echo '<p>No optional subjects found for this student\'s department/group.</p>';
                            }
                            
                            // Get fourth subjects
                            echo '<h4 class="mt-4">Fourth Subjects</h4>';
                            $fourthSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code
                                                   FROM subjects s
                                                   JOIN subject_groups sg ON s.id = sg.subject_id
                                                   WHERE sg.group_id = ? AND s.is_active = 1
                                                   AND sg.subject_type = 'fourth' AND sg.is_applicable = 1
                                                   GROUP BY s.id
                                                   ORDER BY s.subject_name";
                            $stmt = $conn->prepare($fourthSubjectsQuery);
                            $stmt->bind_param("i", $groupId);
                            $stmt->execute();
                            $fourthSubjects = $stmt->get_result();
                            
                            if ($fourthSubjects && $fourthSubjects->num_rows > 0) {
                                while ($subject = $fourthSubjects->fetch_assoc()) {
                                    echo '<div class="form-check">';
                                    echo '<input class="form-check-input" type="checkbox" name="fourth_subjects[]" value="' . $subject['id'] . '" id="fourth' . $subject['id'] . '">';
                                    echo '<label class="form-check-label" for="fourth' . $subject['id'] . '">' . $subject['subject_name'] . ' (' . $subject['subject_code'] . ')</label>';
                                    echo '</div>';
                                }
                            } else {
                                echo '<p>No fourth subjects found for this student\'s department/group.</p>';
                            }
                        } else {
                            echo '<p>Student not found.</p>';
                        }
                        ?>
                    </div>
                    
                    <button type="submit" name="submit_selection" class="btn btn-primary">Submit</button>
                </form>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">Database Information</h5>
            </div>
            <div class="card-body">
                <?php
                // Check student_subjects table
                $tableExistsQuery = "SHOW TABLES LIKE 'student_subjects'";
                $tableExists = $conn->query($tableExistsQuery)->num_rows > 0;
                
                if ($tableExists) {
                    echo "<p>student_subjects table exists.</p>";
                    
                    // Check for any data in the table
                    $dataQuery = "SELECT COUNT(*) as count FROM student_subjects";
                    $dataResult = $conn->query($dataQuery);
                    
                    if ($dataResult) {
                        $dataCount = $dataResult->fetch_assoc()['count'];
                        echo "<p>Found $dataCount records in the student_subjects table.</p>";
                    }
                    
                    // Check for student's data
                    $studentQuery = "SELECT s.id FROM students s WHERE s.student_id = 'STD-601523'";
                    $studentResult = $conn->query($studentQuery);
                    
                    if ($studentResult && $studentResult->num_rows > 0) {
                        $studentDbId = $studentResult->fetch_assoc()['id'];
                        
                        $studentSubjectsQuery = "SELECT ss.*, s.subject_name, s.subject_code 
                                                FROM student_subjects ss 
                                                JOIN subjects s ON ss.subject_id = s.id 
                                                WHERE ss.student_id = $studentDbId";
                        $studentSubjects = $conn->query($studentSubjectsQuery);
                        
                        if ($studentSubjects && $studentSubjects->num_rows > 0) {
                            echo "<h5>Selected Subjects for STD-601523:</h5>";
                            echo "<table class='table table-bordered'>";
                            echo "<thead><tr><th>Subject ID</th><th>Subject Name</th><th>Subject Code</th><th>Category</th></tr></thead>";
                            echo "<tbody>";
                            
                            while ($subject = $studentSubjects->fetch_assoc()) {
                                echo "<tr>";
                                echo "<td>{$subject['subject_id']}</td>";
                                echo "<td>{$subject['subject_name']}</td>";
                                echo "<td>{$subject['subject_code']}</td>";
                                echo "<td>{$subject['category']}</td>";
                                echo "</tr>";
                            }
                            
                            echo "</tbody></table>";
                        } else {
                            echo "<p>No subjects selected for student STD-601523.</p>";
                        }
                    }
                } else {
                    echo "<p>student_subjects table does not exist.</p>";
                }
                ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
