<?php
session_start();

// Check if user is logged in and is a teacher
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'teacher') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if exam ID is provided
if (!isset($_GET['exam_id']) || empty($_GET['exam_id'])) {
    header("Location: exams.php");
    exit();
}

$examId = intval($_GET['exam_id']);

// Get teacher information with department name
$userId = $_SESSION['userId'];
$username = $_SESSION['username'] ?? '';

// First try to get teacher by user_id
$sql = "SELECT t.*, d.department_name
        FROM teachers t
        LEFT JOIN departments d ON t.department_id = d.id
        WHERE t.user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();

// If no results, try to get by username
if ($result->num_rows === 0 && !empty($username)) {
    $sql = "SELECT t.*, d.department_name
            FROM teachers t
            LEFT JOIN departments d ON t.department_id = d.id
            WHERE t.username = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
}

$teacher = $result->fetch_assoc();

// If still no teacher found, redirect to login
if (!$teacher) {
    header("Location: ../index.php");
    exit();
}

// Create results table if it doesn't exist
$resultsTableQuery = "CREATE TABLE IF NOT EXISTS results (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    exam_id INT(11) NOT NULL,
    student_id INT(11) NOT NULL,
    subject_id INT(11),
    marks_obtained FLOAT NOT NULL,
    total_marks FLOAT NOT NULL,
    grade VARCHAR(10),
    remarks TEXT,
    date DATE NOT NULL,
    created_by INT(11),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";
$conn->query($resultsTableQuery);

// Check if subject_id column exists in results table
$checkSubjectIdColumnQuery = "SHOW COLUMNS FROM results LIKE 'subject_id'";
$subjectIdColumnResult = $conn->query($checkSubjectIdColumnQuery);
if ($subjectIdColumnResult->num_rows == 0) {
    // Add subject_id column if it doesn't exist
    $addSubjectIdColumnQuery = "ALTER TABLE results ADD COLUMN subject_id INT(11) NULL";
    $conn->query($addSubjectIdColumnQuery);
}

// Get exam information
try {
    // Check if subjects table exists
    $checkSubjectsTableQuery = "SHOW TABLES LIKE 'subjects'";
    $subjectsTableResult = $conn->query($checkSubjectsTableQuery);
    $subjectsTableExists = $subjectsTableResult->num_rows > 0;

    // Check if classes table exists
    $checkClassesTableQuery = "SHOW TABLES LIKE 'classes'";
    $classesTableResult = $conn->query($checkClassesTableQuery);
    $classesTableExists = $classesTableResult->num_rows > 0;

    // Build query based on existing tables
    $examQuery = "SELECT e.*";

    if ($subjectsTableExists) {
        $examQuery .= ", s.subject_name, s.subject_code";
    }

    if ($classesTableExists) {
        $examQuery .= ", c.class_name";
    }

    $examQuery .= " FROM exams e";

    if ($subjectsTableExists) {
        $examQuery .= " LEFT JOIN subjects s ON e.subject_id = s.id";
    }

    if ($classesTableExists) {
        $examQuery .= " LEFT JOIN classes c ON e.class_id = c.id";
    }

    $examQuery .= " WHERE e.id = ?";

    $stmt = $conn->prepare($examQuery);
    $stmt->bind_param("i", $examId);
    $stmt->execute();
    $examResult = $stmt->get_result();

    if ($examResult->num_rows === 0) {
        header("Location: exams.php");
        exit();
    }

    $exam = $examResult->fetch_assoc();

    // Check if exam belongs to teacher's department
    if (isset($exam['department_id']) && $exam['department_id'] != $teacher['department_id'] && $_SESSION['userType'] !== 'admin') {
        header("Location: exams.php");
        exit();
    }
} catch (Exception $e) {
    $error_msg = "পরীক্ষার তথ্য লোড করতে সমস্যা হয়েছে: " . $e->getMessage();
    $exam = null;
}

// Get students for this exam
try {
    // Check if students table exists
    $checkStudentsTableQuery = "SHOW TABLES LIKE 'students'";
    $studentsTableResult = $conn->query($checkStudentsTableQuery);

    if ($studentsTableResult->num_rows > 0) {
        // If students table exists, get students for this class/department
        $studentsQuery = "SELECT s.id, s.student_id, s.first_name, s.last_name, s.roll_number, s.class_id, c.class_name
                         FROM students s
                         LEFT JOIN classes c ON s.class_id = c.id
                         WHERE 1=1";

        $params = [];
        $types = "";

        // If exam has class_id, filter by class
        if (!empty($exam['class_id'])) {
            $studentsQuery .= " AND s.class_id = ?";
            $params[] = $exam['class_id'];
            $types .= "i";
        }

        // If exam has department_id, filter by department
        if (!empty($exam['department_id'])) {
            $studentsQuery .= " AND s.department_id = ?";
            $params[] = $exam['department_id'];
            $types .= "i";
        }

        $studentsQuery .= " ORDER BY s.roll_number, s.first_name, s.last_name";

        $stmt = $conn->prepare($studentsQuery);

        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }

        $stmt->execute();
        $students = $stmt->get_result();
    } else {
        $students = null;
    }
} catch (Exception $e) {
    $error_msg = "শিক্ষার্থীদের তথ্য লোড করতে সমস্যা হয়েছে: " . $e->getMessage();
    $students = null;
}

// Handle result submission
$success_msg = '';
$error_msg = '';

if (isset($_POST['submit_results'])) {
    $date = date('Y-m-d');
    $createdBy = $teacher['id'];
    $totalMarks = $exam['total_marks'];
    $subjectId = $exam['subject_id'] ?? null;
    $updateExisting = isset($_POST['update_existing']) && $_POST['update_existing'] == '1';

    // Begin transaction
    $conn->begin_transaction();

    try {
        // If update_existing is checked, delete existing results for this exam
        if ($updateExisting) {
            $deleteQuery = "DELETE FROM results WHERE exam_id = ?";
            $deleteStmt = $conn->prepare($deleteQuery);
            $deleteStmt->bind_param("i", $examId);
            $deleteStmt->execute();
        }

        // Check if all required columns exist in results table
        $requiredColumns = ['exam_id', 'student_id', 'marks_obtained', 'total_marks', 'grade', 'date'];
        $missingColumns = [];

        foreach ($requiredColumns as $column) {
            $checkColumnQuery = "SHOW COLUMNS FROM results LIKE '$column'";
            $columnResult = $conn->query($checkColumnQuery);
            if ($columnResult->num_rows == 0) {
                $missingColumns[] = $column;
                // Add column if it doesn't exist
                $dataType = ($column == 'grade') ? 'VARCHAR(10)' :
                           (($column == 'date') ? 'DATE' :
                           (($column == 'marks_obtained' || $column == 'total_marks') ? 'FLOAT' : 'INT(11)'));
                $nullability = ($column == 'exam_id' || $column == 'student_id' || $column == 'marks_obtained' || $column == 'total_marks' || $column == 'date') ? 'NOT NULL' : 'NULL';
                $addColumnQuery = "ALTER TABLE results ADD COLUMN $column $dataType $nullability";
                $conn->query($addColumnQuery);
            }
        }

        // Check if optional columns exist
        $optionalColumns = ['subject_id', 'remarks', 'created_by'];
        foreach ($optionalColumns as $column) {
            $checkColumnQuery = "SHOW COLUMNS FROM results LIKE '$column'";
            $columnResult = $conn->query($checkColumnQuery);
            if ($columnResult->num_rows == 0) {
                // Add column if it doesn't exist
                $dataType = ($column == 'remarks') ? 'TEXT' : 'INT(11)';
                $addColumnQuery = "ALTER TABLE results ADD COLUMN $column $dataType NULL";
                $conn->query($addColumnQuery);
            }
        }

        // Build dynamic insert query based on available columns
        $columns = ['exam_id', 'student_id', 'marks_obtained', 'total_marks', 'grade', 'date'];
        $placeholders = ['?', '?', '?', '?', '?', '?'];
        $bindTypes = 'iiddss'; // i=integer, d=double, s=string
        $bindParams = [$examId, 0, 0.0, $totalMarks, '', $date]; // Will update student_id and marks_obtained in loop

        // Check if subject_id column exists and add it to query if it does
        $checkSubjectIdQuery = "SHOW COLUMNS FROM results LIKE 'subject_id'";
        $subjectIdResult = $conn->query($checkSubjectIdQuery);
        if ($subjectIdResult->num_rows > 0 && $subjectId !== null) {
            $columns[] = 'subject_id';
            $placeholders[] = '?';
            $bindTypes .= 'i';
            $bindParams[] = $subjectId;
        }

        // Check if remarks column exists
        $checkRemarksQuery = "SHOW COLUMNS FROM results LIKE 'remarks'";
        $remarksResult = $conn->query($checkRemarksQuery);
        $remarksExists = $remarksResult->num_rows > 0;

        // Check if created_by column exists
        $checkCreatedByQuery = "SHOW COLUMNS FROM results LIKE 'created_by'";
        $createdByResult = $conn->query($checkCreatedByQuery);
        if ($createdByResult->num_rows > 0) {
            $columns[] = 'created_by';
            $placeholders[] = '?';
            $bindTypes .= 'i';
            $bindParams[] = $createdBy;
        }

        $insertQuery = "INSERT INTO results (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $placeholders) . ")";
        $stmt = $conn->prepare($insertQuery);

        $success = true;
        $insertCount = 0;

        foreach ($_POST['marks'] as $studentId => $marks) {
            $marksObtained = floatval($marks);

            // Calculate grade based on marks percentage
            $percentage = ($marksObtained / $totalMarks) * 100;
            $grade = '';

            if ($percentage >= 80) {
                $grade = 'A+';
            } elseif ($percentage >= 70) {
                $grade = 'A';
            } elseif ($percentage >= 60) {
                $grade = 'A-';
            } elseif ($percentage >= 50) {
                $grade = 'B';
            } elseif ($percentage >= 40) {
                $grade = 'C';
            } elseif ($percentage >= 33) {
                $grade = 'D';
            } else {
                $grade = 'F';
            }

            // Update bind parameters with current values
            $bindParams[1] = $studentId; // student_id
            $bindParams[2] = $marksObtained; // marks_obtained
            $bindParams[4] = $grade; // grade

            // If remarks column exists, add it to the query
            if ($remarksExists) {
                $remarks = $_POST['remarks'][$studentId] ?? '';
                // Find the position to insert remarks in the query
                $remarksIndex = array_search('remarks', $columns);
                if ($remarksIndex !== false) {
                    $bindParams[$remarksIndex] = $remarks;
                } else {
                    // Add remarks to the query if not already included
                    $columns[] = 'remarks';
                    $placeholders[] = '?';
                    $bindTypes .= 's';
                    $bindParams[] = $remarks;

                    // Rebuild the query with remarks
                    $insertQuery = "INSERT INTO results (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $placeholders) . ")";
                    $stmt = $conn->prepare($insertQuery);
                }
            }

            // Bind parameters dynamically
            $stmt->bind_param($bindTypes, ...$bindParams);

            if ($stmt->execute()) {
                $insertCount++;
            } else {
                $success = false;
                break;
            }
        }

        if ($success) {
            $conn->commit();
            $success_msg = "$insertCount জন শিক্ষার্থীর ফলাফল সফলভাবে যোগ করা হয়েছে";
        } else {
            $conn->rollback();
            $error_msg = "ফলাফল যোগ করতে সমস্যা হয়েছে: " . $conn->error;
        }
    } catch (Exception $e) {
        $conn->rollback();
        $error_msg = "ফলাফল যোগ করতে সমস্যা হয়েছে: " . $e->getMessage();
    }
}

// Check if results already exist for this exam and get existing results
$resultsExist = false;
$existingResults = [];
try {
    $checkResultsQuery = "SELECT r.*, s.student_id as student_code, s.first_name, s.last_name
                         FROM results r
                         JOIN students s ON r.student_id = s.id
                         WHERE r.exam_id = ?";
    $stmt = $conn->prepare($checkResultsQuery);
    $stmt->bind_param("i", $examId);
    $stmt->execute();
    $checkResult = $stmt->get_result();

    if ($checkResult->num_rows > 0) {
        $resultsExist = true;
        while ($row = $checkResult->fetch_assoc()) {
            $existingResults[$row['student_id']] = $row;
        }
    }
} catch (Exception $e) {
    // Ignore error
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include __DIR__ . '/includes/global-head.php'; ?>
    <title>Add Results - Teacher Panel</title>

    <style>
        body, html, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, select, textarea, label, li, th, td {
            font-family: 'Hind Siliguri', sans-serif;
        }

        .table-responsive {
            overflow-x: auto;
        }

        .marks-input {
            width: 80px;
        }

        .remarks-input {
            width: 150px;
        }

        .exam-info {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .exam-info h4 {
            margin-bottom: 15px;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>Teacher Panel</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user me-2"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> Students
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> Courses
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> Exams
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> Results
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">
                            <i class="fas fa-calendar-check me-2"></i> Attendance
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2>ফলাফল যোগ করুন</h2>
                            <a href="exams.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i> পরীক্ষা তালিকায় ফিরে যান
                            </a>
                        </div>

                        <?php if (!empty($success_msg)): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <?php echo $success_msg; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($error_msg)): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <?php echo $error_msg; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($exam) && $exam): ?>
                            <div class="exam-info">
                                <h4>পরীক্ষার তথ্য</h4>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>পরীক্ষার নাম:</strong> <?php echo htmlspecialchars($exam['exam_name']); ?></p>
                                        <p><strong>পরীক্ষার ধরন:</strong>
                                            <?php
                                                $examTypeText = '';
                                                switch ($exam['exam_type']) {
                                                    case 'midterm':
                                                        $examTypeText = 'মিডটার্ম';
                                                        break;
                                                    case 'final':
                                                        $examTypeText = 'ফাইনাল';
                                                        break;
                                                    case 'quiz':
                                                        $examTypeText = 'কুইজ';
                                                        break;
                                                    case 'assignment':
                                                        $examTypeText = 'অ্যাসাইনমেন্ট';
                                                        break;
                                                    case 'practical':
                                                        $examTypeText = 'প্র্যাকটিক্যাল';
                                                        break;
                                                    default:
                                                        $examTypeText = 'অন্যান্য';
                                                }
                                                echo $examTypeText;
                                            ?>
                                        </p>
                                        <p><strong>বিষয়:</strong> <?php echo isset($exam['subject_name']) ? htmlspecialchars($exam['subject_name'] . ' (' . $exam['subject_code'] . ')') : 'N/A'; ?></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>শ্রেণী:</strong> <?php echo isset($exam['class_name']) ? htmlspecialchars($exam['class_name']) : 'N/A'; ?></p>
                                        <p><strong>তারিখ:</strong> <?php echo date('d M Y', strtotime($exam['exam_date'])); ?></p>
                                        <p><strong>মোট নম্বর:</strong> <?php echo $exam['total_marks']; ?></p>
                                    </div>
                                </div>
                            </div>

                            <?php if ($resultsExist): ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i> এই পরীক্ষার ফলাফল ইতিমধ্যে যোগ করা হয়েছে।
                                    <div class="form-check mt-2">
                                        <input class="form-check-input" type="checkbox" id="update_existing" name="update_existing" value="1">
                                        <label class="form-check-label" for="update_existing">
                                            পূর্বের ফলাফল আপডেট করুন (সতর্কতা: এটি পূর্বের সকল ফলাফল মুছে ফেলবে)
                                        </label>
                                    </div>
                                </div>

                                <?php if (count($existingResults) > 0): ?>
                                <div class="card mb-3">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="card-title mb-0">বিদ্যমান ফলাফল</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-sm table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>আইডি</th>
                                                        <th>নাম</th>
                                                        <th>প্রাপ্ত নম্বর</th>
                                                        <th>গ্রেড</th>
                                                        <th>অ্যাকশন</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($existingResults as $studentId => $result): ?>
                                                        <tr>
                                                            <td><?php echo htmlspecialchars($result['student_code']); ?></td>
                                                            <td><?php echo htmlspecialchars($result['first_name'] . ' ' . $result['last_name']); ?></td>
                                                            <td><?php echo $result['marks_obtained']; ?></td>
                                                            <td><?php echo $result['grade']; ?></td>
                                                            <td>
                                                                <a href="edit_result.php?id=<?php echo $result['id']; ?>" class="btn btn-sm btn-warning" target="_blank">
                                                                    <i class="fas fa-edit"></i> এডিট
                                                                </a>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php if (isset($students) && $students && $students->num_rows > 0): ?>
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="card-title mb-0">শিক্ষার্থীদের ফলাফল যোগ করুন</h5>
                                    </div>
                                    <div class="card-body">
                                        <form action="" method="post">
                                            <div class="table-responsive">
                                                <table class="table table-hover">
                                                    <thead>
                                                        <tr>
                                                            <th>রোল</th>
                                                            <th>আইডি</th>
                                                            <th>নাম</th>
                                                            <th>শ্রেণী</th>
                                                            <th>প্রাপ্ত নম্বর (<?php echo $exam['total_marks']; ?>)</th>
                                                            <th>মন্তব্য</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php while ($student = $students->fetch_assoc()): ?>
                                                            <tr>
                                                                <td><?php echo htmlspecialchars($student['roll_number'] ?? 'N/A'); ?></td>
                                                                <td><?php echo htmlspecialchars($student['student_id']); ?></td>
                                                                <td><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></td>
                                                                <td><?php echo htmlspecialchars($student['class_name'] ?? 'N/A'); ?></td>
                                                                <td>
                                                                    <input type="number" class="form-control marks-input" name="marks[<?php echo $student['id']; ?>]" min="0" max="<?php echo $exam['total_marks']; ?>" step="0.01" required>
                                                                </td>
                                                                <td>
                                                                    <input type="text" class="form-control remarks-input" name="remarks[<?php echo $student['id']; ?>]" placeholder="মন্তব্য (ঐচ্ছিক)">
                                                                </td>
                                                            </tr>
                                                        <?php endwhile; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <button type="submit" name="submit_results" class="btn btn-primary mt-3">
                                                <i class="fas fa-save me-2"></i> ফলাফল সংরক্ষণ করুন
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i> এই পরীক্ষার জন্য কোন শিক্ষার্থী খুঁজে পাওয়া যায়নি। শিক্ষার্থীদের শ্রেণী/বিভাগ নিশ্চিত করুন।
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i> পরীক্ষার তথ্য লোড করতে সমস্যা হয়েছে। অনুগ্রহ করে আবার চেষ্টা করুন।
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const totalMarks = <?php echo isset($exam) ? $exam['total_marks'] : 0; ?>;

            if (form) {
                form.addEventListener('submit', function(e) {
                    const marksInputs = document.querySelectorAll('.marks-input');
                    let isValid = true;

                    marksInputs.forEach(function(input) {
                        const value = parseFloat(input.value);

                        if (isNaN(value) || value < 0 || value > totalMarks) {
                            isValid = false;
                            input.classList.add('is-invalid');
                        } else {
                            input.classList.remove('is-invalid');
                        }
                    });

                    if (!isValid) {
                        e.preventDefault();
                        alert('অনুগ্রহ করে সঠিক নম্বর দিন। নম্বর 0 থেকে ' + totalMarks + ' এর মধ্যে হতে হবে।');
                    }
                });

                // Auto-calculate percentage and validate on input
                const marksInputs = document.querySelectorAll('.marks-input');
                marksInputs.forEach(function(input) {
                    input.addEventListener('input', function() {
                        const value = parseFloat(this.value);

                        if (isNaN(value) || value < 0 || value > totalMarks) {
                            this.classList.add('is-invalid');
                        } else {
                            this.classList.remove('is-invalid');
                        }
                    });
                });
            }
        });
    </script>
</body>
</html>
