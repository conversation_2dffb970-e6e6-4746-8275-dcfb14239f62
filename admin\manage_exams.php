<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if necessary tables exist
$tables = ['exams', 'subjects', 'departments'];
$missingTables = [];

foreach ($tables as $table) {
    $tableCheck = $conn->query("SHOW TABLES LIKE '$table'");
    if ($tableCheck->num_rows == 0) {
        $missingTables[] = $table;
    }
}

// Create departments table if it doesn't exist
$departmentsTableCheck = $conn->query("SHOW TABLES LIKE 'departments'");
if ($departmentsTableCheck->num_rows == 0) {
    $createDepartmentsTable = "CREATE TABLE IF NOT EXISTS departments (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        department_name VARCHAR(100) NOT NULL,
        description TEXT,
        is_active TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $conn->query($createDepartmentsTable);

    // Add sample departments
    $sampleDepartments = [
        'বিজ্ঞান বিভাগ',
        'মানবিক বিভাগ',
        'বাণিজ্য বিভাগ'
    ];

    foreach ($sampleDepartments as $dept) {
        $conn->query("INSERT INTO departments (department_name) VALUES ('$dept')");
    }
}

// Create subjects table if it doesn't exist
$subjectsTableCheck = $conn->query("SHOW TABLES LIKE 'subjects'");
if ($subjectsTableCheck->num_rows == 0) {
    $createSubjectsTable = "CREATE TABLE IF NOT EXISTS subjects (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        subject_name VARCHAR(100) NOT NULL,
        subject_code VARCHAR(20),
        description TEXT,
        is_active TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $conn->query($createSubjectsTable);

    // Add sample subjects
    $sampleSubjects = [
        'বাংলা',
        'ইংরেজি',
        'গণিত',
        'বিজ্ঞান',
        'সমাজ বিজ্ঞান',
        'ধর্ম শিক্ষা',
        'তথ্য ও যোগাযোগ প্রযুক্তি'
    ];

    foreach ($sampleSubjects as $subj) {
        $conn->query("INSERT INTO subjects (subject_name) VALUES ('$subj')");
    }
}

if (!empty($missingTables)) {
    header("Location: result_management.php");
    exit();
}

// Handle form submissions
$successMessage = '';
$errorMessage = '';

// Handle exam deletion
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $examId = intval($_GET['delete']);

    // Check if this exam has any results
    $checkResultsQuery = "SELECT COUNT(*) as count FROM results WHERE exam_id = ?";
    $stmt = $conn->prepare($checkResultsQuery);
    $stmt->bind_param("i", $examId);
    $stmt->execute();
    $resultsCount = $stmt->get_result()->fetch_assoc()['count'];

    if ($resultsCount > 0) {
        $errorMessage = "এই পরীক্ষার $resultsCount টি ফলাফল রয়েছে। আগে সেগুলি মুছুন।";
    } else {
        // Delete the exam
        $deleteQuery = "DELETE FROM exams WHERE id = ?";
        $stmt = $conn->prepare($deleteQuery);
        $stmt->bind_param("i", $examId);

        if ($stmt->execute()) {
            $successMessage = "পরীক্ষা সফলভাবে মুছে ফেলা হয়েছে!";
        } else {
            $errorMessage = "পরীক্ষা মুছতে সমস্যা হয়েছে: " . $conn->error;
        }
    }
}

// Get filter values
$classFilter = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;
$typeFilter = isset($_GET['exam_type']) ? $_GET['exam_type'] : '';
$subjectFilter = isset($_GET['subject_id']) ? intval($_GET['subject_id']) : 0;
$departmentFilter = isset($_GET['department_id']) ? intval($_GET['department_id']) : 0;
$searchFilter = isset($_GET['search']) ? $_GET['search'] : '';

// Build query with filters
$examsQuery = "SELECT e.*, c.class_name, s.subject_name, d.department_name
              FROM exams e
              LEFT JOIN classes c ON e.class_id = c.id
              LEFT JOIN subjects s ON e.subject_id = s.id
              LEFT JOIN departments d ON e.department_id = d.id
              WHERE 1=1";

$queryParams = [];
$paramTypes = "";

if ($classFilter > 0) {
    $examsQuery .= " AND e.class_id = ?";
    $queryParams[] = $classFilter;
    $paramTypes .= "i";
}

if (!empty($typeFilter)) {
    $examsQuery .= " AND e.exam_type = ?";
    $queryParams[] = $typeFilter;
    $paramTypes .= "s";
}

if ($subjectFilter > 0) {
    $examsQuery .= " AND e.subject_id = ?";
    $queryParams[] = $subjectFilter;
    $paramTypes .= "i";
}

if ($departmentFilter > 0) {
    $examsQuery .= " AND e.department_id = ?";
    $queryParams[] = $departmentFilter;
    $paramTypes .= "i";
}

if (!empty($searchFilter)) {
    $examsQuery .= " AND (e.exam_name LIKE ? OR c.class_name LIKE ? OR s.subject_name LIKE ? OR d.department_name LIKE ?)";
    $searchTerm = "%$searchFilter%";
    $queryParams[] = $searchTerm;
    $queryParams[] = $searchTerm;
    $queryParams[] = $searchTerm;
    $queryParams[] = $searchTerm;
    $paramTypes .= "ssss";
}

$examsQuery .= " ORDER BY e.exam_date DESC";

$stmt = $conn->prepare($examsQuery);

if (!empty($queryParams)) {
    $stmt->bind_param($paramTypes, ...$queryParams);
}

$stmt->execute();
$exams = $stmt->get_result();

// Get all classes for filter
$classesQuery = "SELECT id, class_name FROM classes ORDER BY class_name";
$classes = $conn->query($classesQuery);

// Get all subjects for filter
$subjectsQuery = "SELECT id, subject_name FROM subjects ORDER BY subject_name";
$subjects = $conn->query($subjectsQuery);

// Get all departments for filter
$departmentsQuery = "SELECT id, department_name FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Get all exam types for filter
$examTypesQuery = "SELECT DISTINCT exam_type FROM exams WHERE exam_type IS NOT NULL AND exam_type != '' ORDER BY exam_type";
$examTypes = $conn->query($examTypesQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>পরীক্ষা ব্যবস্থাপনা - স্কুল ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>পরীক্ষা ব্যবস্থাপনা</h2>
                        <p class="text-muted">সকল পরীক্ষার তালিকা দেখুন, সম্পাদনা করুন এবং মুছুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="create_exam.php" class="btn btn-primary">
                            <i class="fas fa-plus-circle me-2"></i>নতুন পরীক্ষা যোগ করুন
                        </a>
                    </div>
                </div>

                <?php if ($successMessage): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo $successMessage; ?>
                    </div>
                <?php endif; ?>

                <?php if ($errorMessage): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $errorMessage; ?>
                    </div>
                <?php endif; ?>

                <!-- Filter Section -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">ফিল্টার</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="" class="row g-3">
                            <div class="col-md-3 mb-3">
                                <label for="class_id" class="form-label">শ্রেণী</label>
                                <select class="form-select" id="class_id" name="class_id">
                                    <option value="0">সকল শ্রেণী</option>
                                    <?php if ($classes && $classes->num_rows > 0): ?>
                                        <?php while ($class = $classes->fetch_assoc()): ?>
                                            <option value="<?php echo $class['id']; ?>" <?php echo ($classFilter == $class['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($class['class_name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="exam_type" class="form-label">পরীক্ষার ধরন</label>
                                <select class="form-select" id="exam_type" name="exam_type">
                                    <option value="">সকল ধরন</option>
                                    <?php if ($examTypes && $examTypes->num_rows > 0): ?>
                                        <?php while ($type = $examTypes->fetch_assoc()): ?>
                                            <option value="<?php echo htmlspecialchars($type['exam_type']); ?>" <?php echo ($typeFilter == $type['exam_type']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($type['exam_type']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="subject_id" class="form-label">বিষয়</label>
                                <select class="form-select" id="subject_id" name="subject_id">
                                    <option value="0">সকল বিষয়</option>
                                    <?php if ($subjects && $subjects->num_rows > 0): ?>
                                        <?php while ($subject = $subjects->fetch_assoc()): ?>
                                            <option value="<?php echo $subject['id']; ?>" <?php echo ($subjectFilter == $subject['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($subject['subject_name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="department_id" class="form-label">বিভাগ</label>
                                <select class="form-select" id="department_id" name="department_id">
                                    <option value="0">সকল বিভাগ</option>
                                    <?php if ($departments && $departments->num_rows > 0): ?>
                                        <?php while ($department = $departments->fetch_assoc()): ?>
                                            <option value="<?php echo $department['id']; ?>" <?php echo ($departmentFilter == $department['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($department['department_name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                            <div class="col-md-9 mb-3">
                                <label for="search" class="form-label">অনুসন্ধান</label>
                                <input type="text" class="form-control" id="search" name="search" value="<?php echo htmlspecialchars($searchFilter); ?>" placeholder="পরীক্ষার নাম, শ্রেণী, বিষয়, বিভাগ...">
                            </div>
                            <div class="col-md-3 mb-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search me-1"></i> খুঁজুন
                                </button>
                                <a href="manage_exams.php" class="btn btn-secondary">
                                    <i class="fas fa-redo me-1"></i> রিসেট
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Exams Table -->
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">পরীক্ষা তালিকা</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>পরীক্ষার নাম</th>
                                        <th>ধরন</th>
                                        <th>শ্রেণী</th>
                                        <th>বিষয়</th>
                                        <th>বিভাগ</th>
                                        <th>তারিখ</th>
                                        <th>সময়</th>
                                        <th>মোট নম্বর</th>
                                        <th>পাস নম্বর</th>
                                        <th>একশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($exams && $exams->num_rows > 0): ?>
                                        <?php while ($exam = $exams->fetch_assoc()):
                                            $isUpcoming = strtotime($exam['exam_date']) >= strtotime(date('Y-m-d'));
                                        ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($exam['exam_name']); ?></td>
                                                <td><?php echo htmlspecialchars($exam['exam_type'] ?? 'N/A'); ?></td>
                                                <td><?php echo htmlspecialchars($exam['class_name'] ?? 'N/A'); ?></td>
                                                <td>
                                                    <?php
                                                    // Check if we need to show "All Subjects"
                                                    if (!isset($exam['subject_id']) || $exam['subject_id'] === null) {
                                                        echo "সকল বিষয়";
                                                    } else {
                                                        echo htmlspecialchars($exam['subject_name'] ?? 'N/A');
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    // Check if we need to show "All Departments"
                                                    if (!isset($exam['department_id']) || $exam['department_id'] === null) {
                                                        echo "সকল বিভাগ";
                                                    } else {
                                                        echo htmlspecialchars($exam['department_name'] ?? 'N/A');
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <span class="badge <?php echo $isUpcoming ? 'bg-success' : 'bg-secondary'; ?>">
                                                        <?php echo date('d/m/Y', strtotime($exam['exam_date'])); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php
                                                    if (!empty($exam['start_time']) && !empty($exam['end_time'])) {
                                                        echo date('h:i A', strtotime($exam['start_time'])) . ' - ' . date('h:i A', strtotime($exam['end_time']));
                                                    } else {
                                                        echo 'N/A';
                                                    }
                                                    ?>
                                                </td>
                                                <td><?php echo $exam['total_marks']; ?></td>
                                                <td><?php echo $exam['passing_marks'] ?? 'N/A'; ?></td>
                                                <td>
                                                    <a href="edit_exam.php?id=<?php echo $exam['id']; ?>" class="btn btn-sm btn-warning">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="manage_exams.php?delete=<?php echo $exam['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই পরীক্ষা মুছে ফেলতে চান?')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                    <a href="result_management.php?exam_id=<?php echo $exam['id']; ?>" class="btn btn-sm btn-success">
                                                        <i class="fas fa-plus-circle"></i> মার্কস এন্ট্রি
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="10" class="text-center">কোন পরীক্ষা খুঁজে পাওয়া যায়নি</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
