<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Initialize variables
$department_id = isset($_GET['department_id']) ? $_GET['department_id'] : '';
$report_type = isset($_GET['report_type']) ? $_GET['report_type'] : 'all_subjects';
$session_id = isset($_GET['session_id']) ? $_GET['session_id'] : '';

// Get departments
$departmentsQuery = "SELECT id, department_name FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Get sessions
$sessionsQuery = "SELECT id, session_name FROM sessions ORDER BY session_name DESC";
$sessions = $conn->query($sessionsQuery);

// Build the base query for subjects
$queryBase = "SELECT s.id, s.subject_name, s.subject_code, s.description, s.is_active, 
              d.department_name, COUNT(ts.id) as teacher_count 
              FROM subjects s
              LEFT JOIN departments d ON s.department_id = d.id
              LEFT JOIN teacher_subjects ts ON s.id = ts.subject_id";

$queryWhere = " WHERE 1=1";
$queryGroup = " GROUP BY s.id";
$queryOrder = " ORDER BY d.department_name, s.subject_name";

// Add filters based on selected options
if (!empty($department_id)) {
    $queryWhere .= " AND s.department_id = $department_id";
}

if (!empty($session_id) && $report_type == 'assigned_subjects') {
    $queryWhere .= " AND ts.session_id = $session_id";
}

// Add specific conditions based on report type
switch ($report_type) {
    case 'active_subjects':
        $queryWhere .= " AND s.is_active = 1";
        break;
    case 'inactive_subjects':
        $queryWhere .= " AND s.is_active = 0";
        break;
    case 'assigned_subjects':
        $queryWhere .= " AND ts.id IS NOT NULL";
        break;
    case 'unassigned_subjects':
        $queryWhere .= " AND ts.id IS NULL";
        break;
}

// Combine the query parts
$query = $queryBase . $queryWhere . $queryGroup . $queryOrder;

// Execute the query
$subjects = $conn->query($query);

// Get summary statistics
$totalSubjectsQuery = "SELECT COUNT(*) as total FROM subjects";
$totalSubjectsResult = $conn->query($totalSubjectsQuery);
$totalSubjects = $totalSubjectsResult->fetch_assoc()['total'];

$activeSubjectsQuery = "SELECT COUNT(*) as active FROM subjects WHERE is_active = 1";
$activeSubjectsResult = $conn->query($activeSubjectsQuery);
$activeSubjects = $activeSubjectsResult->fetch_assoc()['active'];

$inactiveSubjectsQuery = "SELECT COUNT(*) as inactive FROM subjects WHERE is_active = 0";
$inactiveSubjectsResult = $conn->query($inactiveSubjectsQuery);
$inactiveSubjects = $inactiveSubjectsResult->fetch_assoc()['inactive'];

$assignedSubjectsQuery = "SELECT COUNT(DISTINCT subject_id) as assigned FROM teacher_subjects";
$assignedSubjectsResult = $conn->query($assignedSubjectsQuery);
$assignedSubjects = $assignedSubjectsResult->fetch_assoc()['assigned'];

$unassignedSubjectsQuery = "SELECT COUNT(*) as unassigned FROM subjects WHERE id NOT IN (SELECT DISTINCT subject_id FROM teacher_subjects)";
$unassignedSubjectsResult = $conn->query($unassignedSubjectsQuery);
$unassignedSubjects = $unassignedSubjectsResult->fetch_assoc()['unassigned'];
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>বিষয় রিপোর্ট - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> কোর্স
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="subject_dashboard.php">
                            <i class="fas fa-book-open me-2"></i> বিষয় ব্যবস্থাপনা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sessions.php">
                            <i class="fas fa-calendar-alt me-2"></i> সেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-file-pdf me-2"></i> রিপোর্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>বিষয় রিপোর্ট</h2>
                        <p class="text-muted">বিষয় সংক্রান্ত রিপোর্ট তৈরি করুন এবং দেখুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="subject_dashboard.php" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>বিষয় ড্যাশবোর্ডে ফিরে যান
                        </a>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-4 col-xl-2 mb-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="text-muted">মোট বিষয়</h5>
                                <h3 class="display-5"><?php echo $totalSubjects; ?></h3>
                            </div>
                            <div class="card-footer bg-primary text-white">
                                <a href="subject_report.php?report_type=all_subjects" class="text-white text-decoration-none">সকল বিষয় দেখুন</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 col-xl-2 mb-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="text-muted">সক্রিয় বিষয়</h5>
                                <h3 class="display-5"><?php echo $activeSubjects; ?></h3>
                            </div>
                            <div class="card-footer bg-success text-white">
                                <a href="subject_report.php?report_type=active_subjects" class="text-white text-decoration-none">সক্রিয় বিষয় দেখুন</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 col-xl-2 mb-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="text-muted">নিষ্ক্রিয় বিষয়</h5>
                                <h3 class="display-5"><?php echo $inactiveSubjects; ?></h3>
                            </div>
                            <div class="card-footer bg-warning text-dark">
                                <a href="subject_report.php?report_type=inactive_subjects" class="text-dark text-decoration-none">নিষ্ক্রিয় বিষয় দেখুন</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 col-xl-3 mb-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="text-muted">বরাদ্দকৃত বিষয়</h5>
                                <h3 class="display-5"><?php echo $assignedSubjects; ?></h3>
                            </div>
                            <div class="card-footer bg-info text-white">
                                <a href="subject_report.php?report_type=assigned_subjects" class="text-white text-decoration-none">বরাদ্দকৃত বিষয় দেখুন</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 col-xl-3 mb-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="text-muted">অবরাদ্দকৃত বিষয়</h5>
                                <h3 class="display-5"><?php echo $unassignedSubjects; ?></h3>
                            </div>
                            <div class="card-footer bg-secondary text-white">
                                <a href="subject_report.php?report_type=unassigned_subjects" class="text-white text-decoration-none">অবরাদ্দকৃত বিষয় দেখুন</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Report Filters -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">রিপোর্ট ফিল্টার</h5>
                    </div>
                    <div class="card-body">
                        <form action="subject_report.php" method="GET" class="row">
                            <div class="col-md-4 mb-3">
                                <label for="report_type" class="form-label">রিপোর্টের ধরন</label>
                                <select class="form-select" id="report_type" name="report_type">
                                    <option value="all_subjects" <?php echo $report_type == 'all_subjects' ? 'selected' : ''; ?>>সকল বিষয়</option>
                                    <option value="active_subjects" <?php echo $report_type == 'active_subjects' ? 'selected' : ''; ?>>সক্রিয় বিষয়</option>
                                    <option value="inactive_subjects" <?php echo $report_type == 'inactive_subjects' ? 'selected' : ''; ?>>নিষ্ক্রিয় বিষয়</option>
                                    <option value="assigned_subjects" <?php echo $report_type == 'assigned_subjects' ? 'selected' : ''; ?>>বরাদ্দকৃত বিষয়</option>
                                    <option value="unassigned_subjects" <?php echo $report_type == 'unassigned_subjects' ? 'selected' : ''; ?>>অবরাদ্দকৃত বিষয়</option>
                                </select>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="department_id" class="form-label">বিভাগ</label>
                                <select class="form-select" id="department_id" name="department_id">
                                    <option value="">সকল বিভাগ</option>
                                    <?php if ($departments && $departments->num_rows > 0): ?>
                                        <?php while ($department = $departments->fetch_assoc()): ?>
                                            <option value="<?php echo $department['id']; ?>" <?php echo $department_id == $department['id'] ? 'selected' : ''; ?>>
                                                <?php echo $department['department_name']; ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3 mb-3" id="session_filter" <?php echo $report_type != 'assigned_subjects' ? 'style="display:none;"' : ''; ?>>
                                <label for="session_id" class="form-label">সেশন</label>
                                <select class="form-select" id="session_id" name="session_id">
                                    <option value="">সকল সেশন</option>
                                    <?php if ($sessions && $sessions->num_rows > 0): ?>
                                        <?php while ($session = $sessions->fetch_assoc()): ?>
                                            <option value="<?php echo $session['id']; ?>" <?php echo $session_id == $session['id'] ? 'selected' : ''; ?>>
                                                <?php echo $session['session_name']; ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-2 align-self-end mb-3">
                                <button type="submit" class="btn btn-primary w-100">ফিল্টার</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Report Results -->
                <div class="card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <?php 
                            switch ($report_type) {
                                case 'active_subjects':
                                    echo 'সক্রিয় বিষয়ের তালিকা';
                                    break;
                                case 'inactive_subjects':
                                    echo 'নিষ্ক্রিয় বিষয়ের তালিকা';
                                    break;
                                case 'assigned_subjects':
                                    echo 'বরাদ্দকৃত বিষয়ের তালিকা';
                                    break;
                                case 'unassigned_subjects':
                                    echo 'অবরাদ্দকৃত বিষয়ের তালিকা';
                                    break;
                                default:
                                    echo 'সকল বিষয়ের তালিকা';
                            }
                            ?>
                        </h5>
                        <div>
                            <div class="dropdown d-inline-block">
                                <button class="btn btn-sm btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-file-export me-1"></i>এক্সপোর্ট
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="subject_export.php?format=pdf&report_type=<?php echo $report_type; ?>&department_id=<?php echo $department_id; ?>&session_id=<?php echo $session_id; ?>">PDF ফরম্যাট</a></li>
                                    <li><a class="dropdown-item" href="subject_export.php?format=excel&report_type=<?php echo $report_type; ?>&department_id=<?php echo $department_id; ?>&session_id=<?php echo $session_id; ?>">Excel ফরম্যাট</a></li>
                                    <li><a class="dropdown-item" href="subject_export.php?format=csv&report_type=<?php echo $report_type; ?>&department_id=<?php echo $department_id; ?>&session_id=<?php echo $session_id; ?>">CSV ফরম্যাট</a></li>
                                </ul>
                            </div>
                            <a href="subject_print.php?report_type=<?php echo $report_type; ?>&department_id=<?php echo $department_id; ?>&session_id=<?php echo $session_id; ?>" class="btn btn-sm btn-light ms-2" target="_blank">
                                <i class="fas fa-print me-1"></i>প্রিন্ট
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover table-striped">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>বিষয়ের নাম</th>
                                        <th>বিষয় কোড</th>
                                        <th>বিভাগ</th>
                                        <th>বিবরণ</th>
                                        <th>বরাদ্দকৃত শিক্ষক</th>
                                        <th>স্ট্যাটাস</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($subjects && $subjects->num_rows > 0): ?>
                                        <?php 
                                        $counter = 1;
                                        while ($subject = $subjects->fetch_assoc()): 
                                        ?>
                                            <tr>
                                                <td><?php echo $counter++; ?></td>
                                                <td><?php echo $subject['subject_name']; ?></td>
                                                <td><?php echo $subject['subject_code']; ?></td>
                                                <td><?php echo $subject['department_name']; ?></td>
                                                <td><?php echo $subject['description'] ? mb_substr($subject['description'], 0, 30) . '...' : 'N/A'; ?></td>
                                                <td><?php echo $subject['teacher_count']; ?></td>
                                                <td>
                                                    <?php if ($subject['is_active'] == 1): ?>
                                                        <span class="badge bg-success">সক্রিয়</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-warning">নিষ্ক্রিয়</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="7" class="text-center">কোন বিষয় পাওয়া যায়নি</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Show/hide session filter based on report type
        document.getElementById('report_type').addEventListener('change', function() {
            const sessionFilter = document.getElementById('session_filter');
            if (this.value === 'assigned_subjects') {
                sessionFilter.style.display = 'block';
            } else {
                sessionFilter.style.display = 'none';
            }
        });
    </script>
</body>
</html> 