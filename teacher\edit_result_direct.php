<?php
session_start();

// Check if user is logged in and is a teacher
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'teacher') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if result ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: results.php");
    exit();
}

$resultId = intval($_GET['id']);

// Get teacher information with department name
$userId = $_SESSION['userId'];
$username = $_SESSION['username'] ?? '';

// First try to get teacher by user_id
$sql = "SELECT t.*, d.department_name 
        FROM teachers t
        LEFT JOIN departments d ON t.department_id = d.id
        WHERE t.user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();

// If no results, try to get by username
if ($result->num_rows === 0 && !empty($username)) {
    $sql = "SELECT t.*, d.department_name 
            FROM teachers t
            LEFT JOIN departments d ON t.department_id = d.id
            WHERE t.username = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
}

$teacher = $result->fetch_assoc();

// If still no teacher found, redirect to login
if (!$teacher) {
    header("Location: ../index.php");
    exit();
}

// Get result information
try {
    // First, get the basic result data
    $basicQuery = "SELECT * FROM results WHERE id = ?";
    $stmt = $conn->prepare($basicQuery);
    $stmt->bind_param("i", $resultId);
    $stmt->execute();
    $basicResult = $stmt->get_result();
    
    if ($basicResult->num_rows === 0) {
        $_SESSION['error_msg'] = "ফলাফল খুঁজে পাওয়া যায়নি";
        header("Location: results.php");
        exit();
    }
    
    $resultInfo = $basicResult->fetch_assoc();
    
    // Get exam data if available
    if (isset($resultInfo['exam_id']) && !empty($resultInfo['exam_id'])) {
        $examQuery = "SELECT * FROM exams WHERE id = ?";
        $stmt = $conn->prepare($examQuery);
        $stmt->bind_param("i", $resultInfo['exam_id']);
        $stmt->execute();
        $examResult = $stmt->get_result();
        
        if ($examResult->num_rows > 0) {
            $examData = $examResult->fetch_assoc();
            $resultInfo['exam_name'] = $examData['exam_name'] ?? 'N/A';
            $resultInfo['exam_type'] = $examData['exam_type'] ?? 'N/A';
            $resultInfo['exam_date'] = $examData['exam_date'] ?? 'N/A';
            $resultInfo['exam_total_marks'] = $examData['total_marks'] ?? $resultInfo['total_marks'];
        }
    }
    
    // Get student data if available
    if (isset($resultInfo['student_id']) && !empty($resultInfo['student_id'])) {
        $studentQuery = "SELECT * FROM students WHERE id = ?";
        $stmt = $conn->prepare($studentQuery);
        $stmt->bind_param("i", $resultInfo['student_id']);
        $stmt->execute();
        $studentResult = $stmt->get_result();
        
        if ($studentResult->num_rows > 0) {
            $studentData = $studentResult->fetch_assoc();
            $resultInfo['student_code'] = $studentData['student_id'] ?? 'N/A';
            $resultInfo['first_name'] = $studentData['first_name'] ?? 'N/A';
            $resultInfo['last_name'] = $studentData['last_name'] ?? '';
            $resultInfo['roll_number'] = $studentData['roll_number'] ?? 'N/A';
            
            // Get class data if available
            if (isset($studentData['class_id']) && !empty($studentData['class_id'])) {
                $classQuery = "SELECT * FROM classes WHERE id = ?";
                $stmt = $conn->prepare($classQuery);
                $stmt->bind_param("i", $studentData['class_id']);
                $stmt->execute();
                $classResult = $stmt->get_result();
                
                if ($classResult->num_rows > 0) {
                    $classData = $classResult->fetch_assoc();
                    $resultInfo['class_name'] = $classData['class_name'] ?? 'N/A';
                }
            }
        }
    }
    
    // Get subject data if available
    if (isset($resultInfo['subject_id']) && !empty($resultInfo['subject_id'])) {
        $subjectQuery = "SELECT * FROM subjects WHERE id = ?";
        $stmt = $conn->prepare($subjectQuery);
        $stmt->bind_param("i", $resultInfo['subject_id']);
        $stmt->execute();
        $subjectResult = $stmt->get_result();
        
        if ($subjectResult->num_rows > 0) {
            $subjectData = $subjectResult->fetch_assoc();
            $resultInfo['subject_name'] = $subjectData['subject_name'] ?? 'N/A';
            $resultInfo['subject_code'] = $subjectData['subject_code'] ?? 'N/A';
        }
    }
} catch (Exception $e) {
    $_SESSION['error_msg'] = "ফলাফল তথ্য লোড করতে সমস্যা হয়েছে: " . $e->getMessage();
    header("Location: results.php");
    exit();
}

// Handle result update
$success_msg = '';
$error_msg = '';

if (isset($_POST['update_result'])) {
    $marksObtained = floatval($_POST['marks_obtained']);
    $totalMarks = floatval($_POST['total_marks']);
    $remarks = $conn->real_escape_string($_POST['remarks'] ?? '');
    
    if ($marksObtained < 0 || $marksObtained > $totalMarks) {
        $error_msg = "প্রাপ্ত নম্বর 0 থেকে $totalMarks এর মধ্যে হতে হবে";
    } else {
        // Calculate grade based on marks percentage
        $percentage = ($marksObtained / $totalMarks) * 100;
        $grade = '';
        
        if ($percentage >= 80) {
            $grade = 'A+';
        } elseif ($percentage >= 70) {
            $grade = 'A';
        } elseif ($percentage >= 60) {
            $grade = 'A-';
        } elseif ($percentage >= 50) {
            $grade = 'B';
        } elseif ($percentage >= 40) {
            $grade = 'C';
        } elseif ($percentage >= 33) {
            $grade = 'D';
        } else {
            $grade = 'F';
        }
        
        $updateQuery = "UPDATE results SET marks_obtained = ?, total_marks = ?, grade = ?, remarks = ? WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("ddssi", $marksObtained, $totalMarks, $grade, $remarks, $resultId);
        
        if ($stmt->execute()) {
            $_SESSION['success_msg'] = "ফলাফল সফলভাবে আপডেট করা হয়েছে";
            header("Location: results.php");
            exit();
        } else {
            $error_msg = "ফলাফল আপডেট করতে সমস্যা হয়েছে: " . $conn->error;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Result</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body, html, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, select, textarea, label, li, th, td {
            font-family: 'Hind Siliguri', sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
        }
        
        .sidebar {
            background-color: #343a40;
            color: white;
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 10px 20px;
            margin: 5px 0;
            border-radius: 5px;
        }
        
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .sidebar .nav-link.active {
            color: white;
            background-color: #007bff;
        }
        
        .main-content {
            padding: 20px;
        }
        
        .card {
            margin-bottom: 20px;
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        .card-header {
            font-weight: bold;
        }
        
        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }
        
        .btn-primary:hover {
            background-color: #0069d9;
            border-color: #0062cc;
        }
        
        .result-info {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .result-info h4 {
            margin-bottom: 15px;
            color: #007bff;
        }
        
        .grade-A-plus {
            background-color: #d4edda;
            font-weight: bold;
        }
        
        .grade-A {
            background-color: #d4edda;
        }
        
        .grade-A-minus {
            background-color: #d4edda;
        }
        
        .grade-B {
            background-color: #fff3cd;
        }
        
        .grade-C {
            background-color: #fff3cd;
        }
        
        .grade-D {
            background-color: #fff3cd;
        }
        
        .grade-F {
            background-color: #f8d7da;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>Teacher Panel</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user me-2"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> Students
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> Courses
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> Exams
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> Results
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">
                            <i class="fas fa-calendar-check me-2"></i> Attendance
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2>ফলাফল এডিট করুন</h2>
                            <a href="results.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i> ফলাফল তালিকায় ফিরে যান
                            </a>
                        </div>
                        
                        <?php if (!empty($success_msg)): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <?php echo $success_msg; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($error_msg)): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <?php echo $error_msg; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>
                        
                        <div class="result-info">
                            <h4>ফলাফল তথ্য</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>পরীক্ষা:</strong> <?php echo htmlspecialchars($resultInfo['exam_name'] ?? 'N/A'); ?></p>
                                    <p><strong>পরীক্ষার ধরন:</strong> 
                                        <?php 
                                            $examTypeText = '';
                                            switch ($resultInfo['exam_type'] ?? '') {
                                                case 'midterm':
                                                    $examTypeText = 'মিডটার্ম';
                                                    break;
                                                case 'final':
                                                    $examTypeText = 'ফাইনাল';
                                                    break;
                                                case 'quiz':
                                                    $examTypeText = 'কুইজ';
                                                    break;
                                                case 'assignment':
                                                    $examTypeText = 'অ্যাসাইনমেন্ট';
                                                    break;
                                                case 'practical':
                                                    $examTypeText = 'প্র্যাকটিক্যাল';
                                                    break;
                                                default:
                                                    $examTypeText = 'অন্যান্য';
                                            }
                                            echo $examTypeText;
                                        ?>
                                    </p>
                                    <p><strong>তারিখ:</strong> <?php echo isset($resultInfo['exam_date']) ? date('d M Y', strtotime($resultInfo['exam_date'])) : 'N/A'; ?></p>
                                    <p><strong>বিষয়:</strong> <?php echo isset($resultInfo['subject_name']) ? htmlspecialchars($resultInfo['subject_name'] . ' (' . $resultInfo['subject_code'] . ')') : 'N/A'; ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>শিক্ষার্থী আইডি:</strong> <?php echo htmlspecialchars($resultInfo['student_code'] ?? 'N/A'); ?></p>
                                    <p><strong>শিক্ষার্থীর নাম:</strong> <?php echo htmlspecialchars(($resultInfo['first_name'] ?? '') . ' ' . ($resultInfo['last_name'] ?? '')); ?></p>
                                    <p><strong>রোল নম্বর:</strong> <?php echo htmlspecialchars($resultInfo['roll_number'] ?? 'N/A'); ?></p>
                                    <p><strong>শ্রেণী:</strong> <?php echo htmlspecialchars($resultInfo['class_name'] ?? 'N/A'); ?></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">ফলাফল এডিট করুন</h5>
                            </div>
                            <div class="card-body">
                                <form action="" method="post">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="marks_obtained" class="form-label">প্রাপ্ত নম্বর*</label>
                                            <input type="number" class="form-control" id="marks_obtained" name="marks_obtained" value="<?php echo $resultInfo['marks_obtained']; ?>" min="0" max="<?php echo $resultInfo['total_marks']; ?>" step="0.01" required>
                                            <div class="form-text">0 থেকে <?php echo $resultInfo['total_marks']; ?> এর মধ্যে</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="total_marks" class="form-label">মোট নম্বর*</label>
                                            <input type="number" class="form-control" id="total_marks" name="total_marks" value="<?php echo $resultInfo['total_marks']; ?>" min="1" step="0.01" required>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="current_grade" class="form-label">বর্তমান গ্রেড</label>
                                            <input type="text" class="form-control <?php echo 'grade-' . str_replace('+', '-plus', $resultInfo['grade']); ?>" id="current_grade" value="<?php echo $resultInfo['grade']; ?>" readonly>
                                            <div class="form-text">গ্রেড অটোমেটিক ক্যালকুলেট করা হবে</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="percentage" class="form-label">শতকরা</label>
                                            <input type="text" class="form-control" id="percentage" value="<?php echo number_format(($resultInfo['marks_obtained'] / $resultInfo['total_marks']) * 100, 2) . '%'; ?>" readonly>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="remarks" class="form-label">মন্তব্য</label>
                                        <textarea class="form-control" id="remarks" name="remarks" rows="3"><?php echo htmlspecialchars($resultInfo['remarks'] ?? ''); ?></textarea>
                                    </div>
                                    <button type="submit" name="update_result" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i> ফলাফল আপডেট করুন
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const marksObtainedInput = document.getElementById('marks_obtained');
            const totalMarksInput = document.getElementById('total_marks');
            const currentGradeInput = document.getElementById('current_grade');
            const percentageInput = document.getElementById('percentage');
            
            function updateGradeAndPercentage() {
                const marksObtained = parseFloat(marksObtainedInput.value);
                const totalMarks = parseFloat(totalMarksInput.value);
                
                if (!isNaN(marksObtained) && !isNaN(totalMarks) && totalMarks > 0) {
                    const percentage = (marksObtained / totalMarks) * 100;
                    percentageInput.value = percentage.toFixed(2) + '%';
                    
                    let grade = '';
                    if (percentage >= 80) {
                        grade = 'A+';
                        currentGradeInput.className = 'form-control grade-A-plus';
                    } else if (percentage >= 70) {
                        grade = 'A';
                        currentGradeInput.className = 'form-control grade-A';
                    } else if (percentage >= 60) {
                        grade = 'A-';
                        currentGradeInput.className = 'form-control grade-A-minus';
                    } else if (percentage >= 50) {
                        grade = 'B';
                        currentGradeInput.className = 'form-control grade-B';
                    } else if (percentage >= 40) {
                        grade = 'C';
                        currentGradeInput.className = 'form-control grade-C';
                    } else if (percentage >= 33) {
                        grade = 'D';
                        currentGradeInput.className = 'form-control grade-D';
                    } else {
                        grade = 'F';
                        currentGradeInput.className = 'form-control grade-F';
                    }
                    
                    currentGradeInput.value = grade;
                }
            }
            
            marksObtainedInput.addEventListener('input', updateGradeAndPercentage);
            totalMarksInput.addEventListener('input', updateGradeAndPercentage);
            
            // Validate marks obtained is not greater than total marks
            marksObtainedInput.addEventListener('change', function() {
                const marksObtained = parseFloat(this.value);
                const totalMarks = parseFloat(totalMarksInput.value);
                
                if (marksObtained > totalMarks) {
                    alert('প্রাপ্ত নম্বর মোট নম্বরের চেয়ে বেশি হতে পারে না');
                    this.value = totalMarks;
                    updateGradeAndPercentage();
                }
            });
            
            // Update max attribute of marks_obtained when total_marks changes
            totalMarksInput.addEventListener('change', function() {
                const totalMarks = parseFloat(this.value);
                
                if (totalMarks > 0) {
                    marksObtainedInput.setAttribute('max', totalMarks);
                    
                    const marksObtained = parseFloat(marksObtainedInput.value);
                    if (marksObtained > totalMarks) {
                        marksObtainedInput.value = totalMarks;
                    }
                    
                    updateGradeAndPercentage();
                }
            });
        });
    </script>
</body>
</html>
