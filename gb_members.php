<?php
// Include database connection
require_once 'includes/dbh.inc.php';

// Check if governing_board_members table exists
$tableCheckQuery = "SHOW TABLES LIKE 'governing_board_members'";
$tableExists = $conn->query($tableCheckQuery)->num_rows > 0;

// Get all active governing board members ordered by display_order
$result = null;
if ($tableExists) {
    $sql = "SELECT * FROM governing_board_members WHERE is_active = 1 ORDER BY display_order ASC, name ASC";
    $result = $conn->query($sql);
}

// Page title
$page_title = "পরিচালনা বোর্ডের সদস্যবৃন্দ";
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title><?php echo $page_title; ?></title>

    <!-- Bootstrap CSS -->


    <!-- Custom Fonts CSS -->


    <!-- Custom CSS -->


    <style>
        .member-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%;
            border: none;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .member-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .member-photo {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 50%;
            border: 3px solid #00a65a;
            margin: 0 auto;
        }

        .member-info {
            text-align: center;
            padding: 20px;
        }

        .member-name {
            font-weight: 600;
            font-size: 1.2rem;
            margin-top: 15px;
            margin-bottom: 5px;
        }

        .member-position {
            color: #00a65a;
            font-weight: 500;
            margin-bottom: 10px;
        }

        .member-contact {
            margin-top: 15px;
            font-size: 0.9rem;
        }

        .member-contact i {
            width: 20px;
            color: #00a65a;
        }

        .page-header {
            background-color: #ffffff;
            color: #00a65a;
            padding: 20px 0;
            margin-bottom: 30px;
            border-bottom: 2px solid #00a65a;
        }

        .page-title {
            font-weight: 700;
            margin-bottom: 0;
        }

        .view-options {
            margin-bottom: 20px;
        }

        .view-options .btn {
            margin-right: 5px;
            background-color: #f8f9fa;
            color: #495057;
            border-color: #dee2e6;
        }

        .view-options .btn.active {
            background-color: #00a65a;
            color: white;
            border-color: #00a65a;
        }

        /* Grid View (Default) */
        .grid-view .member-card {
            height: 100%;
        }

        /* List View */
        .list-view .member-card {
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: 15px;
            margin-bottom: 15px;
        }

        .list-view .member-photo {
            width: 80px;
            height: 80px;
            margin: 0 20px 0 0;
        }

        .list-view .member-info {
            text-align: left;
            padding: 0;
            flex: 1;
        }

        /* Detail View */
        .detail-view .member-card {
            margin-bottom: 30px;
        }

        .detail-view .member-info {
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            text-align: left;
        }

        .detail-view .member-photo {
            margin: 0 30px 0 0;
        }

        .detail-view .member-details {
            flex: 1;
        }

        /* Title View */
        .title-view .member-card {
            padding: 10px 15px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .title-view .member-photo {
            width: 50px;
            height: 50px;
            margin: 0 15px 0 0;
        }

        .title-view .member-info {
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: 0;
            flex: 1;
        }

        .title-view .member-name {
            margin: 0 15px 0 0;
        }

        .title-view .member-position {
            margin-bottom: 0;
        }

        /* Table View */
        .table-view table {
            width: 100%;
            border-collapse: collapse;
        }

        .table-view th {
            background-color: #00a65a;
            color: white;
            padding: 12px 15px;
            text-align: left;
        }

        .table-view td {
            padding: 12px 15px;
            border-bottom: 1px solid #dee2e6;
        }

        .table-view .member-photo {
            width: 50px;
            height: 50px;
            margin: 0;
        }
    </style>
</head>
<body>
    <!-- Header Top with Logo and Title -->
    <div class="header-top">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-2 text-center text-md-start mb-3 mb-md-0">
                    <img src="img/logo.jpg" alt="School Logo" class="school-logo" onerror="this.src='https://via.placeholder.com/80?text=ZFAW'">
                </div>
                <div class="col-md-10 text-center text-md-start">
                    <h1 class="school-title mb-1">নিশাত এডুকেশন সেন্টার</h1>
                    <h2 class="school-subtitle fs-5">চুয়াডাঙ্গা, বাংলাদেশ</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Navigation -->
    <nav class="main-nav">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <ul class="nav">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php"><i class="fas fa-home me-1"></i> হোম</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="subjects.php"><i class="fas fa-book me-1"></i> বিষয়সমূহ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="teachers.php"><i class="fas fa-chalkboard-teacher me-1"></i> শিক্ষকবৃন্দ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="students.php"><i class="fas fa-user-graduate me-1"></i> শিক্ষার্থীবৃন্দ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="notices.php"><i class="fas fa-bullhorn me-1"></i> নোটিশ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="gb_members.php"><i class="fas fa-users me-1"></i> পরিচালনা বোর্ড</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="personal_sms.php"><i class="fas fa-sms me-1"></i> পার্সোনাল এসএমএস</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php"><i class="fas fa-sign-in-alt me-1"></i> লগইন</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <h1 class="page-title text-center"><?php echo $page_title; ?></h1>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container my-5">
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <p class="lead text-center mb-0">
                            আমাদের পরিচালনা বোর্ডের সদস্যবৃন্দ প্রতিষ্ঠানের উন্নয়নে নিরলসভাবে কাজ করে যাচ্ছেন। তাদের নেতৃত্বে আমাদের প্রতিষ্ঠান দিন দিন এগিয়ে যাচ্ছে।
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- View Options -->
        <div class="view-options d-flex justify-content-end">
            <div class="btn-group" role="group" aria-label="View options">
                <button type="button" class="btn btn-sm active" data-view="grid"><i class="fas fa-th me-1"></i> গ্রীড</button>
                <button type="button" class="btn btn-sm" data-view="list"><i class="fas fa-list me-1"></i> লিস্ট</button>
                <button type="button" class="btn btn-sm" data-view="detail"><i class="fas fa-id-card me-1"></i> ডিটেল</button>
                <button type="button" class="btn btn-sm" data-view="title"><i class="fas fa-heading me-1"></i> টাইটেল</button>
                <button type="button" class="btn btn-sm" data-view="table"><i class="fas fa-table me-1"></i> টেবিল</button>
            </div>
        </div>

        <!-- Grid View (Default) -->
        <div class="view-container grid-view">
            <div class="row g-4">
                <?php
                if ($result && $result->num_rows > 0) {
                    // Reset the result pointer to the beginning
                    $result->data_seek(0);

                    while ($member = $result->fetch_assoc()) {
                        ?>
                        <div class="col-md-6 col-lg-4">
                            <div class="member-card">
                                <div class="member-info">
                                    <img src="<?php echo !empty($member['photo']) ? $member['photo'] : 'img/default-user.png'; ?>"
                                         alt="<?php echo htmlspecialchars($member['name']); ?>"
                                         class="member-photo"
                                         onerror="this.src='img/default-user.png'">
                                    <h3 class="member-name"><?php echo htmlspecialchars($member['name']); ?></h3>
                                    <div class="member-position"><?php echo htmlspecialchars($member['position']); ?></div>

                                    <?php if (!empty($member['bio'])): ?>
                                    <p class="member-bio"><?php echo htmlspecialchars($member['bio']); ?></p>
                                    <?php endif; ?>

                                    <div class="member-contact">
                                        <?php if (!empty($member['address'])): ?>
                                        <p><i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($member['address']); ?></p>
                                        <?php endif; ?>

                                        <?php if (!empty($member['phone'])): ?>
                                        <p><i class="fas fa-phone"></i> <?php echo htmlspecialchars($member['phone']); ?></p>
                                        <?php endif; ?>

                                        <?php if (!empty($member['email'])): ?>
                                        <p><i class="fas fa-envelope"></i> <?php echo htmlspecialchars($member['email']); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php
                    }
                } else {
                    ?>
                    <div class="col-12">
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle me-2"></i> বর্তমানে কোন পরিচালনা বোর্ডের সদস্য তথ্য উপলব্ধ নেই।
                        </div>
                    </div>
                    <?php
                }
                ?>
            </div>
        </div>

        <!-- List View -->
        <div class="view-container list-view" style="display: none;">
            <?php
            if ($result && $result->num_rows > 0) {
                // Reset the result pointer to the beginning
                $result->data_seek(0);

                while ($member = $result->fetch_assoc()) {
                    ?>
                    <div class="member-card">
                        <img src="<?php echo !empty($member['photo']) ? $member['photo'] : 'img/default-user.png'; ?>"
                             alt="<?php echo htmlspecialchars($member['name']); ?>"
                             class="member-photo"
                             onerror="this.src='img/default-user.png'">
                        <div class="member-info">
                            <h3 class="member-name"><?php echo htmlspecialchars($member['name']); ?></h3>
                            <div class="member-position"><?php echo htmlspecialchars($member['position']); ?></div>

                            <div class="member-contact">
                                <?php if (!empty($member['phone'])): ?>
                                <span class="me-3"><i class="fas fa-phone me-1"></i> <?php echo htmlspecialchars($member['phone']); ?></span>
                                <?php endif; ?>

                                <?php if (!empty($member['email'])): ?>
                                <span><i class="fas fa-envelope me-1"></i> <?php echo htmlspecialchars($member['email']); ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            } else {
                ?>
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i> বর্তমানে কোন পরিচালনা বোর্ডের সদস্য তথ্য উপলব্ধ নেই।
                </div>
                <?php
            }
            ?>
        </div>

        <!-- Detail View -->
        <div class="view-container detail-view" style="display: none;">
            <?php
            if ($result && $result->num_rows > 0) {
                // Reset the result pointer to the beginning
                $result->data_seek(0);

                while ($member = $result->fetch_assoc()) {
                    ?>
                    <div class="member-card">
                        <div class="member-info">
                            <img src="<?php echo !empty($member['photo']) ? $member['photo'] : 'img/default-user.png'; ?>"
                                 alt="<?php echo htmlspecialchars($member['name']); ?>"
                                 class="member-photo"
                                 onerror="this.src='img/default-user.png'">
                            <div class="member-details">
                                <h3 class="member-name"><?php echo htmlspecialchars($member['name']); ?></h3>
                                <div class="member-position mb-3"><?php echo htmlspecialchars($member['position']); ?></div>

                                <?php if (!empty($member['bio'])): ?>
                                <p class="member-bio"><?php echo htmlspecialchars($member['bio']); ?></p>
                                <?php endif; ?>

                                <div class="member-contact">
                                    <h5>যোগাযোগ তথ্য</h5>
                                    <?php if (!empty($member['address'])): ?>
                                    <p><i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($member['address']); ?></p>
                                    <?php endif; ?>

                                    <?php if (!empty($member['phone'])): ?>
                                    <p><i class="fas fa-phone"></i> <?php echo htmlspecialchars($member['phone']); ?></p>
                                    <?php endif; ?>

                                    <?php if (!empty($member['email'])): ?>
                                    <p><i class="fas fa-envelope"></i> <?php echo htmlspecialchars($member['email']); ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <?php
                }
            } else {
                ?>
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i> বর্তমানে কোন পরিচালনা বোর্ডের সদস্য তথ্য উপলব্ধ নেই।
                </div>
                <?php
            }
            ?>
        </div>

        <!-- Title View -->
        <div class="view-container title-view" style="display: none;">
            <?php
            if ($result && $result->num_rows > 0) {
                // Reset the result pointer to the beginning
                $result->data_seek(0);

                while ($member = $result->fetch_assoc()) {
                    ?>
                    <div class="member-card">
                        <img src="<?php echo !empty($member['photo']) ? $member['photo'] : 'img/default-user.png'; ?>"
                             alt="<?php echo htmlspecialchars($member['name']); ?>"
                             class="member-photo"
                             onerror="this.src='img/default-user.png'">
                        <div class="member-info">
                            <h3 class="member-name"><?php echo htmlspecialchars($member['name']); ?></h3>
                            <div class="member-position"><?php echo htmlspecialchars($member['position']); ?></div>
                        </div>
                    </div>
                    <?php
                }
            } else {
                ?>
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i> বর্তমানে কোন পরিচালনা বোর্ডের সদস্য তথ্য উপলব্ধ নেই।
                </div>
                <?php
            }
            ?>
        </div>

        <!-- Table View -->
        <div class="view-container table-view" style="display: none;">
            <?php if ($result && $result->num_rows > 0): ?>
                <table>
                    <thead>
                        <tr>
                            <th>ছবি</th>
                            <th>নাম</th>
                            <th>পদবী</th>
                            <th>ঠিকানা</th>
                            <th>যোগাযোগ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Reset the result pointer to the beginning
                        $result->data_seek(0);

                        while ($member = $result->fetch_assoc()) {
                            ?>
                            <tr>
                                <td>
                                    <img src="<?php echo !empty($member['photo']) ? $member['photo'] : 'img/default-user.png'; ?>"
                                         alt="<?php echo htmlspecialchars($member['name']); ?>"
                                         class="member-photo"
                                         onerror="this.src='img/default-user.png'">
                                </td>
                                <td><?php echo htmlspecialchars($member['name']); ?></td>
                                <td><?php echo htmlspecialchars($member['position']); ?></td>
                                <td><?php echo !empty($member['address']) ? htmlspecialchars($member['address']) : '-'; ?></td>
                                <td>
                                    <?php if (!empty($member['phone'])): ?>
                                    <div><i class="fas fa-phone me-1"></i> <?php echo htmlspecialchars($member['phone']); ?></div>
                                    <?php endif; ?>

                                    <?php if (!empty($member['email'])): ?>
                                    <div><i class="fas fa-envelope me-1"></i> <?php echo htmlspecialchars($member['email']); ?></div>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php
                        }
                        ?>
                    </tbody>
                </table>
            <?php else: ?>
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i> বর্তমানে কোন পরিচালনা বোর্ডের সদস্য তথ্য উপলব্ধ নেই।
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer mt-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5>ZFAW শিক্ষালয়</h5>
                    <p>উচ্চমানের শিক্ষা প্রদানের মাধ্যমে শিক্ষার্থীদের ভবিষ্যৎ গড়তে সাহায্য করা আমাদের লক্ষ্য।</p>
                    <div class="social-icons mt-3">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>

                <div class="col-lg-4 mb-4">
                    <h5>লিঙ্কসমূহ</h5>
                    <ul class="footer-links">
                        <li><a href="index.php"><i class="fas fa-chevron-right me-2"></i> হোম</a></li>
                        <li><a href="about.php"><i class="fas fa-chevron-right me-2"></i> আমাদের সম্পর্কে</a></li>
                        <li><a href="subjects.php"><i class="fas fa-chevron-right me-2"></i> বিষয়সমূহ</a></li>
                        <li><a href="teachers.php"><i class="fas fa-chevron-right me-2"></i> শিক্ষকবৃন্দ</a></li>
                        <li><a href="students.php"><i class="fas fa-chevron-right me-2"></i> শিক্ষার্থীবৃন্দ</a></li>
                        <li><a href="login.php"><i class="fas fa-chevron-right me-2"></i> লগইন</a></li>
                    </ul>
                </div>

                <div class="col-lg-4 mb-4">
                    <h5>যোগাযোগ, মোঃ আসফ উদ্দৌলাহ্ (ঝন্টু)</h5>
                    <ul class="footer-links">
                        <li><i class="fas fa-map-marker-alt me-2"></i> চুয়াডাঙ্গা, বাংলাদেশ</li>
                        <li><i class="fas fa-phone me-2"></i> +৮৮০১৭১৭৮৬১৭৬২, +৮৮০১৯৭৭৮৬১৭৬২,</li>
                        <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="copyright text-center">
            <div class="container">
                <p class="mb-0">&copy; <?php echo date('Y'); ?> আসফ উদ্দৌল্লাহ্. সর্বস্বত্ব সংরক্ষিত।</p>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <a href="#" class="btn back-to-top position-fixed bottom-0 end-0 m-4 rounded-circle" style="width: 45px; height: 45px; line-height: 45px; display: none; background-color: var(--accent-color); color: var(--dark-color);">
        <i class="fas fa-arrow-up"></i>
    </a>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Back to top button
        window.addEventListener('scroll', function() {
            var backToTopBtn = document.querySelector('.back-to-top');
            if (backToTopBtn) {
                if (window.pageYOffset > 300) {
                    backToTopBtn.style.display = 'flex';
                    backToTopBtn.style.justifyContent = 'center';
                    backToTopBtn.style.alignItems = 'center';
                } else {
                    backToTopBtn.style.display = 'none';
                }
            }
        });

        // View switching functionality
        document.addEventListener('DOMContentLoaded', function() {
            const viewButtons = document.querySelectorAll('.view-options button');
            const viewContainers = document.querySelectorAll('.view-container');

            // Function to switch views
            function switchView(viewName) {
                // Hide all view containers
                viewContainers.forEach(container => {
                    container.style.display = 'none';
                });

                // Remove active class from all buttons
                viewButtons.forEach(button => {
                    button.classList.remove('active');
                });

                // Show the selected view container
                const selectedContainer = document.querySelector('.' + viewName + '-view');
                if (selectedContainer) {
                    selectedContainer.style.display = 'block';
                }

                // Add active class to the clicked button
                const selectedButton = document.querySelector(`[data-view="${viewName}"]`);
                if (selectedButton) {
                    selectedButton.classList.add('active');
                }

                // Save the current view preference in localStorage
                localStorage.setItem('gb_members_view', viewName);
            }

            // Add click event listeners to view buttons
            viewButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const viewName = this.getAttribute('data-view');
                    switchView(viewName);
                });
            });

            // Check if there's a saved view preference
            const savedView = localStorage.getItem('gb_members_view');
            if (savedView) {
                switchView(savedView);
            } else {
                // Default to grid view
                switchView('grid');
            }
        });
    </script>
</body>
</html>
