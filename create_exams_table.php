<?php
// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "college_management";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Create exams table
$examsTableQuery = "CREATE TABLE IF NOT EXISTS exams (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    exam_name VARCHAR(100) NOT NULL,
    class_id INT(11) NOT NULL,
    exam_date DATE NOT NULL,
    start_time TIME NULL,
    end_time TIME NULL,
    total_marks INT(11) DEFAULT 100,
    passing_marks INT(11) DEFAULT 33,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE
)";

if ($conn->query($examsTableQuery)) {
    echo "Exams table created successfully!<br>";
} else {
    echo "Error creating exams table: " . $conn->error . "<br>";
}

// Create exam_subjects table for mapping exams to subjects
$examSubjectsTableQuery = "CREATE TABLE IF NOT EXISTS exam_subjects (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    exam_id INT(11) NOT NULL,
    subject_id INT(11) NOT NULL,
    max_marks INT(11) DEFAULT 100,
    passing_marks INT(11) DEFAULT 33,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (exam_id) REFERENCES exams(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    UNIQUE KEY (exam_id, subject_id)
)";

if ($conn->query($examSubjectsTableQuery)) {
    echo "Exam_subjects table created successfully!<br>";
} else {
    echo "Error creating exam_subjects table: " . $conn->error . "<br>";
}

// Create exam_results table for storing exam results
$examResultsTableQuery = "CREATE TABLE IF NOT EXISTS exam_results (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    exam_id INT(11) NOT NULL,
    student_id INT(11) NOT NULL,
    subject_id INT(11) NOT NULL,
    marks_obtained DECIMAL(5,2) NOT NULL,
    is_pass TINYINT(1) DEFAULT 0,
    remarks TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (exam_id) REFERENCES exams(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    UNIQUE KEY (exam_id, student_id, subject_id)
)";

if ($conn->query($examResultsTableQuery)) {
    echo "Exam_results table created successfully!<br>";
} else {
    echo "Error creating exam_results table: " . $conn->error . "<br>";
}

// Add a sample exam if a class exists
$classCheckQuery = "SELECT id FROM classes LIMIT 1";
$classResult = $conn->query($classCheckQuery);

if ($classResult && $classResult->num_rows > 0) {
    $classRow = $classResult->fetch_assoc();
    $classId = $classRow['id'];
    
    // Add a sample exam
    $insertExamQuery = "INSERT INTO exams (exam_name, class_id, exam_date, total_marks, passing_marks) 
                        VALUES ('মধ্য-সেমিস্টার পরীক্ষা', $classId, DATE_ADD(CURDATE(), INTERVAL 10 DAY), 100, 33)";
                        
    if ($conn->query($insertExamQuery)) {
        echo "Sample exam added successfully!<br>";
    } else {
        echo "Error adding sample exam: " . $conn->error . "<br>";
    }
}

echo "<p>All tables have been created. <a href='admin/exam_dashboard.php'>Go to Exam Dashboard</a></p>";

$conn->close();
?> 