<?php
// Include database connection
require_once 'includes/dbh.inc.php';

// Create announcements table if it doesn't exist
$sql = "CREATE TABLE IF NOT EXISTS announcements (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    created_by INT(11),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active TINYINT(1) DEFAULT 1
)";

if ($conn->query($sql) === TRUE) {
    echo "Announcements table created successfully or already exists.<br>";
    
    // Check if there are any announcements
    $checkAnnouncements = $conn->query("SELECT * FROM announcements LIMIT 1");
    
    if ($checkAnnouncements->num_rows == 0) {
        // Insert sample announcements
        $sampleAnnouncements = [
            [
                'title' => 'শিক্ষা বর্ষ ২০২৩-২০২৪ শুরু',
                'content' => 'আগামী ১ জানুয়ারি ২০২৪ থেকে নতুন শিক্ষা বর্ষ শুরু হবে। সকল শিক্ষার্থীদের যথাসময়ে ক্লাসে উপস্থিত থাকার জন্য অনুরোধ করা হচ্ছে।'
            ],
            [
                'title' => 'মিড টার্ম পরীক্ষার সময়সূচি',
                'content' => 'আগামী ১৫ ফেব্রুয়ারি ২০২৪ থেকে মিড টার্ম পরীক্ষা শুরু হবে। বিস্তারিত সময়সূচি শীঘ্রই জানানো হবে।'
            ],
            [
                'title' => 'বার্ষিক ক্রীড়া প্রতিযোগিতা',
                'content' => 'আগামী ১০ মার্চ ২০২৪ তারিখে বার্ষিক ক্রীড়া প্রতিযোগিতা অনুষ্ঠিত হবে। অংশগ্রহণে ইচ্ছুক শিক্ষার্থীরা শারীরিক শিক্ষা বিভাগে যোগাযোগ করুন।'
            ],
            [
                'title' => 'ছুটির নোটিশ',
                'content' => 'আগামী ২১ ফেব্রুয়ারি ২০২৪ আন্তর্জাতিক মাতৃভাষা দিবস উপলক্ষে কলেজ বন্ধ থাকবে।'
            ],
            [
                'title' => 'বেতন জমা দেওয়ার শেষ তারিখ',
                'content' => 'ফেব্রুয়ারি মাসের বেতন জমা দেওয়ার শেষ তারিখ ১০ ফেব্রুয়ারি ২০২৪। নির্ধারিত সময়ের মধ্যে বেতন জমা দিতে অনুরোধ করা হচ্ছে।'
            ]
        ];
        
        $insertQuery = "INSERT INTO announcements (title, content) VALUES (?, ?)";
        $stmt = $conn->prepare($insertQuery);
        
        foreach ($sampleAnnouncements as $announcement) {
            $stmt->bind_param("ss", $announcement['title'], $announcement['content']);
            $stmt->execute();
        }
        
        echo "Sample announcements added successfully.<br>";
        $stmt->close();
    } else {
        echo "Announcements already exist in the table.<br>";
    }
} else {
    echo "Error creating announcements table: " . $conn->error . "<br>";
}

// Redirect to student dashboard after 5 seconds
echo "<p>Redirecting to student dashboard in 5 seconds...</p>";
echo "<script>setTimeout(function() { window.location.href = 'student/dashboard.php'; }, 5000);</script>";
echo "<p><a href='student/dashboard.php'>Click here if you are not redirected automatically</a></p>";

$conn->close();
?>
