<?php
// Include database connection
require_once "includes/dbh.inc.php";

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>বিষয় নির্বাচন ডিবাগিং</h1>";

// Get the student info
$studentId = 'STD-601523'; // The student ID from the URL
$studentQuery = "SELECT s.id, s.department_id, d.department_name 
                FROM students s 
                JOIN departments d ON s.department_id = d.id 
                WHERE s.student_id = ?";
$stmt = $conn->prepare($studentQuery);
$stmt->bind_param("s", $studentId);
$stmt->execute();
$studentResult = $stmt->get_result();

if ($studentResult && $studentResult->num_rows > 0) {
    $studentInfo = $studentResult->fetch_assoc();
    $departmentId = $studentInfo['department_id'];
    $departmentName = $studentInfo['department_name'];
    
    echo "<h2>শিক্ষার্থীর তথ্য</h2>";
    echo "<p><strong>শিক্ষার্থী আইডি:</strong> $studentId</p>";
    echo "<p><strong>বিভাগ:</strong> $departmentName (ID: $departmentId)</p>";
    
    // Check if department_subject_types table exists
    $tableExists = $conn->query("SHOW TABLES LIKE 'department_subject_types'")->num_rows > 0;
    echo "<p><strong>department_subject_types টেবিল অস্তিত্ব:</strong> " . ($tableExists ? 'হ্যাঁ' : 'না') . "</p>";
    
    if ($tableExists) {
        // Check the structure of department_subject_types table
        $tableStructure = $conn->query("DESCRIBE department_subject_types");
        echo "<h3>department_subject_types টেবিল স্ট্রাকচার</h3>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        while ($field = $tableStructure->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$field['Field']}</td>";
            echo "<td>{$field['Type']}</td>";
            echo "<td>{$field['Null']}</td>";
            echo "<td>{$field['Key']}</td>";
            echo "<td>{$field['Default']}</td>";
            echo "<td>{$field['Extra']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check mappings in department_subject_types
        $mappingsQuery = "SELECT COUNT(*) as count FROM department_subject_types WHERE department_id = ?";
        $stmt = $conn->prepare($mappingsQuery);
        $stmt->bind_param("i", $departmentId);
        $stmt->execute();
        $mappingsCount = $stmt->get_result()->fetch_assoc()['count'];
        
        echo "<p><strong>বিভাগের জন্য বিষয় ম্যাপিং সংখ্যা:</strong> $mappingsCount</p>";
        
        // Check mappings by type
        $typeCountsQuery = "SELECT subject_type, COUNT(*) as count FROM department_subject_types 
                           WHERE department_id = ? GROUP BY subject_type";
        $stmt = $conn->prepare($typeCountsQuery);
        $stmt->bind_param("i", $departmentId);
        $stmt->execute();
        $typeCounts = $stmt->get_result();
        
        echo "<h3>বিষয় ধরন অনুযায়ী ম্যাপিং সংখ্যা</h3>";
        echo "<ul>";
        while ($typeCount = $typeCounts->fetch_assoc()) {
            echo "<li>{$typeCount['subject_type']}: {$typeCount['count']}</li>";
        }
        echo "</ul>";
        
        // Show the actual queries used in student_subject_selection.php
        echo "<h2>student_subject_selection.php এ ব্যবহৃত কোয়েরি</h2>";
        
        // Required subjects query
        $requiredSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code, dst.subject_type as category
                FROM subjects s
                JOIN department_subject_types dst ON s.id = dst.subject_id
                WHERE dst.department_id = ? AND s.is_active = 1
                AND dst.subject_type = 'required'
                ORDER BY s.subject_name";
        $stmt = $conn->prepare($requiredSubjectsQuery);
        $stmt->bind_param("i", $departmentId);
        $stmt->execute();
        $requiredSubjects = $stmt->get_result();
        
        echo "<h3>আবশ্যিক বিষয় কোয়েরি</h3>";
        echo "<pre>" . htmlspecialchars($requiredSubjectsQuery) . "</pre>";
        echo "<p><strong>ফলাফল সংখ্যা:</strong> " . $requiredSubjects->num_rows . "</p>";
        
        if ($requiredSubjects->num_rows > 0) {
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>ID</th><th>বিষয়</th><th>কোড</th><th>ক্যাটাগরি</th></tr>";
            while ($subject = $requiredSubjects->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$subject['id']}</td>";
                echo "<td>{$subject['subject_name']}</td>";
                echo "<td>{$subject['subject_code']}</td>";
                echo "<td>{$subject['category']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>কোন আবশ্যিক বিষয় পাওয়া যায়নি</p>";
            
            // Debug: Check if there are any subjects with 'required' category
            $checkRequiredQuery = "SELECT COUNT(*) as count FROM subjects WHERE category = 'required' AND is_active = 1";
            $requiredCount = $conn->query($checkRequiredQuery)->fetch_assoc()['count'];
            echo "<p><strong>subjects টেবিলে 'required' ক্যাটাগরির বিষয় সংখ্যা:</strong> $requiredCount</p>";
            
            // Debug: Check if there are any mappings with 'required' type
            $checkRequiredMappingsQuery = "SELECT COUNT(*) as count FROM department_subject_types 
                                         WHERE subject_type = 'required' AND department_id = ?";
            $stmt = $conn->prepare($checkRequiredMappingsQuery);
            $stmt->bind_param("i", $departmentId);
            $stmt->execute();
            $requiredMappingsCount = $stmt->get_result()->fetch_assoc()['count'];
            echo "<p><strong>department_subject_types টেবিলে 'required' টাইপের ম্যাপিং সংখ্যা:</strong> $requiredMappingsCount</p>";
            
            // Fix: Insert required subjects if missing
            echo "<h4>আবশ্যিক বিষয় যোগ করা হচ্ছে</h4>";
            
            // Get subjects with 'required' category
            $getRequiredSubjectsQuery = "SELECT id, subject_name FROM subjects WHERE category = 'required' AND is_active = 1";
            $requiredSubjectsList = $conn->query($getRequiredSubjectsQuery);
            
            if ($requiredSubjectsList && $requiredSubjectsList->num_rows > 0) {
                $insertTypeQuery = "INSERT INTO department_subject_types (department_id, subject_id, subject_type) 
                                  VALUES (?, ?, 'required') 
                                  ON DUPLICATE KEY UPDATE subject_type = 'required'";
                $stmt = $conn->prepare($insertTypeQuery);
                
                echo "<ul>";
                while ($subject = $requiredSubjectsList->fetch_assoc()) {
                    $subjectId = $subject['id'];
                    $stmt->bind_param("ii", $departmentId, $subjectId);
                    $stmt->execute();
                    echo "<li>আবশ্যিক বিষয় যোগ করা হয়েছে: {$subject['subject_name']} (ID: $subjectId)</li>";
                    
                    // Also ensure it's in subject_departments
                    $insertDeptSubjectQuery = "INSERT IGNORE INTO subject_departments (department_id, subject_id) VALUES (?, ?)";
                    $deptStmt = $conn->prepare($insertDeptSubjectQuery);
                    $deptStmt->bind_param("ii", $departmentId, $subjectId);
                    $deptStmt->execute();
                }
                echo "</ul>";
            }
        }
        
        // Optional subjects query
        $optionalSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code, dst.subject_type as category
                FROM subjects s
                JOIN department_subject_types dst ON s.id = dst.subject_id
                WHERE dst.department_id = ? AND s.is_active = 1
                AND dst.subject_type = 'optional'
                ORDER BY s.subject_name";
        $stmt = $conn->prepare($optionalSubjectsQuery);
        $stmt->bind_param("i", $departmentId);
        $stmt->execute();
        $optionalSubjects = $stmt->get_result();
        
        echo "<h3>ঐচ্ছিক বিষয় কোয়েরি</h3>";
        echo "<pre>" . htmlspecialchars($optionalSubjectsQuery) . "</pre>";
        echo "<p><strong>ফলাফল সংখ্যা:</strong> " . $optionalSubjects->num_rows . "</p>";
        
        if ($optionalSubjects->num_rows > 0) {
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>ID</th><th>বিষয়</th><th>কোড</th><th>ক্যাটাগরি</th></tr>";
            while ($subject = $optionalSubjects->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$subject['id']}</td>";
                echo "<td>{$subject['subject_name']}</td>";
                echo "<td>{$subject['subject_code']}</td>";
                echo "<td>{$subject['category']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>কোন ঐচ্ছিক বিষয় পাওয়া যায়নি</p>";
            
            // Debug: Check if there are any subjects with 'optional' category
            $checkOptionalQuery = "SELECT COUNT(*) as count FROM subjects WHERE category = 'optional' AND is_active = 1";
            $optionalCount = $conn->query($checkOptionalQuery)->fetch_assoc()['count'];
            echo "<p><strong>subjects টেবিলে 'optional' ক্যাটাগরির বিষয় সংখ্যা:</strong> $optionalCount</p>";
            
            // Fix: Insert optional subjects if missing
            echo "<h4>ঐচ্ছিক বিষয় যোগ করা হচ্ছে</h4>";
            
            // Get subjects with 'optional' category
            $getOptionalSubjectsQuery = "SELECT id, subject_name FROM subjects WHERE category = 'optional' AND is_active = 1";
            $optionalSubjectsList = $conn->query($getOptionalSubjectsQuery);
            
            if ($optionalSubjectsList && $optionalSubjectsList->num_rows > 0) {
                $insertTypeQuery = "INSERT INTO department_subject_types (department_id, subject_id, subject_type) 
                                  VALUES (?, ?, 'optional') 
                                  ON DUPLICATE KEY UPDATE subject_type = 'optional'";
                $stmt = $conn->prepare($insertTypeQuery);
                
                echo "<ul>";
                while ($subject = $optionalSubjectsList->fetch_assoc()) {
                    $subjectId = $subject['id'];
                    $stmt->bind_param("ii", $departmentId, $subjectId);
                    $stmt->execute();
                    echo "<li>ঐচ্ছিক বিষয় যোগ করা হয়েছে: {$subject['subject_name']} (ID: $subjectId)</li>";
                    
                    // Also ensure it's in subject_departments
                    $insertDeptSubjectQuery = "INSERT IGNORE INTO subject_departments (department_id, subject_id) VALUES (?, ?)";
                    $deptStmt = $conn->prepare($insertDeptSubjectQuery);
                    $deptStmt->bind_param("ii", $departmentId, $subjectId);
                    $deptStmt->execute();
                }
                echo "</ul>";
            }
        }
        
        // Fourth subjects query
        $fourthSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code, dst.subject_type as category
               FROM subjects s
               JOIN department_subject_types dst ON s.id = dst.subject_id
               WHERE dst.department_id = ? AND s.is_active = 1
               AND dst.subject_type = 'fourth'
               ORDER BY s.subject_name";
        $stmt = $conn->prepare($fourthSubjectsQuery);
        $stmt->bind_param("i", $departmentId);
        $stmt->execute();
        $fourthSubjects = $stmt->get_result();
        
        echo "<h3>৪র্থ বিষয় কোয়েরি</h3>";
        echo "<pre>" . htmlspecialchars($fourthSubjectsQuery) . "</pre>";
        echo "<p><strong>ফলাফল সংখ্যা:</strong> " . $fourthSubjects->num_rows . "</p>";
        
        if ($fourthSubjects->num_rows > 0) {
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>ID</th><th>বিষয়</th><th>কোড</th><th>ক্যাটাগরি</th></tr>";
            while ($subject = $fourthSubjects->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$subject['id']}</td>";
                echo "<td>{$subject['subject_name']}</td>";
                echo "<td>{$subject['subject_code']}</td>";
                echo "<td>{$subject['category']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>কোন ৪র্থ বিষয় পাওয়া যায়নি</p>";
            
            // Debug: Check if there are any subjects with 'fourth' category
            $checkFourthQuery = "SELECT COUNT(*) as count FROM subjects WHERE category = 'fourth' AND is_active = 1";
            $fourthCount = $conn->query($checkFourthQuery)->fetch_assoc()['count'];
            echo "<p><strong>subjects টেবিলে 'fourth' ক্যাটাগরির বিষয় সংখ্যা:</strong> $fourthCount</p>";
            
            // Fix: Insert fourth subjects if missing
            echo "<h4>৪র্থ বিষয় যোগ করা হচ্ছে</h4>";
            
            // Get subjects with 'fourth' category
            $getFourthSubjectsQuery = "SELECT id, subject_name FROM subjects WHERE category = 'fourth' AND is_active = 1";
            $fourthSubjectsList = $conn->query($getFourthSubjectsQuery);
            
            if ($fourthSubjectsList && $fourthSubjectsList->num_rows > 0) {
                $insertTypeQuery = "INSERT INTO department_subject_types (department_id, subject_id, subject_type) 
                                  VALUES (?, ?, 'fourth') 
                                  ON DUPLICATE KEY UPDATE subject_type = 'fourth'";
                $stmt = $conn->prepare($insertTypeQuery);
                
                echo "<ul>";
                while ($subject = $fourthSubjectsList->fetch_assoc()) {
                    $subjectId = $subject['id'];
                    $stmt->bind_param("ii", $departmentId, $subjectId);
                    $stmt->execute();
                    echo "<li>৪র্থ বিষয় যোগ করা হয়েছে: {$subject['subject_name']} (ID: $subjectId)</li>";
                    
                    // Also ensure it's in subject_departments
                    $insertDeptSubjectQuery = "INSERT IGNORE INTO subject_departments (department_id, subject_id) VALUES (?, ?)";
                    $deptStmt = $conn->prepare($insertDeptSubjectQuery);
                    $deptStmt->bind_param("ii", $departmentId, $subjectId);
                    $deptStmt->execute();
                }
                echo "</ul>";
            }
        }
    } else {
        // Create the department_subject_types table if it doesn't exist
        $createTableQuery = "CREATE TABLE department_subject_types (
            id INT AUTO_INCREMENT PRIMARY KEY,
            department_id INT NOT NULL,
            subject_id INT NOT NULL,
            subject_type ENUM('required', 'optional', 'fourth') NOT NULL DEFAULT 'optional',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_dept_subject (department_id, subject_id),
            FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
            FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
        )";
        
        if ($conn->query($createTableQuery)) {
            echo "<p style='color:green'>Successfully created department_subject_types table.</p>";
            
            // Now populate it
            $populateQuery = "INSERT INTO department_subject_types (department_id, subject_id, subject_type)
                             SELECT sd.department_id, sd.subject_id, s.category
                             FROM subject_departments sd
                             JOIN subjects s ON sd.subject_id = s.id
                             WHERE s.is_active = 1";
            
            if ($conn->query($populateQuery)) {
                echo "<p style='color:green'>Successfully populated department_subject_types table.</p>";
            } else {
                echo "<p style='color:red'>Error populating table: " . $conn->error . "</p>";
            }
        } else {
            echo "<p style='color:red'>Error creating table: " . $conn->error . "</p>";
        }
    }
    
    echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>ডিবাগিং সম্পন্ন হয়েছে!</h3>";
    echo "<p>এখন আপনি <a href='admin/student_subject_selection.php?id=$studentId'>শিক্ষার্থী বিষয় নির্বাচন</a> পেজে যেতে পারেন।</p>";
    echo "</div>";
} else {
    echo "<p>শিক্ষার্থী পাওয়া যায়নি (ID: $studentId)</p>";
}

// Close connection
$conn->close();
?>
