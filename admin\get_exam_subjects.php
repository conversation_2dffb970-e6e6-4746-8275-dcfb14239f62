<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

require_once '../includes/dbh.inc.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if exam_type_subjects table exists
$tableCheck = $conn->query("SHOW TABLES LIKE 'exam_type_subjects'");
if ($tableCheck->num_rows == 0) {
    // If table doesn't exist, return all subjects
    $subjectsQuery = "SELECT id, subject_name FROM subjects WHERE is_active = 1 ORDER BY subject_name";
    $subjects = $conn->query($subjectsQuery);

    $result = [];
    while ($subject = $subjects->fetch_assoc()) {
        $result[] = $subject;
    }

    echo json_encode($result);
    exit();
}

// Get parameters
$examType = isset($_GET['exam_type']) ? $_GET['exam_type'] : '';
$classId = isset($_GET['class_id']) ? intval($_GET['class_id']) : null;
$sessionId = isset($_GET['session_id']) ? intval($_GET['session_id']) : null;

// Handle department IDs
$departmentIds = [];
$allDepartments = false;

if (isset($_GET['department_ids'])) {
    if ($_GET['department_ids'] === 'all') {
        $allDepartments = true;
    } else {
        $departmentIdsParam = explode(',', $_GET['department_ids']);
        foreach ($departmentIdsParam as $id) {
            if (is_numeric($id)) {
                $departmentIds[] = intval($id);
            }
        }
    }
}

if (empty($examType)) {
    echo json_encode([]);
    exit();
}

// Get exam type ID
$examTypeQuery = "SELECT id FROM exam_types WHERE type_name = ?";
$stmt = $conn->prepare($examTypeQuery);
$stmt->bind_param("s", $examType);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    echo json_encode([]);
    exit();
}

$examTypeId = $result->fetch_assoc()['id'];

// Build query to get subjects for this exam type
$query = "SELECT DISTINCT s.id, s.subject_name
          FROM exam_type_subjects ets
          JOIN subjects s ON ets.subject_id = s.id
          WHERE ets.exam_type_id = ? AND s.is_active = 1";

$params = [$examTypeId];
$types = "i";

// Add class filter if provided
if (!empty($classId)) {
    $query .= " AND (ets.class_id = ? OR ets.class_id IS NULL)";
    $params[] = $classId;
    $types .= "i";
}

// Add department filter if provided
if (!$allDepartments) {
    if (!empty($departmentIds)) {
        // If multiple departments are selected
        if (count($departmentIds) > 1) {
            $placeholders = str_repeat('?,', count($departmentIds) - 1) . '?';
            $query .= " AND (ets.department_id IN ($placeholders) OR ets.department_id IS NULL)";
            foreach ($departmentIds as $deptId) {
                $params[] = $deptId;
                $types .= "i";
            }
        }
        // If only one department is selected
        elseif (count($departmentIds) == 1) {
            $query .= " AND (ets.department_id = ? OR ets.department_id IS NULL)";
            $params[] = $departmentIds[0];
            $types .= "i";
        }
    }
}

$query .= " ORDER BY s.subject_name";

$stmt = $conn->prepare($query);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();

$subjects = [];
while ($row = $result->fetch_assoc()) {
    $subjects[] = $row;
}

// If no subjects found for this exam type, return all subjects
if (empty($subjects)) {
    $subjectsQuery = "SELECT id, subject_name FROM subjects WHERE is_active = 1 ORDER BY subject_name";
    $allSubjects = $conn->query($subjectsQuery);

    $subjects = [];
    while ($subject = $allSubjects->fetch_assoc()) {
        $subjects[] = $subject;
    }
}

echo json_encode($subjects);
?>
