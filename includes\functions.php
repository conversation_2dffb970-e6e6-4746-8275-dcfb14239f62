<?php
// Function to sanitize user input
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Function to check if user is logged in
function is_logged_in() {
    return isset($_SESSION['user_id']);
}

// Function to check if user is admin
function is_admin() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

// Function to generate random ID
function generate_id($prefix = '', $length = 8) {
    $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $id = $prefix;
    for ($i = 0; $i < $length; $i++) {
        $id .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $id;
}

// Function to format date to Bengali
function format_bengali_date($date) {
    $english_months = array('January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December');
    $bengali_months = array('জানুয়ারি', 'ফেব্রুয়ারি', 'মার্চ', 'এপ্রিল', 'মে', 'জুন', 'জুলাই', 'আগস্ট', 'সেপ্টেম্বর', 'অক্টোবর', 'নভেম্বর', 'ডিসেম্বর');
    
    $formatted_date = date('d F Y', strtotime($date));
    return str_replace($english_months, $bengali_months, $formatted_date);
}

// Function to convert English numbers to Bengali
function en_to_bn_number($number) {
    $en = array('0', '1', '2', '3', '4', '5', '6', '7', '8', '9');
    $bn = array('০', '১', '২', '৩', '৪', '৫', '৬', '৭', '৮', '৯');
    return str_replace($en, $bn, $number);
}

// Function to get user details
function get_user_details($user_id) {
    global $conn;
    $query = "SELECT * FROM users WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->fetch_assoc();
}

// Function to check if a notice is expired
function is_notice_expired($expiry_date) {
    return strtotime($expiry_date) < strtotime('today');
}

// Function to get active notices for a specific audience
function get_active_notices($audience = 'all') {
    global $conn;
    $today = date('Y-m-d');
    $query = "SELECT * FROM notices 
              WHERE (target_audience = ? OR target_audience = 'all') 
              AND expiry_date >= ? 
              ORDER BY created_at DESC";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ss", $audience, $today);
    $stmt->execute();
    return $stmt->get_result();
}
?> 