-- Portable Database Backup for college_management - 2025-04-22 04:50:57
-- This backup can be used on any computer



CREATE TABLE `attendance` (
  `id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `class_id` int(11) NOT NULL,
  `subject_id` int(11) DEFAULT NULL,
  `date` date NOT NULL,
  `status` enum('present','absent','late','excused') DEFAULT 'absent',
  `remarks` text DEFAULT NULL,
  `recorded_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `book_issues` (
  `id` int(11) unsigned NOT NULL,
  `book_id` int(11) unsigned NOT NULL,
  `student_id` int(11) unsigned NOT NULL,
  `issue_date` date NOT NULL,
  `return_date` date DEFAULT NULL,
  `due_date` date NOT NULL,
  `returned` tinyint(1) NOT NULL DEFAULT 0,
  `fine_amount` decimal(10,2) DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `classes` (
  `id` int(11) NOT NULL,
  `class_name` varchar(50) NOT NULL,
  `department_id` int(11) DEFAULT NULL,
  `section` varchar(20) DEFAULT 'A',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `classes` VALUES("1","একাদশ",NULL,"A","2025-04-15 14:13:07");
INSERT INTO `classes` VALUES("2","দ্বাদশ",NULL,"A","2025-04-15 14:13:13");




CREATE TABLE `committee_members` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `position` varchar(255) NOT NULL,
  `details` text DEFAULT NULL,
  `photo` varchar(255) DEFAULT NULL,
  `priority` int(11) DEFAULT 100,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `committee_members` VALUES("1","মাননীয় অধ্যক্ষ","সভাপতি","অভিজ্ঞ শিক্ষাবিদ এবং প্রশাসক, ২০১০ সাল থেকে সংস্থার সাথে যুক্ত।",NULL,"1","2025-04-18 15:55:54");
INSERT INTO `committee_members` VALUES("2","মাননীয় সচিব","সদস্য সচিব","অভিজ্ঞ প্রশাসক এবং শিক্ষাবিদ, ২০১২ সাল থেকে সংস্থার সাথে যুক্ত।",NULL,"2","2025-04-18 15:55:54");
INSERT INTO `committee_members` VALUES("3","মোঃ আব্দুল কাদের","সদস্য","বিশিষ্ট ব্যবসায়ী এবং সমাজসেবক, ২০১৪ সাল থেকে সংস্থার সাথে যুক্ত।",NULL,"3","2025-04-18 15:55:54");




CREATE TABLE `courses` (
  `id` int(11) NOT NULL,
  `course_name` varchar(100) NOT NULL,
  `course_code` varchar(20) NOT NULL,
  `department_id` int(11) DEFAULT NULL,
  `credit_hours` decimal(4,2) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `courses` VALUES("1","বাংলা সাহিত্য","BNG101","1","3.00","বাংলা সাহিত্যের প্রাথমিক পাঠ","1","2025-04-15 14:05:57");
INSERT INTO `courses` VALUES("2","ইংরেজী ভাষা","ENG101","1","3.00","ইংরেজী ভাষার প্রাথমিক পাঠ","1","2025-04-15 14:05:57");
INSERT INTO `courses` VALUES("3","গণিত","MATH101","1","4.00","প্রাথমিক গণিত","1","2025-04-15 14:05:57");
INSERT INTO `courses` VALUES("4","পদার্থবিজ্ঞান","PHY101","1","3.50","পদার্থবিজ্ঞানের মৌলিক ধারণা","1","2025-04-15 14:05:57");
INSERT INTO `courses` VALUES("5","রসায়ন","CHEM101","1","3.50","রসায়নের মৌলিক ধারণা","1","2025-04-15 14:05:57");




CREATE TABLE `departments` (
  `id` int(11) NOT NULL,
  `department_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `departments` VALUES("1","সাধারণ বিভাগ","ডিফল্ট বিভাগ এটা সবার জন্য","2025-04-15 14:04:37");
INSERT INTO `departments` VALUES("2","বিজ্ঞান","বিজ্ঞান","2025-04-15 14:13:28");
INSERT INTO `departments` VALUES("3","ব্যবসায়","ব্যবসায়","2025-04-15 14:13:34");
INSERT INTO `departments` VALUES("4","মানবিক","মানবিক","2025-04-15 14:13:41");




CREATE TABLE `events` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `event_date` date NOT NULL,
  `event_time` time DEFAULT NULL,
  `venue` varchar(255) DEFAULT NULL,
  `organizer` varchar(255) DEFAULT NULL,
  `status` enum('upcoming','ongoing','completed','canceled') DEFAULT 'upcoming',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `exam_results` (
  `id` int(11) NOT NULL,
  `exam_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `marks_obtained` decimal(5,2) NOT NULL,
  `is_pass` tinyint(1) DEFAULT 0,
  `remarks` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `exam_schedule` (
  `id` int(11) NOT NULL,
  `exam_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `exam_date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `room_no` varchar(20) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `exam_subjects` (
  `id` int(11) NOT NULL,
  `exam_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `max_marks` int(11) DEFAULT 100,
  `passing_marks` int(11) DEFAULT 33,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `total_marks` int(11) NOT NULL DEFAULT 100,
  `cq_marks` int(11) NOT NULL DEFAULT 60,
  `mcq_marks` int(11) NOT NULL DEFAULT 30,
  `practical_marks` int(11) NOT NULL DEFAULT 10
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `exam_subjects` VALUES("1","2","1","100","33","2025-04-15 17:38:52","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("2","2","2","100","33","2025-04-15 17:38:52","100","100","0","0");
INSERT INTO `exam_subjects` VALUES("3","2","3","100","33","2025-04-15 17:38:52","100","50","25","25");
INSERT INTO `exam_subjects` VALUES("4","2","14","100","33","2025-04-15 17:38:52","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("5","2","15","100","33","2025-04-15 17:38:52","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("6","2","21","100","33","2025-04-15 17:38:52","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("7","2","19","100","33","2025-04-15 17:38:52","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("8","2","22","100","33","2025-04-15 17:38:52","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("9","2","12","100","33","2025-04-15 17:38:52","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("10","2","6","100","33","2025-04-15 17:38:53","100","50","25","25");
INSERT INTO `exam_subjects` VALUES("11","2","7","100","33","2025-04-15 17:38:53","100","50","25","25");
INSERT INTO `exam_subjects` VALUES("12","2","4","100","33","2025-04-15 17:38:53","100","50","25","25");
INSERT INTO `exam_subjects` VALUES("13","2","8","100","33","2025-04-15 17:38:53","100","50","25","25");
INSERT INTO `exam_subjects` VALUES("14","2","9","100","33","2025-04-15 17:38:53","100","50","25","25");
INSERT INTO `exam_subjects` VALUES("15","2","20","100","33","2025-04-15 17:38:53","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("16","2","13","100","33","2025-04-15 17:38:53","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("17","2","11","100","33","2025-04-15 17:38:53","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("18","2","16","100","33","2025-04-15 17:38:53","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("19","2","5","100","33","2025-04-15 17:38:53","100","50","25","25");
INSERT INTO `exam_subjects` VALUES("20","2","18","100","33","2025-04-15 17:38:53","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("21","2","17","100","33","2025-04-15 17:38:53","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("22","2","10","100","33","2025-04-15 17:38:53","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("23","1","1","100","33","2025-04-19 08:39:09","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("24","1","2","100","33","2025-04-19 08:39:09","100","100","0","0");
INSERT INTO `exam_subjects` VALUES("25","1","3","100","33","2025-04-19 08:39:09","100","50","25","25");
INSERT INTO `exam_subjects` VALUES("26","1","14","100","33","2025-04-19 08:39:09","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("27","1","15","100","33","2025-04-19 08:39:09","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("28","1","21","100","33","2025-04-19 08:39:09","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("29","1","19","100","33","2025-04-19 08:39:09","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("30","1","22","100","33","2025-04-19 08:39:09","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("31","1","12","100","33","2025-04-19 08:39:09","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("32","1","6","100","33","2025-04-19 08:39:09","100","50","25","25");
INSERT INTO `exam_subjects` VALUES("33","1","7","100","33","2025-04-19 08:39:09","100","50","25","25");
INSERT INTO `exam_subjects` VALUES("34","1","4","100","33","2025-04-19 08:39:09","100","50","25","25");
INSERT INTO `exam_subjects` VALUES("35","1","8","100","33","2025-04-19 08:39:09","100","50","25","25");
INSERT INTO `exam_subjects` VALUES("36","1","9","100","33","2025-04-19 08:39:09","100","50","25","25");
INSERT INTO `exam_subjects` VALUES("37","1","20","100","33","2025-04-19 08:39:09","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("38","1","13","100","33","2025-04-19 08:39:09","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("39","1","11","100","33","2025-04-19 08:39:09","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("40","1","16","100","33","2025-04-19 08:39:09","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("41","1","5","100","33","2025-04-19 08:39:09","100","50","25","25");
INSERT INTO `exam_subjects` VALUES("42","1","18","100","33","2025-04-19 08:39:10","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("43","1","17","100","33","2025-04-19 08:39:10","100","70","30","0");
INSERT INTO `exam_subjects` VALUES("44","1","10","100","33","2025-04-19 08:39:10","100","70","30","0");




CREATE TABLE `exams` (
  `id` int(11) NOT NULL,
  `exam_name` varchar(100) NOT NULL,
  `course_name` varchar(100) NOT NULL,
  `exam_date` date NOT NULL,
  `total_marks` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `exams` VALUES("1","মধ্যবর্তী পরীক্ষা","বাংলা সাহিত্য","2025-04-25","100","2025-04-15 14:05:56");
INSERT INTO `exams` VALUES("2","সেমেস্টার ফাইনাল","ইংরেজী ভাষা","2025-05-15","100","2025-04-15 14:05:56");




CREATE TABLE `fee_map_class` (
  `id` int(11) NOT NULL,
  `fee_type_id` int(11) NOT NULL,
  `class_id` int(11) NOT NULL,
  `academic_year` varchar(20) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `fee_map_session` (
  `id` int(11) NOT NULL,
  `fee_type_id` int(11) NOT NULL,
  `session_name` varchar(50) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `fee_map_student` (
  `id` int(11) NOT NULL,
  `fee_type_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `fee_payments` (
  `id` int(11) NOT NULL,
  `fee_id` int(11) NOT NULL,
  `receipt_id` int(11) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_date` date NOT NULL,
  `payment_method` varchar(50) DEFAULT 'cash',
  `receipt_no` varchar(50) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `payment_amount` decimal(10,2) GENERATED ALWAYS AS (`amount`) STORED
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `fee_types` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_recurring` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `amount` decimal(10,2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `fee_types` VALUES("1","মাসিক বেতন","প্রতিমাসে দিতে হবে","1","2025-04-16 13:11:42","150.00");
INSERT INTO `fee_types` VALUES("2","অর্ধ বার্ষিক পরীক্ষা","একাদশ","0","2025-04-16 13:12:45","450.00");




CREATE TABLE `fees` (
  `id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `fee_type` varchar(50) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `paid` decimal(10,2) DEFAULT 0.00,
  `due_date` date NOT NULL,
  `payment_status` enum('due','partial','paid','overpaid') DEFAULT 'due',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `payment_date` date DEFAULT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `fourth_subject_config` (
  `id` int(11) NOT NULL,
  `excess_point_limit` float DEFAULT 2,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `fourth_subject_config` VALUES("1","2","1","2025-04-16 07:21:36","2025-04-16 07:21:36");




CREATE TABLE `library_books` (
  `id` int(11) unsigned NOT NULL,
  `title` varchar(255) NOT NULL,
  `author` varchar(255) NOT NULL,
  `isbn` varchar(50) DEFAULT NULL,
  `publication_year` int(4) DEFAULT NULL,
  `category` varchar(100) DEFAULT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `available_quantity` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `marks` (
  `id` int(11) NOT NULL,
  `exam_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `cq_marks` decimal(10,2) DEFAULT 0.00,
  `mcq_marks` decimal(10,2) DEFAULT 0.00,
  `practical_marks` decimal(10,2) DEFAULT 0.00,
  `total_marks` decimal(10,2) DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `notices` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `date` date NOT NULL,
  `added_by` varchar(50) DEFAULT NULL,
  `attachment_path` varchar(255) DEFAULT NULL,
  `attachment_type` varchar(10) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `notices` VALUES("3","পরীক্ষা অর্ধ বাষিক","পরীক্ষা অর্ধ বাষিক","2025-04-18","admin","uploads/notices/68021c35f0c17.pdf",NULL,"2025-04-18 15:32:37");
INSERT INTO `notices` VALUES("4","d","d","2025-04-18","admin","uploads/notices/68021cb5903c8.pdf","pdf","2025-04-18 15:34:45");




CREATE TABLE `passing_marks_config` (
  `id` int(11) NOT NULL,
  `subject_id` int(11) DEFAULT NULL,
  `min_percentage` decimal(5,2) NOT NULL,
  `max_percentage` decimal(5,2) NOT NULL,
  `passing_mark` decimal(5,2) NOT NULL,
  `cq_passing_percent` decimal(5,2) NOT NULL DEFAULT 33.00,
  `mcq_passing_percent` decimal(5,2) NOT NULL DEFAULT 33.00,
  `practical_passing_percent` decimal(5,2) NOT NULL DEFAULT 33.00,
  `grade` varchar(5) NOT NULL,
  `grade_point` decimal(3,2) NOT NULL,
  `description` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `passing_marks_config` VALUES("1",NULL,"80.00","100.00","80.00","40.00","40.00","40.00","A+","5.00","শ্রেষ্ঠত্ব (Excellence)","2025-04-15 17:39:20","2025-04-15 17:39:20");
INSERT INTO `passing_marks_config` VALUES("2",NULL,"70.00","79.99","70.00","35.00","35.00","35.00","A","4.00","অতি উত্তম (Very Good)","2025-04-15 17:39:20","2025-04-15 17:39:20");
INSERT INTO `passing_marks_config` VALUES("3",NULL,"60.00","69.99","60.00","33.00","33.00","33.00","A-","3.50","উত্তম (Good)","2025-04-15 17:39:20","2025-04-15 17:39:20");
INSERT INTO `passing_marks_config` VALUES("4",NULL,"50.00","59.99","50.00","33.00","33.00","33.00","B","3.00","ভালো (Satisfactory)","2025-04-15 17:39:20","2025-04-15 17:39:20");
INSERT INTO `passing_marks_config` VALUES("5",NULL,"40.00","49.99","40.00","33.00","33.00","33.00","C","2.00","মোটামুটি (Average)","2025-04-15 17:39:21","2025-04-15 17:39:21");
INSERT INTO `passing_marks_config` VALUES("6",NULL,"33.00","39.99","33.00","33.00","33.00","33.00","D","1.00","নিম্নমান (Poor)","2025-04-15 17:39:21","2025-04-15 17:39:21");
INSERT INTO `passing_marks_config` VALUES("7",NULL,"0.00","32.99","0.00","0.00","0.00","0.00","F","0.00","অকৃতকার্য (Fail)","2025-04-15 17:39:21","2025-04-15 17:39:21");




CREATE TABLE `payment_receipts` (
  `id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `payment_date` date NOT NULL,
  `payment_method` varchar(50) DEFAULT 'cash',
  `total_amount` decimal(10,2) NOT NULL,
  `receipt_no` varchar(50) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `results` (
  `id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `exam_id` int(11) NOT NULL,
  `marks_obtained` float NOT NULL,
  `grade` varchar(5) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `school_settings` (
  `id` int(11) NOT NULL,
  `school_name` varchar(255) NOT NULL,
  `school_address` text DEFAULT NULL,
  `school_phone` varchar(50) DEFAULT NULL,
  `school_email` varchar(100) DEFAULT NULL,
  `logo_path` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `school_settings` VALUES("1","দামুড়হুদা কলেজ","দামুড়হুদা","০১৭১৭৮৬১৭৬২","<EMAIL>","uploads/logos/1744788813_IMG_20240517_184559.jpg","2025-04-16 13:30:37","2025-04-16 13:33:33");




CREATE TABLE `sessions` (
  `id` int(11) NOT NULL,
  `session_name` varchar(50) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `is_active` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `sessions` VALUES("1","2025-2026","2025-01-01","2026-12-31","1","2025-04-15 14:04:37");
INSERT INTO `sessions` VALUES("2","2026-2027","2026-01-01","2027-12-31","0","2025-04-16 12:33:28");




CREATE TABLE `staff` (
  `id` int(11) NOT NULL,
  `staff_id` varchar(20) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `gender` enum('male','female','other') NOT NULL,
  `dob` date DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(50) DEFAULT NULL,
  `state` varchar(50) DEFAULT NULL,
  `postal_code` varchar(20) DEFAULT NULL,
  `country` varchar(50) DEFAULT 'Bangladesh',
  `joining_date` date DEFAULT NULL,
  `profile_photo` varchar(255) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `role` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `staff` VALUES("1","STF001","Nasrin","Akter","<EMAIL>","01812345678","female",NULL,NULL,NULL,NULL,NULL,"Bangladesh","2025-04-15",NULL,"1","4","2025-04-15 14:11:17",NULL);
INSERT INTO `staff` VALUES("2","stf01","dulal chandra devnath","MLSS","<EMAIL>","01977861762","male",NULL,NULL,NULL,NULL,NULL,"Bangladesh","2025-04-01",NULL,"1",NULL,"2025-04-16 13:08:00","Office Assictance");




CREATE TABLE `student_subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `category` varchar(50) NOT NULL,
  `selection_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `session_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `student_id` (`student_id`,`subject_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `student_subjects` VALUES("1","2","1","required","2025-04-22 07:34:36",NULL);




CREATE TABLE `students` (
  `id` int(11) NOT NULL,
  `student_id` varchar(20) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `gender` enum('male','female','other') NOT NULL,
  `dob` date DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(50) DEFAULT NULL,
  `state` varchar(50) DEFAULT NULL,
  `postal_code` varchar(20) DEFAULT NULL,
  `country` varchar(50) DEFAULT 'Bangladesh',
  `admission_date` date DEFAULT NULL,
  `profile_photo` varchar(255) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `class_id` int(11) DEFAULT NULL,
  `session_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `role` varchar(50) DEFAULT NULL,
  `roll_number` varchar(20) DEFAULT NULL,
  `guardian_name` varchar(100) DEFAULT NULL,
  `guardian_relation` varchar(50) DEFAULT NULL,
  `guardian_phone` varchar(20) DEFAULT NULL,
  `guardian_email` varchar(100) DEFAULT NULL,
  `guardian_address` text DEFAULT NULL,
  `guardian_occupation` varchar(100) DEFAULT NULL,
  `batch` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `students` VALUES("2","202501","Md. Bidhan","Mollick","<EMAIL>","1717861762","male","2004-04-15","Chuadanga",NULL,NULL,NULL,"Bangladesh","2025-04-15","uploads/profile_photos/67fe4b8fefa93.jpg","4","1","1","8","2025-04-15 18:05:36","Regular Student","1",NULL,NULL,NULL,NULL,NULL,NULL,"2025");




CREATE TABLE `subject_departments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` int(11) NOT NULL,
  `department_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `subject_id` (`subject_id`,`department_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `subject_departments` VALUES("1","1","1");




CREATE TABLE `subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_name` varchar(100) NOT NULL,
  `subject_code` varchar(20) NOT NULL,
  `department_id` int(11) DEFAULT NULL,
  `category` varchar(255) DEFAULT 'required',
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `subjects` VALUES("1","Mathematics","MATH-101",NULL,"required","Basic mathematics course","1","2025-04-22 07:31:44");




CREATE TABLE `teachers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `teacher_id` varchar(20) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `gender` enum('male','female','other') NOT NULL,
  `dob` date DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(50) DEFAULT NULL,
  `state` varchar(50) DEFAULT NULL,
  `postal_code` varchar(20) DEFAULT NULL,
  `country` varchar(50) DEFAULT 'Bangladesh',
  `joining_date` date DEFAULT NULL,
  `profile_photo` varchar(255) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `subject` varchar(100) DEFAULT NULL,
  `designation` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `teacher_id` (`teacher_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `teachers` VALUES("1","TCH-001","Rahim","Ahmed","<EMAIL>","01712345678","male",NULL,NULL,NULL,NULL,NULL,"Bangladesh","2025-04-22",NULL,"1",NULL,"2025-04-22 07:27:53",NULL,NULL);




CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `user_type` enum('admin','teacher','student','staff') NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_login` timestamp NULL DEFAULT NULL,
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `role` varchar(20) NOT NULL DEFAULT 'student',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `users` VALUES("1","admin","$2y$10$4u8yVjXVYBhTVn/txxWod.VYwCfT4ONYHicny1Hu4yOpC2MKf0J6i","admin","<EMAIL>","2025-04-22 07:21:01",NULL,"active","student");


