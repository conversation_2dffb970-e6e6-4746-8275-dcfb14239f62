<?php
session_start();

// Check if user is logged in and is a teacher
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'teacher') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get teacher information with department name
$userId = $_SESSION['userId'];
$username = $_SESSION['username'] ?? '';

// Get teacher info
$sql = "SELECT t.*, d.department_name
        FROM teachers t
        LEFT JOIN departments d ON t.department_id = d.id
        WHERE t.user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();
$teacher = $result->fetch_assoc();

// Get upcoming exams
try {
    $upcomingExamsQuery = "SELECT e.*, s.subject_name, c.class_name
                          FROM exams e
                          LEFT JOIN subjects s ON e.subject_id = s.id
                          LEFT JOIN classes c ON e.class_id = c.id
                          WHERE e.department_id = ? AND e.exam_date >= CURDATE()
                          ORDER BY e.exam_date ASC
                          LIMIT 5";
    $stmt = $conn->prepare($upcomingExamsQuery);
    $stmt->bind_param("i", $teacher['department_id']);
    $stmt->execute();
    $upcomingExams = $stmt->get_result();
} catch (Exception $e) {
    $upcomingExams = null;
}

// Get recent results
try {
    $recentResultsQuery = "SELECT r.*, e.exam_name, s.student_id, s.first_name, s.last_name
                          FROM results r
                          JOIN exams e ON r.exam_id = e.id
                          JOIN students s ON r.student_id = s.id
                          WHERE e.department_id = ?
                          ORDER BY r.created_at DESC
                          LIMIT 5";
    $stmt = $conn->prepare($recentResultsQuery);
    $stmt->bind_param("i", $teacher['department_id']);
    $stmt->execute();
    $recentResults = $stmt->get_result();
} catch (Exception $e) {
    $recentResults = null;
}

// Count total exams
try {
    $totalExamsQuery = "SELECT COUNT(*) as total FROM exams WHERE department_id = ?";
    $stmt = $conn->prepare($totalExamsQuery);
    $stmt->bind_param("i", $teacher['department_id']);
    $stmt->execute();
    $totalExamsResult = $stmt->get_result();
    $totalExams = $totalExamsResult->fetch_assoc()['total'];
} catch (Exception $e) {
    $totalExams = 0;
}

// Count total results
try {
    $totalResultsQuery = "SELECT COUNT(*) as total 
                         FROM results r
                         JOIN exams e ON r.exam_id = e.id
                         WHERE e.department_id = ?";
    $stmt = $conn->prepare($totalResultsQuery);
    $stmt->bind_param("i", $teacher['department_id']);
    $stmt->execute();
    $totalResultsResult = $stmt->get_result();
    $totalResults = $totalResultsResult->fetch_assoc()['total'];
} catch (Exception $e) {
    $totalResults = 0;
}

include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include_once 'sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">পরীক্ষা ব্যবস্থাপনা ড্যাশবোর্ড</h1>
            </div>
            
            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <h5 class="card-title">মোট পরীক্ষা</h5>
                            <h2 class="display-4"><?php echo $totalExams; ?></h2>
                            <p class="card-text"><i class="fas fa-file-alt"></i> সকল পরীক্ষার সংখ্যা</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <h5 class="card-title">আসন্ন পরীক্ষা</h5>
                            <h2 class="display-4"><?php echo $upcomingExams ? $upcomingExams->num_rows : 0; ?></h2>
                            <p class="card-text"><i class="fas fa-calendar-alt"></i> আগামী দিনের পরীক্ষা</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body">
                            <h5 class="card-title">ফলাফল সংখ্যা</h5>
                            <h2 class="display-4"><?php echo $totalResults; ?></h2>
                            <p class="card-text"><i class="fas fa-chart-bar"></i> মোট ফলাফল</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <h5 class="card-title">বিভাগ</h5>
                            <h2 class="display-4">1</h2>
                            <p class="card-text"><i class="fas fa-building"></i> <?php echo $teacher['department_name']; ?></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-dark text-white">
                            <h5 class="mb-0">দ্রুত কার্যক্রম</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-2">
                                    <a href="exams.php" class="btn btn-primary btn-lg w-100">
                                        <i class="fas fa-list me-2"></i> পরীক্ষা তালিকা
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="exams.php" class="btn btn-success btn-lg w-100">
                                        <i class="fas fa-plus-circle me-2"></i> নতুন পরীক্ষা
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="add_results.php" class="btn btn-warning btn-lg w-100">
                                        <i class="fas fa-plus-circle me-2"></i> ফলাফল যোগ
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="results.php" class="btn btn-info btn-lg w-100">
                                        <i class="fas fa-chart-bar me-2"></i> ফলাফল দেখুন
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Upcoming Exams and Recent Results -->
            <div class="row">
                <!-- Upcoming Exams -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">আসন্ন পরীক্ষাসমূহ</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>পরীক্ষার নাম</th>
                                            <th>বিষয়</th>
                                            <th>তারিখ</th>
                                            <th>মোট নম্বর</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if ($upcomingExams && $upcomingExams->num_rows > 0): ?>
                                            <?php while ($exam = $upcomingExams->fetch_assoc()): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($exam['exam_name']); ?></td>
                                                    <td><?php echo htmlspecialchars($exam['subject_name'] ?? 'N/A'); ?></td>
                                                    <td><?php echo date('d M Y', strtotime($exam['exam_date'])); ?></td>
                                                    <td><?php echo $exam['total_marks']; ?></td>
                                                </tr>
                                            <?php endwhile; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="4" class="text-center">কোন আসন্ন পরীক্ষা পাওয়া যায়নি</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-end mt-2">
                                <a href="exams.php" class="btn btn-sm btn-warning">সব দেখুন <i class="fas fa-arrow-right ms-1"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Results -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">সাম্প্রতিক ফলাফল</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>পরীক্ষা</th>
                                            <th>শিক্ষার্থী</th>
                                            <th>প্রাপ্ত নম্বর</th>
                                            <th>শতকরা</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if ($recentResults && $recentResults->num_rows > 0): ?>
                                            <?php while ($result = $recentResults->fetch_assoc()):
                                                $percentage = ($result['marks_obtained'] / $result['total_marks']) * 100;
                                            ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($result['exam_name']); ?></td>
                                                    <td><?php echo htmlspecialchars($result['first_name'] . ' ' . $result['last_name']); ?></td>
                                                    <td><?php echo $result['marks_obtained']; ?>/<?php echo $result['total_marks']; ?></td>
                                                    <td><?php echo number_format($percentage, 2); ?>%</td>
                                                </tr>
                                            <?php endwhile; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="4" class="text-center">কোন সাম্প্রতিক ফলাফল পাওয়া যায়নি</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-end mt-2">
                                <a href="results.php" class="btn btn-sm btn-success">সব দেখুন <i class="fas fa-arrow-right ms-1"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
