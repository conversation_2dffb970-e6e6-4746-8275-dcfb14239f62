<?php
// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "college_management";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Create a teacher user first
$teacherUserSql = "INSERT INTO users (username, password, user_type) 
                VALUES ('teacher', ?, 'teacher')";
$hashedPassword = password_hash('teacher123', PASSWORD_DEFAULT);

$stmt = $conn->prepare($teacherUserSql);
$stmt->bind_param("s", $hashedPassword);

if ($stmt->execute()) {
    $userId = $conn->insert_id;
    echo "Teacher user created successfully.<br>";
    
    // Make sure we have at least one department
    $checkDeptSql = "SELECT id FROM departments LIMIT 1";
    $deptResult = $conn->query($checkDeptSql);
    
    $deptId = null;
    if ($deptResult->num_rows == 0) {
        // Create a department
        $createDeptSql = "INSERT INTO departments (department_name, description) 
                        VALUES ('Science', 'Science Department')";
        if ($conn->query($createDeptSql)) {
            $deptId = $conn->insert_id;
            echo "Department created successfully.<br>";
        }
    } else {
        $deptRow = $deptResult->fetch_assoc();
        $deptId = $deptRow['id'];
    }
    
    // Now create the teacher
    $teacherSql = "INSERT INTO teachers (teacher_id, first_name, last_name, email, phone, 
                gender, joining_date, department_id, user_id) 
                VALUES ('TCH001', 'Mahbub', 'Rahman', '<EMAIL>', '01712345678', 
                'male', CURDATE(), ?, ?)";
                
    $stmt = $conn->prepare($teacherSql);
    $stmt->bind_param("ii", $deptId, $userId);
    
    if ($stmt->execute()) {
        echo "Sample teacher added successfully.<br>";
        echo "<p>Teacher login credentials:</p>";
        echo "<p>Username: teacher</p>";
        echo "<p>Password: teacher123</p>";
        echo "<p><a href='admin/dashboard.php'>Go to Admin Dashboard</a></p>";
    } else {
        echo "Error adding teacher: " . $stmt->error;
    }
    
} else {
    echo "Error creating teacher user: " . $stmt->error;
}

$conn->close();
?> 