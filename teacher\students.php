<?php
session_start();

// Check if user is logged in and is a teacher
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'teacher') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get teacher information with department name
$userId = $_SESSION['userId'];
$username = $_SESSION['username'] ?? '';

// First try to get teacher by user_id
$sql = "SELECT t.*, d.department_name 
        FROM teachers t
        LEFT JOIN departments d ON t.department_id = d.id
        WHERE t.user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();

// If no results, try to get by username
if ($result->num_rows === 0 && !empty($username)) {
    $sql = "SELECT t.*, d.department_name 
            FROM teachers t
            LEFT JOIN departments d ON t.department_id = d.id
            WHERE t.username = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
}

$teacher = $result->fetch_assoc();

// If still no teacher found, redirect to login
if (!$teacher) {
    header("Location: ../index.php");
    exit();
}

// Get students assigned to this teacher's department
try {
    if (!empty($teacher['department_id'])) {
        $studentsQuery = "SELECT s.*, c.class_name
                        FROM students s
                        LEFT JOIN classes c ON s.class_id = c.id
                        WHERE s.department_id = ?
                        ORDER BY s.batch, s.first_name, s.last_name";
        $stmt = $conn->prepare($studentsQuery);
        $stmt->bind_param("i", $teacher['department_id']);
        $stmt->execute();
        $students = $stmt->get_result();
    } else {
        // If no department_id, show empty result
        $students = null;
    }
} catch (Exception $e) {
    // Handle error gracefully
    $students = null;
    error_log("Error fetching students: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include __DIR__ . '/includes/global-head.php'; ?>
    <title>Students - Teacher Panel</title>
    
    <style>
        body, html, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, select, textarea, label, li, th, td {
            font-family: 'Hind Siliguri', sans-serif;
        }
        
        .student-search {
            margin-bottom: 20px;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>Teacher Panel</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user me-2"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> Students
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> Courses
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> Exams
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> Results
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">
                            <i class="fas fa-calendar-check me-2"></i> Attendance
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h2 class="mb-4">শিক্ষার্থী তালিকা</h2>
                        
                        <div class="card">
                            <div class="card-body">
                                <div class="student-search">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <input type="text" id="studentSearch" class="form-control" placeholder="শিক্ষার্থী খুঁজুন (নাম, আইডি, ব্যাচ)">
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <select id="batchFilter" class="form-control">
                                                <option value="">সকল ব্যাচ</option>
                                                <?php
                                                if ($students && $students->num_rows > 0) {
                                                    // Reset pointer to beginning
                                                    $students->data_seek(0);
                                                    $batches = [];
                                                    
                                                    while ($student = $students->fetch_assoc()) {
                                                        if (!empty($student['batch']) && !in_array($student['batch'], $batches)) {
                                                            $batches[] = $student['batch'];
                                                            echo '<option value="' . $student['batch'] . '">' . $student['batch'] . '</option>';
                                                        }
                                                    }
                                                    
                                                    // Reset pointer to beginning again
                                                    $students->data_seek(0);
                                                }
                                                ?>
                                            </select>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <select id="classFilter" class="form-control">
                                                <option value="">সকল শ্রেণী</option>
                                                <?php
                                                if ($students && $students->num_rows > 0) {
                                                    // Reset pointer to beginning
                                                    $students->data_seek(0);
                                                    $classes = [];
                                                    
                                                    while ($student = $students->fetch_assoc()) {
                                                        if (!empty($student['class_name']) && !in_array($student['class_name'], $classes)) {
                                                            $classes[] = $student['class_name'];
                                                            echo '<option value="' . $student['class_name'] . '">' . $student['class_name'] . '</option>';
                                                        }
                                                    }
                                                    
                                                    // Reset pointer to beginning again
                                                    $students->data_seek(0);
                                                }
                                                ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="table-responsive">
                                    <table class="table table-hover" id="studentsTable">
                                        <thead>
                                            <tr>
                                                <th>আইডি</th>
                                                <th>নাম</th>
                                                <th>শ্রেণী</th>
                                                <th>ব্যাচ</th>
                                                <th>ইমেইল</th>
                                                <th>ফোন</th>
                                                <th>অ্যাকশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($students && $students->num_rows > 0): ?>
                                                <?php while ($student = $students->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($student['student_id']); ?></td>
                                                        <td><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($student['class_name'] ?? 'N/A'); ?></td>
                                                        <td><?php echo htmlspecialchars($student['batch'] ?? 'N/A'); ?></td>
                                                        <td><?php echo htmlspecialchars($student['email'] ?? 'N/A'); ?></td>
                                                        <td><?php echo htmlspecialchars($student['phone'] ?? 'N/A'); ?></td>
                                                        <td>
                                                            <a href="view_student.php?id=<?php echo $student['id']; ?>" class="btn btn-sm btn-primary">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="7" class="text-center">কোন শিক্ষার্থী খুঁজে পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Search and filter functionality
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('studentSearch');
            const batchFilter = document.getElementById('batchFilter');
            const classFilter = document.getElementById('classFilter');
            const table = document.getElementById('studentsTable');
            const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
            
            function filterTable() {
                const searchText = searchInput.value.toLowerCase();
                const batchValue = batchFilter.value.toLowerCase();
                const classValue = classFilter.value.toLowerCase();
                
                for (let i = 0; i < rows.length; i++) {
                    const row = rows[i];
                    
                    // Skip the "No students found" row
                    if (row.cells.length === 1) continue;
                    
                    const id = row.cells[0].textContent.toLowerCase();
                    const name = row.cells[1].textContent.toLowerCase();
                    const className = row.cells[2].textContent.toLowerCase();
                    const batch = row.cells[3].textContent.toLowerCase();
                    
                    const matchesSearch = id.includes(searchText) || name.includes(searchText) || batch.includes(searchText);
                    const matchesBatch = batchValue === '' || batch.includes(batchValue);
                    const matchesClass = classValue === '' || className.includes(classValue);
                    
                    row.style.display = (matchesSearch && matchesBatch && matchesClass) ? '' : 'none';
                }
            }
            
            searchInput.addEventListener('keyup', filterTable);
            batchFilter.addEventListener('change', filterTable);
            classFilter.addEventListener('change', filterTable);
        });
    </script>
</body>
</html>
