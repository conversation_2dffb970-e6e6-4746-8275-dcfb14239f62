<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$errorMessage = "";
$successMessage = "";

// Get student ID from URL
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: students.php");
    exit();
}

$studentId = $_GET['id'];

// Get departments for the dropdown
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Get classes for the dropdown
$classesQuery = "SELECT * FROM classes ORDER BY class_name";
$classes = $conn->query($classesQuery);

// Get sessions for the dropdown
$sessionsQuery = "SELECT * FROM sessions ORDER BY session_name";
$sessions = $conn->query($sessionsQuery);

// Get student data
$studentQuery = "SELECT s.*, u.username FROM students s
                LEFT JOIN users u ON s.user_id = u.id
                WHERE s.id = ?";
$stmt = $conn->prepare($studentQuery);
$stmt->bind_param("i", $studentId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header("Location: students.php");
    exit();
}

$student = $result->fetch_assoc();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $student_id = $_POST['student_id'];
    $firstName = $_POST['first_name'];
    $lastName = $_POST['last_name'];
    $email = $_POST['email'];
    $phone = $_POST['phone'];
    $address = $_POST['address'];
    $dob = $_POST['dob'];
    $gender = $_POST['gender'];
    $batch = $_POST['batch'];
    $roll_number = $_POST['roll_number'];
    $department_id = $_POST['department_id'];
    $class_id = isset($_POST['class_id']) ? $_POST['class_id'] : null;
    $session_id = isset($_POST['session_id']) ? $_POST['session_id'] : null;
    $admissionDate = $_POST['admission_date'];
    $username = $_POST['username'];
    $password = $_POST['password'];
    $student_role = isset($_POST['student_role']) ? $_POST['student_role'] : null;

    // Validate input
    $errors = [];

    if (empty($student_id)) {
        $errors[] = "শিক্ষার্থী আইডি আবশ্যক";
    }

    if (empty($firstName)) {
        $errors[] = "নাম আবশ্যক";
    }

    if (empty($email)) {
        $errors[] = "ইমেইল আবশ্যক";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "সঠিক ইমেইল ঠিকানা দিন";
    }

    if (empty($errors)) {
        // Begin transaction
        $conn->begin_transaction();

        try {
            // Update student information
            $studentQuery = "UPDATE students SET
                          student_id = ?,
                          first_name = ?,
                          last_name = ?,
                          email = ?,
                          phone = ?,
                          address = ?,
                          dob = ?,
                          gender = ?,
                          batch = ?,
                          roll_number = ?,
                          department_id = ?,
                          class_id = ?,
                          session_id = ?,
                          admission_date = ?,
                          role = ?
                          WHERE id = ?";

            // Prepare and execute the query
            $stmt = $conn->prepare($studentQuery);
            $stmt->bind_param("ssssssssssiisisi",
                $student_id,
                $firstName,
                $lastName,
                $email,
                $phone,
                $address,
                $dob,
                $gender,
                $batch,
                $roll_number,
                $department_id,
                $class_id,
                $session_id,
                $admissionDate,
                $student_role,
                $studentId);
            $stmt->execute();

            // Update user information if username is provided
            if (!empty($username)) {
                // Check if user exists
                $userQuery = "SELECT id FROM users WHERE id = ?";
                $stmt = $conn->prepare($userQuery);
                $stmt->bind_param("i", $student['user_id']);
                $stmt->execute();
                $userResult = $stmt->get_result();

                if ($userResult->num_rows > 0) {
                    // Update existing user
                    $updateUserQuery = "UPDATE users SET username = ? WHERE id = ?";
                    $stmt = $conn->prepare($updateUserQuery);
                    $stmt->bind_param("si", $username, $student['user_id']);
                    $stmt->execute();

                    // Update password if provided
                    if (!empty($password)) {
                        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                        $updatePasswordQuery = "UPDATE users SET password = ? WHERE id = ?";
                        $stmt = $conn->prepare($updatePasswordQuery);
                        $stmt->bind_param("si", $hashedPassword, $student['user_id']);
                        $stmt->execute();
                    }
                } else {
                    // Create new user if password is provided
                    if (!empty($password)) {
                        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                        $createUserQuery = "INSERT INTO users (username, password, user_type) VALUES (?, ?, 'student')";
                        $stmt = $conn->prepare($createUserQuery);
                        $stmt->bind_param("ss", $username, $hashedPassword);
                        $stmt->execute();
                        $userId = $conn->insert_id;

                        // Link user to student
                        $linkUserQuery = "UPDATE students SET user_id = ? WHERE id = ?";
                        $stmt = $conn->prepare($linkUserQuery);
                        $stmt->bind_param("ii", $userId, $studentId);
                        $stmt->execute();
                    }
                }
            }

            // Handle profile photo upload
            if (isset($_FILES['profile_photo']) && $_FILES['profile_photo']['error'] === 0) {
                $uploadDir = '../uploads/students/';

                // Create directory if it doesn't exist
                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }

                $fileName = time() . '_' . basename($_FILES['profile_photo']['name']);
                $targetFilePath = $uploadDir . $fileName;
                $fileType = pathinfo($targetFilePath, PATHINFO_EXTENSION);

                // Allow certain file formats
                $allowTypes = array('jpg', 'png', 'jpeg', 'gif');
                if (in_array(strtolower($fileType), $allowTypes)) {
                    // Upload file to server
                    if (move_uploaded_file($_FILES['profile_photo']['tmp_name'], $targetFilePath)) {
                        // Update profile photo path in database
                        $photoPath = 'uploads/students/' . $fileName;
                        $updatePhotoQuery = "UPDATE students SET profile_photo = ? WHERE id = ?";
                        $stmt = $conn->prepare($updatePhotoQuery);
                        $stmt->bind_param("si", $photoPath, $studentId);
                        $stmt->execute();
                    } else {
                        $errors[] = "ছবি আপলোড করতে সমস্যা হয়েছে।";
                    }
                } else {
                    $errors[] = "শুধুমাত্র JPG, JPEG, PNG, & GIF ফাইল আপলোড করা যাবে।";
                }
            }

            // Commit transaction
            $conn->commit();
            $successMessage = "শিক্ষার্থীর তথ্য সফলভাবে আপডেট করা হয়েছে!";

            // Refresh student data
            $refreshQuery = "SELECT s.*, u.username FROM students s
                            LEFT JOIN users u ON s.user_id = u.id
                            WHERE s.id = ?";
            $stmt = $conn->prepare($refreshQuery);
            $stmt->bind_param("i", $studentId);
            $stmt->execute();
            $result = $stmt->get_result();
            $student = $result->fetch_assoc();

        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            $errorMessage = "শিক্ষার্থীর তথ্য আপডেট করতে সমস্যা হয়েছে: " . $e->getMessage();
        }
    } else {
        $errorMessage = implode("<br>", $errors);
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>শিক্ষার্থী সম্পাদনা - কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> কোর্স
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="departments.php">
                            <i class="fas fa-building me-2"></i> বিভাগ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>শিক্ষার্থী সম্পাদনা</h2>
                        <p class="text-muted">শিক্ষার্থীর তথ্য আপডেট করুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="students.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>শিক্ষার্থী তালিকায় ফিরে যান
                        </a>
                    </div>
                </div>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo $successMessage; ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $errorMessage; ?>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-body">
                        <form action="" method="post" enctype="multipart/form-data">
                            <div class="row">
                                <!-- Left Column -->
                                <div class="col-md-6">
                                    <h4 class="mb-4">ব্যক্তিগত তথ্য</h4>

                                    <div class="mb-3">
                                        <label for="student_id" class="form-label">শিক্ষার্থী আইডি*</label>
                                        <input type="text" class="form-control" id="student_id" name="student_id" value="<?php echo htmlspecialchars($student['student_id']); ?>" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="first_name" class="form-label">নাম*</label>
                                        <input type="text" class="form-control" id="first_name" name="first_name" value="<?php echo htmlspecialchars($student['first_name']); ?>" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="last_name" class="form-label">উপাধি</label>
                                        <input type="text" class="form-control" id="last_name" name="last_name" value="<?php echo htmlspecialchars($student['last_name']); ?>">
                                    </div>

                                    <div class="mb-3">
                                        <label for="email" class="form-label">ইমেইল*</label>
                                        <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($student['email']); ?>" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="phone" class="form-label">ফোন</label>
                                        <input type="text" class="form-control" id="phone" name="phone" value="<?php echo htmlspecialchars($student['phone']); ?>">
                                    </div>

                                    <div class="mb-3">
                                        <label for="address" class="form-label">ঠিকানা</label>
                                        <textarea class="form-control" id="address" name="address" rows="3"><?php echo htmlspecialchars($student['address']); ?></textarea>
                                    </div>

                                    <div class="mb-3">
                                        <label for="dob" class="form-label">জন্ম তারিখ</label>
                                        <input type="date" class="form-control" id="dob" name="dob" value="<?php echo htmlspecialchars($student['dob']); ?>">
                                    </div>

                                    <div class="mb-3">
                                        <label for="gender" class="form-label">লিঙ্গ</label>
                                        <select class="form-select" id="gender" name="gender">
                                            <option value="">লিঙ্গ নির্বাচন করুন</option>
                                            <option value="male" <?php echo ($student['gender'] == 'male') ? 'selected' : ''; ?>>পুরুষ</option>
                                            <option value="female" <?php echo ($student['gender'] == 'female') ? 'selected' : ''; ?>>মহিলা</option>
                                            <option value="other" <?php echo ($student['gender'] == 'other') ? 'selected' : ''; ?>>অন্যান্য</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="profile_photo" class="form-label">প্রোফাইল ছবি</label>
                                        <?php if (!empty($student['profile_photo'])): ?>
                                            <div class="mb-2">
                                                <img src="../<?php echo $student['profile_photo']; ?>" alt="Current Profile Photo" class="img-thumbnail" style="max-width: 100px;">
                                            </div>
                                        <?php endif; ?>
                                        <input type="file" class="form-control" id="profile_photo" name="profile_photo">
                                        <small class="form-text text-muted">JPG, PNG, or GIF ফাইল আপলোড করুন। সর্বোচ্চ সাইজ 2MB।</small>
                                    </div>
                                </div>

                                <!-- Right Column -->
                                <div class="col-md-6">
                                    <h4 class="mb-4">একাডেমিক তথ্য</h4>

                                    <div class="mb-3">
                                        <label for="batch" class="form-label">ব্যাচ</label>
                                        <input type="text" class="form-control" id="batch" name="batch" value="<?php echo htmlspecialchars($student['batch']); ?>">
                                    </div>

                                    <div class="mb-3">
                                        <label for="roll_number" class="form-label">রোল নম্বর</label>
                                        <input type="text" class="form-control" id="roll_number" name="roll_number" value="<?php echo htmlspecialchars($student['roll_number']); ?>">
                                    </div>

                                    <div class="mb-3">
                                        <label for="department_id" class="form-label">বিভাগ*</label>
                                        <select class="form-select" id="department_id" name="department_id" required>
                                            <option value="">বিভাগ নির্বাচন করুন</option>
                                            <?php if ($departments && $departments->num_rows > 0): ?>
                                                <?php while ($dept = $departments->fetch_assoc()): ?>
                                                    <option value="<?php echo htmlspecialchars($dept['id']); ?>" <?php echo ($student['department_id'] == $dept['id']) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($dept['department_name']); ?>
                                                    </option>
                                                <?php endwhile; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="class_id" class="form-label">ক্লাস</label>
                                        <select class="form-select" id="class_id" name="class_id">
                                            <option value="">ক্লাস নির্বাচন করুন</option>
                                            <?php if ($classes && $classes->num_rows > 0): ?>
                                                <?php while ($class = $classes->fetch_assoc()): ?>
                                                    <option value="<?php echo htmlspecialchars($class['id']); ?>" <?php echo ($student['class_id'] == $class['id']) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($class['class_name']); ?>
                                                    </option>
                                                <?php endwhile; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="session_id" class="form-label">সেশন</label>
                                        <select class="form-select" id="session_id" name="session_id">
                                            <option value="">সেশন নির্বাচন করুন</option>
                                            <?php if ($sessions && $sessions->num_rows > 0): ?>
                                                <?php while ($session = $sessions->fetch_assoc()): ?>
                                                    <option value="<?php echo htmlspecialchars($session['id']); ?>" <?php echo ($student['session_id'] == $session['id']) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($session['session_name']); ?>
                                                    </option>
                                                <?php endwhile; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="admission_date" class="form-label">ভর্তির তারিখ*</label>
                                        <input type="date" class="form-control" id="admission_date" name="admission_date" value="<?php echo htmlspecialchars($student['admission_date']); ?>" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="student_role" class="form-label">ভূমিকা</label>
                                        <select class="form-select" id="student_role" name="student_role">
                                            <option value="">ভূমিকা নির্বাচন করুন</option>
                                            <option value="class_captain" <?php echo ($student['role'] == 'class_captain') ? 'selected' : ''; ?>>ক্লাস ক্যাপ্টেন</option>
                                            <option value="cr" <?php echo ($student['role'] == 'cr') ? 'selected' : ''; ?>>সিআর</option>
                                            <option value="regular" <?php echo ($student['role'] == 'regular') ? 'selected' : ''; ?>>নিয়মিত</option>
                                        </select>
                                    </div>

                                    <h4 class="mb-4 mt-5">লগইন তথ্য</h4>

                                    <div class="mb-3">
                                        <label for="username" class="form-label">ইউজারনেম</label>
                                        <input type="text" class="form-control" id="username" name="username" value="<?php echo htmlspecialchars($student['username'] ?? ''); ?>">
                                        <small class="form-text text-muted">শিক্ষার্থীর লগইন ইউজারনেম</small>
                                    </div>

                                    <div class="mb-3">
                                        <label for="password" class="form-label">পাসওয়ার্ড</label>
                                        <input type="password" class="form-control" id="password" name="password">
                                        <small class="form-text text-muted">পাসওয়ার্ড পরিবর্তন করতে চাইলে নতুন পাসওয়ার্ড দিন, অন্যথায় খালি রাখুন</small>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4 text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>শিক্ষার্থী আপডেট করুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
