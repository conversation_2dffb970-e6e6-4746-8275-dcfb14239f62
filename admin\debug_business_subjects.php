<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get all groups
echo "<h2>All Groups</h2>";
$groupsQuery = "SELECT * FROM groups WHERE is_active = 1 ORDER BY id";
$groups = $conn->query($groupsQuery);

if ($groups && $groups->num_rows > 0) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Group Name</th></tr>";
    while ($group = $groups->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $group['id'] . "</td>";
        echo "<td>" . $group['group_name'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "No groups found.";
}

// Get all departments
echo "<h2>All Departments</h2>";
$departmentsQuery = "SELECT * FROM departments ORDER BY id";
$departments = $conn->query($departmentsQuery);

if ($departments && $departments->num_rows > 0) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Department Name</th></tr>";
    while ($department = $departments->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $department['id'] . "</td>";
        echo "<td>" . $department['department_name'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "No departments found.";
}

// Find Business Group
echo "<h2>Business Group Search</h2>";
$businessGroupQuery = "SELECT * FROM groups WHERE group_name LIKE '%ব্যবসায়%' OR group_name LIKE '%বাবসায়%' OR group_name LIKE '%বানিজ্য%' OR group_name LIKE '%Business%'";
$businessGroups = $conn->query($businessGroupQuery);

if ($businessGroups && $businessGroups->num_rows > 0) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Group Name</th></tr>";
    while ($group = $businessGroups->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $group['id'] . "</td>";
        echo "<td>" . $group['group_name'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "No business groups found.";
}

// Get subjects for Business group
echo "<h2>Subjects for Business Group</h2>";

// First find the business group ID
$businessGroupQuery = "SELECT id FROM groups WHERE group_name LIKE '%ব্যবসায়%' OR group_name LIKE '%বাবসায়%' OR group_name LIKE '%বানিজ্য%' OR group_name LIKE '%Business%' LIMIT 1";
$businessGroupResult = $conn->query($businessGroupQuery);

if ($businessGroupResult && $businessGroupResult->num_rows > 0) {
    $businessGroupId = $businessGroupResult->fetch_assoc()['id'];
    echo "Found Business Group ID: " . $businessGroupId . "<br><br>";
    
    // Get all subjects for this group
    $subjectsQuery = "SELECT s.id, s.subject_name, s.subject_code, sg.subject_type, sg.is_applicable
                     FROM subjects s
                     JOIN subject_groups sg ON s.id = sg.subject_id
                     WHERE sg.group_id = ? AND sg.is_applicable = 1
                     ORDER BY sg.subject_type, s.subject_name";
    
    $stmt = $conn->prepare($subjectsQuery);
    $stmt->bind_param("i", $businessGroupId);
    $stmt->execute();
    $subjects = $stmt->get_result();
    
    if ($subjects && $subjects->num_rows > 0) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Subject Name</th><th>Subject Code</th><th>Type</th><th>Applicable</th></tr>";
        while ($subject = $subjects->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $subject['id'] . "</td>";
            echo "<td>" . $subject['subject_name'] . "</td>";
            echo "<td>" . $subject['subject_code'] . "</td>";
            echo "<td>" . $subject['subject_type'] . "</td>";
            echo "<td>" . ($subject['is_applicable'] ? 'Yes' : 'No') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No subjects found for Business group.";
    }
    
    // Check if there are any required subjects
    $requiredQuery = "SELECT COUNT(*) as count FROM subject_groups 
                     WHERE group_id = ? AND subject_type = 'required' AND is_applicable = 1";
    $stmt = $conn->prepare($requiredQuery);
    $stmt->bind_param("i", $businessGroupId);
    $stmt->execute();
    $requiredResult = $stmt->get_result();
    $requiredCount = $requiredResult->fetch_assoc()['count'];
    
    echo "<br>Required subjects count: " . $requiredCount;
    
    // Check if there are any optional subjects
    $optionalQuery = "SELECT COUNT(*) as count FROM subject_groups 
                     WHERE group_id = ? AND subject_type = 'optional' AND is_applicable = 1";
    $stmt = $conn->prepare($optionalQuery);
    $stmt->bind_param("i", $businessGroupId);
    $stmt->execute();
    $optionalResult = $stmt->get_result();
    $optionalCount = $optionalResult->fetch_assoc()['count'];
    
    echo "<br>Optional subjects count: " . $optionalCount;
    
    // Check if there are any fourth subjects
    $fourthQuery = "SELECT COUNT(*) as count FROM subject_groups 
                   WHERE group_id = ? AND subject_type = 'fourth' AND is_applicable = 1";
    $stmt = $conn->prepare($fourthQuery);
    $stmt->bind_param("i", $businessGroupId);
    $stmt->execute();
    $fourthResult = $stmt->get_result();
    $fourthCount = $fourthResult->fetch_assoc()['count'];
    
    echo "<br>Fourth subjects count: " . $fourthCount;
} else {
    echo "No business group found.";
}

// Check for department with name "বাবসায়" or "ব্যবসায়"
echo "<h2>Business Department Search</h2>";
$businessDeptQuery = "SELECT * FROM departments WHERE department_name LIKE '%ব্যবসায়%' OR department_name LIKE '%বাবসায়%' OR department_name LIKE '%বানিজ্য%' OR department_name LIKE '%Business%'";
$businessDepts = $conn->query($businessDeptQuery);

if ($businessDepts && $businessDepts->num_rows > 0) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Department Name</th></tr>";
    while ($dept = $businessDepts->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $dept['id'] . "</td>";
        echo "<td>" . $dept['department_name'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "No business departments found.";
}

// Check student with ID STD-833027
echo "<h2>Student Information</h2>";
$studentQuery = "SELECT s.*, d.department_name 
                FROM students s 
                LEFT JOIN departments d ON s.department_id = d.id
                WHERE s.student_id = 'STD-833027'";
$studentResult = $conn->query($studentQuery);

if ($studentResult && $studentResult->num_rows > 0) {
    $student = $studentResult->fetch_assoc();
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Name</th><th>Department ID</th><th>Department Name</th></tr>";
    echo "<tr>";
    echo "<td>" . $student['student_id'] . "</td>";
    echo "<td>" . $student['first_name'] . " " . $student['last_name'] . "</td>";
    echo "<td>" . $student['department_id'] . "</td>";
    echo "<td>" . $student['department_name'] . "</td>";
    echo "</tr>";
    echo "</table>";
} else {
    echo "Student not found.";
}
?>
