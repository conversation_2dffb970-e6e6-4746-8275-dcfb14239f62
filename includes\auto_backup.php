<?php
// This script handles automatic database backups

require_once 'dbh.inc.php';
require_once 'Encryption.php';

// Create backup directory if it doesn't exist
$backupDir = '../backups';
if (!file_exists($backupDir)) {
    mkdir($backupDir, 0777, true);
}

// Check if auto backup is enabled
$configFile = '../config/backup_config.php';
if (!file_exists($configFile)) {
    exit('Auto backup not configured');
}

include $configFile;

if (!isset($config['auto_backup_enabled']) || !$config['auto_backup_enabled']) {
    exit('Auto backup not enabled');
}

// Check if it's time for a backup
$lastBackupTime = isset($config['last_backup_time']) ? $config['last_backup_time'] : 0;
$currentTime = time();
$interval = isset($config['auto_backup_interval']) ? $config['auto_backup_interval'] : 'daily';

$shouldBackup = false;

switch ($interval) {
    case 'daily':
        // Check if last backup was more than 24 hours ago
        if (($currentTime - $lastBackupTime) > (24 * 60 * 60)) {
            $shouldBackup = true;
        }
        break;
    case 'weekly':
        // Check if last backup was more than 7 days ago
        if (($currentTime - $lastBackupTime) > (7 * 24 * 60 * 60)) {
            $shouldBackup = true;
        }
        break;
    case 'monthly':
        // Check if last backup was more than 30 days ago
        if (($currentTime - $lastBackupTime) > (30 * 24 * 60 * 60)) {
            $shouldBackup = true;
        }
        break;
}

if (!$shouldBackup) {
    exit('No backup needed at this time');
}

// Function to create database backup
function backupDatabase($conn, $backupDir) {
    global $dbname;

    $tables = array();
    $result = $conn->query("SHOW TABLES");

    while ($row = $result->fetch_row()) {
        $tables[] = $row[0];
    }

    $backupFile = $backupDir . '/' . $dbname . '_auto_' . date("Y-m-d_H-i-s") . '.sql';
    $output = "-- Automatic Database Backup for $dbname - " . date("Y-m-d H:i:s") . "\n\n";

    foreach ($tables as $table) {
        try {
            // Check if table exists
            $tableExistsQuery = "SHOW TABLES LIKE '$table'";
            $tableExists = $conn->query($tableExistsQuery);

            if ($tableExists && $tableExists->num_rows > 0) {
                // Get table structure
                $result = $conn->query("SHOW CREATE TABLE `$table`");
                if ($result && $row = $result->fetch_row()) {
                    $output .= "\n\n" . $row[1] . ";\n\n";

                    // Get table data
                    $dataResult = $conn->query("SELECT * FROM `$table`");
                    if ($dataResult) {
                        $numFields = $dataResult->field_count;

                        while ($row = $dataResult->fetch_row()) {
                            $output .= "INSERT INTO `$table` VALUES(";

                            for ($i = 0; $i < $numFields; $i++) {
                                if (isset($row[$i])) {
                                    // Escape special characters
                                    $row[$i] = str_replace("\n", "\\n", addslashes($row[$i]));
                                    $output .= '"' . $row[$i] . '"';
                                } else {
                                    $output .= 'NULL';
                                }

                                if ($i < ($numFields - 1)) {
                                    $output .= ',';
                                }
                            }

                            $output .= ");\n";
                        }
                    }

                    $output .= "\n\n";
                }
            }
        } catch (Exception $e) {
            // Skip this table and continue with others
            continue;
        }
    }

    // Save the backup file
    if (file_put_contents($backupFile, $output)) {
        return basename($backupFile);
    } else {
        return false;
    }
}

// Create encryption object with a unique key based on server and session
$encryptionKey = 'auto_backup_' . gethostname() . '_' . session_id();
$encryption = new Encryption($encryptionKey);

// Create backup
$tempFile = $backupDir . '/temp_auto_' . time() . '.sql';
$backupFile = $backupDir . '/' . $dbname . '_auto_' . date("Y-m-d_H-i-s") . '.enc';
$output = "-- Automatic Database Backup for $dbname - " . date("Y-m-d H:i:s") . "\n";
$output .= "-- This backup is encrypted and can only be restored on this computer\n\n";

// Generate SQL backup
$tables = array();
$result = $conn->query("SHOW TABLES");

while ($row = $result->fetch_row()) {
    $tables[] = $row[0];
}

foreach ($tables as $table) {
    try {
        // Check if table exists
        $tableExistsQuery = "SHOW TABLES LIKE '$table'";
        $tableExists = $conn->query($tableExistsQuery);

        if ($tableExists && $tableExists->num_rows > 0) {
            // Get table structure
            $result = $conn->query("SHOW CREATE TABLE `$table`");
            if ($result && $row = $result->fetch_row()) {
                $output .= "\n\n" . $row[1] . ";\n\n";

                // Get table data
                $dataResult = $conn->query("SELECT * FROM `$table`");
                if ($dataResult) {
                    $numFields = $dataResult->field_count;

                    while ($row = $dataResult->fetch_row()) {
                        $output .= "INSERT INTO `$table` VALUES(";

                        for ($i = 0; $i < $numFields; $i++) {
                            if (isset($row[$i])) {
                                // Escape special characters
                                $row[$i] = str_replace("\n", "\\n", addslashes($row[$i]));
                                $output .= '"' . $row[$i] . '"';
                            } else {
                                $output .= 'NULL';
                            }

                            if ($i < ($numFields - 1)) {
                                $output .= ',';
                            }
                        }

                        $output .= ");\n";
                    }
                }

                $output .= "\n\n";
            }
        }
    } catch (Exception $e) {
        // Skip this table and continue with others
        continue;
    }
}

// Save the SQL to a temporary file
if (file_put_contents($tempFile, $output)) {
    // Encrypt the file
    if ($encryption->encryptFile($tempFile, $backupFile, true)) {
        // Delete the temporary file
        unlink($tempFile);

        // Update last backup time
        $config['last_backup_time'] = $currentTime;
        $configContent = "<?php\n\$config = " . var_export($config, true) . ";\n?>";
        file_put_contents($configFile, $configContent);

        echo "Automatic encrypted backup created successfully: $backupFile";
    } else {
        // Delete the temporary file
        unlink($tempFile);
        echo "Failed to encrypt automatic backup";
    }
} else {
    echo "Failed to create automatic backup";
}
?>
