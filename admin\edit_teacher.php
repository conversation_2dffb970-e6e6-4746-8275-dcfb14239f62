<?php
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Handle success and error messages
$success_msg = '';
$error_msg = '';

// Check if teacher ID is provided
if (!isset($_GET['id'])) {
    header("Location: teachers.php");
    exit();
}

$teacherId = $_GET['id'];

// Get teacher information with username from users table
$teacherQuery = "SELECT t.*, u.username
                FROM teachers t
                LEFT JOIN users u ON t.username = u.username
                WHERE t.id = ?";
$stmt = $conn->prepare($teacherQuery);
$stmt->bind_param("i", $teacherId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header("Location: teachers.php");
    exit();
}

$teacher = $result->fetch_assoc();

// Get all departments for the dropdown
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Handle form submission
if (isset($_POST['update_teacher'])) {
    $teacherIdValue = $conn->real_escape_string($_POST['teacher_id']);
    $firstName = $conn->real_escape_string($_POST['first_name']);
    $lastName = $conn->real_escape_string($_POST['last_name']);
    $email = $conn->real_escape_string($_POST['email'] ?? '');
    $phone = $conn->real_escape_string($_POST['phone'] ?? '');
    $departmentId = $_POST['department_id'] ?? null;
    $subject = $conn->real_escape_string($_POST['subject'] ?? '');
    $designation = $conn->real_escape_string($_POST['designation'] ?? '');
    $joiningDate = $conn->real_escape_string($_POST['joining_date'] ?? '');
    $username = $conn->real_escape_string($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';

    // Handle profile photo upload
    $profilePhoto = $teacher['profile_photo']; // Keep existing photo by default

    if (isset($_FILES['profile_photo']) && $_FILES['profile_photo']['error'] == 0) {
        $allowed = ['jpg', 'jpeg', 'png', 'gif'];
        $filename = $_FILES['profile_photo']['name'];
        $fileExt = pathinfo($filename, PATHINFO_EXTENSION);

        // Check if the file extension is allowed
        if (in_array(strtolower($fileExt), $allowed)) {
            // Create upload directory if it doesn't exist
            $uploadDir = '../uploads/teachers/';
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            // Generate a unique filename
            $newFilename = 'teacher_' . $teacherIdValue . '_' . time() . '.' . $fileExt;
            $destination = $uploadDir . $newFilename;

            // Move the uploaded file
            if (move_uploaded_file($_FILES['profile_photo']['tmp_name'], $destination)) {
                // Delete old photo if exists
                if (!empty($teacher['profile_photo']) && file_exists('../' . $teacher['profile_photo'])) {
                    unlink('../' . $teacher['profile_photo']);
                }

                $profilePhoto = 'uploads/teachers/' . $newFilename;
            } else {
                $error_msg = "ছবি আপলোড করতে সমস্যা হয়েছে";
            }
        } else {
            $error_msg = "অনুমোদিত ফাইল টাইপ: jpg, jpeg, png, gif";
        }
    }

    // Update teacher information
    $updateQuery = "UPDATE teachers SET
                    teacher_id = ?,
                    first_name = ?,
                    last_name = ?,
                    email = ?,
                    phone = ?,
                    department_id = ?,
                    subject = ?,
                    designation = ?,
                    joining_date = ?,
                    profile_photo = ?
                    WHERE id = ?";

    $stmt = $conn->prepare($updateQuery);
    $stmt->bind_param("ssssssssssi", $teacherIdValue, $firstName, $lastName, $email, $phone, $departmentId, $subject, $designation, $joiningDate, $profilePhoto, $teacherId);

    if ($stmt->execute()) {
        $success_msg = "শিক্ষকের তথ্য সফলভাবে আপডেট করা হয়েছে";

        // Refresh teacher data
        $stmt = $conn->prepare($teacherQuery);
        $stmt->bind_param("i", $teacherId);
        $stmt->execute();
        $result = $stmt->get_result();
        $teacher = $result->fetch_assoc();
    } else {
        $error_msg = "শিক্ষকের তথ্য আপডেট করতে সমস্যা হয়েছে: " . $conn->error;
    }
}

// Handle photo deletion
if (isset($_POST['delete_photo'])) {
    if (!empty($teacher['profile_photo']) && file_exists('../' . $teacher['profile_photo'])) {
        unlink('../' . $teacher['profile_photo']);
    }

    $updatePhotoQuery = "UPDATE teachers SET profile_photo = NULL WHERE id = ?";
    $stmt = $conn->prepare($updatePhotoQuery);
    $stmt->bind_param("i", $teacherId);

    if ($stmt->execute()) {
        $success_msg = "শিক্ষকের ছবি সফলভাবে মুছে ফেলা হয়েছে";

        // Refresh teacher data
        $stmt = $conn->prepare($teacherQuery);
        $stmt->bind_param("i", $teacherId);
        $stmt->execute();
        $result = $stmt->get_result();
        $teacher = $result->fetch_assoc();
    } else {
        $error_msg = "শিক্ষকের ছবি মুছে ফেলতে সমস্যা হয়েছে: " . $conn->error;
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>শিক্ষকের তথ্য সম্পাদনা | অ্যাডমিন ড্যাশবোর্ড</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar {
            height: 100vh;
            background-color: #343a40;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            display: block;
        }
        .sidebar a:hover {
            background-color: #495057;
        }
        .active {
            background-color: #0d6efd;
        }
        .content {
            margin-left: 220px;
            padding: 20px;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
            }
            .content {
                margin-left: 0;
            }
        }
        .profile-image {
            max-width: 200px;
            max-height: 200px;
            object-fit: cover;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar">
                <h2 class="text-white text-center mb-4">অ্যাডমিন প্যানেল</h2>
                <a href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড</a>
                <a href="students.php"><i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী</a>
                <a href="teachers.php" class="active"><i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক</a>
                <a href="sessions.php"><i class="fas fa-calendar-alt me-2"></i> সেশন</a>
                <a href="classes.php"><i class="fas fa-chalkboard me-2"></i> ক্লাস</a>
                <a href="departments.php"><i class="fas fa-building me-2"></i> বিভাগ</a>
                <a href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i> লগআউট</a>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>শিক্ষকের তথ্য সম্পাদনা</h1>
                    <a href="teachers.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i> শিক্ষক তালিকায় ফিরে যান
                    </a>
                </div>

                <!-- Success and Error Messages -->
                <?php if (!empty($success_msg)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error_msg)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">শিক্ষকের তথ্য সম্পাদনা করুন</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Profile Photo Section -->
                            <div class="col-md-3 text-center mb-4">
                                <h5 class="mb-3">প্রোফাইল ছবি</h5>
                                <?php if (!empty($teacher['profile_photo'])): ?>
                                    <img src="../<?php echo $teacher['profile_photo']; ?>" alt="<?php echo $teacher['first_name']; ?>" class="profile-image mb-3">
                                    <form method="POST" class="mt-2">
                                        <button type="submit" name="delete_photo" class="btn btn-sm btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই ছবি মুছে ফেলতে চান?')">
                                            <i class="fas fa-trash me-1"></i> ছবি মুছুন
                                        </button>
                                    </form>
                                <?php else: ?>
                                    <div class="border p-3 mb-3 text-center">
                                        <i class="fas fa-user fa-5x text-secondary"></i>
                                        <p class="mt-2 text-muted">কোন ছবি নেই</p>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Edit Form -->
                            <div class="col-md-9">
                                <form method="POST" action="" enctype="multipart/form-data">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="teacher_id" class="form-label">শিক্ষক আইডি*</label>
                                            <input type="text" class="form-control" id="teacher_id" name="teacher_id" value="<?php echo htmlspecialchars($teacher['teacher_id']); ?>" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="profile_photo" class="form-label">প্রোফাইল ছবি পরিবর্তন করুন</label>
                                            <input type="file" class="form-control" id="profile_photo" name="profile_photo" accept="image/*">
                                            <div class="form-text">অনুমোদিত ফাইল টাইপ: jpg, jpeg, png, gif</div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="first_name" class="form-label">নাম*</label>
                                            <input type="text" class="form-control" id="first_name" name="first_name" value="<?php echo htmlspecialchars($teacher['first_name']); ?>" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="last_name" class="form-label">উপাধি</label>
                                            <input type="text" class="form-control" id="last_name" name="last_name" value="<?php echo htmlspecialchars($teacher['last_name']); ?>">
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label">ইমেইল</label>
                                            <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($teacher['email'] ?? ''); ?>">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="phone" class="form-label">ফোন নম্বর</label>
                                            <input type="text" class="form-control" id="phone" name="phone" value="<?php echo htmlspecialchars($teacher['phone'] ?? ''); ?>">
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="department_id" class="form-label">বিভাগ</label>
                                            <select class="form-select" id="department_id" name="department_id">
                                                <option value="">বিভাগ নির্বাচন করুন</option>
                                                <?php if ($departments && $departments->num_rows > 0): ?>
                                                    <?php while ($dept = $departments->fetch_assoc()): ?>
                                                        <option value="<?php echo $dept['id']; ?>" <?php echo ($teacher['department_id'] == $dept['id']) ? 'selected' : ''; ?>>
                                                            <?php echo htmlspecialchars($dept['department_name']); ?>
                                                        </option>
                                                    <?php endwhile; ?>
                                                <?php endif; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="subject" class="form-label">বিষয়</label>
                                            <input type="text" class="form-control" id="subject" name="subject" value="<?php echo htmlspecialchars($teacher['subject'] ?? ''); ?>" placeholder="যেমন: বাংলা, ইংরেজি, গণিত">
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="designation" class="form-label">পদবি</label>
                                            <input type="text" class="form-control" id="designation" name="designation" value="<?php echo htmlspecialchars($teacher['designation'] ?? ''); ?>">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="joining_date" class="form-label">যোগদানের তারিখ</label>
                                            <input type="date" class="form-control" id="joining_date" name="joining_date" value="<?php echo $teacher['joining_date'] ?? ''; ?>">
                                        </div>
                                    </div>

                                    <div class="mt-4">
                                        <button type="submit" name="update_teacher" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i> আপডেট করুন
                                        </button>
                                        <a href="teachers.php" class="btn btn-secondary ms-2">
                                            <i class="fas fa-times me-2"></i> বাতিল করুন
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Image preview script
        document.getElementById('profile_photo').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const previewImg = document.querySelector('.profile-image');
                    if (previewImg) {
                        previewImg.src = e.target.result;
                    } else {
                        const newImg = document.createElement('img');
                        newImg.src = e.target.result;
                        newImg.alt = "Preview";
                        newImg.className = "profile-image mb-3";

                        const container = document.querySelector('.col-md-3.text-center');
                        const oldContent = container.querySelector('.border');
                        if (oldContent) {
                            container.replaceChild(newImg, oldContent);
                        } else {
                            container.appendChild(newImg);
                        }
                    }
                }
                reader.readAsDataURL(file);
            }
        });
    </script>
</body>
</html>
