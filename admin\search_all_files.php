<?php
// Get all PHP files in the directory
$files = glob('*.php');

foreach ($files as $file) {
    // Skip this search script
    if (in_array($file, ['search_all_files.php', 'fix_function.php', 'find_duplicate.php', 'check_lines.php', 'fix_duplicate_function.php'])) {
        continue;
    }
    
    $content = file_get_contents($file);
    $count = substr_count($content, 'function getStudentsByClass');
    
    if ($count > 0) {
        echo "File: $file - Found $count occurrence(s) of 'function getStudentsByClass'\n";
        
        // If found, show line numbers
        $lines = explode("\n", $content);
        $lineNumbers = [];
        
        foreach ($lines as $i => $line) {
            if (strpos($line, 'function getStudentsByClass') !== false) {
                $lineNumbers[] = $i + 1;
            }
        }
        
        echo "  Found on lines: " . implode(", ", $lineNumbers) . "\n";
        echo "  Total lines in file: " . count($lines) . "\n\n";
    }
}
?> 