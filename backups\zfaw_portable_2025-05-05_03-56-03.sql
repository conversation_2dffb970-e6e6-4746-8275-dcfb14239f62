-- Portable Database Backup for zfaw - 2025-05-05 03:56:03
-- This backup can be used on any computer



CREATE TABLE `bkash_payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fee_id` int(11) NOT NULL,
  `payment_id` varchar(100) NOT NULL,
  `trx_id` varchar(100) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status` varchar(50) NOT NULL,
  `payer_reference` varchar(100) DEFAULT NULL,
  `payment_date` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `classes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `class_name` varchar(50) NOT NULL,
  `department_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `department_id` (`department_id`),
  CONSTRAINT `classes_ibfk_1` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `classes` VALUES("1","একাদশ",NULL,"2025-05-04 21:59:04");
INSERT INTO `classes` VALUES("2","দ্বাদশ",NULL,"2025-05-04 21:59:13");




CREATE TABLE `committee_members` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `position` varchar(255) NOT NULL,
  `details` text DEFAULT NULL,
  `photo` varchar(255) DEFAULT NULL,
  `priority` int(11) DEFAULT 100,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `committee_members` VALUES("1","মাননীয় অধ্যক্ষ","সভাপতি","অভিজ্ঞ শিক্ষাবিদ এবং প্রশাসক, ২০১০ সাল থেকে সংস্থার সাথে যুক্ত।",NULL,"1","2025-05-04 22:14:12");
INSERT INTO `committee_members` VALUES("2","মাননীয় সচিব","সদস্য সচিব","অভিজ্ঞ প্রশাসক এবং শিক্ষাবিদ, ২০১২ সাল থেকে সংস্থার সাথে যুক্ত।",NULL,"2","2025-05-04 22:14:12");
INSERT INTO `committee_members` VALUES("3","মোঃ আব্দুল কাদের","সদস্য","বিশিষ্ট ব্যবসায়ী এবং সমাজসেবক, ২০১৪ সাল থেকে সংস্থার সাথে যুক্ত।",NULL,"3","2025-05-04 22:14:12");




CREATE TABLE `department_subject_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `department_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `subject_type` enum('required','optional','fourth') NOT NULL DEFAULT 'optional',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `department_id` (`department_id`,`subject_id`),
  KEY `subject_id` (`subject_id`),
  CONSTRAINT `department_subject_types_ibfk_1` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE CASCADE,
  CONSTRAINT `department_subject_types_ibfk_2` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `departments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `department_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `department_code` varchar(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `department_name` (`department_name`),
  UNIQUE KEY `department_code` (`department_code`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `departments` VALUES("1","সাধারন","সাধারন","2025-05-04 18:16:51","সা-০");
INSERT INTO `departments` VALUES("2","বিজ্ঞান","বিজ্ঞান","2025-05-04 21:59:32","বি-১");
INSERT INTO `departments` VALUES("5","মানবিক","মানবিক","2025-05-04 22:05:42","মা-২");
INSERT INTO `departments` VALUES("6","ব্যবসায়","ব্যবসায়","2025-05-04 22:06:00","ব্য-৩");




CREATE TABLE `exam_results` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `exam_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `marks_obtained` decimal(5,2) NOT NULL,
  `grade` varchar(10) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `exam_id` (`exam_id`),
  KEY `student_id` (`student_id`),
  KEY `subject_id` (`subject_id`),
  CONSTRAINT `exam_results_ibfk_1` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`id`) ON DELETE CASCADE,
  CONSTRAINT `exam_results_ibfk_2` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE,
  CONSTRAINT `exam_results_ibfk_3` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `exam_subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `exam_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `total_marks` int(11) NOT NULL DEFAULT 100,
  `passing_marks` int(11) DEFAULT 33,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `exam_id` (`exam_id`),
  KEY `subject_id` (`subject_id`),
  CONSTRAINT `exam_subjects_ibfk_1` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`id`) ON DELETE CASCADE,
  CONSTRAINT `exam_subjects_ibfk_2` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `exam_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `exam_types` VALUES("1","সামায়িক","সামায়িক পরীক্ষা","1","2025-05-04 21:17:00");
INSERT INTO `exam_types` VALUES("2","অর্ধ-বার্ষিক","অর্ধ-বার্ষিক পরীক্ষা","1","2025-05-04 21:17:00");
INSERT INTO `exam_types` VALUES("3","বার্ষিক","বার্ষিক পরীক্ষা","1","2025-05-04 21:17:00");
INSERT INTO `exam_types` VALUES("4","মডেল টেস্ট","মডেল টেস্ট পরীক্ষা","1","2025-05-04 21:17:00");
INSERT INTO `exam_types` VALUES("5","নির্বাচনী","নির্বাচনী পরীক্ষা","1","2025-05-04 21:17:00");
INSERT INTO `exam_types` VALUES("6","সাপ্তাহিক","সাপ্তাহিক পরীক্ষা","1","2025-05-04 21:17:00");
INSERT INTO `exam_types` VALUES("7","মাসিক","মাসিক পরীক্ষা","1","2025-05-04 21:17:00");
INSERT INTO `exam_types` VALUES("8","অন্যান্য","অন্যান্য পরীক্ষা","1","2025-05-04 21:17:00");
INSERT INTO `exam_types` VALUES("9","প্রাক-নির্বাচনী","প্রাক-নির্বাচনী পরীক্ষা","1","2025-05-04 21:17:47");




CREATE TABLE `exams` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `exam_name` varchar(100) NOT NULL,
  `exam_date` date NOT NULL,
  `exam_type` varchar(100) DEFAULT NULL,
  `total_marks` int(11) NOT NULL DEFAULT 100,
  `passing_marks` int(11) DEFAULT 33,
  `class_id` int(11) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `subject_id` int(11) DEFAULT NULL,
  `subject_name` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `class_id` (`class_id`),
  KEY `department_id` (`department_id`),
  KEY `subject_id` (`subject_id`),
  CONSTRAINT `exams_ibfk_1` FOREIGN KEY (`class_id`) REFERENCES `classes` (`id`) ON DELETE SET NULL,
  CONSTRAINT `exams_ibfk_2` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL,
  CONSTRAINT `exams_ibfk_3` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `fee_map_class` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fee_type_id` int(11) NOT NULL,
  `class_id` int(11) NOT NULL,
  `academic_year` varchar(20) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `fee_type_id` (`fee_type_id`),
  KEY `class_id` (`class_id`),
  CONSTRAINT `fee_map_class_ibfk_1` FOREIGN KEY (`fee_type_id`) REFERENCES `fee_types` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fee_map_class_ibfk_2` FOREIGN KEY (`class_id`) REFERENCES `classes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `fee_map_session` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fee_type_id` int(11) NOT NULL,
  `session_name` varchar(50) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `fee_type_id` (`fee_type_id`),
  CONSTRAINT `fee_map_session_ibfk_1` FOREIGN KEY (`fee_type_id`) REFERENCES `fee_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `fee_map_student` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fee_type_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `fee_type_id` (`fee_type_id`),
  KEY `student_id` (`student_id`),
  CONSTRAINT `fee_map_student_ibfk_1` FOREIGN KEY (`fee_type_id`) REFERENCES `fee_types` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fee_map_student_ibfk_2` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `fee_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_recurring` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `fees` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `fee_type` varchar(100) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `paid` decimal(10,2) DEFAULT 0.00,
  `due_date` date NOT NULL,
  `payment_status` enum('due','partial','paid') DEFAULT 'due',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `student_id` (`student_id`),
  CONSTRAINT `fees_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_name` varchar(255) NOT NULL,
  `group_code` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `group_code` (`group_code`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `groups` VALUES("1","বিজ্ঞান","SCI","বিজ্ঞান বিভাগের জন্য","1","2025-05-05 07:10:43");
INSERT INTO `groups` VALUES("2","মানবিক","HUM","মানবিক বিভাগের জন্য","1","2025-05-05 07:10:43");
INSERT INTO `groups` VALUES("3","ব্যবসায় শিক্ষা","COM","ব্যবসায় শিক্ষা বিভাগের জন্য","1","2025-05-05 07:10:43");




CREATE TABLE `marks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `exam_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `cq_marks` decimal(10,2) DEFAULT 0.00,
  `mcq_marks` decimal(10,2) DEFAULT 0.00,
  `practical_marks` decimal(10,2) DEFAULT 0.00,
  `total_marks` decimal(10,2) DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `exam_id` (`exam_id`),
  KEY `subject_id` (`subject_id`),
  KEY `student_id` (`student_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `notices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `date` date NOT NULL,
  `target_audience` enum('all','students','teachers','staff') NOT NULL DEFAULT 'all',
  `expiry_date` date NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `notices` VALUES("1","স্বাগতম আমাদের কলেজ ম্যানেজমেন্ট সিস্টেমে","এই সিস্টেমটি ব্যবহার করে আপনি সহজেই আপনার কলেজের সকল কার্যক্রম পরিচালনা করতে পারবেন।","2025-05-04","all","2025-06-03","2025-05-04 18:31:41","2025-05-04 18:31:41");




CREATE TABLE `passing_marks_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` int(11) DEFAULT NULL,
  `min_percentage` decimal(5,2) NOT NULL,
  `max_percentage` decimal(5,2) NOT NULL,
  `passing_mark` decimal(5,2) NOT NULL,
  `cq_passing_percent` decimal(5,2) NOT NULL DEFAULT 33.00,
  `mcq_passing_percent` decimal(5,2) NOT NULL DEFAULT 33.00,
  `practical_passing_percent` decimal(5,2) NOT NULL DEFAULT 33.00,
  `grade` varchar(5) NOT NULL,
  `grade_point` decimal(3,2) NOT NULL,
  `description` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `subject_id` (`subject_id`,`min_percentage`,`max_percentage`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `passing_marks_config` VALUES("1",NULL,"80.00","100.00","80.00","40.00","40.00","40.00","A+","5.00","শ্রেষ্ঠত্ব (Excellence)","2025-05-04 22:13:55","2025-05-04 22:13:55");
INSERT INTO `passing_marks_config` VALUES("2",NULL,"70.00","79.99","70.00","35.00","35.00","35.00","A","4.00","অতি উত্তম (Very Good)","2025-05-04 22:13:55","2025-05-04 22:13:55");
INSERT INTO `passing_marks_config` VALUES("3",NULL,"60.00","69.99","60.00","33.00","33.00","33.00","A-","3.50","উত্তম (Good)","2025-05-04 22:13:55","2025-05-04 22:13:55");
INSERT INTO `passing_marks_config` VALUES("4",NULL,"50.00","59.99","50.00","33.00","33.00","33.00","B","3.00","ভালো (Satisfactory)","2025-05-04 22:13:55","2025-05-04 22:13:55");
INSERT INTO `passing_marks_config` VALUES("5",NULL,"40.00","49.99","40.00","33.00","33.00","33.00","C","2.00","মোটামুটি (Average)","2025-05-04 22:13:55","2025-05-04 22:13:55");
INSERT INTO `passing_marks_config` VALUES("6",NULL,"33.00","39.99","33.00","33.00","33.00","33.00","D","1.00","নিম্নমান (Poor)","2025-05-04 22:13:55","2025-05-04 22:13:55");
INSERT INTO `passing_marks_config` VALUES("7",NULL,"0.00","32.99","0.00","0.00","0.00","0.00","F","0.00","অকৃতকার্য (Fail)","2025-05-04 22:13:55","2025-05-04 22:13:55");




CREATE TABLE `sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_name` varchar(50) NOT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `is_current` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `session_name` (`session_name`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `sessions` VALUES("1","2025-26","2025-05-01","2026-05-12","0","2025-05-04 22:08:26");
INSERT INTO `sessions` VALUES("2","2026-27","2026-05-04","2027-12-31","0","2025-05-04 22:09:14");




CREATE TABLE `staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` varchar(20) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `gender` enum('male','female','other') NOT NULL,
  `dob` date DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(50) DEFAULT NULL,
  `state` varchar(50) DEFAULT NULL,
  `postal_code` varchar(20) DEFAULT NULL,
  `country` varchar(50) DEFAULT 'Bangladesh',
  `joining_date` date DEFAULT NULL,
  `profile_photo` varchar(255) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `designation` varchar(100) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `role` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `staff_id` (`staff_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `staff` VALUES("1","STF-001","Jamal","Hossain","<EMAIL>","01912345678","male",NULL,NULL,NULL,NULL,NULL,"Bangladesh","2025-05-04",NULL,"1","Office Assistant",NULL,"2025-05-04 18:23:13",NULL);




CREATE TABLE `student_subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `category` varchar(50) NOT NULL,
  `selection_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `session_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `student_id` (`student_id`,`subject_id`),
  KEY `subject_id` (`subject_id`),
  KEY `session_id` (`session_id`),
  CONSTRAINT `student_subjects_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE,
  CONSTRAINT `student_subjects_ibfk_2` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE,
  CONSTRAINT `student_subjects_ibfk_3` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `students` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` varchar(20) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `gender` enum('male','female','other') NOT NULL,
  `address` text DEFAULT NULL,
  `class_id` int(11) DEFAULT NULL,
  `session_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `admission_date` date DEFAULT NULL,
  `profile_photo` varchar(255) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `batch` varchar(20) DEFAULT NULL,
  `group_name` varchar(50) DEFAULT NULL,
  `role` varchar(50) DEFAULT NULL,
  `roll_number` varchar(20) DEFAULT NULL,
  `guardian_name` varchar(100) DEFAULT NULL,
  `guardian_relation` varchar(50) DEFAULT NULL,
  `guardian_phone` varchar(20) DEFAULT NULL,
  `guardian_email` varchar(100) DEFAULT NULL,
  `guardian_address` text DEFAULT NULL,
  `guardian_occupation` varchar(100) DEFAULT NULL,
  `father_name` varchar(100) DEFAULT NULL,
  `father_phone` varchar(20) DEFAULT NULL,
  `father_email` varchar(100) DEFAULT NULL,
  `father_occupation` varchar(100) DEFAULT NULL,
  `father_income` varchar(50) DEFAULT NULL,
  `mother_name` varchar(100) DEFAULT NULL,
  `mother_phone` varchar(20) DEFAULT NULL,
  `mother_email` varchar(100) DEFAULT NULL,
  `mother_occupation` varchar(100) DEFAULT NULL,
  `mother_income` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `student_id` (`student_id`),
  KEY `class_id` (`class_id`),
  KEY `session_id` (`session_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `students_ibfk_1` FOREIGN KEY (`class_id`) REFERENCES `classes` (`id`) ON DELETE SET NULL,
  CONSTRAINT `students_ibfk_2` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`id`) ON DELETE SET NULL,
  CONSTRAINT `students_ibfk_3` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `subject_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `category_name` (`category_name`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `subject_categories` VALUES("1","required","আবশ্যিক বিষয়সমূহ","2025-05-04 20:58:37");
INSERT INTO `subject_categories` VALUES("2","optional","ঐচ্ছিক বিষয়সমূহ","2025-05-04 20:58:37");
INSERT INTO `subject_categories` VALUES("3","fourth","৪র্থ বিষয়","2025-05-04 20:58:37");




CREATE TABLE `subject_departments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` int(11) NOT NULL,
  `department_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `subject_id` (`subject_id`,`department_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `subject_departments` VALUES("1","1","1");




CREATE TABLE `subject_exam_pattern` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` int(11) NOT NULL,
  `has_cq` tinyint(1) DEFAULT 1,
  `has_mcq` tinyint(1) DEFAULT 1,
  `has_practical` tinyint(1) DEFAULT 0,
  `cq_marks` float DEFAULT 70,
  `mcq_marks` float DEFAULT 30,
  `practical_marks` float DEFAULT 0,
  `total_marks` float DEFAULT 100,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `subject_id` (`subject_id`),
  KEY `subject_id_2` (`subject_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `subject_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` int(11) NOT NULL,
  `group_id` int(11) NOT NULL,
  `subject_type` enum('required','optional','fourth') NOT NULL DEFAULT 'optional',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `subject_id` (`subject_id`),
  KEY `group_id` (`group_id`),
  CONSTRAINT `subject_groups_ibfk_1` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE,
  CONSTRAINT `subject_groups_ibfk_2` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `subject_groups` VALUES("1","8","1","optional","2025-05-05 07:35:02");
INSERT INTO `subject_groups` VALUES("2","8","1","fourth","2025-05-05 07:35:02");
INSERT INTO `subject_groups` VALUES("4","13","1","required","2025-05-05 07:35:36");
INSERT INTO `subject_groups` VALUES("5","13","3","required","2025-05-05 07:35:36");
INSERT INTO `subject_groups` VALUES("6","19","2","optional","2025-05-05 07:35:56");
INSERT INTO `subject_groups` VALUES("7","19","2","fourth","2025-05-05 07:35:56");
INSERT INTO `subject_groups` VALUES("8","10","1","fourth","2025-05-05 07:36:24");
INSERT INTO `subject_groups` VALUES("9","10","3","optional","2025-05-05 07:36:24");
INSERT INTO `subject_groups` VALUES("10","10","3","fourth","2025-05-05 07:36:24");
INSERT INTO `subject_groups` VALUES("11","10","2","optional","2025-05-05 07:36:24");
INSERT INTO `subject_groups` VALUES("12","10","2","fourth","2025-05-05 07:36:24");
INSERT INTO `subject_groups` VALUES("13","3","1","required","2025-05-05 07:36:46");
INSERT INTO `subject_groups` VALUES("14","3","3","required","2025-05-05 07:36:46");
INSERT INTO `subject_groups` VALUES("15","3","2","required","2025-05-05 07:36:46");
INSERT INTO `subject_groups` VALUES("16","11","3","required","2025-05-05 07:37:21");
INSERT INTO `subject_groups` VALUES("17","20","2","optional","2025-05-05 07:37:39");
INSERT INTO `subject_groups` VALUES("18","20","2","fourth","2025-05-05 07:37:39");
INSERT INTO `subject_groups` VALUES("22","4","1","required","2025-05-05 07:38:13");
INSERT INTO `subject_groups` VALUES("23","4","3","required","2025-05-05 07:38:13");
INSERT INTO `subject_groups` VALUES("24","4","2","required","2025-05-05 07:38:13");
INSERT INTO `subject_groups` VALUES("25","21","2","optional","2025-05-05 07:38:59");
INSERT INTO `subject_groups` VALUES("26","21","2","fourth","2025-05-05 07:38:59");
INSERT INTO `subject_groups` VALUES("27","14","2","optional","2025-05-05 07:39:35");
INSERT INTO `subject_groups` VALUES("28","14","2","fourth","2025-05-05 07:39:35");
INSERT INTO `subject_groups` VALUES("29","7","1","optional","2025-05-05 07:39:48");
INSERT INTO `subject_groups` VALUES("30","7","1","fourth","2025-05-05 07:39:48");
INSERT INTO `subject_groups` VALUES("31","5","1","required","2025-05-05 07:39:58");
INSERT INTO `subject_groups` VALUES("32","12","3","required","2025-05-05 07:40:38");




CREATE TABLE `subject_marks_distribution` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` int(11) NOT NULL,
  `cq_marks` float DEFAULT 70,
  `mcq_marks` float DEFAULT 30,
  `practical_marks` float DEFAULT 0,
  `total_marks` float DEFAULT 100,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `subject_id` (`subject_id`),
  KEY `subject_id_2` (`subject_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `subject_minimum_pass` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` int(11) NOT NULL,
  `cq_min_marks` float DEFAULT 0,
  `mcq_min_marks` float DEFAULT 0,
  `practical_min_marks` float DEFAULT 0,
  `total_min_marks` float DEFAULT 33,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `subject_id` (`subject_id`),
  KEY `subject_id_2` (`subject_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_name` varchar(100) NOT NULL,
  `subject_code` varchar(20) NOT NULL,
  `department_id` int(11) DEFAULT NULL,
  `category` varchar(255) DEFAULT 'required',
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `subjects` VALUES("2","BANGLA","101-102","1","required","সবার জন্য","1","2025-05-05 06:35:12");
INSERT INTO `subjects` VALUES("3","ENGLISH","107-108","1","required","সবার জন্য","1","2025-05-05 06:40:34");
INSERT INTO `subjects` VALUES("4","ICT","275","1","required","সবার জন্য","1","2025-05-05 06:41:05");
INSERT INTO `subjects` VALUES("5","PHYSICS","174-175","2","required","SCIENCE","1","2025-05-05 06:56:59");
INSERT INTO `subjects` VALUES("6","CHEMISTRY","176-177","2","required","SCIENCE","1","2025-05-05 06:57:35");
INSERT INTO `subjects` VALUES("7","MATH","265-266","2","required","SCIENCE","1","2025-05-05 06:58:12");
INSERT INTO `subjects` VALUES("8","BIOLOGY","178-179","2","required","SCIENCE","1","2025-05-05 06:58:41");
INSERT INTO `subjects` VALUES("9","STATISTICS","129-130","1","required","সবার জন্য","1","2025-05-05 07:14:04");
INSERT INTO `subjects` VALUES("10","ECONOMICS","109-110","1","required","সবার জন্য","1","2025-05-05 07:14:55");
INSERT INTO `subjects` VALUES("11","FBI","292-293","6","required","BUSINESS","1","2025-05-05 07:16:08");
INSERT INTO `subjects` VALUES("12","PMM","286-287","6","required","BUSINESS","1","2025-05-05 07:17:22");
INSERT INTO `subjects` VALUES("13","BOM","277-278","6","required","BUSINESS","1","2025-05-05 07:18:17");
INSERT INTO `subjects` VALUES("14","LOGIC","121-122","5","required","ARTS","1","2025-05-05 07:18:58");
INSERT INTO `subjects` VALUES("15","SOCIOLOGY","117-118","5","required","ARTS","1","2025-05-05 07:19:36");
INSERT INTO `subjects` VALUES("16","SOCIAL WORK","271-272","5","required","ARTS","1","2025-05-05 07:20:57");
INSERT INTO `subjects` VALUES("17","STUDY OF ISLAM","249-250","5","required","ARTS","1","2025-05-05 07:21:50");
INSERT INTO `subjects` VALUES("18","ACCOUNTING","253-254","2","required","BUSINESS","1","2025-05-05 07:22:50");
INSERT INTO `subjects` VALUES("19","CIVICS","269-270","5","required","ARTS","1","2025-05-05 07:23:48");
INSERT INTO `subjects` VALUES("20","HISTORY","304-305","5","required","ARTS","1","2025-05-05 07:24:16");
INSERT INTO `subjects` VALUES("21","ISLAMIC HISTORY","267-268","5","required","ARTS","1","2025-05-05 07:24:50");




CREATE TABLE `teacher_subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `teacher_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `session_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `teacher_id` (`teacher_id`,`subject_id`,`session_id`),
  KEY `subject_id` (`subject_id`),
  KEY `session_id` (`session_id`),
  CONSTRAINT `teacher_subjects_ibfk_1` FOREIGN KEY (`teacher_id`) REFERENCES `teachers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `teacher_subjects_ibfk_2` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE,
  CONSTRAINT `teacher_subjects_ibfk_3` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `teachers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `teacher_id` varchar(20) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `gender` enum('male','female','other') NOT NULL,
  `dob` date DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(50) DEFAULT NULL,
  `state` varchar(50) DEFAULT NULL,
  `postal_code` varchar(20) DEFAULT NULL,
  `country` varchar(50) DEFAULT 'Bangladesh',
  `joining_date` date DEFAULT NULL,
  `profile_photo` varchar(255) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `subject` varchar(100) DEFAULT NULL,
  `designation` varchar(100) DEFAULT NULL,
  `username` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `teacher_id` (`teacher_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `user_type` enum('admin','teacher','student','staff') NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `role` varchar(20) NOT NULL DEFAULT 'student',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `users` VALUES("1","admin","$2y$10$C6EA2xrSbOEX9fKd9sR/Tucy6f8rmK4/eu2WzdPwgk6JSpfamJRXi","admin","<EMAIL>","2025-05-04 17:57:58","student");


