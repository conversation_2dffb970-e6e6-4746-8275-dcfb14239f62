<?php
session_start();
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

echo "<h2>টেস্ট পেমেন্ট তৈরি করি</h2>";

try {
    // First, let's check what we have
    $studentCheck = $conn->query("SELECT COUNT(*) as count FROM students");
    $studentCount = $studentCheck->fetch_assoc()['count'];
    
    $feeCheck = $conn->query("SELECT COUNT(*) as count FROM fees");
    $feeCount = $feeCheck->fetch_assoc()['count'];
    
    echo "<p>স্টুডেন্ট: $studentCount, ফি: $feeCount</p>";
    
    // If no students, create one
    if ($studentCount == 0) {
        echo "<p>স্টুডেন্ট তৈরি করছি...</p>";
        
        // Check if classes table exists and has data
        $classCheck = $conn->query("SELECT COUNT(*) as count FROM classes");
        if ($classCheck->num_rows > 0) {
            $classCount = $classCheck->fetch_assoc()['count'];
            if ($classCount == 0) {
                // Create a sample class
                $conn->query("INSERT INTO classes (class_name, description) VALUES ('একাদশ শ্রেণী', 'একাদশ শ্রেণীর শিক্ষার্থীরা')");
                echo "<p>নমুনা ক্লাস তৈরি করা হয়েছে</p>";
            }
        }
        
        // Get class ID
        $classResult = $conn->query("SELECT id FROM classes LIMIT 1");
        $classId = $classResult->fetch_assoc()['id'];
        
        // Create a sample student
        $insertStudent = "INSERT INTO students (first_name, last_name, roll_no, class_id, student_id, email, phone, gender) 
                         VALUES ('মোহাম্মদ', 'রহিম উদ্দিন', '2024001', ?, 'S2024001', '<EMAIL>', '01712345678', 'male')";
        $stmt = $conn->prepare($insertStudent);
        $stmt->bind_param('i', $classId);
        $stmt->execute();
        echo "<p>নমুনা স্টুডেন্ট তৈরি করা হয়েছে</p>";
    }
    
    // If no fees, create one
    if ($feeCount == 0) {
        echo "<p>ফি তৈরি করছি...</p>";
        
        // Get student ID
        $studentResult = $conn->query("SELECT id FROM students LIMIT 1");
        $studentId = $studentResult->fetch_assoc()['id'];
        
        // Create a sample fee
        $insertFee = "INSERT INTO fees (student_id, fee_type, amount, due_date, payment_status) 
                     VALUES (?, 'মাসিক বেতন', 1500.00, ?, 'due')";
        $stmt = $conn->prepare($insertFee);
        $dueDate = date('Y-m-d', strtotime('+30 days'));
        $stmt->bind_param('is', $studentId, $dueDate);
        $stmt->execute();
        echo "<p>নমুনা ফি তৈরি করা হয়েছে</p>";
    }
    
    // Now create a payment
    echo "<p>পেমেন্ট তৈরি করছি...</p>";
    
    // Get fee and student data
    $feeQuery = "SELECT f.*, s.first_name, s.last_name, s.roll_no, c.class_name 
                 FROM fees f 
                 JOIN students s ON f.student_id = s.id 
                 LEFT JOIN classes c ON s.class_id = c.id 
                 LIMIT 1";
    $feeResult = $conn->query($feeQuery);
    $feeData = $feeResult->fetch_assoc();
    
    if ($feeData) {
        // Check if fee_payments table exists
        $checkTable = $conn->query("SHOW TABLES LIKE 'fee_payments'");
        if ($checkTable->num_rows == 0) {
            $createTable = "CREATE TABLE fee_payments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                fee_id INT NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                payment_date DATE NOT NULL,
                payment_method VARCHAR(50) NOT NULL,
                receipt_no VARCHAR(50) DEFAULT NULL,
                notes TEXT DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (fee_id) REFERENCES fees(id) ON DELETE CASCADE
            )";
            $conn->query($createTable);
            echo "<p>fee_payments টেবিল তৈরি করা হয়েছে</p>";
        }
        
        // Create payment
        $receiptNo = 'RCP-' . date('Ymd') . '-' . rand(1000, 9999);
        $insertPayment = "INSERT INTO fee_payments (fee_id, receipt_no, amount, payment_date, payment_method, notes) 
                         VALUES (?, ?, ?, ?, 'cash', 'টেস্ট পেমেন্ট')";
        $stmt = $conn->prepare($insertPayment);
        $amount = 1500.00;
        $paymentDate = date('Y-m-d');
        $stmt->bind_param('isds', $feeData['id'], $receiptNo, $amount, $paymentDate);
        
        if ($stmt->execute()) {
            echo "<p style='color: green;'>✅ টেস্ট পেমেন্ট সফলভাবে তৈরি হয়েছে!</p>";
            echo "<p><strong>রিসিপ্ট নং:</strong> $receiptNo</p>";
            echo "<p><strong>শিক্ষার্থী:</strong> " . $feeData['first_name'] . ' ' . $feeData['last_name'] . "</p>";
            echo "<p><strong>রোল নং:</strong> " . $feeData['roll_no'] . "</p>";
            echo "<p><strong>ক্লাস:</strong> " . $feeData['class_name'] . "</p>";
            echo "<p><strong>পরিমাণ:</strong> ৳" . number_format($amount, 2) . "</p>";
            
            echo "<hr>";
            echo "<h3>রিসিপ্ট লিংক:</h3>";
            echo "<p><a href='receipt_final.php?receipt_no=$receiptNo' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>রিসিপ্ট দেখুন</a></p>";
            echo "<p><a href='receipt_final.php?receipt_no=$receiptNo&debug=1' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ডিবাগ মোডে দেখুন</a></p>";
            echo "<p><a href='receipt_improved.php?receipt_no=$receiptNo' target='_blank' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>উন্নত রিসিপ্ট দেখুন</a></p>";
            
        } else {
            echo "<p style='color: red;'>❌ পেমেন্ট তৈরি করতে সমস্যা: " . $stmt->error . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ ফি ডেটা পাওয়া যায়নি</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<br><br><a href='fee_management.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ফি ম্যানেজমেন্টে ফিরে যান</a>";
?>
