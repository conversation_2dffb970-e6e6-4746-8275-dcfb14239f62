<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if subjects table exists
$subjectsTableCheckQuery = "SHOW TABLES LIKE 'subjects'";
$subjectsTableResult = $conn->query($subjectsTableCheckQuery);

if (!$subjectsTableResult) {
    $error_message = "বিষয় টেবিল চেক করতে সমস্যা হয়েছে: " . $conn->error;
} else {
    $subjectsTableExists = $subjectsTableResult->num_rows > 0;

    if (!$subjectsTableExists) {
        // Create subjects table
        $createSubjectsTableQuery = "CREATE TABLE subjects (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            subject_name VARCHAR(255) NOT NULL,
            subject_code VARCHAR(50) NOT NULL,
            subject_description TEXT,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        if ($conn->query($createSubjectsTableQuery)) {
            $success_message = "বিষয় টেবিল সফলভাবে তৈরি করা হয়েছে।";

            // Add some sample subjects
            $insertSubjectsQuery = "INSERT INTO subjects (subject_name, subject_code, subject_description) VALUES
                ('বাংলা', 'BAN101', 'বাংলা ভাষা ও সাহিত্য'),
                ('ইংরেজি', 'ENG101', 'ইংরেজি ভাষা ও সাহিত্য'),
                ('গণিত', 'MAT101', 'গণিত'),
                ('বিজ্ঞান', 'SCI101', 'বিজ্ঞান'),
                ('সামাজিক বিজ্ঞান', 'SOC101', 'সামাজিক বিজ্ঞান'),
                ('ধর্ম', 'REL101', 'ধর্ম শিক্ষা'),
                ('তথ্য ও যোগাযোগ প্রযুক্তি', 'ICT101', 'তথ্য ও যোগাযোগ প্রযুক্তি')";

            if ($conn->query($insertSubjectsQuery)) {
                $success_message .= " এবং নমুনা বিষয় যোগ করা হয়েছে।";
            } else {
                $error_message = "নমুনা বিষয় যোগ করতে সমস্যা হয়েছে: " . $conn->error;
            }
        } else {
            $error_message = "বিষয় টেবিল তৈরি করতে সমস্যা হয়েছে: " . $conn->error;
        }
    } else {
        // Check if subjects table has data
        $checkSubjectsDataQuery = "SELECT COUNT(*) as count FROM subjects";
        $subjectsDataResult = $conn->query($checkSubjectsDataQuery);

        if ($subjectsDataResult) {
            $subjectsCount = $subjectsDataResult->fetch_assoc()['count'];

            if ($subjectsCount == 0) {
                // Add some sample subjects
                $insertSubjectsQuery = "INSERT INTO subjects (subject_name, subject_code, subject_description) VALUES
                    ('বাংলা', 'BAN101', 'বাংলা ভাষা ও সাহিত্য'),
                    ('ইংরেজি', 'ENG101', 'ইংরেজি ভাষা ও সাহিত্য'),
                    ('গণিত', 'MAT101', 'গণিত'),
                    ('বিজ্ঞান', 'SCI101', 'বিজ্ঞান'),
                    ('সামাজিক বিজ্ঞান', 'SOC101', 'সামাজিক বিজ্ঞান'),
                    ('ধর্ম', 'REL101', 'ধর্ম শিক্ষা'),
                    ('তথ্য ও যোগাযোগ প্রযুক্তি', 'ICT101', 'তথ্য ও যোগাযোগ প্রযুক্তি')";

                if ($conn->query($insertSubjectsQuery)) {
                    $success_message = "নমুনা বিষয় সফলভাবে যোগ করা হয়েছে।";
                } else {
                    $error_message = "নমুনা বিষয় যোগ করতে সমস্যা হয়েছে: " . $conn->error;
                }
            } else {
                $success_message = "বিষয় টেবিলে ইতিমধ্যে $subjectsCount টি বিষয় আছে।";
            }
        } else {
            $error_message = "বিষয় ডাটা চেক করতে সমস্যা হয়েছে: " . $conn->error;
        }
    }
}

// Display results
echo "<!DOCTYPE html>
<html lang='bn'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>বিষয় টেবিল চেক</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body>
    <div class='container mt-5'>
        <div class='row justify-content-center'>
            <div class='col-md-8'>
                <div class='card shadow'>
                    <div class='card-header bg-primary text-white'>
                        <h3 class='mb-0'>বিষয় টেবিল চেক</h3>
                    </div>
                    <div class='card-body'>";

if (isset($success_message)) {
    echo "<div class='alert alert-success'>
            <h4 class='alert-heading'>সফল!</h4>
            <p>$success_message</p>
          </div>";
}

if (isset($error_message)) {
    echo "<div class='alert alert-danger'>
            <h4 class='alert-heading'>ত্রুটি!</h4>
            <p>$error_message</p>
          </div>";
}

// Display subjects if they exist
if ($subjectsTableExists) {
    $subjectsQuery = "SELECT * FROM subjects ORDER BY subject_name";
    $subjects = $conn->query($subjectsQuery);

    if ($subjects && $subjects->num_rows > 0) {
        echo "<h4 class='mt-4'>বিষয় তালিকা</h4>
              <div class='table-responsive'>
                <table class='table table-striped table-hover'>
                    <thead class='table-light'>
                        <tr>
                            <th>আইডি</th>
                            <th>বিষয়ের নাম</th>
                            <th>বিষয় কোড</th>
                            <th>বিবরণ</th>
                            <th>স্ট্যাটাস</th>
                        </tr>
                    </thead>
                    <tbody>";

        while ($subject = $subjects->fetch_assoc()) {
            $status = $subject['is_active'] ? '<span class="badge bg-success">সক্রিয়</span>' : '<span class="badge bg-danger">নিষ্ক্রিয়</span>';
            $description = isset($subject['subject_description']) ? $subject['subject_description'] : '';
            echo "<tr>
                    <td>{$subject['id']}</td>
                    <td>{$subject['subject_name']}</td>
                    <td>{$subject['subject_code']}</td>
                    <td>{$description}</td>
                    <td>$status</td>
                  </tr>";
        }

        echo "</tbody>
              </table>
              </div>";
    } else {
        echo "<div class='alert alert-warning'>
                <p>কোন বিষয় পাওয়া যায়নি।</p>
              </div>";
    }
}

echo "          <div class='mt-4'>
                    <a href='subject_marks_distribution.php' class='btn btn-primary'>বিষয় মার্কস ডিস্ট্রিবিউশন পেজে ফিরে যান</a>
                    <a href='fix_database.php' class='btn btn-warning ms-2'>ডাটাবেস ঠিক করুন</a>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>";
?>
