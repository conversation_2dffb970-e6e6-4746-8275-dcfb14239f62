<?php
// Include database connection
require_once "includes/dbh.inc.php";

echo "<h2>Departments</h2>";
$result = $conn->query("SELECT id, department_name FROM departments");
if ($result && $result->num_rows > 0) {
    echo "<ul>";
    while ($row = $result->fetch_assoc()) {
        echo "<li>ID: " . $row['id'] . " - " . $row['department_name'] . "</li>";
    }
    echo "</ul>";
} else {
    echo "<p>No departments found.</p>";
}

echo "<h2>Subjects</h2>";
$result = $conn->query("SELECT id, subject_name, subject_code, category FROM subjects WHERE is_active = 1");
if ($result && $result->num_rows > 0) {
    echo "<ul>";
    while ($row = $result->fetch_assoc()) {
        echo "<li>ID: " . $row['id'] . " - " . $row['subject_name'] . " (" . $row['subject_code'] . ") - Category: " . $row['category'] . "</li>";
    }
    echo "</ul>";
} else {
    echo "<p>No subjects found.</p>";
}

echo "<h2>Science Department Subjects</h2>";
$result = $conn->query("SELECT s.id, s.subject_name, s.subject_code, s.category 
                       FROM subjects s
                       JOIN subject_departments sd ON s.id = sd.subject_id
                       JOIN departments d ON sd.department_id = d.id
                       WHERE (d.department_name LIKE '%Science%' OR d.department_name LIKE '%বিজ্ঞান%')
                       AND s.is_active = 1");
if ($result && $result->num_rows > 0) {
    echo "<ul>";
    while ($row = $result->fetch_assoc()) {
        echo "<li>ID: " . $row['id'] . " - " . $row['subject_name'] . " (" . $row['subject_code'] . ") - Category: " . $row['category'] . "</li>";
    }
    echo "</ul>";
} else {
    echo "<p>No science department subjects found.</p>";
}

// Create a form to assign subjects to science department
echo "<h2>Assign Subjects to Science Department</h2>";
echo "<form method='post' action='assign_science_subjects.php'>";
echo "<p>Select subjects to assign to the Science Department:</p>";

$result = $conn->query("SELECT id, subject_name, subject_code, category FROM subjects WHERE is_active = 1");
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        echo "<div>";
        echo "<input type='checkbox' name='subject_ids[]' value='" . $row['id'] . "' id='subject_" . $row['id'] . "'>";
        echo "<label for='subject_" . $row['id'] . "'>" . $row['subject_name'] . " (" . $row['subject_code'] . ") - " . $row['category'] . "</label>";
        echo "</div>";
    }
    echo "<p>Select category for these subjects:</p>";
    echo "<select name='category'>";
    echo "<option value='required'>Required (আবশ্যিক)</option>";
    echo "<option value='optional'>Optional (ঐচ্ছিক)</option>";
    echo "<option value='fourth'>Fourth (৪র্থ)</option>";
    echo "</select>";
    echo "<p><input type='submit' value='Assign Selected Subjects'></p>";
} else {
    echo "<p>No subjects available to assign.</p>";
}
echo "</form>";
?>
