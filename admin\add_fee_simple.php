<?php
// Disable caching
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");

session_start();
require_once '../includes/dbh.inc.php';

// Debug session data
error_log("Session data in add_fee_simple.php: " . print_r($_SESSION, true));

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Create necessary tables if they don't exist
// Create fee_types table
$conn->query("CREATE TABLE IF NOT EXISTS fee_types (
    id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_recurring TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    amount DECIMAL(10,2) DEFAULT 0.00
)");

// Create fee_categories table
$conn->query("CREATE TABLE IF NOT EXISTS fee_categories (
    id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)");

// Insert default category if it doesn't exist
$result = $conn->query("SELECT id FROM fee_categories WHERE id = 1");
if ($result->num_rows === 0) {
    $conn->query("INSERT INTO fee_categories (id, name, description) VALUES (1, 'Default', 'Default fee category')");
}

// Create fees table
$conn->query("CREATE TABLE IF NOT EXISTS fees (
    id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    fee_type VARCHAR(100) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    paid DECIMAL(10,2) DEFAULT 0.00,
    due_date DATE NOT NULL,
    payment_status ENUM('due','partial','paid') DEFAULT 'due',
    category_id INT(11) DEFAULT 1,
    payment_date DATE DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)");

// Process form submission
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    error_log("POST data received: " . print_r($_POST, true));

    $studentIds = isset($_POST['student_ids']) ? $_POST['student_ids'] : [];
    $feeType = isset($_POST['fee_type']) ? $_POST['fee_type'] : '';
    $amount = isset($_POST['amount']) ? floatval($_POST['amount']) : 0;
    $dueDate = isset($_POST['due_date']) ? $_POST['due_date'] : date('Y-m-d');

    error_log("Processing fee: Student IDs: " . implode(',', $studentIds) . ", Fee Type: $feeType, Amount: $amount, Due Date: $dueDate");

    // Validate input
    if (empty($studentIds) || empty($feeType) || $amount <= 0) {
        $message = 'সমস্ত প্রয়োজনীয় ফিল্ড পূরণ করুন!';
        $messageType = 'danger';
        error_log("Validation failed: $message");
    } else {
        // Start transaction
        $conn->begin_transaction();

        try {
            $successCount = 0;
            $existingCount = 0;
            $errorCount = 0;

            // Prepare statements
            $checkQuery = "SELECT id FROM fees WHERE student_id = ? AND fee_type = ? AND due_date = ?";
            $checkStmt = $conn->prepare($checkQuery);

            $insertQuery = "INSERT INTO fees (student_id, fee_type, amount, due_date, payment_status, category_id)
                           VALUES (?, ?, ?, ?, 'due', 1)";
            $insertStmt = $conn->prepare($insertQuery);

            // Process each student
            foreach ($studentIds as $studentId) {
                $studentId = intval($studentId);
                error_log("Processing student ID: $studentId");

                // Check if fee already exists
                $checkStmt->bind_param('iss', $studentId, $feeType, $dueDate);
                $checkStmt->execute();
                $result = $checkStmt->get_result();

                if ($result->num_rows > 0) {
                    $existingCount++;
                    error_log("Fee already exists for student ID: $studentId");
                } else {
                    // Insert new fee
                    $insertStmt->bind_param('isds', $studentId, $feeType, $amount, $dueDate);

                    if ($insertStmt->execute()) {
                        $successCount++;
                        error_log("Fee added successfully for student ID: $studentId");
                    } else {
                        $errorCount++;
                        error_log("Error adding fee for student ID: $studentId - " . $insertStmt->error);
                    }
                }
            }

            // Commit transaction
            $conn->commit();

            // Set appropriate message
            if ($successCount > 0) {
                $message = $successCount . ' জন শিক্ষার্থীর ফি সফলভাবে যোগ করা হয়েছে!';
                $messageType = 'success';

                if ($existingCount > 0) {
                    $message .= ' ' . $existingCount . ' জন শিক্ষার্থীর ফি ইতিমধ্যে বিদ্যমান ছিল।';
                }

                if ($errorCount > 0) {
                    $message .= ' ' . $errorCount . ' জন শিক্ষার্থীর ফি যোগ করতে সমস্যা হয়েছে।';
                }
            } else if ($existingCount > 0 && $errorCount == 0) {
                $message = 'সকল শিক্ষার্থীর ফি ইতিমধ্যে বিদ্যমান!';
                $messageType = 'warning';
            } else {
                $message = 'ফি যোগ করতে সমস্যা হয়েছে।';
                $messageType = 'danger';
            }

            error_log("Final result: Success: $successCount, Existing: $existingCount, Errors: $errorCount");

        } catch (Exception $e) {
            // Roll back transaction on error
            $conn->rollback();
            $message = 'ফি যোগ করতে সমস্যা: ' . $e->getMessage();
            $messageType = 'danger';
            error_log("Transaction rolled back. Error: " . $e->getMessage());
        }
    }
}

// Get all sessions for dropdown
$sessionsQuery = "SELECT * FROM sessions ORDER BY session_name DESC";
$sessionsResult = $conn->query($sessionsQuery);

// Get all classes for dropdown
$classesQuery = "SELECT * FROM classes ORDER BY class_name";
$classesResult = $conn->query($classesQuery);

// Get all departments for dropdown
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departmentsResult = $conn->query($departmentsQuery);

// Get all students for dropdown (will be filtered by AJAX)
$studentsQuery = "SELECT s.id, s.first_name, s.last_name, s.student_id, c.class_name, ss.session_name, d.department_name,
                 s.class_id, s.session_id, s.department_id, s.gender
                 FROM students s
                 LEFT JOIN classes c ON s.class_id = c.id
                 LEFT JOIN sessions ss ON s.session_id = ss.id
                 LEFT JOIN departments d ON s.department_id = d.id
                 ORDER BY s.first_name, s.last_name";
$studentsResult = $conn->query($studentsQuery);

// Get all fee types for dropdown
$feeTypesQuery = "SELECT * FROM fee_types ORDER BY name";
$feeTypesResult = $conn->query($feeTypesQuery);

// Insert some default fee types if none exist
if ($feeTypesResult->num_rows === 0) {
    $defaultFeeTypes = [
        ["মাসিক বেতন", "প্রতি মাসে দিতে হবে", 1, 150.00],
        ["ভর্তি ফি", "ভর্তির সময় একবার দিতে হবে", 0, 500.00],
        ["পরীক্ষার ফি", "পরীক্ষার আগে দিতে হবে", 0, 200.00]
    ];

    foreach ($defaultFeeTypes as $feeType) {
        $conn->query("INSERT INTO fee_types (name, description, is_recurring, amount)
                     VALUES ('{$feeType[0]}', '{$feeType[1]}', {$feeType[2]}, {$feeType[3]})");
    }

    // Refresh fee types
    $feeTypesResult = $conn->query($feeTypesQuery);
}

// Include header
include_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include_once 'includes/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">সহজ ফি যোগ করুন</h1>
            </div>

            <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-plus-circle me-2"></i> ফি যোগ করুন
                </div>
                <div class="card-body">
                    <!-- Form validation error message -->
                    <div class="alert alert-danger" id="form-error" style="display: none;"></div>
                    <form method="post" action="add_fee_simple.php" id="feeForm">
                        <!-- Filter Options -->
                        <div class="card mb-3">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="fas fa-filter me-2"></i> ফিল্টার অপশন</h6>
                                <button type="button" class="btn btn-sm btn-link text-decoration-none" id="toggleAdvancedSearch">
                                    <i class="fas fa-sliders-h me-1"></i> উন্নত ফিল্টার <i class="fas fa-chevron-down ms-1" id="advancedSearchIcon"></i>
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="session_id" class="form-label">সেশন</label>
                                        <select class="form-select filter-select" id="session_id" name="session_id">
                                            <option value="">সকল সেশন</option>
                                            <?php while ($session = $sessionsResult->fetch_assoc()): ?>
                                                <option value="<?php echo $session['id']; ?>">
                                                    <?php echo $session['session_name']; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="class_id" class="form-label">শ্রেণী</label>
                                        <select class="form-select filter-select" id="class_id" name="class_id">
                                            <option value="">সকল শ্রেণী</option>
                                            <?php while ($class = $classesResult->fetch_assoc()): ?>
                                                <option value="<?php echo $class['id']; ?>">
                                                    <?php echo $class['class_name']; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="department_id" class="form-label">বিভাগ</label>
                                        <select class="form-select filter-select" id="department_id" name="department_id">
                                            <option value="">সকল বিভাগ</option>
                                            <?php while ($department = $departmentsResult->fetch_assoc()): ?>
                                                <option value="<?php echo $department['id']; ?>">
                                                    <?php echo $department['department_name']; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        </select>
                                    </div>
                                </div>

                                <!-- Advanced Filter Options (Initially Hidden) -->
                                <div class="advanced-search-section" style="display: none;">
                                    <hr>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="student_search" class="form-label">শিক্ষার্থী খুঁজুন</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                                <input type="text" class="form-control" id="student_search" placeholder="নাম বা রোল দিয়ে খুঁজুন">
                                                <button type="button" class="btn btn-outline-secondary clear-input" data-target="student_search">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="student_gender" class="form-label">লিঙ্গ</label>
                                            <select class="form-select filter-select" id="student_gender">
                                                <option value="">সকল</option>
                                                <option value="male">ছেলে</option>
                                                <option value="female">মেয়ে</option>
                                                <option value="other">অন্যান্য</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Student Selection Section -->
                        <div class="card mb-3">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="fas fa-users me-2"></i> শিক্ষার্থী নির্বাচন</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="select_all_students">
                                    <label class="form-check-label" for="select_all_students">
                                        সকল শিক্ষার্থী নির্বাচন করুন
                                    </label>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="student-selection-container border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                    <div id="student_list">
                                        <?php
                                        // Reset result pointer
                                        $studentsResult->data_seek(0);
                                        while ($student = $studentsResult->fetch_assoc()):
                                        ?>
                                            <div class="form-check mb-2 student-item"
                                                 data-class="<?php echo $student['class_id']; ?>"
                                                 data-session="<?php echo $student['session_id']; ?>"
                                                 data-department="<?php echo $student['department_id']; ?>"
                                                 data-gender="<?php echo $student['gender']; ?>">
                                                <input class="form-check-input student-checkbox" type="checkbox"
                                                       name="student_ids[]" value="<?php echo $student['id']; ?>"
                                                       id="student_<?php echo $student['id']; ?>">
                                                <label class="form-check-label" for="student_<?php echo $student['id']; ?>">
                                                    <?php echo $student['first_name'] . ' ' . $student['last_name']; ?>
                                                    (<?php echo $student['student_id']; ?>) -
                                                    <?php echo $student['class_name'] . ', ' . $student['session_name']; ?>
                                                    <?php if (!empty($student['department_name'])): ?>
                                                        - <?php echo $student['department_name']; ?>
                                                    <?php endif; ?>
                                                </label>
                                            </div>
                                        <?php endwhile; ?>
                                    </div>
                                </div>
                                <div class="mt-2 text-end">
                                    <span class="badge bg-primary" id="selected_students_count">0</span> জন শিক্ষার্থী নির্বাচিত
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="fee_type" class="form-label">ফি ধরন <span class="text-danger">*</span></label>
                                <select class="form-select" id="fee_type" name="fee_type" required>
                                    <option value="">ফি ধরন নির্বাচন করুন</option>
                                    <?php while ($feeType = $feeTypesResult->fetch_assoc()): ?>
                                        <option value="<?php echo $feeType['name']; ?>" data-amount="<?php echo $feeType['amount']; ?>">
                                            <?php echo $feeType['name']; ?>
                                            <?php if (!empty($feeType['amount'])): ?>
                                                (৳<?php echo number_format($feeType['amount'], 2); ?>)
                                            <?php endif; ?>
                                        </option>
                                    <?php endwhile; ?>
                                    <option value="other">অন্যান্য</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3" id="other_fee_type_div" style="display: none;">
                            <div class="col-md-12">
                                <label for="other_fee_type" class="form-label">অন্য ফি ধরন <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="other_fee_type" name="other_fee_type">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="amount" class="form-label">পরিমাণ (৳) <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">৳</span>
                                    <input type="number" class="form-control" id="amount" name="amount" min="0" step="0.01" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="due_date" class="form-label">তারিখ <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="due_date" name="due_date" value="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                        </div>

                        <div class="text-end">
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-plus-circle me-1"></i> ফি যোগ করুন
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle fee type selection
    const feeTypeSelect = document.getElementById('fee_type');
    const otherFeeTypeDiv = document.getElementById('other_fee_type_div');
    const otherFeeTypeInput = document.getElementById('other_fee_type');
    const amountInput = document.getElementById('amount');

    feeTypeSelect.addEventListener('change', function() {
        console.log('Fee type changed to:', this.value);

        if (this.value === 'other') {
            otherFeeTypeDiv.style.display = 'block';
            otherFeeTypeInput.setAttribute('required', 'required');
            amountInput.value = '';
        } else {
            otherFeeTypeDiv.style.display = 'none';
            otherFeeTypeInput.removeAttribute('required');

            // Set amount based on selected fee type
            const selectedOption = this.options[this.selectedIndex];
            const amount = selectedOption.getAttribute('data-amount');
            console.log('Amount from data attribute:', amount);

            if (amount) {
                amountInput.value = amount;
            }
        }
    });

    // Toggle advanced search section
    const toggleAdvancedSearch = document.getElementById('toggleAdvancedSearch');
    const advancedSearchSection = document.querySelector('.advanced-search-section');
    const advancedSearchIcon = document.getElementById('advancedSearchIcon');

    if (toggleAdvancedSearch && advancedSearchSection && advancedSearchIcon) {
        toggleAdvancedSearch.addEventListener('click', function() {
            if (advancedSearchSection.style.display === 'none') {
                advancedSearchSection.style.display = 'block';
                advancedSearchIcon.classList.remove('fa-chevron-down');
                advancedSearchIcon.classList.add('fa-chevron-up');
            } else {
                advancedSearchSection.style.display = 'none';
                advancedSearchIcon.classList.remove('fa-chevron-up');
                advancedSearchIcon.classList.add('fa-chevron-down');
            }
        });
    }

    // Filter students based on selected criteria
    const sessionSelect = document.getElementById('session_id');
    const classSelect = document.getElementById('class_id');
    const departmentSelect = document.getElementById('department_id');
    const genderSelect = document.getElementById('student_gender');
    const studentSearchInput = document.getElementById('student_search');
    const studentItems = document.querySelectorAll('.student-item');
    const selectAllCheckbox = document.getElementById('select_all_students');
    const selectedStudentsCount = document.getElementById('selected_students_count');

    // Function to update selected students count
    function updateSelectedStudentsCount() {
        const checkedCheckboxes = document.querySelectorAll('.student-checkbox:checked');
        selectedStudentsCount.textContent = checkedCheckboxes.length;
    }

    // Function to filter students
    function filterStudents() {
        const sessionId = sessionSelect.value;
        const classId = classSelect.value;
        const departmentId = departmentSelect.value;
        const gender = genderSelect ? genderSelect.value : '';
        const searchTerm = studentSearchInput ? studentSearchInput.value.toLowerCase() : '';

        console.log('Filtering students with:', { sessionId, classId, departmentId, gender, searchTerm });

        let visibleCount = 0;

        // Process each student item
        studentItems.forEach(item => {
            const optionSessionId = item.getAttribute('data-session');
            const optionClassId = item.getAttribute('data-class');
            const optionDepartmentId = item.getAttribute('data-department');
            const optionGender = item.getAttribute('data-gender');
            const optionText = item.textContent.toLowerCase();

            // Check if item matches all selected filters
            const matchesSession = !sessionId || optionSessionId === sessionId;
            const matchesClass = !classId || optionClassId === classId;
            const matchesDepartment = !departmentId || optionDepartmentId === departmentId;
            const matchesGender = !gender || optionGender === gender;
            const matchesSearch = !searchTerm || optionText.includes(searchTerm);

            // Show or hide item based on filter matches
            if (matchesSession && matchesClass && matchesDepartment && matchesGender && matchesSearch) {
                item.style.display = '';
                visibleCount++;
            } else {
                item.style.display = 'none';
            }
        });

        console.log('Visible students after filtering:', visibleCount);

        // Update select all checkbox state
        updateSelectAllCheckbox();
    }

    // Function to update select all checkbox state
    function updateSelectAllCheckbox() {
        const visibleCheckboxes = Array.from(document.querySelectorAll('.student-item')).filter(
            item => item.style.display !== 'none'
        ).map(
            item => item.querySelector('.student-checkbox')
        );

        const checkedVisibleCheckboxes = visibleCheckboxes.filter(checkbox => checkbox.checked);

        if (visibleCheckboxes.length === 0) {
            selectAllCheckbox.disabled = true;
            selectAllCheckbox.checked = false;
        } else {
            selectAllCheckbox.disabled = false;
            selectAllCheckbox.checked = visibleCheckboxes.length === checkedVisibleCheckboxes.length;
        }

        // Update selected students count
        updateSelectedStudentsCount();
    }

    // Add event listeners to filter elements
    const filterElements = [sessionSelect, classSelect, departmentSelect, genderSelect];
    filterElements.forEach(element => {
        if (element) {
            element.addEventListener('change', filterStudents);
        }
    });

    // Add event listener to search input
    if (studentSearchInput) {
        studentSearchInput.addEventListener('input', filterStudents);
    }

    // Clear input button functionality
    const clearButtons = document.querySelectorAll('.clear-input');
    clearButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const targetInput = document.getElementById(targetId);
            if (targetInput) {
                targetInput.value = '';
                // Trigger the input event to update filters
                targetInput.dispatchEvent(new Event('input'));
            }
        });
    });

    // Handle select all checkbox
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;

            // Get all visible student checkboxes
            const visibleCheckboxes = Array.from(document.querySelectorAll('.student-item')).filter(
                item => item.style.display !== 'none'
            ).map(
                item => item.querySelector('.student-checkbox')
            );

            // Set all visible checkboxes to the same state as the select all checkbox
            visibleCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });

            // Update selected students count
            updateSelectedStudentsCount();
        });
    }

    // Handle individual student checkboxes
    const studentCheckboxes = document.querySelectorAll('.student-checkbox');
    studentCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            // Update select all checkbox state
            updateSelectAllCheckbox();
        });
    });

    // Initialize selected students count
    updateSelectedStudentsCount();

    // Form validation
    const feeForm = document.getElementById('feeForm');
    const formError = document.getElementById('form-error');
    const submitBtn = document.getElementById('submitBtn');

    if (feeForm) {
        feeForm.addEventListener('submit', function(e) {
            // Reset error message
            formError.style.display = 'none';
            formError.textContent = '';

            // Check if at least one student is selected
            const selectedStudents = document.querySelectorAll('.student-checkbox:checked');
            if (selectedStudents.length === 0) {
                e.preventDefault();
                formError.textContent = 'কমপক্ষে একজন শিক্ষার্থী নির্বাচন করুন!';
                formError.style.display = 'block';
                formError.scrollIntoView({ behavior: 'smooth' });
                return false;
            }

            // Check if fee type is selected
            const feeTypeValue = document.getElementById('fee_type').value;
            if (!feeTypeValue) {
                e.preventDefault();
                formError.textContent = 'ফি ধরন নির্বাচন করুন!';
                formError.style.display = 'block';
                formError.scrollIntoView({ behavior: 'smooth' });
                return false;
            }

            // Check if amount is valid
            const amountValue = parseFloat(document.getElementById('amount').value);
            if (isNaN(amountValue) || amountValue <= 0) {
                e.preventDefault();
                formError.textContent = 'সঠিক পরিমাণ প্রদান করুন!';
                formError.style.display = 'block';
                formError.scrollIntoView({ behavior: 'smooth' });
                return false;
            }

            // If all validations pass, show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> প্রক্রিয়াকরণ হচ্ছে...';
            submitBtn.disabled = true;

            return true;
        });
    }
});
</script>

<?php
// Include footer
include_once 'includes/footer.php';
?>
