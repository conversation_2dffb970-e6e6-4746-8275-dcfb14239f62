<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

$errorMessage = '';
$successMessage = '';

// Check for session messages
if (isset($_SESSION['success_message'])) {
    $successMessage = $_SESSION['success_message'];
    unset($_SESSION['success_message']);
}

if (isset($_SESSION['error_message'])) {
    $errorMessage = $_SESSION['error_message'];
    unset($_SESSION['error_message']);
}

// Process image upload
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check if file was uploaded without errors
    if (isset($_FILES['watermark_image']) && $_FILES['watermark_image']['error'] === 0) {
        $allowed = ['jpg', 'jpeg', 'png', 'gif'];
        $filename = $_FILES['watermark_image']['name'];
        $filesize = $_FILES['watermark_image']['size'];
        $filetype = $_FILES['watermark_image']['type'];

        // Get file extension
        $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

        // Verify file extension
        if (!in_array($ext, $allowed)) {
            $errorMessage = 'ছবির ফরম্যাট সঠিক নয়। অনুগ্রহ করে JPG, JPEG, PNG, বা GIF ফরম্যাটের ছবি আপলোড করুন।';
        } else {
            // Verify file size - 5MB maximum
            $maxsize = 5 * 1024 * 1024;
            if ($filesize > $maxsize) {
                $errorMessage = 'ছবির সাইজ খুব বড়। সর্বোচ্চ 5MB সাইজের ছবি আপলোড করুন।';
            } else {
                // Create uploads directory if it doesn't exist
                if (!file_exists('../uploads')) {
                    mkdir('../uploads', 0777, true);
                }

                // Save the uploaded file with a fixed name
                $new_filename = '../uploads/watermark.png';

                if (move_uploaded_file($_FILES['watermark_image']['tmp_name'], $new_filename)) {
                    $successMessage = 'ওয়াটারমার্ক ছবি সফলভাবে আপলোড করা হয়েছে।';
                } else {
                    $errorMessage = 'ছবি আপলোড করতে সমস্যা হয়েছে। অনুগ্রহ করে আবার চেষ্টা করুন।';
                }
            }
        }
    } else {
        $errorMessage = 'কোন ছবি নির্বাচন করা হয়নি বা আপলোড করতে সমস্যা হয়েছে।';
    }
}

// Check if watermark image exists
$watermarkExists = file_exists('../uploads/watermark.png');
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>ওয়াটারমার্ক আপলোড - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .upload-form {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .preview-container {
            margin-top: 30px;
            text-align: center;
        }
        .watermark-preview {
            max-width: 300px;
            max-height: 300px;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 0 auto;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .watermark-preview img {
            max-width: 100%;
            max-height: 280px;
        }
        .file-input-wrapper {
            position: relative;
            overflow: hidden;
            display: inline-block;
        }
        .file-input-wrapper input[type=file] {
            font-size: 100px;
            position: absolute;
            left: 0;
            top: 0;
            opacity: 0;
            cursor: pointer;
        }
        .file-input-button {
            display: inline-block;
            padding: 8px 16px;
            background-color: #6c757d;
            color: white;
            border-radius: 4px;
            cursor: pointer;
        }
        .selected-file {
            margin-top: 10px;
            font-style: italic;
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="certificates.php">
                            <i class="fas fa-certificate me-2"></i> সার্টিফিকেট
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="simple_certificate.php">
                            <i class="fas fa-file-alt me-2"></i> সাধারণ সার্টিফিকেট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="upload_watermark.php">
                            <i class="fas fa-image me-2"></i> ওয়াটারমার্ক আপলোড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>ওয়াটারমার্ক ছবি আপলোড করুন</h2>
                        <p class="text-muted">সার্টিফিকেটের জন্য ওয়াটারমার্ক ছবি আপলোড করুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="simple_certificate.php" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>সার্টিফিকেট পেজে ফিরে যান
                        </a>
                    </div>
                </div>

                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Upload Form -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="upload-form">
                            <form method="POST" action="upload_watermark.php" enctype="multipart/form-data">
                                <div class="mb-4">
                                    <label for="watermark_image" class="form-label">ওয়াটারমার্ক ছবি নির্বাচন করুন*</label>
                                    <div class="file-input-wrapper">
                                        <button class="file-input-button" type="button">
                                            <i class="fas fa-upload me-2"></i>ছবি নির্বাচন করুন
                                        </button>
                                        <input type="file" name="watermark_image" id="watermark_image" accept="image/*" required>
                                    </div>
                                    <div class="selected-file" id="selected-file-name">কোন ফাইল নির্বাচন করা হয়নি</div>
                                    <div class="form-text">
                                        সর্বোচ্চ ফাইল সাইজ: 5MB<br>
                                        অনুমোদিত ফরম্যাট: JPG, JPEG, PNG, GIF<br>
                                        সর্বোত্তম ফলাফলের জন্য স্বচ্ছ পটভূমি (transparent background) সহ PNG ফাইল ব্যবহার করুন।
                                    </div>
                                </div>

                                <div class="text-center mt-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-upload me-2"></i>আপলোড করুন
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <?php if ($watermarkExists): ?>
                            <div class="preview-container">
                                <h4 class="mb-3">বর্তমান ওয়াটারমার্ক</h4>
                                <div class="watermark-preview">
                                    <img src="../uploads/watermark.png?v=<?php echo time(); ?>" alt="Current Watermark">
                                </div>
                                <div class="mt-3">
                                    <form method="POST" action="delete_watermark.php" onsubmit="return confirm('আপনি কি নিশ্চিত যে আপনি ওয়াটারমার্ক ছবি মুছতে চান?');">
                                        <button type="submit" class="btn btn-danger">
                                            <i class="fas fa-trash me-2"></i>ওয়াটারমার্ক মুছুন
                                        </button>
                                    </form>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="preview-container">
                                <h4 class="mb-3">কোন ওয়াটারমার্ক নেই</h4>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>এখনো কোন ওয়াটারমার্ক ছবি আপলোড করা হয়নি। ওয়াটারমার্ক ছবি আপলোড করলে সার্টিফিকেটের পিছনে প্রদর্শিত হবে।
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.getElementById('watermark_image');
            const fileNameDisplay = document.getElementById('selected-file-name');

            fileInput.addEventListener('change', function() {
                if (fileInput.files.length > 0) {
                    fileNameDisplay.textContent = fileInput.files[0].name;
                } else {
                    fileNameDisplay.textContent = 'কোন ফাইল নির্বাচন করা হয়নি';
                }
            });
        });
    </script>
</body>
</html>
