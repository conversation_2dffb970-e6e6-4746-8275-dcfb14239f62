-- <PERSON><PERSON>t to create the certificates table

-- Check if table exists and drop it if it does (optional, comment out if you don't want to drop existing table)
-- DROP TABLE IF EXISTS `certificates`;

-- Create certificates table
CREATE TABLE IF NOT EXISTS `certificates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `certificate_date` date NOT NULL,
  `issued_by` varchar(255) NOT NULL,
  `certificate_type` varchar(50) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `student_id` (`student_id`),
  CONSTRAINT `certificates_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Display success message
SELECT 'Certificates table created successfully!' AS 'Message';
