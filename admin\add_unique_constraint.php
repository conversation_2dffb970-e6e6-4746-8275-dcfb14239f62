<?php
require_once '../includes/dbh.inc.php';

echo "<h2>Adding Unique Constraint to Classes Table</h2>";

// First, check if unique constraint already exists
echo "<h3>1. Checking Current Table Structure</h3>";

$showIndexQuery = "SHOW INDEX FROM classes WHERE Key_name = 'class_name'";
$indexResult = $conn->query($showIndexQuery);

if ($indexResult && $indexResult->num_rows > 0) {
    echo "<p style='color: green;'>✅ Unique constraint already exists on class_name column.</p>";
    
    while ($row = $indexResult->fetch_assoc()) {
        echo "<p>Index: " . $row['Key_name'] . ", Column: " . $row['Column_name'] . ", Non_unique: " . $row['Non_unique'] . "</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠️ No unique constraint found on class_name column.</p>";
    
    // Add unique constraint
    echo "<h3>2. Adding Unique Constraint</h3>";
    
    $addConstraintQuery = "ALTER TABLE classes ADD UNIQUE KEY unique_class_name (class_name)";
    
    if ($conn->query($addConstraintQuery)) {
        echo "<p style='color: green;'>✅ Successfully added unique constraint to class_name column!</p>";
        echo "<p>This will prevent duplicate class names from being inserted in the future.</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to add unique constraint: " . $conn->error . "</p>";
        
        // If it fails, it might be because there are still duplicates
        if (strpos($conn->error, 'Duplicate entry') !== false) {
            echo "<p style='color: orange;'>⚠️ Cannot add unique constraint because duplicate entries still exist.</p>";
            echo "<p>Please run the <a href='remove_duplicate_classes.php'>duplicate removal script</a> first.</p>";
        }
    }
}

// Show current table structure
echo "<h3>3. Current Table Structure</h3>";

$describeQuery = "DESCRIBE classes";
$describeResult = $conn->query($describeQuery);

if ($describeResult) {
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th>";
    echo "</tr>";
    
    while ($row = $describeResult->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
}

// Show all indexes
echo "<h3>4. All Indexes on Classes Table</h3>";

$showAllIndexQuery = "SHOW INDEX FROM classes";
$allIndexResult = $conn->query($showAllIndexQuery);

if ($allIndexResult && $allIndexResult->num_rows > 0) {
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>Key Name</th><th>Column</th><th>Unique</th><th>Type</th>";
    echo "</tr>";
    
    while ($row = $allIndexResult->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Key_name'] . "</td>";
        echo "<td>" . $row['Column_name'] . "</td>";
        echo "<td>" . ($row['Non_unique'] == 0 ? 'Yes' : 'No') . "</td>";
        echo "<td>" . $row['Index_type'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>No indexes found.</p>";
}

// Test the constraint
echo "<h3>5. Testing Duplicate Prevention</h3>";

// Try to insert a duplicate (this should fail if constraint is working)
$testClassName = "TEST_DUPLICATE_CLASS";

// First, clean up any existing test class
$cleanupQuery = "DELETE FROM classes WHERE class_name = '$testClassName'";
$conn->query($cleanupQuery);

// Insert first instance
$insertTest1 = "INSERT INTO classes (class_name) VALUES ('$testClassName')";
if ($conn->query($insertTest1)) {
    echo "<p style='color: green;'>✅ First test class inserted successfully.</p>";
    
    // Try to insert duplicate
    $insertTest2 = "INSERT INTO classes (class_name) VALUES ('$testClassName')";
    if ($conn->query($insertTest2)) {
        echo "<p style='color: red;'>❌ Duplicate was allowed! Constraint is not working.</p>";
    } else {
        echo "<p style='color: green;'>✅ Duplicate insertion prevented! Constraint is working.</p>";
        echo "<p>Error: " . $conn->error . "</p>";
    }
    
    // Clean up test class
    $cleanupQuery = "DELETE FROM classes WHERE class_name = '$testClassName'";
    $conn->query($cleanupQuery);
    echo "<p style='color: blue;'>ℹ️ Test class cleaned up.</p>";
    
} else {
    echo "<p style='color: red;'>❌ Failed to insert test class: " . $conn->error . "</p>";
}

echo "<br><a href='classes.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>← Back to Classes</a>";

$conn->close();
?>
