<?php
session_start();
require_once 'includes/dbh.inc.php';

// Check if user is logged in
if (!isset($_SESSION['userId'])) {
    header("Location: login.php");
    exit();
}

// Get user information
$userId = $_SESSION['userId'];
$userType = $_SESSION['userType'];

// Get user data
$userQuery = "SELECT * FROM users WHERE id = ?";
$stmt = $conn->prepare($userQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$userResult = $stmt->get_result();
$user = $userResult->fetch_assoc();

// Get profile data based on user type
$profileData = null;
$profileQuery = "";

switch ($userType) {
    case 'student':
        $profileQuery = "SELECT s.*, c.class_name, d.department_name
                        FROM students s
                        LEFT JOIN classes c ON s.class_id = c.id
                        LEFT JOIN departments d ON s.department_id = d.id
                        WHERE s.user_id = ?";
        break;
    case 'teacher':
        $profileQuery = "SELECT t.*, d.department_name
                        FROM teachers t
                        LEFT JOIN departments d ON t.department_id = d.id
                        WHERE t.user_id = ?";
        break;
    case 'admin':
        // For admin, we'll just use the user data
        $profileData = [
            'name' => 'Admin',
            'email' => $user['email'] ?? '<EMAIL>',
            'phone' => $user['phone'] ?? 'N/A',
            'role' => 'Administrator'
        ];
        break;
    case 'staff':
        $profileQuery = "SELECT s.*, d.department_name
                        FROM staff s
                        LEFT JOIN departments d ON s.department_id = d.id
                        WHERE s.user_id = ?";
        break;
}

// If we have a profile query, execute it
if (!empty($profileQuery)) {
    $stmt = $conn->prepare($profileQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $profileResult = $stmt->get_result();
    $profileData = $profileResult->fetch_assoc();
}

// Handle profile update
$successMessage = '';
$errorMessage = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    // Common fields
    $email = $_POST['email'] ?? '';
    $phone = $_POST['phone'] ?? '';
    $address = $_POST['address'] ?? '';

    // Update user table
    $updateUserQuery = "UPDATE users SET email = ?, phone = ? WHERE id = ?";
    $stmt = $conn->prepare($updateUserQuery);
    $stmt->bind_param("ssi", $email, $phone, $userId);

    if ($stmt->execute()) {
        // Update profile table based on user type
        $updateProfileQuery = "";

        switch ($userType) {
            case 'student':
                $updateProfileQuery = "UPDATE students SET email = ?, phone = ?, address = ? WHERE user_id = ?";
                break;
            case 'teacher':
                $updateProfileQuery = "UPDATE teachers SET email = ?, phone = ?, address = ? WHERE user_id = ?";
                break;
            case 'staff':
                $updateProfileQuery = "UPDATE staff SET email = ?, phone = ?, address = ? WHERE user_id = ?";
                break;
        }

        if (!empty($updateProfileQuery)) {
            $stmt = $conn->prepare($updateProfileQuery);
            $stmt->bind_param("sssi", $email, $phone, $address, $userId);

            if ($stmt->execute()) {
                $successMessage = "প্রোফাইল সফলভাবে আপডেট করা হয়েছে!";

                // Refresh profile data
                $stmt = $conn->prepare($profileQuery);
                $stmt->bind_param("i", $userId);
                $stmt->execute();
                $profileResult = $stmt->get_result();
                $profileData = $profileResult->fetch_assoc();
            } else {
                $errorMessage = "প্রোফাইল আপডেট করতে সমস্যা হয়েছে!";
            }
        } else {
            $successMessage = "প্রোফাইল সফলভাবে আপডেট করা হয়েছে!";
        }
    } else {
        $errorMessage = "ইউজার তথ্য আপডেট করতে সমস্যা হয়েছে!";
    }
}

// Handle password change
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['change_password'])) {
    $currentPassword = $_POST['current_password'] ?? '';
    $newPassword = $_POST['new_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';

    // Validate input
    if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
        $errorMessage = "সকল ফিল্ড পূরণ করুন!";
    } elseif ($newPassword !== $confirmPassword) {
        $errorMessage = "নতুন পাসওয়ার্ড এবং কনফার্ম পাসওয়ার্ড মিলছে না!";
    } elseif (strlen($newPassword) < 6) {
        $errorMessage = "পাসওয়ার্ড কমপক্ষে ৬ অক্ষরের হতে হবে!";
    } else {
        // Verify current password
        if (password_verify($currentPassword, $user['password'])) {
            // Hash new password
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);

            // Update password
            $updatePasswordQuery = "UPDATE users SET password = ? WHERE id = ?";
            $stmt = $conn->prepare($updatePasswordQuery);
            $stmt->bind_param("si", $hashedPassword, $userId);

            if ($stmt->execute()) {
                $successMessage = "পাসওয়ার্ড সফলভাবে পরিবর্তন করা হয়েছে!";
            } else {
                $errorMessage = "পাসওয়ার্ড পরিবর্তন করতে সমস্যা হয়েছে!";
            }
        } else {
            $errorMessage = "বর্তমান পাসওয়ার্ড সঠিক নয়!";
        }
    }
}

// Set page title
$page_title = "প্রোফাইল";
$school_name = "নিশাত এডুকেশন সেন্টার";
$school_address = "চুয়াডাঙ্গা, বাংলাদেশ";
$school_logo = "img/logo.jpg";
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title><?php echo $page_title; ?> - <?php echo $school_name; ?></title>

    <!-- Bootstrap CSS -->
    

    <!-- Custom Fonts CSS -->
    

    <!-- Custom CSS -->
    

    <style>
        :root {
            --primary-color: #006A4E; /* Deep Green */
            --secondary-color: #00563B; /* Darker Green */
            --accent-color: #F39C12; /* Amber/Gold */
            --dark-color: #2C3E50; /* Dark Blue-Gray */
            --light-color: #F5F5F5; /* Off-White */
            --text-color: #333333; /* Dark Gray */
            --light-text: #FFFFFF; /* White */
            --highlight-color: #E74C3C; /* Red Accent */
            --soft-color: #E3F2FD; /* Soft Blue */
        }

        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: var(--light-color);
            color: var(--text-color);
        }

        /* Header Styles */
        .header-top {
            background-color: white;
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .school-logo {
            width: 80px;
            height: 80px;
            object-fit: contain;
        }

        .school-title {
            color: var(--primary-color);
            font-weight: 700;
            font-size: 1.8rem;
        }

        .school-subtitle {
            color: var(--dark-color);
            font-weight: 500;
        }

        /* Navigation Styles */
        .main-nav {
            background-color: var(--primary-color);
            padding: 0;
        }

        .main-nav .nav-link {
            color: var(--light-text);
            font-weight: 500;
            padding: 15px 20px;
            transition: all 0.3s;
            border-radius: 0;
        }

        .main-nav .nav-link:hover,
        .main-nav .nav-link.active {
            background-color: var(--secondary-color);
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 60px 0;
            margin-bottom: 30px;
        }

        .hero-title {
            font-weight: 700;
            margin-bottom: 20px;
            color: white;
        }

        .hero-text {
            font-size: 1.1rem;
            margin-bottom: 30px;
            opacity: 0.9;
            color: white;
        }

        /* Card Styles */
        .card {
            border: none;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            height: 100%;
            background-color: white;
            margin-bottom: 20px;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .card-header {
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
            padding: 15px 20px;
            border: none;
        }

        .card-header.warning-header {
            background-color: var(--accent-color);
            color: var(--dark-color);
        }

        .card-body {
            padding: 25px;
        }

        /* Profile Image */
        .profile-img {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 50%;
            border: 5px solid white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .profile-img:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(0,0,0,0.25);
        }

        .profile-placeholder {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background-color: var(--dark-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            border: 5px solid white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .profile-placeholder:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(0,0,0,0.25);
        }

        /* Profile Details */
        .profile-details {
            margin-top: 20px;
        }

        .detail-item {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
            transition: all 0.2s;
        }

        .detail-item:hover {
            background-color: rgba(0, 106, 78, 0.05);
            padding-left: 5px;
            border-radius: 5px;
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-item i {
            color: var(--primary-color);
            width: 25px;
            text-align: center;
            margin-right: 10px;
        }

        /* Form Styles */
        .form-control {
            border-radius: 8px;
            padding: 12px 15px;
            border: 1px solid #ced4da;
            transition: all 0.3s;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(0, 106, 78, 0.25);
        }

        .form-label {
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--dark-color);
        }

        /* Button Styles */
        .btn-primary {
            background-color: var(--primary-color);
            border: none;
            padding: 10px 20px;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }

        .btn-warning {
            background-color: var(--accent-color);
            border: none;
            color: var(--dark-color);
            padding: 10px 20px;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .btn-warning:hover {
            background-color: #E67E22;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }

        /* Alert Styles */
        .alert {
            border-radius: 8px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
        }

        /* Footer Styles */
        .footer {
            background-color: var(--dark-color);
            color: white;
            padding: 40px 0 20px;
            margin-top: 50px;
        }

        .footer h5 {
            color: var(--light-color);
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--accent-color);
            display: inline-block;
        }

        .footer-links {
            list-style: none;
            padding: 0;
        }

        .footer-links li {
            margin-bottom: 10px;
        }

        .footer-links a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s;
        }

        .footer-links a:hover {
            color: white;
            padding-left: 5px;
        }

        .social-icons a {
            display: inline-block;
            width: 36px;
            height: 36px;
            background-color: rgba(255,255,255,0.1);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 36px;
            margin-right: 10px;
            transition: all 0.3s;
        }

        .social-icons a:hover {
            background-color: var(--accent-color);
            transform: translateY(-3px);
        }

        .copyright {
            background-color: rgba(0,0,0,0.2);
            padding: 15px 0;
            margin-top: 30px;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .school-title {
                font-size: 1.4rem;
            }

            .school-subtitle {
                font-size: 1rem;
            }

            .hero-section {
                padding: 40px 0;
            }

            .hero-img {
                margin-top: 30px;
            }
        }
    </style>
</head>
<body>
    <!-- Header Top with Logo and Title -->
    <div class="header-top">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-2 text-center text-md-start mb-3 mb-md-0">
                    <img src="<?php echo $school_logo; ?>" alt="School Logo" class="school-logo" onerror="this.src='https://via.placeholder.com/80?text=ZFAW'">
                </div>
                <div class="col-md-10 text-center text-md-start">
                    <h1 class="school-title mb-1"><?php echo $school_name; ?></h1>
                    <h2 class="school-subtitle fs-5"><?php echo $school_address; ?></h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Navigation -->
    <nav class="main-nav">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <ul class="nav">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php"><i class="fas fa-home me-1"></i> হোম</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="subjects.php"><i class="fas fa-book me-1"></i> বিষয়সমূহ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="teachers.php"><i class="fas fa-chalkboard-teacher me-1"></i> শিক্ষকবৃন্দ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="students.php"><i class="fas fa-user-graduate me-1"></i> শিক্ষার্থীবৃন্দ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="notices.php"><i class="fas fa-bullhorn me-1"></i> নোটিশ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="gb_members.php"><i class="fas fa-users me-1"></i> পরিচালনা বোর্ড</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="personal_sms.php"><i class="fas fa-sms me-1"></i> পার্সোনাল এসএমএস</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="profile.php"><i class="fas fa-user-circle me-1"></i> প্রোফাইল</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

<style>
    :root {
        --primary-color: #006A4E; /* Deep Green */
        --secondary-color: #00563B; /* Darker Green */
        --accent-color: #F39C12; /* Amber/Gold */
        --dark-color: #2C3E50; /* Dark Blue-Gray */
        --light-color: #F5F5F5; /* Off-White */
        --text-color: #333333; /* Dark Gray */
        --light-text: #FFFFFF; /* White */
        --highlight-color: #E74C3C; /* Red Accent */
        --soft-color: #E3F2FD; /* Soft Blue */
    }

    body {
        font-family: 'Hind Siliguri', sans-serif;
        background-color: var(--light-color);
        color: var(--text-color);
    }

    /* Profile Hero Section */
    .profile-hero {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 40px 0;
        margin-bottom: 30px;
        border-radius: 0 0 10px 10px;
    }

    .profile-hero h1 {
        font-weight: 700;
        margin-bottom: 10px;
        color: white;
    }

    .profile-hero p {
        font-size: 1.1rem;
        opacity: 0.9;
        color: white;
    }

    /* Card Styles */
    .card {
        border: none;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s, box-shadow 0.3s;
        height: 100%;
        background-color: white;
        margin-bottom: 25px;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .card-header {
        background-color: var(--primary-color);
        color: white;
        font-weight: 600;
        padding: 15px 20px;
        border: none;
    }

    .card-header.warning-header {
        background-color: var(--accent-color);
        color: var(--dark-color);
    }

    .card-body {
        padding: 25px;
    }

    /* Profile Image */
    .profile-img-container {
        position: relative;
        width: 150px;
        height: 150px;
        margin: 0 auto 20px;
    }

    .profile-img {
        width: 150px;
        height: 150px;
        object-fit: cover;
        border-radius: 50%;
        border: 5px solid white;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        transition: all 0.3s ease;
    }

    .profile-img:hover {
        transform: scale(1.05);
        box-shadow: 0 8px 25px rgba(0,0,0,0.25);
    }

    .profile-placeholder {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        background-color: var(--dark-color);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 3rem;
        border: 5px solid white;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        transition: all 0.3s ease;
    }

    .profile-placeholder:hover {
        transform: scale(1.05);
        box-shadow: 0 8px 25px rgba(0,0,0,0.25);
    }

    /* Profile Details */
    .profile-details {
        margin-top: 20px;
    }

    .detail-item {
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid #eee;
        transition: all 0.2s;
    }

    .detail-item:hover {
        background-color: rgba(0, 106, 78, 0.05);
        padding-left: 5px;
        border-radius: 5px;
    }

    .detail-item:last-child {
        border-bottom: none;
    }

    .detail-item i {
        color: var(--primary-color);
        width: 25px;
        text-align: center;
        margin-right: 10px;
    }

    /* Form Styles */
    .form-control {
        border-radius: 8px;
        padding: 12px 15px;
        border: 1px solid #ced4da;
        transition: all 0.3s;
    }

    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.25rem rgba(0, 106, 78, 0.25);
    }

    .form-label {
        font-weight: 500;
        margin-bottom: 8px;
        color: var(--dark-color);
    }

    /* Button Styles */
    .btn-primary {
        background-color: var(--primary-color);
        border: none;
        padding: 10px 20px;
        font-weight: 500;
        border-radius: 8px;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        background-color: var(--secondary-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    }

    .btn-warning {
        background-color: var(--accent-color);
        border: none;
        color: var(--dark-color);
        padding: 10px 20px;
        font-weight: 500;
        border-radius: 8px;
        transition: all 0.3s;
    }

    .btn-warning:hover {
        background-color: #E67E22;
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    }

    /* Alert Styles */
    .alert {
        border-radius: 8px;
        border: none;
        padding: 15px 20px;
        margin-bottom: 25px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    }

    .alert-success {
        background-color: #d4edda;
        color: #155724;
    }

    .alert-danger {
        background-color: #f8d7da;
        color: #721c24;
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .profile-hero {
            padding: 30px 0;
        }

        .profile-hero h1 {
            font-size: 1.8rem;
        }

        .profile-hero p {
            font-size: 1rem;
        }

        .card-body {
            padding: 20px;
        }
    }
</style>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="hero-title">প্রোফাইল ম্যানেজমেন্ট</h1>
                <p class="hero-text">আপনার ব্যক্তিগত তথ্য দেখুন এবং আপডেট করুন। আপনার প্রোফাইল সম্পর্কিত সকল তথ্য এখানে সংরক্ষিত আছে।</p>
                <a href="index.php" class="btn btn-lg" style="background-color: #00a65a; color: white; font-weight: 500; box-shadow: 0 4px 15px rgba(0,0,0,0.2); transition: all 0.3s ease;">হোমপেজে ফিরুন <i class="fas fa-arrow-right ms-2"></i></a>
            </div>
            <div class="col-lg-6 text-center">
                <?php if (isset($profileData['profile_photo']) && !empty($profileData['profile_photo'])): ?>
                    <img src="<?php echo $profileData['profile_photo']; ?>" alt="Profile Photo" class="img-fluid rounded hero-img" style="max-width: 300px; border: 5px solid white; box-shadow: 0 5px 15px rgba(0,0,0,0.2);">
                <?php else: ?>
                    <div class="profile-placeholder mx-auto hero-img" style="width: 200px; height: 200px;">
                        <i class="fas fa-user"></i>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Alert Messages -->
<section class="container mt-4">
    <?php if (!empty($successMessage)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i> <?php echo $successMessage; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (!empty($errorMessage)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i> <?php echo $errorMessage; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
</section>

<!-- Profile Information Section -->
<section class="container mt-4 mb-4">
    <div class="row">
        <!-- Left Column - Profile Information -->
        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body p-0">
                    <div class="text-center p-4 bg-light">
                        <?php if (isset($profileData['profile_photo']) && !empty($profileData['profile_photo'])): ?>
                            <img src="<?php echo $profileData['profile_photo']; ?>" alt="Profile Photo" class="img-fluid rounded-circle" style="width: 120px; height: 120px; object-fit: cover;" onerror="this.src='https://via.placeholder.com/120?text=Profile'">
                        <?php else: ?>
                            <div class="profile-placeholder mx-auto" style="width: 120px; height: 120px;">
                                <i class="fas fa-user"></i>
                            </div>
                        <?php endif; ?>

                        <h4 class="mt-3 fw-bold">
                            <?php
                            if ($userType === 'admin') {
                                echo 'Admin';
                            } else {
                                echo isset($profileData['first_name']) ? $profileData['first_name'] . ' ' . $profileData['last_name'] : $user['username'];
                            }
                            ?>
                        </h4>
                        <p class="badge" style="background-color: #00a65a; color: white; font-size: 0.9rem; padding: 8px 15px; border-radius: 20px;">
                            <?php
                            switch ($userType) {
                                case 'student':
                                    echo '<i class="fas fa-user-graduate me-1"></i> শিক্ষার্থী';
                                    break;
                                case 'teacher':
                                    echo '<i class="fas fa-chalkboard-teacher me-1"></i> শিক্ষক';
                                    break;
                                case 'admin':
                                    echo '<i class="fas fa-user-shield me-1"></i> অ্যাডমিন';
                                    break;
                                case 'staff':
                                    echo '<i class="fas fa-user-tie me-1"></i> কর্মচারী';
                                    break;
                            }
                            ?>
                        </p>
                    </div>

                    <div class="profile-details p-3">
                        <div class="detail-item">
                            <i class="fas fa-user"></i>
                            <strong>ইউজারনেম:</strong> <?php echo $user['username']; ?>
                        </div>

                        <?php if ($userType === 'student' && isset($profileData['student_id'])): ?>
                            <div class="detail-item">
                                <i class="fas fa-id-card"></i>
                                <strong>শিক্ষার্থী আইডি:</strong> <?php echo $profileData['student_id']; ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($userType === 'teacher' && isset($profileData['teacher_id'])): ?>
                            <div class="detail-item">
                                <i class="fas fa-id-card"></i>
                                <strong>শিক্ষক আইডি:</strong> <?php echo $profileData['teacher_id']; ?>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($profileData['email'])): ?>
                            <div class="detail-item">
                                <i class="fas fa-envelope"></i>
                                <strong>ইমেইল:</strong> <?php echo $profileData['email']; ?>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($profileData['phone'])): ?>
                            <div class="detail-item">
                                <i class="fas fa-phone"></i>
                                <strong>ফোন:</strong> <?php echo $profileData['phone']; ?>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($profileData['department_name'])): ?>
                            <div class="detail-item">
                                <i class="fas fa-building"></i>
                                <strong>বিভাগ:</strong> <?php echo $profileData['department_name']; ?>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($profileData['class_name'])): ?>
                            <div class="detail-item">
                                <i class="fas fa-graduation-cap"></i>
                                <strong>শ্রেণী:</strong> <?php echo $profileData['class_name']; ?>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($profileData['address'])): ?>
                            <div class="detail-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <strong>ঠিকানা:</strong> <?php echo $profileData['address']; ?>
                            </div>
                        <?php endif; ?>

                        <div class="detail-item">
                            <i class="fas fa-calendar-alt"></i>
                            <strong>অ্যাকাউন্ট তৈরির তারিখ:</strong> <?php echo date('d M Y', strtotime($user['created_at'])); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column - Profile Update and Password Change -->
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-white border-bottom-0 p-3">
                    <ul class="nav nav-tabs card-header-tabs" id="profileTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="update-tab" data-bs-toggle="tab" data-bs-target="#update-content" type="button" role="tab" aria-controls="update-content" aria-selected="true">
                                <i class="fas fa-edit me-2"></i>প্রোফাইল আপডেট
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="password-tab" data-bs-toggle="tab" data-bs-target="#password-content" type="button" role="tab" aria-controls="password-content" aria-selected="false">
                                <i class="fas fa-lock me-2"></i>পাসওয়ার্ড পরিবর্তন
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="profileTabsContent">
                        <!-- Profile Update Tab -->
                        <div class="tab-pane fade show active" id="update-content" role="tabpanel" aria-labelledby="update-tab">
                            <form method="POST" action="">
                                <div class="row mb-4">
                                    <div class="col-md-6 mb-3 mb-md-0">
                                        <label for="email" class="form-label">ইমেইল</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                            <input type="email" class="form-control" id="email" name="email" value="<?php echo isset($profileData['email']) ? $profileData['email'] : ''; ?>" placeholder="আপনার ইমেইল লিখুন">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="phone" class="form-label">ফোন</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                            <input type="text" class="form-control" id="phone" name="phone" value="<?php echo isset($profileData['phone']) ? $profileData['phone'] : ''; ?>" placeholder="আপনার ফোন নম্বর লিখুন">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label for="address" class="form-label">ঠিকানা</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                                        <textarea class="form-control" id="address" name="address" rows="3" placeholder="আপনার ঠিকানা লিখুন"><?php echo isset($profileData['address']) ? $profileData['address'] : ''; ?></textarea>
                                    </div>
                                </div>

                                <div class="text-end">
                                    <button type="submit" name="update_profile" class="btn" style="background-color: #00a65a; color: white;">
                                        <i class="fas fa-save me-2"></i> আপডেট করুন
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Password Change Tab -->
                        <div class="tab-pane fade" id="password-content" role="tabpanel" aria-labelledby="password-tab">
                            <form method="POST" action="">
                                <div class="mb-4">
                                    <label for="current_password" class="form-label">বর্তমান পাসওয়ার্ড</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-key"></i></span>
                                        <input type="password" class="form-control" id="current_password" name="current_password" required placeholder="আপনার বর্তমান পাসওয়ার্ড লিখুন">
                                        <span class="input-group-text toggle-password" style="cursor: pointer;" onclick="togglePassword('current_password')">
                                            <i class="fas fa-eye"></i>
                                        </span>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label for="new_password" class="form-label">নতুন পাসওয়ার্ড</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                        <input type="password" class="form-control" id="new_password" name="new_password" required placeholder="নতুন পাসওয়ার্ড লিখুন">
                                        <span class="input-group-text toggle-password" style="cursor: pointer;" onclick="togglePassword('new_password')">
                                            <i class="fas fa-eye"></i>
                                        </span>
                                    </div>
                                    <div class="form-text mt-2"><i class="fas fa-info-circle me-1"></i> পাসওয়ার্ড কমপক্ষে ৬ অক্ষরের হতে হবে।</div>
                                </div>

                                <div class="mb-4">
                                    <label for="confirm_password" class="form-label">কনফার্ম পাসওয়ার্ড</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-lock-open"></i></span>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required placeholder="নতুন পাসওয়ার্ড আবার লিখুন">
                                        <span class="input-group-text toggle-password" style="cursor: pointer;" onclick="togglePassword('confirm_password')">
                                            <i class="fas fa-eye"></i>
                                        </span>
                                    </div>
                                </div>

                                <div class="text-end">
                                    <button type="submit" name="change_password" class="btn" style="background-color: #F39C12; color: #2C3E50;">
                                        <i class="fas fa-key me-2"></i> পাসওয়ার্ড পরিবর্তন করুন
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Back to Top Button -->
<a href="#" class="btn back-to-top position-fixed bottom-0 end-0 m-4 rounded-circle" style="width: 45px; height: 45px; line-height: 45px; display: none; background-color: #F39C12; color: #2C3E50; z-index: 1000;">
    <i class="fas fa-arrow-up"></i>
</a>

<!-- Footer Section -->
<footer class="footer">
    <div class="container">
        <div class="row">
            <div class="col-md-4 mb-4 mb-md-0">
                <h5>আমাদের সম্পর্কে</h5>
                <p class="text-light opacity-75">নিশাত এডুকেশন সেন্টার একটি আধুনিক শিক্ষা প্রতিষ্ঠান যা উচ্চমানের শিক্ষা প্রদানের জন্য প্রতিশ্রুতিবদ্ধ।</p>
                <div class="social-icons mt-3">
                    <a href="#"><i class="fab fa-facebook-f"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                    <a href="#"><i class="fab fa-youtube"></i></a>
                </div>
            </div>
            <div class="col-md-4 mb-4 mb-md-0">
                <h5>দ্রুত লিঙ্ক</h5>
                <ul class="footer-links">
                    <li><a href="index.php"><i class="fas fa-angle-right me-2"></i> হোম</a></li>
                    <li><a href="about.php"><i class="fas fa-angle-right me-2"></i> আমাদের সম্পর্কে</a></li>
                    <li><a href="teachers.php"><i class="fas fa-angle-right me-2"></i> শিক্ষকবৃন্দ</a></li>
                    <li><a href="notices.php"><i class="fas fa-angle-right me-2"></i> নোটিশ</a></li>
                    <li><a href="contact.php"><i class="fas fa-angle-right me-2"></i> যোগাযোগ</a></li>
                </ul>
            </div>
            <div class="col-md-4">
                <h5>যোগাযোগ করুন</h5>
                <ul class="footer-links">
                    <li><i class="fas fa-map-marker-alt me-2"></i> চুয়াডাঙ্গা, বাংলাদেশ</li>
                    <li><i class="fas fa-phone me-2"></i> +880 1234-567890</li>
                    <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                    <li><i class="fas fa-clock me-2"></i> সকাল ৯টা - বিকাল ৫টা</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="copyright text-center">
        <div class="container">
            <p class="mb-0">&copy; <?php echo date('Y'); ?> নিশাত এডুকেশন সেন্টার - সর্বসত্ত্ব সংরক্ষিত</p>
        </div>
    </div>
</footer>

<!-- Custom JavaScript -->
<script>
    // Back to top button
    window.addEventListener('scroll', function() {
        var backToTopBtn = document.querySelector('.back-to-top');
        if (backToTopBtn) {
            if (window.pageYOffset > 300) {
                backToTopBtn.style.display = 'flex';
                backToTopBtn.style.justifyContent = 'center';
                backToTopBtn.style.alignItems = 'center';
            } else {
                backToTopBtn.style.display = 'none';
            }
        }
    });

    // Password field toggle
    function togglePassword(inputId) {
        const input = document.getElementById(inputId);
        const icon = input.parentElement.querySelector('.toggle-password i');

        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            input.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    // Add hover effect to detail items
    document.addEventListener('DOMContentLoaded', function() {
        const detailItems = document.querySelectorAll('.detail-item');
        detailItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(5px)';
            });

            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0)';
            });
        });
    });
</script>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
