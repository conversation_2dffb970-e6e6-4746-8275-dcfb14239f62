<?php
require_once '../includes/dbh.inc.php';

echo "<h2>Duplicate Classes Remo<PERSON></h2>";

// First, let's see what duplicates exist
echo "<h3>1. Checking for Duplicate Classes</h3>";

$duplicatesQuery = "
    SELECT class_name, COUNT(*) as count, GROUP_CONCAT(id) as ids
    FROM classes
    GROUP BY class_name
    HAVING COUNT(*) > 1
    ORDER BY class_name
";

$result = $conn->query($duplicatesQuery);

if ($result && $result->num_rows > 0) {
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>Class Name</th><th>Count</th><th>IDs</th><th>Action</th>";
    echo "</tr>";
    
    $duplicatesToRemove = [];
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['class_name']) . "</td>";
        echo "<td>" . $row['count'] . "</td>";
        echo "<td>" . $row['ids'] . "</td>";
        
        // Parse IDs and keep the first one, mark others for removal
        $ids = explode(',', $row['ids']);
        $keepId = $ids[0]; // Keep the first ID
        $removeIds = array_slice($ids, 1); // Remove the rest
        
        $duplicatesToRemove = array_merge($duplicatesToRemove, $removeIds);
        
        echo "<td>Keep ID: $keepId, Remove: " . implode(', ', $removeIds) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Now remove the duplicates
    if (!empty($duplicatesToRemove)) {
        echo "<h3>2. Removing Duplicate Classes</h3>";
        
        foreach ($duplicatesToRemove as $idToRemove) {
            // First check if this class is referenced in other tables
            $referencesFound = false;
            
            // Check students table
            $studentCheck = $conn->query("SELECT COUNT(*) as count FROM students WHERE class_id = $idToRemove");
            if ($studentCheck) {
                $studentCount = $studentCheck->fetch_assoc()['count'];
                if ($studentCount > 0) {
                    echo "<p style='color: orange;'>⚠️ Class ID $idToRemove has $studentCount students. Moving them to the primary class...</p>";
                    
                    // Find the primary class ID for this class name
                    $classNameQuery = $conn->query("SELECT class_name FROM classes WHERE id = $idToRemove");
                    if ($classNameQuery) {
                        $className = $classNameQuery->fetch_assoc()['class_name'];
                        $primaryClassQuery = $conn->query("SELECT id FROM classes WHERE class_name = '$className' ORDER BY id ASC LIMIT 1");
                        if ($primaryClassQuery) {
                            $primaryClassId = $primaryClassQuery->fetch_assoc()['id'];
                            
                            // Update students to use primary class ID
                            $updateStudents = $conn->query("UPDATE students SET class_id = $primaryClassId WHERE class_id = $idToRemove");
                            if ($updateStudents) {
                                echo "<p style='color: green;'>✅ Moved $studentCount students to primary class ID $primaryClassId</p>";
                            } else {
                                echo "<p style='color: red;'>❌ Failed to move students: " . $conn->error . "</p>";
                            }
                        }
                    }
                }
            }
            
            // Check subjects table
            $subjectCheck = $conn->query("SELECT COUNT(*) as count FROM subjects WHERE class_id = $idToRemove");
            if ($subjectCheck) {
                $subjectCount = $subjectCheck->fetch_assoc()['count'];
                if ($subjectCount > 0) {
                    echo "<p style='color: orange;'>⚠️ Class ID $idToRemove has $subjectCount subjects. Moving them to the primary class...</p>";
                    
                    // Find the primary class ID for this class name
                    $classNameQuery = $conn->query("SELECT class_name FROM classes WHERE id = $idToRemove");
                    if ($classNameQuery) {
                        $className = $classNameQuery->fetch_assoc()['class_name'];
                        $primaryClassQuery = $conn->query("SELECT id FROM classes WHERE class_name = '$className' ORDER BY id ASC LIMIT 1");
                        if ($primaryClassQuery) {
                            $primaryClassId = $primaryClassQuery->fetch_assoc()['id'];
                            
                            // Update subjects to use primary class ID
                            $updateSubjects = $conn->query("UPDATE subjects SET class_id = $primaryClassId WHERE class_id = $idToRemove");
                            if ($updateSubjects) {
                                echo "<p style='color: green;'>✅ Moved $subjectCount subjects to primary class ID $primaryClassId</p>";
                            } else {
                                echo "<p style='color: red;'>❌ Failed to move subjects: " . $conn->error . "</p>";
                            }
                        }
                    }
                }
            }
            
            // Check exams table
            $examCheck = $conn->query("SELECT COUNT(*) as count FROM exams WHERE class_id = $idToRemove");
            if ($examCheck) {
                $examCount = $examCheck->fetch_assoc()['count'];
                if ($examCount > 0) {
                    echo "<p style='color: orange;'>⚠️ Class ID $idToRemove has $examCount exams. Moving them to the primary class...</p>";
                    
                    // Find the primary class ID for this class name
                    $classNameQuery = $conn->query("SELECT class_name FROM classes WHERE id = $idToRemove");
                    if ($classNameQuery) {
                        $className = $classNameQuery->fetch_assoc()['class_name'];
                        $primaryClassQuery = $conn->query("SELECT id FROM classes WHERE class_name = '$className' ORDER BY id ASC LIMIT 1");
                        if ($primaryClassQuery) {
                            $primaryClassId = $primaryClassQuery->fetch_assoc()['id'];
                            
                            // Update exams to use primary class ID
                            $updateExams = $conn->query("UPDATE exams SET class_id = $primaryClassId WHERE class_id = $idToRemove");
                            if ($updateExams) {
                                echo "<p style='color: green;'>✅ Moved $examCount exams to primary class ID $primaryClassId</p>";
                            } else {
                                echo "<p style='color: red;'>❌ Failed to move exams: " . $conn->error . "</p>";
                            }
                        }
                    }
                }
            }
            
            // Now delete the duplicate class
            $deleteQuery = "DELETE FROM classes WHERE id = $idToRemove";
            if ($conn->query($deleteQuery)) {
                echo "<p style='color: green;'>✅ Deleted duplicate class ID: $idToRemove</p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to delete class ID $idToRemove: " . $conn->error . "</p>";
            }
        }
        
        echo "<h3>3. Final Check</h3>";
        
        // Check again for duplicates
        $finalCheck = $conn->query($duplicatesQuery);
        if ($finalCheck && $finalCheck->num_rows > 0) {
            echo "<p style='color: red;'>❌ Still have duplicates remaining:</p>";
            while ($row = $finalCheck->fetch_assoc()) {
                echo "<p>- " . htmlspecialchars($row['class_name']) . " (Count: " . $row['count'] . ")</p>";
            }
        } else {
            echo "<p style='color: green;'>✅ All duplicates have been successfully removed!</p>";
        }
        
    } else {
        echo "<p style='color: blue;'>ℹ️ No duplicates to remove.</p>";
    }
    
} else {
    echo "<p style='color: green;'>✅ No duplicate classes found!</p>";
}

echo "<h3>4. Current Classes List</h3>";

// Show current classes
$currentClassesQuery = "SELECT id, class_name FROM classes ORDER BY class_name";
$currentClasses = $conn->query($currentClassesQuery);

if ($currentClasses && $currentClasses->num_rows > 0) {
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>ID</th><th>Class Name</th>";
    echo "</tr>";
    
    while ($row = $currentClasses->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . htmlspecialchars($row['class_name']) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>No classes found.</p>";
}

echo "<br><a href='classes.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>← Back to Classes</a>";

$conn->close();
?>
