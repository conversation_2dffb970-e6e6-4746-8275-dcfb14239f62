/* Global Styles for ZFAW Project
   This file contains global styles that should be applied to all pages
   to ensure consistent design and typography across the site.
*/

/* Import Font CSS */
@import url('fonts.css');

/* Color Variables */
:root {
    --primary-color: #006A4E; /* Deep Green */
    --secondary-color: #00563B; /* Darker Green */
    --accent-color: #F39C12; /* Amber/Gold */
    --dark-color: #2C3E50; /* Dark Blue-Gray */
    --light-color: #F5F5F5; /* Off-White */
    --text-color: #333333; /* Dark Gray */
    --light-text: #FFFFFF; /* White */
    --highlight-color: #E74C3C; /* Red Accent */
    --soft-color: #E3F2FD; /* Soft Blue */
    --success-color: #27AE60; /* Green */
    --warning-color: #F39C12; /* Orange */
    --danger-color: #E74C3C; /* Red */
    --info-color: #3498DB; /* Blue */
}

/* Base Styles */
body {
    font-family: var(--primary-font);
    background-color: var(--light-color);
    color: var(--text-color);
    line-height: 1.6;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--primary-font);
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

h1 {
    font-size: 2.25rem;
}

h2 {
    font-size: 1.875rem;
}

h3 {
    font-size: 1.5rem;
}

h4 {
    font-size: 1.25rem;
}

h5 {
    font-size: 1.125rem;
}

h6 {
    font-size: 1rem;
}

p {
    margin-bottom: 1rem;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: var(--secondary-color);
    text-decoration: underline;
}

/* Header Styles */
.header-top {
    background-color: white;
    padding: 15px 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.school-logo {
    width: 80px;
    height: 80px;
    object-fit: contain;
}

.school-title {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.8rem;
}

.school-subtitle {
    color: var(--dark-color);
    font-weight: 500;
}

/* Navigation Styles */
.main-nav {
    background-color: var(--primary-color);
    padding: 0;
}

.main-nav .nav-link {
    color: var(--light-text);
    font-weight: 500;
    padding: 15px 20px;
    transition: all 0.3s;
    border-radius: 0;
}

.main-nav .nav-link:hover,
.main-nav .nav-link.active {
    background-color: var(--secondary-color);
}

/* Card Styles */
.card {
    border: none;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
    height: 100%;
    background-color: white;
    margin-bottom: 1.5rem;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.card-header {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
    padding: 15px 20px;
}

.card-img-top {
    height: 180px;
    object-fit: cover;
}

/* Button Styles */
.btn {
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: white;
}

.btn-secondary:hover {
    background-color: #E67E22;
    border-color: #E67E22;
    transform: translateY(-2px);
}

/* Form Styles */
.form-control {
    border-radius: 5px;
    border: 1px solid #ddd;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 106, 78, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Table Styles */
.table {
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.table thead th {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
    border-bottom: none;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 106, 78, 0.05);
}

/* Footer Styles */
.footer {
    background-color: var(--dark-color);
    color: white;
    padding: 40px 0 20px;
    margin-top: 50px;
}

.footer h5 {
    color: var(--light-color);
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--accent-color);
    display: inline-block;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s;
}

.footer-links a:hover {
    color: white;
    padding-left: 5px;
}

.social-icons a {
    display: inline-block;
    width: 36px;
    height: 36px;
    background-color: rgba(255,255,255,0.1);
    color: white;
    border-radius: 50%;
    text-align: center;
    line-height: 36px;
    margin-right: 10px;
    transition: all 0.3s;
}

.social-icons a:hover {
    background-color: var(--accent-color);
    transform: translateY(-3px);
}

.copyright {
    background-color: rgba(0,0,0,0.2);
    padding: 15px 0;
    margin-top: 30px;
}

/* Utility Classes */
.text-primary {
    color: var(--primary-color) !important;
}

.text-secondary {
    color: var(--secondary-color) !important;
}

.text-accent {
    color: var(--accent-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-secondary {
    background-color: var(--secondary-color) !important;
}

.bg-accent {
    background-color: var(--accent-color) !important;
}

.shadow-sm {
    box-shadow: 0 2px 5px rgba(0,0,0,0.1) !important;
}

.shadow-md {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

.shadow-lg {
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .school-title {
        font-size: 1.4rem;
    }

    .school-subtitle {
        font-size: 1rem;
    }

    h1 {
        font-size: 1.875rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    h3 {
        font-size: 1.25rem;
    }
}

@media (max-width: 576px) {
    .school-title {
        font-size: 1.2rem;
    }

    .school-subtitle {
        font-size: 0.875rem;
    }

    h1 {
        font-size: 1.5rem;
    }

    h2 {
        font-size: 1.25rem;
    }

    h3 {
        font-size: 1.125rem;
    }
}
