<?php
// Include database connection
require_once 'includes/dbh.inc.php';

// Check and add missing columns
$columns_to_check = [
    'status' => "ALTER TABLE exams ADD COLUMN status VARCHAR(20) DEFAULT 'active' AFTER exam_date",
    'exam_type' => "ALTER TABLE exams ADD COLUMN exam_type VARCHAR(50) DEFAULT NULL AFTER status",
    'start_time' => "ALTER TABLE exams ADD COLUMN start_time TIME DEFAULT NULL AFTER exam_type",
    'end_time' => "ALTER TABLE exams ADD COLUMN end_time TIME DEFAULT NULL AFTER start_time",
    'total_marks' => "ALTER TABLE exams ADD COLUMN total_marks INT DEFAULT 100 AFTER end_time",
    'pass_marks' => "ALTER TABLE exams ADD COLUMN pass_marks INT DEFAULT 0 AFTER total_marks",
    'description' => "ALTER TABLE exams ADD COLUMN description TEXT DEFAULT NULL AFTER pass_marks",
    'class_id' => "ALTER TABLE exams ADD COLUMN class_id INT DEFAULT 0 AFTER course_name"
];

foreach ($columns_to_check as $column => $alter_sql) {
    $sql = "SHOW COLUMNS FROM exams LIKE '$column'";
    $result = $conn->query($sql);

    if ($result->num_rows > 0) {
        echo "$column column already exists in exams table.<br>";
    } else {
        if ($conn->query($alter_sql) === TRUE) {
            echo "$column column added successfully to exams table.<br>";
        } else {
            echo "Error adding $column column: " . $conn->error . "<br>";
        }
    }
}

// Check if exams table exists
$check_table_sql = "SHOW TABLES LIKE 'exams'";
$check_table_result = $conn->query($check_table_sql);

if ($check_table_result->num_rows == 0) {
    // Create exams table if it doesn't exist
    $create_table_sql = "CREATE TABLE exams (
        id INT AUTO_INCREMENT PRIMARY KEY,
        exam_name VARCHAR(255) NOT NULL,
        course_name VARCHAR(255) NOT NULL,
        class_id INT DEFAULT 0,
        exam_date DATE NOT NULL,
        status VARCHAR(20) DEFAULT 'active',
        exam_type VARCHAR(50) DEFAULT NULL,
        start_time TIME DEFAULT NULL,
        end_time TIME DEFAULT NULL,
        total_marks INT DEFAULT 100,
        pass_marks INT DEFAULT 0,
        description TEXT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";

    if ($conn->query($create_table_sql) === TRUE) {
        echo "<div style='color: green; font-weight: bold;'>Exams table created successfully!</div><br>";
    } else {
        echo "<div style='color: red; font-weight: bold;'>Error creating exams table: " . $conn->error . "</div><br>";
    }
}

// Display table structure
echo "<h2>Exams Table Structure:</h2>";
$sql = "DESCRIBE exams";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background-color: #f2f2f2;'><th style='padding: 8px; text-align: left;'>Field</th><th style='padding: 8px; text-align: left;'>Type</th><th style='padding: 8px; text-align: left;'>Null</th><th style='padding: 8px; text-align: left;'>Key</th><th style='padding: 8px; text-align: left;'>Default</th><th style='padding: 8px; text-align: left;'>Extra</th></tr>";

    $row_count = 0;
    while($row = $result->fetch_assoc()) {
        $bg_color = ($row_count % 2 == 0) ? '#ffffff' : '#f9f9f9';
        echo "<tr style='background-color: $bg_color;'>";
        echo "<td style='padding: 8px;'>" . $row["Field"] . "</td>";
        echo "<td style='padding: 8px;'>" . $row["Type"] . "</td>";
        echo "<td style='padding: 8px;'>" . $row["Null"] . "</td>";
        echo "<td style='padding: 8px;'>" . $row["Key"] . "</td>";
        echo "<td style='padding: 8px;'>" . $row["Default"] . "</td>";
        echo "<td style='padding: 8px;'>" . $row["Extra"] . "</td>";
        echo "</tr>";
        $row_count++;
    }

    echo "</table>";
} else {
    echo "<div style='color: red;'>No results found.</div>";
}

// Display sample data
echo "<h2>Sample Exams Data:</h2>";
$sql = "SELECT * FROM exams LIMIT 5";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background-color: #f2f2f2;'><th style='padding: 8px; text-align: left;'>ID</th><th style='padding: 8px; text-align: left;'>Exam Name</th><th style='padding: 8px; text-align: left;'>Course</th><th style='padding: 8px; text-align: left;'>Class ID</th><th style='padding: 8px; text-align: left;'>Date</th><th style='padding: 8px; text-align: left;'>Status</th><th style='padding: 8px; text-align: left;'>Type</th></tr>";

    $row_count = 0;
    while($row = $result->fetch_assoc()) {
        $bg_color = ($row_count % 2 == 0) ? '#ffffff' : '#f9f9f9';
        echo "<tr style='background-color: $bg_color;'>";
        echo "<td style='padding: 8px;'>" . $row["id"] . "</td>";
        echo "<td style='padding: 8px;'>" . $row["exam_name"] . "</td>";
        echo "<td style='padding: 8px;'>" . $row["course_name"] . "</td>";
        echo "<td style='padding: 8px;'>" . $row["class_id"] . "</td>";
        echo "<td style='padding: 8px;'>" . $row["exam_date"] . "</td>";
        echo "<td style='padding: 8px;'>" . $row["status"] . "</td>";
        echo "<td style='padding: 8px;'>" . $row["exam_type"] . "</td>";
        echo "</tr>";
        $row_count++;
    }

    echo "</table>";
} else {
    echo "<div style='color: orange;'>No exam data found. Add some exams to see them here.</div>";
}

// Close connection
$conn->close();
?>
