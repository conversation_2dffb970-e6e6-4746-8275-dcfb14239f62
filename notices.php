<?php
session_start();
require_once "includes/dbh.inc.php";

// Create notices table if it doesn't exist
$result = $conn->query("SHOW TABLES LIKE 'notices'");
if ($result->num_rows == 0) {
    // Create notices table
    $sql = "CREATE TABLE notices (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        date DATE NOT NULL,
        added_by VARCHAR(50),
        attachment_path VARCHAR(255),
        attachment_type VARCHAR(10),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "<div class='alert alert-success'>নোটিশ টেবিল সফলভাবে তৈরি করা হয়েছে।</div>";
        
        // Add sample notice
        $sql = "INSERT INTO notices (title, content, date, added_by) VALUES 
        ('স্বাগতম', '<PERSON><PERSON><PERSON><PERSON> ম্যানেজমেন্ট সিস্টেমে স্বাগতম। এটি একটি নমুনা নোটিশ।', CURDATE(), 'admin')";
        $conn->query($sql);
    }
}

// Get all notices
$sql = "SELECT * FROM notices ORDER BY date DESC";
$result = $conn->query($sql);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>নোটিশ - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-university me-2"></i>কলেজ ম্যানেজমেন্ট সিস্টেম
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home me-1"></i> হোম
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book me-1"></i> বিষয়সমূহ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="notices.php">
                            <i class="fas fa-bullhorn me-1"></i> নোটিশ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">
                            <i class="fas fa-info-circle me-1"></i> আমাদের সম্পর্কে
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">
                            <i class="fas fa-envelope me-1"></i> যোগাযোগ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-outline-light ms-2" href="index.php#login-section">
                            <i class="fas fa-sign-in-alt me-1"></i> লগইন
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Notice Section -->
    <div class="container mt-5 mb-5">
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="display-5 text-center mb-4">সকল নোটিশ</h1>
                <p class="lead text-center text-muted">আমাদের কলেজের সর্বশেষ নোটিশ ও ঘোষণা সমূহ</p>
            </div>
        </div>
        
        <div class="row">
            <?php if ($result && $result->num_rows > 0): ?>
                <?php while ($row = $result->fetch_assoc()): ?>
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 shadow-sm border-primary">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0"><?php echo htmlspecialchars($row['title']); ?></h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-3">
                                    <span class="badge bg-secondary">
                                        <i class="far fa-calendar-alt me-1"></i> <?php echo $row['date']; ?>
                                    </span>
                                    <?php if (!empty($row['added_by'])): ?>
                                    <span class="badge bg-info">
                                        <i class="fas fa-user me-1"></i> <?php echo htmlspecialchars($row['added_by']); ?>
                                    </span>
                                    <?php endif; ?>
                                </div>
                                <p class="card-text"><?php echo nl2br(htmlspecialchars($row['content'])); ?></p>
                                
                                <?php if (!empty($row['attachment_path'])): ?>
                                <div class="mt-3">
                                    <a href="<?php echo $row['attachment_path']; ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                                        <i class="fas fa-paperclip me-1"></i> 
                                        সংযুক্তি দেখুন 
                                        <?php if (!empty($row['attachment_type'])): ?>
                                            <span class="badge bg-light text-dark ms-1"><?php echo strtoupper($row['attachment_type']); ?></span>
                                        <?php endif; ?>
                                    </a>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endwhile; ?>
            <?php else: ?>
                <div class="col-12">
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle me-2"></i> কোন নোটিশ পাওয়া যায়নি
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>কলেজ ম্যানেজমেন্ট সিস্টেম</h5>
                    <p>একটি সম্পূর্ণ কলেজ ব্যবস্থাপনা পদ্ধতি, যা সকল শিক্ষার্থী, শিক্ষক ও কর্মীদের জন্য বিকশিত করা হয়েছে।</p>
                </div>
                <div class="col-md-4">
                    <h5>দ্রুত লিংক</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-white">হোম</a></li>
                        <li><a href="subjects.php" class="text-white">বিষয়সমূহ</a></li>
                        <li><a href="notices.php" class="text-white">নোটিশ</a></li>
                        <li><a href="about.php" class="text-white">আমাদের সম্পর্কে</a></li>
                        <li><a href="contact.php" class="text-white">যোগাযোগ</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>যোগাযোগ করুন</h5>
                    <address>
                        <i class="fas fa-map-marker-alt me-2"></i> ঠিকানা, শহর, দেশ<br>
                        <i class="fas fa-phone me-2"></i> +৮৮০১৭XXXXXXXX<br>
                        <i class="fas fa-envelope me-2"></i> <EMAIL>
                    </address>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12 text-center">
                    <p class="mb-0">কপিরাইট &copy; ২০২৩ কলেজ ম্যানেজমেন্ট সিস্টেম। সর্বস্বত্ব সংরক্ষিত।</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 