/* Modern Subject Selection Page Styling */

/* Root Variables */
:root {
    --primary-color: #4361ee;
    --primary-light: #4895ef;
    --primary-dark: #3f37c9;
    --secondary-color: #4cc9f0;
    --accent-color: #f72585;
    --success-color: #4ade80;
    --warning-color: #fbbf24;
    --danger-color: #f43f5e;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-hover: 0 20px 30px -10px rgba(0, 0, 0, 0.15);
    --border-radius-sm: 0.25rem;
    --border-radius: 0.5rem;
    --border-radius-md: 0.75rem;
    --border-radius-lg: 1rem;
    --transition-fast: 0.15s ease;
    --transition: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Modern Typography */
body, html, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, select, textarea, label, li, table, th, td {
    font-family: 'Hind Siliguri', 'Noto Sans Bengali', 'Baloo Da 2', sans-serif !important;
    letter-spacing: -0.01em;
}

/* Improved Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: var(--gray-800);
    line-height: 1.3;
}

h2 {
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
}

p {
    color: var(--gray-600);
    line-height: 1.6;
}

.text-muted {
    color: var(--gray-500) !important;
}

/* Modern Sidebar */
.sidebar {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    box-shadow: var(--shadow);
}

.sidebar .nav-link {
    border-radius: var(--border-radius);
    margin: 0.25rem 0.75rem;
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-sm);
}

.sidebar .nav-link i {
    width: 24px;
    text-align: center;
    margin-right: 8px;
}

/* Modern Cards */
.card {
    border: none;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid var(--gray-200);
    padding: 1rem 1.25rem;
}

.card-header.bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%) !important;
}

.card-body {
    padding: 1.5rem;
}

/* Student Info Card */
.student-info {
    background: linear-gradient(to right, #ffffff, #f8f9fa);
    border-radius: var(--border-radius-md);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow);
    border-left: 5px solid var(--primary-color);
}

.student-info h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.student-info p {
    margin-bottom: 0.5rem;
}

.student-info strong {
    color: var(--gray-700);
}

/* Selection Summary */
.selection-summary {
    background: linear-gradient(to right, #ffffff, #f0f7ff);
    border-radius: var(--border-radius-md);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow);
    border-left: 5px solid var(--secondary-color);
}

.selection-summary h4 {
    color: var(--primary-dark);
    margin-bottom: 1rem;
}

.counter-badge {
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    box-shadow: var(--shadow-sm);
}

/* Subject Cards */
.subject-card {
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    overflow: hidden;
    border: 2px solid transparent;
}

.subject-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.subject-card .card-body {
    padding: 1rem;
}

.subject-card .form-check {
    padding-left: 2rem;
}

.subject-card .form-check-input {
    width: 1.25rem;
    height: 1.25rem;
    margin-top: 0.25rem;
    margin-left: -2rem;
    cursor: pointer;
}

.subject-card .form-check-label {
    cursor: pointer;
    display: block;
    width: 100%;
}

/* Required Subjects */
.subject-card.required-subject {
    border-left: 4px solid var(--primary-color);
}

.subject-card.required-subject.selected {
    background-color: rgba(67, 97, 238, 0.1);
    border-color: var(--primary-color);
}

/* Optional Subjects */
.subject-card.optional-subject {
    border-left: 4px solid var(--secondary-color);
}

.subject-card.optional-subject.selected {
    background-color: rgba(76, 201, 240, 0.1);
    border-color: var(--secondary-color);
}

/* Fourth Subjects */
.subject-card.fourth-subject {
    border-left: 4px solid var(--warning-color);
}

.subject-card.fourth-subject.selected {
    background-color: rgba(251, 191, 36, 0.1);
    border-color: var(--warning-color);
}

/* Subject Card Animation */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(67, 97, 238, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
    }
}

.subject-card.selected {
    animation: pulse 2s infinite;
}

/* Subject Section Headers */
.subject-section-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--gray-200);
}

.subject-section-header h4 {
    margin-bottom: 0;
    margin-right: 1rem;
}

.subject-section-header .badge {
    font-size: 0.8rem;
    padding: 0.35rem 0.65rem;
}

/* Search Form */
.search-container {
    background: white;
    border-radius: var(--border-radius-md);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow);
}

.search-container .form-control,
.search-container .form-select {
    border-radius: var(--border-radius);
    padding: 0.65rem 1rem;
    border: 1px solid var(--gray-300);
    transition: var(--transition-fast);
}

.search-container .form-control:focus,
.search-container .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

.search-container .input-group-text {
    background-color: white;
    border-right: none;
    border-color: var(--gray-300);
}

.search-container .form-control {
    border-left: none;
}

/* Advanced Search Panel */
.advanced-search-panel {
    background-color: var(--gray-100);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-top: 1rem;
    box-shadow: var(--shadow-sm);
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: var(--transition);
    border: none;
    box-shadow: var(--shadow-sm);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
}

.btn-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
    color: #fff;
}

.btn-info {
    background: linear-gradient(135deg, #0ea5e9 0%, #38bdf8 100%);
    color: #fff;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}

/* Save Button */
.save-button-container {
    position: sticky;
    bottom: 20px;
    z-index: 100;
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.9);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(5px);
}

.save-button-container .btn {
    min-width: 250px;
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.25rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-sm);
}

.alert-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: #065f46;
    border-left: 4px solid #10b981;
}

.alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: #991b1b;
    border-left: 4px solid #ef4444;
}

.alert-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: #92400e;
    border-left: 4px solid #f59e0b;
}

/* Table Styles */
.table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
}

.table th {
    background-color: var(--gray-100);
    color: var(--gray-700);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    padding: 1rem;
    border-top: none;
    border-bottom: 2px solid var(--gray-200);
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--gray-200);
    color: var(--gray-700);
}

.table tbody tr {
    transition: var(--transition-fast);
}

.table tbody tr:hover {
    background-color: var(--gray-100);
    transform: scale(1.01);
}

.table-responsive {
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .subject-card {
        margin-bottom: 1rem;
    }
    
    .counter-badge {
        font-size: 0.8rem;
        padding: 0.35rem 0.5rem;
    }
    
    .selection-summary .row {
        flex-direction: column;
    }
    
    .selection-summary .col-auto {
        margin-top: 1rem;
    }
}

/* Loading Animation */
@keyframes shimmer {
    0% {
        background-position: -1000px 0;
    }
    100% {
        background-position: 1000px 0;
    }
}

.loading {
    animation: shimmer 2s infinite linear;
    background: linear-gradient(to right, var(--gray-100) 4%, var(--gray-200) 25%, var(--gray-100) 36%);
    background-size: 1000px 100%;
}
