<?php
require_once '../includes/dbh.inc.php';

echo "<h2>Creating Student Subjects Table</h2>";

// First, check if the database exists
echo "<p>Checking database connection...</p>";
if ($conn->connect_error) {
    die("<p>Connection failed: " . $conn->connect_error . "</p>");
} else {
    echo "<p>Database connection successful!</p>";
}

// Check if required tables exist
$requiredTables = ['students', 'subjects', 'sessions'];
$missingTables = [];

foreach ($requiredTables as $table) {
    $tableCheck = $conn->query("SHOW TABLES LIKE '$table'");
    if ($tableCheck->num_rows == 0) {
        $missingTables[] = $table;
    }
}

if (!empty($missingTables)) {
    echo "<div style='color: red; margin: 20px 0;'>";
    echo "<strong>Warning:</strong> Some required tables are missing: " . implode(', ', $missingTables) . ". <br>";
    echo "Please create these tables first before creating the student_subjects table.";
    echo "</div>";
    
    if (in_array('students', $missingTables) || in_array('subjects', $missingTables)) {
        echo "<p><a href='../create_essential_tables.php' style='display: inline-block; padding: 10px 15px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 4px;'>Create Essential Tables</a></p>";
    }
    
    if (in_array('subjects', $missingTables)) {
        echo "<p><a href='../create_subjects_table.php' style='display: inline-block; padding: 10px 15px; background-color: #2196F3; color: white; text-decoration: none; border-radius: 4px;'>Create Subjects Table</a></p>";
    }
    
    echo "<hr>";
}

// Create student_subjects table without foreign key constraints
$studentSubjectsTableQuery = "CREATE TABLE IF NOT EXISTS student_subjects (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    subject_id INT(11) NOT NULL,
    category VARCHAR(50) NOT NULL,
    selection_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    session_id INT(11) NULL,
    UNIQUE KEY(student_id, subject_id)
)";

if ($conn->query($studentSubjectsTableQuery)) {
    echo "<p style='color: green;'>Student subjects table created successfully!</p>";
    
    // Insert sample data if tables exist
    if (empty($missingTables)) {
        // Check if there's any data in the students table
        $checkStudents = $conn->query("SELECT id FROM students LIMIT 1");
        $checkSubjects = $conn->query("SELECT id FROM subjects LIMIT 1");
        
        if ($checkStudents && $checkStudents->num_rows > 0 && 
            $checkSubjects && $checkSubjects->num_rows > 0) {
            
            $studentId = $checkStudents->fetch_assoc()['id'];
            $subjectId = $checkSubjects->fetch_assoc()['id'];
            
            // Check if sample data already exists
            $checkExisting = $conn->query("SELECT id FROM student_subjects WHERE student_id = $studentId AND subject_id = $subjectId");
            
            if ($checkExisting && $checkExisting->num_rows == 0) {
                $insertSample = $conn->query("INSERT INTO student_subjects (student_id, subject_id, category) 
                                           VALUES ($studentId, $subjectId, 'required')");
                
                if ($insertSample) {
                    echo "<p style='color: green;'>Sample student-subject mapping created successfully!</p>";
                } else {
                    echo "<p style='color: orange;'>Note: Could not create sample data: " . $conn->error . "</p>";
                }
            } else {
                echo "<p>Sample data already exists.</p>";
            }
        } else {
            echo "<p style='color: orange;'>Note: Could not create sample data because students or subjects data is missing.</p>";
        }
    }
} else {
    echo "<p style='color: red;'>Error creating student_subjects table: " . $conn->error . "</p>";
}

echo "<p><strong>Note:</strong> Foreign key constraints have been omitted for simplicity. The table will still work for basic functionality.</p>";

// Navigation links
echo "<div style='margin-top: 20px;'>";
echo "<a href='../index.php' style='display: inline-block; padding: 10px 15px; background-color: #607D8B; color: white; text-decoration: none; border-radius: 4px; margin-right: 10px;'>Return to Homepage</a>";
echo "<a href='../admin/dashboard.php' style='display: inline-block; padding: 10px 15px; background-color: #3F51B5; color: white; text-decoration: none; border-radius: 4px; margin-right: 10px;'>Go to Admin Dashboard</a>";
echo "<a href='../admin/subject_categories.php' style='display: inline-block; padding: 10px 15px; background-color: #FF9800; color: white; text-decoration: none; border-radius: 4px;'>Go to Subject Categories</a>";
echo "</div>";

$conn->close();
?>
