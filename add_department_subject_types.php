<?php
// Include database connection
require_once "includes/dbh.inc.php";

// Check if department_subject_types table exists
$tableExists = $conn->query("SHOW TABLES LIKE 'department_subject_types'")->num_rows > 0;

// Create the table if it doesn't exist
if (!$tableExists) {
    $createTableQuery = "CREATE TABLE department_subject_types (
        id INT AUTO_INCREMENT PRIMARY KEY,
        department_id INT NOT NULL,
        subject_id INT NOT NULL,
        subject_type ENUM('required', 'optional', 'fourth') NOT NULL DEFAULT 'optional',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_dept_subject (department_id, subject_id),
        FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
    )";
    
    if ($conn->query($createTableQuery)) {
        echo "<p style='color:green'>Successfully created department_subject_types table.</p>";
    } else {
        echo "<p style='color:red'>Error creating table: " . $conn->error . "</p>";
        exit;
    }
}

// Get all departments
$departmentsQuery = "SELECT * FROM departments";
$departments = $conn->query($departmentsQuery);

if (!$departments || $departments->num_rows == 0) {
    echo "<p style='color:red'>No departments found. Please add departments first.</p>";
    exit;
}

// Get all subjects
$subjectsQuery = "SELECT * FROM subjects WHERE is_active = 1";
$subjects = $conn->query($subjectsQuery);

if (!$subjects || $subjects->num_rows == 0) {
    echo "<p style='color:red'>No subjects found. Please add subjects first.</p>";
    exit;
}

// Get existing subject-department mappings
$mappingsQuery = "SELECT * FROM subject_departments";
$mappings = $conn->query($mappingsQuery);

if (!$mappings) {
    echo "<p style='color:red'>Error fetching subject-department mappings: " . $conn->error . "</p>";
    exit;
}

// Begin transaction
$conn->begin_transaction();

try {
    // Process each mapping
    $insertCount = 0;
    $updateCount = 0;
    
    if ($mappings->num_rows > 0) {
        while ($mapping = $mappings->fetch_assoc()) {
            $departmentId = $mapping['department_id'];
            $subjectId = $mapping['subject_id'];
            
            // Get subject category
            $subjectQuery = "SELECT category FROM subjects WHERE id = ?";
            $stmt = $conn->prepare($subjectQuery);
            $stmt->bind_param("i", $subjectId);
            $stmt->execute();
            $subjectResult = $stmt->get_result();
            
            if ($subjectResult && $subjectResult->num_rows > 0) {
                $subject = $subjectResult->fetch_assoc();
                $category = $subject['category'];
                
                // Determine subject type
                $subjectType = 'optional'; // Default
                
                if (strpos($category, 'required') !== false) {
                    $subjectType = 'required';
                } elseif (strpos($category, 'fourth') !== false) {
                    $subjectType = 'fourth';
                }
                
                // Check if mapping already exists in department_subject_types
                $checkQuery = "SELECT id FROM department_subject_types WHERE department_id = ? AND subject_id = ?";
                $stmt = $conn->prepare($checkQuery);
                $stmt->bind_param("ii", $departmentId, $subjectId);
                $stmt->execute();
                $checkResult = $stmt->get_result();
                
                if ($checkResult && $checkResult->num_rows > 0) {
                    // Update existing mapping
                    $updateQuery = "UPDATE department_subject_types SET subject_type = ? WHERE department_id = ? AND subject_id = ?";
                    $stmt = $conn->prepare($updateQuery);
                    $stmt->bind_param("sii", $subjectType, $departmentId, $subjectId);
                    $stmt->execute();
                    $updateCount++;
                } else {
                    // Insert new mapping
                    $insertQuery = "INSERT INTO department_subject_types (department_id, subject_id, subject_type) VALUES (?, ?, ?)";
                    $stmt = $conn->prepare($insertQuery);
                    $stmt->bind_param("iis", $departmentId, $subjectId, $subjectType);
                    $stmt->execute();
                    $insertCount++;
                }
            }
        }
    }
    
    // Commit transaction
    $conn->commit();
    
    echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
    echo "<h3>Success!</h3>";
    echo "<p>Added $insertCount new subject type mappings and updated $updateCount existing mappings.</p>";
    echo "<p>You can now go to <a href='admin/student_subject_selection.php'>Student Subject Selection</a> page.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    // Rollback on error
    $conn->rollback();
    
    echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
    echo "<h3>Error!</h3>";
    echo "<p>Failed to process subject type mappings: " . $e->getMessage() . "</p>";
    echo "</div>";
}

// Close connection
$conn->close();
?>
