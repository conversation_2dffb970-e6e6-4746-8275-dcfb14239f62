<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if students table exists and has the correct structure
$check_students_table = "SHOW TABLES LIKE 'students'";
$students_table_exists = $conn->query($check_students_table)->num_rows > 0;

if ($students_table_exists) {
    // Check students table primary key
    $check_students_pk = "SHOW KEYS FROM students WHERE Key_name = 'PRIMARY'";
    $students_pk_result = $conn->query($check_students_pk);
    $students_pk_column = '';

    if ($students_pk_result && $students_pk_result->num_rows > 0) {
        $pk_info = $students_pk_result->fetch_assoc();
        $students_pk_column = $pk_info['Column_name'];
    }

    // Create shortcodes table if not exists
    if (!empty($students_pk_column)) {
        $create_table_sql = "CREATE TABLE IF NOT EXISTS student_shortcodes (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            student_id INT(11) NOT NULL,
            shortcode VARCHAR(20) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY (shortcode)
        )";
        $conn->query($create_table_sql);
    } else {
        $error_message = "শিক্ষার্থী টেবিলের প্রাইমারি কী খুঁজে পাওয়া যায়নি।";
    }
} else {
    $error_message = "শিক্ষার্থী টেবিল খুঁজে পাওয়া যায়নি।";
}

// Handle generate shortcodes
$success_message = "";
$error_message = "";

if (isset($_POST['generate_shortcodes'])) {
    $prefix = $_POST['prefix'] ?? 'S';
    $start_number = $_POST['start_number'] ?? 1;
    $class_id = $_POST['class_id'] ?? '';

    // Check if students table exists
    $check_students_table = "SHOW TABLES LIKE 'students'";
    $students_table_exists = $conn->query($check_students_table)->num_rows > 0;

    if (!$students_table_exists) {
        $error_message = "শিক্ষার্থী টেবিল খুঁজে পাওয়া যায়নি।";
        return;
    }

    // Check if student_shortcodes table exists
    $check_shortcodes_table = "SHOW TABLES LIKE 'student_shortcodes'";
    $shortcodes_table_exists = $conn->query($check_shortcodes_table)->num_rows > 0;

    if (!$shortcodes_table_exists) {
        // Create the table if it doesn't exist
        $create_table_sql = "CREATE TABLE IF NOT EXISTS student_shortcodes (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            student_id INT(11) NOT NULL,
            shortcode VARCHAR(20) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY (shortcode)
        )";
        $conn->query($create_table_sql);
    }

    // Check students table structure
    $check_students_columns = "SHOW COLUMNS FROM students";
    $students_columns_result = $conn->query($check_students_columns);
    $has_first_name = false;
    $has_last_name = false;
    $has_student_id = false;
    $has_class_id = false;

    if ($students_columns_result) {
        while ($column = $students_columns_result->fetch_assoc()) {
            if ($column['Field'] == 'first_name') $has_first_name = true;
            if ($column['Field'] == 'last_name') $has_last_name = true;
            if ($column['Field'] == 'student_id') $has_student_id = true;
            if ($column['Field'] == 'class_id') $has_class_id = true;
        }
    }

    // Build query based on available columns
    $select_fields = "s.id";
    if ($has_first_name) $select_fields .= ", s.first_name";
    if ($has_last_name) $select_fields .= ", s.last_name";
    if ($has_student_id) $select_fields .= ", s.student_id";

    $where_clause = "";
    if (!empty($class_id) && $has_class_id) {
        $where_clause = " AND s.class_id = " . intval($class_id);
    }

    // Check if classes table exists
    $check_classes_table = "SHOW TABLES LIKE 'classes'";
    $classes_table_exists = $conn->query($check_classes_table)->num_rows > 0;

    $join_clause = "";
    if ($classes_table_exists && $has_class_id) {
        $join_clause = "LEFT JOIN classes c ON s.class_id = c.id";
        $select_fields .= ", c.class_name";
    }

    $students_query = "SELECT $select_fields
                      FROM students s
                      $join_clause
                      LEFT JOIN student_shortcodes sc ON s.id = sc.student_id
                      WHERE sc.id IS NULL" . $where_clause;

    $students_result = $conn->query($students_query);

    if ($students_result && $students_result->num_rows > 0) {
        $counter = $start_number;
        $conn->begin_transaction();

        try {
            while ($student = $students_result->fetch_assoc()) {
                $shortcode = $prefix . str_pad($counter, 3, '0', STR_PAD_LEFT);

                $insert_query = "INSERT INTO student_shortcodes (student_id, shortcode) VALUES (?, ?)";
                $stmt = $conn->prepare($insert_query);
                $stmt->bind_param("is", $student['id'], $shortcode);
                $stmt->execute();

                $counter++;
            }

            $conn->commit();
            $success_message = "সর্টকোড সফলভাবে তৈরি করা হয়েছে!";
        } catch (Exception $e) {
            $conn->rollback();
            $error_message = "সর্টকোড তৈরি করতে সমস্যা হয়েছে: " . $e->getMessage();
        }
    } else {
        $error_message = "কোন শিক্ষার্থী পাওয়া যায়নি বা সবার সর্টকোড আছে।";
    }
}

// Handle regenerate shortcodes
if (isset($_POST['regenerate_shortcodes'])) {
    $confirm = $_POST['confirm'] ?? '';

    if ($confirm === 'yes') {
        $truncate_query = "TRUNCATE TABLE student_shortcodes";

        if ($conn->query($truncate_query)) {
            $success_message = "সমস্ত সর্টকোড মুছে ফেলা হয়েছে। এখন নতুন করে তৈরি করুন।";
        } else {
            $error_message = "সর্টকোড মুছতে সমস্যা হয়েছে।";
        }
    } else {
        $error_message = "নিশ্চিত করার জন্য 'হ্যাঁ' চেকবক্স সিলেক্ট করুন।";
    }
}

// Get classes for dropdown
$classes_result = null;
$check_classes_table = "SHOW TABLES LIKE 'classes'";
$classes_table_exists = $conn->query($check_classes_table)->num_rows > 0;

if ($classes_table_exists) {
    $classes_query = "SELECT id, class_name FROM classes ORDER BY class_name";
    $classes_result = $conn->query($classes_query);
}

// Check if student_shortcodes table exists
$check_shortcodes_table = "SHOW TABLES LIKE 'student_shortcodes'";
$shortcodes_table_exists = $conn->query($check_shortcodes_table)->num_rows > 0;

// Initialize variables
$shortcodes_result = null;
$total_shortcodes = 0;
$missing_shortcodes = 0;

if ($shortcodes_table_exists) {
    // Check students table structure
    $check_students_columns = "SHOW COLUMNS FROM students";
    $students_columns_result = $conn->query($check_students_columns);
    $has_first_name = false;
    $has_last_name = false;
    $has_student_id = false;
    $has_class_id = false;

    if ($students_columns_result) {
        while ($column = $students_columns_result->fetch_assoc()) {
            if ($column['Field'] == 'first_name') $has_first_name = true;
            if ($column['Field'] == 'last_name') $has_last_name = true;
            if ($column['Field'] == 'student_id') $has_student_id = true;
            if ($column['Field'] == 'class_id') $has_class_id = true;
        }
    }

    // Build select fields based on available columns
    $select_fields = "sc.id, sc.shortcode, sc.created_at";
    if ($has_first_name) $select_fields .= ", s.first_name";
    if ($has_last_name) $select_fields .= ", s.last_name";
    if ($has_student_id) $select_fields .= ", s.student_id";

    // Check if classes table exists
    $check_classes_table = "SHOW TABLES LIKE 'classes'";
    $classes_table_exists = $conn->query($check_classes_table)->num_rows > 0;

    $join_clause = "";
    if ($classes_table_exists && $has_class_id) {
        $join_clause = "LEFT JOIN classes c ON s.class_id = c.id";
        $select_fields .= ", c.class_name";
    }

    // Get existing shortcodes
    $shortcodes_query = "SELECT $select_fields
                        FROM student_shortcodes sc
                        JOIN students s ON sc.student_id = s.id
                        $join_clause
                        ORDER BY sc.shortcode";
    $shortcodes_result = $conn->query($shortcodes_query);

    // Count total shortcodes
    $count_query = "SELECT COUNT(*) as total FROM student_shortcodes";
    $count_result = $conn->query($count_query);
    $total_shortcodes = $count_result->fetch_assoc()['total'] ?? 0;

    // Count students without shortcodes
    $missing_query = "SELECT COUNT(*) as missing FROM students s
                     LEFT JOIN student_shortcodes sc ON s.id = sc.student_id
                     WHERE sc.id IS NULL";
    $missing_result = $conn->query($missing_query);
    $missing_shortcodes = $missing_result->fetch_assoc()['missing'] ?? 0;
}

// Page title
$page_title = "শিক্ষার্থী সর্টকোড";
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title><?php echo $page_title; ?> - ZFAW</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/admin.css">

    <style>
        .shortcode-badge {
            font-size: 1rem;
            font-weight: bold;
            padding: 0.35em 0.65em;
            border-radius: 0.5rem;
        }
        .whatsapp-btn {
            background-color: #25D366;
            color: white;
        }
        .whatsapp-btn:hover {
            background-color: #128C7E;
            color: white;
        }
        .copy-btn {
            cursor: pointer;
        }
        .copy-btn:hover {
            color: #0d6efd;
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <!-- Include Admin Header -->
    <?php include 'includes/admin_header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <!-- Include Admin Sidebar -->
            <?php include 'includes/admin_sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><?php echo $page_title; ?></h1>
                </div>

                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i> <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Dashboard Cards -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card bg-primary text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-uppercase mb-1">মোট সর্টকোড</h6>
                                        <h2 class="mb-0"><?php echo number_format($total_shortcodes); ?></h2>
                                    </div>
                                    <i class="fas fa-hashtag fa-3x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-warning text-dark h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-uppercase mb-1">সর্টকোড বিহীন শিক্ষার্থী</h6>
                                        <h2 class="mb-0"><?php echo number_format($missing_shortcodes); ?></h2>
                                    </div>
                                    <i class="fas fa-user-slash fa-3x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="fas fa-cog me-2"></i> সর্টকোড তৈরি করুন</h5>
                            </div>
                            <div class="card-body">
                                <form method="post" action="">
                                    <div class="mb-3">
                                        <label for="prefix" class="form-label">প্রিফিক্স</label>
                                        <input type="text" class="form-control" id="prefix" name="prefix" value="S" maxlength="5">
                                        <div class="form-text">সর্টকোডের শুরুতে যে অক্ষর থাকবে (যেমন: S001)</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="start_number" class="form-label">শুরুর নম্বর</label>
                                        <input type="number" class="form-control" id="start_number" name="start_number" value="1" min="1">
                                        <div class="form-text">সর্টকোডের নম্বর কত থেকে শুরু হবে</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="class_id" class="form-label">শ্রেণী (ঐচ্ছিক)</label>
                                        <select class="form-select" id="class_id" name="class_id">
                                            <option value="">সব শ্রেণী</option>
                                            <?php if ($classes_result && $classes_result->num_rows > 0): ?>
                                                <?php while ($class = $classes_result->fetch_assoc()): ?>
                                                    <option value="<?php echo $class['id']; ?>"><?php echo $class['class_name']; ?></option>
                                                <?php endwhile; ?>
                                            <?php endif; ?>
                                        </select>
                                        <div class="form-text">শুধু নির্দিষ্ট শ্রেণীর শিক্ষার্থীদের জন্য সর্টকোড তৈরি করতে চাইলে সিলেক্ট করুন</div>
                                    </div>

                                    <button type="submit" name="generate_shortcodes" class="btn btn-primary">
                                        <i class="fas fa-magic me-1"></i> সর্টকোড তৈরি করুন
                                    </button>
                                </form>
                            </div>
                        </div>

                        <div class="card mt-4">
                            <div class="card-header bg-danger text-white">
                                <h5 class="mb-0"><i class="fas fa-trash-alt me-2"></i> সর্টকোড রিসেট করুন</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i> সতর্কতা: এটি সমস্ত সর্টকোড মুছে ফেলবে!
                                </div>

                                <form method="post" action="">
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="confirm" name="confirm" value="yes">
                                        <label class="form-check-label" for="confirm">হ্যাঁ, আমি সমস্ত সর্টকোড মুছে ফেলতে চাই</label>
                                    </div>

                                    <button type="submit" name="regenerate_shortcodes" class="btn btn-danger">
                                        <i class="fas fa-sync-alt me-1"></i> সর্টকোড রিসেট করুন
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="fas fa-download me-2"></i> এক্সপোর্ট অপশন</h5>
                            </div>
                            <div class="card-body">
                                <p>শিক্ষার্থীদের সর্টকোড এক্সপোর্ট করুন:</p>

                                <div class="d-grid gap-2">
                                    <a href="export_shortcodes.php?format=excel" class="btn btn-success">
                                        <i class="fas fa-file-excel me-1"></i> এক্সেল ফাইল (.xlsx)
                                    </a>

                                    <a href="export_shortcodes.php?format=csv" class="btn btn-info">
                                        <i class="fas fa-file-csv me-1"></i> CSV ফাইল (.csv)
                                    </a>

                                    <a href="export_shortcodes.php?format=pdf" class="btn btn-danger">
                                        <i class="fas fa-file-pdf me-1"></i> PDF ফাইল (.pdf)
                                    </a>

                                    <a href="https://docs.google.com/spreadsheets/d/1Yx8RHHyuTQQOUkLKQpKiQHGGZpvvDMwi9xELjDRxVGQ/copy" target="_blank" class="btn btn-warning">
                                        <i class="fab fa-google me-1"></i> Google Sheets টেমপ্লেট
                                    </a>
                                </div>

                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-info-circle me-2"></i> Google Sheets টেমপ্লেটে WhatsApp লিংক অটোমেটিক তৈরি হবে। টেমপ্লেট কপি করে আপনার শিক্ষার্থীদের তথ্য পেস্ট করুন।
                                </div>
                            </div>
                        </div>

                        <div class="card mt-4">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="fas fa-qrcode me-2"></i> QR কোড</h5>
                            </div>
                            <div class="card-body">
                                <p>শিক্ষার্থীদের সর্টকোড QR কোড হিসেবে প্রিন্ট করুন:</p>

                                <div class="d-grid gap-2">
                                    <a href="print_shortcode_qr.php" target="_blank" class="btn btn-info">
                                        <i class="fas fa-print me-1"></i> QR কোড প্রিন্ট করুন
                                    </a>
                                </div>

                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-info-circle me-2"></i> QR কোড স্ক্যান করলে সরাসরি WhatsApp-এ শিক্ষার্থীর সর্টকোড পাঠানো যাবে।
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Shortcodes List -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i> সর্টকোড তালিকা</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>সর্টকোড</th>
                                        <th>শিক্ষার্থীর নাম</th>
                                        <th>রোল</th>
                                        <th>শ্রেণী</th>
                                        <th>তৈরির তারিখ</th>
                                        <th>অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($shortcodes_result && $shortcodes_result->num_rows > 0): ?>
                                        <?php while ($shortcode = $shortcodes_result->fetch_assoc()): ?>
                                            <tr>
                                                <td>
                                                    <span class="badge bg-primary shortcode-badge"><?php echo $shortcode['shortcode']; ?></span>
                                                    <i class="fas fa-copy ms-2 copy-btn" data-clipboard-text="<?php echo $shortcode['shortcode']; ?>" title="কপি করুন"></i>
                                                </td>
                                                <td>
                                                    <?php
                                                    $name = '';
                                                    if (isset($shortcode['first_name'])) {
                                                        $name .= $shortcode['first_name'];
                                                    }
                                                    if (isset($shortcode['last_name'])) {
                                                        $name .= ' ' . $shortcode['last_name'];
                                                    }
                                                    echo !empty($name) ? $name : 'N/A';
                                                    ?>
                                                </td>
                                                <td><?php echo isset($shortcode['student_id']) ? $shortcode['student_id'] : 'N/A'; ?></td>
                                                <td><?php echo isset($shortcode['class_name']) ? $shortcode['class_name'] : 'N/A'; ?></td>
                                                <td><?php echo date('d/m/Y', strtotime($shortcode['created_at'])); ?></td>
                                                <td>
                                                    <a href="https://wa.me/?text=শিক্ষার্থীর সর্টকোড: <?php echo $shortcode['shortcode']; ?>" target="_blank" class="btn btn-sm whatsapp-btn">
                                                        <i class="fab fa-whatsapp"></i> WhatsApp
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="6" class="text-center">কোন সর্টকোড পাওয়া যায়নি। উপরে "সর্টকোড তৈরি করুন" বাটনে ক্লিক করুন।</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Clipboard.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.8/clipboard.min.js"></script>

    <script>
        // Initialize clipboard.js
        new ClipboardJS('.copy-btn');

        // Show tooltip when copying
        document.querySelectorAll('.copy-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const originalTitle = this.getAttribute('title');
                this.setAttribute('title', 'কপি করা হয়েছে!');

                setTimeout(() => {
                    this.setAttribute('title', originalTitle);
                }, 1000);
            });
        });
    </script>
</body>
</html>
