<?php
// Simple login test page without looping issues
header('Content-Type: text/html; charset=UTF-8');
header('Cache-Control: no-cache, must-revalidate');
header('Connection: close');

ob_start();
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>লগইন টেস্ট - নিশাত এডুকেশন সেন্টার</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: white;
        }
        
        .test-container {
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            text-align: center;
            max-width: 500px;
        }
        
        .status-box {
            background: rgba(76, 175, 80, 0.3);
            border: 2px solid #4CAF50;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .info-box {
            background: rgba(33, 150, 243, 0.3);
            border: 2px solid #2196F3;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        button {
            background: #FF5722;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 16px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s;
        }
        
        button:hover {
            background: #E64A19;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔐 লগইন পেজ টেস্ট</h1>
        
        <div class="status-box">
            ✅ <strong>No Looping Issues!</strong><br>
            This test page loads without any loops
        </div>
        
        <div class="info-box">
            📊 <strong>Status:</strong> Page loaded successfully<br>
            🕒 <strong>Time:</strong> <?php echo date('Y-m-d H:i:s'); ?><br>
            🌐 <strong>URL:</strong> <?php echo $_SERVER['REQUEST_URI']; ?>
        </div>
        
        <div class="info-box">
            🎯 <strong>Test Results:</strong><br>
            • No meta refresh tags<br>
            • No infinite redirects<br>
            • No JavaScript loops<br>
            • Proper response headers
        </div>
        
        <button onclick="window.location.href='login.php'">Go to Fixed Login Page</button>
        <button onclick="window.location.href='index.php'">Go to Home Page</button>
        
        <div style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
            <p>📝 If this page loads without looping, the fix is working!</p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Login test page loaded - no looping detected');
            
            // Monitor for any unexpected redirects
            let redirectCount = 0;
            const originalLocation = window.location.href;
            
            setInterval(function() {
                if (window.location.href !== originalLocation) {
                    redirectCount++;
                    console.warn('Unexpected redirect detected:', redirectCount);
                }
            }, 1000);
        });
    </script>
</body>
</html>
<?php
$content = ob_get_contents();
ob_end_clean();

header('Content-Length: ' . strlen($content));
echo $content;

if (function_exists('fastcgi_finish_request')) {
    fastcgi_finish_request();
}
exit();
?>
