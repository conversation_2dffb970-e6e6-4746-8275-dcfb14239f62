<?php
/**
 * Update Font Script
 * This script updates all PHP files in the admin directory to use the Hind Siliguri font.
 */

// Define the directory to scan
$directory = __DIR__;

// Get all PHP files in the directory and subdirectories
$files = new RecursiveIteratorIterator(
    new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS),
    RecursiveIteratorIterator::SELF_FIRST,
    RecursiveIteratorIterator::CATCH_GET_CHILD
);

// Files to exclude from processing
$exclude_files = [
    'update_font.php',
    'dashboard.php', // Already updated
    'dbh.inc.php',
    'functions.php'
];

// Counter for updated files
$updated_files = 0;

// Process each PHP file
foreach ($files as $file) {
    if ($file->isFile() && $file->getExtension() === 'php') {
        $filename = $file->getBasename();
        $filepath = $file->getPathname();
        
        // Skip excluded files
        if (in_array($filename, $exclude_files)) {
            continue;
        }
        
        // Skip files in the includes directory
        if (strpos($filepath, 'includes' . DIRECTORY_SEPARATOR) !== false) {
            continue;
        }
        
        // Read the file content
        $content = file_get_contents($filepath);
        
        // Check if the file has a head section
        if (strpos($content, '<head>') !== false && strpos($content, 'hind-siliguri.css') === false) {
            // Add Hind Siliguri CSS link
            $pattern = '/<head>(.*?)<\/head>/s';
            if (preg_match($pattern, $content, $matches)) {
                $head_content = $matches[1];
                
                // Check if there's already a Google Fonts link for Hind Siliguri
                if (strpos($head_content, 'Hind+Siliguri') !== false) {
                    // Replace Google Fonts link with our CSS file
                    $new_head = preg_replace(
                        '/<link.*?Hind\+Siliguri.*?>/s',
                        '<link rel="stylesheet" href="css/hind-siliguri.css">',
                        $head_content
                    );
                } else {
                    // Add our CSS file before the closing head tag
                    $new_head = $head_content . "\n    <!-- Hind Siliguri Font CSS -->\n    <link rel=\"stylesheet\" href=\"css/hind-siliguri.css\">";
                }
                
                // Replace the head section
                $new_content = str_replace($matches[0], "<head>" . $new_head . "</head>", $content);
                
                // Write the updated content back to the file
                if ($new_content !== $content) {
                    file_put_contents($filepath, $new_content);
                    $updated_files++;
                    echo "Updated: $filename<br>";
                }
            }
        }
    }
}

echo "<h3>Font Update Complete</h3>";
echo "Updated $updated_files files.<br>";
echo "<a href='dashboard.php'>Return to Dashboard</a>";
?>
