<?php
// Get the latest notice from database
$notice_text = "বর্তমানে কোন নোটিশ নেই।";
try {
    // Include database connection file if not already included
    if (!function_exists('ensure_connection')) {
        require_once 'dbh.inc.php';
    }

    // Ensure we have a valid connection
    $conn = ensure_connection();

    // Check if notices table exists
    $latest_notice_query = "SHOW TABLES LIKE 'notices'";
    $latest_notice_result = $conn->query($latest_notice_query);

    if ($latest_notice_result && $latest_notice_result->num_rows > 0) {
        // Get latest notice
        $sql = "SELECT * FROM notices ORDER BY date DESC LIMIT 1";
        $result = $conn->query($sql);

        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $notice_text = htmlspecialchars($row['title']) . ' - ' . htmlspecialchars(substr($row['content'], 0, 150)) .
                 (strlen($row['content']) > 150 ? '...' : '');
        }
    }
} catch (Exception $e) {
    $notice_text = "নোটিশ লোড করতে সমস্যা হচ্ছে। পরে আবার চেষ্টা করুন।";
    error_log('Notice Error: ' . $e->getMessage());
}
?>

<!-- Very Simple Marquee Notice -->
<style>
.notice-bar {
    background-color: #f5f5f5;
    border-top: 1px solid #dee2e6;
    border-bottom: 1px solid #dee2e6;
    padding: 0;
    height: 60px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.notice-marquee {
    color: #006A4E;
    font-weight: 600;
    font-size: 22px;
    line-height: 60px;
    height: 60px;
}
</style>

<div class="notice-bar">
    <div class="container">
        <marquee behavior="scroll" scrollamount="5" direction="left" onmouseover="this.stop();" onmouseout="this.start();" class="notice-marquee">
            <i class="fas fa-bullhorn me-2"></i> <strong>সর্বশেষ নোটিশ:</strong> <?php echo $notice_text; ?>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <i class="fas fa-calendar-alt me-2"></i> <strong>আজকের তারিখ:</strong> <?php echo date('d/m/Y'); ?>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <i class="fas fa-graduation-cap me-2"></i> <strong>ভর্তি চলছে:</strong> নতুন শিক্ষার্থীদের জন্য ভর্তি চলছে। আজই যোগাযোগ করুন।
        </marquee>
    </div>
</div>
