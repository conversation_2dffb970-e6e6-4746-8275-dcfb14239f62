<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    echo '<div class="alert alert-danger">অননুমোদিত প্রবেশ!</div>';
    exit();
}

require_once '../includes/dbh.inc.php';
require_once 'class_subjects_helper.php';

$classId = isset($_POST['class_id']) ? intval($_POST['class_id']) : 0;
$departmentId = isset($_POST['department_id']) && !empty($_POST['department_id']) ? intval($_POST['department_id']) : null;

if (!$classId) {
    echo '<div class="alert alert-danger">ক্লাস আইডি প্রয়োজন!</div>';
    exit();
}

try {
    // Get class name
    $classQuery = "SELECT class_name FROM classes WHERE id = ?";
    $stmt = $conn->prepare($classQuery);
    $stmt->bind_param("i", $classId);
    $stmt->execute();
    $classResult = $stmt->get_result();
    $className = $classResult->num_rows > 0 ? $classResult->fetch_assoc()['class_name'] : 'অজানা ক্লাস';

    // Get department name if specified
    $departmentName = 'সকল বিভাগ';
    if ($departmentId) {
        $deptQuery = "SELECT department_name FROM departments WHERE id = ?";
        $stmt = $conn->prepare($deptQuery);
        $stmt->bind_param("i", $departmentId);
        $stmt->execute();
        $deptResult = $stmt->get_result();
        $departmentName = $deptResult->num_rows > 0 ? $deptResult->fetch_assoc()['department_name'] : 'অজানা বিভাগ';
    }

    // Get class subjects with their exam patterns
    $classSubjects = getClassSubjects($classId, $departmentId);
    
    if (empty($classSubjects)) {
        echo '<div class="alert alert-warning">এই ক্লাসের জন্য কোন বিষয় কনফিগার করা হয়নি।</div>';
        exit();
    }

    // Get existing exam patterns for these subjects
    $subjectIds = array_column($classSubjects, 'subject_id');
    $placeholders = str_repeat('?,', count($subjectIds) - 1) . '?';
    
    $patternsQuery = "SELECT p.*, s.subject_name, s.subject_code 
                     FROM subject_exam_pattern p
                     JOIN subjects s ON p.subject_id = s.id
                     WHERE p.subject_id IN ($placeholders)
                     ORDER BY s.subject_name";
    $stmt = $conn->prepare($patternsQuery);
    $stmt->bind_param(str_repeat('i', count($subjectIds)), ...$subjectIds);
    $stmt->execute();
    $patternsResult = $stmt->get_result();
    
    $patterns = [];
    while ($pattern = $patternsResult->fetch_assoc()) {
        $patterns[$pattern['subject_id']] = $pattern;
    }

    echo '<div class="alert alert-info">';
    echo '<h6><i class="fas fa-info-circle me-2"></i>ক্লাস: ' . htmlspecialchars($className) . ' - ' . htmlspecialchars($departmentName) . '</h6>';
    echo '<p class="mb-0">বর্তমান মার্কস প্যাটার্ন কনফিগারেশন:</p>';
    echo '</div>';

    if (empty($patterns)) {
        echo '<div class="alert alert-warning">';
        echo '<i class="fas fa-exclamation-triangle me-2"></i>এই ক্লাসের কোন বিষয়ের জন্য মার্কস প্যাটার্ন কনফিগার করা হয়নি।';
        echo '</div>';
        exit();
    }

    echo '<div class="table-responsive">';
    echo '<table class="table table-striped table-hover">';
    echo '<thead class="table-dark">';
    echo '<tr>';
    echo '<th>বিষয়</th>';
    echo '<th>বিষয়ের ধরন</th>';
    echo '<th>মোট নম্বর</th>';
    echo '<th>সৃজনশীল</th>';
    echo '<th>বহুনির্বাচনি</th>';
    echo '<th>ব্যবহারিক</th>';
    echo '<th>স্ট্যাটাস</th>';
    echo '<th>সর্বশেষ আপডেট</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';

    // Create a map of subject types for quick lookup
    $subjectTypeMap = [];
    foreach ($classSubjects as $subject) {
        $subjectTypeMap[$subject['subject_id']] = $subject['subject_type'];
    }

    foreach ($patterns as $pattern) {
        $subjectType = isset($subjectTypeMap[$pattern['subject_id']]) ? $subjectTypeMap[$pattern['subject_id']] : 'unknown';
        
        // Subject type display
        $typeText = '';
        $typeBadgeClass = '';
        switch ($subjectType) {
            case 'required':
                $typeText = 'আবশ্যিক';
                $typeBadgeClass = 'bg-success';
                break;
            case 'optional':
                $typeText = 'ঐচ্ছিক';
                $typeBadgeClass = 'bg-info';
                break;
            case 'fourth':
                $typeText = 'চতুর্থ বিষয়';
                $typeBadgeClass = 'bg-warning';
                break;
            default:
                $typeText = 'অজানা';
                $typeBadgeClass = 'bg-secondary';
        }

        echo '<tr>';
        echo '<td>';
        echo '<strong>' . htmlspecialchars($pattern['subject_name']) . '</strong><br>';
        echo '<small class="text-muted">' . htmlspecialchars($pattern['subject_code']) . '</small>';
        echo '</td>';
        echo '<td><span class="badge ' . $typeBadgeClass . '">' . $typeText . '</span></td>';
        echo '<td><span class="badge bg-primary">' . $pattern['total_marks'] . '</span></td>';
        
        // CQ marks
        echo '<td>';
        if ($pattern['has_cq']) {
            echo '<span class="badge bg-success">' . $pattern['cq_marks'] . '</span>';
        } else {
            echo '<span class="badge bg-secondary">নেই</span>';
        }
        echo '</td>';
        
        // MCQ marks
        echo '<td>';
        if ($pattern['has_mcq']) {
            echo '<span class="badge bg-info">' . $pattern['mcq_marks'] . '</span>';
        } else {
            echo '<span class="badge bg-secondary">নেই</span>';
        }
        echo '</td>';
        
        // Practical marks
        echo '<td>';
        if ($pattern['has_practical']) {
            echo '<span class="badge bg-warning">' . $pattern['practical_marks'] . '</span>';
        } else {
            echo '<span class="badge bg-secondary">নেই</span>';
        }
        echo '</td>';
        
        // Status
        echo '<td>';
        if ($pattern['status'] == 'active') {
            echo '<span class="badge bg-success">সক্রিয়</span>';
        } else {
            echo '<span class="badge bg-danger">নিষ্ক্রিয়</span>';
        }
        echo '</td>';
        
        // Last updated
        echo '<td>';
        echo '<small class="text-muted">' . date('d/m/Y H:i', strtotime($pattern['updated_at'])) . '</small>';
        echo '</td>';
        
        echo '</tr>';
    }

    echo '</tbody>';
    echo '</table>';
    echo '</div>';

    // Summary statistics
    $totalSubjects = count($classSubjects);
    $configuredSubjects = count($patterns);
    $unconfiguredSubjects = $totalSubjects - $configuredSubjects;

    echo '<div class="row mt-4">';
    echo '<div class="col-md-4">';
    echo '<div class="card bg-primary text-white">';
    echo '<div class="card-body text-center">';
    echo '<h5>' . $totalSubjects . '</h5>';
    echo '<p class="mb-0">মোট বিষয়</p>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="col-md-4">';
    echo '<div class="card bg-success text-white">';
    echo '<div class="card-body text-center">';
    echo '<h5>' . $configuredSubjects . '</h5>';
    echo '<p class="mb-0">কনফিগার করা বিষয়</p>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="col-md-4">';
    echo '<div class="card bg-warning text-white">';
    echo '<div class="card-body text-center">';
    echo '<h5>' . $unconfiguredSubjects . '</h5>';
    echo '<p class="mb-0">অকনফিগার বিষয়</p>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';

    if ($unconfiguredSubjects > 0) {
        echo '<div class="alert alert-warning mt-3">';
        echo '<i class="fas fa-exclamation-triangle me-2"></i>';
        echo '<strong>সতর্কতা:</strong> ' . $unconfiguredSubjects . 'টি বিষয়ের জন্য এখনও মার্কস প্যাটার্ন কনফিগার করা হয়নি।';
        echo '</div>';
    }

} catch (Exception $e) {
    echo '<div class="alert alert-danger">';
    echo '<i class="fas fa-exclamation-circle me-2"></i>ডেটা লোড করতে সমস্যা হয়েছে: ' . htmlspecialchars($e->getMessage());
    echo '</div>';
}
?>
