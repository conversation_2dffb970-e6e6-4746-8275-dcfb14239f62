<?php
// Database Connection
require_once 'includes/dbh.inc.php';

echo "<h1>Results Table Structure</h1>";

// Check if results table exists
$tableCheck = $conn->query("SHOW TABLES LIKE 'results'");
if ($tableCheck->num_rows == 0) {
    echo "<p>Results table does not exist!</p>";
    exit;
}

// Get table structure
echo "<h2>Table Structure</h2>";
$columnsQuery = "SHOW COLUMNS FROM results";
$columnsResult = $conn->query($columnsQuery);

if ($columnsResult) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($column = $columnsResult->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>Error getting columns: " . $conn->error . "</p>";
}

// Check if total_marks column exists
$checkTotalMarksColumn = $conn->query("SHOW COLUMNS FROM results LIKE 'total_marks'");
if ($checkTotalMarksColumn->num_rows == 0) {
    echo "<p style='color:red;'><strong>The 'total_marks' column does not exist in the results table!</strong></p>";
    
    // Suggest adding the column
    echo "<h2>Add total_marks Column</h2>";
    echo "<p>To add the missing column, click the button below:</p>";
    
    if (isset($_POST['add_column'])) {
        $alterQuery = "ALTER TABLE results ADD COLUMN total_marks FLOAT NOT NULL DEFAULT 100 AFTER marks_obtained";
        if ($conn->query($alterQuery)) {
            echo "<p style='color:green;'>Successfully added 'total_marks' column to the results table!</p>";
        } else {
            echo "<p style='color:red;'>Error adding column: " . $conn->error . "</p>";
        }
    }
    
    echo "<form method='post'>";
    echo "<input type='submit' name='add_column' value='Add total_marks Column'>";
    echo "</form>";
} else {
    echo "<p style='color:green;'><strong>The 'total_marks' column exists in the results table.</strong></p>";
}

// Show a sample of data from the results table
echo "<h2>Sample Data (10 rows)</h2>";
$sampleQuery = "SELECT * FROM results LIMIT 10";
$sampleResult = $conn->query($sampleQuery);

if ($sampleResult && $sampleResult->num_rows > 0) {
    echo "<table border='1' cellpadding='5'>";
    
    // Get field names
    $fields = $sampleResult->fetch_fields();
    echo "<tr>";
    foreach ($fields as $field) {
        echo "<th>" . $field->name . "</th>";
    }
    echo "</tr>";
    
    // Reset result pointer
    $sampleResult->data_seek(0);
    
    // Output data
    while ($row = $sampleResult->fetch_assoc()) {
        echo "<tr>";
        foreach ($row as $value) {
            echo "<td>" . htmlspecialchars($value) . "</td>";
        }
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>No data in the results table or error: " . $conn->error . "</p>";
}

$conn->close();
?>
