<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Disable caching
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");

session_start();
require_once '../includes/dbh.inc.php';

// Export Content Generation Functions
function generatePdfContent($data) {
    $html = '<!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>ফি রিপোর্ট</title>
        <style>
            body { font-family: "Hind Siliguri", Arial, sans-serif; font-size: 12px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .header { text-align: center; margin-bottom: 20px; }
            .total { font-weight: bold; background-color: #f8f9fa; }
        </style>
    </head>
    <body>
        <div class="header">
            <h2>ফি ম্যানেজমেন্ট রিপোর্ট</h2>
            <p>তারিখ: ' . date('d/m/Y H:i:s') . '</p>
        </div>
        <table>
            <thead>
                <tr>
                    <th>আইডি</th>
                    <th>শিক্ষার্থী</th>
                    <th>রোল</th>
                    <th>শ্রেণী</th>
                    <th>ফি ধরন</th>
                    <th>পরিমাণ</th>
                    <th>পরিশোধিত</th>
                    <th>বকেয়া</th>
                    <th>স্ট্যাটাস</th>
                </tr>
            </thead>
            <tbody>';

    $totalAmount = 0;
    $totalPaid = 0;
    $totalDue = 0;

    foreach ($data as $fee) {
        $dueAmount = $fee['amount'] - $fee['paid'];
        $statusText = $fee['payment_status'] === 'due' ? 'বকেয়া' : ($fee['payment_status'] === 'partial' ? 'আংশিক' : 'পরিশোধিত');

        $totalAmount += $fee['amount'];
        $totalPaid += $fee['paid'];
        $totalDue += $dueAmount;

        $html .= '<tr>
            <td>' . $fee['id'] . '</td>
            <td>' . htmlspecialchars($fee['first_name'] . ' ' . $fee['last_name']) . '</td>
            <td>' . htmlspecialchars($fee['roll']) . '</td>
            <td>' . htmlspecialchars($fee['class_name'] ?? 'N/A') . '</td>
            <td>' . htmlspecialchars($fee['fee_type']) . '</td>
            <td>৳ ' . number_format($fee['amount'], 2) . '</td>
            <td>৳ ' . number_format($fee['paid'], 2) . '</td>
            <td>৳ ' . number_format($dueAmount, 2) . '</td>
            <td>' . $statusText . '</td>
        </tr>';
    }

    $html .= '<tr class="total">
        <td colspan="5"><strong>মোট</strong></td>
        <td><strong>৳ ' . number_format($totalAmount, 2) . '</strong></td>
        <td><strong>৳ ' . number_format($totalPaid, 2) . '</strong></td>
        <td><strong>৳ ' . number_format($totalDue, 2) . '</strong></td>
        <td></td>
    </tr>';

    $html .= '</tbody></table></body></html>';
    return $html;
}

function generatePrintContent($data) {
    $html = '<!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>ফি রিপোর্ট - প্রিন্ট</title>
        <style>
            body { font-family: "Hind Siliguri", Arial, sans-serif; font-size: 14px; margin: 20px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #333; padding: 10px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; text-align: center; }
            .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
            .total { font-weight: bold; background-color: #e9ecef; }
            .summary { margin-top: 30px; padding: 20px; background-color: #f8f9fa; border: 1px solid #ddd; }
            @media print {
                body { margin: 0; }
                .no-print { display: none; }
            }
        </style>
        <script>
            window.onload = function() {
                window.print();
            }
        </script>
    </head>
    <body>
        <div class="header">
            <h1>ফি ম্যানেজমেন্ট রিপোর্ট</h1>
            <p><strong>প্রিন্ট তারিখ:</strong> ' . date('d/m/Y H:i:s') . '</p>
        </div>

        <table>
            <thead>
                <tr>
                    <th style="width: 5%;">আইডি</th>
                    <th style="width: 25%;">শিক্ষার্থী</th>
                    <th style="width: 10%;">রোল</th>
                    <th style="width: 10%;">শ্রেণী</th>
                    <th style="width: 15%;">ফি ধরন</th>
                    <th style="width: 10%;">পরিমাণ</th>
                    <th style="width: 10%;">পরিশোধিত</th>
                    <th style="width: 10%;">বকেয়া</th>
                    <th style="width: 5%;">স্ট্যাটাস</th>
                </tr>
            </thead>
            <tbody>';

    $totalAmount = 0;
    $totalPaid = 0;
    $totalDue = 0;
    $recordCount = 0;

    foreach ($data as $fee) {
        $dueAmount = $fee['amount'] - $fee['paid'];
        $statusText = $fee['payment_status'] === 'due' ? 'বকেয়া' : ($fee['payment_status'] === 'partial' ? 'আংশিক' : 'পরিশোধিত');

        $totalAmount += $fee['amount'];
        $totalPaid += $fee['paid'];
        $totalDue += $dueAmount;
        $recordCount++;

        $html .= '<tr>
            <td style="text-align: center;">' . $fee['id'] . '</td>
            <td>' . htmlspecialchars($fee['first_name'] . ' ' . $fee['last_name']) . '</td>
            <td style="text-align: center;">' . htmlspecialchars($fee['roll']) . '</td>
            <td style="text-align: center;">' . htmlspecialchars($fee['class_name'] ?? 'N/A') . '</td>
            <td>' . htmlspecialchars($fee['fee_type']) . '</td>
            <td style="text-align: right;">৳ ' . number_format($fee['amount'], 2) . '</td>
            <td style="text-align: right;">৳ ' . number_format($fee['paid'], 2) . '</td>
            <td style="text-align: right;">৳ ' . number_format($dueAmount, 2) . '</td>
            <td style="text-align: center;">' . $statusText . '</td>
        </tr>';
    }

    $html .= '<tr class="total">
        <td colspan="5" style="text-align: right;"><strong>মোট (' . $recordCount . ' টি রেকর্ড):</strong></td>
        <td style="text-align: right;"><strong>৳ ' . number_format($totalAmount, 2) . '</strong></td>
        <td style="text-align: right;"><strong>৳ ' . number_format($totalPaid, 2) . '</strong></td>
        <td style="text-align: right;"><strong>৳ ' . number_format($totalDue, 2) . '</strong></td>
        <td></td>
    </tr>';

    $html .= '</tbody></table>

    <div class="summary">
        <h3>সারসংক্ষেপ</h3>
        <div style="display: flex; justify-content: space-between;">
            <div><strong>মোট রেকর্ড:</strong> ' . $recordCount . ' টি</div>
            <div><strong>মোট ফি:</strong> ৳ ' . number_format($totalAmount, 2) . '</div>
            <div><strong>মোট পরিশোধিত:</strong> ৳ ' . number_format($totalPaid, 2) . '</div>
            <div><strong>মোট বকেয়া:</strong> ৳ ' . number_format($totalDue, 2) . '</div>
        </div>
    </div>

    </body></html>';
    return $html;
}

// Debug session data
error_log("Session data in fee_management.php: " . print_r($_SESSION, true));

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Check if fees table exists and create it if it doesn't
$checkFeesTableQuery = "SHOW TABLES LIKE 'fees'";
$feesTableResult = $conn->query($checkFeesTableQuery);

// Check if fee_categories table exists
$checkCategoriesTableQuery = "SHOW TABLES LIKE 'fee_categories'";
$categoriesTableResult = $conn->query($checkCategoriesTableQuery);

// Check if fee_types table exists
$checkFeeTypesTableQuery = "SHOW TABLES LIKE 'fee_types'";
$feeTypesTableResult = $conn->query($checkFeeTypesTableQuery);

// Create fee_types table if it doesn't exist
if ($feeTypesTableResult->num_rows === 0) {
    $createFeeTypesTableQuery = "CREATE TABLE IF NOT EXISTS fee_types (
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        is_recurring TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        amount DECIMAL(10,2) DEFAULT 0.00
    )";

    if ($conn->query($createFeeTypesTableQuery)) {
        error_log("Fee types table created successfully");

        // Insert some default fee types
        $defaultFeeTypes = [
            ["মাসিক বেতন", "প্রতি মাসে দিতে হবে", 1, 150.00],
            ["ভর্তি ফি", "ভর্তির সময় একবার দিতে হবে", 0, 500.00],
            ["পরীক্ষার ফি", "পরীক্ষার আগে দিতে হবে", 0, 200.00]
        ];

        $insertFeeTypeQuery = "INSERT INTO fee_types (name, description, is_recurring, amount) VALUES (?, ?, ?, ?)";
        $stmt = $conn->prepare($insertFeeTypeQuery);

        foreach ($defaultFeeTypes as $feeType) {
            $stmt->bind_param('ssid', $feeType[0], $feeType[1], $feeType[2], $feeType[3]);
            $stmt->execute();
        }

        error_log("Default fee types added");
    } else {
        error_log("Error creating fee types table: " . $conn->error);
    }
}

// Create fee_categories table if it doesn't exist
if ($categoriesTableResult->num_rows === 0) {
    $createCategoriesTableQuery = "CREATE TABLE IF NOT EXISTS fee_categories (
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";

    if ($conn->query($createCategoriesTableQuery)) {
        error_log("Fee categories table created successfully");

        // Insert default category
        $insertDefaultCategoryQuery = "INSERT INTO fee_categories (id, name, description) VALUES (1, 'Default', 'Default fee category')";
        if ($conn->query($insertDefaultCategoryQuery)) {
            error_log("Default fee category added");
        } else {
            error_log("Error adding default fee category: " . $conn->error);
        }
    } else {
        error_log("Error creating fee categories table: " . $conn->error);
    }
}

if ($feesTableResult->num_rows === 0) {
    // Create fees table
    $createFeesTableQuery = "CREATE TABLE IF NOT EXISTS fees (
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        student_id INT(11) NOT NULL,
        fee_type VARCHAR(100) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        paid DECIMAL(10,2) DEFAULT 0.00,
        due_date DATE NOT NULL,
        payment_status ENUM('due','partial','paid') DEFAULT 'due',
        category_id INT(11) DEFAULT 1,
        payment_date DATE DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE
    )";

    if ($conn->query($createFeesTableQuery)) {
        error_log("Fees table created successfully");
    } else {
        error_log("Error creating fees table: " . $conn->error);
    }
}

// Check if payments table exists and create it if it doesn't
$checkPaymentsTableQuery = "SHOW TABLES LIKE 'payments'";
$paymentsTableResult = $conn->query($checkPaymentsTableQuery);

if ($paymentsTableResult->num_rows === 0) {
    // Create payments table
    $createPaymentsTableQuery = "CREATE TABLE IF NOT EXISTS payments (
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        fee_id INT(11) NOT NULL,
        student_id INT(11) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        payment_date DATE NOT NULL,
        payment_method ENUM('cash', 'bank', 'mobile_banking', 'card', 'cheque') DEFAULT 'cash',
        receipt_no VARCHAR(100) UNIQUE,
        transaction_id VARCHAR(100),
        reference_number VARCHAR(100),
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (fee_id) REFERENCES fees(id) ON DELETE CASCADE,
        FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE
    )";

    if ($conn->query($createPaymentsTableQuery)) {
        error_log("Payments table created successfully");
    } else {
        error_log("Error creating payments table: " . $conn->error);
    }
}

// Include header
include_once 'includes/header.php';
?>

<!-- Custom CSS for Fee Management Header -->
<style>
    /* Enhanced Header Styling */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        position: relative;
        overflow: hidden;
    }

    .bg-gradient-primary::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='7' cy='7' r='7'/%3E%3Ccircle cx='53' cy='53' r='7'/%3E%3Ccircle cx='53' cy='7' r='7'/%3E%3Ccircle cx='7' cy='53' r='7'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        opacity: 0.3;
        z-index: 0;
    }

    .card-header > * {
        position: relative;
        z-index: 1;
    }

    /* Button Group Enhancements */
    .btn-group .btn {
        border-radius: 6px !important;
        margin: 0 2px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .btn-group .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .btn-light {
        background: rgba(255,255,255,0.95);
        border: 1px solid rgba(255,255,255,0.3);
        color: #495057;
    }

    .btn-light:hover {
        background: #ffffff;
        color: #212529;
    }

    .btn-outline-light {
        border: 2px solid rgba(255,255,255,0.5);
        color: #ffffff;
        background: rgba(255,255,255,0.1);
    }

    .btn-outline-light:hover {
        background: rgba(255,255,255,0.2);
        border-color: rgba(255,255,255,0.8);
        color: #ffffff;
    }

    .btn-success {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
    }

    .btn-info {
        background: linear-gradient(45deg, #17a2b8, #6f42c1);
        border: none;
    }

    .btn-secondary {
        background: linear-gradient(45deg, #6c757d, #495057);
        border: none;
    }

    /* Responsive Button Text */
    @media (max-width: 768px) {
        .btn-group {
            flex-wrap: wrap;
        }

        .btn-group .btn {
            margin-bottom: 5px;
        }
    }

    /* Dropdown Menu Styling */
    .dropdown-menu {
        border: none;
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        border-radius: 10px;
        padding: 10px 0;
    }

    .dropdown-item {
        padding: 8px 20px;
        transition: all 0.3s ease;
    }

    .dropdown-item:hover {
        background: linear-gradient(45deg, #f8f9fa, #e9ecef);
        transform: translateX(5px);
    }

    /* Header Title Enhancement */
    .card-header h5 {
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        font-weight: 600;
    }

    .card-header small {
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    /* Icon Enhancements */
    .fas, .far {
        filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
    }

    /* Animation for buttons */
    @keyframes buttonPulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .btn:active {
        animation: buttonPulse 0.3s ease;
    }
</style>

<?php

// Process fee addition
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    error_log("POST request received: " . print_r($_POST, true));

    if (isset($_POST['add_fee'])) {
        // Debug log
        error_log("Add fee form submitted");
        error_log("POST data: " . print_r($_POST, true));

        $studentIds = isset($_POST['student_ids']) ? $_POST['student_ids'] : [];
        $feeType = isset($_POST['fee_type']) ? $_POST['fee_type'] : '';
        $amount = isset($_POST['amount']) ? floatval($_POST['amount']) : 0;
        $dueDate = isset($_POST['due_date']) ? $_POST['due_date'] : date('Y-m-d');

        error_log("Processing fee addition - Students: " . count($studentIds) . ", Fee Type: $feeType, Amount: $amount, Due Date: $dueDate");

        // Check if "other" fee type is selected
        if ($feeType === 'other' && !empty($_POST['other_fee_type'])) {
            $feeType = $_POST['other_fee_type'];
            error_log("Using custom fee type: $feeType");

            // Add this new fee type to fee_types table
            $checkFeeTypeQuery = "SELECT id FROM fee_types WHERE name = ?";
            $stmt = $conn->prepare($checkFeeTypeQuery);
            $stmt->bind_param('s', $feeType);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows === 0) {
                // Insert new fee type
                $insertFeeTypeQuery = "INSERT INTO fee_types (name, description, is_recurring) VALUES (?, 'অন্যান্য ফি', 0)";
                $stmt = $conn->prepare($insertFeeTypeQuery);
                $stmt->bind_param('s', $feeType);
                $stmt->execute();
                error_log("Added new fee type: $feeType");
            }
        }

        // Get category_id from form or use default value 1
        $categoryId = isset($_POST['category_id']) ? intval($_POST['category_id']) : 1;

        // Check if category exists
        $getCategoryQuery = "SELECT id FROM fee_categories WHERE id = ?";
        $stmt = $conn->prepare($getCategoryQuery);
        $stmt->bind_param('i', $categoryId);
        $stmt->execute();
        $categoryResult = $stmt->get_result();

        // If category doesn't exist, use default category
        if ($categoryResult->num_rows === 0) {
            $categoryId = 1;

            // Check if fee_categories table exists and has default category
            $checkDefaultQuery = "SELECT id FROM fee_categories WHERE id = 1";
            $defaultResult = $conn->query($checkDefaultQuery);

            // If fee_categories table doesn't exist or has no default record, create it
            if (!$defaultResult || $defaultResult->num_rows === 0) {
                // Create fee_categories table if it doesn't exist
                $createCategoryTableQuery = "CREATE TABLE IF NOT EXISTS fee_categories (
                    id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )";
                $conn->query($createCategoryTableQuery);

                // Insert default category
                $insertDefaultCategoryQuery = "INSERT INTO fee_categories (id, name, description) VALUES (1, 'Default', 'Default fee category')";
                $conn->query($insertDefaultCategoryQuery);
            }
        }

        if (!empty($studentIds) && !empty($feeType) && $amount > 0) {
            // Start transaction
            $conn->begin_transaction();
            error_log("Starting transaction for fee addition");

            try {
                $successCount = 0;
                $existingCount = 0;
                $errorCount = 0;

                // Prepare statements
                $checkQuery = "SELECT id FROM fees WHERE student_id = ? AND fee_type = ? AND due_date = ?";
                $checkStmt = $conn->prepare($checkQuery);

                if (!$checkStmt) {
                    error_log("Error preparing check statement: " . $conn->error);
                    throw new Exception("Database error: " . $conn->error);
                }

                $insertQuery = "INSERT INTO fees (student_id, fee_type, amount, paid, due_date, payment_status, category_id)
                               VALUES (?, ?, ?, 0, ?, ?, ?)";
                $insertStmt = $conn->prepare($insertQuery);

                if (!$insertStmt) {
                    error_log("Error preparing insert statement: " . $conn->error);
                    throw new Exception("Database error: " . $conn->error);
                }

                $paymentStatus = 'due';
                $categoryId = isset($_POST['category_id']) ? (int)$_POST['category_id'] : 1;
                error_log("Processing " . count($studentIds) . " students with category ID: " . $categoryId);

                // Process each student
                foreach ($studentIds as $studentId) {
                    error_log("Processing student ID: $studentId");

                    // Check if fee already exists
                    $checkStmt->bind_param('iss', $studentId, $feeType, $dueDate);
                    $checkResult = $checkStmt->execute();

                    if (!$checkResult) {
                        error_log("Error executing check statement: " . $checkStmt->error);
                        $errorCount++;
                        continue;
                    }

                    $result = $checkStmt->get_result();

                    if ($result->num_rows > 0) {
                        error_log("Fee already exists for student ID: $studentId");
                        $existingCount++;
                    } else {
                        // Create new fee
                        $insertStmt->bind_param('isdssi', $studentId, $feeType, $amount, $dueDate, $paymentStatus, $categoryId);
                        $insertResult = $insertStmt->execute();

                        if ($insertResult) {
                            error_log("Successfully added fee for student ID: $studentId");
                            $successCount++;
                        } else {
                            error_log("Error adding fee for student ID: $studentId - " . $insertStmt->error);
                            $errorCount++;
                        }
                    }
                }

                // Commit transaction
                $conn->commit();
                error_log("Transaction committed. Success: $successCount, Existing: $existingCount, Errors: $errorCount");

                // Set appropriate message
                if ($successCount > 0) {
                    $_SESSION['success'] = $successCount . ' জন শিক্ষার্থীর ফি সফলভাবে যোগ করা হয়েছে!';
                    error_log("Success message set: " . $_SESSION['success']);

                    if ($existingCount > 0) {
                        $_SESSION['warning'] = $existingCount . ' জন শিক্ষার্থীর ফি ইতিমধ্যে বিদ্যমান ছিল।';
                        error_log("Warning message set: " . $_SESSION['warning']);
                    }

                    if ($errorCount > 0) {
                        $_SESSION['error'] = $errorCount . ' জন শিক্ষার্থীর ফি যোগ করতে সমস্যা হয়েছে।';
                        error_log("Error message set: " . $_SESSION['error']);
                    }
                } else if ($existingCount > 0 && $errorCount == 0) {
                    $_SESSION['warning'] = 'সকল শিক্ষার্থীর ফি ইতিমধ্যে বিদ্যমান!';
                    error_log("Warning message set: " . $_SESSION['warning']);
                } else {
                    $_SESSION['error'] = 'ফি যোগ করতে সমস্যা হয়েছে।';
                    error_log("Error message set: " . $_SESSION['error']);
                }

                // Always show results instead of redirecting to avoid blank page
                echo "<!DOCTYPE html><html><head><title>Fee Addition Result</title>";
                echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
                echo "</head><body><div class='container mt-4'>";

                echo "<div class='alert alert-success'>";
                echo "<h3><i class='fas fa-check-circle'></i> ফি সফলভাবে যোগ করা হয়েছে!</h3>";
                echo "<ul class='mb-0'>";
                echo "<li><strong>সফল:</strong> $successCount জন</li>";
                if ($existingCount > 0) echo "<li><strong>ইতিমধ্যে বিদ্যমান:</strong> $existingCount জন</li>";
                if ($errorCount > 0) echo "<li><strong>ত্রুটি:</strong> $errorCount জন</li>";
                echo "</ul>";
                echo "</div>";

                echo "<div class='d-flex gap-2'>";
                echo "<a href='fee_management.php' class='btn btn-primary'>ফি ম্যানেজমেন্টে ফিরে যান</a>";
                echo "<a href='fee_management.php#add-fee' class='btn btn-success'>আরো ফি যোগ করুন</a>";
                echo "</div>";

                echo "</div></body></html>";
                exit();

            } catch (Exception $e) {
                // Roll back transaction on error
                $conn->rollback();
                $errorMessage = 'ফি যোগ করতে সমস্যা: ' . $e->getMessage();
                $_SESSION['error'] = $errorMessage;
                error_log("Transaction rolled back. Error: " . $e->getMessage());

                // Always show error instead of redirecting to avoid blank page
                echo "<!DOCTYPE html><html><head><title>Fee Addition Error</title>";
                echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
                echo "</head><body><div class='container mt-4'>";

                echo "<div class='alert alert-danger'>";
                echo "<h3><i class='fas fa-exclamation-triangle'></i> ফি যোগ করতে সমস্যা হয়েছে!</h3>";
                echo "<p>" . htmlspecialchars($errorMessage) . "</p>";
                echo "</div>";

                echo "<div class='d-flex gap-2'>";
                echo "<a href='fee_management.php' class='btn btn-primary'>ফি ম্যানেজমেন্টে ফিরে যান</a>";
                echo "<a href='fee_management.php#add-fee' class='btn btn-warning'>আবার চেষ্টা করুন</a>";
                echo "</div>";

                echo "</div></body></html>";
                exit();
            }
        } else {
            // Check which fields are missing
            $errors = [];
            error_log("Validation failed for fee addition");

            if (empty($studentIds)) {
                $errors[] = 'কমপক্ষে একজন শিক্ষার্থী নির্বাচন করুন!';
                error_log("Error: No students selected");
            }

            if (empty($feeType)) {
                $errors[] = 'ফি ধরন নির্বাচন করুন!';
                error_log("Error: No fee type selected");
            }

            if ($amount <= 0) {
                $errors[] = 'সঠিক পরিমাণ প্রদান করুন!';
                error_log("Error: Invalid amount: $amount");
            }

            if (!empty($errors)) {
                $_SESSION['error'] = implode('<br>', $errors);
                error_log("Error message set: " . $_SESSION['error']);
            } else {
                $_SESSION['error'] = 'সমস্ত প্রয়োজনীয় ফিল্ড পূরণ করুন!';
                error_log("Error message set: " . $_SESSION['error']);
            }

            // Always show validation errors instead of redirecting to avoid blank page
            echo "<!DOCTYPE html><html><head><title>Validation Error</title>";
            echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
            echo "</head><body><div class='container mt-4'>";

            echo "<div class='alert alert-warning'>";
            echo "<h3><i class='fas fa-exclamation-triangle'></i> ফর্ম পূরণে সমস্যা!</h3>";
            if (!empty($errors)) {
                echo "<ul class='mb-0'>";
                foreach ($errors as $error) {
                    echo "<li>" . htmlspecialchars($error) . "</li>";
                }
                echo "</ul>";
            } else {
                echo "<p class='mb-0'>সমস্ত প্রয়োজনীয় ফিল্ড পূরণ করুন!</p>";
            }
            echo "</div>";

            echo "<div class='d-flex gap-2'>";
            echo "<a href='fee_management.php' class='btn btn-primary'>ফি ম্যানেজমেন্টে ফিরে যান</a>";
            echo "<a href='fee_management.php#add-fee' class='btn btn-warning'>আবার চেষ্টা করুন</a>";
            echo "</div>";

            echo "</div></body></html>";
            exit();
        }
    }
}

// Process fee deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_fee'])) {
    $feeId = isset($_POST['delete_fee_id']) ? intval($_POST['delete_fee_id']) : 0;

    if ($feeId > 0) {
        // Check if fee exists
        $checkFeeQuery = "SELECT f.id, f.fee_type, s.first_name, s.last_name
                         FROM fees f
                         JOIN students s ON f.student_id = s.id
                         WHERE f.id = ?";
        $stmt = $conn->prepare($checkFeeQuery);
        $stmt->bind_param('i', $feeId);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $feeData = $result->fetch_assoc();

            // Delete fee
            $deleteFeeQuery = "DELETE FROM fees WHERE id = ?";
            $stmt = $conn->prepare($deleteFeeQuery);
            $stmt->bind_param('i', $feeId);

            if ($stmt->execute()) {
                $_SESSION['success'] = $feeData['first_name'] . ' ' . $feeData['last_name'] . ' এর ' . $feeData['fee_type'] . ' ফি সফলভাবে ডিলেট করা হয়েছে।';
                error_log("Fee ID: $feeId successfully deleted");
            } else {
                $_SESSION['error'] = 'ফি ডিলেট করতে সমস্যা হয়েছে: ' . $stmt->error;
                error_log("Error deleting fee ID: $feeId - " . $stmt->error);
            }
        } else {
            $_SESSION['error'] = 'ফি রেকর্ড খুঁজে পাওয়া যায়নি!';
            error_log("Fee ID: $feeId not found");
        }

        // Don't redirect, just let the page continue loading
        // The session messages will be displayed when the page loads
    } else {
        $_SESSION['error'] = 'অবৈধ ফি আইডি!';
        error_log("Invalid fee ID for deletion: $feeId");

        // Don't redirect, just let the page continue loading
        // The session messages will be displayed when the page loads
    }
}

// Process simple inline payment (No Modal)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['simple_pay_fee'])) {
    $feeId = intval($_POST['fee_id']);
    $studentId = intval($_POST['student_id']);
    $paymentAmount = floatval($_POST['payment_amount']);
    $maxAmount = floatval($_POST['max_amount']);

    if ($feeId > 0 && $paymentAmount > 0 && $paymentAmount <= $maxAmount) {
        try {
            // Check/create payments table
            $checkPaymentsTable = $conn->query("SHOW TABLES LIKE 'payments'");
            if ($checkPaymentsTable->num_rows === 0) {
                $createPaymentsTable = "CREATE TABLE payments (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    fee_id INT NOT NULL,
                    student_id INT NOT NULL,
                    amount DECIMAL(10,2) NOT NULL,
                    payment_date DATE NOT NULL,
                    payment_method VARCHAR(50) DEFAULT 'cash',
                    receipt_no VARCHAR(100),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )";
                $conn->query($createPaymentsTable);
            }

            // Check/add paid column
            $checkPaidColumn = $conn->query("SHOW COLUMNS FROM fees LIKE 'paid'");
            if ($checkPaidColumn->num_rows === 0) {
                $conn->query("ALTER TABLE fees ADD COLUMN paid DECIMAL(10,2) DEFAULT 0");
            }

            // Check/add payment_status column
            $checkStatusColumn = $conn->query("SHOW COLUMNS FROM fees LIKE 'payment_status'");
            if ($checkStatusColumn->num_rows === 0) {
                $conn->query("ALTER TABLE fees ADD COLUMN payment_status VARCHAR(20) DEFAULT 'due'");
            }

            // Get current fee info
            $feeQuery = "SELECT f.*, CONCAT(s.first_name, ' ', s.last_name) as student_name
                        FROM fees f
                        JOIN students s ON f.student_id = s.id
                        WHERE f.id = ?";
            $stmt = $conn->prepare($feeQuery);
            $stmt->bind_param('i', $feeId);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                $fee = $result->fetch_assoc();
                $currentPaid = isset($fee['paid']) ? $fee['paid'] : 0;
                $totalAmount = $fee['amount'];
                $dueAmount = $totalAmount - $currentPaid;

                // Validate payment amount
                if ($paymentAmount > $dueAmount) {
                    throw new Exception('❌ পেমেন্ট পরিমাণ বকেয়া পরিমাণের চেয়ে বেশি হতে পারে না!\n\n📊 ফি বিবরণ:\n• মোট ফি: ৳' . number_format($totalAmount, 2) . '\n• পরিশোধিত: ৳' . number_format($currentPaid, 2) . '\n• বকেয়া: ৳' . number_format($dueAmount, 2) . '\n• আপনার পরিমাণ: ৳' . number_format($paymentAmount, 2));
                }

                // Generate receipt number
                $receiptNumber = 'RCP-' . date('Ymd') . '-' . $feeId . '-' . time();

                // Insert payment record
                $insertPayment = "INSERT INTO payments (fee_id, student_id, amount, payment_date, payment_method, receipt_no)
                                 VALUES (?, ?, ?, ?, 'cash', ?)";
                $stmt = $conn->prepare($insertPayment);
                $paymentDate = date('Y-m-d');
                $stmt->bind_param('iidss', $feeId, $studentId, $paymentAmount, $paymentDate, $receiptNumber);

                if ($stmt->execute()) {
                    // Update fee
                    $newPaidAmount = $currentPaid + $paymentAmount;
                    $newStatus = ($newPaidAmount >= $fee['amount']) ? 'paid' : 'partial';

                    $updateFee = "UPDATE fees SET paid = ?, payment_status = ? WHERE id = ?";
                    $stmt = $conn->prepare($updateFee);
                    $stmt->bind_param('dsi', $newPaidAmount, $newStatus, $feeId);

                    if ($stmt->execute()) {
                        // Success page
                        echo "<!DOCTYPE html><html><head><title>Payment Success</title>";
                        echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
                        echo "</head><body><div class='container mt-4'>";

                        echo "<div class='alert alert-success'>";
                        echo "<h3><i class='fas fa-check-circle'></i> পেমেন্ট সফল!</h3>";
                        echo "<ul class='mb-0'>";
                        echo "<li><strong>শিক্ষার্থী:</strong> " . htmlspecialchars($fee['student_name']) . "</li>";
                        echo "<li><strong>ফি ধরন:</strong> " . htmlspecialchars($fee['fee_type']) . "</li>";
                        echo "<li><strong>পেমেন্ট:</strong> ৳" . number_format($paymentAmount, 2) . "</li>";
                        echo "<li><strong>রিসিপ্ট:</strong> " . htmlspecialchars($receiptNumber) . "</li>";
                        echo "<li><strong>নতুন স্ট্যাটাস:</strong> " . ($newStatus === 'paid' ? 'সম্পূর্ণ পরিশোধিত' : 'আংশিক পরিশোধিত') . "</li>";
                        echo "</ul>";
                        echo "</div>";

                        echo "<div class='d-flex gap-2'>";
                        echo "<a href='fee_management.php' class='btn btn-primary'>ফি ম্যানেজমেন্টে ফিরে যান</a>";
                        echo "<a href='simple-payment.php' class='btn btn-success'>আরো পেমেন্ট করুন</a>";
                        echo "</div>";

                        echo "</div></body></html>";
                        exit();
                    } else {
                        throw new Exception('ফি আপডেট করতে সমস্যা: ' . $stmt->error);
                    }
                } else {
                    throw new Exception('পেমেন্ট রেকর্ড সংরক্ষণ করতে সমস্যা: ' . $stmt->error);
                }
            } else {
                throw new Exception('ফি রেকর্ড খুঁজে পাওয়া যায়নি!');
            }

        } catch (Exception $e) {
            echo "<!DOCTYPE html><html><head><title>Payment Error</title>";
            echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
            echo "</head><body><div class='container mt-4'>";

            echo "<div class='alert alert-danger'>";
            echo "<h3><i class='fas fa-exclamation-triangle'></i> পেমেন্ট করতে সমস্যা!</h3>";
            echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";

            echo "<a href='fee_management.php' class='btn btn-primary'>ফি ম্যানেজমেন্টে ফিরে যান</a>";

            echo "</div></body></html>";
            exit();
        }
    } else {
        echo "<!DOCTYPE html><html><head><title>Payment Error</title>";
        echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
        echo "</head><body><div class='container mt-4'>";

        echo "<div class='alert alert-warning'>";
        echo "<h3><i class='fas fa-exclamation-triangle'></i> অবৈধ পেমেন্ট পরিমাণ!</h3>";
        echo "<p>সঠিক পরিমাণ প্রদান করুন।</p>";
        echo "</div>";

        echo "<a href='fee_management.php' class='btn btn-primary'>ফি ম্যানেজমেন্টে ফিরে যান</a>";

        echo "</div></body></html>";
        exit();
    }
}

// Process single fee payment from modal
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['pay_fee'])) {
    error_log("Payment form submitted: " . print_r($_POST, true));
    $feeId = intval($_POST['fee_id']);
    $paymentAmount = floatval($_POST['payment_amount']);
    $paymentMethod = $_POST['payment_method'];
    $paymentDate = $_POST['payment_date'];
    $receiptNumber = $_POST['receipt_no'] ?? $_POST['receipt_number'] ?? '';
    $transactionId = $_POST['transaction_id'] ?? '';
    $referenceNumber = $_POST['reference_number'] ?? '';
    $paymentNotes = $_POST['payment_notes'] ?? '';

    if ($feeId > 0 && $paymentAmount > 0) {
        try {
            // Check if payments table exists
            $checkPaymentsTable = $conn->query("SHOW TABLES LIKE 'payments'");
            if ($checkPaymentsTable->num_rows === 0) {
                // Create payments table
                $createPaymentsTable = "CREATE TABLE IF NOT EXISTS payments (
                    id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
                    fee_id INT(11) NOT NULL,
                    student_id INT(11) NOT NULL,
                    amount DECIMAL(10,2) NOT NULL,
                    payment_date DATE NOT NULL,
                    payment_method VARCHAR(50) DEFAULT 'cash',
                    receipt_no VARCHAR(100),
                    transaction_id VARCHAR(100),
                    reference_number VARCHAR(100),
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )";

                if (!$conn->query($createPaymentsTable)) {
                    throw new Exception('Payments table তৈরি করতে সমস্যা: ' . $conn->error);
                }
                error_log("Payments table created successfully");
            }

            $conn->begin_transaction();

            // Get current fee details
            $feeQuery = "SELECT f.*, CONCAT(s.first_name, ' ', s.last_name) as student_name
                        FROM fees f
                        JOIN students s ON f.student_id = s.id
                        WHERE f.id = ?";
            $stmt = $conn->prepare($feeQuery);
            $stmt->bind_param('i', $feeId);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                $fee = $result->fetch_assoc();
                $dueAmount = $fee['amount'] - $fee['paid'];

                // Validate payment amount
                if ($paymentAmount > $dueAmount) {
                    throw new Exception('পেমেন্ট পরিমাণ বকেয়া পরিমাণের চেয়ে বেশি হতে পারে না!');
                }

                // Generate receipt number if not provided
                if (empty($receiptNumber)) {
                    $receiptNumber = 'RCP-' . date('Ymd') . '-' . str_pad($feeId, 4, '0', STR_PAD_LEFT) . '-' . time();
                }

                // Insert payment record
                $paymentQuery = "INSERT INTO payments (fee_id, student_id, amount, payment_date, payment_method,
                                receipt_no, transaction_id, reference_number, notes, created_at)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
                $stmt = $conn->prepare($paymentQuery);
                $stmt->bind_param('iidsssss', $feeId, $fee['student_id'], $paymentAmount, $paymentDate,
                                 $paymentMethod, $receiptNumber, $transactionId, $referenceNumber, $paymentNotes);

                if (!$stmt->execute()) {
                    throw new Exception('পেমেন্ট রেকর্ড সংরক্ষণ করতে সমস্যা: ' . $stmt->error);
                }

                // Update fee paid amount
                $newPaidAmount = $fee['paid'] + $paymentAmount;
                $newStatus = ($newPaidAmount >= $fee['amount']) ? 'paid' : 'partial';

                $updateFeeQuery = "UPDATE fees SET paid = ?, payment_status = ?, updated_at = NOW() WHERE id = ?";
                $stmt = $conn->prepare($updateFeeQuery);
                $stmt->bind_param('dsi', $newPaidAmount, $newStatus, $feeId);

                if (!$stmt->execute()) {
                    throw new Exception('ফি আপডেট করতে সমস্যা: ' . $stmt->error);
                }

                $conn->commit();

                // Success message
                echo "<!DOCTYPE html><html><head><title>Payment Success</title>";
                echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
                echo "</head><body><div class='container mt-4'>";

                echo "<div class='alert alert-success'>";
                echo "<h3><i class='fas fa-check-circle'></i> পেমেন্ট সফল!</h3>";
                echo "<ul class='mb-0'>";
                echo "<li><strong>শিক্ষার্থী:</strong> " . htmlspecialchars($fee['student_name']) . "</li>";
                echo "<li><strong>ফি ধরন:</strong> " . htmlspecialchars($fee['fee_type']) . "</li>";
                echo "<li><strong>পেমেন্ট পরিমাণ:</strong> ৳" . number_format($paymentAmount, 2) . "</li>";
                echo "<li><strong>রিসিপ্ট নম্বর:</strong> " . htmlspecialchars($receiptNumber) . "</li>";
                echo "<li><strong>নতুন স্ট্যাটাস:</strong> " . ($newStatus === 'paid' ? 'পরিশোধিত' : 'আংশিক') . "</li>";
                echo "</ul>";
                echo "</div>";

                echo "<div class='d-flex gap-2'>";
                echo "<a href='fee_management.php' class='btn btn-primary'>ফি ম্যানেজমেন্টে ফিরে যান</a>";
                echo "<a href='student_payment_receipt.php?student_id=" . $fee['student_id'] . "' class='btn btn-success'>রিসিপ্ট প্রিন্ট করুন</a>";
                echo "</div>";

                echo "</div></body></html>";
                exit();

            } else {
                throw new Exception('ফি রেকর্ড খুঁজে পাওয়া যায়নি!');
            }

        } catch (Exception $e) {
            $conn->rollback();

            echo "<!DOCTYPE html><html><head><title>Payment Error</title>";
            echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
            echo "</head><body><div class='container mt-4'>";

            echo "<div class='alert alert-danger'>";
            echo "<h3><i class='fas fa-exclamation-triangle'></i> পেমেন্ট করতে সমস্যা!</h3>";
            echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";

            echo "<div class='d-flex gap-2'>";
            echo "<a href='fee_management.php' class='btn btn-primary'>ফি ম্যানেজমেন্টে ফিরে যান</a>";
            echo "<a href='fee_management.php#fee-list' class='btn btn-warning'>আবার চেষ্টা করুন</a>";
            echo "</div>";

            echo "</div></body></html>";
            exit();
        }
    } else {
        echo "<!DOCTYPE html><html><head><title>Payment Error</title>";
        echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
        echo "</head><body><div class='container mt-4'>";

        echo "<div class='alert alert-warning'>";
        echo "<h3><i class='fas fa-exclamation-triangle'></i> অবৈধ তথ্য!</h3>";
        echo "<p>সঠিক ফি আইডি এবং পেমেন্ট পরিমাণ প্রদান করুন।</p>";
        echo "</div>";

        echo "<a href='fee_management.php' class='btn btn-primary'>ফি ম্যানেজমেন্টে ফিরে যান</a>";

        echo "</div></body></html>";
        exit();
    }
}

// Process fee edit
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['edit_fee'])) {
    $feeId = intval($_POST['edit_fee_id']);
    $feeType = $_POST['edit_fee_type'];
    $amount = floatval($_POST['edit_amount']);
    $dueDate = $_POST['edit_due_date'];

    if ($feeId > 0 && !empty($feeType) && $amount > 0) {
        try {
            // Get current fee details
            $feeQuery = "SELECT f.*, CONCAT(s.first_name, ' ', s.last_name) as student_name
                        FROM fees f
                        JOIN students s ON f.student_id = s.id
                        WHERE f.id = ?";
            $stmt = $conn->prepare($feeQuery);
            $stmt->bind_param('i', $feeId);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                $fee = $result->fetch_assoc();

                // Check if new amount is less than already paid
                if ($amount < $fee['paid']) {
                    throw new Exception('নতুন পরিমাণ ইতিমধ্যে পরিশোধিত পরিমাণের চেয়ে কম হতে পারে না!');
                }

                // Update fee
                $updateQuery = "UPDATE fees SET fee_type = ?, amount = ?, due_date = ?, updated_at = NOW() WHERE id = ?";
                $stmt = $conn->prepare($updateQuery);
                $stmt->bind_param('sdsi', $feeType, $amount, $dueDate, $feeId);

                if ($stmt->execute()) {
                    // Update payment status
                    $newStatus = ($fee['paid'] >= $amount) ? 'paid' : (($fee['paid'] > 0) ? 'partial' : 'due');
                    $statusQuery = "UPDATE fees SET payment_status = ? WHERE id = ?";
                    $stmt = $conn->prepare($statusQuery);
                    $stmt->bind_param('si', $newStatus, $feeId);
                    $stmt->execute();

                    echo "<!DOCTYPE html><html><head><title>Fee Updated</title>";
                    echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
                    echo "</head><body><div class='container mt-4'>";

                    echo "<div class='alert alert-success'>";
                    echo "<h3><i class='fas fa-check-circle'></i> ফি সফলভাবে আপডেট করা হয়েছে!</h3>";
                    echo "<ul class='mb-0'>";
                    echo "<li><strong>শিক্ষার্থী:</strong> " . htmlspecialchars($fee['student_name']) . "</li>";
                    echo "<li><strong>ফি ধরন:</strong> " . htmlspecialchars($feeType) . "</li>";
                    echo "<li><strong>নতুন পরিমাণ:</strong> ৳" . number_format($amount, 2) . "</li>";
                    echo "<li><strong>নির্ধারিত তারিখ:</strong> " . date('d/m/Y', strtotime($dueDate)) . "</li>";
                    echo "</ul>";
                    echo "</div>";

                    echo "<a href='fee_management.php' class='btn btn-primary'>ফি ম্যানেজমেন্টে ফিরে যান</a>";

                    echo "</div></body></html>";
                    exit();
                } else {
                    throw new Exception('ফি আপডেট করতে সমস্যা: ' . $stmt->error);
                }
            } else {
                throw new Exception('ফি রেকর্ড খুঁজে পাওয়া যায়নি!');
            }

        } catch (Exception $e) {
            echo "<!DOCTYPE html><html><head><title>Update Error</title>";
            echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
            echo "</head><body><div class='container mt-4'>";

            echo "<div class='alert alert-danger'>";
            echo "<h3><i class='fas fa-exclamation-triangle'></i> ফি আপডেট করতে সমস্যা!</h3>";
            echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";

            echo "<a href='fee_management.php' class='btn btn-primary'>ফি ম্যানেজমেন্টে ফিরে যান</a>";

            echo "</div></body></html>";
            exit();
        }
    }
}

// Process payment for a single fee
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_payment'])) {
    $feeId = $_POST['fee_id'] ?? 0;
    $paymentAmount = $_POST['payment_amount'] ?? 0;
    $paymentDate = $_POST['payment_date'] ?? date('Y-m-d');
    $paymentMethod = $_POST['payment_method'] ?? 'cash';
    $receiptNo = $_POST['receipt_no'] ?? '';
    $notes = $_POST['notes'] ?? '';

    if ($feeId > 0 && $paymentAmount > 0) {
        // Get current fee details
        $feeQuery = "SELECT * FROM fees WHERE id = ?";
        $stmt = $conn->prepare($feeQuery);
        $stmt->bind_param('i', $feeId);
        $stmt->execute();
        $feeResult = $stmt->get_result();

        if ($feeResult->num_rows > 0) {
            $fee = $feeResult->fetch_assoc();
            $currentPaid = $fee['paid'];
            $totalAmount = $fee['amount'];
            $dueAmount = $totalAmount - $currentPaid;

            // Validate payment amount
            if ($paymentAmount > $dueAmount) {
                throw new Exception('❌ পেমেন্ট পরিমাণ বকেয়া পরিমাণের চেয়ে বেশি হতে পারে না!\n\n📊 ফি বিবরণ:\n• মোট ফি: ৳' . number_format($totalAmount, 2) . '\n• পরিশোধিত: ৳' . number_format($currentPaid, 2) . '\n• বকেয়া: ৳' . number_format($dueAmount, 2) . '\n• আপনার পরিমাণ: ৳' . number_format($paymentAmount, 2));
            }

            $newPaidAmount = $currentPaid + $paymentAmount;

            // Determine payment status
            $paymentStatus = 'due';
            if ($newPaidAmount >= $fee['amount']) {
                $paymentStatus = 'paid';
            } else if ($newPaidAmount > 0) {
                $paymentStatus = 'partial';
            }

            // Start transaction
            $conn->begin_transaction();

            try {
                // Update fee record
                $updateFeeQuery = "UPDATE fees SET paid = ?, payment_status = ?, payment_date = ? WHERE id = ?";
                $stmt = $conn->prepare($updateFeeQuery);
                $stmt->bind_param('dssi', $newPaidAmount, $paymentStatus, $paymentDate, $feeId);
                $stmt->execute();

                // Check if fee_payments table exists
                $checkTableQuery = "SHOW TABLES LIKE 'fee_payments'";
                $tableResult = $conn->query($checkTableQuery);

                if ($tableResult->num_rows === 0) {
                    // Create fee_payments table
                    $createTableQuery = "CREATE TABLE IF NOT EXISTS fee_payments (
                        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
                        fee_id INT(11) NOT NULL,
                        receipt_no VARCHAR(50),
                        amount DECIMAL(10,2) NOT NULL,
                        payment_date DATE NOT NULL,
                        payment_method VARCHAR(50) NOT NULL DEFAULT 'cash',
                        notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (fee_id) REFERENCES fees(id) ON DELETE CASCADE
                    )";
                    $conn->query($createTableQuery);
                }

                // Generate a unique receipt number if not provided
                if (empty($receiptNo)) {
                    $receiptNo = 'RCPT-' . date('Ymd') . '-' . rand(1000, 9999);
                }

                // Add payment record
                $paymentQuery = "INSERT INTO fee_payments (fee_id, receipt_no, amount, payment_date, payment_method, notes)
                               VALUES (?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($paymentQuery);
                $stmt->bind_param('isdsss', $feeId, $receiptNo, $paymentAmount, $paymentDate, $paymentMethod, $notes);
                $stmt->execute();

                // Commit transaction
                $conn->commit();

                $_SESSION['success'] = 'পেমেন্ট সফলভাবে যোগ করা হয়েছে!';
            } catch (Exception $e) {
                // Roll back transaction on error
                $conn->rollback();
                $_SESSION['error'] = 'পেমেন্ট যোগ করতে সমস্যা: ' . $e->getMessage();
            }
        } else {
            $_SESSION['error'] = 'ফি রেকর্ড খুঁজে পাওয়া যায়নি!';
        }
    } else {
        $_SESSION['error'] = 'সমস্ত প্রয়োজনীয় ফিল্ড পূরণ করুন!';
    }
}

// Process payment for multiple fees
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_multiple_payments'])) {
    $studentId = $_POST['student_id'] ?? 0;
    $feeIds = $_POST['fee_ids'] ?? [];
    $paymentAmounts = $_POST['payment_amounts'] ?? [];
    $paymentDate = $_POST['multi_payment_date'] ?? date('Y-m-d');
    $paymentMethod = $_POST['multi_payment_method'] ?? 'cash';
    $receiptNo = $_POST['multi_receipt_no'] ?? '';
    $notes = $_POST['multi_notes'] ?? '';

    // Generate a unique receipt number if not provided
    if (empty($receiptNo)) {
        $receiptNo = 'RCPT-' . date('Ymd') . '-' . rand(1000, 9999);
    }

    if ($studentId > 0 && !empty($feeIds) && !empty($paymentAmounts)) {
        // Start transaction
        $conn->begin_transaction();

        try {
            $successCount = 0;
            $totalPaid = 0;

            // Check if fee_payments table exists
            $checkTableQuery = "SHOW TABLES LIKE 'fee_payments'";
            $tableResult = $conn->query($checkTableQuery);

            if ($tableResult->num_rows === 0) {
                // Create fee_payments table
                $createTableQuery = "CREATE TABLE IF NOT EXISTS fee_payments (
                    id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
                    fee_id INT(11) NOT NULL,
                    receipt_no VARCHAR(50),
                    amount DECIMAL(10,2) NOT NULL,
                    payment_date DATE NOT NULL,
                    payment_method VARCHAR(50) NOT NULL DEFAULT 'cash',
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (fee_id) REFERENCES fees(id) ON DELETE CASCADE
                )";
                $conn->query($createTableQuery);
            }

            // Prepare statements
            $getFeeQuery = "SELECT * FROM fees WHERE id = ?";
            $getFeeStmt = $conn->prepare($getFeeQuery);

            $updateFeeQuery = "UPDATE fees SET paid = ?, payment_status = ?, payment_date = ? WHERE id = ?";
            $updateFeeStmt = $conn->prepare($updateFeeQuery);

            $addPaymentQuery = "INSERT INTO fee_payments (fee_id, receipt_no, amount, payment_date, payment_method, notes)
                               VALUES (?, ?, ?, ?, ?, ?)";
            $addPaymentStmt = $conn->prepare($addPaymentQuery);

            // Process each fee
            foreach ($feeIds as $index => $feeId) {
                $paymentAmount = isset($paymentAmounts[$index]) ? floatval($paymentAmounts[$index]) : 0;

                if ($paymentAmount <= 0) {
                    continue; // Skip if payment amount is zero or negative
                }

                // Get current fee details
                $getFeeStmt->bind_param('i', $feeId);
                $getFeeStmt->execute();
                $feeResult = $getFeeStmt->get_result();

                if ($feeResult->num_rows > 0) {
                    $fee = $feeResult->fetch_assoc();
                    $currentPaid = $fee['paid'];
                    $totalAmount = $fee['amount'];
                    $dueAmount = $totalAmount - $currentPaid;

                    // Validate payment amount
                    if ($paymentAmount > $dueAmount) {
                        throw new Exception('❌ ফি ID ' . $feeId . ' (' . $fee['fee_type'] . ') এর জন্য পেমেন্ট পরিমাণ বকেয়া পরিমাণের চেয়ে বেশি হতে পারে না!\n\n📊 ফি বিবরণ:\n• মোট ফি: ৳' . number_format($totalAmount, 2) . '\n• পরিশোধিত: ৳' . number_format($currentPaid, 2) . '\n• বকেয়া: ৳' . number_format($dueAmount, 2) . '\n• আপনার পরিমাণ: ৳' . number_format($paymentAmount, 2));
                    }

                    $newPaidAmount = $currentPaid + $paymentAmount;

                    // Determine payment status
                    $paymentStatus = 'due';
                    if ($newPaidAmount >= $fee['amount']) {
                        $paymentStatus = 'paid';
                    } else if ($newPaidAmount > 0) {
                        $paymentStatus = 'partial';
                    }

                    // Update fee record
                    $updateFeeStmt->bind_param('dssi', $newPaidAmount, $paymentStatus, $paymentDate, $feeId);
                    $updateFeeStmt->execute();

                    // Add payment record
                    $addPaymentStmt->bind_param('isdsss', $feeId, $receiptNo, $paymentAmount, $paymentDate, $paymentMethod, $notes);
                    $addPaymentStmt->execute();

                    $successCount++;
                    $totalPaid += $paymentAmount;
                }
            }

            // Commit transaction
            $conn->commit();

            if ($successCount > 0) {
                $_SESSION['success'] = $successCount . ' টি ফি এর জন্য মোট ৳' . number_format($totalPaid, 2) . ' পেমেন্ট সফলভাবে যোগ করা হয়েছে!';
            } else {
                $_SESSION['warning'] = 'কোন পেমেন্ট যোগ করা হয়নি।';
            }
        } catch (Exception $e) {
            // Roll back transaction on error
            $conn->rollback();
            $_SESSION['error'] = 'পেমেন্ট যোগ করতে সমস্যা: ' . $e->getMessage();
        }
    } else {
        $_SESSION['error'] = 'সমস্ত প্রয়োজনীয় ফিল্ড পূরণ করুন!';
    }
}





// Get all sessions for dropdown
$sessionsQuery = "SELECT * FROM sessions ORDER BY session_name DESC";
$sessionsResult = $conn->query($sessionsQuery);

// Get all classes for dropdown
$classesQuery = "SELECT * FROM classes ORDER BY class_name";
$classesResult = $conn->query($classesQuery);

// Get all students for dropdown
$studentsQuery = "SELECT s.id, s.first_name, s.last_name, s.student_id, c.class_name, ss.session_name, d.department_name
                 FROM students s
                 LEFT JOIN classes c ON s.class_id = c.id
                 LEFT JOIN sessions ss ON s.session_id = ss.id
                 LEFT JOIN departments d ON s.department_id = d.id
                 ORDER BY s.first_name, s.last_name";
$studentsResult = $conn->query($studentsQuery);

// Get all fee types for dropdown
$feeTypesQuery = "SELECT * FROM fee_types ORDER BY name";
$feeTypesResult = $conn->query($feeTypesQuery);

// Get all fee categories for dropdown
$categoriesQuery = "SELECT * FROM fee_categories ORDER BY name";
$categoriesResult = $conn->query($categoriesQuery);

// Initialize search parameters
$searchTerm = $_GET['search'] ?? '';
$feeType = $_GET['fee_type'] ?? '';
$paymentStatus = $_GET['payment_status'] ?? '';
$classId = $_GET['class_id'] ?? '';
$sessionId = $_GET['session_id'] ?? '';
$departmentId = $_GET['department_id'] ?? '';
$studentId = $_GET['student_id'] ?? '';
$fromDate = $_GET['from_date'] ?? '';
$toDate = $_GET['to_date'] ?? '';
$minAmount = $_GET['min_amount'] ?? '';
$maxAmount = $_GET['max_amount'] ?? '';
$export = $_GET['export'] ?? '';
$categoryId = $_GET['category_id'] ?? '';
$receiptNo = $_GET['receipt_no'] ?? '';
$sortBy = $_GET['sort_by'] ?? 'due_date_desc';
$perPage = $_GET['per_page'] ?? '10';
$feeCurrentPage = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;

// Initialize payment search parameters
$paymentSearchTerm = $_GET['payment_search'] ?? '';
$paymentFeeType = $_GET['payment_fee_type'] ?? '';
$paymentMethod = $_GET['payment_method'] ?? '';
$paymentClassId = $_GET['payment_class_id'] ?? '';
$paymentSessionId = $_GET['payment_session_id'] ?? '';
$paymentDepartmentId = $_GET['payment_department_id'] ?? '';
$paymentStudentId = $_GET['payment_student_id'] ?? '';
$paymentFromDate = $_GET['payment_from_date'] ?? '';
$paymentToDate = $_GET['payment_to_date'] ?? '';
$paymentMinAmount = $_GET['payment_min_amount'] ?? '';
$paymentMaxAmount = $_GET['payment_max_amount'] ?? '';
$paymentReceiptNo = $_GET['payment_receipt_no'] ?? '';
$paymentSortBy = $_GET['payment_sort_by'] ?? 'payment_date_desc';
$paymentPerPage = $_GET['payment_per_page'] ?? '10';

// Handle Export Functionality
if (!empty($export)) {
    error_log("🔍 Export requested: " . $export);
    $exportData = [];
    $filename = 'fee_report_' . date('Y-m-d_H-i-s');

    // Set appropriate headers based on export type
    switch ($export) {
        case 'csv':
            error_log("📄 Setting CSV headers");
            header('Content-Type: text/csv; charset=utf-8');
            header('Content-Disposition: attachment; filename=' . $filename . '.csv');
            $output = fopen('php://output', 'w');
            fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF)); // UTF-8 BOM
            break;

        case 'excel':
            error_log("📊 Setting Excel headers");
            header('Content-Type: application/vnd.ms-excel; charset=utf-8');
            header('Content-Disposition: attachment; filename=' . $filename . '.xls');
            $output = fopen('php://output', 'w');
            fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF)); // UTF-8 BOM
            break;

        case 'pdf':
            error_log("📋 PDF export requested");
            // PDF will be handled after data collection
            break;

        case 'print':
            error_log("🖨️ Print export requested");
            // Print view will be handled after data collection
            break;

        default:
            error_log("❌ Unknown export format: " . $export);
            break;
    }

    // Add headers for CSV/Excel
    if ($export === 'csv' || $export === 'excel') {
        error_log("📝 Adding CSV/Excel headers");
        fputcsv($output, [
            'আইডি', 'শিক্ষার্থী', 'রোল', 'শ্রেণী', 'সেশন', 'ফি ধরন', 'পরিমাণ',
            'পরিশোধিত', 'বকেয়া', 'তারিখ', 'স্ট্যাটাস'
        ]);
    }
}

// Build the query with search filters
$feesQuery = "SELECT f.*, s.first_name, s.last_name, s.student_id as roll, c.class_name, s.class_id,
             ss.session_name, s.session_id, d.department_name, s.department_id
             FROM fees f
             JOIN students s ON f.student_id = s.id
             LEFT JOIN classes c ON s.class_id = c.id
             LEFT JOIN sessions ss ON s.session_id = ss.id
             LEFT JOIN departments d ON s.department_id = d.id
             WHERE 1=1";

$queryParams = [];
$paramTypes = "";

// Add search filters
if (!empty($searchTerm)) {
    $feesQuery .= " AND (s.first_name LIKE ? OR s.last_name LIKE ? OR s.student_id LIKE ? OR CONCAT(s.first_name, ' ', s.last_name) LIKE ?)";
    $searchTermWithWildcards = "%$searchTerm%";
    $queryParams[] = $searchTermWithWildcards;
    $queryParams[] = $searchTermWithWildcards;
    $queryParams[] = $searchTermWithWildcards;
    $queryParams[] = $searchTermWithWildcards;
    $paramTypes .= "ssss";
}

if (!empty($feeType)) {
    $feesQuery .= " AND f.fee_type = ?";
    $queryParams[] = $feeType;
    $paramTypes .= "s";
}

if (!empty($paymentStatus)) {
    $feesQuery .= " AND f.payment_status = ?";
    $queryParams[] = $paymentStatus;
    $paramTypes .= "s";
}

if (!empty($classId)) {
    $feesQuery .= " AND s.class_id = ?";
    $queryParams[] = $classId;
    $paramTypes .= "i";
}

if (!empty($sessionId)) {
    $feesQuery .= " AND s.session_id = ?";
    $queryParams[] = $sessionId;
    $paramTypes .= "i";
}

if (!empty($departmentId)) {
    $feesQuery .= " AND s.department_id = ?";
    $queryParams[] = $departmentId;
    $paramTypes .= "i";
}

if (!empty($studentId)) {
    $feesQuery .= " AND s.id = ?";
    $queryParams[] = $studentId;
    $paramTypes .= "i";
}

if (!empty($fromDate)) {
    $feesQuery .= " AND f.due_date >= ?";
    $queryParams[] = $fromDate;
    $paramTypes .= "s";
}

if (!empty($toDate)) {
    $feesQuery .= " AND f.due_date <= ?";
    $queryParams[] = $toDate;
    $paramTypes .= "s";
}

if (!empty($minAmount)) {
    $feesQuery .= " AND f.amount >= ?";
    $queryParams[] = $minAmount;
    $paramTypes .= "d";
}

if (!empty($maxAmount)) {
    $feesQuery .= " AND f.amount <= ?";
    $queryParams[] = $maxAmount;
    $paramTypes .= "d";
}

if (!empty($categoryId)) {
    $feesQuery .= " AND f.category_id = ?";
    $queryParams[] = $categoryId;
    $paramTypes .= "i";
}

if (!empty($receiptNo)) {
    $feesQuery .= " AND EXISTS (SELECT 1 FROM fee_payments fp WHERE fp.fee_id = f.id AND fp.receipt_no LIKE ?)";
    $queryParams[] = "%$receiptNo%";
    $paramTypes .= "s";
}

// Add order by clause based on sort parameter
switch ($sortBy) {
    case 'due_date_asc':
        $feesQuery .= " ORDER BY f.due_date ASC";
        break;
    case 'amount_desc':
        $feesQuery .= " ORDER BY f.amount DESC";
        break;
    case 'amount_asc':
        $feesQuery .= " ORDER BY f.amount ASC";
        break;
    case 'student_name':
        $feesQuery .= " ORDER BY s.first_name ASC, s.last_name ASC";
        break;
    case 'due_date_desc':
    default:
        $feesQuery .= " ORDER BY f.due_date DESC";
        break;
}

// First, let's get total count for pagination
$countQuery = "SELECT COUNT(*) FROM fees f
               JOIN students s ON f.student_id = s.id
               LEFT JOIN classes c ON s.class_id = c.id
               LEFT JOIN sessions ss ON s.session_id = ss.id
               LEFT JOIN departments d ON s.department_id = d.id
               WHERE 1=1";

// Add the same filters to count query
$countParams = [];
$countParamTypes = "";

if (!empty($searchTerm)) {
    $countQuery .= " AND (s.first_name LIKE ? OR s.last_name LIKE ? OR s.student_id LIKE ? OR CONCAT(s.first_name, ' ', s.last_name) LIKE ?)";
    $searchTermWithWildcards = "%$searchTerm%";
    $countParams[] = $searchTermWithWildcards;
    $countParams[] = $searchTermWithWildcards;
    $countParams[] = $searchTermWithWildcards;
    $countParams[] = $searchTermWithWildcards;
    $countParamTypes .= "ssss";
}

if (!empty($feeType)) {
    $countQuery .= " AND f.fee_type = ?";
    $countParams[] = $feeType;
    $countParamTypes .= "s";
}

if (!empty($paymentStatus)) {
    $countQuery .= " AND f.payment_status = ?";
    $countParams[] = $paymentStatus;
    $countParamTypes .= "s";
}

if (!empty($classId)) {
    $countQuery .= " AND s.class_id = ?";
    $countParams[] = $classId;
    $countParamTypes .= "i";
}

if (!empty($sessionId)) {
    $countQuery .= " AND s.session_id = ?";
    $countParams[] = $sessionId;
    $countParamTypes .= "i";
}

if (!empty($departmentId)) {
    $countQuery .= " AND s.department_id = ?";
    $countParams[] = $departmentId;
    $countParamTypes .= "i";
}

if (!empty($studentId)) {
    $countQuery .= " AND s.id = ?";
    $countParams[] = $studentId;
    $countParamTypes .= "i";
}

if (!empty($fromDate)) {
    $countQuery .= " AND f.due_date >= ?";
    $countParams[] = $fromDate;
    $countParamTypes .= "s";
}

if (!empty($toDate)) {
    $countQuery .= " AND f.due_date <= ?";
    $countParams[] = $toDate;
    $countParamTypes .= "s";
}

if (!empty($minAmount)) {
    $countQuery .= " AND f.amount >= ?";
    $countParams[] = $minAmount;
    $countParamTypes .= "d";
}

if (!empty($maxAmount)) {
    $countQuery .= " AND f.amount <= ?";
    $countParams[] = $maxAmount;
    $countParamTypes .= "d";
}

if (!empty($categoryId)) {
    $countQuery .= " AND f.category_id = ?";
    $countParams[] = $categoryId;
    $countParamTypes .= "i";
}

if (!empty($receiptNo)) {
    $countQuery .= " AND EXISTS (SELECT 1 FROM fee_payments fp WHERE fp.fee_id = f.id AND fp.receipt_no LIKE ?)";
    $countParams[] = "%$receiptNo%";
    $countParamTypes .= "s";
}

$countStmt = $conn->prepare($countQuery);
if (!empty($countParams)) {
    $countStmt->bind_param($countParamTypes, ...$countParams);
}
$countStmt->execute();
$totalRecords = $countStmt->get_result()->fetch_row()[0];

// Apply pagination unless 'all' is selected
if ($perPage !== 'all') {
    $limit = intval($perPage);
    if ($limit <= 0) $limit = 10; // Default to 10 if invalid

    $totalPages = $totalRecords > 0 ? ceil($totalRecords / $limit) : 1;
    $feeCurrentPage = min($feeCurrentPage, max(1, $totalPages)); // Ensure current page doesn't exceed total pages
    $offset = ($feeCurrentPage - 1) * $limit;

    $feesQuery .= " LIMIT $limit OFFSET $offset";
} else {
    $totalPages = 1;
}

// Prepare and execute the query
$stmt = $conn->prepare($feesQuery);
if (!empty($queryParams)) {
    $stmt->bind_param($paramTypes, ...$queryParams);
}
$stmt->execute();
$feesResult = $stmt->get_result();

// Handle Export Data Output
if (!empty($export)) {
    error_log("📊 Processing export data for format: " . $export);
    $exportData = [];
    $rowCount = 0;

    while ($fee = $feesResult->fetch_assoc()) {
        $rowCount++;
        $dueAmount = $fee['amount'] - $fee['paid'];
        $statusText = $fee['payment_status'] === 'due' ? 'বকেয়া' : ($fee['payment_status'] === 'partial' ? 'আংশিক' : 'পরিশোধিত');

        $rowData = [
            $fee['id'],
            $fee['first_name'] . ' ' . $fee['last_name'],
            $fee['roll'],
            $fee['class_name'] ?? 'N/A',
            $fee['session_name'] ?? 'N/A',
            $fee['fee_type'],
            $fee['amount'],
            $fee['paid'],
            $dueAmount,
            date('d/m/Y', strtotime($fee['due_date'])),
            $statusText
        ];

        // Output for CSV/Excel
        if ($export === 'csv' || $export === 'excel') {
            fputcsv($output, $rowData);
        }

        // Store for PDF/Print
        if ($export === 'pdf' || $export === 'print') {
            $exportData[] = $fee;
        }
    }

    error_log("📈 Processed " . $rowCount . " rows for export");

    // Handle CSV/Excel completion
    if ($export === 'csv' || $export === 'excel') {
        error_log("✅ Completing CSV/Excel export");
        fclose($output);
        exit;
    }

    // Handle PDF Export
    if ($export === 'pdf') {
        error_log("📋 Generating PDF content");
        // Generate PDF content
        $html = generatePdfContent($exportData);

        // Set PDF headers
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: attachment; filename=' . $filename . '.html');

        // Output HTML (can be enhanced with PDF libraries)
        echo $html;
        exit;
    }

    // Handle Print View
    if ($export === 'print') {
        error_log("🖨️ Generating print content");
        $html = generatePrintContent($exportData);
        echo $html;
        exit;
    }
}

// Build the payments query with search filters
$paymentsQuery = "SELECT p.*, f.fee_type, s.first_name, s.last_name, s.student_id as roll,
                 c.class_name, s.class_id, ss.session_name, s.session_id
                 FROM fee_payments p
                 JOIN fees f ON p.fee_id = f.id
                 JOIN students s ON f.student_id = s.id
                 LEFT JOIN classes c ON s.class_id = c.id
                 LEFT JOIN sessions ss ON s.session_id = ss.id
                 WHERE 1=1";

$paymentQueryParams = [];
$paymentParamTypes = "";

// Add payment search filters
if (!empty($paymentSearchTerm)) {
    $paymentsQuery .= " AND (s.first_name LIKE ? OR s.last_name LIKE ? OR s.student_id LIKE ? OR CONCAT(s.first_name, ' ', s.last_name) LIKE ?)";
    $paymentSearchTermWithWildcards = "%$paymentSearchTerm%";
    $paymentQueryParams[] = $paymentSearchTermWithWildcards;
    $paymentQueryParams[] = $paymentSearchTermWithWildcards;
    $paymentQueryParams[] = $paymentSearchTermWithWildcards;
    $paymentQueryParams[] = $paymentSearchTermWithWildcards;
    $paymentParamTypes .= "ssss";
}

if (!empty($paymentFeeType)) {
    $paymentsQuery .= " AND f.fee_type = ?";
    $paymentQueryParams[] = $paymentFeeType;
    $paymentParamTypes .= "s";
}

if (!empty($paymentMethod)) {
    $paymentsQuery .= " AND p.payment_method = ?";
    $paymentQueryParams[] = $paymentMethod;
    $paymentParamTypes .= "s";
}

if (!empty($paymentClassId)) {
    $paymentsQuery .= " AND s.class_id = ?";
    $paymentQueryParams[] = $paymentClassId;
    $paymentParamTypes .= "i";
}

if (!empty($paymentSessionId)) {
    $paymentsQuery .= " AND s.session_id = ?";
    $paymentQueryParams[] = $paymentSessionId;
    $paymentParamTypes .= "i";
}

if (!empty($paymentFromDate)) {
    $paymentsQuery .= " AND p.payment_date >= ?";
    $paymentQueryParams[] = $paymentFromDate;
    $paymentParamTypes .= "s";
}

if (!empty($paymentToDate)) {
    $paymentsQuery .= " AND p.payment_date <= ?";
    $paymentQueryParams[] = $paymentToDate;
    $paymentParamTypes .= "s";
}

if (!empty($paymentDepartmentId)) {
    $paymentsQuery .= " AND s.department_id = ?";
    $paymentQueryParams[] = $paymentDepartmentId;
    $paymentParamTypes .= "i";
}

if (!empty($paymentStudentId)) {
    $paymentsQuery .= " AND s.id = ?";
    $paymentQueryParams[] = $paymentStudentId;
    $paymentParamTypes .= "i";
}

if (!empty($paymentMinAmount)) {
    $paymentsQuery .= " AND p.amount >= ?";
    $paymentQueryParams[] = $paymentMinAmount;
    $paymentParamTypes .= "d";
}

if (!empty($paymentMaxAmount)) {
    $paymentsQuery .= " AND p.amount <= ?";
    $paymentQueryParams[] = $paymentMaxAmount;
    $paymentParamTypes .= "d";
}

if (!empty($paymentReceiptNo)) {
    $paymentsQuery .= " AND p.receipt_no LIKE ?";
    $paymentQueryParams[] = "%$paymentReceiptNo%";
    $paymentParamTypes .= "s";
}

// Add order by clause based on sort parameter
switch ($paymentSortBy) {
    case 'payment_date_asc':
        $paymentsQuery .= " ORDER BY p.payment_date ASC";
        break;
    case 'amount_desc':
        $paymentsQuery .= " ORDER BY p.amount DESC";
        break;
    case 'amount_asc':
        $paymentsQuery .= " ORDER BY p.amount ASC";
        break;
    case 'student_name':
        $paymentsQuery .= " ORDER BY s.first_name ASC, s.last_name ASC";
        break;
    case 'payment_date_desc':
    default:
        $paymentsQuery .= " ORDER BY p.payment_date DESC";
        break;
}

// Apply pagination unless 'all' is selected
if ($paymentPerPage !== 'all') {
    // Only limit if no search filters are applied or if explicitly requested
    if ((empty($paymentSearchTerm) && empty($paymentFeeType) && empty($paymentMethod) &&
        empty($paymentClassId) && empty($paymentSessionId) && empty($paymentFromDate) &&
        empty($paymentToDate) && empty($paymentDepartmentId) && empty($paymentStudentId) &&
        empty($paymentMinAmount) && empty($paymentMaxAmount) && empty($paymentReceiptNo)) ||
        !empty($paymentPerPage)) {

        $limit = intval($paymentPerPage);
        if ($limit <= 0) $limit = 10; // Default to 10 if invalid

        $paymentsQuery .= " LIMIT $limit";
    }
}

// Prepare and execute the query
$paymentStmt = $conn->prepare($paymentsQuery);
if (!empty($paymentQueryParams)) {
    $paymentStmt->bind_param($paymentParamTypes, ...$paymentQueryParams);
}
$paymentStmt->execute();
$paymentsResult = $paymentStmt->get_result();
?>

<div class="container-fluid">
    <div class="row">
        <!-- Include sidebar -->
        <?php include_once 'includes/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-4 border-bottom">
                <h1 class="h2"><i class="fas fa-money-bill-wave me-2"></i> ফি ম্যানেজমেন্ট</h1>
            </div>

            <!-- Modern Quick Access Cards -->
            <div class="row mb-4">
                <div class="col-md-12 mb-3">
                    <h5 class="text-muted mb-3"><i class="fas fa-bolt me-2"></i> কুইক অ্যাকসেস</h5>
                </div>
                <div class="col-md-4 col-lg-2 mb-3">
                    <div class="card h-100 border-0 shadow-sm hover-card">
                        <a href="fee_types.php" class="card-body text-center p-4 d-flex flex-column align-items-center justify-content-center h-100 text-decoration-none text-dark">
                            <div class="icon-circle bg-success text-white mb-3">
                                <i class="fas fa-tags"></i>
                            </div>
                            <h6 class="card-title mb-0">ফি টাইপ</h6>
                        </a>
                    </div>
                </div>
                <div class="col-md-4 col-lg-2 mb-3">
                    <div class="card h-100 border-0 shadow-sm hover-card">
                        <a href="fee_categories.php" class="card-body text-center p-4 d-flex flex-column align-items-center justify-content-center h-100 text-decoration-none text-dark">
                            <div class="icon-circle bg-info text-white mb-3">
                                <i class="fas fa-folder"></i>
                            </div>
                            <h6 class="card-title mb-0">ফি ক্যাটাগরি</h6>
                        </a>
                    </div>
                </div>
                <div class="col-md-4 col-lg-2 mb-3">
                    <div class="card h-100 border-0 shadow-sm hover-card">
                        <a href="fee_report.php" class="card-body text-center p-4 d-flex flex-column align-items-center justify-content-center h-100 text-decoration-none text-dark">
                            <div class="icon-circle bg-secondary text-white mb-3">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <h6 class="card-title mb-0">রিপোর্ট</h6>
                        </a>
                    </div>
                </div>
                <div class="col-md-4 col-lg-2 mb-3">
                    <div class="card h-100 border-0 shadow-sm hover-card">
                        <a href="fee_memo_report.php" class="card-body text-center p-4 d-flex flex-column align-items-center justify-content-center h-100 text-decoration-none text-dark">
                            <div class="icon-circle bg-warning text-white mb-3">
                                <i class="fas fa-file-invoice"></i>
                            </div>
                            <h6 class="card-title mb-0">মেমো রিপোর্ট</h6>
                        </a>
                    </div>
                </div>
                <div class="col-md-4 col-lg-2 mb-3">
                    <div class="card h-100 border-0 shadow-sm hover-card">
                        <a href="student_dues_summary.php" class="card-body text-center p-4 d-flex flex-column align-items-center justify-content-center h-100 text-decoration-none text-dark">
                            <div class="icon-circle bg-danger text-white mb-3">
                                <i class="fas fa-users"></i>
                            </div>
                            <h6 class="card-title mb-0">ছাত্র বকেয়া</h6>
                        </a>
                    </div>
                </div>

            </div>

            <!-- Display success/error messages -->
            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i><?= $_SESSION['success'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['warning'])): ?>
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i><?= $_SESSION['warning'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['warning']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-times-circle me-2"></i><?= $_SESSION['error'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>

            <!-- Debug Info (Remove in production) -->
            <?php if (isset($_GET['debug'])): ?>
                <div class="alert alert-info">
                    <strong>Debug Info:</strong><br>
                    Session ID: <?= session_id() ?><br>
                    User ID: <?= $_SESSION['userId'] ?? 'Not set' ?><br>
                    User Type: <?= $_SESSION['userType'] ?? 'Not set' ?><br>
                    POST Method: <?= $_SERVER['REQUEST_METHOD'] ?><br>
                    <a href="debug-fee.php" class="btn btn-sm btn-primary">Open Debug Tool</a>
                </div>
            <?php endif; ?>

            <!-- Tab Navigation -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <ul class="nav nav-tabs card-header-tabs" id="feeManagementTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="fee-list-tab" data-bs-toggle="tab" data-bs-target="#fee-list" type="button" role="tab">
                                <i class="fas fa-list me-2"></i>ফি তালিকা
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="add-fee-tab" data-bs-toggle="tab" data-bs-target="#add-fee" type="button" role="tab">
                                <i class="fas fa-plus-circle me-2"></i>নতুন ফি যোগ করুন
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="quick-add-tab" data-bs-toggle="tab" data-bs-target="#quick-add" type="button" role="tab">
                                <i class="fas fa-bolt me-2"></i>দ্রুত ফি যোগ
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="feeManagementTabContent">
                        <!-- Fee List Tab -->
                        <div class="tab-pane fade show active" id="fee-list" role="tabpanel">

            <!-- Fees Table -->
            <div class="card mb-4">
                <div class="card-header bg-gradient-primary text-white">
                    <div class="d-flex flex-column flex-lg-row justify-content-between align-items-start align-items-lg-center">
                        <!-- Title Section -->
                        <div class="mb-3 mb-lg-0">
                            <h5 class="mb-1 fw-bold">
                                <i class="fas fa-list me-2"></i> ফি তালিকা
                            </h5>
                            <small class="opacity-75">শিক্ষার্থীদের ফি ব্যবস্থাপনা ও পেমেন্ট ট্র্যাকিং</small>
                        </div>

                        <!-- Action Buttons Section -->
                        <div class="d-flex flex-wrap gap-2">
                            <!-- Primary Actions -->
                            <div class="btn-group" role="group">
                                <button class="btn btn-light btn-sm" type="button" data-bs-toggle="modal" data-bs-target="#addFeeModal" title="নতুন ফি যোগ করুন">
                                    <i class="fas fa-plus-circle me-1"></i>
                                    <span class="d-none d-md-inline">ফি যোগ করুন</span>
                                </button>
                                <a href="add_fee_simple.php" class="btn btn-light btn-sm" title="সহজ পদ্ধতিতে ফি যোগ করুন">
                                    <i class="fas fa-plus-square me-1"></i>
                                    <span class="d-none d-md-inline">সহজ ফি যোগ</span>
                                </a>
                            </div>

                            <!-- Payment Actions -->
                            <div class="btn-group" role="group">
                                <button class="btn btn-success btn-sm" id="multiPaymentBtn" type="button" disabled title="একাধিক ফি পরিশোধ করুন">
                                    <i class="fas fa-money-bill-wave me-1"></i>
                                    <span class="d-none d-lg-inline">একাধিক পেমেন্ট</span>
                                </button>
                            </div>

                            <!-- Report Actions -->
                            <div class="btn-group" role="group">
                                <a href="payment_search.php" class="btn btn-info btn-sm" title="পেমেন্ট সার্চ করুন">
                                    <i class="fas fa-search me-1"></i>
                                    <span class="d-none d-md-inline">পেমেন্ট সার্চ</span>
                                </a>
                                <button class="btn btn-info btn-sm" id="memoReportBtn" type="button" title="মেমো রিপোর্ট দেখুন">
                                    <i class="fas fa-file-invoice me-1"></i>
                                    <span class="d-none d-md-inline">মেমো রিপোর্ট</span>
                                </button>

                                <!-- Export Dropdown -->
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" title="ডেটা এক্সপোর্ট করুন">
                                        <i class="fas fa-file-export me-1"></i>
                                        <span class="d-none d-lg-inline">এক্সপোর্ট</span>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li><h6 class="dropdown-header">
                                            <i class="fas fa-download me-2"></i>ডাউনলোড অপশন
                                        </h6></li>
                                        <li><button class="dropdown-item" type="button" id="exportCsvBtn">
                                            <i class="fas fa-file-csv me-2 text-success"></i>
                                            CSV ফাইল ডাউনলোড
                                            <small class="text-muted d-block">Excel এ খোলার জন্য</small>
                                        </button></li>
                                        <li><button class="dropdown-item" type="button" id="exportExcelBtn">
                                            <i class="fas fa-file-excel me-2 text-success"></i>
                                            Excel ফাইল ডাউনলোড
                                            <small class="text-muted d-block">সরাসরি Excel ফরম্যাট</small>
                                        </button></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><button class="dropdown-item" type="button" id="exportPdfBtn">
                                            <i class="fas fa-file-pdf me-2 text-danger"></i>
                                            PDF রিপোর্ট
                                            <small class="text-muted d-block">প্রিন্ট করার জন্য</small>
                                        </button></li>
                                        <li><button class="dropdown-item" type="button" id="exportPrintBtn">
                                            <i class="fas fa-print me-2 text-primary"></i>
                                            সরাসরি প্রিন্ট
                                            <small class="text-muted d-block">তাৎক্ষণিক প্রিন্ট</small>
                                        </button></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><button class="dropdown-item" type="button" id="exportFilteredBtn">
                                            <i class="fas fa-filter me-2 text-warning"></i>
                                            ফিল্টার করা ডেটা
                                            <small class="text-muted d-block">বর্তমান সার্চ অনুযায়ী</small>
                                        </button></li>
                                        <li><button class="dropdown-item" type="button" id="exportAllBtn">
                                            <i class="fas fa-database me-2 text-info"></i>
                                            সম্পূর্ণ ডেটা
                                            <small class="text-muted d-block">সব রেকর্ড</small>
                                        </button></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="test_export.php" target="_blank">
                                            <i class="fas fa-bug me-2 text-warning"></i>
                                            Export Test
                                            <small class="text-muted d-block">Debug export functionality</small>
                                        </a></li>
                                        <li><a class="dropdown-item" href="fee_management.php?export=csv" target="_blank">
                                            <i class="fas fa-download me-2 text-success"></i>
                                            Quick CSV Download
                                            <small class="text-muted d-block">Direct CSV export</small>
                                        </a></li>
                                    </ul>
                                </div>
                            </div>

                            <!-- Utility Actions -->
                            <div class="btn-group" role="group">
                                <button class="btn btn-outline-light btn-sm" type="button" data-bs-toggle="collapse" data-bs-target="#searchCollapse" aria-expanded="false" aria-controls="searchCollapse" title="সার্চ ও ফিল্টার">
                                    <i class="fas fa-search me-1"></i>
                                    <span class="d-none d-md-inline">সার্চ ফিল্টার</span>
                                </button>

                                <!-- Debug Actions (Development Only) -->
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-light btn-sm dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" title="ডেভেলপমেন্ট টুলস">
                                        <i class="fas fa-cog"></i>
                                        <span class="d-none d-xl-inline ms-1">টুলস</span>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li><a class="dropdown-item" href="?debug=1">
                                            <i class="fas fa-bug me-2 text-warning"></i> Debug Mode
                                        </a></li>
                                        <li><button class="dropdown-item" type="button" onclick="showDebugInfo()">
                                            <i class="fas fa-info-circle me-2 text-info"></i> Debug Info
                                        </button></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="payment_system_checker.php">
                                            <i class="fas fa-stethoscope me-2 text-success"></i> System Checker
                                        </a></li>
                                        <li><a class="dropdown-item" href="fix_students_table.php">
                                            <i class="fas fa-wrench me-2 text-primary"></i> Table Fixer
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search Form -->
                <div class="collapse" id="searchCollapse">
                    <div class="card-body border-bottom">
                        <form method="GET" action="fee_management.php" class="row g-3" id="feeSearchForm">
                            <!-- Basic Search Section -->
                            <div class="col-12 mb-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0"><i class="fas fa-filter me-1"></i> বেসিক ফিল্টার</h6>
                                    <button type="button" class="btn btn-sm btn-link text-decoration-none" id="toggleAdvancedSearch">
                                        <i class="fas fa-sliders-h me-1"></i> উন্নত সার্চ <i class="fas fa-chevron-down ms-1" id="advancedSearchIcon"></i>
                                    </button>
                                </div>
                                <hr class="mt-2 mb-3">
                            </div>

                            <!-- Basic Search Fields -->
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" name="search" id="searchInput" placeholder="শিক্ষার্থীর নাম বা রোল" value="<?php echo htmlspecialchars($searchTerm); ?>">
                                    <button type="button" class="btn btn-outline-secondary clear-input" data-target="searchInput">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <select name="fee_type" class="form-select">
                                    <option value="">সকল ফি টাইপ</option>
                                    <?php
                                    if ($feeTypesResult) {
                                        $feeTypesResult->data_seek(0);
                                        while ($type = $feeTypesResult->fetch_assoc()):
                                    ?>
                                        <option value="<?php echo htmlspecialchars($type['name']); ?>" <?php echo ($feeType === $type['name']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($type['name']); ?>
                                        </option>
                                    <?php
                                        endwhile;
                                    }
                                    ?>
                                </select>
                            </div>

                            <div class="col-md-4">
                                <select name="payment_status" class="form-select">
                                    <option value="">সকল স্ট্যাটাস</option>
                                    <option value="due" <?php echo ($paymentStatus === 'due') ? 'selected' : ''; ?>>বকেয়া</option>
                                    <option value="partial" <?php echo ($paymentStatus === 'partial') ? 'selected' : ''; ?>>আংশিক</option>
                                    <option value="paid" <?php echo ($paymentStatus === 'paid') ? 'selected' : ''; ?>>পরিশোধিত</option>
                                </select>
                            </div>

                            <div class="col-md-4">
                                <select name="class_id" class="form-select">
                                    <option value="">সকল শ্রেণী</option>
                                    <?php
                                    if ($classesResult) {
                                        $classesResult->data_seek(0);
                                        while ($class = $classesResult->fetch_assoc()):
                                    ?>
                                        <option value="<?php echo $class['id']; ?>" <?php echo ($classId == $class['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($class['class_name']); ?>
                                        </option>
                                    <?php
                                        endwhile;
                                    }
                                    ?>
                                </select>
                            </div>

                            <div class="col-md-4">
                                <select name="session_id" class="form-select">
                                    <option value="">সকল সেশন</option>
                                    <?php
                                    if ($sessionsResult) {
                                        $sessionsResult->data_seek(0);
                                        while ($session = $sessionsResult->fetch_assoc()):
                                    ?>
                                        <option value="<?php echo $session['id']; ?>" <?php echo ($sessionId == $session['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($session['session_name']); ?>
                                        </option>
                                    <?php
                                        endwhile;
                                    }
                                    ?>
                                </select>
                            </div>

                            <div class="col-md-4">
                                <select name="department_id" class="form-select">
                                    <option value="">সকল বিভাগ</option>
                                    <?php
                                    // Get all departments
                                    $departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
                                    $departmentsResult = $conn->query($departmentsQuery);

                                    if ($departmentsResult && $departmentsResult->num_rows > 0) {
                                        while ($department = $departmentsResult->fetch_assoc()):
                                    ?>
                                        <option value="<?php echo $department['id']; ?>" <?php echo ($departmentId == $department['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($department['department_name']); ?>
                                        </option>
                                    <?php
                                        endwhile;
                                    }
                                    ?>
                                </select>
                            </div>

                            <!-- Advanced Search Section (Initially Hidden) -->
                            <div class="col-12 advanced-search-section" style="display: none;">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title mb-3"><i class="fas fa-sliders-h me-1"></i> উন্নত সার্চ অপশন</h6>
                                        <div class="row g-3">
                                            <!-- Date Range -->
                                            <div class="col-md-6">
                                                <label class="form-label">তারিখ রেঞ্জ</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                                                    <input type="date" class="form-control" name="from_date" id="fromDateInput" placeholder="শুরুর তারিখ" value="<?php echo htmlspecialchars($fromDate); ?>">
                                                    <span class="input-group-text"><i class="fas fa-arrow-right"></i></span>
                                                    <input type="date" class="form-control" name="to_date" id="toDateInput" placeholder="শেষের তারিখ" value="<?php echo htmlspecialchars($toDate); ?>">
                                                    <button type="button" class="btn btn-outline-secondary clear-date-range">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                                <div class="invalid-feedback" id="dateRangeError">শেষের তারিখ শুরুর তারিখের পরে হতে হবে</div>
                                            </div>

                                            <!-- Amount Range -->
                                            <div class="col-md-6">
                                                <label class="form-label">পরিমাণ রেঞ্জ (৳)</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">৳</span>
                                                    <input type="number" class="form-control" id="min_amount" name="min_amount" min="0" step="0.01" placeholder="সর্বনিম্ন" value="<?php echo htmlspecialchars($minAmount); ?>">
                                                    <span class="input-group-text"><i class="fas fa-arrow-right"></i></span>
                                                    <input type="number" class="form-control" id="max_amount" name="max_amount" min="0" step="0.01" placeholder="সর্বোচ্চ" value="<?php echo htmlspecialchars($maxAmount); ?>">
                                                    <button type="button" class="btn btn-outline-secondary clear-amount-range">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                                <div class="invalid-feedback" id="amountRangeError">সর্বোচ্চ পরিমাণ সর্বনিম্ন পরিমাণের চেয়ে বেশি হতে হবে</div>
                                            </div>

                                            <!-- Student Selection -->
                                            <div class="col-md-6">
                                                <label for="student_id_search" class="form-label">নির্দিষ্ট শিক্ষার্থী</label>
                                                <select class="form-select" id="student_id_search" name="student_id">
                                                    <option value="">সকল শিক্ষার্থী</option>
                                                    <?php
                                                    if ($studentsResult && $studentsResult->num_rows > 0) {
                                                        $studentsResult->data_seek(0);
                                                        while ($student = $studentsResult->fetch_assoc()):
                                                    ?>
                                                        <option value="<?php echo $student['id']; ?>" <?php echo ($studentId == $student['id']) ? 'selected' : ''; ?>>
                                                            <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>
                                                            (<?php echo htmlspecialchars($student['student_id']); ?>)
                                                        </option>
                                                    <?php
                                                        endwhile;
                                                    }
                                                    ?>
                                                </select>
                                            </div>

                                            <!-- Fee Category -->
                                            <div class="col-md-6">
                                                <label for="category_id" class="form-label">ফি ক্যাটাগরি</label>
                                                <select class="form-select" id="category_id" name="category_id">
                                                    <option value="">সকল ক্যাটাগরি</option>
                                                    <?php
                                                    if ($categoriesResult && $categoriesResult->num_rows > 0) {
                                                        $categoriesResult->data_seek(0);
                                                        while ($category = $categoriesResult->fetch_assoc()):
                                                    ?>
                                                        <option value="<?php echo $category['id']; ?>" <?php echo (isset($_GET['category_id']) && $_GET['category_id'] == $category['id']) ? 'selected' : ''; ?>>
                                                            <?php echo htmlspecialchars($category['name']); ?>
                                                        </option>
                                                    <?php
                                                        endwhile;
                                                    }
                                                    ?>
                                                </select>
                                            </div>

                                            <!-- Receipt Number -->
                                            <div class="col-md-6">
                                                <label for="receipt_no" class="form-label">রিসিপ্ট নম্বর</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="fas fa-receipt"></i></span>
                                                    <input type="text" class="form-control" id="receipt_no" name="receipt_no" placeholder="রিসিপ্ট নম্বর দিয়ে খুঁজুন" value="<?php echo isset($_GET['receipt_no']) ? htmlspecialchars($_GET['receipt_no']) : ''; ?>">
                                                    <button type="button" class="btn btn-outline-secondary clear-input" data-target="receipt_no">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>

                                            <!-- Sort Order -->
                                            <div class="col-md-6">
                                                <label for="sort_by" class="form-label">সাজানোর ক্রম</label>
                                                <select class="form-select" id="sort_by" name="sort_by">
                                                    <option value="due_date_desc" <?php echo (isset($_GET['sort_by']) && $_GET['sort_by'] == 'due_date_desc') ? 'selected' : ''; ?>>তারিখ (নতুন থেকে পুরাতন)</option>
                                                    <option value="due_date_asc" <?php echo (isset($_GET['sort_by']) && $_GET['sort_by'] == 'due_date_asc') ? 'selected' : ''; ?>>তারিখ (পুরাতন থেকে নতুন)</option>
                                                    <option value="amount_desc" <?php echo (isset($_GET['sort_by']) && $_GET['sort_by'] == 'amount_desc') ? 'selected' : ''; ?>>পরিমাণ (বড় থেকে ছোট)</option>
                                                    <option value="amount_asc" <?php echo (isset($_GET['sort_by']) && $_GET['sort_by'] == 'amount_asc') ? 'selected' : ''; ?>>পরিমাণ (ছোট থেকে বড়)</option>
                                                    <option value="student_name" <?php echo (isset($_GET['sort_by']) && $_GET['sort_by'] == 'student_name') ? 'selected' : ''; ?>>শিক্ষার্থীর নাম</option>
                                                </select>
                                            </div>

                                            <!-- Results Per Page -->
                                            <div class="col-md-6">
                                                <label for="per_page" class="form-label">প্রতি পৃষ্ঠায় রেজাল্ট</label>
                                                <select class="form-select" id="per_page" name="per_page">
                                                    <option value="5" <?php echo (isset($_GET['per_page']) && $_GET['per_page'] == '5') ? 'selected' : ''; ?>>৫ টি</option>
                                                    <option value="10" <?php echo (isset($_GET['per_page']) && $_GET['per_page'] == '10') ? 'selected' : ''; ?>>১০ টি</option>
                                                    <option value="25" <?php echo (isset($_GET['per_page']) && $_GET['per_page'] == '25') ? 'selected' : ''; ?>>২৫ টি</option>
                                                    <option value="50" <?php echo (isset($_GET['per_page']) && $_GET['per_page'] == '50') ? 'selected' : ''; ?>>৫০ টি</option>
                                                    <option value="100" <?php echo (isset($_GET['per_page']) && $_GET['per_page'] == '100') ? 'selected' : ''; ?>>১০০ টি</option>
                                                    <option value="all" <?php echo (isset($_GET['per_page']) && $_GET['per_page'] == 'all') ? 'selected' : ''; ?>>সব দেখান</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Search Buttons -->
                            <div class="col-12 d-flex justify-content-between align-items-center mt-3">
                                <div>
                                    <span class="text-muted small">
                                        <i class="fas fa-keyboard me-1"></i> টিপস: সার্চ করতে <kbd>Ctrl</kbd> + <kbd>Enter</kbd> চাপুন
                                    </span>
                                </div>
                                <div>
                                    <button type="reset" class="btn btn-outline-secondary me-2" id="resetSearchForm">
                                        <i class="fas fa-redo me-1"></i> রিসেট করুন
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i> সার্চ করুন
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card-body">
                    <?php if ($feesResult && $feesResult->num_rows > 0): ?>
                        <div class="mb-3">
                            <span class="badge bg-info"><?php echo $feesResult->num_rows; ?> টি রেকর্ড পাওয়া গেছে</span>

                            <?php
                            $activeFilters = [];
                            if (!empty($searchTerm)) $activeFilters[] = "সার্চ: " . htmlspecialchars($searchTerm);
                            if (!empty($feeType)) $activeFilters[] = "ফি টাইপ: " . htmlspecialchars($feeType);
                            if (!empty($paymentStatus)) {
                                $statusText = $paymentStatus === 'due' ? 'বকেয়া' : ($paymentStatus === 'partial' ? 'আংশিক' : 'পরিশোধিত');
                                $activeFilters[] = "স্ট্যাটাস: " . $statusText;
                            }
                            if (!empty($classId)) {
                                $classesResult->data_seek(0);
                                while ($class = $classesResult->fetch_assoc()) {
                                    if ($class['id'] == $classId) {
                                        $activeFilters[] = "শ্রেণী: " . htmlspecialchars($class['class_name']);
                                        break;
                                    }
                                }
                            }
                            if (!empty($sessionId)) {
                                $sessionsResult->data_seek(0);
                                while ($session = $sessionsResult->fetch_assoc()) {
                                    if ($session['id'] == $sessionId) {
                                        $activeFilters[] = "সেশন: " . htmlspecialchars($session['session_name']);
                                        break;
                                    }
                                }
                            }
                            if (!empty($departmentId)) {
                                $departmentsResult->data_seek(0);
                                while ($department = $departmentsResult->fetch_assoc()) {
                                    if ($department['id'] == $departmentId) {
                                        $activeFilters[] = "বিভাগ: " . htmlspecialchars($department['department_name']);
                                        break;
                                    }
                                }
                            }
                            if (!empty($fromDate)) $activeFilters[] = "শুরুর তারিখ: " . date('d/m/Y', strtotime($fromDate));
                            if (!empty($toDate)) $activeFilters[] = "শেষের তারিখ: " . date('d/m/Y', strtotime($toDate));
                            if (!empty($minAmount)) $activeFilters[] = "সর্বনিম্ন পরিমাণ: ৳" . number_format($minAmount, 2);
                            if (!empty($maxAmount)) $activeFilters[] = "সর্বোচ্চ পরিমাণ: ৳" . number_format($maxAmount, 2);
                            if (!empty($categoryId)) {
                                $categoriesResult->data_seek(0);
                                while ($category = $categoriesResult->fetch_assoc()) {
                                    if ($category['id'] == $categoryId) {
                                        $activeFilters[] = "ক্যাটাগরি: " . htmlspecialchars($category['name']);
                                        break;
                                    }
                                }
                            }
                            if (!empty($receiptNo)) $activeFilters[] = "রিসিপ্ট নম্বর: " . htmlspecialchars($receiptNo);
                            if (!empty($perPage) && $perPage != '10') {
                                $activeFilters[] = "প্রতি পৃষ্ঠায়: " . ($perPage == 'all' ? 'সব' : $perPage . ' টি');
                            }

                            if (!empty($activeFilters)):
                            ?>
                                <div class="mt-2 d-flex flex-wrap gap-2 align-items-center">
                                    <span class="text-muted me-2">সক্রিয় ফিল্টার:</span>
                                    <?php foreach ($activeFilters as $filter): ?>
                                        <span class="badge bg-light text-dark border"><?php echo $filter; ?></span>
                                    <?php endforeach; ?>
                                    <a href="fee_management.php" class="btn btn-sm btn-outline-danger ms-2">
                                        <i class="fas fa-times-circle"></i> সব ফিল্টার মুছুন
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th width="30">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="selectAllFees">
                                        </div>
                                    </th>
                                    <th>আইডি</th>
                                    <th>শিক্ষার্থী</th>
                                    <th>রোল</th>
                                    <th>শ্রেণী</th>
                                    <th>সেশন</th>
                                    <th>ফি ধরন</th>
                                    <th>পরিমাণ</th>
                                    <th>পরিশোধিত</th>
                                    <th>বকেয়া</th>
                                    <th>তারিখ</th>
                                    <th>স্ট্যাটাস</th>
                                    <th>পদক্ষেপ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($feesResult && $feesResult->num_rows > 0): ?>
                                    <?php while ($fee = $feesResult->fetch_assoc()): ?>
                                        <?php
                                            $dueAmount = $fee['amount'] - $fee['paid'];
                                            $statusClass = 'bg-danger text-white';
                                            if ($fee['payment_status'] === 'paid') {
                                                $statusClass = 'bg-success text-white';
                                            } else if ($fee['payment_status'] === 'partial') {
                                                $statusClass = 'bg-warning text-dark';
                                            }
                                        ?>
                                        <tr data-student-id="<?= $fee['student_id'] ?>" data-student-name="<?= htmlspecialchars($fee['first_name'] . ' ' . $fee['last_name']) ?>">
                                            <td>
                                                <?php if ($fee['payment_status'] !== 'paid'): ?>
                                                <div class="form-check">
                                                    <input class="form-check-input fee-checkbox" type="checkbox"
                                                           data-fee-id="<?= $fee['id'] ?>"
                                                           data-student-id="<?= $fee['student_id'] ?>"
                                                           data-fee-type="<?= htmlspecialchars($fee['fee_type']) ?>"
                                                           data-amount="<?= $fee['amount'] ?>"
                                                           data-paid="<?= $fee['paid'] ?>"
                                                           data-due="<?= $fee['amount'] - $fee['paid'] ?>">
                                                </div>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= $fee['id'] ?></td>
                                            <td><?= htmlspecialchars($fee['first_name'] . ' ' . $fee['last_name']) ?></td>
                                            <td><?= htmlspecialchars($fee['roll']) ?></td>
                                            <td><?= htmlspecialchars($fee['class_name'] ?? 'N/A') ?></td>
                                            <td><?= htmlspecialchars($fee['session_name'] ?? 'N/A') ?></td>
                                            <td><?= htmlspecialchars($fee['fee_type']) ?></td>
                                            <td>৳ <?= number_format($fee['amount'], 2) ?></td>
                                            <td>৳ <?= number_format($fee['paid'], 2) ?></td>
                                            <td>৳ <?= number_format($dueAmount, 2) ?></td>
                                            <td><?= date('d/m/Y', strtotime($fee['due_date'])) ?></td>
                                            <td><span class="badge <?= $statusClass ?>"><?= $fee['payment_status'] === 'due' ? 'বকেয়া' : ($fee['payment_status'] === 'partial' ? 'আংশিক' : 'পরিশোধিত') ?></span></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <?php
                                                    $paidAmount = isset($fee['paid']) ? $fee['paid'] : 0;
                                                    $dueAmount = $fee['amount'] - $paidAmount;
                                                    $paymentStatus = isset($fee['payment_status']) ? $fee['payment_status'] : 'due';
                                                    ?>

                                                    <?php if ($dueAmount > 0): ?>
                                                        <!-- Simple Payment Form (No Modal) -->
                                                        <form method="post" action="fee_management.php" style="display: inline-block;" onsubmit="return confirmPayment(this)">
                                                            <input type="hidden" name="simple_pay_fee" value="1">
                                                            <input type="hidden" name="fee_id" value="<?= $fee['id'] ?>">
                                                            <input type="hidden" name="student_id" value="<?= $fee['student_id'] ?>">
                                                            <input type="hidden" name="max_amount" value="<?= $dueAmount ?>">

                                                            <div class="input-group input-group-sm" style="width: 200px;">
                                                                <span class="input-group-text">৳</span>
                                                                <input type="number" name="payment_amount" class="form-control"
                                                                       value="<?= $dueAmount ?>" min="0.01" max="<?= $dueAmount ?>"
                                                                       step="0.01" required style="width: 80px;"
                                                                       title="সর্বোচ্চ: ৳<?= number_format($dueAmount, 2) ?>">
                                                                <button type="submit" class="btn btn-success btn-sm" title="পেমেন্ট করুন">
                                                                    <i class="fas fa-money-bill-wave"></i> Pay
                                                                </button>
                                                            </div>
                                                        </form>
                                                    <?php else: ?>
                                                        <span class="btn btn-sm btn-outline-success disabled">
                                                            <i class="fas fa-check-circle"></i> পরিশোধিত
                                                        </span>
                                                    <?php endif; ?>

                                                    <?php
                                                    // Get the latest payment receipt for this fee
                                                    $latestReceiptQuery = "SELECT receipt_no FROM fee_payments WHERE fee_id = ? ORDER BY payment_date DESC, id DESC LIMIT 1";
                                                    $latestReceiptStmt = $conn->prepare($latestReceiptQuery);
                                                    $latestReceiptStmt->bind_param('i', $fee['id']);
                                                    $latestReceiptStmt->execute();
                                                    $latestReceiptResult = $latestReceiptStmt->get_result();
                                                    $latestReceipt = $latestReceiptResult->fetch_assoc();

                                                    if ($latestReceipt && !empty($latestReceipt['receipt_no'])) {
                                                        // If there's a receipt, link to memo receipt
                                                        echo '<a href="receipt_final.php?receipt_no=' . urlencode($latestReceipt['receipt_no']) . '" class="btn btn-sm btn-info" title="রিসিপ্ট দেখুন - ' . htmlspecialchars($latestReceipt['receipt_no']) . '">';
                                                        echo '<i class="fas fa-receipt"></i>';
                                                        echo '</a>';
                                                    } else {
                                                        // If no receipt, show disabled button
                                                        echo '<span class="btn btn-sm btn-secondary disabled" title="কোন পেমেন্ট নেই">';
                                                        echo '<i class="fas fa-receipt"></i>';
                                                        echo '</span>';
                                                    }
                                                    ?>

                                                    <a href="simple-payment.php" class="btn btn-sm btn-warning" title="Advanced Payment">
                                                        <i class="fas fa-cog"></i>
                                                    </a>

                                                    <button type="button" class="btn btn-sm btn-danger delete-fee-btn"
                                                            data-fee-id="<?= $fee['id'] ?>"
                                                            data-student-name="<?= htmlspecialchars($fee['first_name'] . ' ' . $fee['last_name']) ?>"
                                                            data-fee-type="<?= htmlspecialchars($fee['fee_type']) ?>"
                                                            title="ফি ডিলেট করুন">
                                                        <i class="fas fa-trash-alt"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="11" class="text-center">কোন ফি রেকর্ড পাওয়া যায়নি</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($perPage !== 'all' && $totalRecords > 0): ?>

                    <div class="card-footer">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <span class="text-muted me-3">
                                        মোট <?php echo number_format($totalRecords); ?> টি রেকর্ডের মধ্যে
                                        <?php
                                        $startRecord = ($feeCurrentPage - 1) * intval($perPage) + 1;
                                        $endRecord = min($feeCurrentPage * intval($perPage), $totalRecords);
                                        echo number_format($startRecord) . '-' . number_format($endRecord);
                                        ?> টি দেখানো হচ্ছে
                                    </span>

                                    <!-- Quick Per Page Selector -->
                                    <form method="GET" action="fee_management.php" class="d-inline-block">
                                        <!-- Preserve all current search parameters -->
                                        <?php foreach ($_GET as $key => $value): ?>
                                            <?php if ($key !== 'per_page' && $key !== 'page'): ?>
                                                <input type="hidden" name="<?php echo htmlspecialchars($key); ?>" value="<?php echo htmlspecialchars($value); ?>">
                                            <?php endif; ?>
                                        <?php endforeach; ?>

                                        <select name="per_page" class="form-select form-select-sm d-inline-block w-auto" onchange="this.form.submit()">
                                            <option value="5" <?php echo ($perPage == '5') ? 'selected' : ''; ?>>৫ টি</option>
                                            <option value="10" <?php echo ($perPage == '10') ? 'selected' : ''; ?>>১০ টি</option>
                                            <option value="25" <?php echo ($perPage == '25') ? 'selected' : ''; ?>>২৫ টি</option>
                                            <option value="50" <?php echo ($perPage == '50') ? 'selected' : ''; ?>>৫০ টি</option>
                                            <option value="100" <?php echo ($perPage == '100') ? 'selected' : ''; ?>>১০০ টি</option>
                                            <option value="all" <?php echo ($perPage == 'all') ? 'selected' : ''; ?>>সব দেখান</option>
                                        </select>
                                    </form>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <?php if ($totalPages > 1): ?>
                                <nav aria-label="Fee pagination">
                                    <ul class="pagination pagination-sm justify-content-end mb-0">
                                        <!-- First Page -->
                                        <?php if ($feeCurrentPage > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>">
                                                <i class="fas fa-angle-double-left"></i>
                                            </a>
                                        </li>
                                        <?php endif; ?>

                                        <!-- Previous Page -->
                                        <?php if ($feeCurrentPage > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $feeCurrentPage - 1])); ?>">
                                                <i class="fas fa-angle-left"></i>
                                            </a>
                                        </li>
                                        <?php endif; ?>

                                        <!-- Page Numbers -->
                                        <?php
                                        $startPage = max(1, $feeCurrentPage - 2);
                                        $endPage = min($totalPages, $feeCurrentPage + 2);

                                        for ($i = $startPage; $i <= $endPage; $i++):
                                        ?>
                                        <li class="page-item <?php echo ($i == $feeCurrentPage) ? 'active' : ''; ?>">
                                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                        <?php endfor; ?>

                                        <!-- Next Page -->
                                        <?php if ($feeCurrentPage < $totalPages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $feeCurrentPage + 1])); ?>">
                                                <i class="fas fa-angle-right"></i>
                                            </a>
                                        </li>
                                        <?php endif; ?>

                                        <!-- Last Page -->
                                        <?php if ($feeCurrentPage < $totalPages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $totalPages])); ?>">
                                                <i class="fas fa-angle-double-right"></i>
                                            </a>
                                        </li>
                                        <?php endif; ?>
                                    </ul>
                                </nav>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Payments -->
            <div class="card">
                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-history me-2"></i> সাম্প্রতিক পেমেন্ট</h5>
                    <button class="btn btn-sm btn-light" type="button" data-bs-toggle="collapse" data-bs-target="#paymentSearchCollapse" aria-expanded="false" aria-controls="paymentSearchCollapse">
                        <i class="fas fa-search me-1"></i> সার্চ ফিল্টার
                    </button>
                </div>

                <!-- Payment Search Form -->
                <div class="collapse" id="paymentSearchCollapse">
                    <div class="card-body border-bottom">
                        <form method="GET" action="fee_management.php" class="row g-3" id="paymentSearchForm">
                            <!-- Keep any existing search parameters for fees table -->
                            <input type="hidden" name="search" value="<?php echo htmlspecialchars($searchTerm); ?>">
                            <input type="hidden" name="fee_type" value="<?php echo htmlspecialchars($feeType); ?>">
                            <input type="hidden" name="payment_status" value="<?php echo htmlspecialchars($paymentStatus); ?>">
                            <input type="hidden" name="class_id" value="<?php echo htmlspecialchars($classId); ?>">
                            <input type="hidden" name="session_id" value="<?php echo htmlspecialchars($sessionId); ?>">
                            <input type="hidden" name="from_date" value="<?php echo htmlspecialchars($fromDate); ?>">
                            <input type="hidden" name="to_date" value="<?php echo htmlspecialchars($toDate); ?>">

                            <!-- Basic Search Section -->
                            <div class="col-12 mb-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0"><i class="fas fa-filter me-1"></i> বেসিক ফিল্টার</h6>
                                    <button type="button" class="btn btn-sm btn-link text-decoration-none" id="togglePaymentAdvancedSearch">
                                        <i class="fas fa-sliders-h me-1"></i> উন্নত সার্চ <i class="fas fa-chevron-down ms-1" id="paymentAdvancedSearchIcon"></i>
                                    </button>
                                </div>
                                <hr class="mt-2 mb-3">
                            </div>

                            <!-- Basic Search Fields -->
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" name="payment_search" id="paymentSearchInput" placeholder="শিক্ষার্থীর নাম বা রোল" value="<?php echo htmlspecialchars($paymentSearchTerm); ?>">
                                    <button type="button" class="btn btn-outline-secondary clear-input" data-target="paymentSearchInput">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <select name="payment_fee_type" class="form-select">
                                    <option value="">সকল ফি টাইপ</option>
                                    <?php
                                    if ($feeTypesResult) {
                                        $feeTypesResult->data_seek(0);
                                        while ($type = $feeTypesResult->fetch_assoc()):
                                    ?>
                                        <option value="<?php echo htmlspecialchars($type['name']); ?>" <?php echo ($paymentFeeType === $type['name']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($type['name']); ?>
                                        </option>
                                    <?php
                                        endwhile;
                                    }
                                    ?>
                                </select>
                            </div>

                            <div class="col-md-4">
                                <select name="payment_method" class="form-select">
                                    <option value="">সকল পদ্ধতি</option>
                                    <option value="cash" <?php echo ($paymentMethod === 'cash') ? 'selected' : ''; ?>>নগদ</option>
                                    <option value="bkash" <?php echo ($paymentMethod === 'bkash') ? 'selected' : ''; ?>>বিকাশ</option>
                                    <option value="nagad" <?php echo ($paymentMethod === 'nagad') ? 'selected' : ''; ?>>নগদ (মোবাইল ব্যাংকিং)</option>
                                    <option value="rocket" <?php echo ($paymentMethod === 'rocket') ? 'selected' : ''; ?>>রকেট</option>
                                    <option value="bank" <?php echo ($paymentMethod === 'bank') ? 'selected' : ''; ?>>ব্যাংক</option>
                                </select>
                            </div>

                            <div class="col-md-4">
                                <select name="payment_class_id" class="form-select">
                                    <option value="">সকল শ্রেণী</option>
                                    <?php
                                    if ($classesResult) {
                                        $classesResult->data_seek(0);
                                        while ($class = $classesResult->fetch_assoc()):
                                    ?>
                                        <option value="<?php echo $class['id']; ?>" <?php echo ($paymentClassId == $class['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($class['class_name']); ?>
                                        </option>
                                    <?php
                                        endwhile;
                                    }
                                    ?>
                                </select>
                            </div>

                            <div class="col-md-4">
                                <select name="payment_session_id" class="form-select">
                                    <option value="">সকল সেশন</option>
                                    <?php
                                    if ($sessionsResult) {
                                        $sessionsResult->data_seek(0);
                                        while ($session = $sessionsResult->fetch_assoc()):
                                    ?>
                                        <option value="<?php echo $session['id']; ?>" <?php echo ($paymentSessionId == $session['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($session['session_name']); ?>
                                        </option>
                                    <?php
                                        endwhile;
                                    }
                                    ?>
                                </select>
                            </div>

                            <div class="col-md-4">
                                <select name="payment_department_id" class="form-select">
                                    <option value="">সকল বিভাগ</option>
                                    <?php
                                    if ($departmentsResult) {
                                        $departmentsResult->data_seek(0);
                                        while ($department = $departmentsResult->fetch_assoc()):
                                    ?>
                                        <option value="<?php echo $department['id']; ?>" <?php echo (isset($_GET['payment_department_id']) && $_GET['payment_department_id'] == $department['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($department['department_name']); ?>
                                        </option>
                                    <?php
                                        endwhile;
                                    }
                                    ?>
                                </select>
                            </div>

                            <!-- Advanced Search Section (Initially Hidden) -->
                            <div class="col-12 payment-advanced-search-section" style="display: none;">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title mb-3"><i class="fas fa-sliders-h me-1"></i> উন্নত সার্চ অপশন</h6>
                                        <div class="row g-3">
                                            <!-- Date Range -->
                                            <div class="col-md-6">
                                                <label class="form-label">পেমেন্ট তারিখ রেঞ্জ</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                                                    <input type="date" class="form-control" name="payment_from_date" id="paymentFromDateInput" placeholder="শুরুর তারিখ" value="<?php echo htmlspecialchars($paymentFromDate); ?>">
                                                    <span class="input-group-text"><i class="fas fa-arrow-right"></i></span>
                                                    <input type="date" class="form-control" name="payment_to_date" id="paymentToDateInput" placeholder="শেষের তারিখ" value="<?php echo htmlspecialchars($paymentToDate); ?>">
                                                    <button type="button" class="btn btn-outline-secondary clear-payment-date-range">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                                <div class="invalid-feedback" id="paymentDateRangeError">শেষের তারিখ শুরুর তারিখের পরে হতে হবে</div>
                                            </div>

                                            <!-- Amount Range -->
                                            <div class="col-md-6">
                                                <label class="form-label">পেমেন্ট পরিমাণ রেঞ্জ (৳)</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">৳</span>
                                                    <input type="number" class="form-control" id="payment_min_amount" name="payment_min_amount" min="0" step="0.01" placeholder="সর্বনিম্ন" value="<?php echo isset($_GET['payment_min_amount']) ? htmlspecialchars($_GET['payment_min_amount']) : ''; ?>">
                                                    <span class="input-group-text"><i class="fas fa-arrow-right"></i></span>
                                                    <input type="number" class="form-control" id="payment_max_amount" name="payment_max_amount" min="0" step="0.01" placeholder="সর্বোচ্চ" value="<?php echo isset($_GET['payment_max_amount']) ? htmlspecialchars($_GET['payment_max_amount']) : ''; ?>">
                                                    <button type="button" class="btn btn-outline-secondary clear-payment-amount-range">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                                <div class="invalid-feedback" id="paymentAmountRangeError">সর্বোচ্চ পরিমাণ সর্বনিম্ন পরিমাণের চেয়ে বেশি হতে হবে</div>
                                            </div>

                                            <!-- Student Selection -->
                                            <div class="col-md-6">
                                                <label for="payment_student_id" class="form-label">নির্দিষ্ট শিক্ষার্থী</label>
                                                <select class="form-select" id="payment_student_id" name="payment_student_id">
                                                    <option value="">সকল শিক্ষার্থী</option>
                                                    <?php
                                                    if ($studentsResult) {
                                                        $studentsResult->data_seek(0);
                                                        while ($student = $studentsResult->fetch_assoc()):
                                                    ?>
                                                        <option value="<?php echo $student['id']; ?>" <?php echo (isset($_GET['payment_student_id']) && $_GET['payment_student_id'] == $student['id']) ? 'selected' : ''; ?>>
                                                            <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>
                                                            (<?php echo htmlspecialchars($student['student_id']); ?>)
                                                        </option>
                                                    <?php
                                                        endwhile;
                                                    }
                                                    ?>
                                                </select>
                                            </div>

                                            <!-- Receipt Number -->
                                            <div class="col-md-6">
                                                <label for="payment_receipt_no" class="form-label">রিসিপ্ট নম্বর</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="fas fa-receipt"></i></span>
                                                    <input type="text" class="form-control" id="payment_receipt_no" name="payment_receipt_no" placeholder="রিসিপ্ট নম্বর দিয়ে খুঁজুন" value="<?php echo isset($_GET['payment_receipt_no']) ? htmlspecialchars($_GET['payment_receipt_no']) : ''; ?>">
                                                    <button type="button" class="btn btn-outline-secondary clear-input" data-target="payment_receipt_no">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>

                                            <!-- Sort Order -->
                                            <div class="col-md-6">
                                                <label for="payment_sort_by" class="form-label">সাজানোর ক্রম</label>
                                                <select class="form-select" id="payment_sort_by" name="payment_sort_by">
                                                    <option value="payment_date_desc" <?php echo (isset($_GET['payment_sort_by']) && $_GET['payment_sort_by'] == 'payment_date_desc') ? 'selected' : ''; ?>>তারিখ (নতুন থেকে পুরাতন)</option>
                                                    <option value="payment_date_asc" <?php echo (isset($_GET['payment_sort_by']) && $_GET['payment_sort_by'] == 'payment_date_asc') ? 'selected' : ''; ?>>তারিখ (পুরাতন থেকে নতুন)</option>
                                                    <option value="amount_desc" <?php echo (isset($_GET['payment_sort_by']) && $_GET['payment_sort_by'] == 'amount_desc') ? 'selected' : ''; ?>>পরিমাণ (বড় থেকে ছোট)</option>
                                                    <option value="amount_asc" <?php echo (isset($_GET['payment_sort_by']) && $_GET['payment_sort_by'] == 'amount_asc') ? 'selected' : ''; ?>>পরিমাণ (ছোট থেকে বড়)</option>
                                                    <option value="student_name" <?php echo (isset($_GET['payment_sort_by']) && $_GET['payment_sort_by'] == 'student_name') ? 'selected' : ''; ?>>শিক্ষার্থীর নাম</option>
                                                </select>
                                            </div>

                                            <!-- Results Per Page -->
                                            <div class="col-md-6">
                                                <label for="payment_per_page" class="form-label">প্রতি পৃষ্ঠায় রেজাল্ট</label>
                                                <select class="form-select" id="payment_per_page" name="payment_per_page">
                                                    <option value="10" <?php echo (isset($_GET['payment_per_page']) && $_GET['payment_per_page'] == '10') ? 'selected' : ''; ?>>১০ টি</option>
                                                    <option value="25" <?php echo (isset($_GET['payment_per_page']) && $_GET['payment_per_page'] == '25') ? 'selected' : ''; ?>>২৫ টি</option>
                                                    <option value="50" <?php echo (isset($_GET['payment_per_page']) && $_GET['payment_per_page'] == '50') ? 'selected' : ''; ?>>৫০ টি</option>
                                                    <option value="100" <?php echo (isset($_GET['payment_per_page']) && $_GET['payment_per_page'] == '100') ? 'selected' : ''; ?>>১০০ টি</option>
                                                    <option value="all" <?php echo (isset($_GET['payment_per_page']) && $_GET['payment_per_page'] == 'all') ? 'selected' : ''; ?>>সব দেখান</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Search Buttons -->
                            <div class="col-12 d-flex justify-content-between align-items-center mt-3">
                                <div>
                                    <span class="text-muted small">
                                        <i class="fas fa-keyboard me-1"></i> টিপস: সার্চ করতে <kbd>Ctrl</kbd> + <kbd>Enter</kbd> চাপুন
                                    </span>
                                </div>
                                <div>
                                    <button type="reset" class="btn btn-outline-secondary me-2" id="resetPaymentSearchForm">
                                        <i class="fas fa-redo me-1"></i> রিসেট করুন
                                    </button>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-search me-1"></i> সার্চ করুন
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card-body">
                    <?php if ($paymentsResult && $paymentsResult->num_rows > 0): ?>
                        <div class="mb-3">
                            <span class="badge bg-info"><?php echo $paymentsResult->num_rows; ?> টি পেমেন্ট রেকর্ড পাওয়া গেছে</span>

                            <?php
                            $activePaymentFilters = [];
                            if (!empty($paymentSearchTerm)) $activePaymentFilters[] = "সার্চ: " . htmlspecialchars($paymentSearchTerm);
                            if (!empty($paymentFeeType)) $activePaymentFilters[] = "ফি টাইপ: " . htmlspecialchars($paymentFeeType);
                            if (!empty($paymentMethod)) {
                                $methodText = '';
                                switch ($paymentMethod) {
                                    case 'cash': $methodText = 'নগদ'; break;
                                    case 'bkash': $methodText = 'বিকাশ'; break;
                                    case 'nagad': $methodText = 'নগদ (মোবাইল ব্যাংকিং)'; break;
                                    case 'rocket': $methodText = 'রকেট'; break;
                                    case 'bank': $methodText = 'ব্যাংক'; break;
                                    default: $methodText = $paymentMethod;
                                }
                                $activePaymentFilters[] = "পদ্ধতি: " . $methodText;
                            }
                            if (!empty($paymentClassId)) {
                                $classesResult->data_seek(0);
                                while ($class = $classesResult->fetch_assoc()) {
                                    if ($class['id'] == $paymentClassId) {
                                        $activePaymentFilters[] = "শ্রেণী: " . htmlspecialchars($class['class_name']);
                                        break;
                                    }
                                }
                            }
                            if (!empty($paymentSessionId)) {
                                $sessionsResult->data_seek(0);
                                while ($session = $sessionsResult->fetch_assoc()) {
                                    if ($session['id'] == $paymentSessionId) {
                                        $activePaymentFilters[] = "সেশন: " . htmlspecialchars($session['session_name']);
                                        break;
                                    }
                                }
                            }
                            if (!empty($paymentDepartmentId)) {
                                $departmentsResult->data_seek(0);
                                while ($department = $departmentsResult->fetch_assoc()) {
                                    if ($department['id'] == $paymentDepartmentId) {
                                        $activePaymentFilters[] = "বিভাগ: " . htmlspecialchars($department['department_name']);
                                        break;
                                    }
                                }
                            }
                            if (!empty($paymentFromDate)) $activePaymentFilters[] = "শুরুর তারিখ: " . date('d/m/Y', strtotime($paymentFromDate));
                            if (!empty($paymentToDate)) $activePaymentFilters[] = "শেষের তারিখ: " . date('d/m/Y', strtotime($paymentToDate));
                            if (!empty($paymentMinAmount)) $activePaymentFilters[] = "সর্বনিম্ন পরিমাণ: ৳" . number_format($paymentMinAmount, 2);
                            if (!empty($paymentMaxAmount)) $activePaymentFilters[] = "সর্বোচ্চ পরিমাণ: ৳" . number_format($paymentMaxAmount, 2);
                            if (!empty($paymentReceiptNo)) $activePaymentFilters[] = "রিসিপ্ট নম্বর: " . htmlspecialchars($paymentReceiptNo);
                            if (!empty($paymentStudentId)) {
                                $studentsResult->data_seek(0);
                                while ($student = $studentsResult->fetch_assoc()) {
                                    if ($student['id'] == $paymentStudentId) {
                                        $activePaymentFilters[] = "শিক্ষার্থী: " . htmlspecialchars($student['first_name'] . ' ' . $student['last_name']);
                                        break;
                                    }
                                }
                            }
                            if (!empty($paymentPerPage) && $paymentPerPage != '10') {
                                $activePaymentFilters[] = "প্রতি পৃষ্ঠায়: " . ($paymentPerPage == 'all' ? 'সব' : $paymentPerPage . ' টি');
                            }

                            if (!empty($activePaymentFilters)):
                            ?>
                                <div class="mt-2 d-flex flex-wrap gap-2 align-items-center">
                                    <span class="text-muted me-2">সক্রিয় ফিল্টার:</span>
                                    <?php foreach ($activePaymentFilters as $filter): ?>
                                        <span class="badge bg-light text-dark border"><?php echo $filter; ?></span>
                                    <?php endforeach; ?>
                                    <a href="fee_management.php" class="btn btn-sm btn-outline-danger ms-2">
                                        <i class="fas fa-times-circle"></i> সব ফিল্টার মুছুন
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>আইডি</th>
                                    <th>শিক্ষার্থী</th>
                                    <th>রোল</th>
                                    <th>শ্রেণী</th>
                                    <th>সেশন</th>
                                    <th>ফি ধরন</th>
                                    <th>পরিমাণ</th>
                                    <th>তারিখ</th>
                                    <th>পদ্ধতি</th>
                                    <th>রিসিপ্ট নং</th>
                                    <th>পদক্ষেপ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($paymentsResult && $paymentsResult->num_rows > 0): ?>
                                    <?php while ($payment = $paymentsResult->fetch_assoc()): ?>
                                        <tr>
                                            <td><?= $payment['id'] ?></td>
                                            <td><?= htmlspecialchars($payment['first_name'] . ' ' . $payment['last_name']) ?></td>
                                            <td><?= htmlspecialchars($payment['roll']) ?></td>
                                            <td><?= htmlspecialchars($payment['class_name'] ?? 'N/A') ?></td>
                                            <td><?= htmlspecialchars($payment['session_name'] ?? 'N/A') ?></td>
                                            <td><?= htmlspecialchars($payment['fee_type']) ?></td>
                                            <td>৳ <?= number_format($payment['amount'], 2) ?></td>
                                            <td><?= date('d/m/Y', strtotime($payment['payment_date'])) ?></td>
                                            <td><?= $payment['payment_method'] === 'cash' ? 'নগদ' : ($payment['payment_method'] === 'bkash' ? 'বিকাশ' : $payment['payment_method']) ?></td>
                                            <td><?= htmlspecialchars($payment['receipt_no'] ?: '-') ?></td>
                                            <td>
                                                <?php if (!empty($payment['receipt_no'])): ?>
                                                    <a href="receipt_final.php?receipt_no=<?= urlencode($payment['receipt_no']) ?>" class="btn btn-sm btn-info" title="রিসিপ্ট দেখুন - <?= htmlspecialchars($payment['receipt_no']) ?>">
                                                        <i class="fas fa-receipt"></i> রিসিপ্ট
                                                    </a>
                                                <?php else: ?>
                                                    <span class="btn btn-sm btn-secondary disabled">
                                                        <i class="fas fa-receipt"></i> রিসিপ্ট
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="11" class="text-center">কোন পেমেন্ট রেকর্ড পাওয়া যায়নি</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
                        </div>
                        <!-- End Fee List Tab -->

                        <!-- Add Fee Tab -->
                        <div class="tab-pane fade" id="add-fee" role="tabpanel">
                            <div class="row">
                                <div class="col-md-8">
                                    <h4><i class="fas fa-plus-circle me-2"></i>নতুন ফি যোগ করুন</h4>
                                    <form method="post" action="fee_management.php" id="newFeeForm">
                                        <input type="hidden" name="add_fee" value="1">

                                        <div class="row mb-3">
                                            <div class="col-md-4">
                                                <label class="form-label">সেশন <span class="text-danger">*</span></label>
                                                <select class="form-select" name="session_id" id="new_session_id" required>
                                                    <option value="">সেশন নির্বাচন করুন</option>
                                                    <?php if ($sessionsResult && $sessionsResult->num_rows > 0): ?>
                                                        <?php
                                                        $sessionsResult->data_seek(0);
                                                        while ($session = $sessionsResult->fetch_assoc()):
                                                        ?>
                                                            <option value="<?= $session['id'] ?>">
                                                                <?= htmlspecialchars($session['session_name']) ?>
                                                            </option>
                                                        <?php endwhile; ?>
                                                    <?php endif; ?>
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">শ্রেণী <span class="text-danger">*</span></label>
                                                <select class="form-select" name="class_id" id="new_class_id" required>
                                                    <option value="">শ্রেণী নির্বাচন করুন</option>
                                                    <?php if ($classesResult && $classesResult->num_rows > 0): ?>
                                                        <?php
                                                        $classesResult->data_seek(0);
                                                        while ($class = $classesResult->fetch_assoc()):
                                                        ?>
                                                            <option value="<?= $class['id'] ?>">
                                                                <?= htmlspecialchars($class['class_name']) ?>
                                                            </option>
                                                        <?php endwhile; ?>
                                                    <?php endif; ?>
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">বিভাগ</label>
                                                <select class="form-select" name="department_id" id="new_department_id">
                                                    <option value="">সকল বিভাগ</option>
                                                    <?php
                                                    if ($departmentsResult && $departmentsResult->num_rows > 0) {
                                                        $departmentsResult->data_seek(0);
                                                        while ($department = $departmentsResult->fetch_assoc()):
                                                    ?>
                                                        <option value="<?= $department['id'] ?>">
                                                            <?= htmlspecialchars($department['department_name']) ?>
                                                        </option>
                                                    <?php
                                                        endwhile;
                                                    }
                                                    ?>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label class="form-label">ফি ধরন <span class="text-danger">*</span></label>
                                                <select class="form-select" name="fee_type" id="new_fee_type" required>
                                                    <option value="">ফি ধরন নির্বাচন করুন</option>
                                                    <?php if ($feeTypesResult && $feeTypesResult->num_rows > 0): ?>
                                                        <?php
                                                        $feeTypesResult->data_seek(0);
                                                        while ($feeType = $feeTypesResult->fetch_assoc()):
                                                        ?>
                                                            <option value="<?= htmlspecialchars($feeType['name']) ?>" data-amount="<?= $feeType['amount'] ?>">
                                                                <?= htmlspecialchars($feeType['name']) ?>
                                                                <?php if (!empty($feeType['amount'])): ?>
                                                                    (৳<?= number_format($feeType['amount'], 2) ?>)
                                                                <?php endif; ?>
                                                            </option>
                                                        <?php endwhile; ?>
                                                    <?php endif; ?>
                                                    <option value="other">অন্যান্য (নতুন টাইপ)</option>
                                                </select>
                                            </div>
                                            <div class="col-md-6" id="new_other_fee_type_div" style="display: none;">
                                                <label class="form-label">অন্য ফি ধরন <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" name="other_fee_type" id="new_other_fee_type">
                                            </div>
                                        </div>

                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label class="form-label">পরিমাণ (৳) <span class="text-danger">*</span></label>
                                                <div class="input-group">
                                                    <span class="input-group-text">৳</span>
                                                    <input type="number" class="form-control" name="amount" id="new_amount" min="0" step="0.01" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">তারিখ <span class="text-danger">*</span></label>
                                                <input type="date" class="form-control" name="due_date" id="new_due_date" value="<?= date('Y-m-d') ?>" required>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">শিক্ষার্থী নির্বাচন <span class="text-danger">*</span></label>
                                            <div class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" id="new_select_all_students">
                                                    <label class="form-check-label" for="new_select_all_students">
                                                        <strong>সকল শিক্ষার্থী নির্বাচন করুন</strong>
                                                    </label>
                                                </div>
                                                <hr>
                                                <div id="new_student_list">
                                                    <div class="text-center text-muted">
                                                        <i>সেশন এবং শ্রেণী নির্বাচন করুন</i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="d-flex justify-content-between">
                                            <div id="new-fee-error" class="text-danger" style="display: none;"></div>
                                            <button type="submit" class="btn btn-primary" id="newFeeSubmitBtn">
                                                <i class="fas fa-plus-circle me-1"></i> ফি যোগ করুন
                                            </button>
                                        </div>
                                    </form>
                                </div>
                                <div class="col-md-4">
                                    <div class="card bg-light">
                                        <div class="card-header">
                                            <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>নির্দেশনা</h6>
                                        </div>
                                        <div class="card-body">
                                            <ul class="list-unstyled mb-0">
                                                <li><i class="fas fa-check text-success me-2"></i>প্রথমে সেশন ও শ্রেণী নির্বাচন করুন</li>
                                                <li><i class="fas fa-check text-success me-2"></i>ফি ধরন ও পরিমাণ নির্ধারণ করুন</li>
                                                <li><i class="fas fa-check text-success me-2"></i>শিক্ষার্থী নির্বাচন করুন</li>
                                                <li><i class="fas fa-check text-success me-2"></i>সবশেষে ফি যোগ করুন বাটনে ক্লিক করুন</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- End Add Fee Tab -->

                        <!-- Quick Add Tab -->
                        <div class="tab-pane fade" id="quick-add" role="tabpanel">
                            <h4><i class="fas fa-bolt me-2"></i>দ্রুত ফি যোগ</h4>
                            <p class="text-muted">একক শিক্ষার্থীর জন্য দ্রুত ফি যোগ করুন</p>

                            <form method="post" action="fee_management.php" id="quickFeeForm">
                                <input type="hidden" name="add_fee" value="1">

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0">শিক্ষার্থী তথ্য</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <label class="form-label">শিক্ষার্থী খুঁজুন</label>
                                                    <input type="text" class="form-control" id="quick_student_search" placeholder="নাম বা রোল নম্বর দিয়ে খুঁজুন">
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">শিক্ষার্থী নির্বাচন <span class="text-danger">*</span></label>
                                                    <select class="form-select" name="student_ids[]" id="quick_student_select" required>
                                                        <option value="">শিক্ষার্থী নির্বাচন করুন</option>
                                                        <?php
                                                        // Get all students for quick selection
                                                        $quickStudentsQuery = "SELECT s.*, CONCAT(s.first_name, ' ', s.last_name) as full_name, c.class_name
                                                                             FROM students s
                                                                             LEFT JOIN classes c ON s.class_id = c.id
                                                                             ORDER BY s.first_name, s.last_name LIMIT 100";
                                                        $quickStudentsResult = $conn->query($quickStudentsQuery);
                                                        if ($quickStudentsResult && $quickStudentsResult->num_rows > 0):
                                                            while ($student = $quickStudentsResult->fetch_assoc()):
                                                                $rollNumber = isset($student['roll_number']) ? $student['roll_number'] : (isset($student['student_id']) ? $student['student_id'] : 'N/A');
                                                        ?>
                                                            <option value="<?= $student['id'] ?>" data-name="<?= htmlspecialchars($student['full_name']) ?>" data-class="<?= htmlspecialchars($student['class_name'] ?? 'N/A') ?>">
                                                                <?= htmlspecialchars($student['full_name']) ?> (<?= $rollNumber ?>) - <?= htmlspecialchars($student['class_name'] ?? 'N/A') ?>
                                                            </option>
                                                        <?php
                                                            endwhile;
                                                        endif;
                                                        ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header bg-success text-white">
                                                <h6 class="mb-0">ফি তথ্য</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <label class="form-label">ফি ধরন <span class="text-danger">*</span></label>
                                                    <select class="form-select" name="fee_type" id="quick_fee_type" required>
                                                        <option value="">ফি ধরন নির্বাচন করুন</option>
                                                        <option value="tuition">টিউশন ফি</option>
                                                        <option value="admission">ভর্তি ফি</option>
                                                        <option value="exam">পরীক্ষার ফি</option>
                                                        <option value="library">লাইব্রেরি ফি</option>
                                                        <option value="transport">পরিবহন ফি</option>
                                                        <option value="other">অন্যান্য</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">পরিমাণ (৳) <span class="text-danger">*</span></label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">৳</span>
                                                        <input type="number" class="form-control" name="amount" id="quick_amount" min="0" step="0.01" required>
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">তারিখ <span class="text-danger">*</span></label>
                                                    <input type="date" class="form-control" name="due_date" value="<?= date('Y-m-d') ?>" required>
                                                </div>
                                                <button type="submit" class="btn btn-success w-100">
                                                    <i class="fas fa-bolt me-1"></i> দ্রুত ফি যোগ করুন
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <!-- End Quick Add Tab -->
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Add Fee Modal -->
<div class="modal fade" id="addFeeModal" tabindex="-1" aria-labelledby="addFeeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addFeeModalLabel"><i class="fas fa-plus-circle me-2"></i> ফি যোগ করুন</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="fee_management.php" id="addFeeForm">
                <div class="modal-body">
                    <!-- Basic Filter Section -->
                    <div class="card mb-3">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-filter me-2"></i> ফিল্টার অপশন</h6>
                            <button type="button" class="btn btn-sm btn-link text-decoration-none" id="toggleAddFeeAdvancedSearch">
                                <i class="fas fa-sliders-h me-1"></i> উন্নত ফিল্টার <i class="fas fa-chevron-down ms-1" id="addFeeAdvancedSearchIcon"></i>
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="session_id" class="form-label">সেশন</label>
                                    <select class="form-select" id="session_id" name="session_id">
                                        <option value="">সকল সেশন</option>
                                        <?php if ($sessionsResult && $sessionsResult->num_rows > 0): ?>
                                            <?php
                                            $sessionsResult->data_seek(0);
                                            while ($session = $sessionsResult->fetch_assoc()):
                                            ?>
                                                <option value="<?= $session['id'] ?>">
                                                    <?= htmlspecialchars($session['session_name']) ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="class_id" class="form-label">শ্রেণী</label>
                                    <select class="form-select" id="class_id" name="class_id">
                                        <option value="">সকল শ্রেণী</option>
                                        <?php if ($classesResult && $classesResult->num_rows > 0): ?>
                                            <?php
                                            $classesResult->data_seek(0);
                                            while ($class = $classesResult->fetch_assoc()):
                                            ?>
                                                <option value="<?= $class['id'] ?>">
                                                    <?= htmlspecialchars($class['class_name']) ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="department_id" class="form-label">বিভাগ</label>
                                    <select class="form-select" id="department_id" name="department_id">
                                        <option value="">সকল বিভাগ</option>
                                        <?php
                                        if ($departmentsResult && $departmentsResult->num_rows > 0) {
                                            $departmentsResult->data_seek(0);
                                            while ($department = $departmentsResult->fetch_assoc()):
                                        ?>
                                            <option value="<?= $department['id'] ?>">
                                                <?= htmlspecialchars($department['department_name']) ?>
                                            </option>
                                        <?php
                                            endwhile;
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>

                            <!-- Advanced Filter Options (Initially Hidden) -->
                            <div class="advanced-fee-filter-section" style="display: none;">
                                <hr>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="student_search" class="form-label">শিক্ষার্থী খুঁজুন</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                                            <input type="text" class="form-control" id="student_search" placeholder="নাম বা রোল দিয়ে খুঁজুন">
                                            <button type="button" class="btn btn-outline-secondary clear-input" data-target="student_search">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="student_gender" class="form-label">লিঙ্গ</label>
                                        <select class="form-select" id="student_gender">
                                            <option value="">সকল</option>
                                            <option value="male">ছেলে</option>
                                            <option value="female">মেয়ে</option>
                                            <option value="other">অন্যান্য</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Student Selection Section -->
                    <div class="card mb-3">
                        <div class="card-header bg-light">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="fas fa-users me-2"></i> শিক্ষার্থী নির্বাচন</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="select_all_students">
                                    <label class="form-check-label" for="select_all_students">
                                        সকল শিক্ষার্থী নির্বাচন করুন
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="student-selection-container border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                <div id="student_list">
                                    <div class="text-center text-muted">
                                        <i>সেশন এবং/অথবা শ্রেণী নির্বাচন করুন</i>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-2 text-end">
                                <span class="badge bg-primary" id="selected_students_count">0</span> জন শিক্ষার্থী নির্বাচিত
                            </div>
                        </div>
                    </div>

                    <!-- Fee Details Section -->
                    <div class="card mb-3">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i> ফি বিবরণ</h6>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="fee_type" class="form-label">ফি ধরন <span class="text-danger">*</span></label>
                                    <select class="form-select" id="fee_type" name="fee_type" required>
                                        <option value="">ফি ধরন নির্বাচন করুন</option>
                                        <?php if ($feeTypesResult && $feeTypesResult->num_rows > 0): ?>
                                            <?php
                                            // Reset result pointer to beginning
                                            $feeTypesResult->data_seek(0);
                                            while ($feeType = $feeTypesResult->fetch_assoc()):
                                            ?>
                                                <option value="<?= htmlspecialchars($feeType['name']) ?>" data-amount="<?= $feeType['amount'] ?>">
                                                    <?= htmlspecialchars($feeType['name']) ?>
                                                    <?php if (!empty($feeType['amount'])): ?>
                                                        (৳<?= number_format($feeType['amount'], 2) ?>)
                                                    <?php endif; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                        <option value="other">অন্যান্য (নতুন টাইপ)</option>
                                    </select>
                                </div>
                                <div class="col-md-6" id="other_fee_type_div" style="display: none;">
                                    <label for="other_fee_type" class="form-label">অন্য ফি ধরন <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="other_fee_type" name="other_fee_type">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="category_id" class="form-label">ফি ক্যাটাগরি</label>
                                    <select class="form-select" id="category_id" name="category_id">
                                        <?php if ($categoriesResult && $categoriesResult->num_rows > 0): ?>
                                            <?php
                                            // Reset result pointer to beginning
                                            $categoriesResult->data_seek(0);
                                            while ($category = $categoriesResult->fetch_assoc()):
                                            ?>
                                                <option value="<?= $category['id'] ?>" <?= $category['id'] == 1 ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($category['name']) ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php else: ?>
                                            <option value="1">Default</option>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="due_date" class="form-label">তারিখ <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="due_date" name="due_date" value="<?= date('Y-m-d') ?>" required>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <label for="amount" class="form-label">পরিমাণ (৳) <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text">৳</span>
                                        <input type="number" class="form-control" id="amount" name="amount" min="0" step="0.01" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div id="fee-form-error" class="text-danger w-100 mb-2" style="display: none;"></div>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="submit" name="add_fee" class="btn btn-primary" id="addFeeSubmitBtn">
                        <i class="fas fa-plus-circle me-1"></i> ফি যোগ করুন
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Payment Modal -->
<div class="modal fade" id="addPaymentModal" tabindex="-1" aria-labelledby="addPaymentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="addPaymentModalLabel"><i class="fas fa-money-bill-wave me-2"></i> পেমেন্ট যোগ করুন</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="paymentForm" method="post" action="direct_payment.php">
                <div class="modal-body">
                    <input type="hidden" id="fee_id" name="fee_id">
                    <div class="mb-3">
                        <label class="form-label">মোট পরিমাণ</label>
                        <div class="input-group">
                            <span class="input-group-text">৳</span>
                            <input type="text" class="form-control" id="total_amount" readonly>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">পরিশোধিত</label>
                        <div class="input-group">
                            <span class="input-group-text">৳</span>
                            <input type="text" class="form-control" id="paid_amount" readonly>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">বকেয়া</label>
                        <div class="input-group">
                            <span class="input-group-text">৳</span>
                            <input type="text" class="form-control" id="due_amount" readonly>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="payment_amount" class="form-label">পেমেন্ট পরিমাণ (৳)</label>
                        <input type="number" class="form-control" id="payment_amount" name="payment_amount" min="0" step="0.01" required>
                        <div class="invalid-feedback">
                            পেমেন্ট পরিমাণ বকেয়া পরিমাণের চেয়ে বেশি হতে পারে না!
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="payment_date" class="form-label">পেমেন্ট তারিখ</label>
                        <input type="date" class="form-control" id="payment_date" name="payment_date" value="<?= date('Y-m-d') ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="payment_method" class="form-label">পেমেন্ট পদ্ধতি</label>
                        <select class="form-select" id="payment_method" name="payment_method" required>
                            <option value="cash">নগদ</option>
                            <option value="bkash">বিকাশ</option>
                            <option value="nagad">নগদ (মোবাইল ব্যাংকিং)</option>
                            <option value="rocket">রকেট</option>
                            <option value="bank">ব্যাংক</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="receipt_no" class="form-label">রিসিপ্ট নং</label>
                        <input type="text" class="form-control" id="receipt_no" name="receipt_no">
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">নোট</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="submit" name="add_payment" class="btn btn-success">পেমেন্ট যোগ করুন</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Fee Confirmation Modal -->
<div class="modal fade" id="deleteFeeModal" tabindex="-1" aria-labelledby="deleteFeeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteFeeModalLabel"><i class="fas fa-trash-alt me-2"></i> ফি ডিলেট করুন</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="delete_fee_id" name="delete_fee_id">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i> আপনি কি নিশ্চিত যে আপনি এই ফি ডিলেট করতে চান?
                </div>
                <p>শিক্ষার্থী: <strong id="delete_student_name"></strong></p>
                <p>ফি ধরন: <strong id="delete_fee_type"></strong></p>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i> সতর্কতা: এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না!
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                <button type="button" id="confirmDeleteFeeBtn" class="btn btn-danger">
                    <i class="fas fa-trash-alt me-1"></i> ডিলেট করুন
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Multiple Payments Modal -->
<div class="modal fade" id="multiplePaymentsModal" tabindex="-1" aria-labelledby="multiplePaymentsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="multiplePaymentsModalLabel"><i class="fas fa-money-bill-wave me-2"></i> একাধিক ফি পরিশোধ করুন</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="fee_management.php">
                <div class="modal-body">
                    <input type="hidden" id="multi_student_id" name="student_id">

                    <div class="alert alert-info">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle me-2 fs-4"></i>
                            <div>
                                <strong>শিক্ষার্থী:</strong> <span id="multi_student_name"></span><br>
                                <strong>নির্বাচিত ফি:</strong> <span id="selected_fees_count">0</span>টি
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive mb-3">
                        <table class="table table-bordered table-sm" id="selectedFeesTable">
                            <thead class="table-light">
                                <tr>
                                    <th>ফি ধরন</th>
                                    <th>মোট</th>
                                    <th>পরিশোধিত</th>
                                    <th>বকেয়া</th>
                                    <th>পরিশোধ</th>
                                </tr>
                            </thead>
                            <tbody id="selectedFeesTableBody">
                                <!-- Selected fees will be added here dynamically -->
                            </tbody>
                            <tfoot>
                                <tr class="table-secondary">
                                    <th colspan="4" class="text-end">মোট পরিশোধ:</th>
                                    <th>৳ <span id="total_payment_amount">0.00</span></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="multi_payment_date" class="form-label">পেমেন্ট তারিখ <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="multi_payment_date" name="multi_payment_date" value="<?= date('Y-m-d') ?>" required>
                        </div>
                        <div class="col-md-6">
                            <label for="multi_payment_method" class="form-label">পেমেন্ট পদ্ধতি <span class="text-danger">*</span></label>
                            <select class="form-select" id="multi_payment_method" name="multi_payment_method" required>
                                <option value="cash">নগদ</option>
                                <option value="bkash">বিকাশ</option>
                                <option value="nagad">নগদ (মোবাইল ব্যাংকিং)</option>
                                <option value="rocket">রকেট</option>
                                <option value="bank">ব্যাংক</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="multi_receipt_no" class="form-label">রিসিপ্ট নং</label>
                            <input type="text" class="form-control" id="multi_receipt_no" name="multi_receipt_no" placeholder="স্বয়ংক্রিয়ভাবে তৈরি হবে">
                        </div>
                        <div class="col-md-6">
                            <label for="multi_notes" class="form-label">নোট</label>
                            <textarea class="form-control" id="multi_notes" name="multi_notes" rows="1"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="submit" name="add_multiple_payments" class="btn btn-success" id="submitMultiplePayments" disabled>
                        <i class="fas fa-save me-2"></i> পেমেন্ট যোগ করুন
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Single Fee Payment Modal -->
<div class="modal fade" id="payFeeModal" tabindex="-1" aria-labelledby="payFeeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="payFeeModalLabel">
                    <i class="fas fa-money-bill-wave me-2"></i>ফি পরিশোধ করুন
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="fee_management.php" id="payFeeForm">
                <div class="modal-body">
                    <input type="hidden" name="pay_fee" value="1">
                    <input type="hidden" name="fee_id" id="pay_fee_id">

                    <!-- Student Info -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title mb-2">
                                        <i class="fas fa-user me-2"></i>শিক্ষার্থীর তথ্য
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <strong>নাম:</strong> <span id="pay_student_name"></span>
                                        </div>
                                        <div class="col-md-6">
                                            <strong>ফি ধরন:</strong> <span id="pay_fee_type"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Fee Details -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label">মোট ফি</label>
                            <div class="input-group">
                                <span class="input-group-text">৳</span>
                                <input type="text" class="form-control" id="pay_total_amount" readonly>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">ইতিমধ্যে পরিশোধিত</label>
                            <div class="input-group">
                                <span class="input-group-text">৳</span>
                                <input type="text" class="form-control" id="pay_paid_amount" readonly>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">বকেয়া</label>
                            <div class="input-group">
                                <span class="input-group-text">৳</span>
                                <input type="text" class="form-control" id="pay_due_amount" readonly>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Details -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">পরিশোধের পরিমাণ <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">৳</span>
                                <input type="number" class="form-control" name="payment_amount" id="payment_amount"
                                       min="0" step="0.01" required>
                                <div class="invalid-feedback">
                                    পেমেন্ট পরিমাণ বকেয়া পরিমাণের চেয়ে বেশি হতে পারে না!
                                </div>
                            </div>
                            <div class="form-text">সর্বোচ্চ: <span id="max_payment_amount"></span> টাকা</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">পেমেন্ট পদ্ধতি <span class="text-danger">*</span></label>
                            <select class="form-select" name="payment_method" id="payment_method" required>
                                <option value="">নির্বাচন করুন</option>
                                <option value="cash">নগদ</option>
                                <option value="bank">ব্যাংক ট্রান্সফার</option>
                                <option value="mobile_banking">মোবাইল ব্যাংকিং</option>
                                <option value="card">কার্ড</option>
                                <option value="cheque">চেক</option>
                            </select>
                        </div>
                    </div>

                    <!-- Payment Date -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">পেমেন্ট তারিখ <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" name="payment_date" id="payment_date"
                                   value="<?= date('Y-m-d') ?>" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">রিসিপ্ট নম্বর</label>
                            <input type="text" class="form-control" name="receipt_no" id="receipt_no"
                                   placeholder="স্বয়ংক্রিয় তৈরি হবে">
                        </div>
                    </div>

                    <!-- Transaction Details (conditional) -->
                    <div class="row mb-3" id="transaction_details" style="display: none;">
                        <div class="col-md-6">
                            <label class="form-label">ট্রানজেকশন আইডি</label>
                            <input type="text" class="form-control" name="transaction_id" id="transaction_id">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">রেফারেন্স নম্বর</label>
                            <input type="text" class="form-control" name="reference_number" id="reference_number">
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label">মন্তব্য</label>
                            <textarea class="form-control" name="payment_notes" id="payment_notes" rows="2"
                                      placeholder="অতিরিক্ত মন্তব্য (ঐচ্ছিক)"></textarea>
                        </div>
                    </div>

                    <!-- Payment Summary -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h6 class="card-title mb-2">
                                        <i class="fas fa-calculator me-2"></i>পেমেন্ট সারসংক্ষেপ
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <strong>পরিশোধের পর বকেয়া:</strong>
                                            <span id="remaining_due">৳ 0.00</span>
                                        </div>
                                        <div class="col-md-6">
                                            <strong>স্ট্যাটাস:</strong>
                                            <span id="payment_status_preview">বকেয়া</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>বাতিল
                    </button>
                    <button type="submit" class="btn btn-success" id="payFeeSubmitBtn">
                        <i class="fas fa-money-bill-wave me-1"></i>পেমেন্ট করুন
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Fee Modal -->
<div class="modal fade" id="editFeeModal" tabindex="-1" aria-labelledby="editFeeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="editFeeModalLabel">
                    <i class="fas fa-edit me-2"></i>ফি সম্পাদনা করুন
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="fee_management.php" id="editFeeForm">
                <div class="modal-body">
                    <input type="hidden" name="edit_fee" value="1">
                    <input type="hidden" name="edit_fee_id" id="edit_fee_id">

                    <div class="mb-3">
                        <label class="form-label">শিক্ষার্থী</label>
                        <input type="text" class="form-control" id="edit_student_name" readonly>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">ফি ধরন <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="edit_fee_type" id="edit_fee_type" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">পরিমাণ (৳) <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">৳</span>
                            <input type="number" class="form-control" name="edit_amount" id="edit_amount"
                                   min="0" step="0.01" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">নির্ধারিত তারিখ <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" name="edit_due_date" id="edit_due_date" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>বাতিল
                    </button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save me-1"></i>সংরক্ষণ করুন
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 DOM Content Loaded - Initializing fee management...');

        // Debug: Check if export buttons exist immediately
        setTimeout(function() {
            console.log('🔍 Checking export buttons after timeout...');
            console.log('CSV Button:', document.getElementById('exportCsvBtn'));
            console.log('Excel Button:', document.getElementById('exportExcelBtn'));
            console.log('PDF Button:', document.getElementById('exportPdfBtn'));
            console.log('Print Button:', document.getElementById('exportPrintBtn'));
        }, 1000);

        // Initialize select2 for select elements
        if (typeof $.fn.select2 !== 'undefined') {
            $('#student_id, #fee_ids, #quick_student_id, #quick_fee_type, #bulk_student_id, #student_id_search, #session_id, #class_id, #department_id').select2({
                placeholder: 'নির্বাচন করুন',
                allowClear: true
            });
        }

        // Handle delete fee button clicks
        const deleteFeeButtons = document.querySelectorAll('.delete-fee-btn');
        const deleteFeeModal = new bootstrap.Modal(document.getElementById('deleteFeeModal'));
        const deleteFeeIdInput = document.getElementById('delete_fee_id');
        const deleteStudentNameSpan = document.getElementById('delete_student_name');
        const deleteFeeTypeSpan = document.getElementById('delete_fee_type');
        const confirmDeleteFeeBtn = document.getElementById('confirmDeleteFeeBtn');

        deleteFeeButtons.forEach(button => {
            button.addEventListener('click', function() {
                const feeId = this.getAttribute('data-fee-id');
                const studentName = this.getAttribute('data-student-name');
                const feeType = this.getAttribute('data-fee-type');

                deleteFeeIdInput.value = feeId;
                deleteStudentNameSpan.textContent = studentName;
                deleteFeeTypeSpan.textContent = feeType;

                deleteFeeModal.show();
            });
        });

        // Handle confirm delete button click
        confirmDeleteFeeBtn.addEventListener('click', function() {
            const feeId = deleteFeeIdInput.value;

            // Create form data
            const formData = new FormData();
            formData.append('delete_fee', '1');
            formData.append('delete_fee_id', feeId);

            // Send AJAX request
            fetch('fee_management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                // Reload the page to show the updated list
                window.location.reload();
            })
            .catch(error => {
                console.error('Error:', error);
                alert('ফি ডিলেট করতে সমস্যা হয়েছে। আবার চেষ্টা করুন।');
            });
        });

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // Check if search parameters exist and show search form
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('search') || urlParams.has('fee_type') || urlParams.has('payment_status') ||
            urlParams.has('class_id') || urlParams.has('session_id') || urlParams.has('department_id') ||
            urlParams.has('from_date') || urlParams.has('to_date') || urlParams.has('min_amount') ||
            urlParams.has('max_amount') || urlParams.has('student_id') || urlParams.has('category_id') ||
            urlParams.has('receipt_no') || urlParams.has('sort_by') || urlParams.has('per_page') || urlParams.has('page')) {

            const searchCollapse = document.getElementById('searchCollapse');
            if (searchCollapse) {
                const bsCollapse = new bootstrap.Collapse(searchCollapse, {
                    toggle: true
                });
            }

            // Show advanced search section if advanced parameters are present
            if (urlParams.has('from_date') || urlParams.has('to_date') || urlParams.has('min_amount') ||
                urlParams.has('max_amount') || urlParams.has('student_id') || urlParams.has('category_id') ||
                urlParams.has('receipt_no') || urlParams.has('sort_by') || urlParams.has('per_page') || urlParams.has('page')) {

                const advancedSearchSection = document.querySelector('.advanced-search-section');
                if (advancedSearchSection) {
                    advancedSearchSection.style.display = 'block';
                    const advancedSearchIcon = document.getElementById('advancedSearchIcon');
                    if (advancedSearchIcon) {
                        advancedSearchIcon.classList.remove('fa-chevron-down');
                        advancedSearchIcon.classList.add('fa-chevron-up');
                    }
                }
            }
        }

        // Check if payment search parameters exist and show payment search form
        if (urlParams.has('payment_search') || urlParams.has('payment_fee_type') || urlParams.has('payment_method') ||
            urlParams.has('payment_class_id') || urlParams.has('payment_session_id') || urlParams.has('payment_department_id') ||
            urlParams.has('payment_from_date') || urlParams.has('payment_to_date') || urlParams.has('payment_min_amount') ||
            urlParams.has('payment_max_amount') || urlParams.has('payment_student_id') || urlParams.has('payment_receipt_no') ||
            urlParams.has('payment_sort_by') || urlParams.has('payment_per_page')) {

            const paymentSearchCollapse = document.getElementById('paymentSearchCollapse');
            if (paymentSearchCollapse) {
                const bsCollapse = new bootstrap.Collapse(paymentSearchCollapse, {
                    toggle: true
                });
            }

            // Show advanced search section if advanced parameters are present
            if (urlParams.has('payment_from_date') || urlParams.has('payment_to_date') || urlParams.has('payment_min_amount') ||
                urlParams.has('payment_max_amount') || urlParams.has('payment_student_id') || urlParams.has('payment_receipt_no') ||
                urlParams.has('payment_sort_by') || urlParams.has('payment_per_page')) {

                const paymentAdvancedSearchSection = document.querySelector('.payment-advanced-search-section');
                if (paymentAdvancedSearchSection) {
                    paymentAdvancedSearchSection.style.display = 'block';
                    const paymentAdvancedSearchIcon = document.getElementById('paymentAdvancedSearchIcon');
                    if (paymentAdvancedSearchIcon) {
                        paymentAdvancedSearchIcon.classList.remove('fa-chevron-down');
                        paymentAdvancedSearchIcon.classList.add('fa-chevron-up');
                    }
                }
            }
        }

        // Toggle advanced search sections
        const toggleAdvancedSearch = document.getElementById('toggleAdvancedSearch');
        if (toggleAdvancedSearch) {
            toggleAdvancedSearch.addEventListener('click', function() {
                const advancedSearchSection = document.querySelector('.advanced-search-section');
                const advancedSearchIcon = document.getElementById('advancedSearchIcon');

                if (advancedSearchSection.style.display === 'none') {
                    advancedSearchSection.style.display = 'block';
                    advancedSearchIcon.classList.remove('fa-chevron-down');
                    advancedSearchIcon.classList.add('fa-chevron-up');
                } else {
                    advancedSearchSection.style.display = 'none';
                    advancedSearchIcon.classList.remove('fa-chevron-up');
                    advancedSearchIcon.classList.add('fa-chevron-down');
                }
            });
        }

        const togglePaymentAdvancedSearch = document.getElementById('togglePaymentAdvancedSearch');
        if (togglePaymentAdvancedSearch) {
            togglePaymentAdvancedSearch.addEventListener('click', function() {
                const paymentAdvancedSearchSection = document.querySelector('.payment-advanced-search-section');
                const paymentAdvancedSearchIcon = document.getElementById('paymentAdvancedSearchIcon');

                if (paymentAdvancedSearchSection.style.display === 'none') {
                    paymentAdvancedSearchSection.style.display = 'block';
                    paymentAdvancedSearchIcon.classList.remove('fa-chevron-down');
                    paymentAdvancedSearchIcon.classList.add('fa-chevron-up');
                } else {
                    paymentAdvancedSearchSection.style.display = 'none';
                    paymentAdvancedSearchIcon.classList.remove('fa-chevron-up');
                    paymentAdvancedSearchIcon.classList.add('fa-chevron-down');
                }
            });
        }

        // Clear input buttons
        const clearInputButtons = document.querySelectorAll('.clear-input');
        clearInputButtons.forEach(button => {
            button.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const targetInput = document.getElementById(targetId);
                if (targetInput) {
                    targetInput.value = '';
                }
            });
        });

        // Clear date range buttons
        const clearDateRangeButton = document.querySelector('.clear-date-range');
        if (clearDateRangeButton) {
            clearDateRangeButton.addEventListener('click', function() {
                document.getElementById('fromDateInput').value = '';
                document.getElementById('toDateInput').value = '';
            });
        }

        const clearPaymentDateRangeButton = document.querySelector('.clear-payment-date-range');
        if (clearPaymentDateRangeButton) {
            clearPaymentDateRangeButton.addEventListener('click', function() {
                document.getElementById('paymentFromDateInput').value = '';
                document.getElementById('paymentToDateInput').value = '';
            });
        }

        // Clear amount range buttons
        const clearAmountRangeButton = document.querySelector('.clear-amount-range');
        if (clearAmountRangeButton) {
            clearAmountRangeButton.addEventListener('click', function() {
                document.getElementById('min_amount').value = '';
                document.getElementById('max_amount').value = '';
            });
        }

        const clearPaymentAmountRangeButton = document.querySelector('.clear-payment-amount-range');
        if (clearPaymentAmountRangeButton) {
            clearPaymentAmountRangeButton.addEventListener('click', function() {
                document.getElementById('payment_min_amount').value = '';
                document.getElementById('payment_max_amount').value = '';
            });
        }

        // Form validation
        const feeSearchForm = document.getElementById('feeSearchForm');
        if (feeSearchForm) {
            feeSearchForm.addEventListener('submit', function(e) {
                let isValid = true;

                // Validate date range
                const fromDate = document.getElementById('fromDateInput').value;
                const toDate = document.getElementById('toDateInput').value;

                if (fromDate && toDate && fromDate > toDate) {
                    document.getElementById('dateRangeError').style.display = 'block';
                    isValid = false;
                } else {
                    document.getElementById('dateRangeError').style.display = 'none';
                }

                // Validate amount range
                const minAmount = parseFloat(document.getElementById('min_amount').value) || 0;
                const maxAmount = parseFloat(document.getElementById('max_amount').value) || 0;

                if (minAmount > 0 && maxAmount > 0 && minAmount > maxAmount) {
                    document.getElementById('amountRangeError').style.display = 'block';
                    isValid = false;
                } else {
                    document.getElementById('amountRangeError').style.display = 'none';
                }

                if (!isValid) {
                    e.preventDefault();
                }
            });
        }

        const paymentSearchForm = document.getElementById('paymentSearchForm');
        if (paymentSearchForm) {
            paymentSearchForm.addEventListener('submit', function(e) {
                let isValid = true;

                // Validate date range
                const fromDate = document.getElementById('paymentFromDateInput').value;
                const toDate = document.getElementById('paymentToDateInput').value;

                if (fromDate && toDate && fromDate > toDate) {
                    document.getElementById('paymentDateRangeError').style.display = 'block';
                    isValid = false;
                } else {
                    document.getElementById('paymentDateRangeError').style.display = 'none';
                }

                // Validate amount range
                const minAmount = parseFloat(document.getElementById('payment_min_amount').value) || 0;
                const maxAmount = parseFloat(document.getElementById('payment_max_amount').value) || 0;

                if (minAmount > 0 && maxAmount > 0 && minAmount > maxAmount) {
                    document.getElementById('paymentAmountRangeError').style.display = 'block';
                    isValid = false;
                } else {
                    document.getElementById('paymentAmountRangeError').style.display = 'none';
                }

                if (!isValid) {
                    e.preventDefault();
                }
            });
        }

        // Reset form buttons
        const resetSearchFormBtn = document.getElementById('resetSearchForm');
        if (resetSearchFormBtn) {
            resetSearchFormBtn.addEventListener('click', function() {
                window.location.href = 'fee_management.php';
            });
        }

        const resetPaymentSearchFormBtn = document.getElementById('resetPaymentSearchForm');
        if (resetPaymentSearchFormBtn) {
            resetPaymentSearchFormBtn.addEventListener('click', function() {
                window.location.href = 'fee_management.php';
            });
        }

        // Handle payment button click
        const payFeeButtons = document.querySelectorAll('.pay-fee-btn');
        payFeeButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const feeId = this.getAttribute('data-fee-id');
                const feeAmount = this.getAttribute('data-fee-amount');
                const feePaid = this.getAttribute('data-fee-paid');
                const feeDue = this.getAttribute('data-fee-due');

                document.getElementById('fee_id').value = feeId;
                document.getElementById('total_amount').value = feeAmount;
                document.getElementById('paid_amount').value = feePaid;
                document.getElementById('due_amount').value = feeDue;
                document.getElementById('payment_amount').value = feeDue;
                document.getElementById('payment_amount').max = feeDue;
            });
        });

        // Handle payment form validation
        const paymentForm = document.getElementById('paymentForm');
        if (paymentForm) {
            paymentForm.addEventListener('submit', function(e) {
                // Get form data
                const feeId = document.getElementById('fee_id').value;
                const paymentAmount = document.getElementById('payment_amount').value;

                // Validate inputs
                if (!feeId || !paymentAmount || paymentAmount <= 0) {
                    e.preventDefault();
                    alert('সমস্ত প্রয়োজনীয় ফিল্ড পূরণ করুন!');
                    return false;
                }

                // Show loading message
                const submitBtn = this.querySelector('button[type="submit"]');
                if (submitBtn) {
                    const originalText = submitBtn.innerHTML;
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> প্রক্রিয়াকরণ হচ্ছে...';

                    // Re-enable button after 5 seconds in case of network issues
                    setTimeout(() => {
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = originalText;
                    }, 5000);
                }

                // Form is valid, continue with submission
                return true;
            });
        }

        // Handle fee type selection
        const feeTypeSelect = document.getElementById('fee_type');
        const otherFeeTypeDiv = document.getElementById('other_fee_type_div');
        const otherFeeTypeInput = document.getElementById('other_fee_type');
        const amountInput = document.getElementById('amount');

        // Quick payment form elements
        const quickFeeTypeSelect = document.getElementById('quick_fee_type');
        const quickOtherFeeTypeDiv = document.getElementById('quick_other_fee_type_div');
        const quickOtherFeeTypeInput = document.getElementById('quick_other_fee_type');
        const quickAmountInput = document.getElementById('quick_amount');
        const quickPaymentAmountInput = document.getElementById('quick_payment_amount');

        // Fee type amounts data
        const feeTypeAmounts = {};

        <?php
        if ($feeTypesResult && $feeTypesResult->num_rows > 0) {
            $feeTypesResult->data_seek(0);
            while ($feeType = $feeTypesResult->fetch_assoc()) {
                echo "feeTypeAmounts['" . htmlspecialchars($feeType['name']) . "'] = " . ($feeType['amount'] ?? 0) . ";\n";
            }
        }
        ?>

        // Toggle advanced filter in add fee modal
        const toggleAddFeeAdvancedSearch = document.getElementById('toggleAddFeeAdvancedSearch');
        const addFeeAdvancedSearchIcon = document.getElementById('addFeeAdvancedSearchIcon');
        const advancedFeeFilterSection = document.querySelector('.advanced-fee-filter-section');

        if (toggleAddFeeAdvancedSearch && addFeeAdvancedSearchIcon && advancedFeeFilterSection) {
            toggleAddFeeAdvancedSearch.addEventListener('click', function() {
                if (advancedFeeFilterSection.style.display === 'none') {
                    advancedFeeFilterSection.style.display = 'block';
                    addFeeAdvancedSearchIcon.classList.remove('fa-chevron-down');
                    addFeeAdvancedSearchIcon.classList.add('fa-chevron-up');
                } else {
                    advancedFeeFilterSection.style.display = 'none';
                    addFeeAdvancedSearchIcon.classList.remove('fa-chevron-up');
                    addFeeAdvancedSearchIcon.classList.add('fa-chevron-down');
                }
            });
        }

        // Handle fee type selection in add fee modal
        feeTypeSelect.addEventListener('change', function() {
            if (this.value === 'other') {
                otherFeeTypeDiv.style.display = 'block';
                otherFeeTypeInput.setAttribute('required', 'required');
                amountInput.value = 0;
            } else {
                otherFeeTypeDiv.style.display = 'none';
                otherFeeTypeInput.removeAttribute('required');

                // Set amount based on selected fee type
                if (feeTypeAmounts[this.value] !== undefined) {
                    amountInput.value = feeTypeAmounts[this.value];
                } else {
                    // Try to get amount from data attribute
                    const selectedOption = this.options[this.selectedIndex];
                    const amount = selectedOption.getAttribute('data-amount');
                    if (amount) {
                        amountInput.value = amount;
                    }
                }
            }
        });

        // Handle fee type selection in quick payment modal
        if (quickFeeTypeSelect) {
            quickFeeTypeSelect.addEventListener('change', function() {
                if (this.value === 'other') {
                    quickOtherFeeTypeDiv.style.display = 'block';
                    quickOtherFeeTypeInput.setAttribute('required', 'required');
                    quickAmountInput.value = 0;
                    quickPaymentAmountInput.value = 0;
                } else {
                    quickOtherFeeTypeDiv.style.display = 'none';
                    quickOtherFeeTypeInput.removeAttribute('required');

                    // Set amount based on selected fee type
                    if (feeTypeAmounts[this.value] !== undefined) {
                        const amount = feeTypeAmounts[this.value];
                        quickAmountInput.value = amount;
                        quickPaymentAmountInput.value = amount;
                    }
                }
            });

            // Update payment amount when total amount changes
            quickAmountInput.addEventListener('input', function() {
                quickPaymentAmountInput.value = this.value;
            });
        }

        // Handle session, class, and department selection for filtering students
        const sessionSelect = document.getElementById('session_id');
        const classSelect = document.getElementById('class_id');
        const departmentSelect = document.getElementById('department_id');
        const studentListContainer = document.getElementById('student_list');
        const selectAllCheckbox = document.getElementById('select_all_students');

        // Function to load students based on session, class, and department with additional filters
        function loadStudents() {
            const sessionId = sessionSelect.value;
            const classId = classSelect.value;
            const departmentId = departmentSelect.value;

            // Get additional filter values if they exist
            const studentGender = document.getElementById('student_gender')?.value || '';

            if (!sessionId && !classId && !departmentId) {
                studentListContainer.innerHTML = '<div class="text-center text-muted"><i>সেশন, শ্রেণী এবং/অথবা বিভাগ নির্বাচন করুন</i></div>';
                return;
            }

            // Show loading indicator
            studentListContainer.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> লোড হচ্ছে...</div>';

            // Build query string with all filters
            let queryString = `session_id=${sessionId}&class_id=${classId}&department_id=${departmentId}`;

            // Add additional filters if they have values
            if (studentGender) queryString += `&gender=${studentGender}`;

            // Fetch students using AJAX
            fetch(`ajax/get_students.php?${queryString}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data); // Debug log
                    if (data.success && data.students && data.students.length > 0) {
                        let html = '';
                        data.students.forEach(student => {
                            // Add data attributes for filtering
                            const genderAttr = student.gender ? `data-gender="${student.gender}"` : '';

                            html += `
                                <div class="form-check mb-2">
                                    <input class="form-check-input student-checkbox" type="checkbox"
                                           name="student_ids[]" value="${student.id}" id="student_${student.id}"
                                           ${genderAttr}>
                                    <label class="form-check-label" for="student_${student.id}">
                                        ${student.first_name} ${student.last_name} (${student.student_id}) - ${student.class_name || ''}
                                    </label>
                                </div>
                            `;
                        });
                        studentListContainer.innerHTML = html;

                        // Add event listeners to new checkboxes
                        const studentCheckboxes = document.querySelectorAll('.student-checkbox');
                        studentCheckboxes.forEach(checkbox => {
                            checkbox.addEventListener('change', updateSelectAllCheckbox);
                        });

                        // Update the selected students count
                        updateSelectAllCheckbox();

                        // Apply any existing search filter
                        const searchInput = document.getElementById('student_search');
                        if (searchInput && searchInput.value) {
                            const event = new Event('input');
                            searchInput.dispatchEvent(event);
                        }
                    } else {
                        studentListContainer.innerHTML = '<div class="text-center text-muted"><i>কোন শিক্ষার্থী পাওয়া যায়নি</i></div>';
                        updateSelectAllCheckbox();
                    }
                })
                .catch(error => {
                    console.error('Error loading students:', error);
                    studentListContainer.innerHTML = '<div class="text-center text-danger">শিক্ষার্থী লোড করতে সমস্যা হয়েছে</div>';
                    updateSelectAllCheckbox();
                });

            // Add a timeout to handle potential network issues
            setTimeout(() => {
                if (studentListContainer.innerHTML.includes('লোড হচ্ছে')) {
                    studentListContainer.innerHTML = '<div class="text-center text-warning">কোন শিক্ষার্থী পাওয়া যায়নি। অন্য সেশন/শ্রেণী নির্বাচন করুন।</div>';
                    updateSelectAllCheckbox();
                }
            }, 3000);
        }

        // Function to update select all checkbox state
        function updateSelectAllCheckbox() {
            const studentCheckboxes = document.querySelectorAll('.student-checkbox');
            const checkedCheckboxes = document.querySelectorAll('.student-checkbox:checked');

            if (studentCheckboxes.length === 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.disabled = true;
            } else {
                selectAllCheckbox.disabled = false;
                selectAllCheckbox.checked = studentCheckboxes.length === checkedCheckboxes.length;
            }

            // Update selected students count
            const selectedStudentsCount = document.getElementById('selected_students_count');
            if (selectedStudentsCount) {
                selectedStudentsCount.textContent = checkedCheckboxes.length;
            }
        }

        // Function to filter students based on search input
        const studentSearchInput = document.getElementById('student_search');
        if (studentSearchInput) {
            studentSearchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const studentItems = document.querySelectorAll('.student-checkbox');

                studentItems.forEach(item => {
                    const label = item.nextElementSibling;
                    const studentName = label.textContent.toLowerCase();

                    if (searchTerm === '' || studentName.includes(searchTerm)) {
                        item.closest('.form-check').style.display = 'block';
                    } else {
                        item.closest('.form-check').style.display = 'none';
                    }
                });
            });
        }

        // We'll handle student status filter in the loadStudents function

        // Handle select all checkbox
        selectAllCheckbox.addEventListener('change', function() {
            const studentCheckboxes = document.querySelectorAll('.student-checkbox');
            studentCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });

        // Add event listeners to session, class, and department selects
        sessionSelect.addEventListener('change', loadStudents);
        classSelect.addEventListener('change', loadStudents);
        departmentSelect.addEventListener('change', loadStudents);

        // Add event listeners to additional filter options
        const studentGenderSelect = document.getElementById('student_gender');

        if (studentGenderSelect) studentGenderSelect.addEventListener('change', loadStudents);

        // Initial load if session, class, or department is pre-selected
        if (sessionSelect.value || classSelect.value || departmentSelect.value) {
            loadStudents();
        }

        // Add form validation for add fee form
        const addFeeForm = document.getElementById('addFeeForm');
        const feeFormError = document.getElementById('fee-form-error');
        const addFeeSubmitBtn = document.getElementById('addFeeSubmitBtn');

        if (addFeeForm) {
            addFeeForm.addEventListener('submit', function(e) {
                // ALWAYS prevent default first to debug
                e.preventDefault();

                console.log('🚀 Form submission intercepted - debugging mode');
                console.log('Form element:', addFeeForm);

                // Reset error message
                feeFormError.style.display = 'none';
                feeFormError.textContent = '';

                console.log('✅ Form submission started');

                // Check if at least one student is selected
                const studentCheckboxes = document.querySelectorAll('.student-checkbox:checked');
                console.log('👥 Selected students:', studentCheckboxes.length);

                // Log all student checkboxes for debugging
                const allStudentCheckboxes = document.querySelectorAll('.student-checkbox');
                console.log('📋 Total student checkboxes found:', allStudentCheckboxes.length);

                if (studentCheckboxes.length === 0) {
                    feeFormError.textContent = 'কমপক্ষে একজন শিক্ষার্থী নির্বাচন করুন!';
                    feeFormError.style.display = 'block';
                    console.log('❌ Error: No students selected');
                    return false;
                }

                // Check if fee type is selected
                const feeTypeElement = document.getElementById('fee_type');
                const feeTypeValue = feeTypeElement ? feeTypeElement.value : '';
                console.log('💰 Fee type element:', feeTypeElement);
                console.log('💰 Fee type value:', feeTypeValue);

                if (!feeTypeValue) {
                    feeFormError.textContent = 'ফি ধরন নির্বাচন করুন!';
                    feeFormError.style.display = 'block';
                    console.log('❌ Error: No fee type selected');
                    return false;
                }

                // Check if amount is valid
                const amountElement = document.getElementById('amount');
                const amountValue = amountElement ? parseFloat(amountElement.value) : 0;
                console.log('💵 Amount element:', amountElement);
                console.log('💵 Amount value:', amountValue);

                if (isNaN(amountValue) || amountValue <= 0) {
                    feeFormError.textContent = 'সঠিক পরিমাণ প্রদান করুন!';
                    feeFormError.style.display = 'block';
                    console.log('❌ Error: Invalid amount');
                    return false;
                }

                // Log form data for debugging
                const formData = new FormData(addFeeForm);
                console.log('📝 Form data:');
                for (let [key, value] of formData.entries()) {
                    console.log(`  ${key}: ${value}`);
                }

                // Check if we should actually submit or just debug
                const debugMode = new URLSearchParams(window.location.search).get('debug');
                if (debugMode) {
                    console.log('🐛 DEBUG MODE: Form submission blocked for debugging');
                    console.log('✅ All validations passed! Form would normally submit now.');

                    // Show success message in debug mode
                    feeFormError.style.display = 'block';
                    feeFormError.className = 'alert alert-success';
                    feeFormError.textContent = 'DEBUG: সব validation পাস! Form submit হতো।';

                    return false; // Don't actually submit in debug mode
                }

                // If all validations pass, show loading state and submit
                addFeeSubmitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> প্রক্রিয়াকরণ হচ্ছে...';
                addFeeSubmitBtn.disabled = true;
                console.log('✅ Form submission proceeding...');

                // Actually submit the form
                addFeeForm.submit();
            });
        }

        // Search form enhancements
        const searchForms = document.querySelectorAll('form[action="fee_management.php"]');
        searchForms.forEach(function(searchForm) {
            // Add date range validation for fee search form
            const fromDateInput = searchForm.querySelector('input[name="from_date"]');
            const toDateInput = searchForm.querySelector('input[name="to_date"]');

            if (fromDateInput && toDateInput) {
                // Update min/max attributes when dates change
                fromDateInput.addEventListener('change', function() {
                    if (this.value) {
                        toDateInput.min = this.value;
                    } else {
                        toDateInput.removeAttribute('min');
                    }
                });

                toDateInput.addEventListener('change', function() {
                    if (this.value) {
                        fromDateInput.max = this.value;
                    } else {
                        fromDateInput.removeAttribute('max');
                    }
                });

                // Set initial min/max if values exist
                if (fromDateInput.value) {
                    toDateInput.min = fromDateInput.value;
                }

                if (toDateInput.value) {
                    fromDateInput.max = toDateInput.value;
                }
            }

            // Add date range validation for payment search form
            const paymentFromDateInput = searchForm.querySelector('input[name="payment_from_date"]');
            const paymentToDateInput = searchForm.querySelector('input[name="payment_to_date"]');

            if (paymentFromDateInput && paymentToDateInput) {
                // Update min/max attributes when dates change
                paymentFromDateInput.addEventListener('change', function() {
                    if (this.value) {
                        paymentToDateInput.min = this.value;
                    } else {
                        paymentToDateInput.removeAttribute('min');
                    }
                });

                paymentToDateInput.addEventListener('change', function() {
                    if (this.value) {
                        paymentFromDateInput.max = this.value;
                    } else {
                        paymentFromDateInput.removeAttribute('max');
                    }
                });

                // Set initial min/max if values exist
                if (paymentFromDateInput.value) {
                    paymentToDateInput.min = paymentFromDateInput.value;
                }

                if (paymentToDateInput.value) {
                    paymentFromDateInput.max = paymentToDateInput.value;
                }
            }

            // Add keyboard shortcut for search (Ctrl+Enter)
            searchForm.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'Enter') {
                    searchForm.submit();
                }
            });
        });

        // Enhanced Export Functionality
        function getExportUrl(format, includeFilters = true) {
            console.log('🔧 getExportUrl called with format:', format, 'includeFilters:', includeFilters);
            const urlParams = new URLSearchParams();

            if (includeFilters) {
                // Get current search parameters
                const currentParams = new URLSearchParams(window.location.search);
                console.log('📋 Current URL params:', currentParams.toString());

                // Copy relevant search parameters
                const searchParams = ['search_term', 'fee_type', 'payment_status', 'class_id', 'session_id',
                                    'department_id', 'student_id', 'from_date', 'to_date', 'min_amount', 'max_amount'];

                searchParams.forEach(param => {
                    if (currentParams.has(param)) {
                        urlParams.set(param, currentParams.get(param));
                        console.log('✅ Added param:', param, '=', currentParams.get(param));
                    }
                });
            }

            // Add export parameter
            urlParams.set('export', format);
            console.log('🎯 Final export params:', urlParams.toString());

            const finalUrl = 'fee_management.php?' + urlParams.toString();
            console.log('🌐 Final URL:', finalUrl);
            return finalUrl;
        }

        function showExportProgress(message) {
            // Create progress toast
            const toast = document.createElement('div');
            toast.className = 'toast align-items-center text-white bg-primary border-0';
            toast.style.position = 'fixed';
            toast.style.top = '20px';
            toast.style.right = '20px';
            toast.style.zIndex = '9999';
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-spinner fa-spin me-2"></i>${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;

            document.body.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();

            // Auto remove after 3 seconds
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        // CSV Export
        const exportCsvBtn = document.getElementById('exportCsvBtn');
        console.log('🔍 CSV Export Button:', exportCsvBtn);
        if (exportCsvBtn) {
            exportCsvBtn.addEventListener('click', function() {
                console.log('📄 CSV Export clicked');
                showExportProgress('CSV ফাইল তৈরি হচ্ছে...');
                const exportUrl = getExportUrl('csv');
                console.log('🔗 Export URL:', exportUrl);
                window.open(exportUrl, '_blank');
            });
        } else {
            console.error('❌ CSV Export button not found!');
        }

        // Excel Export (Enhanced CSV with .xlsx extension)
        const exportExcelBtn = document.getElementById('exportExcelBtn');
        console.log('🔍 Excel Export Button:', exportExcelBtn);
        if (exportExcelBtn) {
            exportExcelBtn.addEventListener('click', function() {
                console.log('📊 Excel Export clicked');
                showExportProgress('Excel ফাইল তৈরি হচ্ছে...');
                const exportUrl = getExportUrl('excel');
                console.log('🔗 Export URL:', exportUrl);
                window.open(exportUrl, '_blank');
            });
        } else {
            console.error('❌ Excel Export button not found!');
        }

        // PDF Export
        const exportPdfBtn = document.getElementById('exportPdfBtn');
        if (exportPdfBtn) {
            exportPdfBtn.addEventListener('click', function() {
                showExportProgress('PDF রিপোর্ট তৈরি হচ্ছে...');
                const exportUrl = getExportUrl('pdf');
                window.open(exportUrl, '_blank');
            });
        }

        // Print Export
        const exportPrintBtn = document.getElementById('exportPrintBtn');
        if (exportPrintBtn) {
            exportPrintBtn.addEventListener('click', function() {
                const exportUrl = getExportUrl('print');
                const printWindow = window.open(exportUrl, '_blank');
                printWindow.onload = function() {
                    printWindow.print();
                };
            });
        }

        // Filtered Data Export
        const exportFilteredBtn = document.getElementById('exportFilteredBtn');
        if (exportFilteredBtn) {
            exportFilteredBtn.addEventListener('click', function() {
                showExportProgress('ফিল্টার করা ডেটা এক্সপোর্ট হচ্ছে...');
                const exportUrl = getExportUrl('csv', true);
                window.open(exportUrl, '_blank');
            });
        }

        // All Data Export
        const exportAllBtn = document.getElementById('exportAllBtn');
        if (exportAllBtn) {
            exportAllBtn.addEventListener('click', function() {
                if (confirm('সম্পূর্ণ ডেটাবেস এক্সপোর্ট করতে চান? এটি সময় নিতে পারে।')) {
                    showExportProgress('সম্পূর্ণ ডেটা এক্সপোর্ট হচ্ছে...');
                    const exportUrl = getExportUrl('csv', false);
                    window.open(exportUrl, '_blank');
                }
            });
        }

        // Add memo report functionality
        const memoReportBtn = document.getElementById('memoReportBtn');
        if (memoReportBtn) {
            memoReportBtn.addEventListener('click', function() {
                // Get current search parameters
                const urlParams = new URLSearchParams(window.location.search);

                // Remove pagination parameters for report
                urlParams.delete('page');
                urlParams.delete('per_page');
                urlParams.delete('export');

                // Set report type based on current filters
                if (urlParams.get('student_id')) {
                    urlParams.set('type', 'student_wise');
                } else {
                    urlParams.set('type', 'detailed');
                }

                // Create memo report URL
                const memoUrl = 'fee_memo_report.php?' + urlParams.toString();

                // Open in new tab
                window.open(memoUrl, '_blank');
            });
        }



        // Multiple fee payment functionality
        const feeCheckboxes = document.querySelectorAll('.fee-checkbox');
        const multiPaymentBtn = document.getElementById('multiPaymentBtn');
        const multiplePaymentsModal = document.getElementById('multiplePaymentsModal');
        const selectedFeesTableBody = document.getElementById('selectedFeesTableBody');
        const totalPaymentAmount = document.getElementById('total_payment_amount');
        const selectedFeesCount = document.getElementById('selected_fees_count');
        const multiStudentId = document.getElementById('multi_student_id');
        const multiStudentName = document.getElementById('multi_student_name');
        const submitMultiplePayments = document.getElementById('submitMultiplePayments');

        // Selected fees data
        let selectedFees = [];

        // Function to update the multiple payment button state
        function updateMultiPaymentButton() {
            if (selectedFees.length > 0) {
                multiPaymentBtn.disabled = false;

                // Group fees by student
                const studentGroups = {};
                selectedFees.forEach(fee => {
                    if (!studentGroups[fee.studentId]) {
                        studentGroups[fee.studentId] = {
                            name: fee.studentName,
                            fees: []
                        };
                    }
                    studentGroups[fee.studentId].fees.push(fee);
                });

                // If more than one student is selected, disable the button
                const studentCount = Object.keys(studentGroups).length;
                if (studentCount > 1) {
                    multiPaymentBtn.disabled = true;
                    multiPaymentBtn.title = 'একই শিক্ষার্থীর ফি নির্বাচন করুন';
                } else {
                    multiPaymentBtn.title = 'নির্বাচিত ফি পরিশোধ করুন';
                }
            } else {
                multiPaymentBtn.disabled = true;
                multiPaymentBtn.title = 'ফি নির্বাচন করুন';
            }
        }

        // Function to update the selected fees table
        function updateSelectedFeesTable() {
            if (selectedFees.length === 0) {
                selectedFeesTableBody.innerHTML = '<tr><td colspan="5" class="text-center">কোন ফি নির্বাচন করা হয়নি</td></tr>';
                totalPaymentAmount.textContent = '0.00';
                selectedFeesCount.textContent = '0';
                submitMultiplePayments.disabled = true;
                return;
            }

            let html = '';
            let total = 0;

            selectedFees.forEach((fee, index) => {
                const paymentAmount = parseFloat(fee.due);
                total += paymentAmount;

                html += `
                    <tr>
                        <td>${fee.feeType}</td>
                        <td>৳ ${parseFloat(fee.amount).toFixed(2)}</td>
                        <td>৳ ${parseFloat(fee.paid).toFixed(2)}</td>
                        <td>৳ ${parseFloat(fee.due).toFixed(2)}</td>
                        <td>
                            <div class="input-group input-group-sm">
                                <span class="input-group-text">৳</span>
                                <input type="number" class="form-control payment-amount-input"
                                       name="payment_amounts[${index}]"
                                       value="${paymentAmount.toFixed(2)}"
                                       min="0" max="${fee.due}" step="0.01"
                                       data-index="${index}" required>
                                <input type="hidden" name="fee_ids[${index}]" value="${fee.feeId}">
                            </div>
                        </td>
                    </tr>
                `;
            });

            selectedFeesTableBody.innerHTML = html;
            totalPaymentAmount.textContent = total.toFixed(2);
            selectedFeesCount.textContent = selectedFees.length;
            submitMultiplePayments.disabled = false;

            // Add event listeners to payment amount inputs
            const paymentAmountInputs = document.querySelectorAll('.payment-amount-input');
            paymentAmountInputs.forEach(input => {
                input.addEventListener('input', function() {
                    updateTotalPaymentAmount();
                });
            });
        }

        // Function to update the total payment amount
        function updateTotalPaymentAmount() {
            const paymentAmountInputs = document.querySelectorAll('.payment-amount-input');
            let total = 0;

            paymentAmountInputs.forEach(input => {
                total += parseFloat(input.value) || 0;
            });

            totalPaymentAmount.textContent = total.toFixed(2);
            submitMultiplePayments.disabled = total <= 0;
        }

        // Add event listeners to fee checkboxes
        feeCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const feeId = this.getAttribute('data-fee-id');
                const studentId = this.getAttribute('data-student-id');
                const feeType = this.getAttribute('data-fee-type');
                const amount = parseFloat(this.getAttribute('data-amount'));
                const paid = parseFloat(this.getAttribute('data-paid'));
                const due = parseFloat(this.getAttribute('data-due'));
                const studentName = this.closest('tr').getAttribute('data-student-name');

                if (this.checked) {
                    // Add to selected fees
                    selectedFees.push({
                        feeId,
                        studentId,
                        studentName,
                        feeType,
                        amount,
                        paid,
                        due
                    });
                } else {
                    // Remove from selected fees
                    selectedFees = selectedFees.filter(fee => fee.feeId !== feeId);
                }

                updateMultiPaymentButton();
            });
        });

        // Add event listener to select all checkbox
        const selectAllFees = document.getElementById('selectAllFees');
        if (selectAllFees) {
            selectAllFees.addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('.fee-checkbox');
                checkboxes.forEach(checkbox => {
                    // Only change if the checkbox is not disabled
                    if (!checkbox.disabled) {
                        checkbox.checked = this.checked;

                        // Trigger the change event
                        const event = new Event('change');
                        checkbox.dispatchEvent(event);
                    }
                });
            });
        }

        // Add event listener to multiple payment button
        if (multiPaymentBtn) {
            multiPaymentBtn.addEventListener('click', function() {
                if (selectedFees.length === 0) {
                    return;
                }

                // Get the first student's info (we've already ensured all fees are for the same student)
                const studentId = selectedFees[0].studentId;
                const studentName = selectedFees[0].studentName;

                // Set student info in the modal
                multiStudentId.value = studentId;
                multiStudentName.textContent = studentName;

                // Update the selected fees table
                updateSelectedFeesTable();

                // Show the modal
                const bsModal = new bootstrap.Modal(multiplePaymentsModal);
                bsModal.show();
            });
        }

        // Debug function
        window.showDebugInfo = function() {
            const debugInfo = {
                'Page URL': window.location.href,
                'Current Time': new Date().toLocaleString(),
                'Form Elements': {
                    'Add Fee Form': document.getElementById('addFeeForm') ? 'Found' : 'Not Found',
                    'Fee Type Select': document.getElementById('fee_type') ? 'Found' : 'Not Found',
                    'Amount Input': document.getElementById('amount') ? 'Found' : 'Not Found',
                    'Student Checkboxes': document.querySelectorAll('.student-checkbox').length + ' found'
                },
                'Session/Class Selects': {
                    'Session Select': document.getElementById('session_id') ? 'Found' : 'Not Found',
                    'Class Select': document.getElementById('class_id') ? 'Found' : 'Not Found'
                }
            };

            console.log('🔍 Debug Information:', debugInfo);

            // Show in alert as well
            let alertText = 'Debug Information:\n\n';
            for (const [key, value] of Object.entries(debugInfo)) {
                if (typeof value === 'object') {
                    alertText += key + ':\n';
                    for (const [subKey, subValue] of Object.entries(value)) {
                        alertText += '  ' + subKey + ': ' + subValue + '\n';
                    }
                } else {
                    alertText += key + ': ' + value + '\n';
                }
            }

            alert(alertText);
        };

        // Global debug mode check
        if (window.location.search.includes('debug=1')) {
            console.log('🐛 DEBUG MODE ENABLED');
            console.log('📝 To test form submission without page reload, fill the form and click submit');
            console.log('🔧 Form will be intercepted and debugged instead of submitted');
        }

        console.log('✅ Fee management page loaded successfully');

        // Debug: Check if export buttons exist
        console.log('🔍 Checking export buttons...');
        console.log('CSV Button:', document.getElementById('exportCsvBtn'));
        console.log('Excel Button:', document.getElementById('exportExcelBtn'));
        console.log('PDF Button:', document.getElementById('exportPdfBtn'));
        console.log('Print Button:', document.getElementById('exportPrintBtn'));

        // Initialize new tab functionality
        initializeNewFeeTab();
        initializeQuickFeeTab();
    });

    // New Fee Tab Functions
    function initializeNewFeeTab() {
        const newSessionSelect = document.getElementById('new_session_id');
        const newClassSelect = document.getElementById('new_class_id');
        const newDepartmentSelect = document.getElementById('new_department_id');
        const newStudentList = document.getElementById('new_student_list');
        const newSelectAllCheckbox = document.getElementById('new_select_all_students');
        const newFeeTypeSelect = document.getElementById('new_fee_type');
        const newOtherFeeTypeDiv = document.getElementById('new_other_fee_type_div');
        const newAmountInput = document.getElementById('new_amount');

        if (!newSessionSelect || !newClassSelect || !newStudentList) return;

        // Load students function for new tab
        function loadNewTabStudents() {
            const sessionId = newSessionSelect.value;
            const classId = newClassSelect.value;
            const departmentId = newDepartmentSelect.value;

            if (!sessionId && !classId && !departmentId) {
                newStudentList.innerHTML = '<div class="text-center text-muted"><i>সেশন এবং শ্রেণী নির্বাচন করুন</i></div>';
                return;
            }

            newStudentList.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> লোড হচ্ছে...</div>';

            let queryString = `session_id=${sessionId}&class_id=${classId}&department_id=${departmentId}`;

            fetch(`ajax/get_students.php?${queryString}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.students && data.students.length > 0) {
                        let html = '';
                        data.students.forEach(student => {
                            html += `
                                <div class="form-check mb-2">
                                    <input class="form-check-input new-student-checkbox" type="checkbox"
                                           name="student_ids[]" value="${student.id}" id="new_student_${student.id}">
                                    <label class="form-check-label" for="new_student_${student.id}">
                                        ${student.first_name} ${student.last_name} (${student.student_id}) - ${student.class_name || ''}
                                    </label>
                                </div>
                            `;
                        });
                        newStudentList.innerHTML = html;

                        // Add event listeners
                        const newStudentCheckboxes = document.querySelectorAll('.new-student-checkbox');
                        newStudentCheckboxes.forEach(checkbox => {
                            checkbox.addEventListener('change', updateNewSelectAllCheckbox);
                        });

                        updateNewSelectAllCheckbox();
                    } else {
                        newStudentList.innerHTML = '<div class="text-center text-muted"><i>কোন শিক্ষার্থী পাওয়া যায়নি</i></div>';
                    }
                })
                .catch(error => {
                    console.error('Error loading students:', error);
                    newStudentList.innerHTML = '<div class="text-center text-danger">শিক্ষার্থী লোড করতে সমস্যা হয়েছে</div>';
                });
        }

        // Update select all checkbox
        function updateNewSelectAllCheckbox() {
            const studentCheckboxes = document.querySelectorAll('.new-student-checkbox');
            const checkedCheckboxes = document.querySelectorAll('.new-student-checkbox:checked');

            if (studentCheckboxes.length === 0) {
                newSelectAllCheckbox.checked = false;
                newSelectAllCheckbox.disabled = true;
            } else {
                newSelectAllCheckbox.disabled = false;
                newSelectAllCheckbox.checked = studentCheckboxes.length === checkedCheckboxes.length;
            }
        }

        // Event listeners
        newSessionSelect.addEventListener('change', loadNewTabStudents);
        newClassSelect.addEventListener('change', loadNewTabStudents);
        newDepartmentSelect.addEventListener('change', loadNewTabStudents);

        // Select all functionality
        newSelectAllCheckbox.addEventListener('change', function() {
            const studentCheckboxes = document.querySelectorAll('.new-student-checkbox');
            studentCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });

        // Fee type change
        if (newFeeTypeSelect) {
            newFeeTypeSelect.addEventListener('change', function() {
                if (this.value === 'other') {
                    newOtherFeeTypeDiv.style.display = 'block';
                    document.getElementById('new_other_fee_type').setAttribute('required', 'required');
                } else {
                    newOtherFeeTypeDiv.style.display = 'none';
                    document.getElementById('new_other_fee_type').removeAttribute('required');

                    // Set amount from fee type
                    const selectedOption = this.options[this.selectedIndex];
                    const amount = selectedOption.getAttribute('data-amount');
                    if (amount && newAmountInput) {
                        newAmountInput.value = amount;
                    }
                }
            });
        }

        // Form validation
        const newFeeForm = document.getElementById('newFeeForm');
        if (newFeeForm) {
            newFeeForm.addEventListener('submit', function(e) {
                const selectedStudents = document.querySelectorAll('.new-student-checkbox:checked');
                const errorDiv = document.getElementById('new-fee-error');

                if (selectedStudents.length === 0) {
                    e.preventDefault();
                    errorDiv.textContent = 'কমপক্ষে একজন শিক্ষার্থী নির্বাচন করুন!';
                    errorDiv.style.display = 'block';
                    return false;
                }

                errorDiv.style.display = 'none';
                return true;
            });
        }
    }

    // Quick Fee Tab Functions
    function initializeQuickFeeTab() {
        const quickStudentSearch = document.getElementById('quick_student_search');
        const quickStudentSelect = document.getElementById('quick_student_select');

        if (quickStudentSearch && quickStudentSelect) {
            quickStudentSearch.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const options = quickStudentSelect.options;

                for (let i = 1; i < options.length; i++) {
                    const option = options[i];
                    const text = option.textContent.toLowerCase();

                    if (text.includes(searchTerm)) {
                        option.style.display = '';
                    } else {
                        option.style.display = 'none';
                    }
                }
            });
        }
    }

    // Payment Modal Functions
    function initializePaymentModal() {
        const payFeeButtons = document.querySelectorAll('.pay-fee-btn');
        const editFeeButtons = document.querySelectorAll('.edit-fee-btn');
        const paymentMethodSelect = document.getElementById('payment_method');
        const paymentAmountInput = document.getElementById('payment_amount');
        const transactionDetails = document.getElementById('transaction_details');

        // Pay Fee Button Click
        payFeeButtons.forEach(button => {
            button.addEventListener('click', function() {
                const feeId = this.getAttribute('data-fee-id');
                const studentName = this.getAttribute('data-student-name');
                const feeType = this.getAttribute('data-fee-type');
                const totalAmount = parseFloat(this.getAttribute('data-amount'));
                const paidAmount = parseFloat(this.getAttribute('data-paid'));
                const dueAmount = parseFloat(this.getAttribute('data-due'));

                // Populate modal fields
                document.getElementById('pay_fee_id').value = feeId;
                document.getElementById('pay_student_name').textContent = studentName;
                document.getElementById('pay_fee_type').textContent = feeType;
                document.getElementById('pay_total_amount').value = totalAmount.toFixed(2);
                document.getElementById('pay_paid_amount').value = paidAmount.toFixed(2);
                document.getElementById('pay_due_amount').value = dueAmount.toFixed(2);
                document.getElementById('payment_amount').value = dueAmount.toFixed(2);
                document.getElementById('payment_amount').max = dueAmount;
                document.getElementById('max_payment_amount').textContent = '৳' + dueAmount.toFixed(2);

                // Update payment summary
                updatePaymentSummary();

                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('payFeeModal'));
                modal.show();
            });
        });

        // Edit Fee Button Click
        editFeeButtons.forEach(button => {
            button.addEventListener('click', function() {
                const feeId = this.getAttribute('data-fee-id');
                const studentName = this.getAttribute('data-student-name');
                const feeType = this.getAttribute('data-fee-type');
                const amount = this.getAttribute('data-amount');
                const dueDate = this.getAttribute('data-due-date');

                // Populate modal fields
                document.getElementById('edit_fee_id').value = feeId;
                document.getElementById('edit_student_name').value = studentName;
                document.getElementById('edit_fee_type').value = feeType;
                document.getElementById('edit_amount').value = amount;
                document.getElementById('edit_due_date').value = dueDate;

                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('editFeeModal'));
                modal.show();
            });
        });

        // Payment Method Change
        if (paymentMethodSelect) {
            paymentMethodSelect.addEventListener('change', function() {
                if (this.value === 'bank' || this.value === 'mobile_banking' || this.value === 'card') {
                    transactionDetails.style.display = 'block';
                } else {
                    transactionDetails.style.display = 'none';
                }
            });
        }

        // Payment Amount Change
        if (paymentAmountInput) {
            paymentAmountInput.addEventListener('input', updatePaymentSummary);
        }

        function updatePaymentSummary() {
            const dueAmount = parseFloat(document.getElementById('pay_due_amount').value) || 0;
            const paymentAmount = parseFloat(document.getElementById('payment_amount').value) || 0;
            const remainingDue = Math.max(0, dueAmount - paymentAmount);

            document.getElementById('remaining_due').textContent = '৳' + remainingDue.toFixed(2);

            let status = 'বকেয়া';
            if (remainingDue === 0) {
                status = 'পরিশোধিত';
            } else if (paymentAmount > 0) {
                status = 'আংশিক';
            }

            document.getElementById('payment_status_preview').textContent = status;

            // Validate payment amount
            const paymentInput = document.getElementById('payment_amount');
            const submitButton = document.querySelector('#paymentModal .btn-primary[type="submit"]');

            if (paymentAmount > dueAmount) {
                paymentInput.classList.add('is-invalid');
                if (submitButton) submitButton.disabled = true;
            } else {
                paymentInput.classList.remove('is-invalid');
                if (submitButton) submitButton.disabled = false;
            }
        }
    }

    // Initialize payment modal when page loads
    initializePaymentModal();

    // Simple payment confirmation
    function confirmPayment(form) {
        const amount = parseFloat(form.querySelector('input[name="payment_amount"]').value);
        const maxAmount = parseFloat(form.querySelector('input[name="max_amount"]').value);

        if (isNaN(amount) || amount <= 0) {
            alert('অনুগ্রহ করে একটি বৈধ পেমেন্ট পরিমাণ লিখুন!');
            return false;
        }

        if (amount > maxAmount) {
            alert('পেমেন্ট পরিমাণ বকেয়া পরিমাণের চেয়ে বেশি হতে পারে না!\nবকেয়া: ৳' + maxAmount.toFixed(2) + '\nআপনার পরিমাণ: ৳' + amount.toFixed(2));
            return false;
        }

        return confirm('আপনি কি ৳' + amount.toFixed(2) + ' টাকা পেমেন্ট করতে চান?');
    }
</script>





<?php
// Include footer
include_once 'includes/footer.php';
?>


