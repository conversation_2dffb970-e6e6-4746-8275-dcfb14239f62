<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$debug_output = '';

// Handle CSV debug upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['debug_csv'])) {
    if (isset($_FILES['csv_file']) && $_FILES['csv_file']['error'] === UPLOAD_ERR_OK) {
        $debug_output .= "<h4>📁 ফাইল তথ্য:</h4>";
        $debug_output .= "<ul>";
        $debug_output .= "<li><strong>নাম:</strong> " . $_FILES['csv_file']['name'] . "</li>";
        $debug_output .= "<li><strong>সাইজ:</strong> " . $_FILES['csv_file']['size'] . " bytes</li>";
        $debug_output .= "<li><strong>টাইপ:</strong> " . $_FILES['csv_file']['type'] . "</li>";
        $debug_output .= "<li><strong>ত্রুটি কোড:</strong> " . $_FILES['csv_file']['error'] . "</li>";
        $debug_output .= "</ul>";

        $csv_file = $_FILES['csv_file']['tmp_name'];
        
        // Read file content and handle UTF-8 BOM
        $content = file_get_contents($csv_file);
        if (substr($content, 0, 3) === "\xEF\xBB\xBF") {
            $content = substr($content, 3); // Remove UTF-8 BOM
            $debug_output .= "<div class='alert alert-info'>✅ UTF-8 BOM সরানো হয়েছে</div>";
        }
        
        // Create temporary file with cleaned content
        $temp_file = tempnam(sys_get_temp_dir(), 'csv_debug');
        file_put_contents($temp_file, $content);
        
        $handle = fopen($temp_file, 'r');
        
        if ($handle !== FALSE) {
            $headers = fgetcsv($handle); // Read headers
            
            // Clean BOM from first header if present
            if (!empty($headers[0])) {
                $original_header = $headers[0];
                $headers[0] = preg_replace('/^\xEF\xBB\xBF/', '', $headers[0]);
                $headers[0] = trim($headers[0], "\xEF\xBB\xBF\x00..\x20");
                if ($original_header !== $headers[0]) {
                    $debug_output .= "<div class='alert alert-warning'>⚠️ প্রথম হেডার পরিষ্কার করা হয়েছে: '$original_header' → '{$headers[0]}'</div>";
                }
            }
            
            $debug_output .= "<h4>📋 CSV হেডার (" . count($headers) . "টি কলাম):</h4>";
            $debug_output .= "<div class='table-responsive'>";
            $debug_output .= "<table class='table table-sm table-bordered'>";
            $debug_output .= "<tr>";
            foreach ($headers as $index => $header) {
                $debug_output .= "<th>$index: " . htmlspecialchars($header) . "</th>";
            }
            $debug_output .= "</tr></table></div>";
            
            $debug_output .= "<h4>📊 CSV ডেটা (প্রথম ৩ সারি):</h4>";
            $row_number = 1;
            $data_rows = 0;
            
            while (($data = fgetcsv($handle)) !== FALSE && $data_rows < 3) {
                $row_number++;
                $data_rows++;
                
                $debug_output .= "<h5>সারি $row_number (" . count($data) . "টি কলাম):</h5>";
                
                // Map data to expected fields
                $student_id = !empty(trim($data[0])) ? trim($data[0]) : '';
                $roll_number = !empty(trim($data[1])) ? trim($data[1]) : '';
                $first_name = !empty(trim($data[2])) ? trim($data[2]) : '';
                $last_name = !empty(trim($data[3])) ? trim($data[3]) : '';
                $email = !empty(trim($data[4])) ? trim($data[4]) : '';
                $phone = !empty(trim($data[5])) ? trim($data[5]) : '';
                $address = !empty(trim($data[6])) ? trim($data[6]) : '';
                $dob = !empty(trim($data[7])) ? trim($data[7]) : '';
                $gender = !empty(trim($data[8])) ? trim($data[8]) : '';
                $batch = !empty(trim($data[9])) ? trim($data[9]) : '';
                $admission_date = !empty(trim($data[10])) ? trim($data[10]) : '';
                $department_id = !empty(trim($data[11])) ? intval($data[11]) : 0;
                $username = !empty(trim($data[14])) ? trim($data[14]) : '';
                $password = !empty(trim($data[15])) ? trim($data[15]) : '';
                
                $mandatory_fields = [
                    'student_id' => $student_id,
                    'first_name' => $first_name,
                    'last_name' => $last_name,
                    'phone' => $phone,
                    'address' => $address,
                    'dob' => $dob,
                    'gender' => $gender,
                    'batch' => $batch,
                    'admission_date' => $admission_date,
                    'department_id' => $department_id,
                    'username' => $username,
                    'password' => $password
                ];
                
                $debug_output .= "<div class='table-responsive'>";
                $debug_output .= "<table class='table table-sm table-bordered'>";
                $debug_output .= "<tr><th>ফিল্ড</th><th>মান</th><th>স্ট্যাটাস</th></tr>";
                
                foreach ($mandatory_fields as $field_name => $field_value) {
                    $status = empty($field_value) ? "<span class='badge bg-danger'>খালি</span>" : "<span class='badge bg-success'>ঠিক আছে</span>";
                    $display_value = empty($field_value) ? '<em>খালি</em>' : htmlspecialchars(substr($field_value, 0, 50));
                    $debug_output .= "<tr><td><strong>$field_name</strong></td><td>$display_value</td><td>$status</td></tr>";
                }
                
                $debug_output .= "</table></div>";
                
                // Check validation
                $missing_fields = [];
                foreach ($mandatory_fields as $field_name => $field_value) {
                    if (empty($field_value)) {
                        $missing_fields[] = $field_name;
                    }
                }
                
                if (!empty($missing_fields)) {
                    $debug_output .= "<div class='alert alert-danger'>";
                    $debug_output .= "<strong>❌ ভ্যালিডেশন ব্যর্থ:</strong> " . implode(', ', $missing_fields) . " ফিল্ড খালি";
                    $debug_output .= "</div>";
                } else {
                    $debug_output .= "<div class='alert alert-success'>";
                    $debug_output .= "<strong>✅ ভ্যালিডেশন সফল:</strong> সব প্রয়োজনীয় ফিল্ড পূরণ করা আছে";
                    $debug_output .= "</div>";
                }
            }
            
            fclose($handle);
            unlink($temp_file);
        } else {
            $debug_output .= "<div class='alert alert-danger'>❌ CSV ফাইল পড়তে সমস্যা হয়েছে।</div>";
        }
    } else {
        $debug_output .= "<div class='alert alert-danger'>❌ ফাইল আপলোড করতে সমস্যা হয়েছে।</div>";
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>CSV ডিবাগ টুল - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/hind-siliguri.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include('includes/sidebar.php'); ?>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">🔍 CSV ডিবাগ টুল</h1>
                    <div>
                        <a href="add_student.php" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-1"></i> ফিরে যান
                        </a>
                    </div>
                </div>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>এই টুলটি ব্যবহার করুন:</strong> CSV ফাইলে কী সমস্যা আছে তা বিস্তারিত দেখার জন্য।
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5>CSV ফাইল ডিবাগ আপলোড</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="csv_file" class="form-label">CSV ফাইল নির্বাচন করুন</label>
                                <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                            </div>
                            <button type="submit" name="debug_csv" class="btn btn-warning">
                                <i class="fas fa-search me-2"></i>ডিবাগ বিশ্লেষণ
                            </button>
                        </form>
                    </div>
                </div>
                
                <?php if (!empty($debug_output)): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>🔍 ডিবাগ ফলাফল</h5>
                    </div>
                    <div class="card-body">
                        <?php echo $debug_output; ?>
                    </div>
                </div>
                <?php endif; ?>
                
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
