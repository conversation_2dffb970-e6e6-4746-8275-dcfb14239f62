<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Get server IP address
function getServerIP() {
    $ip = $_SERVER['SERVER_ADDR'];
    if ($ip == '::1' || $ip == '127.0.0.1') {
        // Get local IP address
        $output = shell_exec('ipconfig');
        preg_match_all('/IPv4 Address[.\s]+: ([0-9.]+)/', $output, $matches);
        if (isset($matches[1][0])) {
            return $matches[1][0];
        }
    }
    return $ip;
}

$serverIP = getServerIP();
$projectPath = dirname(dirname(__FILE__)); // Get project root directory
$xamppPath = 'D:/xampp'; // Default XAMPP path

// Handle form submission
$message = '';
$error = '';
$setupComplete = false;

if (isset($_POST['setup_server'])) {
    try {
        // Create batch file content
        $batchContent = "@echo off\n";
        $batchContent .= "echo ===================================================\n";
        $batchContent .= "echo        কলেজ ম্যানেজমেন্ট সিস্টেম সার্ভার সেটআপ\n";
        $batchContent .= "echo ===================================================\n";
        $batchContent .= "echo.\n\n";
        
        $batchContent .= "echo সার্ভার সেটআপ শুরু হচ্ছে...\n";
        $batchContent .= "echo.\n\n";
        
        // Update Apache configuration
        $batchContent .= "echo Apache কনফিগারেশন আপডেট করা হচ্ছে...\n";
        $batchContent .= "set APACHE_CONF=$xamppPath\\apache\\conf\\httpd.conf\n\n";
        
        // Create backup of original config
        $batchContent .= "copy \"%APACHE_CONF%\" \"%APACHE_CONF%.bak\" >nul\n\n";
        
        // Update Listen directive
        $batchContent .= "powershell -Command \"(Get-Content '%APACHE_CONF%') -replace 'Listen 80', 'Listen 0.0.0.0:80' | Set-Content '%APACHE_CONF%'\"\n\n";
        
        // Update ServerName directive
        $batchContent .= "powershell -Command \"(Get-Content '%APACHE_CONF%') -replace '#ServerName www.example.com:80', 'ServerName localhost' | Set-Content '%APACHE_CONF%'\"\n\n";
        
        $batchContent .= "echo Apache কনফিগারেশন আপডেট করা হয়েছে।\n";
        $batchContent .= "echo.\n\n";
        
        // Create firewall rules
        $batchContent .= "echo ফায়ারওয়াল রুল তৈরি করা হচ্ছে...\n";
        $batchContent .= "netsh advfirewall firewall add rule name=\"Apache HTTP Server\" dir=in action=allow protocol=TCP localport=80 >nul\n";
        $batchContent .= "netsh advfirewall firewall add rule name=\"Apache HTTPS Server\" dir=in action=allow protocol=TCP localport=443 >nul\n";
        $batchContent .= "netsh advfirewall firewall add rule name=\"MySQL Server\" dir=in action=allow protocol=TCP localport=3306 >nul\n";
        $batchContent .= "echo ফায়ারওয়াল রুল তৈরি করা হয়েছে।\n";
        $batchContent .= "echo.\n\n";
        
        // Restart Apache
        $batchContent .= "echo Apache সার্ভার রিস্টার্ট করা হচ্ছে...\n";
        $batchContent .= "net stop Apache2.4 >nul 2>&1\n";
        $batchContent .= "timeout /t 2 /nobreak >nul\n";
        $batchContent .= "net start Apache2.4 >nul 2>&1\n";
        $batchContent .= "echo Apache সার্ভার রিস্টার্ট করা হয়েছে।\n";
        $batchContent .= "echo.\n\n";
        
        // Create start_server.bat
        $startServerContent = "@echo off\n";
        $startServerContent .= "echo XAMPP সার্ভার স্টার্ট করা হচ্ছে...\n";
        $startServerContent .= "cd /d $xamppPath\n";
        $startServerContent .= "start /min xampp-control.exe\n";
        $startServerContent .= "timeout /t 5 /nobreak > nul\n";
        $startServerContent .= "start /min xampp_start.exe\n";
        $startServerContent .= "timeout /t 10 /nobreak > nul\n";
        $startServerContent .= "echo সার্ভার স্টার্ট হয়েছে!\n";
        $startServerContent .= "echo আপনার প্রজেক্ট এখন নিম্নলিখিত URL-এ অ্যাক্সেসযোগ্য:\n";
        $startServerContent .= "echo - লোকাল: http://localhost/zfaw\n";
        $startServerContent .= "echo - নেটওয়ার্ক: http://%computername%/zfaw\n";
        $startServerContent .= "ipconfig | findstr IPv4\n";
        $startServerContent .= "echo.\n";
        $startServerContent .= "start http://localhost/zfaw\n";
        $startServerContent .= "echo যে কোন কী চাপুন বন্ধ করতে...\n";
        $startServerContent .= "pause > nul\n";
        
        // Create stop_server.bat
        $stopServerContent = "@echo off\n";
        $stopServerContent .= "echo XAMPP সার্ভার বন্ধ করা হচ্ছে...\n";
        $stopServerContent .= "cd /d $xamppPath\n";
        $stopServerContent .= "start /min xampp_stop.exe\n";
        $stopServerContent .= "timeout /t 5 /nobreak > nul\n";
        $stopServerContent .= "echo সার্ভার বন্ধ হয়েছে!\n";
        $stopServerContent .= "echo.\n";
        $stopServerContent .= "echo যে কোন কী চাপুন বন্ধ করতে...\n";
        $stopServerContent .= "pause > nul\n";
        
        // Create network access instructions
        $readmeContent = "# নেটওয়ার্ক অ্যাক্সেস নির্দেশাবলী\n\n";
        $readmeContent .= "আপনার প্রজেক্ট এখন অন্য কম্পিউটার থেকে অ্যাক্সেস করা যাবে। নিচের নির্দেশাবলী অনুসরণ করুন।\n\n";
        $readmeContent .= "## সার্ভার স্টার্ট/স্টপ করা\n\n";
        $readmeContent .= "- সার্ভার স্টার্ট করতে: `start_server.bat` ফাইলে ডাবল-ক্লিক করুন\n";
        $readmeContent .= "- সার্ভার স্টপ করতে: `stop_server.bat` ফাইলে ডাবল-ক্লিক করুন\n\n";
        $readmeContent .= "## অন্য কম্পিউটার থেকে অ্যাক্সেস করা\n\n";
        $readmeContent .= "1. নিশ্চিত করুন যে উভয় কম্পিউটার একই নেটওয়ার্কে সংযুক্ত আছে (একই WiFi বা LAN)\n";
        $readmeContent .= "2. আপনার কম্পিউটারের IP অ্যাড্রেস জানতে `start_server.bat` ফাইল চালান এবং আউটপুট দেখুন\n";
        $readmeContent .= "3. অন্য কম্পিউটারের ব্রাউজারে এই URL টাইপ করুন: `http://$serverIP/zfaw`\n\n";
        $readmeContent .= "## সমস্যা সমাধান\n\n";
        $readmeContent .= "যদি অন্য কম্পিউটার থেকে অ্যাক্সেস করতে না পারেন:\n\n";
        $readmeContent .= "1. উভয় কম্পিউটার একই নেটওয়ার্কে আছে কিনা নিশ্চিত করুন\n";
        $readmeContent .= "2. Windows ফায়ারওয়াল Apache-কে ব্লক করছে কিনা চেক করুন\n";
        $readmeContent .= "3. XAMPP কন্ট্রোল প্যানেল থেকে Apache এবং MySQL সার্ভিস রিস্টার্ট করুন\n\n";
        $readmeContent .= "## সিকিউরিটি টিপস\n\n";
        $readmeContent .= "1. অ্যাডমিন অ্যাকাউন্টের জন্য শক্তিশালী পাসওয়ার্ড ব্যবহার করুন\n";
        $readmeContent .= "2. নিয়মিত ব্যাকআপ নিন\n";
        $readmeContent .= "3. অপ্রয়োজনীয় সময়ে সার্ভার বন্ধ রাখুন\n";
        
        // Save batch files
        $setupBatchPath = $projectPath . '/server_setup.bat';
        $startServerPath = $projectPath . '/start_server.bat';
        $stopServerPath = $projectPath . '/stop_server.bat';
        $readmePath = $projectPath . '/NETWORK_ACCESS.md';
        
        file_put_contents($setupBatchPath, $batchContent);
        file_put_contents($startServerPath, $startServerContent);
        file_put_contents($stopServerPath, $stopServerContent);
        file_put_contents($readmePath, $readmeContent);
        
        // Execute the setup batch file
        $command = "start /wait $setupBatchPath";
        pclose(popen($command, 'r'));
        
        $setupComplete = true;
        $message = "সার্ভার সেটআপ সম্পন্ন হয়েছে! আপনার প্রজেক্ট এখন নিম্নলিখিত URL-এ অ্যাক্সেসযোগ্য:<br>";
        $message .= "- লোকাল: <a href='http://localhost/zfaw' target='_blank'>http://localhost/zfaw</a><br>";
        $message .= "- নেটওয়ার্ক: <a href='http://$serverIP/zfaw' target='_blank'>http://$serverIP/zfaw</a><br>";
        $message .= "সার্ভার স্টার্ট/স্টপ করার জন্য প্রজেক্ট ফোল্ডারে তৈরি করা start_server.bat এবং stop_server.bat ফাইল ব্যবহার করুন।";
        
    } catch (Exception $e) {
        $error = "সমস্যা হয়েছে: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>সার্ভার সেটআপ - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include_once '../includes/admin_sidebar.php'; ?>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>সার্ভার সেটআপ</h2>
                        <p class="text-muted">আপনার প্রজেক্ট নেটওয়ার্কে অ্যাক্সেসযোগ্য করুন</p>
                    </div>
                </div>

                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($message)): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">সার্ভার সেটআপ</h5>
                            </div>
                            <div class="card-body">
                                <?php if ($setupComplete): ?>
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle me-2"></i>
                                        <strong>সার্ভার সেটআপ সম্পন্ন হয়েছে!</strong>
                                    </div>
                                    
                                    <p>আপনার প্রজেক্ট এখন নিম্নলিখিত URL-এ অ্যাক্সেসযোগ্য:</p>
                                    <ul>
                                        <li>লোকাল: <a href="http://localhost/zfaw" target="_blank">http://localhost/zfaw</a></li>
                                        <li>নেটওয়ার্ক: <a href="http://<?php echo $serverIP; ?>/zfaw" target="_blank">http://<?php echo $serverIP; ?>/zfaw</a></li>
                                    </ul>
                                    
                                    <p>এক-ক্লিক সার্ভার স্টার্ট/স্টপ:</p>
                                    <ul>
                                        <li>সার্ভার স্টার্ট করতে: <code>start_server.bat</code> ফাইলে ডাবল-ক্লিক করুন</li>
                                        <li>সার্ভার স্টপ করতে: <code>stop_server.bat</code> ফাইলে ডাবল-ক্লিক করুন</li>
                                    </ul>
                                    
                                    <div class="mt-3">
                                        <button class="btn btn-warning" onclick="if(confirm('আপনি কি নিশ্চিত যে আপনি সার্ভার সেটআপ পুনরায় করতে চান?')) document.getElementById('setup_form').submit();">
                                            <i class="fas fa-redo-alt"></i> সার্ভার সেটআপ পুনরায় করুন
                                        </button>
                                    </div>
                                <?php else: ?>
                                    <p>এই টুলটি আপনার প্রজেক্টকে নেটওয়ার্কে অ্যাক্সেসযোগ্য করবে, যাতে অন্য কম্পিউটার থেকে আপনার প্রজেক্ট অ্যাক্সেস করা যায়।</p>
                                    
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>প্রয়োজনীয়তা:</strong>
                                        <ul class="mb-0">
                                            <li>XAMPP ইনস্টল করা থাকতে হবে</li>
                                            <li>Apache এবং MySQL সার্ভিস চালু থাকতে হবে</li>
                                            <li>অ্যাডমিনিস্ট্রেটর অধিকার প্রয়োজন</li>
                                        </ul>
                                    </div>
                                    
                                    <form id="setup_form" method="POST" action="server_setup.php">
                                        <div class="d-grid gap-2">
                                            <button type="submit" name="setup_server" class="btn btn-primary btn-lg">
                                                <i class="fas fa-server me-2"></i>সার্ভার সেটআপ করুন
                                            </button>
                                        </div>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0">অন্য কম্পিউটার থেকে অ্যাক্সেস করার নির্দেশাবলী</h5>
                            </div>
                            <div class="card-body">
                                <h6><i class="fas fa-network-wired me-2"></i>একই নেটওয়ার্কে অ্যাক্সেস করা:</h6>
                                <ol>
                                    <li>নিশ্চিত করুন যে উভয় কম্পিউটার একই নেটওয়ার্কে সংযুক্ত আছে (একই WiFi বা LAN)</li>
                                    <li>অন্য কম্পিউটারের ব্রাউজারে এই URL টাইপ করুন: <code>http://<?php echo $serverIP; ?>/zfaw</code></li>
                                    <li>আপনার প্রজেক্ট লোড হওয়া উচিত</li>
                                </ol>
                                
                                <h6><i class="fas fa-shield-alt me-2"></i>সিকিউরিটি টিপস:</h6>
                                <ul>
                                    <li>অ্যাডমিন অ্যাকাউন্টের জন্য শক্তিশালী পাসওয়ার্ড ব্যবহার করুন</li>
                                    <li>নিয়মিত ব্যাকআপ নিন</li>
                                    <li>অপ্রয়োজনীয় সময়ে সার্ভার বন্ধ রাখুন</li>
                                </ul>
                                
                                <div class="alert alert-warning mt-3">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>সতর্কতা:</strong> আপনার সার্ভার নেটওয়ার্কে অ্যাক্সেসযোগ্য করার পর, একই নেটওয়ার্কের যে কেউ আপনার প্রজেক্ট অ্যাক্সেস করতে পারবে। নিরাপত্তা নিশ্চিত করতে শক্তিশালী পাসওয়ার্ড ব্যবহার করুন।
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
