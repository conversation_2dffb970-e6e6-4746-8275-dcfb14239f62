<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'অননুমোদিত প্রবেশ']);
    exit();
}

require_once '../includes/dbh.inc.php';

// Set content type to JSON
header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('অবৈধ অনুরোধ পদ্ধতি');
    }
    
    // Get form data
    $class_level = trim($_POST['class_level'] ?? '');
    $class_level_name = trim($_POST['class_level_name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $default_total_marks = floatval($_POST['default_total_marks'] ?? 100);
    $default_cq_marks = floatval($_POST['default_cq_marks'] ?? 0);
    $default_mcq_marks = floatval($_POST['default_mcq_marks'] ?? 0);
    $default_practical_marks = floatval($_POST['default_practical_marks'] ?? 0);
    $has_cq = isset($_POST['has_cq']) ? 1 : 0;
    $has_mcq = isset($_POST['has_mcq']) ? 1 : 0;
    $has_practical = isset($_POST['has_practical']) ? 1 : 0;
    
    // Validate required fields
    if (empty($class_level) || empty($class_level_name)) {
        throw new Exception('টেমপ্লেট আইডি এবং নাম অবশ্যই পূরণ করতে হবে');
    }
    
    // Validate total marks
    if ($default_total_marks <= 0) {
        throw new Exception('মোট নম্বর অবশ্যই ০ এর চেয়ে বেশি হতে হবে');
    }
    
    // Validate marks distribution
    $total_calculated = $default_cq_marks + $default_mcq_marks + $default_practical_marks;
    if (abs($total_calculated - $default_total_marks) > 0.01) {
        throw new Exception("মোট নম্বরের সাথে উপাদানগুলির যোগফল মিলছে না। মোট: $default_total_marks, যোগফল: $total_calculated");
    }
    
    // Check if template ID already exists
    $checkQuery = "SELECT id FROM class_level_templates WHERE class_level = ?";
    $stmt = $conn->prepare($checkQuery);
    $stmt->bind_param("s", $class_level);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        throw new Exception('এই টেমপ্লেট আইডি ইতিমধ্যে বিদ্যমান');
    }
    
    // Calculate percentages
    $cq_percentage = $default_total_marks > 0 ? round(($default_cq_marks / $default_total_marks) * 100, 2) : 0;
    $mcq_percentage = $default_total_marks > 0 ? round(($default_mcq_marks / $default_total_marks) * 100, 2) : 0;
    $practical_percentage = $default_total_marks > 0 ? round(($default_practical_marks / $default_total_marks) * 100, 2) : 0;
    
    // Insert new template
    $insertQuery = "INSERT INTO class_level_templates 
                   (class_level, class_level_name, description, default_total_marks, 
                    has_cq, has_mcq, has_practical, 
                    default_cq_marks, default_mcq_marks, default_practical_marks,
                    cq_percentage, mcq_percentage, practical_percentage,
                    is_active, created_at, updated_at) 
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())";
    
    $stmt = $conn->prepare($insertQuery);
    $stmt->bind_param("sssdiiiidddddd", 
        $class_level, $class_level_name, $description, $default_total_marks,
        $has_cq, $has_mcq, $has_practical,
        $default_cq_marks, $default_mcq_marks, $default_practical_marks,
        $cq_percentage, $mcq_percentage, $practical_percentage
    );
    
    if ($stmt->execute()) {
        $newId = $conn->insert_id;
        echo json_encode([
            'success' => true, 
            'message' => "নতুন টেমপ্লেট '{$class_level_name}' সফলভাবে যোগ করা হয়েছে।",
            'template_id' => $newId
        ]);
    } else {
        throw new Exception('টেমপ্লেট যোগ করতে সমস্যা হয়েছে');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false, 
        'message' => $e->getMessage()
    ]);
}
?>
