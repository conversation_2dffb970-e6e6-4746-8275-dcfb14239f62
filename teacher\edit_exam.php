<?php
session_start();

// Check if user is logged in and is a teacher
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'teacher') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if exam ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: exams.php");
    exit();
}

$exam_id = $_GET['id'];

// Get teacher information with department name
$userId = $_SESSION['userId'];
$username = $_SESSION['username'] ?? '';

// First try to get teacher by user_id
$sql = "SELECT t.*, d.department_name
        FROM teachers t
        LEFT JOIN departments d ON t.department_id = d.id
        WHERE t.user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();

// If no results, try to get by username
if ($result->num_rows === 0 && !empty($username)) {
    $sql = "SELECT t.*, d.department_name
            FROM teachers t
            LEFT JOIN departments d ON t.department_id = d.id
            WHERE t.username = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
}

$teacher = $result->fetch_assoc();

// If still no teacher found, redirect to login
if (!$teacher) {
    header("Location: ../index.php");
    exit();
}

// Check if the exam belongs to this teacher's department
$checkQuery = "SELECT e.* FROM exams e WHERE e.id = ? AND (e.department_id = ? OR e.created_by = ?)";
$checkStmt = $conn->prepare($checkQuery);
$checkStmt->bind_param("iii", $exam_id, $teacher['department_id'], $teacher['id']);
$checkStmt->execute();
$checkResult = $checkStmt->get_result();

if ($checkResult->num_rows === 0) {
    // Exam doesn't belong to this teacher's department
    header("Location: exams.php");
    exit();
}

// Handle form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_exam'])) {
    // Validate and sanitize input
    $errors = [];

    if (empty($_POST['exam_name'])) {
        $errors[] = "পরীক্ষার নাম আবশ্যক";
    }

    if (empty($_POST['exam_type'])) {
        $errors[] = "পরীক্ষার ধরন আবশ্যক";
    }

    if (empty($_POST['exam_date'])) {
        $errors[] = "পরীক্ষার তারিখ আবশ্যক";
    }

    if (empty($_POST['total_marks'])) {
        $errors[] = "মোট নম্বর আবশ্যক";
    }

    if (empty($errors)) {
        $exam_name = $conn->real_escape_string($_POST['exam_name']);
        $exam_type = $conn->real_escape_string($_POST['exam_type']);
        $subject_id = $_POST['subject_id'] ? intval($_POST['subject_id']) : null;
        $class_id = $_POST['class_id'] ? intval($_POST['class_id']) : null;
        $exam_date = $conn->real_escape_string($_POST['exam_date']);
        $start_time = $conn->real_escape_string($_POST['start_time'] ?? null);
        $end_time = $conn->real_escape_string($_POST['end_time'] ?? null);
        $total_marks = intval($_POST['total_marks']);
        $passing_marks = $_POST['passing_marks'] ? intval($_POST['passing_marks']) : null;
        $description = $conn->real_escape_string($_POST['description'] ?? '');

        // Update exam
        $sql = "UPDATE exams SET
                exam_name = ?,
                exam_type = ?,
                subject_id = ?,
                class_id = ?,
                exam_date = ?,
                start_time = ?,
                end_time = ?,
                total_marks = ?,
                passing_marks = ?,
                description = ?
                WHERE id = ?";

        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssiisssiisi", $exam_name, $exam_type, $subject_id, $class_id, $exam_date, $start_time, $end_time, $total_marks, $passing_marks, $description, $exam_id);

        if ($stmt->execute()) {
            $success_message = "পরীক্ষা সফলভাবে আপডেট করা হয়েছে!";
        } else {
            $error_message = "পরীক্ষা আপডেট করতে সমস্যা হয়েছে: " . $stmt->error;
        }

        $stmt->close();
    } else {
        $error_message = "পরীক্ষা আপডেট করতে সমস্যা হয়েছে:<br>" . implode("<br>", $errors);
    }
}

// Get exam details
$exam_sql = "SELECT * FROM exams WHERE id = ?";
$stmt = $conn->prepare($exam_sql);
$stmt->bind_param("i", $exam_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    header("Location: exams.php");
    exit();
}

$exam = $result->fetch_assoc();

// Get subjects for dropdown
try {
    // Check if subjects table exists
    $checkSubjectsTableQuery = "SHOW TABLES LIKE 'subjects'";
    $subjectsTableResult = $conn->query($checkSubjectsTableQuery);

    if ($subjectsTableResult->num_rows > 0) {
        // If subjects table exists, get subjects for this teacher's department
        $subjectsQuery = "SELECT id, subject_name, subject_code
                         FROM subjects
                         WHERE department_id = ?
                         ORDER BY subject_name";
        $stmt = $conn->prepare($subjectsQuery);
        $stmt->bind_param("i", $teacher['department_id']);
        $stmt->execute();
        $subjects = $stmt->get_result();
    } else {
        $subjects = null;
    }
} catch (Exception $e) {
    $subjects = null;
}

// Get classes for dropdown
try {
    // Check if classes table exists
    $checkClassesTableQuery = "SHOW TABLES LIKE 'classes'";
    $classesTableResult = $conn->query($checkClassesTableQuery);

    if ($classesTableResult->num_rows > 0) {
        // If classes table exists, get all classes
        $classesQuery = "SELECT id, class_name
                        FROM classes
                        ORDER BY class_name";
        $stmt = $conn->prepare($classesQuery);
        $stmt->execute();
        $classes = $stmt->get_result();
    } else {
        $classes = null;
    }
} catch (Exception $e) {
    $classes = null;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include __DIR__ . '/includes/global-head.php'; ?>
    <title>Edit Exam - Teacher Panel</title>

    <style>
        body, html, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, select, textarea, label, li, th, td {
            font-family: 'Hind Siliguri', sans-serif;
        }

        .form-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            padding: 30px;
            margin-bottom: 30px;
        }

        .form-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        .form-section h4 {
            margin-bottom: 20px;
            color: #333;
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>Teacher Panel</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user me-2"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> Students
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> Courses
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> Exams
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> Results
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">
                            <i class="fas fa-calendar-check me-2"></i> Attendance
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h2 class="mb-4">পরীক্ষা সম্পাদনা করুন</h2>

                        <?php if (!empty($success_message)): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <?php echo $success_message; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($error_message)): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <?php echo $error_message; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>

                        <!-- Edit Exam Form -->
                        <div class="card form-card">
                            <form action="" method="post">
                                <div class="form-section">
                                    <h4>পরীক্ষার মৌলিক তথ্য</h4>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="exam_name" class="form-label">পরীক্ষার নাম*</label>
                                            <input type="text" class="form-control" id="exam_name" name="exam_name" value="<?php echo htmlspecialchars($exam['exam_name']); ?>" required>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="exam_type" class="form-label">পরীক্ষার ধরন*</label>
                                            <select class="form-control" id="exam_type" name="exam_type" required>
                                                <option value="">পরীক্ষার ধরন নির্বাচন করুন</option>
                                                <option value="midterm" <?php echo ($exam['exam_type'] == 'midterm') ? 'selected' : ''; ?>>মিডটার্ম</option>
                                                <option value="final" <?php echo ($exam['exam_type'] == 'final') ? 'selected' : ''; ?>>ফাইনাল</option>
                                                <option value="quiz" <?php echo ($exam['exam_type'] == 'quiz') ? 'selected' : ''; ?>>কুইজ</option>
                                                <option value="assignment" <?php echo ($exam['exam_type'] == 'assignment') ? 'selected' : ''; ?>>অ্যাসাইনমেন্ট</option>
                                                <option value="practical" <?php echo ($exam['exam_type'] == 'practical') ? 'selected' : ''; ?>>প্র্যাকটিক্যাল</option>
                                                <option value="other" <?php echo ($exam['exam_type'] == 'other') ? 'selected' : ''; ?>>অন্যান্য</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="subject_id" class="form-label">বিষয়</label>
                                            <select class="form-control" id="subject_id" name="subject_id">
                                                <option value="">বিষয় নির্বাচন করুন</option>
                                                <?php if ($subjects && $subjects->num_rows > 0): ?>
                                                    <?php while ($subject = $subjects->fetch_assoc()): ?>
                                                        <option value="<?php echo $subject['id']; ?>" <?php echo ($exam['subject_id'] == $subject['id']) ? 'selected' : ''; ?>>
                                                            <?php echo $subject['subject_name'] . ' (' . $subject['subject_code'] . ')'; ?>
                                                        </option>
                                                    <?php endwhile; ?>
                                                <?php endif; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="class_id" class="form-label">শ্রেণী</label>
                                            <select class="form-control" id="class_id" name="class_id">
                                                <option value="">শ্রেণী নির্বাচন করুন</option>
                                                <?php if ($classes && $classes->num_rows > 0): ?>
                                                    <?php while ($class = $classes->fetch_assoc()): ?>
                                                        <option value="<?php echo $class['id']; ?>" <?php echo ($exam['class_id'] == $class['id']) ? 'selected' : ''; ?>>
                                                            <?php echo $class['class_name']; ?>
                                                        </option>
                                                    <?php endwhile; ?>
                                                <?php endif; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-section">
                                    <h4>পরীক্ষার সময়সূচী</h4>
                                    <div class="row mb-3">
                                        <div class="col-md-4">
                                            <label for="exam_date" class="form-label">পরীক্ষার তারিখ*</label>
                                            <input type="date" class="form-control" id="exam_date" name="exam_date" value="<?php echo isset($exam['exam_date']) ? $exam['exam_date'] : ''; ?>" required>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="start_time" class="form-label">শুরুর সময়</label>
                                            <input type="time" class="form-control" id="start_time" name="start_time" value="<?php echo isset($exam['start_time']) ? $exam['start_time'] : ''; ?>">
                                        </div>
                                        <div class="col-md-4">
                                            <label for="end_time" class="form-label">শেষের সময়</label>
                                            <input type="time" class="form-control" id="end_time" name="end_time" value="<?php echo isset($exam['end_time']) ? $exam['end_time'] : ''; ?>">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-section">
                                    <h4>পরীক্ষার নম্বর</h4>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="total_marks" class="form-label">মোট নম্বর*</label>
                                            <input type="number" class="form-control" id="total_marks" name="total_marks" min="1" value="<?php echo isset($exam['total_marks']) ? $exam['total_marks'] : ''; ?>" required>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="passing_marks" class="form-label">পাস নম্বর</label>
                                            <input type="number" class="form-control" id="passing_marks" name="passing_marks" min="1" value="<?php echo isset($exam['passing_marks']) ? $exam['passing_marks'] : ''; ?>">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-section">
                                    <h4>অতিরিক্ত তথ্য</h4>
                                    <div class="row mb-3">
                                        <div class="col-md-12">
                                            <label for="description" class="form-label">বিবরণ</label>
                                            <textarea class="form-control" id="description" name="description" rows="4"><?php echo htmlspecialchars($exam['description'] ?? ''); ?></textarea>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <a href="exams.php" class="btn btn-secondary">বাতিল করুন</a>
                                    <button type="submit" name="update_exam" class="btn btn-primary">পরীক্ষা আপডেট করুন</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Validate end time is after start time
            const startTimeInput = document.getElementById('start_time');
            const endTimeInput = document.getElementById('end_time');

            endTimeInput.addEventListener('change', function() {
                if (startTimeInput.value && endTimeInput.value) {
                    if (endTimeInput.value <= startTimeInput.value) {
                        alert('শেষের সময় অবশ্যই শুরুর সময়ের পরে হতে হবে');
                        endTimeInput.value = '';
                    }
                }
            });

            // Validate passing marks is less than total marks
            const totalMarksInput = document.getElementById('total_marks');
            const passingMarksInput = document.getElementById('passing_marks');

            passingMarksInput.addEventListener('change', function() {
                if (parseInt(passingMarksInput.value) > parseInt(totalMarksInput.value)) {
                    alert('পাস নম্বর অবশ্যই মোট নম্বরের চেয়ে কম হতে হবে');
                    passingMarksInput.value = '';
                }
            });

            totalMarksInput.addEventListener('change', function() {
                if (passingMarksInput.value && parseInt(passingMarksInput.value) > parseInt(totalMarksInput.value)) {
                    passingMarksInput.value = '';
                }
            });
        });
    </script>
</body>
</html>
