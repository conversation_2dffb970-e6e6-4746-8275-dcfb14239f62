<?php
session_start();

// Check if user is logged in and is a student
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'student') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get student information
$userId = $_SESSION['userId'];
$sql = "SELECT s.*, d.department_name as department
        FROM students s
        LEFT JOIN departments d ON s.department_id = d.id
        WHERE s.user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();
$student = $result->fetch_assoc();

// Check if assignments table exists
$tableExists = false;
$checkTable = $conn->query("SHOW TABLES LIKE 'assignments'");
if ($checkTable->num_rows > 0) {
    $tableExists = true;
}

// Create assignments table if it doesn't exist
if (!$tableExists) {
    $createTableQuery = "CREATE TABLE IF NOT EXISTS assignments (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT NOT NULL,
        subject_id INT(11) NOT NULL,
        class_id INT(11) NOT NULL,
        department_id INT(11) DEFAULT NULL,
        due_date DATE NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
        FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
        FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
    )";
    $conn->query($createTableQuery);
    
    // Create assignment_submissions table
    $createSubmissionsTableQuery = "CREATE TABLE IF NOT EXISTS assignment_submissions (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        assignment_id INT(11) NOT NULL,
        student_id INT(11) NOT NULL,
        submission_text TEXT,
        file_path VARCHAR(255),
        submission_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        grade VARCHAR(10) DEFAULT NULL,
        feedback TEXT,
        FOREIGN KEY (assignment_id) REFERENCES assignments(id) ON DELETE CASCADE,
        FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE
    )";
    $conn->query($createSubmissionsTableQuery);
}

// Get student's assignments
$assignmentsQuery = "SELECT a.*, s.subject_name, c.class_name, d.department_name,
                    (SELECT COUNT(*) FROM assignment_submissions WHERE assignment_id = a.id AND student_id = ?) as is_submitted
                    FROM assignments a
                    JOIN subjects s ON a.subject_id = s.id
                    JOIN classes c ON a.class_id = c.id
                    LEFT JOIN departments d ON a.department_id = d.id
                    WHERE a.class_id = ? AND (a.department_id IS NULL OR a.department_id = ?)
                    ORDER BY a.due_date ASC";

$assignments = null;
if ($tableExists) {
    $stmt = $conn->prepare($assignmentsQuery);
    $stmt->bind_param("iii", $student['id'], $student['class_id'], $student['department_id']);
    $stmt->execute();
    $assignments = $stmt->get_result();
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>অ্যাসাইনমেন্ট - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    
    <style>
        .assignment-card {
            transition: all 0.3s ease;
            border-left: 5px solid #6c757d;
        }
        .assignment-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .assignment-card.pending {
            border-left-color: #dc3545;
        }
        .assignment-card.submitted {
            border-left-color: #28a745;
        }
        .due-date {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .due-date.overdue {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>শিক্ষার্থী প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user me-2"></i> প্রোফাইল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subject_selection.php">
                            <i class="fas fa-book-open me-2"></i> বিষয় নির্বাচন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> কোর্সসমূহ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষাসমূহ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">
                            <i class="fas fa-calendar-check me-2"></i> উপস্থিতি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি সমূহ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="library.php">
                            <i class="fas fa-book-reader me-2"></i> লাইব্রেরি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="notifications.php">
                            <i class="fas fa-bell me-2"></i> নোটিফিকেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="messages.php">
                            <i class="fas fa-envelope me-2"></i> বার্তাসমূহ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events.php">
                            <i class="fas fa-calendar-alt me-2"></i> ইভেন্টসমূহ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="assignments.php">
                            <i class="fas fa-tasks me-2"></i> অ্যাসাইনমেন্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="timetable.php">
                            <i class="fas fa-clock me-2"></i> রুটিন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">
                            <i class="fas fa-cog me-2"></i> সেটিংস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>অ্যাসাইনমেন্ট</h2>
                        <p class="text-muted">আপনার সকল অ্যাসাইনমেন্ট এখানে দেখুন এবং জমা দিন</p>
                    </div>
                </div>

                <!-- Assignment Filters -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body">
                                <form class="row g-3">
                                    <div class="col-md-4">
                                        <label for="subject" class="form-label">বিষয়</label>
                                        <select class="form-select" id="subject">
                                            <option value="">সকল বিষয়</option>
                                            <!-- Subject options would be populated here -->
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="status" class="form-label">স্ট্যাটাস</label>
                                        <select class="form-select" id="status">
                                            <option value="">সকল স্ট্যাটাস</option>
                                            <option value="pending">বাকি আছে</option>
                                            <option value="submitted">জমা দেওয়া হয়েছে</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="dueDate" class="form-label">শেষ তারিখ</label>
                                        <select class="form-select" id="dueDate">
                                            <option value="">সকল</option>
                                            <option value="upcoming">আসন্ন</option>
                                            <option value="overdue">সময় শেষ</option>
                                        </select>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Assignments List -->
                <div class="row">
                    <?php if ($assignments && $assignments->num_rows > 0): ?>
                        <?php while ($assignment = $assignments->fetch_assoc()): ?>
                            <?php
                                $isSubmitted = $assignment['is_submitted'] > 0;
                                $isDueDate = strtotime($assignment['due_date']) < time();
                                $cardClass = $isSubmitted ? 'submitted' : 'pending';
                                $dueDateClass = $isDueDate && !$isSubmitted ? 'overdue' : '';
                            ?>
                            <div class="col-md-6 mb-4">
                                <div class="card assignment-card <?php echo $cardClass; ?>">
                                    <div class="card-body">
                                        <h5 class="card-title"><?php echo htmlspecialchars($assignment['title']); ?></h5>
                                        <h6 class="card-subtitle mb-2 text-muted">
                                            <?php echo htmlspecialchars($assignment['subject_name']); ?> | 
                                            <?php echo htmlspecialchars($assignment['class_name']); ?> | 
                                            <?php echo htmlspecialchars($assignment['department_name'] ?? 'সকল বিভাগ'); ?>
                                        </h6>
                                        <p class="card-text"><?php echo nl2br(htmlspecialchars(substr($assignment['description'], 0, 150))); ?>...</p>
                                        <p class="due-date <?php echo $dueDateClass; ?>">
                                            <i class="fas fa-calendar-alt me-1"></i> শেষ তারিখ: <?php echo date('d F, Y', strtotime($assignment['due_date'])); ?>
                                            <?php if ($isDueDate && !$isSubmitted): ?>
                                                <span class="badge bg-danger ms-2">সময় শেষ</span>
                                            <?php endif; ?>
                                        </p>
                                        <div class="d-flex justify-content-between align-items-center mt-3">
                                            <a href="view_assignment.php?id=<?php echo $assignment['id']; ?>" class="btn btn-primary">বিস্তারিত দেখুন</a>
                                            <?php if ($isSubmitted): ?>
                                                <span class="badge bg-success"><i class="fas fa-check me-1"></i> জমা দেওয়া হয়েছে</span>
                                            <?php else: ?>
                                                <a href="submit_assignment.php?id=<?php echo $assignment['id']; ?>" class="btn btn-outline-success">জমা দিন</a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> বর্তমানে কোন অ্যাসাইনমেন্ট নেই।
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Filter assignments based on selection
        document.addEventListener('DOMContentLoaded', function() {
            const subjectFilter = document.getElementById('subject');
            const statusFilter = document.getElementById('status');
            const dueDateFilter = document.getElementById('dueDate');
            
            const filterAssignments = () => {
                // Filter logic would be implemented here
                console.log('Filtering assignments...');
            };
            
            subjectFilter.addEventListener('change', filterAssignments);
            statusFilter.addEventListener('change', filterAssignments);
            dueDateFilter.addEventListener('change', filterAssignments);
        });
    </script>
</body>
</html>
