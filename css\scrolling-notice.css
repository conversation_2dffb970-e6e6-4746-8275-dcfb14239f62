/* Scrolling Notice Styles */
@keyframes scrollText {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

.scrolling-notice-container {
    position: relative;
    overflow: hidden;
    width: 100%;
    background-color: #f5f5f5;
    padding: 10px 0;
    border-top: 1px solid #dee2e6;
    border-bottom: 1px solid #dee2e6;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    z-index: 100;
}

.scrolling-notice {
    width: 100%;
    overflow: hidden;
    position: relative;
}

.notice-content {
    padding: 5px 0;
    color: #006A4E;
    font-weight: 500;
    display: inline-block;
    white-space: nowrap;
    animation: scrollText 30s linear infinite;
    animation-delay: 0s;
    position: absolute;
    left: 0;
}

/* Ensure animation works in all browsers */
@-webkit-keyframes scrollText {
    0% { -webkit-transform: translateX(100%); }
    100% { -webkit-transform: translateX(-100%); }
}

@-moz-keyframes scrollText {
    0% { -moz-transform: translateX(100%); }
    100% { -moz-transform: translateX(-100%); }
}

@-o-keyframes scrollText {
    0% { -o-transform: translateX(100%); }
    100% { -o-transform: translateX(-100%); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .notice-content {
        animation-duration: 20s; /* Faster on mobile */
    }
}
