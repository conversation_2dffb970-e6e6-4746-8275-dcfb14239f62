<?php
// Database Connection
require_once 'includes/dbh.inc.php';

echo "<h1>Add date Column to Results Table</h1>";

// Check if results table exists
$tableCheck = $conn->query("SHOW TABLES LIKE 'results'");
if ($tableCheck->num_rows == 0) {
    echo "<p style='color:red;'>Results table does not exist!</p>";
    exit;
}

// Check if date column already exists
$columnCheck = $conn->query("SHOW COLUMNS FROM results LIKE 'date'");
if ($columnCheck->num_rows > 0) {
    echo "<p style='color:orange;'>The 'date' column already exists in the results table.</p>";
} else {
    // Add the date column
    $alterQuery = "ALTER TABLE results ADD COLUMN date DATE NULL AFTER grade";
    
    if ($conn->query($alterQuery)) {
        echo "<p style='color:green;'>Successfully added 'date' column to the results table!</p>";
        
        // Update existing records to set a default value (current date)
        $currentDate = date('Y-m-d');
        $updateQuery = "UPDATE results SET date = '$currentDate' WHERE date IS NULL";
        if ($conn->query($updateQuery)) {
            echo "<p style='color:green;'>Updated existing records with current date.</p>";
        } else {
            echo "<p style='color:red;'>Error updating existing records: " . $conn->error . "</p>";
        }
    } else {
        echo "<p style='color:red;'>Error adding column: " . $conn->error . "</p>";
    }
}

// Show the updated table structure
echo "<h2>Updated Table Structure</h2>";
$columnsQuery = "SHOW COLUMNS FROM results";
$columnsResult = $conn->query($columnsQuery);

if ($columnsResult) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($column = $columnsResult->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>Error getting columns: " . $conn->error . "</p>";
}

echo "<p><a href='admin/marks_entry.php'>Go to Marks Entry Page</a></p>";

$conn->close();
?>
