<?php
session_start();
require_once '../includes/dbh.inc.php';
require_once '../includes/bkash_config.php';
require_once '../includes/bkash_functions.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Check if payment ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = 'পেমেন্ট আইডি প্রদান করা হয়নি!';
    header('Location: bkash_payment_list.php');
    exit();
}

$paymentId = intval($_GET['id']);

// Get payment details
$paymentQuery = "SELECT * FROM bkash_payments WHERE id = ?";
$stmt = $conn->prepare($paymentQuery);
$stmt->bind_param('i', $paymentId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    $_SESSION['error'] = 'পেমেন্ট রেকর্ড খুঁজে পাওয়া যায়নি!';
    header('Location: bkash_payment_list.php');
    exit();
}

$payment = $result->fetch_assoc();

// Get bKash token
$tokenResponse = bkashGrantToken();

if (!isset($tokenResponse['id_token'])) {
    $_SESSION['error'] = 'বিকাশ টোকেন পেতে সমস্যা হয়েছে! দয়া করে আবার চেষ্টা করুন।';
    header("Location: bkash_payment_details.php?id=$paymentId");
    exit();
}

$token = $tokenResponse['id_token'];

// Query payment status from bKash
$response = bkashQueryPayment($token, $payment['payment_id']);

// Update payment status in database
if (isset($response['transactionStatus'])) {
    $status = $response['transactionStatus'];
    $trxId = $response['trxID'] ?? $payment['trx_id'];
    
    // Update payment status
    $updateQuery = "UPDATE bkash_payments SET status = ?, trx_id = ? WHERE id = ?";
    $stmt = $conn->prepare($updateQuery);
    $stmt->bind_param('ssi', $status, $trxId, $paymentId);
    $stmt->execute();
    
    // If payment is completed, update fee payment
    if ($status === 'Completed') {
        $amount = floatval($payment['amount']);
        updateFeePaymentAfterBkash($conn, $payment['fee_id'], $amount, $trxId);
        
        $_SESSION['success'] = 'পেমেন্ট স্ট্যাটাস সফলভাবে আপডেট করা হয়েছে! পেমেন্ট সফল হয়েছে।';
    } else {
        $_SESSION['warning'] = 'পেমেন্ট স্ট্যাটাস আপডেট করা হয়েছে! স্ট্যাটাস: ' . $status;
    }
} else {
    $_SESSION['error'] = 'পেমেন্ট স্ট্যাটাস চেক করতে সমস্যা হয়েছে! ' . ($response['errorMessage'] ?? 'অজানা ত্রুটি');
}

// Redirect back to payment details
header("Location: bkash_payment_details.php?id=$paymentId");
exit();
?>
