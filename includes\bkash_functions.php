<?php
/**
 * bKash Payment Gateway Functions
 *
 * This file contains helper functions for interacting with the bKash payment gateway API.
 */

require_once 'bkash_config.php';

/**
 * Get bKash authorization token
 *
 * @return array Response from bKash API
 */
function bkashGrantToken() {
    $post_data = [
        'app_key' => BKASH_APP_KEY,
        'app_secret' => BKASH_APP_SECRET
    ];

    $headers = [
        'Content-Type: application/json',
        'Accept: application/json',
        'username: ' . BKASH_USERNAME,
        'password: ' . BKASH_PASSWORD
    ];

    return bkashCurlRequest(BKASH_GRANT_TOKEN_URL, $post_data, $headers);
}

/**
 * Create a bKash payment
 *
 * @param string $token Authorization token
 * @param float $amount Payment amount
 * @param string $invoice Invoice/reference number
 * @param string $intent Payment intent (sale/authorize)
 * @param string $currency Currency code (default: BDT)
 * @return array Response from bKash API
 */
function bkashCreatePayment($token, $amount, $invoice, $intent = 'sale', $currency = 'BDT') {
    $post_data = [
        'amount' => $amount,
        'currency' => $currency,
        'intent' => $intent,
        'merchantInvoiceNumber' => $invoice
    ];

    $headers = getBkashHeaders($token);

    return bkashCurlRequest(BKASH_CREATE_PAYMENT_URL, $post_data, $headers);
}

/**
 * Execute a bKash payment
 *
 * @param string $token Authorization token
 * @param string $paymentId Payment ID from create payment response
 * @return array Response from bKash API
 */
function bkashExecutePayment($token, $paymentId) {
    $post_data = [
        'paymentID' => $paymentId
    ];

    $headers = getBkashHeaders($token);

    return bkashCurlRequest(BKASH_EXECUTE_PAYMENT_URL, $post_data, $headers);
}

/**
 * Query a bKash payment status
 *
 * @param string $token Authorization token
 * @param string $paymentId Payment ID
 * @return array Response from bKash API
 */
function bkashQueryPayment($token, $paymentId) {
    $post_data = [
        'paymentID' => $paymentId
    ];

    $headers = getBkashHeaders($token);

    return bkashCurlRequest(BKASH_QUERY_PAYMENT_URL, $post_data, $headers);
}

/**
 * Make a cURL request to bKash API
 *
 * @param string $url API endpoint URL
 * @param array $post_data Data to send in the request
 * @param array $headers HTTP headers
 * @return array Response from API
 */
function bkashCurlRequest($url, $post_data, $headers) {
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post_data));
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);

    $response = curl_exec($ch);
    $error = curl_error($ch);

    if ($error) {
        return [
            'status' => 'error',
            'message' => 'cURL Error: ' . $error
        ];
    }

    curl_close($ch);

    return json_decode($response, true);
}

/**
 * Create bKash payments table if it doesn't exist
 *
 * @param mysqli $conn Database connection
 * @return bool True on success, false on failure
 */
function createBkashPaymentsTable($conn) {
    // First, check if bkash_payments table exists
    $checkTableQuery = "SHOW TABLES LIKE 'bkash_payments'";
    $tableExists = $conn->query($checkTableQuery)->num_rows > 0;

    // Create table if it doesn't exist
    if (!$tableExists) {
        // Create table without foreign key constraint to avoid issues
        $createTableQuery = "CREATE TABLE IF NOT EXISTS bkash_payments (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            fee_id INT(11) NOT NULL,
            payment_id VARCHAR(100) NOT NULL,
            trx_id VARCHAR(100) NULL,
            amount DECIMAL(10,2) NOT NULL,
            status VARCHAR(50) NOT NULL,
            payer_reference VARCHAR(100) NULL,
            payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        return $conn->query($createTableQuery);
    }

    return true;
}

/**
 * Record bKash payment in the database
 *
 * @param mysqli $conn Database connection
 * @param int $feeId Fee ID
 * @param float $amount Payment amount
 * @param string $paymentId bKash payment ID
 * @param string $trxId bKash transaction ID
 * @param string $status Payment status
 * @param string $payerReference Payer reference
 * @return int|bool ID of inserted record or false on failure
 */
function recordBkashPayment($conn, $feeId, $amount, $paymentId, $trxId, $status, $payerReference = '') {
    // Create table if it doesn't exist
    createBkashPaymentsTable($conn);

    // Insert payment record
    $insertQuery = "INSERT INTO bkash_payments (fee_id, payment_id, trx_id, amount, status, payer_reference)
                   VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($insertQuery);
    $stmt->bind_param('issdss', $feeId, $paymentId, $trxId, $amount, $status, $payerReference);

    if ($stmt->execute()) {
        return $conn->insert_id;
    }

    return false;
}

/**
 * Update bKash payment status in the database
 *
 * @param string $paymentId bKash payment ID
 * @param string $trxId bKash transaction ID
 * @param string $status Payment status
 * @return bool True on success, false on failure
 */
function updateBkashPaymentStatus($conn, $paymentId, $trxId, $status) {
    $updateQuery = "UPDATE bkash_payments SET trx_id = ?, status = ? WHERE payment_id = ?";
    $stmt = $conn->prepare($updateQuery);
    $stmt->bind_param('sss', $trxId, $status, $paymentId);

    return $stmt->execute();
}

/**
 * Update fee payment after successful bKash payment
 *
 * @param int $feeId Fee ID
 * @param float $amount Payment amount
 * @param string $trxId bKash transaction ID
 * @return bool True on success, false on failure
 */
function updateFeePaymentAfterBkash($conn, $feeId, $amount, $trxId) {
    // Start transaction
    $conn->begin_transaction();

    try {
        // Get current fee details
        $feeQuery = "SELECT * FROM fees WHERE id = ?";
        $stmt = $conn->prepare($feeQuery);
        $stmt->bind_param('i', $feeId);
        $stmt->execute();
        $feeResult = $stmt->get_result();

        if ($feeResult->num_rows === 0) {
            throw new Exception("Fee not found");
        }

        $fee = $feeResult->fetch_assoc();

        // Calculate new paid amount and determine payment status
        $newPaidAmount = $fee['paid'] + $amount;
        $newPaymentStatus = 'due';

        if ($newPaidAmount >= $fee['amount']) {
            $newPaymentStatus = 'paid';
        } else if ($newPaidAmount > 0) {
            $newPaymentStatus = 'partial';
        }

        // Update fee record
        $updateFeeQuery = "UPDATE fees SET paid = ?, payment_status = ? WHERE id = ?";
        $stmt = $conn->prepare($updateFeeQuery);
        $stmt->bind_param('dsi', $newPaidAmount, $newPaymentStatus, $feeId);
        $stmt->execute();

        // Insert into fee_payments table
        $insertPaymentQuery = "INSERT INTO fee_payments (fee_id, amount, payment_date, payment_method, notes)
                              VALUES (?, ?, CURRENT_DATE, 'bkash', ?)";
        $notes = "bKash Transaction ID: " . $trxId;
        $stmt = $conn->prepare($insertPaymentQuery);
        $stmt->bind_param('ids', $feeId, $amount, $notes);
        $stmt->execute();

        // Commit transaction
        $conn->commit();

        return true;
    } catch (Exception $e) {
        // Rollback on error
        $conn->rollback();
        error_log("Error updating fee payment after bKash: " . $e->getMessage());
        return false;
    }
}
