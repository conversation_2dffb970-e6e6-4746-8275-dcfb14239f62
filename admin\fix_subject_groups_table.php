<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$successMessages = [];
$errorMessages = [];

// Check if subject_groups table exists
$tableExists = $conn->query("SHOW TABLES LIKE 'subject_groups'")->num_rows > 0;

if (!$tableExists) {
    $errorMessages[] = "subject_groups টেবিল পাওয়া যায়নি। আগে টেবিল তৈরি করুন।";
} else {
    // Get table structure
    $tableInfoQuery = "SHOW CREATE TABLE subject_groups";
    $result = $conn->query($tableInfoQuery);
    $tableInfo = $result->fetch_assoc();
    $createTableStatement = $tableInfo['Create Table'];
    
    // Check if UNIQUE KEY exists
    $hasUniqueKey = strpos($createTableStatement, 'UNIQUE KEY') !== false;
    
    if ($hasUniqueKey) {
        // Drop the existing table and recreate it without UNIQUE KEY
        $dropTableQuery = "DROP TABLE subject_groups";
        
        if ($conn->query($dropTableQuery)) {
            $successMessages[] = "subject_groups টেবিল সফলভাবে মুছে ফেলা হয়েছে।";
            
            // Create the table without UNIQUE KEY
            $createTableQuery = "CREATE TABLE subject_groups (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                subject_id INT(11) NOT NULL,
                group_id INT(11) NOT NULL,
                subject_type ENUM('required', 'optional', 'fourth') NOT NULL DEFAULT 'optional',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
                FOREIGN KEY (group_id) REFERENCES groups(id) ON DELETE CASCADE
            )";
            
            if ($conn->query($createTableQuery)) {
                $successMessages[] = "subject_groups টেবিল সফলভাবে পুনরায় তৈরি করা হয়েছে (UNIQUE KEY ছাড়া)।";
            } else {
                $errorMessages[] = "subject_groups টেবিল পুনরায় তৈরি করতে সমস্যা হয়েছে: " . $conn->error;
            }
        } else {
            $errorMessages[] = "subject_groups টেবিল মুছতে সমস্যা হয়েছে: " . $conn->error;
        }
    } else {
        $successMessages[] = "subject_groups টেবিলে কোন UNIQUE KEY নেই। কোন পরিবর্তন করা হয়নি।";
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>টেবিল ফিক্স - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="groups.php">
                            <i class="fas fa-layer-group me-2"></i> গ্রুপ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sessions.php">
                            <i class="fas fa-calendar-alt me-2"></i> সেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>subject_groups টেবিল ফিক্স</h2>
                        <p class="text-muted">subject_groups টেবিল থেকে UNIQUE KEY সরানো</p>
                    </div>
                    <div class="col-auto">
                        <a href="subject_groups.php" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>বিষয়-গ্রুপ সংযোগ পৃষ্ঠায় ফিরে যান
                        </a>
                    </div>
                </div>

                <?php if (!empty($successMessages)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <h5 class="alert-heading"><i class="fas fa-check-circle me-2"></i>সফল!</h5>
                        <ul class="mb-0">
                            <?php foreach ($successMessages as $message): ?>
                                <li><?php echo $message; ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($errorMessages)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <h5 class="alert-heading"><i class="fas fa-exclamation-circle me-2"></i>ত্রুটি!</h5>
                        <ul class="mb-0">
                            <?php foreach ($errorMessages as $message): ?>
                                <li><?php echo $message; ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Table Info -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">টেবিল স্ট্রাকচার</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($tableExists): ?>
                            <pre class="bg-light p-3 rounded"><?php echo htmlspecialchars($createTableStatement); ?></pre>
                            
                            <?php if ($hasUniqueKey): ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>টেবিলে UNIQUE KEY আছে। এটি সরানো হয়েছে।
                                </div>
                            <?php else: ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>টেবিলে কোন UNIQUE KEY নেই। সবকিছু ঠিক আছে।
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-times-circle me-2"></i>subject_groups টেবিল পাওয়া যায়নি।
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer">
                        <form method="POST" action="fix_subject_groups_table.php">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-sync-alt me-2"></i>পুনরায় চেক করুন
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Next Steps -->
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">পরবর্তী পদক্ষেপ</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-link fa-3x text-primary mb-3"></i>
                                        <h5>বিষয়-গ্রুপ সংযোগ</h5>
                                        <p>বিষয়গুলিকে গ্রুপের সাথে সংযুক্ত করুন</p>
                                        <a href="subject_groups.php" class="btn btn-outline-primary">সংযোগ পৃষ্ঠায় যান</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-database fa-3x text-success mb-3"></i>
                                        <h5>গ্রুপ টেবিল সেটআপ</h5>
                                        <p>গ্রুপ সংক্রান্ত টেবিল তৈরি এবং সেটআপ করুন</p>
                                        <a href="create_groups_tables.php" class="btn btn-outline-success">সেটআপ পৃষ্ঠায় যান</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
