<?php
require_once '../includes/dbh.inc.php';

echo "<h2>Adding Unique Constraint to Classes Table</h2>";

// First, remove any existing duplicates
echo "<h3>Step 1: Removing Existing Duplicates</h3>";

$duplicatesQuery = "
    SELECT class_name, COUNT(*) as count, GROUP_CONCAT(id ORDER BY id) as ids
    FROM classes
    GROUP BY class_name
    HAVING COUNT(*) > 1
    ORDER BY class_name
";

$result = $conn->query($duplicatesQuery);

if ($result && $result->num_rows > 0) {
    echo "<p style='color: orange;'>⚠️ Found duplicates. Removing them first...</p>";
    
    while ($row = $result->fetch_assoc()) {
        $ids = explode(',', $row['ids']);
        $keepId = $ids[0]; // Keep the first ID
        $removeIds = array_slice($ids, 1); // Remove the rest
        
        echo "<p>Class: " . htmlspecialchars($row['class_name']) . " - Keep ID: $keepId, Remove: " . implode(', ', $removeIds) . "</p>";
        
        foreach ($removeIds as $idToRemove) {
            // Update references in other tables first
            
            // Update students table
            $updateStudents = $conn->query("UPDATE students SET class_id = $keepId WHERE class_id = $idToRemove");
            if ($updateStudents) {
                echo "<p style='color: green;'>✅ Updated students for class ID $idToRemove</p>";
            }
            
            // Update subjects table
            $updateSubjects = $conn->query("UPDATE subjects SET class_id = $keepId WHERE class_id = $idToRemove");
            if ($updateSubjects) {
                echo "<p style='color: green;'>✅ Updated subjects for class ID $idToRemove</p>";
            }
            
            // Update exams table
            $updateExams = $conn->query("UPDATE exams SET class_id = $keepId WHERE class_id = $idToRemove");
            if ($updateExams) {
                echo "<p style='color: green;'>✅ Updated exams for class ID $idToRemove</p>";
            }
            
            // Now delete the duplicate class
            $deleteQuery = "DELETE FROM classes WHERE id = $idToRemove";
            if ($conn->query($deleteQuery)) {
                echo "<p style='color: green;'>✅ Deleted duplicate class ID: $idToRemove</p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to delete class ID $idToRemove: " . $conn->error . "</p>";
            }
        }
    }
} else {
    echo "<p style='color: green;'>✅ No duplicates found!</p>";
}

// Now add the unique constraint
echo "<h3>Step 2: Adding Unique Constraint</h3>";

// Check if constraint already exists
$showIndexQuery = "SHOW INDEX FROM classes WHERE Key_name = 'unique_class_name'";
$indexResult = $conn->query($showIndexQuery);

if ($indexResult && $indexResult->num_rows > 0) {
    echo "<p style='color: green;'>✅ Unique constraint already exists!</p>";
} else {
    // Add unique constraint
    $addConstraintQuery = "ALTER TABLE classes ADD UNIQUE KEY unique_class_name (class_name)";
    
    if ($conn->query($addConstraintQuery)) {
        echo "<p style='color: green;'>✅ Successfully added unique constraint to class_name column!</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to add unique constraint: " . $conn->error . "</p>";
        
        // If it still fails, check for remaining duplicates
        $checkDuplicates = $conn->query($duplicatesQuery);
        if ($checkDuplicates && $checkDuplicates->num_rows > 0) {
            echo "<p style='color: orange;'>⚠️ Still have duplicates:</p>";
            while ($dup = $checkDuplicates->fetch_assoc()) {
                echo "<p>- " . htmlspecialchars($dup['class_name']) . " (IDs: " . $dup['ids'] . ")</p>";
            }
        }
    }
}

// Test the constraint
echo "<h3>Step 3: Testing the Constraint</h3>";

$testClassName = "TEST_DUPLICATE_" . time();

// Insert first test class
$insertTest1 = "INSERT INTO classes (class_name) VALUES ('$testClassName')";
if ($conn->query($insertTest1)) {
    echo "<p style='color: green;'>✅ First test class inserted successfully.</p>";
    
    // Try to insert duplicate
    $insertTest2 = "INSERT INTO classes (class_name) VALUES ('$testClassName')";
    if ($conn->query($insertTest2)) {
        echo "<p style='color: red;'>❌ Duplicate was allowed! Constraint is not working.</p>";
    } else {
        echo "<p style='color: green;'>✅ Duplicate insertion prevented! Constraint is working.</p>";
        echo "<p style='color: blue;'>Error message: " . $conn->error . "</p>";
    }
    
    // Clean up test class
    $cleanupQuery = "DELETE FROM classes WHERE class_name = '$testClassName'";
    $conn->query($cleanupQuery);
    echo "<p style='color: blue;'>ℹ️ Test class cleaned up.</p>";
    
} else {
    echo "<p style='color: red;'>❌ Failed to insert test class: " . $conn->error . "</p>";
}

// Show final status
echo "<h3>Step 4: Final Status</h3>";

// Check for any remaining duplicates
$finalDuplicateCheck = $conn->query($duplicatesQuery);
if ($finalDuplicateCheck && $finalDuplicateCheck->num_rows > 0) {
    echo "<p style='color: red;'>❌ Still have duplicates:</p>";
    while ($row = $finalDuplicateCheck->fetch_assoc()) {
        echo "<p>- " . htmlspecialchars($row['class_name']) . " (Count: " . $row['count'] . ")</p>";
    }
} else {
    echo "<p style='color: green;'>✅ No duplicates remaining!</p>";
}

// Check constraint status
$constraintCheck = $conn->query("SHOW INDEX FROM classes WHERE Key_name = 'unique_class_name'");
if ($constraintCheck && $constraintCheck->num_rows > 0) {
    echo "<p style='color: green;'>✅ Unique constraint is active!</p>";
} else {
    echo "<p style='color: red;'>❌ Unique constraint is not active!</p>";
}

// Show current classes
echo "<h3>Current Classes List</h3>";
$currentClassesQuery = "SELECT id, class_name FROM classes ORDER BY class_name";
$currentClasses = $conn->query($currentClassesQuery);

if ($currentClasses && $currentClasses->num_rows > 0) {
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>ID</th><th>Class Name</th>";
    echo "</tr>";
    
    while ($row = $currentClasses->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . htmlspecialchars($row['class_name']) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    echo "<p>Total Classes: " . $currentClasses->num_rows . "</p>";
} else {
    echo "<p>No classes found.</p>";
}

echo "<br><a href='classes.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>← Back to Classes</a>";
echo " <a href='duplicate_classes_summary.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>View Summary</a>";

$conn->close();
?>
