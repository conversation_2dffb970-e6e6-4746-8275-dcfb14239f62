<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Set current page for sidebar highlighting
$currentPage = basename($_SERVER['PHP_SELF']);

// Initialize filter variables
$sessionId = $_GET['session_id'] ?? '';
$classId = $_GET['class_id'] ?? '';
$departmentId = $_GET['department_id'] ?? '';
$studentId = $_GET['student_id'] ?? '';
$feeType = $_GET['fee_type'] ?? '';
$fromDate = $_GET['from_date'] ?? '';
$toDate = $_GET['to_date'] ?? '';
$paymentStatus = $_GET['payment_status'] ?? 'all_due';
$sortBy = $_GET['sort_by'] ?? 'due_date_asc';

// Process bulk payment if submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['bulk_payment_submit'])) {
    $selectedFeeIds = $_POST['fee_ids'] ?? [];
    $paymentAmounts = $_POST['payment_amounts'] ?? [];
    $paymentMethod = $_POST['payment_method'] ?? 'cash';
    $paymentDate = $_POST['payment_date'] ?? date('Y-m-d');
    $paymentNotes = $_POST['payment_notes'] ?? '';

    if (!empty($selectedFeeIds)) {
        // Generate receipt number
        $receiptNo = 'RCPT-' . date('Ymd') . '-' . rand(1000, 9999);

        // Start transaction
        $conn->begin_transaction();

        try {
            $successCount = 0;
            $totalPaid = 0;

            // Prepare statements
            $getFeeStmt = $conn->prepare("SELECT * FROM fees WHERE id = ?");
            $updateFeeStmt = $conn->prepare("UPDATE fees SET paid = ?, payment_status = ?, payment_date = ? WHERE id = ?");
            $addPaymentStmt = $conn->prepare("INSERT INTO fee_payments (fee_id, receipt_no, amount, payment_date, payment_method, notes) VALUES (?, ?, ?, ?, ?, ?)");

            foreach ($selectedFeeIds as $index => $feeId) {
                $paymentAmount = floatval($paymentAmounts[$index] ?? 0);

                if ($paymentAmount <= 0) continue;

                // Get fee details
                $getFeeStmt->bind_param('i', $feeId);
                $getFeeStmt->execute();
                $feeResult = $getFeeStmt->get_result();

                if ($feeResult->num_rows > 0) {
                    $fee = $feeResult->fetch_assoc();
                    $newPaidAmount = $fee['paid'] + $paymentAmount;

                    // Determine payment status
                    $newPaymentStatus = 'due';
                    if ($newPaidAmount >= $fee['amount']) {
                        $newPaymentStatus = 'paid';
                    } else if ($newPaidAmount > 0) {
                        $newPaymentStatus = 'partial';
                    }

                    // Update fee record
                    $updateFeeStmt->bind_param('dssi', $newPaidAmount, $newPaymentStatus, $paymentDate, $feeId);
                    $updateFeeStmt->execute();

                    // Add payment record
                    $addPaymentStmt->bind_param('isdsss', $feeId, $receiptNo, $paymentAmount, $paymentDate, $paymentMethod, $paymentNotes);
                    $addPaymentStmt->execute();

                    $successCount++;
                    $totalPaid += $paymentAmount;
                }
            }

            // Commit transaction
            $conn->commit();

            if ($successCount > 0) {
                $_SESSION['success'] = "$successCount টি বকেয়া সফলভাবে পরিশোধ করা হয়েছে। মোট পরিশোধিত: ৳" . number_format($totalPaid, 2);
            } else {
                $_SESSION['warning'] = "কোন বকেয়া পরিশোধ করা হয়নি।";
            }
        } catch (Exception $e) {
            // Roll back transaction on error
            $conn->rollback();
            $_SESSION['error'] = "পেমেন্ট প্রক্রিয়াকরণে সমস্যা: " . $e->getMessage();
        }
    } else {
        $_SESSION['error'] = "কোন বকেয়া নির্বাচন করা হয়নি।";
    }

    // Redirect to avoid form resubmission
    header("Location: due_fees.php?" . http_build_query($_GET));
    exit();
}

// Get fee types for dropdown
$feeTypesQuery = "SELECT DISTINCT fee_type FROM fees ORDER BY fee_type";
$feeTypesResult = $conn->query($feeTypesQuery);

// Get sessions for dropdown
$sessionsQuery = "SELECT * FROM sessions ORDER BY id DESC";
$sessionsResult = $conn->query($sessionsQuery);

// Get classes for dropdown
$classesQuery = "SELECT * FROM classes ORDER BY id";
$classesResult = $conn->query($classesQuery);

// Get departments for dropdown
$departmentsQuery = "SELECT * FROM departments ORDER BY id";
$departmentsResult = $conn->query($departmentsQuery);

// Build the query with search filters
$dueFeesQuery = "SELECT f.*,
                s.id as student_id, s.first_name, s.last_name, s.student_id as roll, s.profile_photo,
                c.class_name, s.class_id,
                ss.session_name, s.session_id,
                d.department_name, s.department_id
                FROM fees f
                JOIN students s ON f.student_id = s.id
                LEFT JOIN classes c ON s.class_id = c.id
                LEFT JOIN sessions ss ON s.session_id = ss.id
                LEFT JOIN departments d ON s.department_id = d.id
                WHERE 1=1";

$queryParams = [];
$paramTypes = "";

// Add payment status filter
if ($paymentStatus === 'due') {
    $dueFeesQuery .= " AND f.payment_status = 'due'";
} else if ($paymentStatus === 'partial') {
    $dueFeesQuery .= " AND f.payment_status = 'partial'";
} else if ($paymentStatus === 'all_due') {
    $dueFeesQuery .= " AND (f.payment_status = 'due' OR f.payment_status = 'partial')";
}

// Add session filter
if (!empty($sessionId)) {
    $dueFeesQuery .= " AND s.session_id = ?";
    $queryParams[] = $sessionId;
    $paramTypes .= "i";
}

// Add class filter
if (!empty($classId)) {
    $dueFeesQuery .= " AND s.class_id = ?";
    $queryParams[] = $classId;
    $paramTypes .= "i";
}

// Add department filter
if (!empty($departmentId)) {
    $dueFeesQuery .= " AND s.department_id = ?";
    $queryParams[] = $departmentId;
    $paramTypes .= "i";
}

// Add student filter
if (!empty($studentId)) {
    $dueFeesQuery .= " AND s.id = ?";
    $queryParams[] = $studentId;
    $paramTypes .= "i";
}

// Add fee type filter
if (!empty($feeType)) {
    $dueFeesQuery .= " AND f.fee_type = ?";
    $queryParams[] = $feeType;
    $paramTypes .= "s";
}

// Add date range filters
if (!empty($fromDate)) {
    $dueFeesQuery .= " AND f.due_date >= ?";
    $queryParams[] = $fromDate;
    $paramTypes .= "s";
}

if (!empty($toDate)) {
    $dueFeesQuery .= " AND f.due_date <= ?";
    $queryParams[] = $toDate;
    $paramTypes .= "s";
}

// Add sorting
if ($sortBy === 'due_date_asc') {
    $dueFeesQuery .= " ORDER BY f.due_date ASC";
} else if ($sortBy === 'due_date_desc') {
    $dueFeesQuery .= " ORDER BY f.due_date DESC";
} else if ($sortBy === 'amount_asc') {
    $dueFeesQuery .= " ORDER BY f.amount ASC";
} else if ($sortBy === 'amount_desc') {
    $dueFeesQuery .= " ORDER BY f.amount DESC";
} else if ($sortBy === 'student_name') {
    $dueFeesQuery .= " ORDER BY s.first_name ASC, s.last_name ASC";
} else if ($sortBy === 'class') {
    $dueFeesQuery .= " ORDER BY c.class_name ASC";
} else {
    $dueFeesQuery .= " ORDER BY f.due_date ASC";
}

// Prepare and execute the query
$stmt = $conn->prepare($dueFeesQuery);
if (!empty($paramTypes)) {
    $stmt->bind_param($paramTypes, ...$queryParams);
}
$stmt->execute();
$dueFeesResult = $stmt->get_result();

// Include header
include_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Include sidebar -->
        <?php include_once 'includes/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-money-bill-wave me-2"></i> বকেয়া বেতন ম্যানেজমেন্ট</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="fee_management.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i> ফি ম্যানেজমেন্ট
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
                            <i class="fas fa-filter me-1"></i> ফিল্টার
                        </button>
                    </div>
                </div>
            </div>

            <!-- Display alerts -->
            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $_SESSION['success'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $_SESSION['error'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['warning'])): ?>
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <?= $_SESSION['warning'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['warning']); ?>
            <?php endif; ?>

            <!-- Filter Form -->
            <div class="collapse <?= !empty($_GET) ? 'show' : '' ?>" id="filterCollapse">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-filter me-2"></i> বকেয়া বেতন ফিল্টার</h5>
                    </div>
                    <div class="card-body">
                        <form action="due_fees.php" method="get" class="row g-3">
                            <div class="col-md-4">
                                <label for="session_id" class="form-label">সেশন</label>
                                <select class="form-select" id="session_id" name="session_id">
                                    <option value="">সকল সেশন</option>
                                    <?php while ($session = $sessionsResult->fetch_assoc()): ?>
                                        <option value="<?= $session['id'] ?>" <?= $sessionId == $session['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($session['session_name']) ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="class_id" class="form-label">শ্রেণী</label>
                                <select class="form-select" id="class_id" name="class_id">
                                    <option value="">সকল শ্রেণী</option>
                                    <?php while ($class = $classesResult->fetch_assoc()): ?>
                                        <option value="<?= $class['id'] ?>" <?= $classId == $class['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($class['class_name']) ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="department_id" class="form-label">বিভাগ</label>
                                <select class="form-select" id="department_id" name="department_id">
                                    <option value="">সকল বিভাগ</option>
                                    <?php while ($department = $departmentsResult->fetch_assoc()): ?>
                                        <option value="<?= $department['id'] ?>" <?= $departmentId == $department['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($department['department_name']) ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="student_id" class="form-label">শিক্ষার্থী</label>
                                <select class="form-select" id="student_id" name="student_id">
                                    <option value="">সকল শিক্ষার্থী</option>
                                    <!-- Students will be loaded via AJAX based on session/class selection -->
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="fee_type" class="form-label">ফি টাইপ</label>
                                <select class="form-select" id="fee_type" name="fee_type">
                                    <option value="">সকল ফি টাইপ</option>
                                    <?php while ($feeTypeRow = $feeTypesResult->fetch_assoc()): ?>
                                        <option value="<?= htmlspecialchars($feeTypeRow['fee_type']) ?>" <?= $feeType == $feeTypeRow['fee_type'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($feeTypeRow['fee_type']) ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="payment_status" class="form-label">পেমেন্ট স্ট্যাটাস</label>
                                <select class="form-select" id="payment_status" name="payment_status">
                                    <option value="all_due" <?= $paymentStatus == 'all_due' ? 'selected' : '' ?>>সকল বকেয়া</option>
                                    <option value="due" <?= $paymentStatus == 'due' ? 'selected' : '' ?>>শুধু বকেয়া</option>
                                    <option value="partial" <?= $paymentStatus == 'partial' ? 'selected' : '' ?>>আংশিক পরিশোধিত</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="from_date" class="form-label">শুরুর তারিখ</label>
                                <input type="date" class="form-control" id="from_date" name="from_date" value="<?= $fromDate ?>">
                            </div>
                            <div class="col-md-4">
                                <label for="to_date" class="form-label">শেষের তারিখ</label>
                                <input type="date" class="form-control" id="to_date" name="to_date" value="<?= $toDate ?>">
                            </div>
                            <div class="col-md-4">
                                <label for="sort_by" class="form-label">সাজানোর ক্রম</label>
                                <select class="form-select" id="sort_by" name="sort_by">
                                    <option value="due_date_asc" <?= $sortBy == 'due_date_asc' ? 'selected' : '' ?>>তারিখ (আরোহণ)</option>
                                    <option value="due_date_desc" <?= $sortBy == 'due_date_desc' ? 'selected' : '' ?>>তারিখ (অবরোহণ)</option>
                                    <option value="amount_asc" <?= $sortBy == 'amount_asc' ? 'selected' : '' ?>>পরিমাণ (আরোহণ)</option>
                                    <option value="amount_desc" <?= $sortBy == 'amount_desc' ? 'selected' : '' ?>>পরিমাণ (অবরোহণ)</option>
                                    <option value="student_name" <?= $sortBy == 'student_name' ? 'selected' : '' ?>>শিক্ষার্থীর নাম</option>
                                    <option value="class" <?= $sortBy == 'class' ? 'selected' : '' ?>>শ্রেণী</option>
                                </select>
                            </div>
                            <div class="col-12 text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i> অনুসন্ধান
                                </button>
                                <a href="due_fees.php" class="btn btn-secondary">
                                    <i class="fas fa-redo me-1"></i> রিসেট
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Due Fees Table -->
            <form action="due_fees.php?<?= http_build_query($_GET) ?>" method="post" id="bulkPaymentForm">
                <div class="card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i> বকেয়া বেতনের তালিকা</h5>
                        <div>
                            <?php if ($dueFeesResult && $dueFeesResult->num_rows > 0): ?>
                                <button type="button" id="selectAllBtn" class="btn btn-sm btn-light me-2">
                                    <i class="fas fa-check-square me-1"></i> সবগুলো নির্বাচন
                                </button>
                                <button type="button" id="bulkPaymentBtn" class="btn btn-sm btn-success" disabled>
                                    <i class="fas fa-money-bill-wave me-1"></i> ব্লাক পেমেন্ট
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if ($dueFeesResult && $dueFeesResult->num_rows > 0): ?>
                            <div class="mb-3">
                                <span class="badge bg-info"><?php echo $dueFeesResult->num_rows; ?> টি রেকর্ড পাওয়া গেছে</span>

                                <?php
                                $activeFilters = [];
                                if (!empty($sessionId)) {
                                    $sessionName = '';
                                    $sessionsResult = $conn->query("SELECT session_name FROM sessions WHERE id = $sessionId");
                                    if ($sessionsResult && $sessionsResult->num_rows > 0) {
                                        $sessionName = $sessionsResult->fetch_assoc()['session_name'];
                                    }
                                    $activeFilters[] = "সেশন: " . htmlspecialchars($sessionName);
                                }
                                if (!empty($classId)) {
                                    $className = '';
                                    $classResult = $conn->query("SELECT class_name FROM classes WHERE id = $classId");
                                    if ($classResult && $classResult->num_rows > 0) {
                                        $className = $classResult->fetch_assoc()['class_name'];
                                    }
                                    $activeFilters[] = "শ্রেণী: " . htmlspecialchars($className);
                                }
                                if (!empty($departmentId)) {
                                    $departmentName = '';
                                    $departmentResult = $conn->query("SELECT department_name FROM departments WHERE id = $departmentId");
                                    if ($departmentResult && $departmentResult->num_rows > 0) {
                                        $departmentName = $departmentResult->fetch_assoc()['department_name'];
                                    }
                                    $activeFilters[] = "বিভাগ: " . htmlspecialchars($departmentName);
                                }
                                if (!empty($studentId)) {
                                    $studentName = '';
                                    $studentResult = $conn->query("SELECT CONCAT(first_name, ' ', last_name) as name FROM students WHERE id = $studentId");
                                    if ($studentResult && $studentResult->num_rows > 0) {
                                        $studentName = $studentResult->fetch_assoc()['name'];
                                    }
                                    $activeFilters[] = "শিক্ষার্থী: " . htmlspecialchars($studentName);
                                }
                                if (!empty($feeType)) $activeFilters[] = "ফি টাইপ: " . htmlspecialchars($feeType);
                                if (!empty($fromDate)) $activeFilters[] = "শুরুর তারিখ: " . date('d/m/Y', strtotime($fromDate));
                                if (!empty($toDate)) $activeFilters[] = "শেষের তারিখ: " . date('d/m/Y', strtotime($toDate));
                                if ($paymentStatus === 'due') {
                                    $activeFilters[] = "স্ট্যাটাস: বকেয়া";
                                } else if ($paymentStatus === 'partial') {
                                    $activeFilters[] = "স্ট্যাটাস: আংশিক পরিশোধিত";
                                } else if ($paymentStatus === 'all_due') {
                                    $activeFilters[] = "স্ট্যাটাস: সকল বকেয়া";
                                }

                                if (!empty($activeFilters)) {
                                    echo '<div class="mt-2 mb-2">';
                                    echo '<strong>সক্রিয় ফিল্টার:</strong> ';
                                    echo implode(' | ', $activeFilters);
                                    echo '</div>';
                                }
                                ?>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="50">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                                </div>
                                            </th>
                                            <th>শিক্ষার্থী</th>
                                            <th>শ্রেণী</th>
                                            <th>সেশন</th>
                                            <th>বিভাগ</th>
                                            <th>ফি টাইপ</th>
                                            <th>মোট</th>
                                            <th>পরিশোধিত</th>
                                            <th>বকেয়া</th>
                                            <th>তারিখ</th>
                                            <th>স্ট্যাটাস</th>
                                            <th>পদক্ষেপ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $index = 0;
                                        while ($fee = $dueFeesResult->fetch_assoc()):
                                            $dueAmount = $fee['amount'] - $fee['paid'];
                                            $statusClass = 'bg-danger text-white';
                                            if ($fee['payment_status'] === 'partial') {
                                                $statusClass = 'bg-warning text-dark';
                                            }

                                            // Check if fee is overdue
                                            $isOverdue = strtotime($fee['due_date']) < strtotime('today');
                                            $rowClass = $isOverdue ? 'table-danger' : '';
                                        ?>
                                            <tr class="<?= $rowClass ?>">
                                                <td>
                                                    <div class="form-check">
                                                        <input class="form-check-input fee-checkbox" type="checkbox" name="fee_ids[]" value="<?= $fee['id'] ?>" data-amount="<?= $dueAmount ?>">
                                                        <input type="hidden" name="payment_amounts[<?= $index ?>]" value="<?= $dueAmount ?>" class="payment-amount">
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="student-photo me-2">
                                                            <?php if (!empty($fee['profile_photo'])): ?>
                                                                <img src="../<?= $fee['profile_photo'] ?>" alt="Profile Photo" class="rounded-circle" width="40" height="40" style="object-fit: cover;">
                                                            <?php else: ?>
                                                                <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                                    <i class="fas fa-user"></i>
                                                                </div>
                                                            <?php endif; ?>
                                                        </div>
                                                        <div>
                                                            <div class="fw-bold"><?= htmlspecialchars($fee['first_name'] . ' ' . $fee['last_name']) ?></div>
                                                            <div class="small text-muted">আইডি: <?= htmlspecialchars($fee['roll']) ?></div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><?= htmlspecialchars($fee['class_name'] ?? 'N/A') ?></td>
                                                <td><?= htmlspecialchars($fee['session_name'] ?? 'N/A') ?></td>
                                                <td><?= htmlspecialchars($fee['department_name'] ?? 'N/A') ?></td>
                                                <td><?= htmlspecialchars($fee['fee_type']) ?></td>
                                                <td>৳ <?= number_format($fee['amount'], 2) ?></td>
                                                <td>৳ <?= number_format($fee['paid'], 2) ?></td>
                                                <td>৳ <?= number_format($dueAmount, 2) ?></td>
                                                <td><?= date('d/m/Y', strtotime($fee['due_date'])) ?></td>
                                                <td><span class="badge <?= $statusClass ?>"><?= $fee['payment_status'] === 'due' ? 'বকেয়া' : 'আংশিক' ?></span></td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-primary individual-payment-btn"
                                                            data-fee-id="<?= $fee['id'] ?>"
                                                            data-student-name="<?= htmlspecialchars($fee['first_name'] . ' ' . $fee['last_name']) ?>"
                                                            data-fee-type="<?= htmlspecialchars($fee['fee_type']) ?>"
                                                            data-amount="<?= $fee['amount'] ?>"
                                                            data-paid="<?= $fee['paid'] ?>"
                                                            data-due="<?= $dueAmount ?>">
                                                        <i class="fas fa-money-bill-wave"></i> পেমেন্ট
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php
                                            $index++;
                                        endwhile;
                                        ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Bulk Payment Options (Initially Hidden) -->
                            <div id="bulkPaymentOptions" class="mt-3 p-3 border rounded bg-light" style="display: none;">
                                <h5 class="mb-3">ব্লাক পেমেন্ট অপশন</h5>
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <label for="payment_method" class="form-label">পেমেন্ট পদ্ধতি</label>
                                        <select class="form-select" id="payment_method" name="payment_method" required>
                                            <option value="cash">নগদ</option>
                                            <option value="bank_transfer">ব্যাংক ট্রান্সফার</option>
                                            <option value="check">চেক</option>
                                            <option value="online">অনলাইন পেমেন্ট</option>
                                            <option value="mobile_banking">মোবাইল ব্যাংকিং</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="payment_date" class="form-label">পেমেন্ট তারিখ</label>
                                        <input type="date" class="form-control" id="payment_date" name="payment_date" value="<?= date('Y-m-d') ?>" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="payment_notes" class="form-label">নোট</label>
                                        <input type="text" class="form-control" id="payment_notes" name="payment_notes" placeholder="পেমেন্ট সম্পর্কে অতিরিক্ত তথ্য">
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <div class="alert alert-info">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span>নির্বাচিত আইটেম:</span>
                                                <span id="selectedItemsCount" class="badge bg-primary">0</span>
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center mt-2">
                                                <span>মোট বকেয়া:</span>
                                                <span id="totalDueAmount" class="fw-bold">৳ 0.00</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <button type="submit" name="bulk_payment_submit" class="btn btn-success">
                                            <i class="fas fa-money-bill-wave me-1"></i> পেমেন্ট করুন
                                        </button>
                                        <button type="button" id="cancelBulkPayment" class="btn btn-secondary">
                                            <i class="fas fa-times me-1"></i> বাতিল
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> কোন বকেয়া বেতন পাওয়া যায়নি। অন্য ফিল্টার অপশন ব্যবহার করে দেখুন।
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </form>
        </main>
    </div>
</div>

<!-- Individual Payment Modal -->
<div class="modal fade" id="individualPaymentModal" tabindex="-1" aria-labelledby="individualPaymentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="individualPaymentModalLabel"><i class="fas fa-money-bill-wave me-2"></i> পেমেন্ট করুন</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="process_payment.php" method="post">
                <div class="modal-body">
                    <input type="hidden" id="modal_fee_id" name="fee_id">

                    <div class="mb-3">
                        <label class="form-label">শিক্ষার্থী</label>
                        <input type="text" class="form-control" id="modal_student_name" readonly>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">ফি টাইপ</label>
                        <input type="text" class="form-control" id="modal_fee_type" readonly>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label">মোট পরিমাণ</label>
                            <div class="input-group">
                                <span class="input-group-text">৳</span>
                                <input type="text" class="form-control" id="modal_amount" readonly>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">পরিশোধিত</label>
                            <div class="input-group">
                                <span class="input-group-text">৳</span>
                                <input type="text" class="form-control" id="modal_paid" readonly>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">বকেয়া</label>
                            <div class="input-group">
                                <span class="input-group-text">৳</span>
                                <input type="text" class="form-control" id="modal_due" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="payment_amount" class="form-label">পেমেন্ট পরিমাণ <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">৳</span>
                            <input type="number" class="form-control" id="payment_amount" name="payment_amount" step="0.01" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="modal_payment_method" class="form-label">পেমেন্ট পদ্ধতি <span class="text-danger">*</span></label>
                        <select class="form-select" id="modal_payment_method" name="payment_method" required>
                            <option value="cash">নগদ</option>
                            <option value="bank_transfer">ব্যাংক ট্রান্সফার</option>
                            <option value="check">চেক</option>
                            <option value="online">অনলাইন পেমেন্ট</option>
                            <option value="mobile_banking">মোবাইল ব্যাংকিং</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="modal_payment_date" class="form-label">পেমেন্ট তারিখ <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="modal_payment_date" name="payment_date" value="<?= date('Y-m-d') ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="modal_notes" class="form-label">নোট</label>
                        <textarea class="form-control" id="modal_notes" name="notes" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="submit" class="btn btn-primary">পেমেন্ট করুন</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize select2 for select elements
        if (typeof $.fn.select2 !== 'undefined') {
            $('#session_id, #class_id, #department_id, #student_id, #fee_type, #payment_status, #sort_by').select2({
                placeholder: 'নির্বাচন করুন',
                allowClear: true
            });
        }

        // Load students based on session and class selection
        const sessionSelect = document.getElementById('session_id');
        const classSelect = document.getElementById('class_id');
        const departmentSelect = document.getElementById('department_id');
        const studentSelect = document.getElementById('student_id');

        function loadStudents() {
            const sessionId = sessionSelect.value;
            const classId = classSelect.value;
            const departmentId = departmentSelect.value;

            if (!sessionId && !classId && !departmentId) {
                return;
            }

            // Clear current options
            while (studentSelect.options.length > 1) {
                studentSelect.remove(1);
            }

            // Add loading option
            const loadingOption = document.createElement('option');
            loadingOption.text = 'লোড হচ্ছে...';
            loadingOption.disabled = true;
            studentSelect.add(loadingOption);

            // Fetch students via AJAX
            fetch(`ajax/get_students.php?session_id=${sessionId}&class_id=${classId}&department_id=${departmentId}`)
                .then(response => response.json())
                .then(data => {
                    // Remove loading option
                    studentSelect.remove(studentSelect.options.length - 1);

                    // Add student options
                    data.forEach(student => {
                        const option = document.createElement('option');
                        option.value = student.id;
                        option.text = `${student.name} (${student.student_id})`;
                        studentSelect.add(option);
                    });

                    // Restore selected value if exists
                    const selectedStudentId = '<?= $studentId ?>';
                    if (selectedStudentId) {
                        studentSelect.value = selectedStudentId;
                    }

                    // Trigger change event for select2
                    if (typeof $.fn.select2 !== 'undefined') {
                        $(studentSelect).trigger('change');
                    }
                })
                .catch(error => {
                    console.error('Error loading students:', error);
                    studentSelect.remove(studentSelect.options.length - 1);

                    const errorOption = document.createElement('option');
                    errorOption.text = 'শিক্ষার্থী লোড করতে সমস্যা হয়েছে';
                    errorOption.disabled = true;
                    studentSelect.add(errorOption);
                });
        }

        // Add event listeners to session, class, and department selects
        sessionSelect.addEventListener('change', loadStudents);
        classSelect.addEventListener('change', loadStudents);
        departmentSelect.addEventListener('change', loadStudents);

        // Initial load if session, class, or department is pre-selected
        if (sessionSelect.value || classSelect.value || departmentSelect.value) {
            loadStudents();
        }

        // Handle individual payment button click
        const individualPaymentBtns = document.querySelectorAll('.individual-payment-btn');
        individualPaymentBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const feeId = this.getAttribute('data-fee-id');
                const studentName = this.getAttribute('data-student-name');
                const feeType = this.getAttribute('data-fee-type');
                const amount = parseFloat(this.getAttribute('data-amount'));
                const paid = parseFloat(this.getAttribute('data-paid'));
                const due = parseFloat(this.getAttribute('data-due'));

                document.getElementById('modal_fee_id').value = feeId;
                document.getElementById('modal_student_name').value = studentName;
                document.getElementById('modal_fee_type').value = feeType;
                document.getElementById('modal_amount').value = amount.toFixed(2);
                document.getElementById('modal_paid').value = paid.toFixed(2);
                document.getElementById('modal_due').value = due.toFixed(2);
                document.getElementById('payment_amount').value = due.toFixed(2);
                document.getElementById('payment_amount').max = due;

                const modal = new bootstrap.Modal(document.getElementById('individualPaymentModal'));
                modal.show();
            });
        });

        // Handle select all checkbox
        const selectAllCheckbox = document.getElementById('selectAll');
        const feeCheckboxes = document.querySelectorAll('.fee-checkbox');

        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            feeCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
            updateBulkPaymentButton();
            updateSelectedItemsInfo();
        });

        // Handle individual checkboxes
        feeCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateBulkPaymentButton();
                updateSelectedItemsInfo();

                // Update select all checkbox state
                const allChecked = Array.from(feeCheckboxes).every(cb => cb.checked);
                const someChecked = Array.from(feeCheckboxes).some(cb => cb.checked);

                selectAllCheckbox.checked = allChecked;
                selectAllCheckbox.indeterminate = someChecked && !allChecked;
            });
        });

        // Handle select all button
        const selectAllBtn = document.getElementById('selectAllBtn');
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', function() {
                const allChecked = Array.from(feeCheckboxes).every(cb => cb.checked);

                feeCheckboxes.forEach(checkbox => {
                    checkbox.checked = !allChecked;
                });

                selectAllCheckbox.checked = !allChecked;
                selectAllCheckbox.indeterminate = false;

                updateBulkPaymentButton();
                updateSelectedItemsInfo();
            });
        }

        // Handle bulk payment button
        const bulkPaymentBtn = document.getElementById('bulkPaymentBtn');
        const bulkPaymentOptions = document.getElementById('bulkPaymentOptions');

        function updateBulkPaymentButton() {
            const anyChecked = Array.from(feeCheckboxes).some(cb => cb.checked);
            bulkPaymentBtn.disabled = !anyChecked;
        }

        function updateSelectedItemsInfo() {
            const selectedCheckboxes = Array.from(feeCheckboxes).filter(cb => cb.checked);
            const selectedCount = selectedCheckboxes.length;

            let totalDue = 0;
            selectedCheckboxes.forEach(checkbox => {
                totalDue += parseFloat(checkbox.getAttribute('data-amount'));
            });

            document.getElementById('selectedItemsCount').textContent = selectedCount;
            document.getElementById('totalDueAmount').textContent = '৳ ' + totalDue.toFixed(2);
        }

        if (bulkPaymentBtn) {
            bulkPaymentBtn.addEventListener('click', function() {
                bulkPaymentOptions.style.display = 'block';
                this.disabled = true;

                // Scroll to bulk payment options
                bulkPaymentOptions.scrollIntoView({ behavior: 'smooth' });
            });
        }

        // Handle cancel bulk payment button
        const cancelBulkPaymentBtn = document.getElementById('cancelBulkPayment');
        if (cancelBulkPaymentBtn) {
            cancelBulkPaymentBtn.addEventListener('click', function() {
                bulkPaymentOptions.style.display = 'none';
                updateBulkPaymentButton();
            });
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    });
</script>

<?php
// Include footer
include_once 'includes/footer.php';
?>