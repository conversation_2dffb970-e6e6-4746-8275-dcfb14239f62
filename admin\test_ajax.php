<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AJAX টেস্ট</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts - Hind Siliguri -->
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            padding: 20px;
        }
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        pre {
            background-color: #f1f1f1;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">AJAX টেস্ট</h1>
        
        <div class="debug-info">
            <h3>ডাটাবেস ইনফরমেশন</h3>
            <?php
            // Check if classes table exists
            $tableCheckQuery = "SHOW TABLES LIKE 'classes'";
            $tableCheckResult = $conn->query($tableCheckQuery);
            $classesTableExists = ($tableCheckResult && $tableCheckResult->num_rows > 0);
            
            // Check if sessions table exists
            $tableCheckQuery = "SHOW TABLES LIKE 'sessions'";
            $tableCheckResult = $conn->query($tableCheckQuery);
            $sessionsTableExists = ($tableCheckResult && $tableCheckResult->num_rows > 0);
            
            // Count classes
            $classCount = 0;
            if ($classesTableExists) {
                $countQuery = "SELECT COUNT(*) as count FROM classes";
                $countResult = $conn->query($countQuery);
                $classCount = $countResult->fetch_assoc()['count'];
            }
            
            // Count sessions
            $sessionCount = 0;
            if ($sessionsTableExists) {
                $countQuery = "SELECT COUNT(*) as count FROM sessions";
                $countResult = $conn->query($countQuery);
                $sessionCount = $countResult->fetch_assoc()['count'];
            }
            ?>
            
            <p>Classes Table Exists: <strong><?= $classesTableExists ? 'Yes' : 'No' ?></strong></p>
            <p>Sessions Table Exists: <strong><?= $sessionsTableExists ? 'Yes' : 'No' ?></strong></p>
            <p>Total Classes: <strong><?= $classCount ?></strong></p>
            <p>Total Sessions: <strong><?= $sessionCount ?></strong></p>
            
            <?php if ($classesTableExists && $classCount > 0): ?>
            <h4>Classes:</h4>
            <ul>
                <?php
                $classesQuery = "SELECT * FROM classes ORDER BY class_name LIMIT 10";
                $classesResult = $conn->query($classesQuery);
                while ($class = $classesResult->fetch_assoc()) {
                    echo "<li>ID: {$class['id']}, Name: {$class['class_name']}</li>";
                }
                ?>
            </ul>
            <?php endif; ?>
            
            <?php if ($sessionsTableExists && $sessionCount > 0): ?>
            <h4>Sessions:</h4>
            <ul>
                <?php
                $sessionsQuery = "SELECT * FROM sessions ORDER BY session_name LIMIT 10";
                $sessionsResult = $conn->query($sessionsQuery);
                while ($session = $sessionsResult->fetch_assoc()) {
                    echo "<li>ID: {$session['id']}, Name: {$session['session_name']}</li>";
                }
                ?>
            </ul>
            <?php endif; ?>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-4">
                <label for="session_id" class="form-label">সেশন নির্বাচন করুন</label>
                <select class="form-select" id="session_id" name="session_id">
                    <option value="">সেশন নির্বাচন করুন</option>
                    <?php
                    if ($sessionsTableExists) {
                        $sessionsQuery = "SELECT * FROM sessions ORDER BY session_name";
                        $sessions = $conn->query($sessionsQuery);
                        if ($sessions && $sessions->num_rows > 0):
                            while ($session = $sessions->fetch_assoc()):
                    ?>
                        <option value="<?= $session['id'] ?>"><?= htmlspecialchars($session['session_name']) ?></option>
                    <?php
                            endwhile;
                        endif;
                    }
                    ?>
                </select>
            </div>
            
            <div class="col-md-4">
                <label for="class_id" class="form-label">ক্লাস নির্বাচন করুন</label>
                <select class="form-select" id="class_id" name="class_id">
                    <option value="">প্রথমে সেশন নির্বাচন করুন</option>
                </select>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h3>AJAX রেসপন্স</h3>
            </div>
            <div class="card-body">
                <pre id="ajax-response">এখানে AJAX রেসপন্স দেখানো হবে...</pre>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Handle session selection
            $('#session_id').change(function() {
                const sessionId = $(this).val();
                
                // Enable/disable class dropdown
                if(sessionId) {
                    $('#class_id').html('<option value="">লোড হচ্ছে...</option>');
                    $('#class_id').prop('disabled', true);
                    
                    // AJAX call to get classes for this session
                    $.ajax({
                        url: 'ajax_handler.php',
                        type: 'POST',
                        data: {
                            action: 'get_classes_by_session',
                            session_id: sessionId
                        },
                        dataType: 'json',
                        success: function(response) {
                            // Display raw response
                            $('#ajax-response').html(JSON.stringify(response, null, 2));
                            
                            if(response.status === 'success') {
                                let options = '<option value="">ক্লাস নির্বাচন করুন</option>';
                                response.classes.forEach(function(cls) {
                                    options += `<option value="${cls.id}">${cls.class_name}</option>`;
                                });
                                $('#class_id').html(options);
                                $('#class_id').prop('disabled', false);
                            } else {
                                $('#class_id').html('<option value="">ক্লাস লোড করতে সমস্যা হয়েছে</option>');
                            }
                        },
                        error: function(xhr, status, error) {
                            $('#ajax-response').html('Error: ' + error + '\n\nStatus: ' + status + '\n\nResponse: ' + xhr.responseText);
                            $('#class_id').html('<option value="">ক্লাস লোড করতে সমস্যা হয়েছে</option>');
                        }
                    });
                } else {
                    // Reset class dropdown
                    $('#class_id').html('<option value="">প্রথমে সেশন নির্বাচন করুন</option>');
                    $('#class_id').prop('disabled', true);
                    $('#ajax-response').html('এখানে AJAX রেসপন্স দেখানো হবে...');
                }
            });
        });
    </script>
</body>
</html>
