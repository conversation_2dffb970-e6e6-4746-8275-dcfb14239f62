<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get student ID, payment IDs, and receipt number
$studentId = isset($_GET['student_id']) ? intval($_GET['student_id']) : 0;
$paymentIds = isset($_GET['payment_ids']) ? $_GET['payment_ids'] : [];
$receiptNo = isset($_GET['receipt_no']) ? trim($_GET['receipt_no']) : '';

// Convert single payment ID to array
if (!is_array($paymentIds) && !empty($paymentIds)) {
    $paymentIds = [$paymentIds];
}

// Debug information
$debugInfo = [
    'received_student_id' => $_GET['student_id'] ?? 'not set',
    'parsed_student_id' => $studentId,
    'payment_ids' => $paymentIds,
    'receipt_no' => $receiptNo
];

// Validate inputs
$studentExists = false;
$studentData = null;
if ($studentId > 0) {
    // Check if student exists in database (handle missing name column)
    try {
        // First check if name column exists
        $columnsResult = $conn->query("SHOW COLUMNS FROM students LIKE 'name'");
        $hasNameColumn = $columnsResult->num_rows > 0;

        if ($hasNameColumn) {
            $checkStudentQuery = "SELECT id, student_id, name, class, section FROM students WHERE id = ?";
        } else {
            // Use first_name and last_name if name doesn't exist
            $columnsResult = $conn->query("SHOW COLUMNS FROM students LIKE 'first_name'");
            $hasFirstName = $columnsResult->num_rows > 0;

            if ($hasFirstName) {
                $checkStudentQuery = "SELECT id, student_id, CONCAT(COALESCE(first_name, ''), ' ', COALESCE(last_name, '')) as name, class, section FROM students WHERE id = ?";
            } else {
                $checkStudentQuery = "SELECT id, student_id, 'Unknown' as name, class, section FROM students WHERE id = ?";
            }
        }

        $checkStmt = $conn->prepare($checkStudentQuery);
        $checkStmt->bind_param('i', $studentId);
        $checkStmt->execute();
        $studentResult = $checkStmt->get_result();

        if ($studentResult->num_rows > 0) {
            $studentExists = true;
            $studentData = $studentResult->fetch_assoc();
        }
    } catch (Exception $e) {
        $debugInfo['validation_error'] = $e->getMessage();
    }
}

if ($studentId <= 0 || !$studentExists) {
    // Show a nice error page instead of plain text
    ?>
    <!DOCTYPE html>
    <html lang="bn">
    <head>
        <?php include 'includes/global-head.php'; ?>
        <title>ত্রুটি - অবৈধ শিক্ষার্থী আইডি</title>
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap');

            :root {
                --primary-color: #4361ee;
                --primary-hover: #3a56d4;
                --secondary-color: #3f37c9;
                --accent-color: #4895ef;
                --error-color: #ef476f;
                --warning-color: #ffd166;
                --success-color: #06d6a0;
                --background-color: #f8f9fa;
                --card-bg: #ffffff;
                --text-dark: #2b2d42;
                --text-light: #6c757d;
                --border-radius: 12px;
                --box-shadow: 0 10px 30px rgba(0,0,0,0.08);
                --transition: all 0.3s ease;
            }

            body {
                font-family: 'Hind Siliguri', 'Noto Sans Bengali', Arial, sans-serif;
                background-color: var(--background-color);
                background-image: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
                margin: 0;
                padding: 20px;
            }

            .error-container {
                max-width: 550px;
                width: 100%;
                background-color: var(--card-bg);
                border-radius: var(--border-radius);
                box-shadow: var(--box-shadow);
                padding: 40px;
                text-align: center;
                transform: translateY(0);
                transition: var(--transition);
                position: relative;
                overflow: hidden;
            }

            .error-container:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 35px rgba(0,0,0,0.12);
            }

            .error-container::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 5px;
                background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            }

            .error-icon {
                font-size: 70px;
                color: var(--error-color);
                margin-bottom: 25px;
                text-shadow: 0 4px 8px rgba(239, 71, 111, 0.2);
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }

            .error-title {
                font-size: 28px;
                font-weight: 700;
                margin-bottom: 20px;
                color: var(--text-dark);
                letter-spacing: -0.5px;
            }

            .error-message {
                font-size: 17px;
                line-height: 1.6;
                color: var(--text-light);
                margin-bottom: 30px;
                padding: 0 10px;
            }

            .btn-back {
                background-color: var(--primary-color);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 50px;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
                text-decoration: none;
                transition: var(--transition);
                box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
                display: inline-flex;
                align-items: center;
                justify-content: center;
            }

            .btn-back:hover {
                background-color: var(--primary-hover);
                box-shadow: 0 6px 16px rgba(67, 97, 238, 0.4);
                transform: translateY(-2px);
            }
        </style>
    </head>
    <body>
        <div class="error-container">
            <div class="error-icon">
                <i class="fas fa-exclamation-circle"></i>
            </div>
            <div class="error-title">অবৈধ শিক্ষার্থী আইডি</div>
            <div class="error-message">
                আপনি যে শিক্ষার্থী আইডি ব্যবহার করেছেন তা অবৈধ বা খুঁজে পাওয়া যায়নি। দয়া করে একটি বৈধ শিক্ষার্থী আইডি ব্যবহার করুন।<br><br>
                <strong>সম্ভাব্য কারণ:</strong><br>
                • শিক্ষার্থী আইডি ভুল টাইপ করা হয়েছে<br>
                • শিক্ষার্থী ডাটাবেসে নেই<br>
                • URL এ ভুল parameter দেওয়া হয়েছে<br><br>

                <?php if (!empty($receiptNo)): ?>
                <div class="alert alert-info">
                    <strong>বিকল্প সমাধান:</strong> আপনি যদি শুধু রিসিপ্ট দেখতে চান, তাহলে
                    <a href="receipt_memo.php?receipt_no=<?= urlencode($receiptNo) ?>" class="btn btn-sm btn-primary">এখানে ক্লিক করুন</a>
                </div>
                <?php endif; ?>
            </div>
            <a href="fee_management.php" class="btn-back">
                <i class="fas fa-arrow-left me-1"></i> ফি ম্যানেজমেন্টে ফিরুন
            </a>
        </div>
    </body>
    </html>
    <?php
    exit();
}

// Get student information
$studentQuery = "SELECT s.*, c.class_name, d.department_name, ss.session_name
                FROM students s
                LEFT JOIN classes c ON s.class_id = c.id
                LEFT JOIN departments d ON s.department_id = d.id
                LEFT JOIN sessions ss ON s.session_id = ss.id
                WHERE s.id = ?";
$stmt = $conn->prepare($studentQuery);
$stmt->bind_param("i", $studentId);
$stmt->execute();
$studentResult = $stmt->get_result();

if ($studentResult->num_rows === 0) {
    // Show a nice error page for student not found
    ?>
    <!DOCTYPE html>
    <html lang="bn">
    <head>
        <?php include 'includes/global-head.php'; ?>
        <title>ত্রুটি - শিক্ষার্থী খুঁজে পাওয়া যায়নি</title>
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap');

            :root {
                --primary-color: #4361ee;
                --primary-hover: #3a56d4;
                --secondary-color: #3f37c9;
                --accent-color: #4895ef;
                --error-color: #ef476f;
                --warning-color: #ffd166;
                --success-color: #06d6a0;
                --background-color: #f8f9fa;
                --card-bg: #ffffff;
                --text-dark: #2b2d42;
                --text-light: #6c757d;
                --border-radius: 12px;
                --box-shadow: 0 10px 30px rgba(0,0,0,0.08);
                --transition: all 0.3s ease;
            }

            body {
                font-family: 'Hind Siliguri', 'Noto Sans Bengali', Arial, sans-serif;
                background-color: var(--background-color);
                background-image: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
                margin: 0;
                padding: 20px;
            }

            .error-container {
                max-width: 550px;
                width: 100%;
                background-color: var(--card-bg);
                border-radius: var(--border-radius);
                box-shadow: var(--box-shadow);
                padding: 40px;
                text-align: center;
                transform: translateY(0);
                transition: var(--transition);
                position: relative;
                overflow: hidden;
            }

            .error-container:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 35px rgba(0,0,0,0.12);
            }

            .error-container::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 5px;
                background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            }

            .error-icon {
                font-size: 70px;
                color: var(--error-color);
                margin-bottom: 25px;
                text-shadow: 0 4px 8px rgba(239, 71, 111, 0.2);
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }

            .error-title {
                font-size: 28px;
                font-weight: 700;
                margin-bottom: 20px;
                color: var(--text-dark);
                letter-spacing: -0.5px;
            }

            .error-message {
                font-size: 17px;
                line-height: 1.6;
                color: var(--text-light);
                margin-bottom: 30px;
                padding: 0 10px;
            }

            .btn-back {
                background-color: var(--primary-color);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 50px;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
                text-decoration: none;
                transition: var(--transition);
                box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
                display: inline-flex;
                align-items: center;
                justify-content: center;
            }

            .btn-back:hover {
                background-color: var(--primary-hover);
                box-shadow: 0 6px 16px rgba(67, 97, 238, 0.4);
                transform: translateY(-2px);
            }
        </style>
    </head>
    <body>
        <div class="error-container">
            <div class="error-icon">
                <i class="fas fa-user-slash"></i>
            </div>
            <div class="error-title">শিক্ষার্থী খুঁজে পাওয়া যায়নি</div>
            <div class="error-message">
                আপনি যে শিক্ষার্থী আইডি (<?= $studentId ?>) ব্যবহার করেছেন তা সিস্টেমে খুঁজে পাওয়া যায়নি। দয়া করে একটি বৈধ শিক্ষার্থী আইডি ব্যবহার করুন।
            </div>
            <a href="fee_management.php" class="btn-back">
                <i class="fas fa-arrow-left me-1"></i> ফি ম্যানেজমেন্টে ফিরুন
            </a>
        </div>
    </body>
    </html>
    <?php
    exit();
}

$student = $studentResult->fetch_assoc();

// Get comprehensive fee and payment information
if (!empty($receiptNo)) {
    // If specific receipt number is provided, get only that payment
    $feesQuery = "SELECT f.*,
                  fp.amount as total_payments,
                  CONCAT(fp.payment_date, '|', fp.amount, '|', fp.payment_method, '|', fp.receipt_no, '|', COALESCE(fp.notes, '')) as payment_details
                  FROM fees f
                  JOIN fee_payments fp ON f.id = fp.fee_id
                  WHERE f.student_id = ? AND fp.receipt_no = ?
                  ORDER BY f.due_date ASC";

    $stmt = $conn->prepare($feesQuery);
    $stmt->bind_param("is", $studentId, $receiptNo);
} else {
    // Get all fees and payments for the student
    $feesQuery = "SELECT f.*,
                  COALESCE(SUM(fp.amount), 0) as total_payments,
                  GROUP_CONCAT(
                      CONCAT(fp.payment_date, '|', fp.amount, '|', fp.payment_method, '|', fp.receipt_no, '|', COALESCE(fp.notes, ''))
                      ORDER BY fp.payment_date DESC
                      SEPARATOR ';;'
                  ) as payment_details
                  FROM fees f
                  LEFT JOIN fee_payments fp ON f.id = fp.fee_id
                  WHERE f.student_id = ?
                  GROUP BY f.id
                  ORDER BY f.due_date ASC";

    $stmt = $conn->prepare($feesQuery);
    $stmt->bind_param("i", $studentId);
}

$stmt->execute();
$feesResult = $stmt->get_result();

$fees = [];
$totalAmount = 0;
$totalPaid = 0;
$totalDue = 0;
$allPayments = [];

while ($fee = $feesResult->fetch_assoc()) {
    $amount = floatval($fee['amount']);
    $paid = floatval($fee['total_payments']);

    // Data validation: paid should not exceed amount
    if ($paid > $amount) {
        $paid = $amount;
    }

    $due = $amount - $paid;

    $fee['calculated_paid'] = $paid;
    $fee['calculated_due'] = $due;

    // Parse payment details
    $payments = [];
    if (!empty($fee['payment_details'])) {
        if (!empty($receiptNo)) {
            // For specific receipt, payment_details is a single entry
            $parts = explode('|', $fee['payment_details']);
            if (count($parts) >= 4) {
                $payment = [
                    'date' => $parts[0],
                    'amount' => floatval($parts[1]),
                    'method' => $parts[2],
                    'receipt_no' => $parts[3],
                    'notes' => $parts[4] ?? '',
                    'fee_type' => $fee['fee_type']
                ];
                $payments[] = $payment;
                $allPayments[] = $payment;
            }
        } else {
            // For all payments, payment_details contains multiple entries
            $paymentEntries = explode(';;', $fee['payment_details']);
            foreach ($paymentEntries as $entry) {
                if (!empty($entry)) {
                    $parts = explode('|', $entry);
                    if (count($parts) >= 4) {
                        $payment = [
                            'date' => $parts[0],
                            'amount' => floatval($parts[1]),
                            'method' => $parts[2],
                            'receipt_no' => $parts[3],
                            'notes' => $parts[4] ?? '',
                            'fee_type' => $fee['fee_type']
                        ];
                        $payments[] = $payment;
                        $allPayments[] = $payment;
                    }
                }
            }
        }
    }
    $fee['payments'] = $payments;

    $totalAmount += $amount;
    $totalPaid += $paid;
    $totalDue += $due;

    $fees[] = $fee;
}

// Sort all payments by date (newest first)
usort($allPayments, function($a, $b) {
    return strtotime($b['date']) - strtotime($a['date']);
});

if (empty($allPayments)) {
    // Show a nice error page for no payment records
    ?>
    <!DOCTYPE html>
    <html lang="bn">
    <head>
        <?php include 'includes/global-head.php'; ?>
        <title>ত্রুটি - কোন পেমেন্ট রেকর্ড পাওয়া যায়নি</title>
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap');

            :root {
                --primary-color: #4361ee;
                --primary-hover: #3a56d4;
                --secondary-color: #3f37c9;
                --accent-color: #4895ef;
                --error-color: #ef476f;
                --warning-color: #ffd166;
                --success-color: #06d6a0;
                --success-hover: #05b589;
                --background-color: #f8f9fa;
                --card-bg: #ffffff;
                --text-dark: #2b2d42;
                --text-light: #6c757d;
                --border-radius: 12px;
                --box-shadow: 0 10px 30px rgba(0,0,0,0.08);
                --transition: all 0.3s ease;
            }

            body {
                font-family: 'Hind Siliguri', 'Noto Sans Bengali', Arial, sans-serif;
                background-color: var(--background-color);
                background-image: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
                margin: 0;
                padding: 20px;
            }

            .error-container {
                max-width: 550px;
                width: 100%;
                background-color: var(--card-bg);
                border-radius: var(--border-radius);
                box-shadow: var(--box-shadow);
                padding: 40px;
                text-align: center;
                transform: translateY(0);
                transition: var(--transition);
                position: relative;
                overflow: hidden;
            }

            .error-container:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 35px rgba(0,0,0,0.12);
            }

            .error-container::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 5px;
                background: linear-gradient(90deg, var(--warning-color), var(--accent-color));
            }

            .error-icon {
                font-size: 70px;
                color: var(--warning-color);
                margin-bottom: 25px;
                text-shadow: 0 4px 8px rgba(255, 209, 102, 0.3);
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }

            .error-title {
                font-size: 28px;
                font-weight: 700;
                margin-bottom: 20px;
                color: var(--text-dark);
                letter-spacing: -0.5px;
            }

            .error-message {
                font-size: 17px;
                line-height: 1.6;
                color: var(--text-light);
                margin-bottom: 30px;
                padding: 0 10px;
            }

            .btn-back {
                background-color: var(--primary-color);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 50px;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
                text-decoration: none;
                transition: var(--transition);
                box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
                display: inline-flex;
                align-items: center;
                justify-content: center;
                margin-right: 15px;
            }

            .btn-back:hover {
                background-color: var(--primary-hover);
                box-shadow: 0 6px 16px rgba(67, 97, 238, 0.4);
                transform: translateY(-2px);
            }

            .btn-add {
                background-color: var(--success-color);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 50px;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
                text-decoration: none;
                transition: var(--transition);
                box-shadow: 0 4px 12px rgba(6, 214, 160, 0.3);
                display: inline-flex;
                align-items: center;
                justify-content: center;
            }

            .btn-add:hover {
                background-color: var(--success-hover);
                box-shadow: 0 6px 16px rgba(6, 214, 160, 0.4);
                transform: translateY(-2px);
            }

            .student-info {
                background-color: rgba(248, 249, 250, 0.7);
                border-radius: var(--border-radius);
                padding: 20px;
                margin-bottom: 25px;
                text-align: left;
                box-shadow: 0 4px 15px rgba(0,0,0,0.03);
                border: 1px solid rgba(0,0,0,0.05);
                position: relative;
                overflow: hidden;
            }

            .student-info::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 4px;
                height: 100%;
                background: linear-gradient(to bottom, var(--primary-color), var(--accent-color));
            }

            .student-info p {
                margin: 8px 0;
                font-size: 15px;
                display: flex;
                align-items: center;
            }

            .student-info strong {
                color: var(--text-dark);
                font-weight: 600;
                min-width: 120px;
                display: inline-block;
            }

            .student-info i {
                margin-right: 8px;
                color: var(--primary-color);
                font-size: 14px;
            }
        </style>
    </head>
    <body>
        <div class="error-container">
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="error-title">কোন পেমেন্ট রেকর্ড পাওয়া যায়নি</div>

            <div class="student-info">
                <p><strong><i class="fas fa-id-card"></i> শিক্ষার্থী আইডি:</strong> <?= htmlspecialchars($student['student_id']) ?></p>
                <p><strong><i class="fas fa-user"></i> নাম:</strong> <?= htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) ?></p>
                <p><strong><i class="fas fa-graduation-cap"></i> ক্লাস:</strong> <?= htmlspecialchars($student['class_name'] ?? 'অনির্দিষ্ট') ?></p>
                <p><strong><i class="fas fa-building"></i> বিভাগ:</strong> <?= htmlspecialchars($student['department_name'] ?? 'অনির্দিষ্ট') ?></p>
                <p><strong><i class="fas fa-calendar-alt"></i> সেশন:</strong> <?= htmlspecialchars($student['session_name'] ?? 'অনির্দিষ্ট') ?></p>
            </div>

            <div class="error-message">
                এই শিক্ষার্থীর জন্য কোন পেমেন্ট রেকর্ড পাওয়া যায়নি। আপনি নতুন পেমেন্ট যোগ করতে পারেন।
            </div>

            <div>
                <a href="fee_management.php" class="btn-back">
                    <i class="fas fa-arrow-left me-1"></i> ফি ম্যানেজমেন্টে ফিরুন
                </a>
                <a href="fee_management.php?add_payment=1&student_id=<?= $studentId ?>" class="btn-add">
                    <i class="fas fa-plus me-1"></i> পেমেন্ট যোগ করুন
                </a>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit();
}

// Calculate total amount
// Use the calculated totals from above
$paymentsData = $allPayments;
$dueAmount = $totalDue;

// Determine payment status based on due amount
$paymentStatus = "পরিশোধিত";
$statusClass = "text-success";

if ($totalDue > 0) {
    if ($totalPaid > 0) {
        $paymentStatus = "আংশিক পরিশোধিত";
        $statusClass = "text-warning";
    } else {
        $paymentStatus = "বকেয়া";
        $statusClass = "text-danger";
    }
} else if ($dueAmount < 0) {
    $paymentStatus = "অতিরিক্ত পরিশোধিত";
    $statusClass = "text-warning";
}

// Generate receipt number if not provided
$receiptNumber = "REC-" . date('Ymd') . "-" . $studentId . "-" . rand(1000, 9999);

// Get school information
$schoolName = "স্কুল ম্যানেজমেন্ট সিস্টেম";
$schoolAddress = "ঢাকা, বাংলাদেশ";
$schoolPhone = "০১৭১২৩৪৫৬৭৮";
$schoolEmail = "<EMAIL>";
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>পেমেন্ট রিসিপ্ট - <?= htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) ?><?= !empty($receiptNo) ? ' - ' . htmlspecialchars($receiptNo) : '' ?></title>

    <style>
        /* A4 size styling */
        @page {
            size: A4 portrait;
            margin: 10mm;
        }

        @page landscape {
            size: A4 landscape;
            margin: 10mm;
        }

        body.landscape {
            width: 297mm;
        }

        body.landscape .page-container {
            width: 297mm;
            min-height: 210mm;
        }

        body {
            font-family: 'Noto Sans Bengali', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            font-size: 14px;
        }

        .page-container {
            width: 210mm;
            min-height: 287mm;
            margin: 0 auto;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            position: relative;
            padding: 10mm;
            display: flex;
            flex-wrap: wrap;
        }

        .receipt-container {
            width: 50%;
            box-sizing: border-box;
            padding: 10mm;
            position: relative;
            border-right: 1px dashed #ccc;
        }

        .receipt-container:nth-child(2) {
            border-right: none;
        }

        /* Landscape mode styles */
        body.landscape .receipt-container {
            width: 50%;
            padding: 10mm;
        }

        /* Adjust font sizes for landscape mode */
        body.landscape .school-name {
            font-size: 20px;
        }

        body.landscape .receipt-title {
            font-size: 18px;
        }

        body.landscape table {
            font-size: 12px;
        }

        .school-header {
            text-align: center;
            margin-bottom: 15px;
            border-bottom: 2px solid #3f51b5;
            padding-bottom: 10px;
        }

        .school-name {
            font-size: 18px;
            font-weight: bold;
            color: #3f51b5;
            margin-bottom: 3px;
        }

        .school-address {
            font-size: 12px;
            color: #555;
            margin-bottom: 3px;
        }

        .receipt-title {
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            margin: 15px 0;
            padding: 5px;
            background-color: #e8eaf6;
            color: #3f51b5;
            border-radius: 5px;
        }

        .receipt-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            font-size: 12px;
        }

        .receipt-info-item {
            padding: 3px 0;
        }

        .receipt-info-label {
            font-weight: 600;
            color: #555;
        }

        .student-info, .payment-info {
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 12px;
        }

        .section-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #3f51b5;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 3px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
            font-size: 11px;
        }

        table, th, td {
            border: 1px solid #ddd;
        }

        th {
            background-color: #f1f3f9;
            color: #333;
            font-weight: 600;
            text-align: left;
            padding: 5px;
        }

        td {
            padding: 5px;
            vertical-align: top;
        }

        .total-row {
            font-weight: bold;
            background-color: #f1f3f9;
        }

        .footer {
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
        }

        .signature {
            width: 30%;
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #000;
            font-size: 11px;
        }

        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 36px;
            font-weight: bold;
            color: rgba(150, 150, 150, 0.7);
            z-index: 1000;
            pointer-events: none;
            letter-spacing: 1px;
            border: 3px solid rgba(150, 150, 150, 0.6);
            padding: 10px 20px;
            border-radius: 10px;
            white-space: nowrap;
            box-shadow: 0 0 15px rgba(150, 150, 150, 0.3);
            background-color: rgba(240, 240, 240, 0.1);
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }

        .payment-summary {
            font-size: 12px;
        }

        .payment-summary h5 {
            font-size: 14px;
            margin-bottom: 5px;
        }

        .payment-summary h3 {
            font-size: 16px;
            margin-top: 0;
        }

        @media print {
            body {
                background-color: #fff;
            }

            .page-container {
                box-shadow: none;
                padding: 0;
                width: 100%;
            }

            .receipt-container {
                padding: 5mm;
            }

            .no-print {
                display: none !important;
            }

            /* Print styles for landscape mode */
            body.landscape {
                width: 100%;
            }

            body.landscape .page-container {
                width: 100%;
                min-height: auto;
            }

            /* Force page breaks between receipts when needed */
            .receipt-container {
                page-break-inside: avoid;
            }

            /* Ensure watermark is visible in print */
            .watermark {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                color: rgba(100, 100, 100, 0.8) !important;
                border: 4px solid rgba(100, 100, 100, 0.7) !important;
                background-color: rgba(240, 240, 240, 0.2) !important;
                box-shadow: none !important;
                font-size: 42px !important;
                z-index: 9999 !important;
                opacity: 1 !important;
                visibility: visible !important;
            }
        }

        /* Button styles are now inline */

        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
        }

        .badge-paid {
            background-color: #d1e7dd;
            color: #0f5132;
        }

        .badge-partial {
            background-color: #fff3cd;
            color: #664d03;
        }

        .badge-due {
            background-color: #f8d7da;
            color: #842029;
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="d-flex gap-2 no-print" style="position: fixed; bottom: 20px; right: 20px; z-index: 999;">
        <a href="fee_management.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i> ফি ম্যানেজমেন্টে ফিরুন
        </a>
        <button id="toggleSettings" class="btn btn-warning">
            <i class="fas fa-cog me-1"></i> সেটিংস
        </button>
        <button id="toggleOrientation" class="btn btn-info">
            <i class="fas fa-sync-alt me-1"></i> <span id="orientationText">ল্যান্ডস্কেপ</span>
        </button>
        <button class="btn btn-primary" onclick="prepareAndPrint()">
            <i class="fas fa-print me-1"></i> প্রিন্ট
        </button>
    </div>

    <!-- Settings Panel -->
    <div id="settingsPanel" class="no-print" style="position: fixed; bottom: 80px; right: 20px; width: 300px; background: white; border: 1px solid #ddd; border-radius: 5px; padding: 15px; box-shadow: 0 0 10px rgba(0,0,0,0.1); z-index: 998; display: none;">
        <h5 class="mb-3">ওয়াটারমার্ক সেটিংস</h5>

        <div class="mb-3">
            <label for="watermarkOpacity" class="form-label">অপাসিটি</label>
            <input type="range" class="form-range" id="watermarkOpacity" min="0.1" max="1" step="0.1" value="0.7">
            <div class="d-flex justify-content-between">
                <small>হালকা</small>
                <small>গাঢ়</small>
            </div>
        </div>

        <div class="mb-3">
            <label for="watermarkSize" class="form-label">সাইজ</label>
            <input type="range" class="form-range" id="watermarkSize" min="20" max="60" step="2" value="36">
            <div class="d-flex justify-content-between">
                <small>ছোট</small>
                <small>বড়</small>
            </div>
        </div>

        <div class="mb-3">
            <label for="watermarkColor" class="form-label">কালার</label>
            <select class="form-select" id="watermarkColor">
                <option value="gray" selected>গ্রে</option>
                <option value="blue">নীল</option>
                <option value="green">সবুজ</option>
                <option value="red">লাল</option>
                <option value="purple">বেগুনি</option>
                <option value="black">কালো</option>
            </select>
        </div>

        <div class="mb-3">
            <label for="watermarkRotation" class="form-label">রোটেশন</label>
            <input type="range" class="form-range" id="watermarkRotation" min="-90" max="90" step="15" value="-45">
            <div class="d-flex justify-content-between">
                <small>-90°</small>
                <small>90°</small>
            </div>
        </div>

        <div class="mb-3">
            <label for="watermarkBorder" class="form-label">বর্ডার</label>
            <select class="form-select" id="watermarkBorder">
                <option value="none">নেই</option>
                <option value="thin" selected>পাতলা</option>
                <option value="medium">মাঝারি</option>
                <option value="thick">মোটা</option>
            </select>
        </div>

        <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="enhanceForPrint">
            <label class="form-check-label" for="enhanceForPrint">প্রিন্টের সময় ওয়াটারমার্ক উন্নত করুন</label>
            <small class="form-text text-muted d-block">প্রিন্টে ওয়াটারমার্ক আরও স্পষ্ট করবে</small>
        </div>

        <div class="d-flex justify-content-between mt-4">
            <button class="btn btn-sm btn-secondary" id="resetSettings">রিসেট</button>
            <button class="btn btn-sm btn-success" id="applySettings">প্রয়োগ করুন</button>
        </div>
    </div>

    <div class="no-print" style="position: fixed; top: 20px; left: 20px; z-index: 999;">
        <a href="fee_management.php" class="btn btn-outline-secondary btn-sm">
            <i class="fas fa-arrow-left me-1"></i> ফি ম্যানেজমেন্টে ফিরুন
        </a>
    </div>

    <div class="page-container">
        <!-- First Copy - Customer Copy -->
        <div class="receipt-container">
            <!-- Watermark -->
            <div class="watermark" data-html2canvas-ignore="false">গ্রাহক কপি</div>

            <!-- School Header -->
            <div class="school-header">
                <div class="school-name"><?= htmlspecialchars($schoolName) ?></div>
                <div class="school-address"><?= htmlspecialchars($schoolAddress) ?></div>
                <div class="school-contact">
                    <span><i class="fas fa-phone me-1"></i> <?= htmlspecialchars($schoolPhone) ?></span> |
                    <span><i class="fas fa-envelope me-1"></i> <?= htmlspecialchars($schoolEmail) ?></span>
                </div>
            </div>

        <!-- Receipt Title -->
        <div class="receipt-title">ফি পেমেন্ট রিসিপ্ট - গ্রাহক কপি<?= !empty($receiptNo) ? ' - ' . htmlspecialchars($receiptNo) : '' ?></div>

        <!-- Receipt Information -->
        <div class="receipt-info">
            <div>
                <div class="receipt-info-item">
                    <span class="receipt-info-label">রিসিপ্ট নং:</span>
                    <span><?= htmlspecialchars($receiptNumber) ?></span>
                </div>
                <div class="receipt-info-item">
                    <span class="receipt-info-label">তারিখ:</span>
                    <span><?= date('d F, Y') ?></span>
                </div>
            </div>
            <div>
                <div class="receipt-info-item">
                    <span class="receipt-info-label">মোট পরিমাণ:</span>
                    <span>৳ <?= number_format($totalAmount, 2) ?></span>
                </div>
                <div class="receipt-info-item">
                    <span class="receipt-info-label">পেমেন্ট সংখ্যা:</span>
                    <span><?= count($paymentsData) ?></span>
                </div>
            </div>
        </div>

        <!-- Student Information -->
        <div class="student-info">
            <div class="section-title"><i class="fas fa-user-graduate me-1"></i> শিক্ষার্থী তথ্য</div>
            <div class="row">
                <div class="col-md-6">
                    <div class="receipt-info-item">
                        <span class="receipt-info-label">শিক্ষার্থী আইডি:</span>
                        <span><?= htmlspecialchars($student['student_id']) ?></span>
                    </div>
                    <div class="receipt-info-item">
                        <span class="receipt-info-label">নাম:</span>
                        <span><?= htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) ?></span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="receipt-info-item">
                        <span class="receipt-info-label">ক্লাস:</span>
                        <span><?= htmlspecialchars($student['class_name'] ?? 'অনির্দিষ্ট') ?></span>
                    </div>
                    <div class="receipt-info-item">
                        <span class="receipt-info-label">বিভাগ:</span>
                        <span><?= htmlspecialchars($student['department_name'] ?? 'অনির্দিষ্ট') ?></span>
                    </div>
                    <div class="receipt-info-item">
                        <span class="receipt-info-label">সেশন:</span>
                        <span><?= htmlspecialchars($student['session_name'] ?? 'অনির্দিষ্ট') ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Details -->
        <div class="payment-info">
            <div class="section-title"><i class="fas fa-money-bill-wave me-1"></i> পেমেন্ট বিবরণ</div>
            <table class="table">
                <thead>
                    <tr>
                        <th width="5%">#</th>
                        <th width="15%">তারিখ</th>
                        <th width="20%">ফি টাইপ</th>
                        <th width="15%">পেমেন্ট পদ্ধতি</th>
                        <th width="15%">রিসিপ্ট নং</th>
                        <th width="15%">পরিমাণ</th>
                        <th width="15%">নোট</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $counter = 1;
                    foreach($paymentsData as $payment):
                        $paymentMethod = $payment['method'];
                        $methodDisplay = $paymentMethod;
                        switch($paymentMethod) {
                            case 'cash': $methodDisplay = 'নগদ'; break;
                            case 'bank': $methodDisplay = 'ব্যাংক ট্রান্সফার'; break;
                            case 'mobile_banking': $methodDisplay = 'মোবাইল ব্যাংকিং'; break;
                            case 'card': $methodDisplay = 'কার্ড'; break;
                            case 'cheque': $methodDisplay = 'চেক'; break;
                        }
                    ?>
                    <tr>
                        <td><?= $counter++ ?></td>
                        <td><?= date('d/m/Y', strtotime($payment['date'])) ?></td>
                        <td><?= htmlspecialchars($payment['fee_type']) ?></td>
                        <td><?= htmlspecialchars($methodDisplay) ?></td>
                        <td><?= htmlspecialchars($payment['receipt_no'] ?: '-') ?></td>
                        <td>৳ <?= number_format($payment['amount'], 2) ?></td>
                        <td><?= htmlspecialchars($payment['notes'] ?: '-') ?></td>
                    </tr>
                    <?php endforeach; ?>
                    <tr class="total-row">
                        <td colspan="5" class="text-end">মোট</td>
                        <td>৳ <?= number_format($totalPaid, 2) ?></td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Payment Summary Information -->
        <div class="payment-summary mb-4">
            <div class="row">
                <div class="col-4 text-center">
                    <h5>মোট ফি</h5>
                    <h3>৳ <?= number_format($totalAmount, 2) ?></h3>
                </div>
                <div class="col-4 text-center">
                    <h5>পরিশোধিত</h5>
                    <h3>৳ <?= number_format($totalPaid, 2) ?></h3>
                </div>
                <div class="col-4 text-center">
                    <h5>স্ট্যাটাস</h5>
                    <h3 class="<?= $statusClass ?>">
                        <?= $paymentStatus ?>
                        <?php if ($totalDue != 0): ?>
                            <small>(৳ <?= number_format(abs($totalDue), 2) ?>)</small>
                        <?php endif; ?>
                    </h3>
                </div>
            </div>
        </div>

        <!-- Amount in Words -->
        <div class="mb-4">
            <strong>কথায়:</strong>
            <?php
            // Convert number to Bangla words
            function numberToWords($number) {
                // Handle the integer part
                $integerPart = floor($number);
                $decimalPart = round(($number - $integerPart) * 100);

                $ones = array(
                    0 => '', 1 => 'এক', 2 => 'দুই', 3 => 'তিন', 4 => 'চার',
                    5 => 'পাঁচ', 6 => 'ছয়', 7 => 'সাত', 8 => 'আট', 9 => 'নয়',
                    10 => 'দশ', 11 => 'এগারো', 12 => 'বারো', 13 => 'তেরো', 14 => 'চৌদ্দ',
                    15 => 'পনেরো', 16 => 'ষোল', 17 => 'সতেরো', 18 => 'আঠারো', 19 => 'উনিশ',
                    20 => 'বিশ', 21 => 'একুশ', 22 => 'বাইশ', 23 => 'তেইশ', 24 => 'চব্বিশ',
                    25 => 'পঁচিশ', 26 => 'ছাব্বিশ', 27 => 'সাতাশ', 28 => 'আটাশ', 29 => 'ঊনত্রিশ',
                    30 => 'ত্রিশ', 31 => 'একত্রিশ', 32 => 'বত্রিশ', 33 => 'তেত্রিশ', 34 => 'চৌত্রিশ',
                    35 => 'পঁয়ত্রিশ', 36 => 'ছত্রিশ', 37 => 'সাঁইত্রিশ', 38 => 'আটত্রিশ', 39 => 'ঊনচল্লিশ',
                    40 => 'চল্লিশ', 41 => 'একচল্লিশ', 42 => 'বিয়াল্লিশ', 43 => 'তেতাল্লিশ', 44 => 'চুয়াল্লিশ',
                    45 => 'পঁয়তাল্লিশ', 46 => 'ছেচল্লিশ', 47 => 'সাতচল্লিশ', 48 => 'আটচল্লিশ', 49 => 'ঊনপঞ্চাশ',
                    50 => 'পঞ্চাশ', 51 => 'একান্ন', 52 => 'বায়ান্ন', 53 => 'তিপ্পান্ন', 54 => 'চুয়ান্ন',
                    55 => 'পঞ্চান্ন', 56 => 'ছাপ্পান্ন', 57 => 'সাতান্ন', 58 => 'আটান্ন', 59 => 'ঊনষাট',
                    60 => 'ষাট', 61 => 'একষট্টি', 62 => 'বাষট্টি', 63 => 'তেষট্টি', 64 => 'চৌষট্টি',
                    65 => 'পঁয়ষট্টি', 66 => 'ছেষট্টি', 67 => 'সাতষট্টি', 68 => 'আটষট্টি', 69 => 'ঊনসত্তর',
                    70 => 'সত্তর', 71 => 'একাত্তর', 72 => 'বাহাত্তর', 73 => 'তিয়াত্তর', 74 => 'চুয়াত্তর',
                    75 => 'পঁচাত্তর', 76 => 'ছিয়াত্তর', 77 => 'সাতাত্তর', 78 => 'আটাত্তর', 79 => 'ঊনআশি',
                    80 => 'আশি', 81 => 'একাশি', 82 => 'বিরাশি', 83 => 'তিরাশি', 84 => 'চুরাশি',
                    85 => 'পঁচাশি', 86 => 'ছিয়াশি', 87 => 'সাতাশি', 88 => 'আটাশি', 89 => 'ঊননব্বই',
                    90 => 'নব্বই', 91 => 'একানব্বই', 92 => 'বিরানব্বই', 93 => 'তিরানব্বই', 94 => 'চুরানব্বই',
                    95 => 'পঁচানব্বই', 96 => 'ছিয়ানব্বই', 97 => 'সাতানব্বই', 98 => 'আটানব্বই', 99 => 'নিরানব্বই'
                );

                $words = '';

                if ($integerPart == 0) {
                    $words = 'শূন্য';
                } else {
                    // Crore (10^7)
                    if ($integerPart >= 10000000) {
                        $crore = floor($integerPart / 10000000);
                        if ($crore <= 99) {
                            $words .= $ones[$crore] . ' কোটি ';
                        } else {
                            // Handle larger crore values
                            $words .= 'বহু কোটি ';
                        }
                        $integerPart %= 10000000;
                    }

                    // Lakh (10^5)
                    if ($integerPart >= 100000) {
                        $lakh = floor($integerPart / 100000);
                        if ($lakh <= 99) {
                            $words .= $ones[$lakh] . ' লক্ষ ';
                        }
                        $integerPart %= 100000;
                    }

                    // Thousand (10^3)
                    if ($integerPart >= 1000) {
                        $thousand = floor($integerPart / 1000);
                        if ($thousand <= 99) {
                            $words .= $ones[$thousand] . ' হাজার ';
                        }
                        $integerPart %= 1000;
                    }

                    // Hundred (10^2)
                    if ($integerPart >= 100) {
                        $hundred = floor($integerPart / 100);
                        $words .= $ones[$hundred] . ' শত ';
                        $integerPart %= 100;
                    }

                    // Remaining number
                    if ($integerPart > 0) {
                        $words .= $ones[$integerPart] . ' ';
                    }
                }

                // Add "Taka"
                $words .= 'টাকা';

                // Add decimal part if exists
                if ($decimalPart > 0) {
                    $words .= ' ' . $ones[$decimalPart] . ' পয়সা';
                }

                return $words . ' মাত্র';
            }

            echo numberToWords($paidAmount); // Using paid amount instead of total amount
            ?>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="signature">
                শিক্ষার্থীর স্বাক্ষর
            </div>
            <div class="signature">
                অভিভাবকের স্বাক্ষর
            </div>
            <div class="signature">
                অফিসিয়াল স্বাক্ষর
            </div>
        </div>

        <div class="mt-4 text-center">
            <small class="text-muted">এই রসিদটি কম্পিউটার দ্বারা জেনারেট করা হয়েছে এবং সিল বা স্বাক্ষর ছাড়াই বৈধ।</small>
        </div>
        </div>

        <!-- Second Copy - Office Copy -->
        <div class="receipt-container">
            <!-- Watermark -->
            <div class="watermark" data-html2canvas-ignore="false">অফিস কপি</div>

            <!-- School Header -->
            <div class="school-header">
                <div class="school-name"><?= htmlspecialchars($schoolName) ?></div>
                <div class="school-address"><?= htmlspecialchars($schoolAddress) ?></div>
                <div class="school-contact">
                    <span><i class="fas fa-phone me-1"></i> <?= htmlspecialchars($schoolPhone) ?></span> |
                    <span><i class="fas fa-envelope me-1"></i> <?= htmlspecialchars($schoolEmail) ?></span>
                </div>
            </div>

            <!-- Receipt Title -->
            <div class="receipt-title">ফি পেমেন্ট রিসিপ্ট - অফিস কপি<?= !empty($receiptNo) ? ' - ' . htmlspecialchars($receiptNo) : '' ?></div>

            <!-- Receipt Information -->
            <div class="receipt-info">
                <div>
                    <div class="receipt-info-item">
                        <span class="receipt-info-label">রিসিপ্ট নং:</span>
                        <span><?= htmlspecialchars($receiptNumber) ?></span>
                    </div>
                    <div class="receipt-info-item">
                        <span class="receipt-info-label">তারিখ:</span>
                        <span><?= date('d F, Y') ?></span>
                    </div>
                </div>
                <div>
                    <div class="receipt-info-item">
                        <span class="receipt-info-label">মোট পরিমাণ:</span>
                        <span>৳ <?= number_format($totalAmount, 2) ?></span>
                    </div>
                    <div class="receipt-info-item">
                        <span class="receipt-info-label">পেমেন্ট সংখ্যা:</span>
                        <span><?= count($paymentsData) ?></span>
                    </div>
                </div>
            </div>

            <!-- Student Information -->
            <div class="student-info">
                <div class="section-title"><i class="fas fa-user-graduate me-1"></i> শিক্ষার্থী তথ্য</div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="receipt-info-item">
                            <span class="receipt-info-label">শিক্ষার্থী আইডি:</span>
                            <span><?= htmlspecialchars($student['student_id']) ?></span>
                        </div>
                        <div class="receipt-info-item">
                            <span class="receipt-info-label">নাম:</span>
                            <span><?= htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) ?></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="receipt-info-item">
                            <span class="receipt-info-label">ক্লাস:</span>
                            <span><?= htmlspecialchars($student['class_name'] ?? 'অনির্দিষ্ট') ?></span>
                        </div>
                        <div class="receipt-info-item">
                            <span class="receipt-info-label">বিভাগ:</span>
                            <span><?= htmlspecialchars($student['department_name'] ?? 'অনির্দিষ্ট') ?></span>
                        </div>
                        <div class="receipt-info-item">
                            <span class="receipt-info-label">সেশন:</span>
                            <span><?= htmlspecialchars($student['session_name'] ?? 'অনির্দিষ্ট') ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Details -->
            <div class="payment-info">
                <div class="section-title"><i class="fas fa-money-bill-wave me-1"></i> পেমেন্ট বিবরণ</div>
                <table class="table">
                    <thead>
                        <tr>
                            <th width="5%">#</th>
                            <th width="15%">তারিখ</th>
                            <th width="20%">ফি টাইপ</th>
                            <th width="15%">পেমেন্ট পদ্ধতি</th>
                            <th width="15%">রিসিপ্ট নং</th>
                            <th width="15%">পরিমাণ</th>
                            <th width="15%">নোট</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $counter = 1;
                        foreach($paymentsData as $payment):
                            $paymentMethod = $payment['payment_method'];
                            $methodDisplay = $paymentMethod;
                            switch($paymentMethod) {
                                case 'cash': $methodDisplay = 'নগদ'; break;
                                case 'bank': $methodDisplay = 'ব্যাংক ট্রান্সফার'; break;
                                case 'bkash': $methodDisplay = 'বিকাশ'; break;
                                case 'nagad': $methodDisplay = 'নগদ (ডিজিটাল)'; break;
                                case 'rocket': $methodDisplay = 'রকেট'; break;
                            }
                        ?>
                        <tr>
                            <td><?= $counter++ ?></td>
                            <td><?= date('d/m/Y', strtotime($payment['payment_date'])) ?></td>
                            <td><?= htmlspecialchars($payment['fee_type']) ?></td>
                            <td><?= htmlspecialchars($methodDisplay) ?></td>
                            <td><?= htmlspecialchars($payment['receipt_no'] ?: '-') ?></td>
                            <td>৳ <?= number_format($payment['amount'], 2) ?></td>
                            <td><?= htmlspecialchars($payment['notes'] ?: '-') ?></td>
                        </tr>
                        <?php endforeach; ?>
                        <tr class="total-row">
                            <td colspan="5" class="text-end">মোট</td>
                            <td>৳ <?= number_format($paidAmount, 2) ?></td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Payment Summary Information -->
            <div class="payment-summary mb-4">
                <div class="row">
                    <div class="col-4 text-center">
                        <h5>মোট ফি</h5>
                        <h3>৳ <?= number_format($totalAmount, 2) ?></h3>
                    </div>
                    <div class="col-4 text-center">
                        <h5>পরিশোধিত</h5>
                        <h3>৳ <?= number_format($paidAmount, 2) ?></h3>
                    </div>
                    <div class="col-4 text-center">
                        <h5>স্ট্যাটাস</h5>
                        <h3 class="<?= $statusClass ?>">
                            <?= $paymentStatus ?>
                            <?php if ($dueAmount != 0): ?>
                                <small>(৳ <?= number_format(abs($dueAmount), 2) ?>)</small>
                            <?php endif; ?>
                        </h3>
                    </div>
                </div>
            </div>

            <!-- Amount in Words -->
            <div class="mb-4">
                <strong>কথায়:</strong>
                <?= numberToWords($paidAmount) ?>
            </div>

            <!-- Footer -->
            <div class="footer">
                <div class="signature">
                    শিক্ষার্থীর স্বাক্ষর
                </div>
                <div class="signature">
                    অভিভাবকের স্বাক্ষর
                </div>
                <div class="signature">
                    অফিসিয়াল স্বাক্ষর
                </div>
            </div>

            <div class="mt-4 text-center">
                <small class="text-muted">এই রসিদটি কম্পিউটার দ্বারা জেনারেট করা হয়েছে এবং সিল বা স্বাক্ষর ছাড়াই বৈধ।</small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Watermark settings
        const watermarkSettings = {
            opacity: 0.7,
            size: 36,
            color: 'gray',
            rotation: -45,
            border: 'thin',
            // Default print settings
            printOpacity: 0.7, // Same as normal opacity by default
            printSize: 44,
            printBorderWidth: 5,
            // Flag to control if print should enhance watermark
            enhanceForPrint: false
        };

        // Color mapping
        const colorMap = {
            gray: { normal: 'rgba(150, 150, 150, $opacity)', print: 'rgba(80, 80, 80, $opacity)' },
            blue: { normal: 'rgba(100, 150, 200, $opacity)', print: 'rgba(70, 100, 180, $opacity)' },
            green: { normal: 'rgba(100, 180, 120, $opacity)', print: 'rgba(60, 140, 80, $opacity)' },
            red: { normal: 'rgba(200, 100, 100, $opacity)', print: 'rgba(160, 60, 60, $opacity)' },
            purple: { normal: 'rgba(150, 100, 180, $opacity)', print: 'rgba(120, 80, 160, $opacity)' },
            black: { normal: 'rgba(80, 80, 80, $opacity)', print: 'rgba(40, 40, 40, $opacity)' }
        };

        // Border mapping
        const borderMap = {
            none: { normal: 'none', print: 'none' },
            thin: { normal: '2px solid $color', print: '3px solid $color' },
            medium: { normal: '3px solid $color', print: '5px solid $color' },
            thick: { normal: '5px solid $color', print: '7px solid $color' }
        };

        // Toggle settings panel
        document.getElementById('toggleSettings').addEventListener('click', function() {
            const panel = document.getElementById('settingsPanel');
            if (panel.style.display === 'none') {
                panel.style.display = 'block';
            } else {
                panel.style.display = 'none';
            }
        });

        // Apply settings
        document.getElementById('applySettings').addEventListener('click', function() {
            // Get values from form
            watermarkSettings.opacity = parseFloat(document.getElementById('watermarkOpacity').value);
            watermarkSettings.size = parseInt(document.getElementById('watermarkSize').value);
            watermarkSettings.color = document.getElementById('watermarkColor').value;
            watermarkSettings.rotation = parseInt(document.getElementById('watermarkRotation').value);
            watermarkSettings.border = document.getElementById('watermarkBorder').value;
            watermarkSettings.enhanceForPrint = document.getElementById('enhanceForPrint').checked;

            // Set print opacity to match normal opacity if not enhancing
            if (!watermarkSettings.enhanceForPrint) {
                watermarkSettings.printOpacity = watermarkSettings.opacity;
            } else {
                // If enhancing, make print opacity higher but not more than 0.9
                watermarkSettings.printOpacity = Math.min(watermarkSettings.opacity + 0.2, 0.9);
            }

            // Apply to watermarks
            applyWatermarkSettings();

            // Hide panel
            document.getElementById('settingsPanel').style.display = 'none';
        });

        // Reset settings
        document.getElementById('resetSettings').addEventListener('click', function() {
            // Reset to defaults
            document.getElementById('watermarkOpacity').value = 0.7;
            document.getElementById('watermarkSize').value = 36;
            document.getElementById('watermarkColor').value = 'gray';
            document.getElementById('watermarkRotation').value = -45;
            document.getElementById('watermarkBorder').value = 'thin';
            document.getElementById('enhanceForPrint').checked = false;

            // Update settings object
            watermarkSettings.opacity = 0.7;
            watermarkSettings.size = 36;
            watermarkSettings.color = 'gray';
            watermarkSettings.rotation = -45;
            watermarkSettings.border = 'thin';
            watermarkSettings.enhanceForPrint = false;
            watermarkSettings.printOpacity = 0.7;

            // Apply to watermarks
            applyWatermarkSettings();
        });

        // Apply watermark settings
        function applyWatermarkSettings() {
            const watermarks = document.querySelectorAll('.watermark');

            // Get color and border styles
            const colorStyle = colorMap[watermarkSettings.color].normal.replace('$opacity', watermarkSettings.opacity);
            let borderStyle = 'none';

            if (watermarkSettings.border !== 'none') {
                borderStyle = borderMap[watermarkSettings.border].normal.replace('$color', colorStyle);
            }

            watermarks.forEach(watermark => {
                watermark.style.fontSize = watermarkSettings.size + 'px';
                watermark.style.color = colorStyle;
                watermark.style.opacity = watermarkSettings.opacity;
                watermark.style.transform = `translate(-50%, -50%) rotate(${watermarkSettings.rotation}deg)`;

                if (watermarkSettings.border === 'none') {
                    watermark.style.border = 'none';
                } else {
                    watermark.style.border = borderStyle;
                }
            });
        }

        // Toggle between portrait and landscape orientation
        document.getElementById('toggleOrientation').addEventListener('click', function() {
            const body = document.body;
            const orientationText = document.getElementById('orientationText');

            if (body.classList.contains('landscape')) {
                // Switch to portrait
                body.classList.remove('landscape');
                orientationText.textContent = 'ল্যান্ডস্কেপ';

                // Create a style element for print orientation
                let style = document.querySelector('#print-orientation');
                if (!style) {
                    style = document.createElement('style');
                    style.id = 'print-orientation';
                    document.head.appendChild(style);
                }
                style.textContent = '@page { size: portrait; }';

            } else {
                // Switch to landscape
                body.classList.add('landscape');
                orientationText.textContent = 'পোর্ট্রেট';

                // Create a style element for print orientation
                let style = document.querySelector('#print-orientation');
                if (!style) {
                    style = document.createElement('style');
                    style.id = 'print-orientation';
                    document.head.appendChild(style);
                }
                style.textContent = '@page { size: landscape; }';
            }

            // Adjust receipt container layout for better fit
            const containers = document.querySelectorAll('.receipt-container');
            if (body.classList.contains('landscape')) {
                containers.forEach(container => {
                    container.style.padding = '5mm';
                });
            } else {
                containers.forEach(container => {
                    container.style.padding = '10mm';
                });
            }
        });

        // Function to prepare watermarks for printing and then print
        function prepareAndPrint() {
            // Make watermarks more visible for printing if enhanceForPrint is enabled
            const watermarks = document.querySelectorAll('.watermark');

            // Store original styles to restore after printing
            const originalStyles = [];

            // Get print color and border styles
            const printColorStyle = colorMap[watermarkSettings.color].print.replace('$opacity', watermarkSettings.printOpacity);
            let printBorderStyle = 'none';

            if (watermarkSettings.border !== 'none') {
                printBorderStyle = borderMap[watermarkSettings.border].print.replace('$color', printColorStyle);
            }

            watermarks.forEach((watermark, index) => {
                // Save original styles
                originalStyles[index] = {
                    color: watermark.style.color,
                    border: watermark.style.border,
                    backgroundColor: watermark.style.backgroundColor,
                    fontSize: watermark.style.fontSize,
                    opacity: watermark.style.opacity,
                    transform: watermark.style.transform
                };

                // Only enhance watermark if the option is enabled
                if (watermarkSettings.enhanceForPrint) {
                    // Apply print-friendly styles
                    watermark.style.color = printColorStyle;

                    if (watermarkSettings.border !== 'none') {
                        watermark.style.border = printBorderStyle;
                    }

                    watermark.style.backgroundColor = 'rgba(240, 240, 240, 0.3)';
                    watermark.style.fontSize = watermarkSettings.printSize + 'px';
                    watermark.style.opacity = '1';
                }
            });

            // Print the page
            setTimeout(() => {
                window.print();

                // Restore original styles after printing
                setTimeout(() => {
                    // Only restore if we changed the styles
                    if (watermarkSettings.enhanceForPrint) {
                        watermarks.forEach((watermark, index) => {
                            watermark.style.color = originalStyles[index].color;
                            watermark.style.border = originalStyles[index].border;
                            watermark.style.backgroundColor = originalStyles[index].backgroundColor;
                            watermark.style.fontSize = originalStyles[index].fontSize;
                            watermark.style.opacity = originalStyles[index].opacity;
                            watermark.style.transform = originalStyles[index].transform;
                        });
                    }
                }, 1000);
            }, 300);
        }

        // Initialize watermark settings on page load
        document.addEventListener('DOMContentLoaded', function() {
            applyWatermarkSettings();
        });
    </script>
</body>
</html>