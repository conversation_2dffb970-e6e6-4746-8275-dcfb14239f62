<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    die('Unauthorized access');
}

require_once '../includes/dbh.inc.php';

// Create class_level_templates table
$createTableQuery = "
CREATE TABLE IF NOT EXISTS class_level_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    class_level VARCHAR(20) NOT NULL UNIQUE,
    class_level_name VARCHAR(100) NOT NULL,
    description TEXT,
    default_total_marks INT DEFAULT 100,
    has_cq TINYINT(1) DEFAULT 1,
    has_mcq TINYINT(1) DEFAULT 1,
    has_practical TINYINT(1) DEFAULT 0,
    default_cq_marks DECIMAL(5,2) DEFAULT 70.00,
    default_mcq_marks DECIMAL(5,2) DEFAULT 30.00,
    default_practical_marks DECIMAL(5,2) DEFAULT 0.00,
    cq_percentage DECIMAL(5,2) DEFAULT 70.00,
    mcq_percentage DECIMAL(5,2) DEFAULT 30.00,
    practical_percentage DECIMAL(5,2) DEFAULT 0.00,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($createTableQuery)) {
    echo "✅ class_level_templates table created successfully<br>";
} else {
    echo "❌ Error creating table: " . $conn->error . "<br>";
}

// Insert default templates for Bangladesh education system
$templates = [
    [
        'class_level' => 'primary_1_5',
        'class_level_name' => 'প্রাথমিক (১ম-৫ম শ্রেণী)',
        'description' => 'প্রাথমিক স্তরের জন্য সাধারণ মার্কস বিতরণ। শুধুমাত্র লিখিত পরীক্ষা।',
        'default_total_marks' => 100,
        'has_cq' => 1,
        'has_mcq' => 0,
        'has_practical' => 0,
        'default_cq_marks' => 100.00,
        'default_mcq_marks' => 0.00,
        'default_practical_marks' => 0.00,
        'cq_percentage' => 100.00,
        'mcq_percentage' => 0.00,
        'practical_percentage' => 0.00
    ],
    [
        'class_level' => 'junior_6_8',
        'class_level_name' => 'জুনিয়র (৬ষ্ঠ-৮ম শ্রেণী)',
        'description' => 'জুনিয়র স্তরের জন্য মার্কস বিতরণ। মূলত লিখিত, কিছু বিষয়ে ব্যবহারিক।',
        'default_total_marks' => 100,
        'has_cq' => 1,
        'has_mcq' => 0,
        'has_practical' => 0,
        'default_cq_marks' => 100.00,
        'default_mcq_marks' => 0.00,
        'default_practical_marks' => 0.00,
        'cq_percentage' => 100.00,
        'mcq_percentage' => 0.00,
        'practical_percentage' => 0.00
    ],
    [
        'class_level' => 'ssc_9_10',
        'class_level_name' => 'মাধ্যমিক/SSC (৯ম-১০ম শ্রেণী)',
        'description' => 'SSC স্তরের জন্য মার্কস বিতরণ। সৃজনশীল ৭০ + বহুনির্বাচনি ৩০ + ব্যবহারিক (প্রযোজ্য ক্ষেত্রে)।',
        'default_total_marks' => 100,
        'has_cq' => 1,
        'has_mcq' => 1,
        'has_practical' => 0,
        'default_cq_marks' => 70.00,
        'default_mcq_marks' => 30.00,
        'default_practical_marks' => 0.00,
        'cq_percentage' => 70.00,
        'mcq_percentage' => 30.00,
        'practical_percentage' => 0.00
    ],
    [
        'class_level' => 'hsc_11_12',
        'class_level_name' => 'উচ্চ মাধ্যমিক/HSC (একাদশ-দ্বাদশ শ্রেণী)',
        'description' => 'HSC স্তরের জন্য মার্কস বিতরণ। সৃজনশীল ৭০ + বহুনির্বাচনি ৩০ + ব্যবহারিক (বিষয় অনুযায়ী)।',
        'default_total_marks' => 100,
        'has_cq' => 1,
        'has_mcq' => 1,
        'has_practical' => 0,
        'default_cq_marks' => 70.00,
        'default_mcq_marks' => 30.00,
        'default_practical_marks' => 0.00,
        'cq_percentage' => 70.00,
        'mcq_percentage' => 30.00,
        'practical_percentage' => 0.00
    ],
    [
        'class_level' => 'science_practical',
        'class_level_name' => 'বিজ্ঞান বিভাগ (ব্যবহারিক সহ)',
        'description' => 'বিজ্ঞান বিভাগের ব্যবহারিক বিষয়ের জন্য। সৃজনশীল ৫০ + বহুনির্বাচনি ২৫ + ব্যবহারিক ২৫।',
        'default_total_marks' => 100,
        'has_cq' => 1,
        'has_mcq' => 1,
        'has_practical' => 1,
        'default_cq_marks' => 50.00,
        'default_mcq_marks' => 25.00,
        'default_practical_marks' => 25.00,
        'cq_percentage' => 50.00,
        'mcq_percentage' => 25.00,
        'practical_percentage' => 25.00
    ],
    [
        'class_level' => 'computer_ict',
        'class_level_name' => 'কম্পিউটার/ICT বিষয়',
        'description' => 'কম্পিউটার ও ICT বিষয়ের জন্য। সৃজনশীল ৪০ + বহুনির্বাচনি ৩০ + ব্যবহারিক ৩০।',
        'default_total_marks' => 100,
        'has_cq' => 1,
        'has_mcq' => 1,
        'has_practical' => 1,
        'default_cq_marks' => 40.00,
        'default_mcq_marks' => 30.00,
        'default_practical_marks' => 30.00,
        'cq_percentage' => 40.00,
        'mcq_percentage' => 30.00,
        'practical_percentage' => 30.00
    ]
];

// Insert templates
foreach ($templates as $template) {
    $checkQuery = "SELECT id FROM class_level_templates WHERE class_level = ?";
    $checkStmt = $conn->prepare($checkQuery);
    $checkStmt->bind_param("s", $template['class_level']);
    $checkStmt->execute();
    $result = $checkStmt->get_result();
    
    if ($result->num_rows == 0) {
        $insertQuery = "INSERT INTO class_level_templates 
                       (class_level, class_level_name, description, default_total_marks, 
                        has_cq, has_mcq, has_practical, default_cq_marks, default_mcq_marks, 
                        default_practical_marks, cq_percentage, mcq_percentage, practical_percentage) 
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $insertStmt = $conn->prepare($insertQuery);
        $insertStmt->bind_param("sssiiiddddddd", 
            $template['class_level'],
            $template['class_level_name'],
            $template['description'],
            $template['default_total_marks'],
            $template['has_cq'],
            $template['has_mcq'],
            $template['has_practical'],
            $template['default_cq_marks'],
            $template['default_mcq_marks'],
            $template['default_practical_marks'],
            $template['cq_percentage'],
            $template['mcq_percentage'],
            $template['practical_percentage']
        );
        
        if ($insertStmt->execute()) {
            echo "✅ Template '{$template['class_level_name']}' inserted successfully<br>";
        } else {
            echo "❌ Error inserting template '{$template['class_level_name']}': " . $insertStmt->error . "<br>";
        }
    } else {
        echo "ℹ️ Template '{$template['class_level_name']}' already exists<br>";
    }
}

echo "<br><strong>✅ Class level templates setup completed!</strong><br>";
echo "<a href='subject_exam_pattern.php'>Go to Subject Exam Pattern</a>";
?>
