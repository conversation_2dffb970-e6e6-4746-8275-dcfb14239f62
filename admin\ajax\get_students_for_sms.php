<?php
// Include database connection
require_once "../../includes/dbh.inc.php";

// Get parameters
$class_id = isset($_POST['class_id']) ? $_POST['class_id'] : '';
$section = isset($_POST['section']) ? $_POST['section'] : '';
$notification_type = isset($_POST['notification_type']) ? $_POST['notification_type'] : 'attendance';
$date = isset($_POST['date']) ? $_POST['date'] : '';
$exam_id = isset($_POST['exam_id']) ? $_POST['exam_id'] : '';

// Validate parameters
if (empty($class_id)) {
    echo '<tr><td colspan="7" class="text-center text-danger">শ্রেণী নির্বাচন করুন</td></tr>';
    exit;
}

if ($notification_type == 'attendance' && empty($date)) {
    echo '<tr><td colspan="7" class="text-center text-danger">তারিখ নির্বাচন করুন</td></tr>';
    exit;
}

if ($notification_type == 'result' && empty($exam_id)) {
    echo '<tr><td colspan="7" class="text-center text-danger">পরীক্ষা নির্বাচন করুন</td></tr>';
    exit;
}

// Check if students table exists
$check_students_sql = "SHOW TABLES LIKE 'students'";
$students_result = $conn->query($check_students_sql);
$students_table_exists = $students_result->num_rows > 0;

if (!$students_table_exists) {
    // If students table doesn't exist, show dummy data
    echo '<tr><td colspan="7" class="text-center text-warning">শিক্ষার্থী টেবিল পাওয়া যায়নি। ডামি ডাটা দেখানো হচ্ছে।</td></tr>';

    // Generate dummy data
    for ($i = 1; $i <= 5; $i++) {
        echo '<tr>';
        echo '<td><input type="checkbox" class="form-check-input student-checkbox" name="selected_students[]" value="' . $i . '"></td>';
        echo '<td>' . $i . '</td>';
        echo '<td>শিক্ষার্থী #' . $i . '</td>';
        echo '<td>01712345' . sprintf("%03d", $i) . '</td>';
        echo '<td>01812345' . sprintf("%03d", $i) . '</td>';

        if ($notification_type == 'attendance') {
            $status = ($i % 2 == 0) ? 'উপস্থিত' : 'অনুপস্থিত';
            $status_class = ($i % 2 == 0) ? 'bg-success' : 'bg-danger';
            echo '<td><span class="badge ' . $status_class . '">' . $status . '</span></td>';
        } else {
            echo '<td>' . rand(60, 95) . '</td>';
            echo '<td>A</td>';
        }

        echo '</tr>';
    }

    exit;
}

// Build SQL query based on notification type
if ($notification_type == 'attendance') {
    $sql = "SELECT s.id, s.name, s.roll, s.phone, p.phone as parent_phone, c.class_name, a.status as attendance_status
           FROM students s
           LEFT JOIN parents p ON s.id = p.student_id
           LEFT JOIN classes c ON s.class_id = c.id
           LEFT JOIN attendance a ON s.id = a.student_id AND a.date = ?
           WHERE s.class_id = ?";

    // Add section filter if provided
    if (!empty($section)) {
        $sql .= " AND s.section = ?";
    }

    $sql .= " ORDER BY s.roll ASC";

    // Prepare and execute query
    $stmt = $conn->prepare($sql);

    if (!empty($section)) {
        $stmt->bind_param("sis", $date, $class_id, $section);
    } else {
        $stmt->bind_param("si", $date, $class_id);
    }
} else { // Result notification
    $sql = "SELECT s.id, s.name, s.roll, s.phone, p.phone as parent_phone, c.class_name, r.marks, r.grade
           FROM students s
           LEFT JOIN parents p ON s.id = p.student_id
           LEFT JOIN classes c ON s.class_id = c.id
           LEFT JOIN results r ON s.id = r.student_id AND r.exam_id = ?
           WHERE s.class_id = ?";

    // Add section filter if provided
    if (!empty($section)) {
        $sql .= " AND s.section = ?";
    }

    $sql .= " ORDER BY s.roll ASC";

    // Prepare and execute query
    $stmt = $conn->prepare($sql);

    if (!empty($section)) {
        $stmt->bind_param("iis", $exam_id, $class_id, $section);
    } else {
        $stmt->bind_param("ii", $exam_id, $class_id);
    }
}

$stmt->execute();
$result = $stmt->get_result();

// Check if any students found
if ($result->num_rows == 0) {
    echo '<tr><td colspan="7" class="text-center">কোন শিক্ষার্থী পাওয়া যায়নি</td></tr>';
    exit;
}

// Display students
while ($student = $result->fetch_assoc()) {
    echo '<tr>';
    echo '<td><input type="checkbox" class="form-check-input student-checkbox" name="selected_students[]" value="' . $student['id'] . '"></td>';
    echo '<td>' . $student['roll'] . '</td>';
    echo '<td>' . $student['name'] . '</td>';
    echo '<td>' . ($student['phone'] ? $student['phone'] : '<span class="text-muted">নেই</span>') . '</td>';
    echo '<td>' . ($student['parent_phone'] ? $student['parent_phone'] : '<span class="text-muted">নেই</span>') . '</td>';

    if ($notification_type == 'attendance') {
        $status_text = '';
        $status_class = '';

        if ($student['attendance_status'] == 'present') {
            $status_text = 'উপস্থিত';
            $status_class = 'bg-success';
        } elseif ($student['attendance_status'] == 'absent') {
            $status_text = 'অনুপস্থিত';
            $status_class = 'bg-danger';
        } else {
            $status_text = 'অজানা';
            $status_class = 'bg-secondary';
        }

        echo '<td><span class="badge ' . $status_class . '">' . $status_text . '</span></td>';
    } else {
        echo '<td>' . (isset($student['marks']) ? $student['marks'] : '<span class="text-muted">নেই</span>') . '</td>';
        echo '<td>' . (isset($student['grade']) ? $student['grade'] : '<span class="text-muted">নেই</span>') . '</td>';
    }

    echo '</tr>';
}

// Close statement
$stmt->close();
?>
