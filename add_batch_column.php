<?php
// Database Connection
require_once 'includes/dbh.inc.php';

echo "Adding 'batch' column to students table...\n";

// SQL to add the batch column
$sql = "ALTER TABLE students ADD COLUMN batch VARCHAR(50) DEFAULT NULL";

if ($conn->query($sql) === TRUE) {
    echo "SUCCESS: The 'batch' column was added to the students table.\n";
} else {
    echo "ERROR: " . $conn->error . "\n";
}

// Verify the column was added
$verify_query = "SHOW COLUMNS FROM students LIKE 'batch'";
$result = $conn->query($verify_query);

if ($result && $result->num_rows > 0) {
    echo "\nVerification successful - 'batch' column exists in the students table.\n";
    
    // Show column details
    $column_info = $result->fetch_assoc();
    echo "Field Name: " . $column_info['Field'] . "\n";
    echo "Type: " . $column_info['Type'] . "\n";
    echo "Null: " . $column_info['Null'] . "\n";
    echo "Key: " . $column_info['Key'] . "\n";
    echo "Default: " . ($column_info['Default'] === NULL ? 'NULL' : $column_info['Default']) . "\n";
    echo "Extra: " . $column_info['Extra'] . "\n";
} else {
    echo "\nVerification failed - 'batch' column was not added.\n";
}

// Close connection
$conn->close();
?> 