<?php
// Database Connection
require_once 'includes/dbh.inc.php';

echo "<h1>Students Table Structure</h1>";

// Check if students table exists
$tableCheck = $conn->query("SHOW TABLES LIKE 'students'");
if ($tableCheck->num_rows == 0) {
    echo "<p style='color:red'>The students table does not exist!</p>";
} else {
    // Get all columns from the students table
    $columnsQuery = "SHOW COLUMNS FROM students";
    $columnsResult = $conn->query($columnsQuery);
    
    if ($columnsResult && $columnsResult->num_rows > 0) {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        $columns = [];
        while ($column = $columnsResult->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . ($column['Default'] === NULL ? 'NULL' : $column['Default']) . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
            
            $columns[] = $column['Field'];
        }
        
        echo "</table>";
        
        // Display all column names in a list for easy reference
        echo "<h2>Column Names:</h2>";
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li>" . $column . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color:red'>Error retrieving columns: " . $conn->error . "</p>";
    }
}

// Close connection
$conn->close();
?>
