<?php
/**
 * Class Subjects Helper Functions
 * Functions to get class-wise subject configuration for exam results
 */

require_once '../includes/dbh.inc.php';

/**
 * Get subjects assigned to a specific class
 * @param int $classId Class ID
 * @param int|null $departmentId Department ID (optional)
 * @param string|null $subjectType Subject type filter (optional)
 * @return array Array of subjects
 */
function getClassSubjects($classId, $departmentId = null, $subjectType = null) {
    global $conn;
    
    $query = "SELECT cs.*, s.subject_name, s.subject_code, s.category
              FROM class_subjects cs
              JOIN subjects s ON cs.subject_id = s.id
              WHERE cs.class_id = ? AND s.is_active = 1";
    
    $params = [$classId];
    $types = "i";
    
    if ($departmentId !== null) {
        $query .= " AND (cs.department_id = ? OR cs.department_id IS NULL)";
        $params[] = $departmentId;
        $types .= "i";
    }
    
    if ($subjectType !== null) {
        $query .= " AND cs.subject_type = ?";
        $params[] = $subjectType;
        $types .= "s";
    }
    
    $query .= " ORDER BY cs.subject_type, s.subject_name";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $subjects = [];
    while ($row = $result->fetch_assoc()) {
        $subjects[] = $row;
    }
    
    return $subjects;
}

/**
 * Get required subjects for a class
 * @param int $classId Class ID
 * @param int|null $departmentId Department ID (optional)
 * @return array Array of required subjects
 */
function getRequiredSubjects($classId, $departmentId = null) {
    return getClassSubjects($classId, $departmentId, 'required');
}

/**
 * Get optional subjects for a class
 * @param int $classId Class ID
 * @param int|null $departmentId Department ID (optional)
 * @return array Array of optional subjects
 */
function getOptionalSubjects($classId, $departmentId = null) {
    return getClassSubjects($classId, $departmentId, 'optional');
}

/**
 * Get fourth subjects for a class
 * @param int $classId Class ID
 * @param int|null $departmentId Department ID (optional)
 * @return array Array of fourth subjects
 */
function getFourthSubjects($classId, $departmentId = null) {
    return getClassSubjects($classId, $departmentId, 'fourth');
}

/**
 * Check if a subject is assigned to a class
 * @param int $classId Class ID
 * @param int $subjectId Subject ID
 * @param int|null $departmentId Department ID (optional)
 * @return bool True if subject is assigned, false otherwise
 */
function isSubjectAssignedToClass($classId, $subjectId, $departmentId = null) {
    global $conn;
    
    $query = "SELECT COUNT(*) as count FROM class_subjects WHERE class_id = ? AND subject_id = ?";
    $params = [$classId, $subjectId];
    $types = "ii";
    
    if ($departmentId !== null) {
        $query .= " AND (department_id = ? OR department_id IS NULL)";
        $params[] = $departmentId;
        $types .= "i";
    }
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    return $row['count'] > 0;
}

/**
 * Get subject type for a class
 * @param int $classId Class ID
 * @param int $subjectId Subject ID
 * @param int|null $departmentId Department ID (optional)
 * @return string|null Subject type or null if not assigned
 */
function getSubjectTypeForClass($classId, $subjectId, $departmentId = null) {
    global $conn;
    
    $query = "SELECT subject_type FROM class_subjects WHERE class_id = ? AND subject_id = ?";
    $params = [$classId, $subjectId];
    $types = "ii";
    
    if ($departmentId !== null) {
        $query .= " AND (department_id = ? OR department_id IS NULL)";
        $params[] = $departmentId;
        $types .= "i";
    }
    
    $query .= " LIMIT 1";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        return $row['subject_type'];
    }
    
    return null;
}

/**
 * Get all classes with their subject configuration
 * @return array Array of classes with subject counts
 */
function getClassesWithSubjectConfig() {
    global $conn;
    
    $query = "SELECT c.id, c.class_name,
                     COUNT(cs.id) as total_subjects,
                     SUM(CASE WHEN cs.subject_type = 'required' THEN 1 ELSE 0 END) as required_count,
                     SUM(CASE WHEN cs.subject_type = 'optional' THEN 1 ELSE 0 END) as optional_count,
                     SUM(CASE WHEN cs.subject_type = 'fourth' THEN 1 ELSE 0 END) as fourth_count
              FROM classes c
              LEFT JOIN class_subjects cs ON c.id = cs.class_id
              GROUP BY c.id, c.class_name
              ORDER BY c.class_name";
    
    $result = $conn->query($query);
    $classes = [];
    
    while ($row = $result->fetch_assoc()) {
        $classes[] = $row;
    }
    
    return $classes;
}

/**
 * Validate class subject configuration for exam result generation
 * @param int $classId Class ID
 * @param int|null $departmentId Department ID (optional)
 * @return array Validation result with status and message
 */
function validateClassSubjectConfig($classId, $departmentId = null) {
    $subjects = getClassSubjects($classId, $departmentId);
    
    if (empty($subjects)) {
        return [
            'valid' => false,
            'message' => 'এই ক্লাসের জন্য কোন বিষয় কনফিগার করা হয়নি।'
        ];
    }
    
    $requiredCount = count(getRequiredSubjects($classId, $departmentId));
    
    if ($requiredCount === 0) {
        return [
            'valid' => false,
            'message' => 'এই ক্লাসের জন্য কোন আবশ্যিক বিষয় নির্ধারণ করা হয়নি।'
        ];
    }
    
    return [
        'valid' => true,
        'message' => 'ক্লাসের বিষয় কনফিগারেশন সঠিক আছে।',
        'total_subjects' => count($subjects),
        'required_subjects' => $requiredCount,
        'optional_subjects' => count(getOptionalSubjects($classId, $departmentId)),
        'fourth_subjects' => count(getFourthSubjects($classId, $departmentId))
    ];
}
?>
