<?php
// Database connection settings
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "zfaw"; // Changed from college_management to zfaw

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Set charset to utf8mb4
$conn->set_charset("utf8mb4");

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Common functions
function isLoggedIn() {
    return isset($_SESSION['userId']);
}

function isAdmin() {
    return isset($_SESSION['userType']) && $_SESSION['userType'] === 'admin';
}

function isTeacher() {
    return isset($_SESSION['userType']) && $_SESSION['userType'] === 'teacher';
}

function isStudent() {
    return isset($_SESSION['userType']) && $_SESSION['userType'] === 'student';
}

// Site settings
define('SITE_NAME', 'School Management System');
define('SITE_URL', 'http://localhost/zfaw/');
?> 
