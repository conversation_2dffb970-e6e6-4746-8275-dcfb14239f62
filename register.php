<?php
// Include database connection
require_once "includes/dbh.inc.php";

// Page title
$page_title = "রেজিস্ট্রেশন";

// Initialize variables
$name = $email = $phone = $password = $confirm_password = "";
$name_err = $email_err = $phone_err = $password_err = $confirm_password_err = "";
$success_message = "";
$error_message = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    
    // Validate name
    if (empty(trim($_POST["name"]))) {
        $name_err = "নাম লিখুন";
    } else {
        $name = trim($_POST["name"]);
    }
    
    // Validate email
    if (empty(trim($_POST["email"]))) {
        $email_err = "ইমেইল লিখুন";
    } else {
        // Prepare a select statement
        $sql = "SELECT id FROM users WHERE email = ?";
        
        if ($stmt = $conn->prepare($sql)) {
            // Bind variables to the prepared statement as parameters
            $stmt->bind_param("s", $param_email);
            
            // Set parameters
            $param_email = trim($_POST["email"]);
            
            // Attempt to execute the prepared statement
            if ($stmt->execute()) {
                // Store result
                $stmt->store_result();
                
                if ($stmt->num_rows == 1) {
                    $email_err = "এই ইমেইল দিয়ে ইতিমধ্যে একটি অ্যাকাউন্ট রয়েছে";
                } else {
                    $email = trim($_POST["email"]);
                }
            } else {
                $error_message = "দুঃখিত! কিছু ভুল হয়েছে। পরে আবার চেষ্টা করুন।";
            }

            // Close statement
            $stmt->close();
        }
    }
    
    // Validate phone
    if (empty(trim($_POST["phone"]))) {
        $phone_err = "ফোন নম্বর লিখুন";
    } else {
        $phone = trim($_POST["phone"]);
    }
    
    // Validate password
    if (empty(trim($_POST["password"]))) {
        $password_err = "পাসওয়ার্ড লিখুন";
    } elseif (strlen(trim($_POST["password"])) < 6) {
        $password_err = "পাসওয়ার্ড কমপক্ষে ৬ অক্ষরের হতে হবে";
    } else {
        $password = trim($_POST["password"]);
    }
    
    // Validate confirm password
    if (empty(trim($_POST["confirm_password"]))) {
        $confirm_password_err = "পাসওয়ার্ড নিশ্চিত করুন";
    } else {
        $confirm_password = trim($_POST["confirm_password"]);
        if (empty($password_err) && ($password != $confirm_password)) {
            $confirm_password_err = "পাসওয়ার্ড মিলছে না";
        }
    }
    
    // Check input errors before inserting in database
    if (empty($name_err) && empty($email_err) && empty($phone_err) && empty($password_err) && empty($confirm_password_err)) {
        
        // Prepare an insert statement
        $sql = "INSERT INTO users (name, email, phone, password, created_at) VALUES (?, ?, ?, ?, NOW())";
         
        if ($stmt = $conn->prepare($sql)) {
            // Bind variables to the prepared statement as parameters
            $stmt->bind_param("ssss", $param_name, $param_email, $param_phone, $param_password);
            
            // Set parameters
            $param_name = $name;
            $param_email = $email;
            $param_phone = $phone;
            $param_password = password_hash($password, PASSWORD_DEFAULT); // Creates a password hash
            
            // Attempt to execute the prepared statement
            if ($stmt->execute()) {
                // Redirect to login page
                $success_message = "রেজিস্ট্রেশন সফল হয়েছে! আপনি এখন লগইন করতে পারেন।";
                $name = $email = $phone = $password = $confirm_password = "";
            } else {
                $error_message = "দুঃখিত! কিছু ভুল হয়েছে। পরে আবার চেষ্টা করুন।";
            }

            // Close statement
            $stmt->close();
        }
    }
    
    // Close connection
    $conn->close();
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title><?php echo $page_title; ?> - ZFAW</title>
    
    <!-- Bootstrap CSS -->
    
    
    <style>
        .register-section {
            padding: 50px 0;
        }
        
        .register-card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .register-card .card-header {
            background-color: #007bff;
            color: white;
            text-align: center;
            padding: 20px;
            border-bottom: none;
        }
        
        .register-card .card-body {
            padding: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-control {
            border-radius: 10px;
            padding: 12px 15px;
        }
        
        .btn-register {
            background-color: #007bff;
            border: none;
            border-radius: 10px;
            padding: 12px 20px;
            font-weight: bold;
            width: 100%;
            margin-top: 10px;
        }
        
        .btn-register:hover {
            background-color: #0056b3;
        }
        
        .register-icon {
            font-size: 50px;
            margin-bottom: 10px;
        }
        
        .login-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .input-group-text {
            background-color: #f8f9fa;
            border-radius: 10px 0 0 10px;
            border-right: none;
        }
        
        .input-group .form-control {
            border-radius: 0 10px 10px 0;
            border-left: none;
        }
        
        .input-group-text i {
            width: 20px;
            text-align: center;
            color: #6c757d;
        }
        
        .alert {
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">ZFAW</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">হোম</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">বিষয়সমূহ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">আমাদের সম্পর্কে</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">যোগাযোগ</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <a href="login.php" class="btn btn-outline-light me-2">লগইন</a>
                    <a href="register.php" class="btn btn-light">রেজিস্ট্রেশন</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Scrolling Notice Section (iframe solution with vertical alignment fix) -->
    <div style="width: 100%; height: 85px; position: relative; margin-bottom: 15px; display: flex; align-items: center; justify-content: center; overflow: hidden;">
        <iframe src="centered_notice.php" style="width: 100%; height: 85px; border: none; overflow: hidden; position: absolute; top: 0; left: 0;"></iframe>
    </div>

    <!-- Header -->
    <header class="bg-light py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="display-4">রেজিস্ট্রেশন</h1>
                    <p class="lead">নতুন অ্যাকাউন্ট তৈরি করুন</p>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <section class="register-section">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-8 col-lg-6">
                        <?php if (!empty($success_message)): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle me-2"></i> <?php echo $success_message; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($error_message)): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error_message; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>
                        
                        <div class="card register-card">
                            <div class="card-header">
                                <i class="fas fa-user-plus register-icon"></i>
                                <h3>নতুন অ্যাকাউন্ট তৈরি করুন</h3>
                            </div>
                            <div class="card-body">
                                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                                    <div class="form-group">
                                        <label for="name" class="form-label">পূর্ণ নাম</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                                            <input type="text" name="name" id="name" class="form-control <?php echo (!empty($name_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $name; ?>" placeholder="আপনার পূর্ণ নাম লিখুন">
                                        </div>
                                        <div class="invalid-feedback"><?php echo $name_err; ?></div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="email" class="form-label">ইমেইল</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                            <input type="email" name="email" id="email" class="form-control <?php echo (!empty($email_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $email; ?>" placeholder="আপনার ইমেইল লিখুন">
                                        </div>
                                        <div class="invalid-feedback"><?php echo $email_err; ?></div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="phone" class="form-label">ফোন নম্বর</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                            <input type="text" name="phone" id="phone" class="form-control <?php echo (!empty($phone_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $phone; ?>" placeholder="আপনার ফোন নম্বর লিখুন">
                                        </div>
                                        <div class="invalid-feedback"><?php echo $phone_err; ?></div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="password" class="form-label">পাসওয়ার্ড</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                            <input type="password" name="password" id="password" class="form-control <?php echo (!empty($password_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $password; ?>" placeholder="পাসওয়ার্ড লিখুন">
                                        </div>
                                        <div class="invalid-feedback"><?php echo $password_err; ?></div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="confirm_password" class="form-label">পাসওয়ার্ড নিশ্চিত করুন</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                            <input type="password" name="confirm_password" id="confirm_password" class="form-control <?php echo (!empty($confirm_password_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $confirm_password; ?>" placeholder="পাসওয়ার্ড আবার লিখুন">
                                        </div>
                                        <div class="invalid-feedback"><?php echo $confirm_password_err; ?></div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <button type="submit" class="btn btn-primary btn-register">
                                            <i class="fas fa-user-plus me-2"></i> রেজিস্ট্রেশন করুন
                                        </button>
                                    </div>
                                </form>
                                
                                <div class="login-link">
                                    <p>ইতিমধ্যে অ্যাকাউন্ট আছে? <a href="login.php">লগইন করুন</a></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <h5>ZFAW</h5>
                    <p>উচ্চমানের শিক্ষা প্রদানের মাধ্যমে শিক্ষার্থীদের ভবিষ্যৎ গড়তে সাহায্য করা আমাদের লক্ষ্য।</p>
                </div>
                <div class="col-md-4 mb-3">
                    <h5>যোগাযোগ</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> ১২৩, মেইন রোড, ঢাকা</p>
                        <p><i class="fas fa-phone me-2"></i> +৮৮০১৭১২৩৪৫৬৭৮</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
                <div class="col-md-4 mb-3">
                    <h5>লিঙ্কসমূহ</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-white">হোম</a></li>
                        <li><a href="subjects.php" class="text-white">বিষয়সমূহ</a></li>
                        <li><a href="about.php" class="text-white">আমাদের সম্পর্কে</a></li>
                        <li><a href="contact.php" class="text-white">যোগাযোগ</a></li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?php echo date('Y'); ?> ZFAW. সর্বস্বত্ব সংরক্ষিত।</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
