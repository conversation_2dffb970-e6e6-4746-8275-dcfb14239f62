<?php
/**
 * Encryption class for database backups
 * This class handles encryption and decryption of backup files
 */
class Encryption {
    private $key;
    private $method = 'aes-256-cbc';
    private $hardwareId;
    
    /**
     * Constructor
     * @param string $password Password for encryption/decryption
     */
    public function __construct($password = null) {
        // If no password is provided, use a default one
        if ($password === null) {
            $password = 'zfaw_default_encryption_key';
        }
        
        // Generate a key from the password
        $this->key = hash('sha256', $password, true);
        
        // Get hardware ID (combination of machine-specific identifiers)
        $this->hardwareId = $this->getHardwareId();
    }
    
    /**
     * Encrypt data
     * @param string $data Data to encrypt
     * @param bool $bindToHardware Whether to bind the encryption to this hardware
     * @return string Encrypted data
     */
    public function encrypt($data, $bindToHardware = true) {
        // Generate an initialization vector
        $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length($this->method));
        
        // Add hardware ID if binding is enabled
        if ($bindToHardware) {
            $data = $this->hardwareId . '|' . $data;
        }
        
        // Encrypt the data
        $encrypted = openssl_encrypt($data, $this->method, $this->key, 0, $iv);
        
        // Combine the IV and encrypted data
        $result = base64_encode($iv . $encrypted);
        
        return $result;
    }
    
    /**
     * Decrypt data
     * @param string $data Encrypted data
     * @param bool $checkHardware Whether to check hardware binding
     * @return string|false Decrypted data or false on failure
     */
    public function decrypt($data, $checkHardware = true) {
        // Decode the data
        $data = base64_decode($data);
        
        // Extract the initialization vector
        $ivLength = openssl_cipher_iv_length($this->method);
        $iv = substr($data, 0, $ivLength);
        
        // Extract the encrypted data
        $encrypted = substr($data, $ivLength);
        
        // Decrypt the data
        $decrypted = openssl_decrypt($encrypted, $this->method, $this->key, 0, $iv);
        
        if ($decrypted === false) {
            return false;
        }
        
        // Check hardware binding if enabled
        if ($checkHardware) {
            $parts = explode('|', $decrypted, 2);
            
            if (count($parts) !== 2) {
                return false;
            }
            
            list($storedHardwareId, $actualData) = $parts;
            
            // Verify hardware ID
            if ($storedHardwareId !== $this->hardwareId) {
                return false;
            }
            
            return $actualData;
        }
        
        return $decrypted;
    }
    
    /**
     * Get a unique hardware identifier
     * @return string Hardware ID
     */
    private function getHardwareId() {
        $identifiers = array();
        
        // Windows-specific hardware identifiers
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            // Get volume serial number
            $output = shell_exec('vol c:');
            if (preg_match('/Volume Serial Number is ([A-Z0-9\-]+)/', $output, $matches)) {
                $identifiers[] = $matches[1];
            }
            
            // Get CPU info
            $output = shell_exec('wmic cpu get ProcessorId');
            if (preg_match('/([A-Z0-9]+)/', $output, $matches)) {
                $identifiers[] = $matches[1];
            }
            
            // Get motherboard serial
            $output = shell_exec('wmic baseboard get serialnumber');
            if (preg_match('/([A-Z0-9]+)/', $output, $matches)) {
                $identifiers[] = $matches[1];
            }
        } else {
            // Linux/Mac identifiers
            // Get MAC address of first network interface
            $output = shell_exec("ifconfig | grep -o -E '([0-9a-f]{2}:){5}[0-9a-f]{2}' | head -n 1");
            if ($output) {
                $identifiers[] = trim($output);
            }
            
            // Get CPU info
            $output = shell_exec("cat /proc/cpuinfo | grep Serial | head -n 1");
            if (preg_match('/Serial\s*:\s*([0-9a-f]+)/', $output, $matches)) {
                $identifiers[] = $matches[1];
            }
        }
        
        // If no hardware identifiers were found, use the hostname
        if (empty($identifiers)) {
            $identifiers[] = gethostname();
            $identifiers[] = php_uname();
        }
        
        // Combine all identifiers and hash them
        return hash('sha256', implode('|', $identifiers));
    }
    
    /**
     * Encrypt a file
     * @param string $sourceFile Source file path
     * @param string $destFile Destination file path
     * @param bool $bindToHardware Whether to bind the encryption to this hardware
     * @return bool Success or failure
     */
    public function encryptFile($sourceFile, $destFile, $bindToHardware = true) {
        if (!file_exists($sourceFile)) {
            return false;
        }
        
        $data = file_get_contents($sourceFile);
        $encrypted = $this->encrypt($data, $bindToHardware);
        
        return file_put_contents($destFile, $encrypted) !== false;
    }
    
    /**
     * Decrypt a file
     * @param string $sourceFile Source file path
     * @param string $destFile Destination file path
     * @param bool $checkHardware Whether to check hardware binding
     * @return bool Success or failure
     */
    public function decryptFile($sourceFile, $destFile, $checkHardware = true) {
        if (!file_exists($sourceFile)) {
            return false;
        }
        
        $data = file_get_contents($sourceFile);
        $decrypted = $this->decrypt($data, $checkHardware);
        
        if ($decrypted === false) {
            return false;
        }
        
        return file_put_contents($destFile, $decrypted) !== false;
    }
}
?>
