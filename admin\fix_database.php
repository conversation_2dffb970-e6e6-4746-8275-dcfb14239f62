<?php
session_start();
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

echo "<h2>ডাটাবেস সমস্যা সমাধান</h2>";

try {
    echo "<h3>ধাপ ১: বর্তমান টেবিল স্ট্রাকচার চেক</h3>";
    
    // Check if students table exists
    $tableCheck = $conn->query("SHOW TABLES LIKE 'students'");
    if ($tableCheck->num_rows == 0) {
        echo "<p style='color: red;'>❌ students টেবিল পাওয়া যায়নি</p>";
        echo "<p>students টেবিল তৈরি করছি...</p>";
        
        $createStudents = "CREATE TABLE students (
            id INT AUTO_INCREMENT PRIMARY KEY,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            roll_no VARCHAR(20),
            student_id VARCHAR(20),
            class_id INT,
            email VARCHAR(100),
            phone VARCHAR(20),
            gender ENUM('male', 'female', 'other'),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $conn->query($createStudents);
        echo "<p style='color: green;'>✅ students টেবিল তৈরি করা হয়েছে</p>";
    } else {
        echo "<p style='color: green;'>✅ students টেবিল আছে</p>";
        
        // Check students table structure
        $result = $conn->query("DESCRIBE students");
        if ($result) {
            echo "<h4>students টেবিলের বর্তমান স্ট্রাকচার:</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            while ($row = $result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['Field'] . "</td>";
                echo "<td>" . $row['Type'] . "</td>";
                echo "<td>" . $row['Null'] . "</td>";
                echo "<td>" . $row['Key'] . "</td>";
                echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Check if roll_no column exists
            $columns = [];
            $result = $conn->query("DESCRIBE students");
            while ($row = $result->fetch_assoc()) {
                $columns[] = $row['Field'];
            }
            
            echo "<h3>ধাপ ২: অনুপস্থিত কলাম যোগ করা</h3>";
            
            if (!in_array('roll_no', $columns)) {
                echo "<p>roll_no কলাম নেই। যোগ করছি...</p>";
                $conn->query("ALTER TABLE students ADD COLUMN roll_no VARCHAR(20) AFTER last_name");
                echo "<p style='color: green;'>✅ roll_no কলাম যোগ করা হয়েছে</p>";
            } else {
                echo "<p style='color: green;'>✅ roll_no কলাম আছে</p>";
            }
            
            if (!in_array('student_id', $columns)) {
                echo "<p>student_id কলাম নেই। যোগ করছি...</p>";
                $conn->query("ALTER TABLE students ADD COLUMN student_id VARCHAR(20) AFTER roll_no");
                echo "<p style='color: green;'>✅ student_id কলাম যোগ করা হয়েছে</p>";
            } else {
                echo "<p style='color: green;'>✅ student_id কলাম আছে</p>";
            }
            
            if (!in_array('class_id', $columns)) {
                echo "<p>class_id কলাম নেই। যোগ করছি...</p>";
                $conn->query("ALTER TABLE students ADD COLUMN class_id INT AFTER student_id");
                echo "<p style='color: green;'>✅ class_id কলাম যোগ করা হয়েছে</p>";
            } else {
                echo "<p style='color: green;'>✅ class_id কলাম আছে</p>";
            }
            
            if (!in_array('email', $columns)) {
                echo "<p>email কলাম নেই। যোগ করছি...</p>";
                $conn->query("ALTER TABLE students ADD COLUMN email VARCHAR(100) AFTER class_id");
                echo "<p style='color: green;'>✅ email কলাম যোগ করা হয়েছে</p>";
            } else {
                echo "<p style='color: green;'>✅ email কলাম আছে</p>";
            }
            
            if (!in_array('phone', $columns)) {
                echo "<p>phone কলাম নেই। যোগ করছি...</p>";
                $conn->query("ALTER TABLE students ADD COLUMN phone VARCHAR(20) AFTER email");
                echo "<p style='color: green;'>✅ phone কলাম যোগ করা হয়েছে</p>";
            } else {
                echo "<p style='color: green;'>✅ phone কলাম আছে</p>";
            }
            
            if (!in_array('gender', $columns)) {
                echo "<p>gender কলাম নেই। যোগ করছি...</p>";
                $conn->query("ALTER TABLE students ADD COLUMN gender ENUM('male', 'female', 'other') AFTER phone");
                echo "<p style='color: green;'>✅ gender কলাম যোগ করা হয়েছে</p>";
            } else {
                echo "<p style='color: green;'>✅ gender কলাম আছে</p>";
            }
        }
    }
    
    // Check and create other required tables
    echo "<h3>ধাপ ৩: অন্যান্য টেবিল চেক</h3>";
    
    // Classes table
    $classCheck = $conn->query("SHOW TABLES LIKE 'classes'");
    if ($classCheck->num_rows == 0) {
        echo "<p>classes টেবিল তৈরি করছি...</p>";
        $createClasses = "CREATE TABLE classes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            class_name VARCHAR(100) NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $conn->query($createClasses);
        echo "<p style='color: green;'>✅ classes টেবিল তৈরি করা হয়েছে</p>";
    } else {
        echo "<p style='color: green;'>✅ classes টেবিল আছে</p>";
    }
    
    // Fees table
    $feeCheck = $conn->query("SHOW TABLES LIKE 'fees'");
    if ($feeCheck->num_rows == 0) {
        echo "<p>fees টেবিল তৈরি করছি...</p>";
        $createFees = "CREATE TABLE fees (
            id INT AUTO_INCREMENT PRIMARY KEY,
            student_id INT NOT NULL,
            fee_type VARCHAR(100) NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            paid DECIMAL(10,2) DEFAULT 0,
            due_date DATE,
            payment_status ENUM('paid', 'partial', 'due') DEFAULT 'due',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $conn->query($createFees);
        echo "<p style='color: green;'>✅ fees টেবিল তৈরি করা হয়েছে</p>";
    } else {
        echo "<p style='color: green;'>✅ fees টেবিল আছে</p>";
    }
    
    // Fee_payments table
    $paymentCheck = $conn->query("SHOW TABLES LIKE 'fee_payments'");
    if ($paymentCheck->num_rows == 0) {
        echo "<p>fee_payments টেবিল তৈরি করছি...</p>";
        $createPayments = "CREATE TABLE fee_payments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            fee_id INT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            payment_date DATE NOT NULL,
            payment_method VARCHAR(50) NOT NULL,
            receipt_no VARCHAR(50) DEFAULT NULL,
            notes TEXT DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $conn->query($createPayments);
        echo "<p style='color: green;'>✅ fee_payments টেবিল তৈরি করা হয়েছে</p>";
    } else {
        echo "<p style='color: green;'>✅ fee_payments টেবিল আছে</p>";
    }
    
    echo "<h3>ধাপ ৪: নমুনা ডেটা যোগ</h3>";
    
    // Add sample class
    $classCount = $conn->query("SELECT COUNT(*) as count FROM classes")->fetch_assoc()['count'];
    if ($classCount == 0) {
        $conn->query("INSERT INTO classes (class_name, description) VALUES ('একাদশ শ্রেণী', 'একাদশ শ্রেণীর শিক্ষার্থীরা')");
        echo "<p style='color: green;'>✅ নমুনা ক্লাস যোগ করা হয়েছে</p>";
    }
    
    // Add sample student
    $studentCount = $conn->query("SELECT COUNT(*) as count FROM students")->fetch_assoc()['count'];
    if ($studentCount == 0) {
        $classId = $conn->query("SELECT id FROM classes LIMIT 1")->fetch_assoc()['id'];
        $stmt = $conn->prepare("INSERT INTO students (first_name, last_name, roll_no, student_id, class_id, email, phone, gender) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $firstName = 'মোহাম্মদ';
        $lastName = 'রহিম উদ্দিন';
        $rollNo = '2024001';
        $studentId = 'S2024001';
        $email = '<EMAIL>';
        $phone = '01712345678';
        $gender = 'male';
        $stmt->bind_param("ssssisss", $firstName, $lastName, $rollNo, $studentId, $classId, $email, $phone, $gender);
        $stmt->execute();
        echo "<p style='color: green;'>✅ নমুনা স্টুডেন্ট যোগ করা হয়েছে</p>";
    }
    
    // Add sample fee
    $feeCount = $conn->query("SELECT COUNT(*) as count FROM fees")->fetch_assoc()['count'];
    if ($feeCount == 0) {
        $studentId = $conn->query("SELECT id FROM students LIMIT 1")->fetch_assoc()['id'];
        $stmt = $conn->prepare("INSERT INTO fees (student_id, fee_type, amount, due_date, payment_status) VALUES (?, ?, ?, ?, ?)");
        $feeType = 'মাসিক বেতন';
        $amount = 1500.00;
        $dueDate = date('Y-m-d', strtotime('+30 days'));
        $status = 'due';
        $stmt->bind_param("isdss", $studentId, $feeType, $amount, $dueDate, $status);
        $stmt->execute();
        echo "<p style='color: green;'>✅ নমুনা ফি যোগ করা হয়েছে</p>";
    }
    
    // Add sample payment
    $paymentCount = $conn->query("SELECT COUNT(*) as count FROM fee_payments")->fetch_assoc()['count'];
    if ($paymentCount == 0) {
        $feeId = $conn->query("SELECT id FROM fees LIMIT 1")->fetch_assoc()['id'];
        $receiptNo = 'RCP-' . date('Ymd') . '-0001';
        $stmt = $conn->prepare("INSERT INTO fee_payments (fee_id, receipt_no, amount, payment_date, payment_method, notes) VALUES (?, ?, ?, ?, ?, ?)");
        $amount = 1500.00;
        $paymentDate = date('Y-m-d');
        $method = 'cash';
        $notes = 'নমুনা পেমেন্ট - ডাটাবেস ফিক্স টেস্ট';
        $stmt->bind_param("isdsss", $feeId, $receiptNo, $amount, $paymentDate, $method, $notes);
        $stmt->execute();
        echo "<p style='color: green;'>✅ নমুনা পেমেন্ট যোগ করা হয়েছে</p>";
    }
    
    echo "<h3>ধাপ ৫: টেস্ট কুয়েরি</h3>";
    
    // Test the problematic query
    $testQuery = "SELECT s.first_name, s.last_name, s.roll_no, s.student_id, c.class_name 
                 FROM students s 
                 LEFT JOIN classes c ON s.class_id = c.id 
                 LIMIT 3";
    $testResult = $conn->query($testQuery);
    
    if ($testResult) {
        echo "<p style='color: green;'>✅ টেস্ট কুয়েরি সফল!</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>নাম</th><th>রোল নং</th><th>স্টুডেন্ট আইডি</th><th>ক্লাস</th></tr>";
        while ($row = $testResult->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['first_name'] . ' ' . $row['last_name'] . "</td>";
            echo "<td>" . ($row['roll_no'] ?? 'N/A') . "</td>";
            echo "<td>" . ($row['student_id'] ?? 'N/A') . "</td>";
            echo "<td>" . ($row['class_name'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ টেস্ট কুয়েরি ব্যর্থ: " . $conn->error . "</p>";
    }
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
    echo "<h4 style='color: #155724;'>✅ সমস্যা সমাধান সম্পন্ন!</h4>";
    echo "<p>এখন সিস্টেম সঠিকভাবে কাজ করবে।</p>";
    echo "</div>";
    
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='payment_list.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>পেমেন্ট তালিকা দেখুন</a>";
    echo "<a href='fee_management.php' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>ফি ম্যানেজমেন্ট</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: 'Hind Siliguri', Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}
h2, h3, h4 {
    color: #333;
}
table {
    margin: 10px 0;
}
th {
    background: #f8f9fa;
    padding: 8px;
}
td {
    padding: 8px;
}
p {
    margin: 10px 0;
}
</style>
