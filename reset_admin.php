<?php
// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "college_management";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Generate new password hash
$newPassword = "admin123";
$passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);

// Update admin password
$sql = "UPDATE users SET password = ? WHERE username = 'admin'";
$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $passwordHash);

if ($stmt->execute()) {
    echo "<h1>Admin Password Reset Successful</h1>";
    echo "<p>New admin password: <strong>admin123</strong></p>";
    echo "<p><a href='index.php'>Go to Login Page</a></p>";
} else {
    echo "<h1>Password Reset Failed</h1>";
    echo "<p>Error: " . $stmt->error . "</p>";
}

$stmt->close();
$conn->close();
?> 