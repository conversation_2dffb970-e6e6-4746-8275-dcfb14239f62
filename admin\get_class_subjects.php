<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

require_once '../includes/dbh.inc.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

$classId = isset($_POST['class_id']) ? intval($_POST['class_id']) : 0;
$departmentId = isset($_POST['department_id']) ? intval($_POST['department_id']) : 0;

if (!$classId) {
    echo json_encode(['success' => false, 'message' => 'Class ID is required']);
    exit();
}

try {
    // Create class_subjects table if it doesn't exist
    $createTableQuery = "CREATE TABLE IF NOT EXISTS class_subjects (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        class_id INT(11) NOT NULL,
        department_id INT(11) DEFAULT NULL,
        subject_id INT(11) NOT NULL,
        subject_type ENUM('required', 'optional', 'fourth') DEFAULT 'optional',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_class_subject (class_id, department_id, subject_id),
        FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
        FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
    )";
    $conn->query($createTableQuery);

    // Get all active subjects
    $subjectsQuery = "SELECT id, subject_name, subject_code, category FROM subjects WHERE is_active = 1 ORDER BY subject_name";
    $subjectsResult = $conn->query($subjectsQuery);
    
    $subjects = [];
    if ($subjectsResult && $subjectsResult->num_rows > 0) {
        while ($subject = $subjectsResult->fetch_assoc()) {
            $subjects[] = $subject;
        }
    }

    // Get already assigned subjects for this class
    $assignedQuery = "SELECT subject_id, subject_type FROM class_subjects WHERE class_id = ?";
    $params = [$classId];
    $types = "i";
    
    if ($departmentId > 0) {
        $assignedQuery .= " AND (department_id = ? OR department_id IS NULL)";
        $params[] = $departmentId;
        $types .= "i";
    } else {
        $assignedQuery .= " AND department_id IS NULL";
    }
    
    $stmt = $conn->prepare($assignedQuery);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $assignedResult = $stmt->get_result();
    
    $assignedSubjects = [];
    if ($assignedResult && $assignedResult->num_rows > 0) {
        while ($assigned = $assignedResult->fetch_assoc()) {
            $assignedSubjects[] = $assigned;
        }
    }

    echo json_encode([
        'success' => true,
        'subjects' => $subjects,
        'assigned_subjects' => $assignedSubjects
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
