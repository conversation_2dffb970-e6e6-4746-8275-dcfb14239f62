<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

$studentId = isset($_GET['student_id']) ? intval($_GET['student_id']) : 0;

echo "<!DOCTYPE html>
<html lang='bn'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Student ID Debug</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap' rel='stylesheet'>
    <style>
        * { font-family: 'Hind Siliguri', sans-serif; }
    </style>
</head>
<body class='bg-light'>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-md-10'>
            <div class='card'>
                <div class='card-header'>
                    <h4 class='mb-0'><i class='fas fa-bug me-2'></i>Student ID Debug</h4>
                </div>
                <div class='card-body'>";

echo "<h5>Debug Information:</h5>";
echo "<p><strong>Received student_id:</strong> " . $studentId . "</p>";

// Check students table
echo "<h6>Students Table Check:</h6>";
try {
    $studentsQuery = "SELECT id, student_id, name, class, section FROM students ORDER BY id DESC LIMIT 10";
    $studentsResult = $conn->query($studentsQuery);
    
    if ($studentsResult->num_rows > 0) {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-sm table-bordered'>";
        echo "<thead><tr><th>ID (Primary)</th><th>Student ID (Roll)</th><th>Name</th><th>Class</th><th>Section</th></tr></thead>";
        echo "<tbody>";
        while ($student = $studentsResult->fetch_assoc()) {
            $highlight = ($student['id'] == $studentId) ? 'table-success' : '';
            echo "<tr class='$highlight'>";
            echo "<td>" . $student['id'] . "</td>";
            echo "<td>" . htmlspecialchars($student['student_id']) . "</td>";
            echo "<td>" . htmlspecialchars($student['name']) . "</td>";
            echo "<td>" . htmlspecialchars($student['class']) . "</td>";
            echo "<td>" . htmlspecialchars($student['section']) . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-warning'>কোন students পাওয়া যায়নি</div>";
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Students table error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

// Check specific student
if ($studentId > 0) {
    echo "<h6>Specific Student Check (ID: $studentId):</h6>";
    try {
        $checkStudentQuery = "SELECT * FROM students WHERE id = ?";
        $checkStmt = $conn->prepare($checkStudentQuery);
        $checkStmt->bind_param('i', $studentId);
        $checkStmt->execute();
        $studentResult = $checkStmt->get_result();
        
        if ($studentResult->num_rows > 0) {
            $student = $studentResult->fetch_assoc();
            echo "<div class='alert alert-success'>";
            echo "<h6>✅ Student Found:</h6>";
            echo "<ul>";
            echo "<li><strong>ID:</strong> " . $student['id'] . "</li>";
            echo "<li><strong>Student ID (Roll):</strong> " . htmlspecialchars($student['student_id']) . "</li>";
            echo "<li><strong>Name:</strong> " . htmlspecialchars($student['name']) . "</li>";
            echo "<li><strong>Class:</strong> " . htmlspecialchars($student['class']) . "</li>";
            echo "<li><strong>Section:</strong> " . htmlspecialchars($student['section']) . "</li>";
            echo "</ul>";
            echo "</div>";
        } else {
            echo "<div class='alert alert-danger'>";
            echo "<h6>❌ Student Not Found!</h6>";
            echo "<p>ID $studentId এর কোন student database এ নেই।</p>";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>Student check error: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
}

// Check fees table
echo "<h6>Fees Table Check:</h6>";
try {
    $feesQuery = "SELECT f.id, f.student_id, f.fee_type, f.amount, f.paid, s.name as student_name, s.student_id as student_roll 
                  FROM fees f 
                  LEFT JOIN students s ON f.student_id = s.id 
                  ORDER BY f.id DESC LIMIT 10";
    $feesResult = $conn->query($feesQuery);
    
    if ($feesResult->num_rows > 0) {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-sm table-bordered'>";
        echo "<thead><tr><th>Fee ID</th><th>Student ID (FK)</th><th>Student Name</th><th>Student Roll</th><th>Fee Type</th><th>Amount</th><th>Paid</th></tr></thead>";
        echo "<tbody>";
        while ($fee = $feesResult->fetch_assoc()) {
            $highlight = ($fee['student_id'] == $studentId) ? 'table-info' : '';
            if (!$fee['student_name']) {
                $highlight = 'table-danger'; // Invalid student reference
            }
            echo "<tr class='$highlight'>";
            echo "<td>" . $fee['id'] . "</td>";
            echo "<td>" . $fee['student_id'] . "</td>";
            echo "<td>" . htmlspecialchars($fee['student_name'] ?: 'INVALID') . "</td>";
            echo "<td>" . htmlspecialchars($fee['student_roll'] ?: 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($fee['fee_type']) . "</td>";
            echo "<td>৳" . number_format($fee['amount'], 2) . "</td>";
            echo "<td>৳" . number_format($fee['paid'], 2) . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-warning'>কোন fees পাওয়া যায়নি</div>";
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Fees table error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

// Check fee_payments table
echo "<h6>Fee Payments Table Check:</h6>";
try {
    $paymentsQuery = "SELECT fp.*, f.student_id, s.name as student_name 
                      FROM fee_payments fp 
                      LEFT JOIN fees f ON fp.fee_id = f.id 
                      LEFT JOIN students s ON f.student_id = s.id 
                      ORDER BY fp.id DESC LIMIT 10";
    $paymentsResult = $conn->query($paymentsQuery);
    
    if ($paymentsResult->num_rows > 0) {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-sm table-bordered'>";
        echo "<thead><tr><th>Payment ID</th><th>Fee ID</th><th>Student ID</th><th>Student Name</th><th>Amount</th><th>Date</th></tr></thead>";
        echo "<tbody>";
        while ($payment = $paymentsResult->fetch_assoc()) {
            $highlight = ($payment['student_id'] == $studentId) ? 'table-info' : '';
            echo "<tr class='$highlight'>";
            echo "<td>" . $payment['id'] . "</td>";
            echo "<td>" . $payment['fee_id'] . "</td>";
            echo "<td>" . $payment['student_id'] . "</td>";
            echo "<td>" . htmlspecialchars($payment['student_name'] ?: 'INVALID') . "</td>";
            echo "<td>৳" . number_format($payment['amount'], 2) . "</td>";
            echo "<td>" . $payment['payment_date'] . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-warning'>কোন payments পাওয়া যায়নি</div>";
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Payments table error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

// Test links
echo "<h6>Test Links:</h6>";
echo "<div class='alert alert-info'>";
echo "<p>Test these links:</p>";
echo "<ul>";

// Get some valid student IDs
try {
    $validStudentsQuery = "SELECT id, name FROM students LIMIT 5";
    $validStudentsResult = $conn->query($validStudentsQuery);
    while ($validStudent = $validStudentsResult->fetch_assoc()) {
        echo "<li>";
        echo "<a href='student_payment_receipt.php?student_id=" . $validStudent['id'] . "' target='_blank'>";
        echo "Student ID " . $validStudent['id'] . " (" . htmlspecialchars($validStudent['name']) . ")";
        echo "</a>";
        echo "</li>";
    }
} catch (Exception $e) {
    echo "<li>Error getting valid students: " . htmlspecialchars($e->getMessage()) . "</li>";
}

echo "</ul>";
echo "</div>";

echo "<div class='mt-4 text-center'>";
echo "<a href='fee_management.php' class='btn btn-primary me-2'>";
echo "<i class='fas fa-arrow-left me-1'></i> ফি ম্যানেজমেন্ট";
echo "</a>";
echo "<a href='student_payment_receipt.php?student_id=1' class='btn btn-success me-2'>";
echo "<i class='fas fa-receipt me-1'></i> Test Receipt (ID=1)";
echo "</a>";
echo "<a href='payment_system_checker.php' class='btn btn-info'>";
echo "<i class='fas fa-stethoscope me-1'></i> System Checker";
echo "</a>";
echo "</div>";

echo "                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>";
?>
