<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if necessary tables exist and create them if they don't
$requiredTables = ['exam_types', 'exams', 'subjects', 'departments', 'classes', 'subject_exam_pattern', 'subject_marks_distribution', 'results'];
$missingTables = [];
$createdTables = [];

foreach ($requiredTables as $table) {
    $tableCheck = $conn->query("SHOW TABLES LIKE '$table'");
    if ($tableCheck->num_rows == 0) {
        $missingTables[] = $table;
    }
}

// Create exam_types table if it doesn't exist
$examTypesTableCheck = $conn->query("SHOW TABLES LIKE 'exam_types'");
if ($examTypesTableCheck->num_rows == 0) {
    $createExamTypesTable = "CREATE TABLE IF NOT EXISTS exam_types (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        type_name VARCHAR(100) NOT NULL,
        description TEXT,
        is_active TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";

    if ($conn->query($createExamTypesTable)) {
        $createdTables[] = 'exam_types';

        // Add default exam types
        $defaultTypes = [
            ['অর্ধ-বার্ষিক পরীক্ষা', 'শিক্ষাবর্ষের মধ্যে অনুষ্ঠিত পরীক্ষা'],
            ['বার্ষিক পরীক্ষা', 'শিক্ষাবর্ষের শেষে অনুষ্ঠিত পরীক্ষা'],
            ['নির্বাচনী পরীক্ষা', 'পাবলিক পরীক্ষার প্রস্তুতি হিসেবে অনুষ্ঠিত পরীক্ষা'],
            ['প্রাক-নির্বাচনী পরীক্ষা', 'নির্বাচনী পরীক্ষার আগে অনুষ্ঠিত পরীক্ষা'],
            ['সাপ্তাহিক পরীক্ষা', 'প্রতি সপ্তাহে অনুষ্ঠিত পরীক্ষা'],
            ['মাসিক পরীক্ষা', 'প্রতি মাসে অনুষ্ঠিত পরীক্ষা']
        ];

        $insertStmt = $conn->prepare("INSERT INTO exam_types (type_name, description) VALUES (?, ?)");

        foreach ($defaultTypes as $type) {
            $insertStmt->bind_param("ss", $type[0], $type[1]);
            $insertStmt->execute();
        }
    }
}

// Get statistics
$stats = [
    'total_exams' => 0,
    'upcoming_exams' => 0,
    'completed_exams' => 0,
    'total_subjects' => 0,
    'total_classes' => 0,
    'total_students' => 0
];

// Check if exams table exists before querying
$examsTableCheck = $conn->query("SHOW TABLES LIKE 'exams'");
if ($examsTableCheck->num_rows > 0) {
    // Total exams
    $totalExamsQuery = "SELECT COUNT(*) as count FROM exams";
    $totalExamsResult = $conn->query($totalExamsQuery);
    if ($totalExamsResult && $totalExamsResult->num_rows > 0) {
        $stats['total_exams'] = $totalExamsResult->fetch_assoc()['count'];
    }

    // Upcoming exams
    $upcomingExamsQuery = "SELECT COUNT(*) as count FROM exams WHERE exam_date >= CURDATE()";
    $upcomingExamsResult = $conn->query($upcomingExamsQuery);
    if ($upcomingExamsResult && $upcomingExamsResult->num_rows > 0) {
        $stats['upcoming_exams'] = $upcomingExamsResult->fetch_assoc()['count'];
    }

    // Completed exams
    $completedExamsQuery = "SELECT COUNT(*) as count FROM exams WHERE exam_date < CURDATE()";
    $completedExamsResult = $conn->query($completedExamsQuery);
    if ($completedExamsResult && $completedExamsResult->num_rows > 0) {
        $stats['completed_exams'] = $completedExamsResult->fetch_assoc()['count'];
    }
}

// Check if subjects table exists before querying
$subjectsTableCheck = $conn->query("SHOW TABLES LIKE 'subjects'");
if ($subjectsTableCheck->num_rows > 0) {
    $totalSubjectsQuery = "SELECT COUNT(*) as count FROM subjects";
    $totalSubjectsResult = $conn->query($totalSubjectsQuery);
    if ($totalSubjectsResult && $totalSubjectsResult->num_rows > 0) {
        $stats['total_subjects'] = $totalSubjectsResult->fetch_assoc()['count'];
    }
}

// Check if classes table exists before querying
$classesTableCheck = $conn->query("SHOW TABLES LIKE 'classes'");
if ($classesTableCheck->num_rows > 0) {
    $totalClassesQuery = "SELECT COUNT(*) as count FROM classes";
    $totalClassesResult = $conn->query($totalClassesQuery);
    if ($totalClassesResult && $totalClassesResult->num_rows > 0) {
        $stats['total_classes'] = $totalClassesResult->fetch_assoc()['count'];
    }
}

// Check if students table exists before querying
$studentsTableCheck = $conn->query("SHOW TABLES LIKE 'students'");
if ($studentsTableCheck->num_rows > 0) {
    $totalStudentsQuery = "SELECT COUNT(*) as count FROM students";
    $totalStudentsResult = $conn->query($totalStudentsQuery);
    if ($totalStudentsResult && $totalStudentsResult->num_rows > 0) {
        $stats['total_students'] = $totalStudentsResult->fetch_assoc()['count'];
    }
}

// Get upcoming exams
$upcomingExams = null;
if ($examsTableCheck->num_rows > 0) {
    $upcomingExamsQuery = "SELECT e.id, e.exam_name, e.exam_type, e.exam_date, e.total_marks,
                          s.subject_name, c.class_name, d.department_name
                          FROM exams e
                          LEFT JOIN subjects s ON e.subject_id = s.id
                          LEFT JOIN classes c ON e.class_id = c.id
                          LEFT JOIN departments d ON e.department_id = d.id
                          WHERE e.exam_date >= CURDATE()
                          ORDER BY e.exam_date ASC
                          LIMIT 5";
    $upcomingExams = $conn->query($upcomingExamsQuery);
}

// Get recent results
$recentResults = null;
$resultsTableCheck = $conn->query("SHOW TABLES LIKE 'results'");
if ($resultsTableCheck->num_rows > 0) {
    $recentResultsQuery = "SELECT r.id, r.marks_obtained, r.grade, e.exam_name,
                          s.first_name, s.last_name, s.student_id as student_code
                          FROM results r
                          JOIN students s ON r.student_id = s.id
                          JOIN exams e ON r.exam_id = e.id
                          ORDER BY r.id DESC
                          LIMIT 5";
    $recentResults = $conn->query($recentResultsQuery);
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>পরীক্ষা ব্যবস্থাপনা ড্যাশবোর্ড - স্কুল ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css">

    <style>
        .step-card {
            border-radius: 10px;
            transition: transform 0.3s, box-shadow 0.3s;
            height: 100%;
        }
        .step-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            border-radius: 50%;
            background-color: #f8f9fa;
            margin-right: 10px;
            font-weight: bold;
        }
        .stat-card {
            border-radius: 10px;
            transition: transform 0.3s;
        }
        .stat-card:hover {
            transform: scale(1.05);
        }
        .workflow-container {
            position: relative;
            padding: 20px 0;
        }
        .workflow-line {
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 3px;
            background-color: #dee2e6;
            z-index: 0;
        }
        .workflow-step {
            position: relative;
            z-index: 1;
            background-color: #fff;
            padding: 10px;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 3px solid #dee2e6;
            transition: all 0.3s;
        }
        .workflow-step:hover {
            background-color: #f8f9fa;
            transform: scale(1.1);
        }
        .workflow-step i {
            font-size: 24px;
        }
        .workflow-label {
            text-align: center;
            margin-top: 10px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>পরীক্ষা ব্যবস্থাপনা ড্যাশবোর্ড</h2>
                        <p class="text-muted">প্রতিষ্ঠানে শিক্ষার্থীদের পরীক্ষা ব্যবস্থাপনার জন্য ধাপে ধাপে করণীয়</p>
                    </div>
                    <div class="col-auto">
                        <a href="dashboard.php" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>মূল ড্যাশবোর্ড
                        </a>
                    </div>
                </div>

                <?php if (!empty($createdTables)): ?>
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i>নিম্নলিখিত টেবিলগুলি সফলভাবে তৈরি করা হয়েছে: <?php echo implode(', ', $createdTables); ?>
                </div>
                <?php endif; ?>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title mb-0">মোট পরীক্ষা</h6>
                                        <h2 class="mt-2 mb-0"><?php echo $stats['total_exams']; ?></h2>
                                    </div>
                                    <div>
                                        <i class="fas fa-file-alt fa-3x opacity-50"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title mb-0">আসন্ন পরীক্ষা</h6>
                                        <h2 class="mt-2 mb-0"><?php echo $stats['upcoming_exams']; ?></h2>
                                    </div>
                                    <div>
                                        <i class="fas fa-calendar-alt fa-3x opacity-50"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title mb-0">মোট বিষয়</h6>
                                        <h2 class="mt-2 mb-0"><?php echo $stats['total_subjects']; ?></h2>
                                    </div>
                                    <div>
                                        <i class="fas fa-book fa-3x opacity-50"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card bg-warning text-dark">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title mb-0">মোট শিক্ষার্থী</h6>
                                        <h2 class="mt-2 mb-0"><?php echo $stats['total_students']; ?></h2>
                                    </div>
                                    <div>
                                        <i class="fas fa-user-graduate fa-3x opacity-50"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Workflow Visualization -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">পরীক্ষা ব্যবস্থাপনা কার্যপ্রবাহ</h5>
                    </div>
                    <div class="card-body">
                        <div class="workflow-container">
                            <div class="workflow-line"></div>
                            <div class="row">
                                <div class="col">
                                    <a href="exam_types.php" class="text-decoration-none">
                                        <div class="workflow-step bg-primary text-white">
                                            <i class="fas fa-tags"></i>
                                        </div>
                                        <div class="workflow-label">পরীক্ষার ধরন</div>
                                    </a>
                                </div>
                                <div class="col">
                                    <a href="exam_subject_assignment.php" class="text-decoration-none">
                                        <div class="workflow-step bg-success text-white">
                                            <i class="fas fa-book"></i>
                                        </div>
                                        <div class="workflow-label">পরীক্ষার বিষয়</div>
                                    </a>
                                </div>
                                <div class="col">
                                    <a href="subject_exam_pattern.php" class="text-decoration-none">
                                        <div class="workflow-step bg-success text-white">
                                            <i class="fas fa-sliders-h"></i>
                                        </div>
                                        <div class="workflow-label">পরীক্ষা প্যাটার্ন</div>
                                    </a>
                                </div>
                                <div class="col">
                                    <a href="subject_marks_distribution.php" class="text-decoration-none">
                                        <div class="workflow-step bg-info text-white">
                                            <i class="fas fa-chart-pie"></i>
                                        </div>
                                        <div class="workflow-label">মার্কস ডিস্ট্রিবিউশন</div>
                                    </a>
                                </div>
                                <div class="col">
                                    <a href="create_exam.php" class="text-decoration-none">
                                        <div class="workflow-step bg-warning text-dark">
                                            <i class="fas fa-plus-circle"></i>
                                        </div>
                                        <div class="workflow-label">পরীক্ষা তৈরি</div>
                                    </a>
                                </div>
                                <div class="col">
                                    <a href="http://localhost/zfaw/admin/exams.php" class="text-decoration-none">
                                        <div class="workflow-step bg-warning text-dark">
                                            <i class="fas fa-list"></i>
                                        </div>
                                        <div class="workflow-label">পরীক্ষা তালিকা</div>
                                    </a>
                                </div>
                                <div class="col">
                                    <a href="assign_exam_subjects.php" class="text-decoration-none">
                                        <div class="workflow-step bg-warning text-dark">
                                            <i class="fas fa-book"></i>
                                        </div>
                                        <div class="workflow-label">বিষয় অ্যাসাইন</div>
                                    </a>
                                </div>
                                <div class="col">
                                    <a href="exam_routine.php" class="text-decoration-none">
                                        <div class="workflow-step bg-warning text-dark">
                                            <i class="fas fa-calendar-alt"></i>
                                        </div>
                                        <div class="workflow-label">পরীক্ষার রুটিন</div>
                                    </a>
                                </div>
                                <div class="col">
                                    <a href="student_exam_attendance.php" class="text-decoration-none">
                                        <div class="workflow-step bg-secondary text-white">
                                            <i class="fas fa-clipboard-check"></i>
                                        </div>
                                        <div class="workflow-label">শিক্ষার্থী হাজিরা পত্র</div>
                                    </a>
                                </div>
                                <div class="col">
                                    <a href="marks_entry.php" class="text-decoration-none">
                                        <div class="workflow-step bg-info text-white">
                                            <i class="fas fa-edit"></i>
                                        </div>
                                        <div class="workflow-label">মার্ক এনট্রি</div>
                                    </a>
                                </div>
                                <div class="col">
                                    <a href="results.php" class="text-decoration-none">
                                        <div class="workflow-step bg-danger text-white">
                                            <i class="fas fa-chart-bar"></i>
                                        </div>
                                        <div class="workflow-label">ফলাফল</div>
                                    </a>
                                </div>
                                <div class="col">
                                    <a href="tabulation_sheet.php" class="text-decoration-none">
                                        <div class="workflow-step bg-dark text-white">
                                            <i class="fas fa-table"></i>
                                        </div>
                                        <div class="workflow-label">টেবুলেশন শীট</div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step-by-Step Guide -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">পরীক্ষা ব্যবস্থাপনা - ধাপে ধাপে করণীয়</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-4">
                                <div class="card step-card h-100 border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0"><span class="step-number bg-white text-primary">১</span> প্রাথমিক সেটআপ</h5>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <a href="exam_types.php" class="text-decoration-none">পরীক্ষার ধরন সেটআপ করুন</a>
                                            </li>
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <a href="subjects.php" class="text-decoration-none">বিষয়সমূহ যোগ করুন</a>
                                            </li>
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <a href="exam_subject_assignment.php" class="text-decoration-none">পরীক্ষার ধরন অনুযায়ী বিষয় নির্ধারণ করুন</a>
                                            </li>
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <a href="classes.php" class="text-decoration-none">শ্রেণীসমূহ যোগ করুন</a>
                                            </li>
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <a href="departments.php" class="text-decoration-none">বিভাগসমূহ যোগ করুন</a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="card-footer bg-light">
                                        <a href="exam_types.php" class="btn btn-primary btn-sm">
                                            <i class="fas fa-arrow-right me-1"></i> শুরু করুন
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4 mb-4">
                                <div class="card step-card h-100 border-success">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0"><span class="step-number bg-white text-success">২</span> পরীক্ষা প্যাটার্ন সেটআপ</h5>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <a href="exam_subject_assignment.php" class="text-decoration-none">পরীক্ষার ধরন অনুযায়ী বিষয় নির্ধারণ করুন</a>
                                            </li>
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <a href="subject_exam_pattern.php" class="text-decoration-none">বিষয় পরীক্ষা প্যাটার্ন সেট করুন</a>
                                            </li>
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <a href="subject_marks_distribution.php" class="text-decoration-none">মার্কস ডিস্ট্রিবিউশন সেট করুন</a>
                                            </li>
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <a href="subject_minimum_pass.php" class="text-decoration-none">ন্যূনতম পাস মার্কস সেট করুন</a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="card-footer bg-light">
                                        <a href="subject_exam_pattern.php" class="btn btn-success btn-sm">
                                            <i class="fas fa-arrow-right me-1"></i> শুরু করুন
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4 mb-4">
                                <div class="card step-card h-100 border-info">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0"><span class="step-number bg-white text-info">৩</span> পরীক্ষা তৈরি ও পরিচালনা</h5>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <a href="create_exam.php" class="text-decoration-none">নতুন পরীক্ষা তৈরি করুন</a>
                                            </li>
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <a href="assign_exam_subjects.php" class="text-decoration-none">পরীক্ষার বিষয় অ্যাসাইন করুন</a>
                                            </li>
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <a href="exam_routine.php" class="text-decoration-none">পরীক্ষার রুটিন তৈরি করুন</a>
                                            </li>
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <a href="http://localhost/zfaw/admin/exams.php" class="text-decoration-none">পরীক্ষা তালিকা দেখুন</a>
                                            </li>
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <a href="student_exam_attendance.php" class="text-decoration-none">শিক্ষার্থী হাজিরা পত্র তৈরি করুন</a>
                                            </li>
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <a href="marks_entry.php" class="text-decoration-none">পরীক্ষার নম্বর এন্ট্রি করুন</a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="card-footer bg-light">
                                        <a href="create_exam.php" class="btn btn-info btn-sm text-white">
                                            <i class="fas fa-arrow-right me-1"></i> শুরু করুন
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 mb-4">
                                <div class="card step-card h-100 border-warning">
                                    <div class="card-header bg-warning text-dark">
                                        <h5 class="mb-0"><span class="step-number bg-white text-warning">৪</span> মার্কস এন্ট্রি ও ফলাফল</h5>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <a href="results.php" class="text-decoration-none">পরীক্ষার ফলাফল ব্যবস্থাপনা</a>
                                            </li>
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <a href="results.php" class="text-decoration-none">ফলাফল দেখুন ও প্রিন্ট করুন</a>
                                            </li>
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <a href="result_analysis.php" class="text-decoration-none">ফলাফল বিশ্লেষণ করুন</a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="card-footer bg-light">
                                        <a href="results.php" class="btn btn-warning btn-sm">
                                            <i class="fas fa-arrow-right me-1"></i> শুরু করুন
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 mb-4">
                                <div class="card step-card h-100 border-danger">
                                    <div class="card-header bg-danger text-white">
                                        <h5 class="mb-0"><span class="step-number bg-white text-danger">৫</span> রিপোর্ট ও সার্টিফিকেট</h5>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <a href="generate_marksheet.php" class="text-decoration-none">মার্কশিট তৈরি করুন</a>
                                            </li>
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <a href="generate_report_card.php" class="text-decoration-none">রিপোর্ট কার্ড তৈরি করুন</a>
                                            </li>
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <a href="generate_certificates.php" class="text-decoration-none">সার্টিফিকেট তৈরি করুন</a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="card-footer bg-light">
                                        <a href="generate_marksheet.php" class="btn btn-danger btn-sm">
                                            <i class="fas fa-arrow-right me-1"></i> শুরু করুন
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Upcoming Exams and Recent Results -->
                <div class="row">
                    <!-- Upcoming Exams -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center bg-success text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-calendar-alt me-2"></i>আসন্ন পরীক্ষা
                                </h5>
                                <a href="create_exam.php" class="btn btn-sm btn-light">
                                    <i class="fas fa-plus-circle me-1"></i> নতুন পরীক্ষা
                                </a>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>পরীক্ষা</th>
                                                <th>বিষয়</th>
                                                <th>শ্রেণী/বিভাগ</th>
                                                <th>তারিখ</th>
                                                <th>মার্ক</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($upcomingExams && $upcomingExams->num_rows > 0): ?>
                                                <?php while ($exam = $upcomingExams->fetch_assoc()): ?>
                                                    <tr>
                                                        <td>
                                                            <div class="fw-medium"><?php echo htmlspecialchars($exam['exam_name']); ?></div>
                                                            <small class="text-muted"><?php echo htmlspecialchars($exam['exam_type'] ?? ''); ?></small>
                                                        </td>
                                                        <td>
                                                            <?php
                                                            // Check if we need to show "All Subjects"
                                                            if (!isset($exam['subject_id']) || $exam['subject_id'] === null) {
                                                                echo "সকল বিষয়";
                                                            } else {
                                                                echo htmlspecialchars($exam['subject_name'] ?? 'N/A');
                                                            }
                                                            ?>
                                                        </td>
                                                        <td>
                                                            <?php
                                                                if (!isset($exam['department_id']) || $exam['department_id'] === null) {
                                                                    echo "সকল বিভাগ";
                                                                } else {
                                                                    $classOrDept = '';
                                                                    if (!empty($exam['class_name'])) {
                                                                        $classOrDept .= $exam['class_name'];
                                                                    }
                                                                    if (!empty($exam['department_name'])) {
                                                                        if (!empty($classOrDept)) {
                                                                            $classOrDept .= ' / ';
                                                                        }
                                                                        $classOrDept .= $exam['department_name'];
                                                                    }
                                                                    echo htmlspecialchars($classOrDept ?: 'N/A');
                                                                }
                                                            ?>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-light text-dark">
                                                                <i class="far fa-calendar-alt me-1"></i>
                                                                <?php echo date('d/m/Y', strtotime($exam['exam_date'])); ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-success text-white">
                                                                <?php echo htmlspecialchars($exam['total_marks']); ?>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="5" class="text-center py-4">
                                                        <div class="text-muted">
                                                            <i class="fas fa-info-circle me-1"></i> কোন আসন্ন পরীক্ষা পাওয়া যায়নি
                                                        </div>
                                                        <div class="mt-2">
                                                            <a href="create_exam.php" class="btn btn-sm btn-success">
                                                                <i class="fas fa-plus-circle me-1"></i> নতুন পরীক্ষা যোগ করুন
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-end mt-3">
                                    <a href="http://localhost/zfaw/admin/exams.php" class="btn btn-outline-success">
                                        <i class="fas fa-list me-1"></i> সকল পরীক্ষা দেখান
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Results -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center bg-danger text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>সাম্প্রতিক ফলাফল
                                </h5>
                                <a href="results.php" class="btn btn-sm btn-light">
                                    <i class="fas fa-chart-bar me-1"></i> ফলাফল দেখুন
                                </a>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>শিক্ষার্থী</th>
                                                <th>পরীক্ষা</th>
                                                <th>প্রাপ্ত মার্কস</th>
                                                <th>গ্রেড</th>
                                                <th>একশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($recentResults && $recentResults->num_rows > 0): ?>
                                                <?php while ($result = $recentResults->fetch_assoc()): ?>
                                                    <tr>
                                                        <td>
                                                            <div class="fw-medium"><?php echo htmlspecialchars($result['first_name'] . ' ' . $result['last_name']); ?></div>
                                                            <small class="text-muted"><?php echo htmlspecialchars($result['student_code']); ?></small>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($result['exam_name']); ?></td>
                                                        <td>
                                                            <span class="badge bg-primary">
                                                                <?php echo htmlspecialchars($result['marks_obtained']); ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <span class="badge <?php echo getGradeColor($result['grade']); ?>">
                                                                <?php echo htmlspecialchars($result['grade']); ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <a href="view_student.php?id=<?php echo $result['id']; ?>" class="btn btn-sm btn-info">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="5" class="text-center py-4">
                                                        <div class="text-muted">
                                                            <i class="fas fa-info-circle me-1"></i> কোন সাম্প্রতিক ফলাফল পাওয়া যায়নি
                                                        </div>
                                                        <div class="mt-2">
                                                            <a href="results.php" class="btn btn-sm btn-danger">
                                                                <i class="fas fa-chart-bar me-1"></i> ফলাফল দেখুন
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-end mt-3">
                                    <a href="results.php" class="btn btn-outline-danger">
                                        <i class="fas fa-chart-bar me-1"></i> সকল ফলাফল দেখান
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <?php
    // Helper function to get grade color
    function getGradeColor($grade) {
        switch ($grade) {
            case 'A+':
            case 'A':
                return 'bg-success text-white';
            case 'A-':
            case 'B+':
            case 'B':
                return 'bg-info text-white';
            case 'B-':
            case 'C+':
            case 'C':
                return 'bg-primary text-white';
            case 'D':
                return 'bg-warning text-dark';
            case 'F':
                return 'bg-danger text-white';
            default:
                return 'bg-secondary text-white';
        }
    }
    ?>
</body>
</html>
