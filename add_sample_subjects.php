<?php
// Include database connection
require_once "includes/dbh.inc.php";

// Check if departments exist
$departmentsResult = $conn->query("SELECT * FROM departments");
if ($departmentsResult->num_rows == 0) {
    echo "<p style='color:red'>No departments found. Please add departments first.</p>";
    exit;
}

// Sample subjects for Science, Arts, and Commerce departments
$sampleSubjects = [
    // Science subjects
    ['Physics', 'PHY-101', 'required', 'Basic physics course covering mechanics, thermodynamics, and waves'],
    ['Chemistry', 'CHEM-101', 'required', 'Introduction to general chemistry principles'],
    ['Biology', 'BIO-101', 'optional', 'Study of living organisms and their interactions'],
    ['Higher Mathematics', 'MATH-201', 'required', 'Advanced mathematics including calculus and algebra'],
    ['Statistics', 'STAT-101', 'optional', 'Introduction to statistical methods and data analysis'],
    ['Computer Science', 'CS-101', 'optional', 'Introduction to computer programming and algorithms'],
    ['Environmental Science', 'ENV-101', 'fourth', 'Study of environmental systems and sustainability'],
    
    // Arts subjects
    ['Bengali Literature', 'BEN-101', 'required', 'Study of Bengali language and literature'],
    ['English Literature', 'ENG-101', 'required', 'Study of English language and literature'],
    ['History', 'HIST-101', 'optional', 'Study of past events and human civilization'],
    ['Geography', 'GEO-101', 'optional', 'Study of Earth and its features, inhabitants, and phenomena'],
    ['Political Science', 'POL-101', 'optional', 'Study of political systems, governance, and policies'],
    ['Sociology', 'SOC-101', 'fourth', 'Study of human society and social relationships'],
    ['Psychology', 'PSY-101', 'fourth', 'Study of mind and behavior'],
    
    // Commerce subjects
    ['Accounting', 'ACC-101', 'required', 'Principles of accounting and financial reporting'],
    ['Business Studies', 'BUS-101', 'required', 'Introduction to business concepts and practices'],
    ['Economics', 'ECON-101', 'required', 'Study of production, distribution, and consumption of goods'],
    ['Finance', 'FIN-101', 'optional', 'Study of money management and financial systems'],
    ['Marketing', 'MKT-101', 'optional', 'Principles of marketing and consumer behavior'],
    ['Management', 'MGT-101', 'optional', 'Study of organizational management and leadership'],
    ['Banking', 'BANK-101', 'fourth', 'Introduction to banking systems and operations']
];

// Begin transaction
$conn->begin_transaction();

try {
    // Get department IDs
    $departments = [];
    while ($dept = $departmentsResult->fetch_assoc()) {
        $departments[$dept['department_name']] = $dept['id'];
    }
    
    // Check if we have Science, Arts, and Commerce departments
    $scienceDeptId = $departments['Science'] ?? null;
    $artsDeptId = $departments['Arts'] ?? null;
    $commerceDeptId = $departments['Commerce'] ?? null;
    
    // If departments don't exist, create them
    if (!$scienceDeptId) {
        $conn->query("INSERT INTO departments (department_name, description) VALUES ('Science', 'Science Department')");
        $scienceDeptId = $conn->insert_id;
        $departments['Science'] = $scienceDeptId;
    }
    
    if (!$artsDeptId) {
        $conn->query("INSERT INTO departments (department_name, description) VALUES ('Arts', 'Arts Department')");
        $artsDeptId = $conn->insert_id;
        $departments['Arts'] = $artsDeptId;
    }
    
    if (!$commerceDeptId) {
        $conn->query("INSERT INTO departments (department_name, description) VALUES ('Commerce', 'Commerce Department')");
        $commerceDeptId = $conn->insert_id;
        $departments['Commerce'] = $commerceDeptId;
    }
    
    // Add subjects
    $subjectCount = 0;
    foreach ($sampleSubjects as $subject) {
        list($name, $code, $category, $description) = $subject;
        
        // Check if subject already exists
        $checkSubject = $conn->prepare("SELECT id FROM subjects WHERE subject_code = ?");
        $checkSubject->bind_param("s", $code);
        $checkSubject->execute();
        $result = $checkSubject->get_result();
        
        if ($result->num_rows == 0) {
            // Insert subject
            $insertSubject = $conn->prepare("INSERT INTO subjects (subject_name, subject_code, category, description, is_active) 
                                           VALUES (?, ?, ?, ?, 1)");
            $insertSubject->bind_param("ssss", $name, $code, $category, $description);
            $insertSubject->execute();
            $subjectId = $conn->insert_id;
            $subjectCount++;
            
            // Assign subject to appropriate department
            $deptId = null;
            if (strpos($name, 'Physics') !== false || strpos($name, 'Chemistry') !== false || 
                strpos($name, 'Biology') !== false || strpos($name, 'Mathematics') !== false || 
                strpos($name, 'Statistics') !== false || strpos($name, 'Computer') !== false || 
                strpos($name, 'Environmental') !== false) {
                $deptId = $scienceDeptId;
            } elseif (strpos($name, 'Bengali') !== false || strpos($name, 'English') !== false || 
                     strpos($name, 'History') !== false || strpos($name, 'Geography') !== false || 
                     strpos($name, 'Political') !== false || strpos($name, 'Sociology') !== false || 
                     strpos($name, 'Psychology') !== false) {
                $deptId = $artsDeptId;
            } elseif (strpos($name, 'Accounting') !== false || strpos($name, 'Business') !== false || 
                     strpos($name, 'Economics') !== false || strpos($name, 'Finance') !== false || 
                     strpos($name, 'Marketing') !== false || strpos($name, 'Management') !== false || 
                     strpos($name, 'Banking') !== false) {
                $deptId = $commerceDeptId;
            }
            
            if ($deptId) {
                // Check if mapping already exists
                $checkMapping = $conn->prepare("SELECT id FROM subject_departments WHERE subject_id = ? AND department_id = ?");
                $checkMapping->bind_param("ii", $subjectId, $deptId);
                $checkMapping->execute();
                $result = $checkMapping->get_result();
                
                if ($result->num_rows == 0) {
                    // Insert mapping
                    $insertMapping = $conn->prepare("INSERT INTO subject_departments (subject_id, department_id) VALUES (?, ?)");
                    $insertMapping->bind_param("ii", $subjectId, $deptId);
                    $insertMapping->execute();
                }
                
                // Add to department_subject_types if table exists
                $tableExists = $conn->query("SHOW TABLES LIKE 'department_subject_types'")->num_rows > 0;
                if ($tableExists) {
                    // Check if type mapping already exists
                    $checkTypeMapping = $conn->prepare("SELECT id FROM department_subject_types WHERE subject_id = ? AND department_id = ?");
                    $checkTypeMapping->bind_param("ii", $subjectId, $deptId);
                    $checkTypeMapping->execute();
                    $result = $checkTypeMapping->get_result();
                    
                    if ($result->num_rows == 0) {
                        // Insert type mapping
                        $insertTypeMapping = $conn->prepare("INSERT INTO department_subject_types (department_id, subject_id, subject_type) VALUES (?, ?, ?)");
                        $insertTypeMapping->bind_param("iis", $deptId, $subjectId, $category);
                        $insertTypeMapping->execute();
                    }
                }
            }
        }
    }
    
    // Commit transaction
    $conn->commit();
    
    echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
    echo "<h3>Success!</h3>";
    echo "<p>Added $subjectCount new subjects to the database and assigned them to departments.</p>";
    echo "<p>You can now go to <a href='admin/student_subject_selection.php'>Student Subject Selection</a> page.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    // Rollback on error
    $conn->rollback();
    
    echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
    echo "<h3>Error!</h3>";
    echo "<p>Failed to add sample subjects: " . $e->getMessage() . "</p>";
    echo "</div>";
}

// Close connection
$conn->close();
?>
