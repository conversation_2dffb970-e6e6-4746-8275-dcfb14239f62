<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include header
include_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Include sidebar -->
        <?php include_once 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-database me-2"></i> ফি টেবিল আপডেট</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="fee_management.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i> ফি ম্যানেজমেন্ট
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-cogs me-2"></i> ডাটাবেস আপডেট</h5>
                </div>
                <div class="card-body">
                    <?php
                    // Check if fees table exists
                    $checkTableQuery = "SHOW TABLES LIKE 'fees'";
                    $tableResult = $conn->query($checkTableQuery);
                    
                    if ($tableResult->num_rows === 0) {
                        echo '<div class="alert alert-warning">ফি টেবিল পাওয়া যায়নি!</div>';
                    } else {
                        // Check if category_id column exists
                        $checkColumnQuery = "SHOW COLUMNS FROM fees LIKE 'category_id'";
                        $columnResult = $conn->query($checkColumnQuery);
                        
                        if ($columnResult->num_rows === 0) {
                            // Create fee_categories table if it doesn't exist
                            $createCategoryTableQuery = "CREATE TABLE IF NOT EXISTS fee_categories (
                                id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
                                name VARCHAR(100) NOT NULL,
                                description TEXT,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                            )";
                            
                            if ($conn->query($createCategoryTableQuery)) {
                                echo '<div class="alert alert-success">ফি ক্যাটাগরি টেবিল সফলভাবে তৈরি করা হয়েছে!</div>';
                                
                                // Check if default category exists
                                $checkDefaultQuery = "SELECT id FROM fee_categories WHERE id = 1";
                                $defaultResult = $conn->query($checkDefaultQuery);
                                
                                if ($defaultResult->num_rows === 0) {
                                    // Insert default category
                                    $insertDefaultQuery = "INSERT INTO fee_categories (id, name, description) VALUES (1, 'Default', 'Default fee category')";
                                    
                                    if ($conn->query($insertDefaultQuery)) {
                                        echo '<div class="alert alert-success">ডিফল্ট ক্যাটাগরি সফলভাবে যোগ করা হয়েছে!</div>';
                                    } else {
                                        echo '<div class="alert alert-danger">ডিফল্ট ক্যাটাগরি যোগ করতে সমস্যা: ' . $conn->error . '</div>';
                                    }
                                }
                                
                                // Add category_id column to fees table
                                $alterTableQuery = "ALTER TABLE fees ADD COLUMN category_id INT(11) NOT NULL DEFAULT 1 AFTER payment_status";
                                
                                if ($conn->query($alterTableQuery)) {
                                    echo '<div class="alert alert-success">ফি টেবিলে category_id কলাম সফলভাবে যোগ করা হয়েছে!</div>';
                                    
                                    // Add foreign key constraint
                                    $addForeignKeyQuery = "ALTER TABLE fees ADD CONSTRAINT fees_ibfk_2 FOREIGN KEY (category_id) REFERENCES fee_categories(id) ON DELETE CASCADE";
                                    
                                    if ($conn->query($addForeignKeyQuery)) {
                                        echo '<div class="alert alert-success">ফরেন কী কনস্ট্রেইন্ট সফলভাবে যোগ করা হয়েছে!</div>';
                                    } else {
                                        echo '<div class="alert alert-danger">ফরেন কী কনস্ট্রেইন্ট যোগ করতে সমস্যা: ' . $conn->error . '</div>';
                                    }
                                } else {
                                    echo '<div class="alert alert-danger">ফি টেবিলে category_id কলাম যোগ করতে সমস্যা: ' . $conn->error . '</div>';
                                }
                            } else {
                                echo '<div class="alert alert-danger">ফি ক্যাটাগরি টেবিল তৈরি করতে সমস্যা: ' . $conn->error . '</div>';
                            }
                        } else {
                            echo '<div class="alert alert-info">ফি টেবিলে category_id কলাম ইতিমধ্যে বিদ্যমান!</div>';
                            
                            // Check if foreign key constraint exists
                            $checkForeignKeyQuery = "SELECT * FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                                                    WHERE TABLE_NAME = 'fees' 
                                                    AND COLUMN_NAME = 'category_id' 
                                                    AND REFERENCED_TABLE_NAME = 'fee_categories'";
                            $foreignKeyResult = $conn->query($checkForeignKeyQuery);
                            
                            if ($foreignKeyResult->num_rows === 0) {
                                // Add foreign key constraint
                                $addForeignKeyQuery = "ALTER TABLE fees ADD CONSTRAINT fees_ibfk_2 FOREIGN KEY (category_id) REFERENCES fee_categories(id) ON DELETE CASCADE";
                                
                                if ($conn->query($addForeignKeyQuery)) {
                                    echo '<div class="alert alert-success">ফরেন কী কনস্ট্রেইন্ট সফলভাবে যোগ করা হয়েছে!</div>';
                                } else {
                                    echo '<div class="alert alert-danger">ফরেন কী কনস্ট্রেইন্ট যোগ করতে সমস্যা: ' . $conn->error . '</div>';
                                }
                            } else {
                                echo '<div class="alert alert-info">ফরেন কী কনস্ট্রেইন্ট ইতিমধ্যে বিদ্যমান!</div>';
                            }
                        }
                        
                        // Check if fee_payments table exists
                        $checkPaymentsTableQuery = "SHOW TABLES LIKE 'fee_payments'";
                        $paymentsTableResult = $conn->query($checkPaymentsTableQuery);
                        
                        if ($paymentsTableResult->num_rows === 0) {
                            // Create fee_payments table
                            $createPaymentsTableQuery = "CREATE TABLE IF NOT EXISTS fee_payments (
                                id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
                                fee_id INT(11) NOT NULL,
                                receipt_no VARCHAR(50),
                                amount DECIMAL(10,2) NOT NULL,
                                payment_date DATE NOT NULL,
                                payment_method VARCHAR(50) NOT NULL DEFAULT 'cash',
                                notes TEXT,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                FOREIGN KEY (fee_id) REFERENCES fees(id) ON DELETE CASCADE
                            )";
                            
                            if ($conn->query($createPaymentsTableQuery)) {
                                echo '<div class="alert alert-success">ফি পেমেন্ট টেবিল সফলভাবে তৈরি করা হয়েছে!</div>';
                            } else {
                                echo '<div class="alert alert-danger">ফি পেমেন্ট টেবিল তৈরি করতে সমস্যা: ' . $conn->error . '</div>';
                            }
                        } else {
                            echo '<div class="alert alert-info">ফি পেমেন্ট টেবিল ইতিমধ্যে বিদ্যমান!</div>';
                        }
                    }
                    ?>
                    
                    <div class="mt-4">
                        <a href="fee_management.php" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-1"></i> ফি ম্যানেজমেন্ট পেজে ফিরে যান
                        </a>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php
// Include footer
include_once 'includes/footer.php';
?>
