<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

require_once '../includes/dbh.inc.php';
require_once 'class_subjects_helper.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

$classId = isset($_POST['class_id']) ? intval($_POST['class_id']) : 0;
$departmentId = isset($_POST['department_id']) && !empty($_POST['department_id']) ? intval($_POST['department_id']) : null;

if (!$classId) {
    echo json_encode(['success' => false, 'message' => 'Class ID is required']);
    exit();
}

try {
    // Validate class subject configuration
    $validation = validateClassSubjectConfig($classId, $departmentId);
    
    if (!$validation['valid']) {
        echo json_encode(['success' => false, 'message' => $validation['message']]);
        exit();
    }

    // Get subject counts by type
    $requiredSubjects = getRequiredSubjects($classId, $departmentId);
    $optionalSubjects = getOptionalSubjects($classId, $departmentId);
    $fourthSubjects = getFourthSubjects($classId, $departmentId);
    $allSubjects = getClassSubjects($classId, $departmentId);

    echo json_encode([
        'success' => true,
        'total_subjects' => count($allSubjects),
        'required_subjects' => count($requiredSubjects),
        'optional_subjects' => count($optionalSubjects),
        'fourth_subjects' => count($fourthSubjects),
        'subjects' => $allSubjects
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
