<?php
// Database Connection
require_once 'includes/dbh.inc.php';

echo "<h1>Fixing Students Table</h1>";

// Check if gender column exists
$checkGender = $conn->query("SHOW COLUMNS FROM students LIKE 'gender'");
if ($checkGender && $checkGender->num_rows == 0) {
    // Gender column doesn't exist, add it
    $alterTable = $conn->query("ALTER TABLE students ADD COLUMN gender ENUM('male', 'female', 'other') NOT NULL AFTER phone");
    
    if ($alterTable) {
        echo "<p style='color:green'>Successfully added 'gender' column to students table.</p>";
    } else {
        echo "<p style='color:red'>Error adding 'gender' column: " . $conn->error . "</p>";
    }
} else {
    echo "<p style='color:blue'>The 'gender' column already exists in the students table.</p>";
}

// Show the updated table structure
echo "<h2>Updated Students Table Structure:</h2>";
$result = $conn->query("DESCRIBE students");

if ($result) {
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>Field Name</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . ($row['Default'] === NULL ? 'NULL' : $row['Default']) . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p style='color:red'>Error: " . $conn->error . "</p>";
}

echo "<p><a href='create_essential_tables.php'>Run Create Essential Tables Script</a></p>";
echo "<p><a href='index.php'>Return to Homepage</a></p>";

// Close connection
$conn->close();
?>
