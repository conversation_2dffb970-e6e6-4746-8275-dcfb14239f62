<?php
session_start();

// Check if user is logged in and is a teacher
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'teacher') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get teacher information with department name
$userId = $_SESSION['userId'];
$username = $_SESSION['username'] ?? '';

// First try to get teacher by user_id
$sql = "SELECT t.*, d.department_name
        FROM teachers t
        LEFT JOIN departments d ON t.department_id = d.id
        WHERE t.user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();

// If no results, try to get by username
if ($result->num_rows === 0 && !empty($username)) {
    $sql = "SELECT t.*, d.department_name
            FROM teachers t
            LEFT JOIN departments d ON t.department_id = d.id
            WHERE t.username = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
}

$teacher = $result->fetch_assoc();

// If still no teacher found, redirect to login
if (!$teacher) {
    header("Location: ../index.php");
    exit();
}

// Handle result deletion
$success_msg = '';
$error_msg = '';

if (isset($_GET['delete'])) {
    $resultId = intval($_GET['delete']);

    // Check if created_by column exists in exams table
    $checkCreatedByColumnQuery = "SHOW COLUMNS FROM exams LIKE 'created_by'";
    $createdByColumnResult = $conn->query($checkCreatedByColumnQuery);
    $createdByExists = $createdByColumnResult->num_rows > 0;

    if ($createdByExists) {
        // Check if result belongs to this teacher's exams
        $checkQuery = "SELECT r.id
                      FROM results r
                      JOIN exams e ON r.exam_id = e.id
                      WHERE r.id = ? AND e.created_by = ?";
        $checkStmt = $conn->prepare($checkQuery);
        $checkStmt->bind_param("ii", $resultId, $teacher['id']);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result();

        if ($checkResult->num_rows > 0) {
            $deleteQuery = "DELETE FROM results WHERE id = ?";
            $deleteStmt = $conn->prepare($deleteQuery);
            $deleteStmt->bind_param("i", $resultId);

            if ($deleteStmt->execute()) {
                $success_msg = "ফলাফল সফলভাবে মুছে ফেলা হয়েছে";
            } else {
                $error_msg = "ফলাফল মুছে ফেলতে সমস্যা হয়েছে: " . $conn->error;
            }
        } else {
            $error_msg = "ফলাফল মুছে ফেলার অনুমতি নেই";
        }
    } else {
        // If created_by doesn't exist, allow deletion of any result
        $checkQuery = "SELECT id FROM results WHERE id = ?";
        $checkStmt = $conn->prepare($checkQuery);
        $checkStmt->bind_param("i", $resultId);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result();

        if ($checkResult->num_rows > 0) {
            $deleteQuery = "DELETE FROM results WHERE id = ?";
            $deleteStmt = $conn->prepare($deleteQuery);
            $deleteStmt->bind_param("i", $resultId);

            if ($deleteStmt->execute()) {
                $success_msg = "ফলাফল সফলভাবে মুছে ফেলা হয়েছে";
            } else {
                $error_msg = "ফলাফল মুছে ফেলতে সমস্যা হয়েছে: " . $conn->error;
            }
        } else {
            $error_msg = "ফলাফল খুঁজে পাওয়া যায়নি";
        }
    }
}

// Check for success or error messages
$success_msg = '';
$error_msg = '';

if (isset($_SESSION['success_msg'])) {
    $success_msg = $_SESSION['success_msg'];
    unset($_SESSION['success_msg']);
}

if (isset($_SESSION['error_msg'])) {
    $error_msg = $_SESSION['error_msg'];
    unset($_SESSION['error_msg']);
}

// Get filter values
$examFilter = isset($_GET['exam']) ? intval($_GET['exam']) : 0;
$classFilter = isset($_GET['class']) ? intval($_GET['class']) : 0;
$studentFilter = isset($_GET['student']) ? $_GET['student'] : '';
$per_page = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 10; // Default 10 items per page
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1; // Default to page 1

// Check if created_by column exists in exams table
$checkCreatedByColumnQuery = "SHOW COLUMNS FROM exams LIKE 'created_by'";
$createdByColumnResult = $conn->query($checkCreatedByColumnQuery);
if ($createdByColumnResult->num_rows == 0) {
    // Add created_by column if it doesn't exist
    $addCreatedByColumnQuery = "ALTER TABLE exams ADD COLUMN created_by INT(11) NULL";
    $conn->query($addCreatedByColumnQuery);

    // Update existing exams to set created_by to this teacher's ID
    $updateExamsQuery = "UPDATE exams SET created_by = ? WHERE 1";
    $updateStmt = $conn->prepare($updateExamsQuery);
    $updateStmt->bind_param("i", $teacher['id']);
    $updateStmt->execute();
}

// Get all results for this teacher's exams
try {
    // Check if tables exist
    $tablesExist = true;

    $checkResultsTableQuery = "SHOW TABLES LIKE 'results'";
    $resultsTableResult = $conn->query($checkResultsTableQuery);
    if ($resultsTableResult->num_rows == 0) {
        $tablesExist = false;
    }

    $checkExamsTableQuery = "SHOW TABLES LIKE 'exams'";
    $examsTableResult = $conn->query($checkExamsTableQuery);
    if ($examsTableResult->num_rows == 0) {
        $tablesExist = false;
    }

    if ($tablesExist) {
        // Check if created_by column exists in exams table
        $checkCreatedByColumnQuery = "SHOW COLUMNS FROM exams LIKE 'created_by'";
        $createdByColumnResult = $conn->query($checkCreatedByColumnQuery);
        $createdByExists = $createdByColumnResult->num_rows > 0;

        // Build query based on filters
        $resultsQuery = "SELECT r.*, e.exam_name, e.exam_type, e.exam_date,
                        s.student_id as student_code, s.first_name, s.last_name, s.roll_number,
                        c.class_name, subj.subject_name, subj.subject_code
                       FROM results r
                       LEFT JOIN exams e ON r.exam_id = e.id
                       LEFT JOIN students s ON r.student_id = s.id
                       LEFT JOIN classes c ON s.class_id = c.id
                       LEFT JOIN subjects subj ON r.subject_id = subj.id
                       WHERE 1=1";

        $params = [];
        $types = "";

        // Only filter by created_by if the column exists
        if ($createdByExists) {
            $resultsQuery .= " AND e.created_by = ?";
            $params[] = $teacher['id'];
            $types .= "i";
        }

        if ($examFilter > 0) {
            $resultsQuery .= " AND r.exam_id = ?";
            $params[] = $examFilter;
            $types .= "i";
        }

        if ($classFilter > 0) {
            $resultsQuery .= " AND s.class_id = ?";
            $params[] = $classFilter;
            $types .= "i";
        }

        if (!empty($studentFilter)) {
            $resultsQuery .= " AND (s.student_id LIKE ? OR s.first_name LIKE ? OR s.last_name LIKE ?)";
            $searchTerm = "%$studentFilter%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $types .= "sss";
        }

        // Count total results for pagination
        $countQuery = str_replace("SELECT r.*, e.exam_name, e.exam_type, e.exam_date,
                        s.student_id as student_code, s.first_name, s.last_name, s.roll_number,
                        c.class_name, subj.subject_name, subj.subject_code", "SELECT COUNT(*) as total", $resultsQuery);

        $countStmt = $conn->prepare($countQuery);
        $countStmt->bind_param($types, ...$params);
        $countStmt->execute();
        $countResult = $countStmt->get_result();
        $totalRows = $countResult->fetch_assoc()['total'];

        // Calculate pagination
        $totalPages = ceil($totalRows / $per_page);
        $offset = ($page - 1) * $per_page;

        // Add pagination to the query
        $resultsQuery .= " ORDER BY r.date DESC, e.exam_name, s.roll_number LIMIT ?, ?";
        $params[] = $offset;
        $params[] = $per_page;
        $types .= "ii";

        $stmt = $conn->prepare($resultsQuery);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $results = $stmt->get_result();
    } else {
        $results = null;
    }
} catch (Exception $e) {
    $error_msg = "ফলাফল লোড করতে সমস্যা হয়েছে: " . $e->getMessage();
    $results = null;
}

// Get exams for filter dropdown
try {
    // Check if created_by column exists in exams table
    $checkCreatedByColumnQuery = "SHOW COLUMNS FROM exams LIKE 'created_by'";
    $createdByColumnResult = $conn->query($checkCreatedByColumnQuery);
    $createdByExists = $createdByColumnResult->num_rows > 0;

    if ($createdByExists) {
        $examsQuery = "SELECT id, exam_name, exam_date FROM exams WHERE created_by = ? ORDER BY exam_date DESC";
        $stmt = $conn->prepare($examsQuery);
        $stmt->bind_param("i", $teacher['id']);
    } else {
        $examsQuery = "SELECT id, exam_name, exam_date FROM exams ORDER BY exam_date DESC";
        $stmt = $conn->prepare($examsQuery);
    }

    $stmt->execute();
    $exams = $stmt->get_result();
} catch (Exception $e) {
    $exams = null;
}

// Get classes for filter dropdown
try {
    $classesQuery = "SELECT id, class_name FROM classes ORDER BY class_name";
    $stmt = $conn->prepare($classesQuery);
    $stmt->execute();
    $classes = $stmt->get_result();
} catch (Exception $e) {
    $classes = null;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include __DIR__ . '/includes/global-head.php'; ?>
    <title>Results - Teacher Panel</title>

    <style>
        body, html, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, select, textarea, label, li, th, td {
            font-family: 'Hind Siliguri', sans-serif;
        }

        .table-responsive {
            overflow-x: auto;
        }

        .filter-section {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .grade-A-plus {
            background-color: #d4edda;
            font-weight: bold;
        }

        .grade-A {
            background-color: #d4edda;
        }

        .grade-A-minus {
            background-color: #d4edda;
        }

        .grade-B {
            background-color: #fff3cd;
        }

        .grade-C {
            background-color: #fff3cd;
        }

        .grade-D {
            background-color: #fff3cd;
        }

        .grade-F {
            background-color: #f8d7da;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include_once 'sidebar.php'; ?>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h2 class="mb-4">ফলাফল ব্যবস্থাপনা</h2>

                        <?php if (!empty($success_msg)): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <?php echo $success_msg; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>



                        <?php if (!empty($error_msg)): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <?php echo $error_msg; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>

                        <!-- Filter Section -->
                        <div class="filter-section">
                            <form action="" method="get" class="row g-3">
                                <div class="col-md-3">
                                    <label for="exam" class="form-label">পরীক্ষা</label>
                                    <select class="form-control" id="exam" name="exam">
                                        <option value="0">সকল পরীক্ষা</option>
                                        <?php if ($exams && $exams->num_rows > 0): ?>
                                            <?php while ($exam = $exams->fetch_assoc()): ?>
                                                <option value="<?php echo $exam['id']; ?>" <?php echo ($examFilter == $exam['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($exam['exam_name']) . ' (' . date('d M Y', strtotime($exam['exam_date'])) . ')'; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="class" class="form-label">শ্রেণী</label>
                                    <select class="form-control" id="class" name="class">
                                        <option value="0">সকল শ্রেণী</option>
                                        <?php if ($classes && $classes->num_rows > 0): ?>
                                            <?php while ($class = $classes->fetch_assoc()): ?>
                                                <option value="<?php echo $class['id']; ?>" <?php echo ($classFilter == $class['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($class['class_name']); ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="student" class="form-label">শিক্ষার্থী (আইডি/নাম)</label>
                                    <input type="text" class="form-control" id="student" name="student" value="<?php echo htmlspecialchars($studentFilter); ?>" placeholder="শিক্ষার্থী খুঁজুন">
                                </div>
                                <div class="col-md-2">
                                    <label for="per_page" class="form-label">প্রতি পৃষ্ঠায় দেখানো হবে</label>
                                    <select class="form-control" id="per_page" name="per_page">
                                        <option value="5" <?php echo ($per_page == 5) ? 'selected' : ''; ?>>৫ জন</option>
                                        <option value="10" <?php echo ($per_page == 10) ? 'selected' : ''; ?>>১০ জন</option>
                                        <option value="15" <?php echo ($per_page == 15) ? 'selected' : ''; ?>>১৫ জন</option>
                                        <option value="20" <?php echo ($per_page == 20) ? 'selected' : ''; ?>>২০ জন</option>
                                        <option value="25" <?php echo ($per_page == 25) ? 'selected' : ''; ?>>২৫ জন</option>
                                        <option value="50" <?php echo ($per_page == 50) ? 'selected' : ''; ?>>৫০ জন</option>
                                        <option value="100" <?php echo ($per_page == 100) ? 'selected' : ''; ?>>১০০ জন</option>
                                    </select>
                                </div>
                                <div class="col-md-1 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-search me-2"></i> খুঁজুন
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Results Table -->
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">ফলাফল তালিকা</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>পরীক্ষা</th>
                                                <th>তারিখ</th>
                                                <th>শিক্ষার্থী আইডি</th>
                                                <th>নাম</th>
                                                <th>শ্রেণী</th>
                                                <th>বিষয়</th>
                                                <th>প্রাপ্ত নম্বর</th>
                                                <th>মোট নম্বর</th>
                                                <th>শতকরা</th>
                                                <th>গ্রেড</th>
                                                <th>অ্যাকশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (isset($results) && $results && $results->num_rows > 0): ?>
                                                <?php while ($result = $results->fetch_assoc()):
                                                    $percentage = ($result['marks_obtained'] / $result['total_marks']) * 100;
                                                    $gradeClass = '';

                                                    switch ($result['grade']) {
                                                        case 'A+':
                                                            $gradeClass = 'grade-A-plus';
                                                            break;
                                                        case 'A':
                                                            $gradeClass = 'grade-A';
                                                            break;
                                                        case 'A-':
                                                            $gradeClass = 'grade-A-minus';
                                                            break;
                                                        case 'B':
                                                            $gradeClass = 'grade-B';
                                                            break;
                                                        case 'C':
                                                            $gradeClass = 'grade-C';
                                                            break;
                                                        case 'D':
                                                            $gradeClass = 'grade-D';
                                                            break;
                                                        case 'F':
                                                            $gradeClass = 'grade-F';
                                                            break;
                                                    }
                                                ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($result['exam_name'] ?? 'N/A'); ?></td>
                                                        <td><?php echo isset($result['date']) ? date('d M Y', strtotime($result['date'])) : 'N/A'; ?></td>
                                                        <td><?php echo htmlspecialchars($result['student_code'] ?? 'N/A'); ?></td>
                                                        <td><?php echo htmlspecialchars(($result['first_name'] ?? '') . ' ' . ($result['last_name'] ?? '')); ?></td>
                                                        <td><?php echo htmlspecialchars($result['class_name'] ?? 'N/A'); ?></td>
                                                        <td><?php echo isset($result['subject_name']) ? htmlspecialchars($result['subject_name'] . ' (' . $result['subject_code'] . ')') : 'N/A'; ?></td>
                                                        <td><?php echo $result['marks_obtained']; ?></td>
                                                        <td><?php echo $result['total_marks']; ?></td>
                                                        <td><?php echo number_format($percentage, 2) . '%'; ?></td>
                                                        <td class="<?php echo $gradeClass; ?>"><?php echo $result['grade']; ?></td>
                                                        <td>
                                                            <a href="edit_result_direct.php?id=<?php echo $result['id']; ?>" class="btn btn-sm btn-warning">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="results.php?delete=<?php echo $result['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই ফলাফল মুছে ফেলতে চান?')">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="11" class="text-center">কোন ফলাফল খুঁজে পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Pagination -->
                                <?php if (isset($totalPages) && $totalPages > 1): ?>
                                <div class="d-flex justify-content-between align-items-center mt-4">
                                    <div>
                                        <p class="mb-0">মোট <?php echo $totalRows; ?> জন শিক্ষার্থীর মধ্যে <?php echo ($page-1)*$per_page+1; ?> থেকে <?php echo min($page*$per_page, $totalRows); ?> পর্যন্ত দেখানো হচ্ছে</p>
                                    </div>
                                    <nav aria-label="Page navigation">
                                        <ul class="pagination mb-0">
                                            <?php if ($page > 1): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?exam=<?php echo $examFilter; ?>&class=<?php echo $classFilter; ?>&student=<?php echo urlencode($studentFilter); ?>&per_page=<?php echo $per_page; ?>&page=<?php echo $page-1; ?>" aria-label="Previous">
                                                    <span aria-hidden="true">&laquo;</span>
                                                </a>
                                            </li>
                                            <?php endif; ?>

                                            <?php
                                            // Show limited page numbers with ellipsis
                                            $startPage = max(1, $page - 2);
                                            $endPage = min($totalPages, $page + 2);

                                            // Always show first page
                                            if ($startPage > 1) {
                                                echo '<li class="page-item"><a class="page-link" href="?exam=' . $examFilter . '&class=' . $classFilter . '&student=' . urlencode($studentFilter) . '&per_page=' . $per_page . '&page=1">১</a></li>';
                                                if ($startPage > 2) {
                                                    echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                                                }
                                            }

                                            // Display page numbers
                                            for ($i = $startPage; $i <= $endPage; $i++) {
                                                echo '<li class="page-item ' . ($page == $i ? 'active' : '') . '">
                                                    <a class="page-link" href="?exam=' . $examFilter . '&class=' . $classFilter . '&student=' . urlencode($studentFilter) . '&per_page=' . $per_page . '&page=' . $i . '">' . $i . '</a>
                                                </li>';
                                            }

                                            // Always show last page
                                            if ($endPage < $totalPages) {
                                                if ($endPage < $totalPages - 1) {
                                                    echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                                                }
                                                echo '<li class="page-item"><a class="page-link" href="?exam=' . $examFilter . '&class=' . $classFilter . '&student=' . urlencode($studentFilter) . '&per_page=' . $per_page . '&page=' . $totalPages . '">' . $totalPages . '</a></li>';
                                            }
                                            ?>

                                            <?php if ($page < $totalPages): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?exam=<?php echo $examFilter; ?>&class=<?php echo $classFilter; ?>&student=<?php echo urlencode($studentFilter); ?>&per_page=<?php echo $per_page; ?>&page=<?php echo $page+1; ?>" aria-label="Next">
                                                    <span aria-hidden="true">&raquo;</span>
                                                </a>
                                            </li>
                                            <?php endif; ?>
                                        </ul>
                                    </nav>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-submit form when dropdown changes
            document.getElementById('exam').addEventListener('change', function() {
                document.querySelector('form').submit();
            });

            document.getElementById('class').addEventListener('change', function() {
                document.querySelector('form').submit();
            });

            document.getElementById('per_page').addEventListener('change', function() {
                document.querySelector('form').submit();
            });
        });
    </script>
</body>
</html>
