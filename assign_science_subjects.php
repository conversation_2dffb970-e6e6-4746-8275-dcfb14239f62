<?php
// Include database connection
require_once "includes/dbh.inc.php";

// Initialize variables
$success_message = '';
$error_message = '';

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get the selected subjects and category
    $subject_ids = $_POST['subject_ids'] ?? [];
    $category = $_POST['category'] ?? 'required';
    
    if (empty($subject_ids)) {
        $error_message = "Please select at least one subject.";
    } else {
        // Get the science department ID
        $dept_query = "SELECT id FROM departments WHERE department_name LIKE '%Science%' OR department_name LIKE '%বিজ্ঞান%' LIMIT 1";
        $dept_result = $conn->query($dept_query);
        
        if ($dept_result && $dept_result->num_rows > 0) {
            $department_id = $dept_result->fetch_assoc()['id'];
            
            // Begin transaction
            $conn->begin_transaction();
            
            try {
                // 1. Insert into subject_departments table
                $insert_dept_query = "INSERT IGNORE INTO subject_departments (subject_id, department_id) VALUES (?, ?)";
                $stmt_dept = $conn->prepare($insert_dept_query);
                
                // 2. Insert into department_subject_types table
                $insert_type_query = "INSERT INTO department_subject_types (subject_id, department_id, subject_type) 
                                     VALUES (?, ?, ?) 
                                     ON DUPLICATE KEY UPDATE subject_type = VALUES(subject_type)";
                $stmt_type = $conn->prepare($insert_type_query);
                
                $success_count = 0;
                foreach ($subject_ids as $subject_id) {
                    // Insert into subject_departments
                    $stmt_dept->bind_param("ii", $subject_id, $department_id);
                    $stmt_dept->execute();
                    
                    // Insert into department_subject_types
                    $stmt_type->bind_param("iis", $subject_id, $department_id, $category);
                    $stmt_type->execute();
                    
                    $success_count++;
                }
                
                // Commit the transaction
                $conn->commit();
                
                $success_message = "$success_count subjects have been successfully assigned to the Science department with category: $category";
            } catch (Exception $e) {
                // Rollback the transaction if something went wrong
                $conn->rollback();
                $error_message = "Error: " . $e->getMessage();
            }
        } else {
            $error_message = "Science department not found.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assign Science Subjects</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Assign Subjects to Science Department</h1>
        
        <?php if (!empty($success_message)): ?>
            <div class="alert alert-success">
                <?php echo $success_message; ?>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($error_message)): ?>
            <div class="alert alert-danger">
                <?php echo $error_message; ?>
            </div>
        <?php endif; ?>
        
        <div class="mt-4">
            <a href="check_science_subjects.php" class="btn btn-primary">Back to Subject List</a>
            <a href="departments/science.php" class="btn btn-success">View Science Department</a>
        </div>
    </div>
</body>
</html>
