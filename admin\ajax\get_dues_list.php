<?php
/**
 * AJAX handler for getting dues list
 * This file handles the AJAX request for getting a list of dues based on filter criteria
 */

session_start();
require_once '../../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => 'Unauthorized access'
    ]);
    exit();
}

// Get filter data from POST request
$data = json_decode(file_get_contents('php://input'), true);

// Initialize filter variables
$sessionId = $data['session_id'] ?? '';
$classId = $data['class_id'] ?? '';
$departmentId = $data['department_id'] ?? '';
$studentSearch = $data['student_search'] ?? '';
$feeType = $data['fee_type'] ?? '';
$paymentStatus = $data['payment_status'] ?? 'due';
$fromDate = $data['from_date'] ?? '';
$toDate = $data['to_date'] ?? '';
$minAmount = $data['min_amount'] ?? '';
$maxAmount = $data['max_amount'] ?? '';
$sortBy = $data['sort_by'] ?? 'due_date_asc';
$perPage = $data['per_page'] ?? '10';

try {
    // Build query to get dues
    $query = "SELECT f.*, 
                    s.first_name, s.last_name, s.student_id as student_roll,
                    CONCAT(s.first_name, ' ', s.last_name) as student_name,
                    c.class_name, ss.session_name, d.department_name
             FROM fees f
             JOIN students s ON f.student_id = s.id
             LEFT JOIN classes c ON s.class_id = c.id
             LEFT JOIN sessions ss ON s.session_id = ss.id
             LEFT JOIN departments d ON s.department_id = d.id
             WHERE 1=1";
    
    $params = [];
    $types = "";
    
    // Add payment status filter
    if ($paymentStatus === 'due') {
        $query .= " AND f.payment_status = 'due'";
    } else if ($paymentStatus === 'partial') {
        $query .= " AND f.payment_status = 'partial'";
    } else if ($paymentStatus !== 'all') {
        $query .= " AND (f.payment_status = 'due' OR f.payment_status = 'partial')";
    }
    
    // Add session filter
    if (!empty($sessionId)) {
        $query .= " AND s.session_id = ?";
        $params[] = $sessionId;
        $types .= "i";
    }
    
    // Add class filter
    if (!empty($classId)) {
        $query .= " AND s.class_id = ?";
        $params[] = $classId;
        $types .= "i";
    }
    
    // Add department filter
    if (!empty($departmentId)) {
        $query .= " AND s.department_id = ?";
        $params[] = $departmentId;
        $types .= "i";
    }
    
    // Add student search filter
    if (!empty($studentSearch)) {
        $query .= " AND (s.first_name LIKE ? OR s.last_name LIKE ? OR s.student_id LIKE ? OR CONCAT(s.first_name, ' ', s.last_name) LIKE ?)";
        $searchTerm = "%$studentSearch%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $types .= "ssss";
    }
    
    // Add fee type filter
    if (!empty($feeType)) {
        $query .= " AND f.fee_type = ?";
        $params[] = $feeType;
        $types .= "s";
    }
    
    // Add date range filters
    if (!empty($fromDate)) {
        $query .= " AND f.due_date >= ?";
        $params[] = $fromDate;
        $types .= "s";
    }
    
    if (!empty($toDate)) {
        $query .= " AND f.due_date <= ?";
        $params[] = $toDate;
        $types .= "s";
    }
    
    // Add amount range filters
    if (!empty($minAmount)) {
        $query .= " AND (f.amount - f.paid) >= ?";
        $params[] = $minAmount;
        $types .= "d";
    }
    
    if (!empty($maxAmount)) {
        $query .= " AND (f.amount - f.paid) <= ?";
        $params[] = $maxAmount;
        $types .= "d";
    }
    
    // Add sorting
    switch ($sortBy) {
        case 'due_date_asc':
            $query .= " ORDER BY f.due_date ASC";
            break;
        case 'due_date_desc':
            $query .= " ORDER BY f.due_date DESC";
            break;
        case 'amount_desc':
            $query .= " ORDER BY (f.amount - f.paid) DESC";
            break;
        case 'amount_asc':
            $query .= " ORDER BY (f.amount - f.paid) ASC";
            break;
        case 'student_name':
            $query .= " ORDER BY s.first_name ASC, s.last_name ASC";
            break;
        default:
            $query .= " ORDER BY f.due_date ASC";
            break;
    }
    
    // Add limit if not 'all'
    if ($perPage !== 'all') {
        $limit = intval($perPage);
        if ($limit <= 0) $limit = 10; // Default to 10 if invalid
        $query .= " LIMIT $limit";
    }
    
    // Prepare and execute query
    $stmt = $conn->prepare($query);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    
    // Fetch all dues
    $dues = [];
    while ($row = $result->fetch_assoc()) {
        $dues[] = [
            'id' => $row['id'],
            'student_id' => $row['student_id'],
            'student_name' => $row['student_name'],
            'student_roll' => $row['student_roll'],
            'class_name' => $row['class_name'],
            'session_name' => $row['session_name'],
            'department_name' => $row['department_name'],
            'fee_type' => $row['fee_type'],
            'amount' => $row['amount'],
            'paid' => $row['paid'],
            'due_date' => $row['due_date'],
            'payment_status' => $row['payment_status']
        ];
    }
    
    // Return success response with dues data
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'success',
        'dues' => $dues,
        'count' => count($dues)
    ]);
    
} catch (Exception $e) {
    // Return error response
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => 'Error fetching dues: ' . $e->getMessage()
    ]);
}
