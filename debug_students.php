<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Database connection parameters
$servername = "127.0.0.1";
$username = "root";
$password = "";
$dbname = "zfaw";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "<h1>Student Debug Script</h1>";
echo "<pre>";

// Check if students table exists
$tableCheckQuery = "SHOW TABLES LIKE 'students'";
$tableCheckResult = $conn->query($tableCheckQuery);
$studentsTableExists = ($tableCheckResult && $tableCheckResult->num_rows > 0);

echo "Students table exists: " . ($studentsTableExists ? 'Yes' : 'No') . "\n\n";

if (!$studentsTableExists) {
    echo "Creating students table...\n";
    
    // Create students table
    $createStudentsTable = "CREATE TABLE IF NOT EXISTS students (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        student_id VARCHAR(20) NOT NULL,
        first_name VARCHAR(50) NOT NULL,
        last_name VARCHAR(50) NOT NULL,
        roll_no VARCHAR(20) DEFAULT NULL,
        class_id INT(11) NOT NULL,
        session_id INT(11) NOT NULL,
        department_id INT(11) DEFAULT NULL,
        gender ENUM('male', 'female', 'other') DEFAULT NULL,
        dob DATE DEFAULT NULL,
        email VARCHAR(100) DEFAULT NULL,
        phone VARCHAR(20) DEFAULT NULL,
        address TEXT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($createStudentsTable)) {
        echo "Students table created successfully.\n";
    } else {
        echo "Error creating students table: " . $conn->error . "\n";
    }
}

// Check if classes table exists
$tableCheckQuery = "SHOW TABLES LIKE 'classes'";
$tableCheckResult = $conn->query($tableCheckQuery);
$classesTableExists = ($tableCheckResult && $tableCheckResult->num_rows > 0);

echo "Classes table exists: " . ($classesTableExists ? 'Yes' : 'No') . "\n\n";

// Check if sessions table exists
$tableCheckQuery = "SHOW TABLES LIKE 'sessions'";
$tableCheckResult = $conn->query($tableCheckQuery);
$sessionsTableExists = ($tableCheckResult && $tableCheckResult->num_rows > 0);

echo "Sessions table exists: " . ($sessionsTableExists ? 'Yes' : 'No') . "\n\n";

// Get all classes
if ($classesTableExists) {
    $classesQuery = "SELECT * FROM classes ORDER BY id";
    $classesResult = $conn->query($classesQuery);
    
    echo "Classes:\n";
    if ($classesResult && $classesResult->num_rows > 0) {
        while ($class = $classesResult->fetch_assoc()) {
            echo "ID: {$class['id']}, Name: {$class['class_name']}\n";
        }
    } else {
        echo "No classes found.\n";
    }
    echo "\n";
}

// Get all sessions
if ($sessionsTableExists) {
    $sessionsQuery = "SELECT * FROM sessions ORDER BY id";
    $sessionsResult = $conn->query($sessionsQuery);
    
    echo "Sessions:\n";
    if ($sessionsResult && $sessionsResult->num_rows > 0) {
        while ($session = $sessionsResult->fetch_assoc()) {
            echo "ID: {$session['id']}, Name: {$session['session_name']}\n";
        }
    } else {
        echo "No sessions found.\n";
    }
    echo "\n";
}

// Get all students
if ($studentsTableExists) {
    $studentsQuery = "SELECT s.*, c.class_name, ss.session_name 
                     FROM students s
                     LEFT JOIN classes c ON s.class_id = c.id
                     LEFT JOIN sessions ss ON s.session_id = ss.id
                     ORDER BY s.id";
    $studentsResult = $conn->query($studentsQuery);
    
    echo "Students:\n";
    if ($studentsResult && $studentsResult->num_rows > 0) {
        while ($student = $studentsResult->fetch_assoc()) {
            echo "ID: {$student['id']}, Name: {$student['first_name']} {$student['last_name']}, ";
            echo "Class: " . ($student['class_name'] ?? 'N/A') . ", ";
            echo "Session: " . ($student['session_name'] ?? 'N/A') . "\n";
        }
    } else {
        echo "No students found.\n";
    }
    echo "\n";
}

// Check for students with specific class and session
if ($studentsTableExists && $classesTableExists && $sessionsTableExists) {
    // Get first class and session
    $classQuery = "SELECT id FROM classes ORDER BY id LIMIT 1";
    $classResult = $conn->query($classQuery);
    $classId = $classResult->fetch_assoc()['id'] ?? 0;
    
    $sessionQuery = "SELECT id FROM sessions ORDER BY id LIMIT 1";
    $sessionResult = $conn->query($sessionQuery);
    $sessionId = $sessionResult->fetch_assoc()['id'] ?? 0;
    
    if ($classId && $sessionId) {
        echo "Checking for students with class_id=$classId and session_id=$sessionId:\n";
        
        $query = "SELECT s.id, s.student_id, s.first_name, s.last_name, s.roll_no, s.email, s.phone, d.department_name
                  FROM students s
                  LEFT JOIN departments d ON s.department_id = d.id
                  WHERE s.class_id = ? AND s.session_id = ?
                  ORDER BY s.first_name, s.last_name";
        
        $stmt = $conn->prepare($query);
        $stmt->bind_param("ii", $classId, $sessionId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            while ($student = $result->fetch_assoc()) {
                echo "ID: {$student['id']}, Name: {$student['first_name']} {$student['last_name']}, ";
                echo "Student ID: {$student['student_id']}, Roll: " . ($student['roll_no'] ?? 'N/A') . "\n";
            }
        } else {
            echo "No students found with class_id=$classId and session_id=$sessionId.\n";
            
            // Insert sample students
            echo "\nInserting sample students for class_id=$classId and session_id=$sessionId...\n";
            
            $sampleStudents = [
                ['Karim', 'Ahmed', '101', 'male', '<EMAIL>', '01712345678'],
                ['Fatima', 'Begum', '102', 'female', '<EMAIL>', '01712345679'],
                ['Rahim', 'Khan', '103', 'male', '<EMAIL>', '01712345680']
            ];
            
            $insertStudentQuery = "INSERT INTO students (student_id, first_name, last_name, roll_no, class_id, session_id, gender, email, phone) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($insertStudentQuery);
            
            foreach ($sampleStudents as $student) {
                // Generate a random 6-digit student ID
                $studentId = 'S' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
                $rollNo = $student[2];
                
                $stmt->bind_param("ssssiisss", 
                    $studentId,
                    $student[0], // first_name
                    $student[1], // last_name
                    $rollNo,     // roll_no
                    $classId,    // class_id
                    $sessionId,  // session_id
                    $student[3], // gender
                    $student[4], // email
                    $student[5]  // phone
                );
                
                if ($stmt->execute()) {
                    echo "Inserted student: {$student[0]} {$student[1]}\n";
                } else {
                    echo "Error inserting student: " . $stmt->error . "\n";
                }
            }
            
            // Check again
            echo "\nChecking again for students with class_id=$classId and session_id=$sessionId:\n";
            
            $stmt = $conn->prepare($query);
            $stmt->bind_param("ii", $classId, $sessionId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result && $result->num_rows > 0) {
                while ($student = $result->fetch_assoc()) {
                    echo "ID: {$student['id']}, Name: {$student['first_name']} {$student['last_name']}, ";
                    echo "Student ID: {$student['student_id']}, Roll: " . ($student['roll_no'] ?? 'N/A') . "\n";
                }
            } else {
                echo "Still no students found with class_id=$classId and session_id=$sessionId.\n";
            }
        }
    }
}

echo "</pre>";
echo "<p>Debug completed. You can now <a href='admin/fee_assign.php'>go to the fee assign page</a>.</p>";

// Close connection
$conn->close();
?>
