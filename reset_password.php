<?php
// Include database connection
require_once 'includes/dbh.inc.php';

// Initialize variables
$message = '';
$error = '';
$success = false;

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate input
    $username = trim($_POST['username']);
    $userType = trim($_POST['userType']);
    $newPassword = trim($_POST['new_password']);
    $confirmPassword = trim($_POST['confirm_password']);
    
    // Basic validation
    if (empty($username) || empty($userType) || empty($newPassword) || empty($confirmPassword)) {
        $error = "সব ক্ষেত্র পূরণ করুন।";
    } else if ($newPassword !== $confirmPassword) {
        $error = "পাসওয়ার্ড মিলছে না।";
    } else if (strlen($newPassword) < 6) {
        $error = "পাসওয়ার্ড কমপক্ষে ৬ অক্ষর হতে হবে।";
    } else {
        // Check if user exists
        $sql = "SELECT * FROM users WHERE username=? AND user_type=?";
        $stmt = $conn->prepare($sql);
        
        if (!$stmt) {
            $error = "ডাটাবেস ত্রুটি: " . $conn->error;
        } else {
            $stmt->bind_param("ss", $username, $userType);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows === 0) {
                $error = "ব্যবহারকারী খুঁজে পাওয়া যায়নি।";
            } else {
                // Update the password
                $user = $result->fetch_assoc();
                $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                
                $updateSql = "UPDATE users SET password=? WHERE id=?";
                $updateStmt = $conn->prepare($updateSql);
                
                if (!$updateStmt) {
                    $error = "ডাটাবেস ত্রুটি: " . $conn->error;
                } else {
                    $updateStmt->bind_param("si", $hashedPassword, $user['id']);
                    
                    if ($updateStmt->execute()) {
                        $success = true;
                        $message = "ব্যবহারকারী '{$username}' এর পাসওয়ার্ড সফলভাবে আপডেট করা হয়েছে।";
                    } else {
                        $error = "পাসওয়ার্ড আপডেট করা যায়নি: " . $updateStmt->error;
                    }
                    
                    $updateStmt->close();
                }
            }
            
            $stmt->close();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>পাসওয়ার্ড রিসেট</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-6 mx-auto">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h3 class="card-title mb-0">পাসওয়ার্ড রিসেট</h3>
                    </div>
                    
                    <div class="card-body">
                        <?php if (!empty($error)): ?>
                            <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success"><?php echo $message; ?></div>
                            <div class="text-center mt-3">
                                <a href="index.php#login-section" class="btn btn-primary">লগইন পেজে যান</a>
                            </div>
                        <?php else: ?>
                            <form method="post">
                                <div class="mb-3">
                                    <label for="username" class="form-label">ইউজারনেম</label>
                                    <input type="text" class="form-control" id="username" name="username" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="userType" class="form-label">ইউজার টাইপ</label>
                                    <select class="form-select" id="userType" name="userType" required>
                                        <option value="admin">অ্যাডমিন</option>
                                        <option value="teacher">শিক্ষক</option>
                                        <option value="student">শিক্ষার্থী</option>
                                        <option value="staff">কর্মচারী</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="new_password" class="form-label">নতুন পাসওয়ার্ড</label>
                                    <input type="password" class="form-control" id="new_password" name="new_password" required>
                                    <small class="text-muted">পাসওয়ার্ড কমপক্ষে ৬ অক্ষর হতে হবে</small>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">পাসওয়ার্ড নিশ্চিত করুন</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100">পাসওয়ার্ড রিসেট করুন</button>
                            </form>
                            
                            <div class="mt-3 text-center">
                                <a href="index.php" class="text-decoration-none">হোম পেজে ফিরে যান</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 