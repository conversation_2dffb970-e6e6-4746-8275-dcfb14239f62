<?php
// Include database connection
require_once "includes/dbh.inc.php";

// Check if the dob column exists in the students table
$check_dob_column = $conn->query("SHOW COLUMNS FROM students LIKE 'dob'");

if ($check_dob_column->num_rows == 0) {
    // The dob column doesn't exist, so add it
    $add_column_query = "ALTER TABLE students ADD COLUMN dob DATE NULL AFTER gender";
    
    if ($conn->query($add_column_query)) {
        echo "<p style='color:green; font-size:18px;'>Success: The 'dob' column has been added to the students table.</p>";
    } else {
        echo "<p style='color:red; font-size:18px;'>Error adding 'dob' column: " . $conn->error . "</p>";
    }
} else {
    echo "<p style='color:blue; font-size:18px;'>The 'dob' column already exists in the students table.</p>";
}

// Display the current structure of the students table
$result = $conn->query("DESCRIBE students");
if ($result) {
    echo "<h2>Current Students Table Structure:</h2>";
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . ($row['Default'] === NULL ? 'NULL' : $row['Default']) . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<p>Now you can go back to <a href='admin/add_student.php'>Add Student</a> page and try again.</p>";
} else {
    echo "<p style='color:red'>Error: " . $conn->error . "</p>";
}

// Close connection
$conn->close();
?>
