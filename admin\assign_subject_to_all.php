<?php
session_start();
require_once 'includes/db_connection.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header("Location: ../login.php");
    exit();
}

// Initialize variables
$success_message = '';
$error_message = '';
$log_messages = [];

// Check if required parameters are provided
if (!isset($_GET['exam_id']) || !isset($_GET['subject_id'])) {
    $error_message = "পরীক্ষা আইডি এবং বিষয় আইডি প্রয়োজন।";
} else {
    $exam_id = $_GET['exam_id'];
    $subject_id = $_GET['subject_id'];

    // Get exam details to find class_id
    $exam_sql = "SELECT e.*, c.class_name
                FROM exams e
                LEFT JOIN classes c ON e.class_id = c.id
                WHERE e.id = ?";
    $exam_stmt = $conn->prepare($exam_sql);
    $exam_stmt->bind_param("i", $exam_id);
    $exam_stmt->execute();
    $exam_result = $exam_stmt->get_result();

    if ($exam_result->num_rows === 0) {
        $error_message = "পরীক্ষা খুঁজে পাওয়া যায়নি।";
    } else {
        $exam_details = $exam_result->fetch_assoc();
        $class_id = $exam_details['class_id'];

        if (!$class_id) {
            $error_message = "এই পরীক্ষার জন্য কোন শ্রেণী নির্ধারিত নেই।";
        } else {
            // Get subject details
            $subject_sql = "SELECT * FROM subjects WHERE id = ?";
            $subject_stmt = $conn->prepare($subject_sql);
            $subject_stmt->bind_param("i", $subject_id);
            $subject_stmt->execute();
            $subject_result = $subject_stmt->get_result();

            if ($subject_result->num_rows === 0) {
                $error_message = "বিষয় খুঁজে পাওয়া যায়নি।";
            } else {
                $subject_details = $subject_result->fetch_assoc();

                // Get all students in the class
                $students_sql = "SELECT * FROM students WHERE class_id = ?";
                $students_stmt = $conn->prepare($students_sql);
                $students_stmt->bind_param("i", $class_id);
                $students_stmt->execute();
                $students_result = $students_stmt->get_result();

                if ($students_result->num_rows === 0) {
                    $error_message = "এই শ্রেণীতে কোন শিক্ষার্থী নেই।";
                } else {
                    // Get current session
                    $session_sql = "SELECT id FROM sessions WHERE is_current = 1 LIMIT 1";
                    $session_result = $conn->query($session_sql);
                    $session_id = 1; // Default session ID

                    if ($session_result && $session_result->num_rows > 0) {
                        $session_id = $session_result->fetch_assoc()['id'];
                    }

                    // Start transaction
                    $conn->begin_transaction();

                    try {
                        // Insert each student into student_subjects
                        $insert_sql = "INSERT IGNORE INTO student_subjects (student_id, subject_id, category, session_id) VALUES (?, ?, 'required', ?)";
                        $insert_stmt = $conn->prepare($insert_sql);

                        $assigned_count = 0;
                        $student_count = $students_result->num_rows;

                        while ($student = $students_result->fetch_assoc()) {
                            $student_id = $student['id'];
                            $insert_stmt->bind_param("iii", $student_id, $subject_id, $session_id);

                            if ($insert_stmt->execute()) {
                                if ($insert_stmt->affected_rows > 0) {
                                    $assigned_count++;
                                    $log_messages[] = "শিক্ষার্থী আইডি {$student['id']} ({$student['first_name']} {$student['last_name']}) - বিষয় বরাদ্দ করা হয়েছে";
                                } else {
                                    $log_messages[] = "শিক্ষার্থী আইডি {$student['id']} ({$student['first_name']} {$student['last_name']}) - ইতিমধ্যে বিষয় বরাদ্দ করা আছে";
                                }
                            } else {
                                $log_messages[] = "শিক্ষার্থী আইডি {$student['id']} - সমস্যা: " . $insert_stmt->error;
                            }
                        }

                        // Commit transaction
                        $conn->commit();

                        if ($assigned_count > 0) {
                            $success_message = "সফলভাবে $assigned_count জন শিক্ষার্থীকে '{$subject_details['subject_name']}' বিষয় বরাদ্দ করা হয়েছে।";
                        } else {
                            $success_message = "সকল শিক্ষার্থীদের ইতিমধ্যে এই বিষয় বরাদ্দ করা আছে।";
                        }
                    } catch (Exception $e) {
                        // Rollback on error
                        $conn->rollback();
                        $error_message = "সমস্যা হয়েছে: " . $e->getMessage();
                    }
                }
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>সকল শিক্ষার্থীকে বিষয় বরাদ্দ - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-size: .875rem;
            background-color: #f8f9fa;
        }
        .main-content {
            margin-left: 240px;
            padding: 20px;
        }
        @media (max-width: 767.98px) {
            .main-content {
                margin-left: 0;
            }
        }
        .log-container {
            max-height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 20px;
        }
        .log-item {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .log-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <?php include('includes/header.php'); ?>

    <div class="container-fluid">
        <div class="row">
            <?php include('includes/sidebar.php'); ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">সকল শিক্ষার্থীকে বিষয় বরাদ্দ</h1>
                </div>

                <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error_message; ?>
                </div>
                <?php endif; ?>

                <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i> <?php echo $success_message; ?>
                </div>
                <?php endif; ?>

                <div class="card mb-4">
                    <div class="card-body">
                        <p>
                            <a href="marks_entry.php?exam_id=<?php echo $exam_id; ?>&subject_id=<?php echo $subject_id; ?>" class="btn btn-primary">
                                <i class="fas fa-arrow-left me-1"></i> নম্বর এন্ট্রি পেজে ফিরে যান
                            </a>
                        </p>

                        <?php if (!empty($log_messages)): ?>
                        <div class="log-container mt-4">
                            <h5>প্রক্রিয়াকরণের লগ:</h5>
                            <?php foreach ($log_messages as $log): ?>
                            <div class="log-item">
                                <?php echo $log; ?>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
