<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>নিশাত এডুকেশন সেন্টার - Offline Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }
        
        .container {
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            max-width: 600px;
            margin: 0 auto;
        }
        
        .status {
            font-size: 24px;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            background: rgba(255,255,255,0.2);
        }
        
        .success {
            background: rgba(76, 175, 80, 0.3);
            border: 2px solid #4CAF50;
        }
        
        .info {
            background: rgba(33, 150, 243, 0.3);
            border: 2px solid #2196F3;
        }
        
        button {
            background: #FF5722;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s;
        }
        
        button:hover {
            background: #E64A19;
            transform: translateY(-2px);
        }
        
        .timer {
            font-size: 48px;
            font-weight: bold;
            margin: 20px 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏫 নিশাত এডুকেশন সেন্টার</h1>
        <h2>Offline Buffer Test</h2>
        
        <div class="status success">
            ✅ This page is completely OFFLINE
        </div>
        
        <div class="status info">
            📊 Current Status: <span id="status">Loading...</span>
        </div>
        
        <div class="timer" id="timer">00:00</div>
        
        <div class="status info">
            🎯 Title: <span id="title-display">Loading...</span>
        </div>
        
        <button onclick="testTitle()">Test Title Change</button>
        <button onclick="testReload()">Test Reload</button>
        <button onclick="openXAMPP()">Open XAMPP Version</button>
        
        <div style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
            <p>📝 Instructions:</p>
            <p>1. Watch the title bar of this page</p>
            <p>2. If this page shows NO buffering, the problem is with XAMPP</p>
            <p>3. If this page ALSO shows buffering, the problem is with your browser/system</p>
        </div>
    </div>

    <script>
        let seconds = 0;
        let titleTestCount = 0;
        
        // Update timer
        function updateTimer() {
            seconds++;
            const mins = Math.floor(seconds / 60);
            const secs = seconds % 60;
            document.getElementById('timer').textContent = 
                String(mins).padStart(2, '0') + ':' + String(secs).padStart(2, '0');
        }
        
        // Update status
        function updateStatus() {
            const status = document.getElementById('status');
            const titleDisplay = document.getElementById('title-display');
            
            status.textContent = 'Running Normally ✅';
            titleDisplay.textContent = document.title;
            
            // Check if title is correct
            if (document.title === 'নিশাত এডুকেশন সেন্টার - Offline Test') {
                titleDisplay.style.color = '#4CAF50';
            } else {
                titleDisplay.style.color = '#F44336';
            }
        }
        
        // Test title change
        function testTitle() {
            titleTestCount++;
            const originalTitle = 'নিশাত এডুকেশন সেন্টার - Offline Test';
            document.title = `Test ${titleTestCount} - ${new Date().getSeconds()}`;
            
            setTimeout(() => {
                document.title = originalTitle;
                updateStatus();
            }, 2000);
        }
        
        // Test reload
        function testReload() {
            if (confirm('This will reload the page. Continue?')) {
                location.reload();
            }
        }
        
        // Open XAMPP version
        function openXAMPP() {
            window.open('http://localhost/zfaw/system-debug.html', '_blank');
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Set correct title immediately
            document.title = 'নিশাত এডুকেশন সেন্টার - Offline Test';
            
            // Start timers
            setInterval(updateTimer, 1000);
            setInterval(updateStatus, 500);
            
            // Initial update
            updateStatus();
            
            console.log('Offline test page loaded successfully');
            console.log('If you see buffering here, the problem is browser/system level');
        });
        
        // Monitor title changes
        let lastTitle = document.title;
        setInterval(() => {
            if (document.title !== lastTitle) {
                console.log('Title changed from:', lastTitle, 'to:', document.title);
                lastTitle = document.title;
            }
        }, 100);
        
        // Performance monitoring
        window.addEventListener('load', function() {
            console.log('Page load time:', (performance.now() / 1000).toFixed(2), 'seconds');
        });
    </script>
</body>
</html>
