/**
 * Stop Loading Script
 * This script completely stops all loading animations and prevents auto-loading
 */

// Execute immediately
(function() {
    // Stop any existing intervals and timeouts
    for (let i = 1; i < 999999; i++) {
        window.clearInterval(i);
        window.clearTimeout(i);
    }
    
    // Force hide all loading elements
    const hideLoaders = function() {
        // Remove any loading elements
        const loadingSelectors = [
            '#page-loader', '.loader', '.loading-spinner', '.loader-spinner', '.loader-text',
            '[id*="loader"]', '[class*="loader"]', '[class*="loading"]', '.preloader', '.spinner',
            '[id*="preloader"]', '[class*="preloader"]', '[class*="spinner"]', '[class*="ajax"]',
            '[id*="ajax"]', '[class*="progress"]', '[id*="progress"]', '[class*="waiting"]',
            '[id*="waiting"]', '[class*="animat"]', '[id*="animat"]'
        ];
        
        loadingSelectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    if (element) {
                        // Hide the element
                        element.style.display = 'none';
                        element.style.opacity = '0';
                        element.style.visibility = 'hidden';
                        element.style.height = '0';
                        element.style.width = '0';
                        element.style.position = 'absolute';
                        element.style.zIndex = '-9999';
                        element.style.pointerEvents = 'none';
                        
                        // Stop animations
                        element.style.animation = 'none';
                        element.style.transition = 'none';
                        
                        // Try to remove the element completely
                        try {
                            element.remove();
                        } catch (e) {
                            console.log('Could not remove element:', e);
                        }
                    }
                });
            } catch (e) {
                console.log('Error processing selector:', selector, e);
            }
        });
        
        // Add loaded class to body
        document.body.classList.add('loaded');
        document.body.classList.remove('loading');
        
        // Force page to be visible
        document.body.style.display = 'block';
        document.body.style.visibility = 'visible';
        document.body.style.opacity = '1';
        
        // Stop any animations
        try {
            const animationElements = document.querySelectorAll('*');
            animationElements.forEach(element => {
                element.style.animation = 'none';
                element.style.transition = 'none';
            });
        } catch (e) {
            console.log('Error stopping animations:', e);
        }
        
        // Clear any pending AJAX requests
        if (window.XMLHttpRequest) {
            try {
                const oldXHR = window.XMLHttpRequest;
                window.XMLHttpRequest = function() {
                    const xhr = new oldXHR();
                    xhr.abort();
                    return xhr;
                };
            } catch (e) {
                console.log('Error overriding XMLHttpRequest:', e);
            }
        }
        
        // Override setTimeout and setInterval
        try {
            const originalSetTimeout = window.setTimeout;
            window.setTimeout = function(callback, delay) {
                if (delay > 1000) return;
                return originalSetTimeout(callback, delay);
            };
            
            const originalSetInterval = window.setInterval;
            window.setInterval = function() {
                return -1; // Return invalid ID to prevent intervals
            };
        } catch (e) {
            console.log('Error overriding timers:', e);
        }
        
        // Override the title to prevent changes
        try {
            const originalTitle = document.title;
            Object.defineProperty(document, 'title', {
                get: function() {
                    return originalTitle;
                },
                set: function(newTitle) {
                    // Only allow setting if it's the original title
                    if (newTitle === originalTitle) {
                        return newTitle;
                    }
                    return originalTitle;
                }
            });
        } catch (e) {
            console.log('Error overriding title:', e);
        }
        
        // Add CSS to hide all loading elements
        try {
            const style = document.createElement('style');
            style.textContent = `
                body.loading,
                body.loaded {
                    display: block !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                }
                
                #page-loader,
                .loader,
                .loading-spinner,
                .loader-spinner,
                .loader-text,
                [id*="loader"],
                [class*="loader"],
                [class*="loading"],
                .preloader,
                .spinner,
                [id*="preloader"],
                [class*="preloader"],
                [class*="spinner"],
                [class*="ajax"],
                [id*="ajax"],
                [class*="progress"],
                [id*="progress"],
                [class*="waiting"],
                [id*="waiting"],
                [class*="animat"],
                [id*="animat"] {
                    display: none !important;
                    opacity: 0 !important;
                    visibility: hidden !important;
                    height: 0 !important;
                    width: 0 !important;
                    position: absolute !important;
                    z-index: -9999 !important;
                    pointer-events: none !important;
                }
            `;
            document.head.appendChild(style);
        } catch (e) {
            console.log('Error adding CSS:', e);
        }
    };
    
    // Execute immediately
    hideLoaders();
    
    // Also execute on DOMContentLoaded
    document.addEventListener('DOMContentLoaded', hideLoaders);
    
    // Also execute on load
    window.addEventListener('load', hideLoaders);
    
    // Execute multiple times with delays
    setTimeout(hideLoaders, 100);
    setTimeout(hideLoaders, 500);
    setTimeout(hideLoaders, 1000);
    setTimeout(hideLoaders, 2000);
    setTimeout(hideLoaders, 5000);
    
    // Execute periodically
    const safeSetInterval = function(callback, delay) {
        const originalSetInterval = window.setInterval;
        return originalSetInterval(callback, delay);
    };
    
    safeSetInterval(hideLoaders, 1000);
})();
