/* Modern Subjects Page Styling */

/* Modern Font Imports */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Baloo+Da+2:wght@400;500;600;700&display=swap');

/* Root Variables */
:root {
    --primary-color: #4361ee;
    --primary-light: #4895ef;
    --primary-dark: #3f37c9;
    --secondary-color: #4cc9f0;
    --accent-color: #f72585;
    --success-color: #4ade80;
    --warning-color: #fbbf24;
    --danger-color: #f43f5e;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-hover: 0 20px 30px -10px rgba(0, 0, 0, 0.15);
    --border-radius-sm: 0.25rem;
    --border-radius: 0.5rem;
    --border-radius-md: 0.75rem;
    --border-radius-lg: 1rem;
    --transition-fast: 0.15s ease;
    --transition: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Modern Typography */
body, html, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, select, textarea, label, li, table, th, td {
    font-family: 'Hind Siliguri', 'Noto Sans Bengali', 'Baloo Da 2', sans-serif !important;
    letter-spacing: -0.01em;
}

/* Improved Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: var(--gray-800);
    line-height: 1.3;
}

h2 {
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
}

p {
    color: var(--gray-600);
    line-height: 1.6;
}

.text-muted {
    color: var(--gray-500) !important;
}

/* Modern Sidebar */
.sidebar {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    box-shadow: var(--shadow);
}

.sidebar .nav-link {
    border-radius: var(--border-radius);
    margin: 0.25rem 0.75rem;
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-sm);
}

.sidebar .nav-link i {
    width: 24px;
    text-align: center;
    margin-right: 8px;
}

/* Modern Cards */
.card {
    border: none;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-3px);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid var(--gray-200);
    padding: 1rem 1.25rem;
}

.card-header.bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%) !important;
}

.card-body {
    padding: 1.5rem;
}

/* Modern Table */
.table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
}

.table th {
    background-color: var(--gray-100);
    color: var(--gray-700);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    padding: 1rem;
    border-top: none;
    border-bottom: 2px solid var(--gray-200);
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--gray-200);
    color: var(--gray-700);
}

.table tbody tr {
    transition: var(--transition-fast);
}

.table tbody tr:hover {
    background-color: var(--gray-100);
    transform: scale(1.01);
}

.table-responsive {
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow);
}

/* Modern Buttons */
.btn {
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: var(--transition);
    border: none;
    box-shadow: var(--shadow-sm);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
}

.btn-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
    color: #fff;
}

.btn-info {
    background: linear-gradient(135deg, #0ea5e9 0%, #38bdf8 100%);
    color: #fff;
}

.btn-outline-primary {
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-outline-warning {
    border: 1px solid var(--warning-color);
    color: var(--warning-color);
}

.btn-outline-warning:hover {
    background-color: var(--warning-color);
    color: white;
}

.btn-outline-success {
    border: 1px solid #10b981;
    color: #10b981;
}

.btn-outline-success:hover {
    background-color: #10b981;
    color: white;
}

/* Action Buttons */
.action-btn {
    width: 36px;
    height: 36px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0 3px;
    padding: 0;
    transition: var(--transition);
}

.action-btn:hover {
    transform: translateY(-3px) scale(1.1);
}

/* Search Container */
.search-container {
    background: white;
    border-radius: var(--border-radius-md);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow);
}

.search-container .form-control,
.search-container .form-select {
    border-radius: var(--border-radius);
    padding: 0.65rem 1rem;
    border: 1px solid var(--gray-300);
    transition: var(--transition-fast);
}

.search-container .form-control:focus,
.search-container .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

.search-container .input-group-text {
    background-color: white;
    border-right: none;
    border-color: var(--gray-300);
}

.search-container .form-control {
    border-left: none;
}

/* Badges */
.badge {
    padding: 0.35em 0.65em;
    font-weight: 500;
    border-radius: var(--border-radius-sm);
}

.badge.bg-success {
    background: linear-gradient(135deg, #10b981 0%, #34d399 100%) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #ef4444 0%, #f87171 100%) !important;
}

.badge.bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%) !important;
}

/* Subject Cards */
.subject-card {
    border-left: 4px solid;
    transition: all 0.3s ease;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.subject-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.subject-card.active {
    border-left-color: var(--success-color);
}

.subject-card.inactive {
    border-left-color: var(--danger-color);
}

/* Card Hover Effect */
.card-hover {
    transition: transform 0.3s, box-shadow 0.3s;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

/* Quick Links Cards */
.card-hover .fas {
    transition: var(--transition);
}

.card-hover:hover .fas {
    transform: scale(1.1);
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.alert-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: #065f46;
}

.alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: #991b1b;
}

.alert-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: #92400e;
}

/* Modal Styling */
.modal-content {
    border: none;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.modal-header {
    border-bottom: 1px solid var(--gray-200);
    padding: 1.25rem 1.5rem;
}

.modal-header.bg-success {
    background: linear-gradient(135deg, #10b981 0%, #34d399 100%) !important;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid var(--gray-200);
    padding: 1.25rem 1.5rem;
}

/* Form Controls */
.form-control, .form-select {
    border-radius: var(--border-radius);
    padding: 0.65rem 1rem;
    border: 1px solid var(--gray-300);
    transition: var(--transition-fast);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

.form-label {
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Loading Animation */
@keyframes shimmer {
    0% {
        background-position: -1000px 0;
    }
    100% {
        background-position: 1000px 0;
    }
}

.loading {
    animation: shimmer 2s infinite linear;
    background: linear-gradient(to right, var(--gray-100) 4%, var(--gray-200) 25%, var(--gray-100) 36%);
    background-size: 1000px 100%;
}
