<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Function to check and fix column names
function checkAndFixColumnNames($conn) {
    $results = [];
    
    try {
        // Check payments table columns
        $paymentsExists = $conn->query("SHOW TABLES LIKE 'payments'")->num_rows > 0;
        $feePaymentsExists = $conn->query("SHOW TABLES LIKE 'fee_payments'")->num_rows > 0;
        
        $results['tables_exist'] = [
            'payments' => $paymentsExists,
            'fee_payments' => $feePaymentsExists
        ];
        
        // Check column names in payments table
        if ($paymentsExists) {
            $paymentsColumns = [];
            $columnsResult = $conn->query("SHOW COLUMNS FROM payments");
            while ($column = $columnsResult->fetch_assoc()) {
                $paymentsColumns[] = $column['Field'];
            }
            $results['payments_columns'] = $paymentsColumns;
            
            // Check if receipt_number exists instead of receipt_no
            $hasReceiptNumber = in_array('receipt_number', $paymentsColumns);
            $hasReceiptNo = in_array('receipt_no', $paymentsColumns);
            
            $results['payments_receipt_column'] = [
                'has_receipt_number' => $hasReceiptNumber,
                'has_receipt_no' => $hasReceiptNo
            ];
        }
        
        // Check column names in fee_payments table
        if ($feePaymentsExists) {
            $feePaymentsColumns = [];
            $columnsResult = $conn->query("SHOW COLUMNS FROM fee_payments");
            while ($column = $columnsResult->fetch_assoc()) {
                $feePaymentsColumns[] = $column['Field'];
            }
            $results['fee_payments_columns'] = $feePaymentsColumns;
            
            // Check if receipt_number exists instead of receipt_no
            $hasReceiptNumber = in_array('receipt_number', $feePaymentsColumns);
            $hasReceiptNo = in_array('receipt_no', $feePaymentsColumns);
            
            $results['fee_payments_receipt_column'] = [
                'has_receipt_number' => $hasReceiptNumber,
                'has_receipt_no' => $hasReceiptNo
            ];
        }
        
        return $results;
        
    } catch (Exception $e) {
        return [
            'error' => $e->getMessage()
        ];
    }
}

// Function to fix column names
function fixColumnNames($conn) {
    try {
        $conn->begin_transaction();
        $fixedColumns = [];
        
        // Fix payments table
        $paymentsExists = $conn->query("SHOW TABLES LIKE 'payments'")->num_rows > 0;
        if ($paymentsExists) {
            // Check if receipt_number column exists
            $checkColumn = $conn->query("SHOW COLUMNS FROM payments LIKE 'receipt_number'");
            if ($checkColumn->num_rows > 0) {
                // Rename receipt_number to receipt_no
                $renameQuery = "ALTER TABLE payments CHANGE COLUMN receipt_number receipt_no VARCHAR(100)";
                if ($conn->query($renameQuery)) {
                    $fixedColumns[] = "payments.receipt_number → receipt_no";
                }
            }
        }
        
        // Fix fee_payments table
        $feePaymentsExists = $conn->query("SHOW TABLES LIKE 'fee_payments'")->num_rows > 0;
        if ($feePaymentsExists) {
            // Check if receipt_number column exists
            $checkColumn = $conn->query("SHOW COLUMNS FROM fee_payments LIKE 'receipt_number'");
            if ($checkColumn->num_rows > 0) {
                // Rename receipt_number to receipt_no
                $renameQuery = "ALTER TABLE fee_payments CHANGE COLUMN receipt_number receipt_no VARCHAR(100)";
                if ($conn->query($renameQuery)) {
                    $fixedColumns[] = "fee_payments.receipt_number → receipt_no";
                }
            }
        }
        
        $conn->commit();
        
        return [
            'success' => true,
            'fixed_columns' => $fixedColumns
        ];
        
    } catch (Exception $e) {
        $conn->rollback();
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// Function to standardize all payment tables
function standardizePaymentTables($conn) {
    try {
        $conn->begin_transaction();
        $actions = [];
        
        // Ensure fee_payments table has the correct structure
        $createFeePaymentsTable = "CREATE TABLE IF NOT EXISTS fee_payments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            fee_id INT NOT NULL,
            student_id INT,
            amount DECIMAL(10,2) NOT NULL,
            payment_date DATE NOT NULL,
            payment_method VARCHAR(50) NOT NULL DEFAULT 'cash',
            receipt_no VARCHAR(50) DEFAULT NULL,
            transaction_id VARCHAR(100) DEFAULT NULL,
            notes TEXT DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (fee_id) REFERENCES fees(id) ON DELETE CASCADE
        )";
        
        if ($conn->query($createFeePaymentsTable)) {
            $actions[] = "fee_payments table structure standardized";
        }
        
        // Check if payments table exists and migrate data
        $paymentsExists = $conn->query("SHOW TABLES LIKE 'payments'")->num_rows > 0;
        if ($paymentsExists) {
            // Get data from payments table
            $paymentsQuery = "SELECT * FROM payments";
            $paymentsResult = $conn->query($paymentsQuery);
            
            $migratedCount = 0;
            while ($payment = $paymentsResult->fetch_assoc()) {
                // Check if this payment already exists in fee_payments
                $checkExisting = "SELECT id FROM fee_payments WHERE fee_id = ? AND amount = ? AND payment_date = ?";
                $checkStmt = $conn->prepare($checkExisting);
                $checkStmt->bind_param('ids', $payment['fee_id'], $payment['amount'], $payment['payment_date']);
                $checkStmt->execute();
                
                if ($checkStmt->get_result()->num_rows == 0) {
                    // Insert into fee_payments
                    $insertQuery = "INSERT INTO fee_payments (fee_id, student_id, amount, payment_date, payment_method, receipt_no, transaction_id, notes, created_at) 
                                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    $insertStmt = $conn->prepare($insertQuery);
                    
                    // Handle different column names
                    $receiptNo = $payment['receipt_no'] ?? $payment['receipt_number'] ?? null;
                    $transactionId = $payment['transaction_id'] ?? null;
                    $notes = $payment['notes'] ?? null;
                    $createdAt = $payment['created_at'] ?? date('Y-m-d H:i:s');
                    
                    $insertStmt->bind_param('iidsssss', 
                        $payment['fee_id'],
                        $payment['student_id'] ?? null,
                        $payment['amount'],
                        $payment['payment_date'],
                        $payment['payment_method'] ?? 'cash',
                        $receiptNo,
                        $transactionId,
                        $notes,
                        $createdAt
                    );
                    
                    if ($insertStmt->execute()) {
                        $migratedCount++;
                    }
                }
            }
            
            $actions[] = "$migratedCount payments migrated to fee_payments table";
        }
        
        $conn->commit();
        
        return [
            'success' => true,
            'actions' => $actions
        ];
        
    } catch (Exception $e) {
        $conn->rollback();
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// Handle form submissions
$checkResults = null;
$fixResults = null;
$standardizeResults = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['check_columns'])) {
        $checkResults = checkAndFixColumnNames($conn);
    } elseif (isset($_POST['fix_columns'])) {
        $fixResults = fixColumnNames($conn);
    } elseif (isset($_POST['standardize_tables'])) {
        $standardizeResults = standardizePaymentTables($conn);
    }
}

// Get current status
$currentStatus = checkAndFixColumnNames($conn);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Column Names ঠিক করুন</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: "Hind Siliguri", sans-serif;
        }
        
        .status-card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .column-correct {
            background-color: #d1e7dd;
            border-left: 4px solid #198754;
        }
        
        .column-incorrect {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        
        .column-mixed {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4 bg-white p-3 rounded shadow-sm">
                    <div>
                        <h2 class="mb-0">
                            <i class="fas fa-columns text-primary me-2"></i>
                            Column Names ঠিক করুন
                        </h2>
                        <small class="text-muted">Database column names এর inconsistency ঠিক করুন</small>
                    </div>
                    <div>
                        <a href="payment_system_checker.php" class="btn btn-secondary me-2">
                            <i class="fas fa-arrow-left me-1"></i> System Checker
                        </a>
                        <a href="fee_management.php" class="btn btn-primary">
                            <i class="fas fa-money-bill-wave me-1"></i> ফি ম্যানেজমেন্ট
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Status -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">বর্তমান অবস্থা</h5>
                    </div>
                    <div class="card-body">
                        <?php if (isset($currentStatus['error'])): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                <?php echo htmlspecialchars($currentStatus['error']); ?>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <!-- Payments Table -->
                                <?php if ($currentStatus['tables_exist']['payments']): ?>
                                    <div class="col-md-6">
                                        <div class="card status-card <?php
                                            $paymentsReceipt = $currentStatus['payments_receipt_column'];
                                            if ($paymentsReceipt['has_receipt_no'] && !$paymentsReceipt['has_receipt_number']) {
                                                echo 'column-correct';
                                            } elseif (!$paymentsReceipt['has_receipt_no'] && $paymentsReceipt['has_receipt_number']) {
                                                echo 'column-incorrect';
                                            } else {
                                                echo 'column-mixed';
                                            }
                                        ?>">
                                            <div class="card-body">
                                                <h6><i class="fas fa-database me-2"></i>Payments Table</h6>
                                                <p class="mb-1">
                                                    <strong>receipt_no:</strong>
                                                    <span class="badge <?php echo $paymentsReceipt['has_receipt_no'] ? 'bg-success' : 'bg-danger'; ?>">
                                                        <?php echo $paymentsReceipt['has_receipt_no'] ? 'আছে' : 'নেই'; ?>
                                                    </span>
                                                </p>
                                                <p class="mb-0">
                                                    <strong>receipt_number:</strong>
                                                    <span class="badge <?php echo $paymentsReceipt['has_receipt_number'] ? 'bg-warning' : 'bg-success'; ?>">
                                                        <?php echo $paymentsReceipt['has_receipt_number'] ? 'আছে (ভুল)' : 'নেই (সঠিক)'; ?>
                                                    </span>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Fee Payments Table -->
                                <?php if ($currentStatus['tables_exist']['fee_payments']): ?>
                                    <div class="col-md-6">
                                        <div class="card status-card <?php
                                            $feePaymentsReceipt = $currentStatus['fee_payments_receipt_column'];
                                            if ($feePaymentsReceipt['has_receipt_no'] && !$feePaymentsReceipt['has_receipt_number']) {
                                                echo 'column-correct';
                                            } elseif (!$feePaymentsReceipt['has_receipt_no'] && $feePaymentsReceipt['has_receipt_number']) {
                                                echo 'column-incorrect';
                                            } else {
                                                echo 'column-mixed';
                                            }
                                        ?>">
                                            <div class="card-body">
                                                <h6><i class="fas fa-receipt me-2"></i>Fee Payments Table</h6>
                                                <p class="mb-1">
                                                    <strong>receipt_no:</strong>
                                                    <span class="badge <?php echo $feePaymentsReceipt['has_receipt_no'] ? 'bg-success' : 'bg-danger'; ?>">
                                                        <?php echo $feePaymentsReceipt['has_receipt_no'] ? 'আছে' : 'নেই'; ?>
                                                    </span>
                                                </p>
                                                <p class="mb-0">
                                                    <strong>receipt_number:</strong>
                                                    <span class="badge <?php echo $feePaymentsReceipt['has_receipt_number'] ? 'bg-warning' : 'bg-success'; ?>">
                                                        <?php echo $feePaymentsReceipt['has_receipt_number'] ? 'আছে (ভুল)' : 'নেই (সঠিক)'; ?>
                                                    </span>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <form method="POST" style="display: inline;">
                            <button type="submit" name="check_columns" class="btn btn-info btn-lg me-3">
                                <i class="fas fa-search me-2"></i>
                                Columns চেক করুন
                            </button>
                        </form>
                        <form method="POST" style="display: inline;">
                            <button type="submit" name="fix_columns" class="btn btn-warning btn-lg me-3"
                                    onclick="return confirm('আপনি কি নিশ্চিত যে column names ঠিক করতে চান?')">
                                <i class="fas fa-wrench me-2"></i>
                                Column Names ঠিক করুন
                            </button>
                        </form>
                        <form method="POST" style="display: inline;">
                            <button type="submit" name="standardize_tables" class="btn btn-success btn-lg"
                                    onclick="return confirm('আপনি কি নিশ্চিত যে সব tables standardize করতে চান?')">
                                <i class="fas fa-cogs me-2"></i>
                                Tables Standardize করুন
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Display -->
        <?php if ($checkResults): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Column Check Results</h5>
                        </div>
                        <div class="card-body">
                            <pre><?php echo json_encode($checkResults, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></pre>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($fixResults): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <?php if ($fixResults['success']): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>সফল!</strong> নিম্নলিখিত columns ঠিক করা হয়েছে:
                            <ul class="mb-0 mt-2">
                                <?php foreach ($fixResults['fixed_columns'] as $column): ?>
                                    <li><?php echo htmlspecialchars($column); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong>ত্রুটি!</strong> <?php echo htmlspecialchars($fixResults['error']); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($standardizeResults): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <?php if ($standardizeResults['success']): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>সফল!</strong> নিম্নলিখিত actions সম্পন্ন হয়েছে:
                            <ul class="mb-0 mt-2">
                                <?php foreach ($standardizeResults['actions'] as $action): ?>
                                    <li><?php echo htmlspecialchars($action); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong>ত্রুটি!</strong> <?php echo htmlspecialchars($standardizeResults['error']); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Instructions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">নির্দেশনা</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>সমস্যা সমাধানের ধাপ:</h6>
                            <ol>
                                <li><strong>Columns চেক করুন</strong> - বর্তমান column names দেখুন</li>
                                <li><strong>Column Names ঠিক করুন</strong> - receipt_number কে receipt_no তে পরিবর্তন করুন</li>
                                <li><strong>Tables Standardize করুন</strong> - সব tables একই structure এ আনুন</li>
                                <li><strong>Fee Management</strong> এ গিয়ে test করুন</li>
                            </ol>
                        </div>

                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>সতর্কতা:</h6>
                            <ul class="mb-0">
                                <li>Database backup নিন operation এর আগে</li>
                                <li>Production environment এ সাবধানে করুন</li>
                                <li>একবারে একটি action করুন</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
