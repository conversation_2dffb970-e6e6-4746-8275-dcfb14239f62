<?php
session_start();
require_once '../includes/dbh.inc.php';
require_once '../includes/bkash_config.php';
require_once '../includes/bkash_functions.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Create bKash payments table if it doesn't exist
createBkashPaymentsTable($conn);

// Get date range for reports
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01'); // First day of current month
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-t'); // Last day of current month

// Get payment statistics
$statsQuery = "SELECT 
                COUNT(*) as total_payments,
                SUM(amount) as total_amount,
                COUNT(CASE WHEN status = 'Completed' THEN 1 END) as completed_payments,
                SUM(CASE WHEN status = 'Completed' THEN amount ELSE 0 END) as completed_amount,
                COUNT(CASE WHEN status != 'Completed' THEN 1 END) as failed_payments
              FROM bkash_payments
              WHERE DATE(payment_date) BETWEEN ? AND ?";
$stmt = $conn->prepare($statsQuery);
$stmt->bind_param('ss', $startDate, $endDate);
$stmt->execute();
$statsResult = $stmt->get_result();
$stats = $statsResult->fetch_assoc();

// Get daily payment data for chart
$dailyDataQuery = "SELECT 
                    DATE(payment_date) as payment_day,
                    COUNT(*) as total_count,
                    SUM(CASE WHEN status = 'Completed' THEN amount ELSE 0 END) as daily_amount
                  FROM bkash_payments
                  WHERE DATE(payment_date) BETWEEN ? AND ?
                  GROUP BY DATE(payment_date)
                  ORDER BY payment_day";
$stmt = $conn->prepare($dailyDataQuery);
$stmt->bind_param('ss', $startDate, $endDate);
$stmt->execute();
$dailyDataResult = $stmt->get_result();

$dates = [];
$amounts = [];
$counts = [];

while ($row = $dailyDataResult->fetch_assoc()) {
    $dates[] = date('d M', strtotime($row['payment_day']));
    $amounts[] = $row['daily_amount'];
    $counts[] = $row['total_count'];
}

// Get payment status distribution
$statusQuery = "SELECT 
                status,
                COUNT(*) as count,
                SUM(amount) as total_amount
              FROM bkash_payments
              WHERE DATE(payment_date) BETWEEN ? AND ?
              GROUP BY status";
$stmt = $conn->prepare($statusQuery);
$stmt->bind_param('ss', $startDate, $endDate);
$stmt->execute();
$statusResult = $stmt->get_result();

$statusLabels = [];
$statusCounts = [];
$statusColors = [];

while ($row = $statusResult->fetch_assoc()) {
    $statusLabels[] = $row['status'];
    $statusCounts[] = $row['count'];
    
    // Assign colors based on status
    if ($row['status'] === 'Completed') {
        $statusColors[] = '#198754'; // Green
    } else if ($row['status'] === 'Initiated') {
        $statusColors[] = '#ffc107'; // Yellow
    } else {
        $statusColors[] = '#dc3545'; // Red
    }
}

// Get top students by payment amount
$topStudentsQuery = "SELECT 
                      s.first_name, s.last_name, s.student_id as roll, c.class_name,
                      COUNT(bp.id) as payment_count,
                      SUM(bp.amount) as total_amount
                    FROM bkash_payments bp
                    JOIN fees f ON bp.fee_id = f.id
                    JOIN students s ON f.student_id = s.id
                    JOIN classes c ON s.class_id = c.id
                    WHERE bp.status = 'Completed' AND DATE(bp.payment_date) BETWEEN ? AND ?
                    GROUP BY s.id
                    ORDER BY total_amount DESC
                    LIMIT 5";
$stmt = $conn->prepare($topStudentsQuery);
$stmt->bind_param('ss', $startDate, $endDate);
$stmt->execute();
$topStudentsResult = $stmt->get_result();

// Include header
include_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Include sidebar -->
        <?php include_once 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-chart-bar me-2"></i> বিকাশ পেমেন্ট রিপোর্ট</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="bkash_dashboard.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-tachometer-alt me-1"></i> ড্যাশবোর্ড
                        </a>
                        <a href="bkash_payment_list.php" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-list me-1"></i> পেমেন্ট তালিকা
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Date Range Filter -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-calendar-alt me-2"></i> তারিখ নির্বাচন করুন</h5>
                </div>
                <div class="card-body">
                    <form action="" method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">শুরুর তারিখ</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="<?= htmlspecialchars($startDate) ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">শেষের তারিখ</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="<?= htmlspecialchars($endDate) ?>">
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <div class="d-grid gap-2 w-100">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-1"></i> ফিল্টার করুন
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card border-primary h-100">
                        <div class="card-body text-center">
                            <div class="display-4 text-primary mb-2">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <h5 class="card-title">মোট পেমেন্ট</h5>
                            <h2 class="display-6 text-primary"><?= $stats['total_payments'] ?? 0 ?></h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card border-success h-100">
                        <div class="card-body text-center">
                            <div class="display-4 text-success mb-2">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <h5 class="card-title">সফল পেমেন্ট</h5>
                            <h2 class="display-6 text-success"><?= $stats['completed_payments'] ?? 0 ?></h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card border-danger h-100">
                        <div class="card-body text-center">
                            <div class="display-4 text-danger mb-2">
                                <i class="fas fa-times-circle"></i>
                            </div>
                            <h5 class="card-title">ব্যর্থ পেমেন্ট</h5>
                            <h2 class="display-6 text-danger"><?= $stats['failed_payments'] ?? 0 ?></h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card border-info h-100">
                        <div class="card-body text-center">
                            <div class="display-4 text-info mb-2">
                                <i class="fas fa-hand-holding-usd"></i>
                            </div>
                            <h5 class="card-title">মোট পরিমাণ</h5>
                            <h2 class="display-6 text-info">৳ <?= number_format($stats['completed_amount'] ?? 0, 2) ?></h2>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Charts -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-chart-line me-2"></i> দৈনিক পেমেন্ট</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="dailyPaymentsChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-chart-pie me-2"></i> পেমেন্ট স্ট্যাটাস</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="paymentStatusChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Top Students -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-trophy me-2"></i> সর্বোচ্চ পেমেন্টকারী শিক্ষার্থী</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover table-striped">
                                    <thead class="table-light">
                                        <tr>
                                            <th>#</th>
                                            <th>শিক্ষার্থীর নাম</th>
                                            <th>রোল</th>
                                            <th>শ্রেণী</th>
                                            <th>পেমেন্ট সংখ্যা</th>
                                            <th>মোট পরিমাণ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if ($topStudentsResult && $topStudentsResult->num_rows > 0): ?>
                                            <?php $rank = 1; ?>
                                            <?php while ($student = $topStudentsResult->fetch_assoc()): ?>
                                                <tr>
                                                    <td><?= $rank++ ?></td>
                                                    <td><?= htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) ?></td>
                                                    <td><?= $student['roll'] ?></td>
                                                    <td><?= htmlspecialchars($student['class_name']) ?></td>
                                                    <td><?= $student['payment_count'] ?></td>
                                                    <td>৳ <?= number_format($student['total_amount'], 2) ?></td>
                                                </tr>
                                            <?php endwhile; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="6" class="text-center">কোন পেমেন্ট রেকর্ড পাওয়া যায়নি</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Export Options -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-file-export me-2"></i> রিপোর্ট এক্সপোর্ট</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <a href="bkash_export_pdf.php?start_date=<?= urlencode($startDate) ?>&end_date=<?= urlencode($endDate) ?>" class="btn btn-danger w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                                        <i class="fas fa-file-pdf fa-2x mb-2"></i>
                                        <span>PDF এক্সপোর্ট</span>
                                    </a>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <a href="bkash_export_excel.php?start_date=<?= urlencode($startDate) ?>&end_date=<?= urlencode($endDate) ?>" class="btn btn-success w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                                        <i class="fas fa-file-excel fa-2x mb-2"></i>
                                        <span>Excel এক্সপোর্ট</span>
                                    </a>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <a href="bkash_print_report.php?start_date=<?= urlencode($startDate) ?>&end_date=<?= urlencode($endDate) ?>" class="btn btn-primary w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                                        <i class="fas fa-print fa-2x mb-2"></i>
                                        <span>প্রিন্ট রিপোর্ট</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Daily Payments Chart
        const dailyPaymentsCtx = document.getElementById('dailyPaymentsChart').getContext('2d');
        const dailyPaymentsChart = new Chart(dailyPaymentsCtx, {
            type: 'line',
            data: {
                labels: <?= json_encode($dates) ?>,
                datasets: [
                    {
                        label: 'দৈনিক পেমেন্ট পরিমাণ (৳)',
                        data: <?= json_encode($amounts) ?>,
                        backgroundColor: 'rgba(25, 135, 84, 0.2)',
                        borderColor: 'rgba(25, 135, 84, 1)',
                        borderWidth: 2,
                        tension: 0.3,
                        yAxisID: 'y'
                    },
                    {
                        label: 'পেমেন্ট সংখ্যা',
                        data: <?= json_encode($counts) ?>,
                        backgroundColor: 'rgba(13, 110, 253, 0.2)',
                        borderColor: 'rgba(13, 110, 253, 1)',
                        borderWidth: 2,
                        tension: 0.3,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'পরিমাণ (৳)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false,
                        },
                        title: {
                            display: true,
                            text: 'পেমেন্ট সংখ্যা'
                        }
                    }
                }
            }
        });
        
        // Payment Status Chart
        const paymentStatusCtx = document.getElementById('paymentStatusChart').getContext('2d');
        const paymentStatusChart = new Chart(paymentStatusCtx, {
            type: 'doughnut',
            data: {
                labels: <?= json_encode($statusLabels) ?>,
                datasets: [{
                    data: <?= json_encode($statusCounts) ?>,
                    backgroundColor: <?= json_encode($statusColors) ?>,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
    });
</script>

<?php
// Include footer
include_once 'includes/footer.php';
?>
