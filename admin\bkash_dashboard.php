<?php
session_start();
require_once '../includes/dbh.inc.php';
require_once '../includes/bkash_config.php';
require_once '../includes/bkash_functions.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Create bKash payments table if it doesn't exist
createBkashPaymentsTable($conn);

// Get bKash payment statistics
$statsQuery = "SELECT
                COUNT(*) as total_payments,
                SUM(amount) as total_amount,
                COUNT(CASE WHEN status = 'Completed' THEN 1 END) as completed_payments,
                SUM(CASE WHEN status = 'Completed' THEN amount ELSE 0 END) as completed_amount,
                COUNT(CASE WHEN status != 'Completed' THEN 1 END) as failed_payments
              FROM bkash_payments";
$statsResult = $conn->query($statsQuery);
$stats = $statsResult->fetch_assoc();

// Get recent bKash payments
$recentPaymentsQuery = "SELECT bp.*, f.fee_type, s.first_name, s.last_name, s.student_id as roll
                        FROM bkash_payments bp
                        JOIN fees f ON bp.fee_id = f.id
                        JOIN students s ON f.student_id = s.id
                        ORDER BY bp.payment_date DESC
                        LIMIT 10";
$recentPayments = $conn->query($recentPaymentsQuery);

// Include header
include_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Include sidebar -->
        <?php include_once 'includes/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-mobile-alt me-2"></i> বিকাশ পেমেন্ট ড্যাশবোর্ড</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="fees.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i> ফি পেজে ফিরে যান
                        </a>
                    </div>
                </div>
            </div>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i> <?= $_SESSION['success'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i> <?= $_SESSION['error'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-bolt me-2"></i> দ্রুত অ্যাকশন</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <a href="bkash_test.php" class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                                        <i class="fas fa-cogs fa-2x mb-2"></i>
                                        <span>বিকাশ সেটিংস</span>
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="bkash_init.php" class="btn btn-outline-success w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                                        <i class="fas fa-database fa-2x mb-2"></i>
                                        <span>ডাটাবেস সেটআপ</span>
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="bkash_payment_list.php" class="btn btn-outline-info w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                                        <i class="fas fa-list-alt fa-2x mb-2"></i>
                                        <span>পেমেন্ট তালিকা</span>
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="bkash_reports.php" class="btn btn-outline-warning w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                                        <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                        <span>পেমেন্ট রিপোর্ট</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-3 mb-4">
                    <div class="card border-primary h-100">
                        <div class="card-body text-center">
                            <div class="display-4 text-primary mb-2">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <h5 class="card-title">মোট পেমেন্ট</h5>
                            <h2 class="display-6 text-primary"><?= $stats['total_payments'] ?? 0 ?></h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card border-success h-100">
                        <div class="card-body text-center">
                            <div class="display-4 text-success mb-2">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <h5 class="card-title">সফল পেমেন্ট</h5>
                            <h2 class="display-6 text-success"><?= $stats['completed_payments'] ?? 0 ?></h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card border-danger h-100">
                        <div class="card-body text-center">
                            <div class="display-4 text-danger mb-2">
                                <i class="fas fa-times-circle"></i>
                            </div>
                            <h5 class="card-title">ব্যর্থ পেমেন্ট</h5>
                            <h2 class="display-6 text-danger"><?= $stats['failed_payments'] ?? 0 ?></h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card border-info h-100">
                        <div class="card-body text-center">
                            <div class="display-4 text-info mb-2">
                                <i class="fas fa-hand-holding-usd"></i>
                            </div>
                            <h5 class="card-title">মোট পরিমাণ</h5>
                            <h2 class="display-6 text-info">৳ <?= number_format($stats['completed_amount'] ?? 0, 2) ?></h2>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Configuration Status -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-cog me-2"></i> কনফিগারেশন স্ট্যাটাস</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-bordered">
                                <tr>
                                    <th width="40%">মোড</th>
                                    <td>
                                        <?php if (BKASH_SANDBOX): ?>
                                            <span class="badge bg-warning">স্যান্ডবক্স (টেস্ট)</span>
                                        <?php else: ?>
                                            <span class="badge bg-success">প্রোডাকশন (লাইভ)</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>API ভার্সন</th>
                                    <td><?= BKASH_VERSION ?></td>
                                </tr>
                                <tr>
                                    <th>APP KEY</th>
                                    <td>
                                        <?php if (BKASH_APP_KEY === 'your_app_key'): ?>
                                            <span class="badge bg-danger">কনফিগার করা হয়নি</span>
                                        <?php else: ?>
                                            <span class="badge bg-success">কনফিগার করা হয়েছে</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>APP SECRET</th>
                                    <td>
                                        <?php if (BKASH_APP_SECRET === 'your_app_secret'): ?>
                                            <span class="badge bg-danger">কনফিগার করা হয়নি</span>
                                        <?php else: ?>
                                            <span class="badge bg-success">কনফিগার করা হয়েছে</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>ডাটাবেস টেবিল</th>
                                    <td>
                                        <?php
                                        // Check if bkash_payments table exists
                                        $checkTableQuery = "SHOW TABLES LIKE 'bkash_payments'";
                                        $tableExists = $conn->query($checkTableQuery)->num_rows > 0;
                                        ?>

                                        <?php if ($tableExists): ?>
                                            <span class="badge bg-success">তৈরি করা হয়েছে</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">তৈরি করা হয়নি</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>

                            <div class="mt-3">
                                <a href="bkash_test.php" class="btn btn-primary">
                                    <i class="fas fa-cogs me-2"></i> কনফিগারেশন পরিবর্তন করুন
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-tools me-2"></i> বিকাশ টুলস</h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group">
                                <a href="bkash_payment_form.php" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-money-bill-wave me-2 text-success"></i> নতুন পেমেন্ট তৈরি করুন
                                    </div>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                                <a href="bkash_payment_list.php" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-list-alt me-2 text-primary"></i> পেমেন্ট তালিকা দেখুন
                                    </div>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                                <a href="bkash_reports.php" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-chart-bar me-2 text-info"></i> পেমেন্ট রিপোর্ট দেখুন
                                    </div>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                                <a href="bkash_test_token.php" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-key me-2 text-warning"></i> টোকেন টেস্ট করুন
                                    </div>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                                <a href="bkash_init.php" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-database me-2 text-danger"></i> ডাটাবেস রিসেট করুন
                                    </div>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Payments -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0"><i class="fas fa-history me-2"></i> সাম্প্রতিক পেমেন্ট</h5>
                            <a href="bkash_payment_list.php" class="btn btn-sm btn-light">
                                <i class="fas fa-list me-1"></i> সব দেখুন
                            </a>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover table-striped">
                                    <thead class="table-light">
                                        <tr>
                                            <th>আইডি</th>
                                            <th>শিক্ষার্থী</th>
                                            <th>ফি টাইপ</th>
                                            <th>পরিমাণ</th>
                                            <th>ট্রানজেকশন আইডি</th>
                                            <th>তারিখ</th>
                                            <th>স্ট্যাটাস</th>
                                            <th>অ্যাকশন</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if ($recentPayments && $recentPayments->num_rows > 0): ?>
                                            <?php while ($payment = $recentPayments->fetch_assoc()): ?>
                                                <tr>
                                                    <td><?= $payment['id'] ?></td>
                                                    <td><?= htmlspecialchars($payment['first_name'] . ' ' . $payment['last_name']) ?> (<?= $payment['roll'] ?>)</td>
                                                    <td><?= htmlspecialchars($payment['fee_type']) ?></td>
                                                    <td>৳ <?= number_format($payment['amount'], 2) ?></td>
                                                    <td>
                                                        <?php if (!empty($payment['trx_id'])): ?>
                                                            <?= $payment['trx_id'] ?>
                                                        <?php else: ?>
                                                            <span class="badge bg-secondary">N/A</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?= date('d/m/Y H:i', strtotime($payment['payment_date'])) ?></td>
                                                    <td>
                                                        <?php if ($payment['status'] === 'Completed'): ?>
                                                            <span class="badge bg-success">সফল</span>
                                                        <?php elseif ($payment['status'] === 'Initiated'): ?>
                                                            <span class="badge bg-warning text-dark">প্রক্রিয়াধীন</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-danger">ব্যর্থ</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <a href="bkash_payment_details.php?id=<?= $payment['id'] ?>" class="btn btn-sm btn-info">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endwhile; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="8" class="text-center">কোন পেমেন্ট রেকর্ড পাওয়া যায়নি</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- bKash Documentation -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-book me-2"></i> বিকাশ পেমেন্ট গেটওয়ে ডকুমেন্টেশন</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <h6 class="card-title mb-0">বিকাশ পেমেন্ট গেটওয়ে কি?</h6>
                                        </div>
                                        <div class="card-body">
                                            <p>বিকাশ পেমেন্ট গেটওয়ে হল একটি অনলাইন পেমেন্ট সিস্টেম যা আপনাকে আপনার ওয়েবসাইট বা অ্যাপে বিকাশ পেমেন্ট গ্রহণ করতে সাহায্য করে। এটি বাংলাদেশের সবচেয়ে জনপ্রিয় মোবাইল ফাইন্যান্সিয়াল সার্ভিস (MFS) প্রদানকারী বিকাশের একটি সমাধান।</p>
                                            <p>এই সিস্টেমের মাধ্যমে, আপনার শিক্ষার্থীরা সহজেই তাদের বিকাশ অ্যাকাউন্ট ব্যবহার করে ফি পরিশোধ করতে পারবেন।</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <h6 class="card-title mb-0">কিভাবে বিকাশ পেমেন্ট গেটওয়ে ব্যবহার করবেন?</h6>
                                        </div>
                                        <div class="card-body">
                                            <ol>
                                                <li>প্রথমে বিকাশ মার্চেন্ট অ্যাকাউন্ট তৈরি করুন</li>
                                                <li>বিকাশ পেমেন্ট গেটওয়ে API ক্রেডেনশিয়ালস সংগ্রহ করুন</li>
                                                <li>API ক্রেডেনশিয়ালস <code>includes/bkash_config.php</code> ফাইলে আপডেট করুন</li>
                                                <li>ফি পেজ থেকে বিকাশ পেমেন্ট বাটন ক্লিক করুন</li>
                                                <li>পেমেন্ট পরিমাণ এবং অন্যান্য তথ্য দিন</li>
                                                <li>বিকাশ পেমেন্ট প্রসেস সম্পন্ন করুন</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <h6 class="card-title mb-0">বিকাশ পেমেন্ট গেটওয়ে সেটআপ</h6>
                                        </div>
                                        <div class="card-body">
                                            <p>বিকাশ পেমেন্ট গেটওয়ে সেটআপ করতে নিম্নলিখিত পদক্ষেপগুলি অনুসরণ করুন:</p>
                                            <ol>
                                                <li><a href="https://www.bkash.com/business/payment/merchant" target="_blank">বিকাশ মার্চেন্ট অ্যাকাউন্ট</a> তৈরি করুন</li>
                                                <li><a href="https://developer.bkash.com/" target="_blank">বিকাশ ডেভেলপার পোর্টাল</a> থেকে API ক্রেডেনশিয়ালস সংগ্রহ করুন</li>
                                                <li><code>includes/bkash_config.php</code> ফাইলে API ক্রেডেনশিয়ালস আপডেট করুন</li>
                                                <li>বিকাশ টেস্ট পেজে গিয়ে কনফিগারেশন টেস্ট করুন</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <h6 class="card-title mb-0">গুরুত্বপূর্ণ লিংক</h6>
                                        </div>
                                        <div class="card-body">
                                            <ul class="list-group">
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    <a href="https://www.bkash.com/business/payment/merchant" target="_blank">বিকাশ মার্চেন্ট রেজিস্ট্রেশন</a>
                                                    <i class="fas fa-external-link-alt"></i>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    <a href="https://developer.bkash.com/" target="_blank">বিকাশ ডেভেলপার পোর্টাল</a>
                                                    <i class="fas fa-external-link-alt"></i>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    <a href="https://developer.bkash.com/docs/checkout-process-overview" target="_blank">বিকাশ API ডকুমেন্টেশন</a>
                                                    <i class="fas fa-external-link-alt"></i>
                                                </li>
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    <a href="https://developer.sandbox.bka.sh/" target="_blank">বিকাশ স্যান্ডবক্স পোর্টাল</a>
                                                    <i class="fas fa-external-link-alt"></i>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
