<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'includes/dbh.inc.php';

// Function to check if a table exists
function tableExists($conn, $tableName) {
    $result = $conn->query("SHOW TABLES LIKE '$tableName'");
    return $result && $result->num_rows > 0;
}

// Ensure connection is active
$conn = ensure_connection();

if ($conn) {
    // Check if certificates table exists
    if (!tableExists($conn, 'certificates')) {
        echo "<h2>Creating certificates table...</h2>";
        
        // Create the certificates table
        $sql = "CREATE TABLE certificates (
            id INT(11) NOT NULL AUTO_INCREMENT,
            student_id INT(11) NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            certificate_date DATE NOT NULL,
            issued_by VARCHAR(255) NOT NULL,
            certificate_type VARCHAR(50) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        )";
        
        if ($conn->query($sql)) {
            echo "<p>Certificates table created successfully!</p>";
        } else {
            echo "<p>Error creating certificates table: " . $conn->error . "</p>";
            exit;
        }
    } else {
        echo "<h2>Certificates table already exists.</h2>";
    }
    
    // Close the connection
    $conn->close();
    
    // Redirect to certificates page
    echo "<p>Redirecting to certificates page...</p>";
    echo "<script>
        setTimeout(function() {
            window.location.href = 'admin/certificates.php';
        }, 2000);
    </script>";
} else {
    echo "<h2>Error: Could not establish database connection.</h2>";
}
?>
