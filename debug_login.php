<?php
require_once 'includes/dbh.inc.php';

echo "<h2>Login Debugging Tool</h2>";

// Set test credentials - change these to test different logins
$username = "student";
$password = "password123";
$userType = "student";

echo "<h3>Testing login for:</h3>";
echo "<p>Username: $username</p>";
echo "<p>User Type: $userType</p>";

// Check if user exists
$sql = "SELECT * FROM users WHERE username=? AND user_type=?";
$stmt = $conn->prepare($sql);

if (!$stmt) {
    echo "<p style='color:red;'>Prepare failed: " . $conn->error . "</p>";
    exit;
}

$stmt->bind_param("ss", $username, $userType);

if (!$stmt->execute()) {
    echo "<p style='color:red;'>Execute failed: " . $stmt->error . "</p>";
    exit;
}

$result = $stmt->get_result();

if ($row = $result->fetch_assoc()) {
    echo "<p style='color:green;'>User found in database!</p>";
    echo "<p>User ID: " . $row['id'] . "</p>";
    echo "<p>Username: " . $row['username'] . "</p>";
    echo "<p>User Type: " . $row['user_type'] . "</p>";
    
    // Check password
    $pwdCheck = password_verify($password, $row['password']);
    
    if ($pwdCheck) {
        echo "<p style='color:green;'>Password is correct!</p>";
        
        // Check if student record exists (for student logins)
        if ($userType == 'student') {
            $studentCheck = "SELECT * FROM students WHERE user_id=?";
            $studentStmt = $conn->prepare($studentCheck);
            
            if (!$studentStmt) {
                echo "<p style='color:red;'>Student check prepare failed: " . $conn->error . "</p>";
            } else {
                $studentStmt->bind_param("i", $row['id']);
                $studentStmt->execute();
                $studentResult = $studentStmt->get_result();
                
                if ($studentResult->num_rows === 0) {
                    echo "<p style='color:red;'>No student record found for user ID: " . $row['id'] . "</p>";
                } else {
                    $studentRow = $studentResult->fetch_assoc();
                    echo "<p style='color:green;'>Student record found!</p>";
                    echo "<p>Student ID: " . $studentRow['student_id'] . "</p>";
                    echo "<p>Name: " . $studentRow['first_name'] . " " . $studentRow['last_name'] . "</p>";
                }
                $studentStmt->close();
            }
        }
        
        echo "<p style='color:green;'>Login successful! User would be redirected to appropriate dashboard.</p>";
    } else {
        echo "<p style='color:red;'>Password is incorrect!</p>";
    }
} else {
    echo "<p style='color:red;'>No user found with username: $username and type: $userType</p>";
    
    // Try to find the username without user_type to help debugging
    $checkUsername = "SELECT * FROM users WHERE username=?";
    $checkStmt = $conn->prepare($checkUsername);
    $checkStmt->bind_param("s", $username);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();
    
    if ($checkResult->num_rows > 0) {
        echo "<p>However, the username \"$username\" exists with different user type(s):</p>";
        echo "<ul>";
        while ($checkRow = $checkResult->fetch_assoc()) {
            echo "<li>User Type: " . $checkRow['user_type'] . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>Username \"$username\" does not exist in the database.</p>";
    }
}

$stmt->close();
$conn->close();
?> 