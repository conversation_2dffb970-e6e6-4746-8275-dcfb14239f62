<!-- Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<!-- Font Awesome Icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<!-- Hind Siliguri Font -->
<link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<!-- Custom Fonts CSS -->
<link rel="stylesheet" href="../css/fonts.css">
<!-- Custom CSS -->
<style>
    body {
        font-size: .875rem;
    }
    .feather {
        width: 16px;
        height: 16px;
        vertical-align: text-bottom;
    }
    /* Sidebar */
    .sidebar {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        z-index: 100;
        padding: 48px 0 0;
        box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
        background: linear-gradient(to bottom, #1a2980 0%, #26d0ce 100%);
        color: #fff;
        overflow: hidden;
        height: 100vh;
    }

    .sidebar::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
        opacity: 0.8;
        z-index: -1;
        height: 100vh;
    }
    @media (max-width: 767.98px) {
        .sidebar {
            top: 5rem;
            height: calc(100vh - 5rem);
        }
        .sidebar.collapse:not(.show) {
            display: none;
        }
        .sidebar-footer {
            width: 50% !important;
        }
    }
    .sidebar-sticky {
        position: relative;
        top: 0;
        height: calc(100vh - 110px); /* Subtract header and footer height */
        padding-top: .5rem;
        padding-bottom: 10px;
        overflow-x: hidden;
        overflow-y: auto;
    }

    /* Custom scrollbar for sidebar */
    .sidebar-sticky::-webkit-scrollbar {
        width: 6px;
    }

    .sidebar-sticky::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
    }

    .sidebar-sticky::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 3px;
    }

    .sidebar-sticky::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.5);
    }

    /* Firefox scrollbar */
    .sidebar-sticky {
        scrollbar-width: thin;
        scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
    }
    .sidebar .nav-link {
        font-weight: 500;
        color: #fff;
        padding: 8px 16px;
        margin: 2px 0;
        border-radius: 0 30px 30px 0;
        transition: all 0.3s ease;
        font-size: 0.95rem;
        white-space: normal;
        word-wrap: break-word;
        line-height: 1.3;
    }
    .sidebar .nav-link i,
    .sidebar .nav-link .feather {
        margin-right: 10px;
        color: rgba(255, 255, 255, 0.9);
        width: 24px;
        text-align: center;
        font-size: 1.1rem;
        transition: all 0.3s ease;
    }
    .sidebar .nav-link.active {
        color: #fff;
        background-color: rgba(255, 255, 255, 0.2);
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
    .sidebar .nav-link:hover {
        background-color: rgba(255, 255, 255, 0.1);
        transform: translateX(5px);
    }
    .sidebar .nav-link:hover i,
    .sidebar .nav-link:hover .feather,
    .sidebar .nav-link.active i,
    .sidebar .nav-link.active .feather {
        color: #fff;
        transform: scale(1.1);
    }
    .sidebar-heading {
        font-size: .75rem;
        text-transform: uppercase;
    }

    /* Dropdown menu styles */
    .btn-toggle-nav {
        padding-left: 1.5rem;
        list-style: none;
    }

    .btn-toggle-nav a {
        padding: 6px 12px;
        margin: 2px 0 2px 1.5rem;
        text-decoration: none;
        color: rgba(255, 255, 255, 0.85);
        display: block;
        border-radius: 0 20px 20px 0;
        transition: all 0.3s ease;
        font-size: 0.9rem;
        white-space: normal;
        word-wrap: break-word;
        line-height: 1.3;
    }

    .btn-toggle-nav a:hover,
    .btn-toggle-nav a:focus {
        background-color: rgba(255, 255, 255, 0.15);
        color: #fff;
        transform: translateX(5px);
    }

    .btn-toggle-nav a.active {
        color: #fff;
        background-color: rgba(255, 255, 255, 0.25);
        font-weight: 600;
        box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
    }

    /* Submenu collapse icon */
    .fa-angle-down {
        transition: transform 0.3s ease;
    }

    .collapsed .fa-angle-down {
        transform: rotate(-90deg);
    }

    /* Sidebar brand and footer */
    .sidebar-brand {
        padding: 10px 15px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .sidebar-footer {
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: inherit;
        z-index: 10;
        padding: 10px 0;
    }

    .sidebar-footer .btn {
        transition: all 0.3s ease;
    }

    .sidebar-footer .btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    /* Navbar */
    .navbar-brand {
        padding-top: .75rem;
        padding-bottom: .75rem;
        font-size: 1rem;
        background-color: rgba(0, 0, 0, .25);
        box-shadow: inset -1px 0 0 rgba(0, 0, 0, .25);
    }
    .navbar .navbar-toggler {
        top: .25rem;
        right: 1rem;
    }

    /* Modern card styles */
    .hover-card {
        transition: all 0.3s ease;
        cursor: pointer;
        border-radius: 10px;
    }

    .hover-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
    }

    .icon-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        margin-bottom: 10px;
        transition: all 0.3s ease;
    }

    .hover-card:hover .icon-circle {
        transform: scale(1.1);
    }

    .card-title {
        font-weight: 600;
        font-size: 14px;
    }

    /* Custom background colors */
    .bg-purple {
        background-color: #6f42c1;
    }

    /* Button reset for card button */
    .card .btn-light {
        background: transparent;
        border: none;
        width: 100%;
        height: 100%;
        padding: 0;
    }

    .card .btn-light:focus {
        box-shadow: none;
    }
</style>

<header class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
    <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="dashboard.php">স্কুল ম্যানেজমেন্ট</a>
    <button class="navbar-toggler position-absolute d-md-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="টগল নেভিগেশন">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="navbar-nav">
        <div class="nav-item text-nowrap">
            <?php if (isset($_SESSION['userId'])): ?>
                <span class="nav-link px-3 text-light"><?php echo htmlspecialchars($_SESSION['userType']); ?></span>
            <?php endif; ?>
            <a class="nav-link px-3" href="../includes/logout.inc.php">লগআউট</a>
        </div>
    </div>
</header>

<!-- Bootstrap & jQuery JS -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // Initialize Bootstrap components
    document.addEventListener('DOMContentLoaded', function() {
        // Make sure sidebar is visible on larger screens
        const checkScreenSize = function() {
            if (window.innerWidth >= 768) {
                document.getElementById('sidebarMenu').classList.add('show');
            }
        };

        // Check on page load
        checkScreenSize();

        // Check on resize
        window.addEventListener('resize', checkScreenSize);
    });
</script>