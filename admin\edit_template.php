<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_template'])) {
    try {
        $id = intval($_POST['id']);
        $class_level = trim($_POST['class_level']);
        $class_level_name = trim($_POST['class_level_name']);
        $description = trim($_POST['description']);
        $default_total_marks = floatval($_POST['default_total_marks']);
        $default_cq_marks = floatval($_POST['default_cq_marks']);
        $default_mcq_marks = floatval($_POST['default_mcq_marks']);
        $default_practical_marks = floatval($_POST['default_practical_marks']);
        $has_cq = isset($_POST['has_cq']) ? 1 : 0;
        $has_mcq = isset($_POST['has_mcq']) ? 1 : 0;
        $has_practical = isset($_POST['has_practical']) ? 1 : 0;
        $is_active = isset($_POST['is_active']) ? 1 : 0;

        // Calculate percentages
        $cq_percentage = $default_total_marks > 0 ? round(($default_cq_marks / $default_total_marks) * 100, 2) : 0;
        $mcq_percentage = $default_total_marks > 0 ? round(($default_mcq_marks / $default_total_marks) * 100, 2) : 0;
        $practical_percentage = $default_total_marks > 0 ? round(($default_practical_marks / $default_total_marks) * 100, 2) : 0;

        // Validate total marks
        $total_calculated = $default_cq_marks + $default_mcq_marks + $default_practical_marks;
        if (abs($total_calculated - $default_total_marks) > 0.01) {
            throw new Exception("মোট নম্বরের সাথে উপাদানগুলির যোগফল মিলছে না। মোট: $default_total_marks, যোগফল: $total_calculated");
        }

        // Update template
        $updateQuery = "UPDATE class_level_templates SET 
                       class_level = ?, 
                       class_level_name = ?, 
                       description = ?, 
                       default_total_marks = ?, 
                       has_cq = ?, 
                       has_mcq = ?, 
                       has_practical = ?, 
                       default_cq_marks = ?, 
                       default_mcq_marks = ?, 
                       default_practical_marks = ?, 
                       cq_percentage = ?, 
                       mcq_percentage = ?, 
                       practical_percentage = ?, 
                       is_active = ?, 
                       updated_at = NOW() 
                       WHERE id = ?";

        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("sssdiiiiddddii", 
            $class_level, $class_level_name, $description, $default_total_marks,
            $has_cq, $has_mcq, $has_practical,
            $default_cq_marks, $default_mcq_marks, $default_practical_marks,
            $cq_percentage, $mcq_percentage, $practical_percentage,
            $is_active, $id
        );

        if ($stmt->execute()) {
            $_SESSION['success_message'] = 'টেমপ্লেট সফলভাবে আপডেট করা হয়েছে।';
            header("Location: template_manager.php");
            exit();
        } else {
            throw new Exception("টেমপ্লেট আপডেট করতে সমস্যা হয়েছে।");
        }

    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get template data for editing
$template_data = null;
if (isset($_GET['id']) && is_numeric($_GET['id'])) {
    $id = intval($_GET['id']);
    $query = "SELECT * FROM class_level_templates WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $template_data = $result->fetch_assoc();
    } else {
        $_SESSION['error_message'] = 'টেমপ্লেট পাওয়া যায়নি।';
        header("Location: template_manager.php");
        exit();
    }
} else {
    $_SESSION['error_message'] = 'অবৈধ টেমপ্লেট আইডি।';
    header("Location: template_manager.php");
    exit();
}

// Set current page for sidebar highlighting
$currentPage = 'template_manager.php';
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>টেমপ্লেট সম্পাদনা - <?php echo htmlspecialchars($template_data['class_level_name']); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    
    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css">
    
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: #f8f9fa;
        }
        
        .template-form {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin: 20px 0;
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #4361ee;
            box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
        }
        
        .btn {
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4361ee 0%, #3f37c9 100%);
            border: none;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(67, 97, 238, 0.4);
        }
        
        .marks-summary {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .marks-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .marks-card:hover {
            transform: translateY(-5px);
        }
        
        .marks-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .marks-label {
            font-size: 14px;
            color: #6c757d;
        }
    </style>
</head>

<body onload="document.body.classList.add('loaded')">
    <div class="container-fluid">
        <div class="row">
            <!-- Include sidebar -->
            <?php include_once 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-edit me-2 text-primary"></i>
                        টেমপ্লেট সম্পাদনা: <?php echo htmlspecialchars($template_data['class_level_name']); ?>
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="template_manager.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>ফিরে যান
                        </a>
                    </div>
                </div>

                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger animate__animated animate__fadeIn">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>

                <div class="template-form animate__animated animate__fadeInUp">
                    <form method="POST" action="" id="templateForm">
                        <input type="hidden" name="id" value="<?php echo $template_data['id']; ?>">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="class_level" class="form-label">
                                    <i class="fas fa-tag me-1 text-primary"></i>টেমপ্লেট আইডি
                                </label>
                                <input type="text" class="form-control" id="class_level" name="class_level" 
                                       value="<?php echo htmlspecialchars($template_data['class_level']); ?>" required>
                                <div class="form-text">অনন্য আইডি যা সিস্টেমে ব্যবহৃত হবে</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="class_level_name" class="form-label">
                                    <i class="fas fa-signature me-1 text-primary"></i>টেমপ্লেট নাম
                                </label>
                                <input type="text" class="form-control" id="class_level_name" name="class_level_name" 
                                       value="<?php echo htmlspecialchars($template_data['class_level_name']); ?>" required>
                                <div class="form-text">ব্যবহারকারীদের জন্য প্রদর্শিত নাম</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">
                                <i class="fas fa-align-left me-1 text-primary"></i>বিবরণ
                            </label>
                            <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($template_data['description']); ?></textarea>
                            <div class="form-text">টেমপ্লেটের সংক্ষিপ্ত বিবরণ</div>
                        </div>

                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="default_total_marks" class="form-label">
                                    <i class="fas fa-calculator me-1 text-primary"></i>মোট নম্বর
                                </label>
                                <input type="number" class="form-control" id="default_total_marks" name="default_total_marks" 
                                       value="<?php echo $template_data['default_total_marks']; ?>" min="1" step="0.01" required>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="default_cq_marks" class="form-label">
                                    <i class="fas fa-pen me-1 text-primary"></i>সৃজনশীল নম্বর
                                </label>
                                <input type="number" class="form-control marks-input" id="default_cq_marks" name="default_cq_marks" 
                                       value="<?php echo $template_data['default_cq_marks']; ?>" min="0" step="0.01">
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="default_mcq_marks" class="form-label">
                                    <i class="fas fa-tasks me-1 text-primary"></i>MCQ নম্বর
                                </label>
                                <input type="number" class="form-control marks-input" id="default_mcq_marks" name="default_mcq_marks" 
                                       value="<?php echo $template_data['default_mcq_marks']; ?>" min="0" step="0.01">
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="default_practical_marks" class="form-label">
                                    <i class="fas fa-flask me-1 text-primary"></i>ব্যবহারিক নম্বর
                                </label>
                                <input type="number" class="form-control marks-input" id="default_practical_marks" name="default_practical_marks" 
                                       value="<?php echo $template_data['default_practical_marks']; ?>" min="0" step="0.01">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="has_cq" name="has_cq" 
                                           <?php echo $template_data['has_cq'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="has_cq">
                                        <i class="fas fa-pen me-1"></i>সৃজনশীল প্রশ্ন আছে
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="has_mcq" name="has_mcq" 
                                           <?php echo $template_data['has_mcq'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="has_mcq">
                                        <i class="fas fa-tasks me-1"></i>বহুনির্বাচনি প্রশ্ন আছে
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="has_practical" name="has_practical" 
                                           <?php echo $template_data['has_practical'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="has_practical">
                                        <i class="fas fa-flask me-1"></i>ব্যবহারিক পরীক্ষা আছে
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           <?php echo $template_data['is_active'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_active">
                                        <i class="fas fa-toggle-on me-1"></i>সক্রিয় অবস্থা
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Marks Summary -->
                        <div class="marks-summary">
                            <h5 class="mb-3">
                                <i class="fas fa-chart-pie me-2"></i>মার্কস বিতরণ সারাংশ
                            </h5>
                            
                            <div class="row" id="marksSummary">
                                <div class="col-md-3 mb-3">
                                    <div class="marks-card">
                                        <div class="marks-value text-primary" id="cqPercentage">0%</div>
                                        <div class="marks-label">সৃজনশীল</div>
                                        <small class="text-muted" id="cqMarks">0 নম্বর</small>
                                    </div>
                                </div>
                                
                                <div class="col-md-3 mb-3">
                                    <div class="marks-card">
                                        <div class="marks-value text-success" id="mcqPercentage">0%</div>
                                        <div class="marks-label">MCQ</div>
                                        <small class="text-muted" id="mcqMarks">0 নম্বর</small>
                                    </div>
                                </div>
                                
                                <div class="col-md-3 mb-3">
                                    <div class="marks-card">
                                        <div class="marks-value text-warning" id="practicalPercentage">0%</div>
                                        <div class="marks-label">ব্যবহারিক</div>
                                        <small class="text-muted" id="practicalMarks">0 নম্বর</small>
                                    </div>
                                </div>
                                
                                <div class="col-md-3 mb-3">
                                    <div class="marks-card">
                                        <div class="marks-value text-info" id="totalMarks">0</div>
                                        <div class="marks-label">মোট নম্বর</div>
                                        <small class="text-muted" id="totalStatus">সঠিক</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a href="template_manager.php" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-2"></i>বাতিল করুন
                            </a>
                            <button type="submit" name="update_template" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-save me-2"></i>আপডেট করুন
                            </button>
                        </div>
                    </form>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Template Edit JavaScript -->
    <script src="js/template_edit.js"></script>
</body>
</html>
