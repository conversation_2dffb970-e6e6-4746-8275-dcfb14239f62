<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);



session_start();

require_once '../includes/dbh.inc.php';





// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Get parameters
$reportType = $_GET['type'] ?? 'summary'; // summary, detailed, student_wise
$studentId = $_GET['student_id'] ?? '';
$classId = $_GET['class_id'] ?? '';
$sessionId = $_GET['session_id'] ?? '';
$departmentId = $_GET['department_id'] ?? '';
$fromDate = $_GET['from_date'] ?? date('Y-m-01'); // First day of current month
$toDate = $_GET['to_date'] ?? date('Y-m-t'); // Last day of current month
$feeType = $_GET['fee_type'] ?? '';

// Get school information
// Check if settings table exists
$checkTable = $conn->query("SHOW TABLES LIKE 'settings'");
if ($checkTable->num_rows == 0) {
    $schoolInfo = [];
} else {
    $schoolQuery = "SELECT * FROM settings WHERE setting_key IN ('school_name', 'school_address', 'school_phone', 'school_email') LIMIT 4";
    $schoolResult = $conn->query($schoolQuery);

    if (!$schoolResult) {
        $schoolInfo = [];
    } else {
        $schoolInfo = [];
        while ($row = $schoolResult->fetch_assoc()) {
            $schoolInfo[$row['setting_key']] = $row['setting_value'];
        }
    }
}

// Default school info if not found in settings
$schoolName = $schoolInfo['school_name'] ?? 'জাকির ফারুক আদর্শ উচ্চ বিদ্যালয়';
$schoolAddress = $schoolInfo['school_address'] ?? 'ঢাকা, বাংলাদেশ';
$schoolPhone = $schoolInfo['school_phone'] ?? '০১৭১২৩৪৫৬৭৮';
$schoolEmail = $schoolInfo['school_email'] ?? '<EMAIL>';

// Build query based on report type
if ($reportType === 'student_wise' && !empty($studentId)) {
    // Individual student report
    $query = "SELECT f.*, s.first_name, s.last_name, s.student_id as roll,
              c.class_name, d.department_name, ss.session_name,
              f.paid as total_paid
              FROM fees f
              JOIN students s ON f.student_id = s.id
              LEFT JOIN classes c ON s.class_id = c.id
              LEFT JOIN departments d ON s.department_id = d.id
              LEFT JOIN sessions ss ON s.session_id = ss.id
              WHERE f.student_id = ?";
    
    $params = [$studentId];
    $types = "i";
    
    if (!empty($fromDate) && !empty($toDate)) {
        $query .= " AND f.due_date BETWEEN ? AND ?";
        $params[] = $fromDate;
        $params[] = $toDate;
        $types .= "ss";
    }
    
    $query .= " ORDER BY f.due_date DESC";
    
} else {
    // Summary or detailed report
    $query = "SELECT f.*, s.first_name, s.last_name, s.student_id as roll,
              c.class_name, d.department_name, ss.session_name,
              f.paid as total_paid
              FROM fees f
              JOIN students s ON f.student_id = s.id
              LEFT JOIN classes c ON s.class_id = c.id
              LEFT JOIN departments d ON s.department_id = d.id
              LEFT JOIN sessions ss ON s.session_id = ss.id
              WHERE 1=1";
    
    $params = [];
    $types = "";
    
    if (!empty($fromDate) && !empty($toDate)) {
        $query .= " AND f.due_date BETWEEN ? AND ?";
        $params[] = $fromDate;
        $params[] = $toDate;
        $types .= "ss";
    }
    
    if (!empty($classId)) {
        $query .= " AND s.class_id = ?";
        $params[] = $classId;
        $types .= "i";
    }
    
    if (!empty($sessionId)) {
        $query .= " AND s.session_id = ?";
        $params[] = $sessionId;
        $types .= "i";
    }
    
    if (!empty($departmentId)) {
        $query .= " AND s.department_id = ?";
        $params[] = $departmentId;
        $types .= "i";
    }
    
    if (!empty($feeType)) {
        $query .= " AND f.fee_type = ?";
        $params[] = $feeType;
        $types .= "s";
    }
    
    $query .= " ORDER BY s.first_name, s.last_name, f.due_date DESC";
}

// Execute query
$stmt = $conn->prepare($query);
if (!$stmt) {
    die("Prepare failed: " . $conn->error);
}

if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}

$stmt->execute();
$result = $stmt->get_result();

// Calculate totals
$totalAmount = 0;
$totalPaid = 0;
$totalDue = 0;
$reportData = [];

while ($row = $result->fetch_assoc()) {
    $amount = floatval($row['amount']);
    $paid = floatval($row['total_paid']);

    // Data validation: paid should not exceed amount
    if ($paid > $amount) {
        $paid = $amount; // Cap paid at amount
    }

    $due = $amount - $paid;

    $totalAmount += $amount;
    $totalPaid += $paid;
    $totalDue += $due;

    $row['calculated_paid'] = $paid;
    $row['calculated_due'] = $due;
    $reportData[] = $row;
}

// Get current date and time
$currentDate = date('d/m/Y');
$currentTime = date('h:i A');
$reportNumber = 'MEMO-' . date('Ymd') . '-' . rand(1000, 9999);

// Get classes, sessions, departments for filters
$classesQuery = "SELECT * FROM classes ORDER BY class_name";
$classesResult = $conn->query($classesQuery);

$sessionsQuery = "SELECT * FROM sessions ORDER BY session_name DESC";
$sessionsResult = $conn->query($sessionsQuery);

$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departmentsResult = $conn->query($departmentsQuery);

$feeTypesQuery = "SELECT DISTINCT fee_type FROM fees ORDER BY fee_type";
$feeTypesResult = $conn->query($feeTypesQuery);

$studentsQuery = "SELECT s.*, c.class_name FROM students s LEFT JOIN classes c ON s.class_id = c.id ORDER BY s.first_name, s.last_name";
$studentsResult = $conn->query($studentsQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ফি হিসাব মেমো - <?php echo $schoolName; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @media print {
            .no-print { display: none !important; }
            .page-break { page-break-before: always; }
            body { font-size: 12px; }
            .memo-container { margin: 0; padding: 10px; }
        }
        
        .memo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border: 2px solid #333;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .memo-header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        
        .memo-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .memo-subtitle {
            font-size: 16px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .memo-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .memo-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .memo-table th,
        .memo-table td {
            border: 1px solid #333;
            padding: 8px;
            text-align: left;
            font-size: 12px;
            vertical-align: middle;
        }

        .memo-table td.center {
            text-align: center;
        }

        .memo-table td.right {
            text-align: right;
        }
        
        .memo-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            text-align: center;
            vertical-align: middle;
        }
        
        .memo-total {
            background-color: #e9ecef;
            font-weight: bold;
        }
        
        .memo-footer {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
        }
        
        .signature-box {
            text-align: center;
            width: 200px;
        }
        
        .signature-line {
            border-top: 1px solid #333;
            margin-top: 50px;
            padding-top: 5px;
        }
        
        .copy-label {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #007bff;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .filter-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- Filter Section -->
    <div class="container-fluid no-print">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-file-invoice me-2"></i> ফি হিসাব মেমো</h2>
                    <div>
                        <a href="fee_management.php" class="btn btn-secondary me-2">
                            <i class="fas fa-arrow-left me-1"></i> ফিরে যান
                        </a>
                        <button onclick="window.print()" class="btn btn-primary">
                            <i class="fas fa-print me-1"></i> প্রিন্ট করুন
                        </button>
                    </div>
                </div>
                
                <div class="filter-section">
                    <form method="GET" action="fee_memo_report.php">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">রিপোর্ট ধরন</label>
                                <select name="type" class="form-select">
                                    <option value="summary" <?php echo ($reportType == 'summary') ? 'selected' : ''; ?>>সারসংক্ষেপ</option>
                                    <option value="detailed" <?php echo ($reportType == 'detailed') ? 'selected' : ''; ?>>বিস্তারিত</option>
                                    <option value="student_wise" <?php echo ($reportType == 'student_wise') ? 'selected' : ''; ?>>শিক্ষার্থী ভিত্তিক</option>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label">শুরুর তারিখ</label>
                                <input type="date" name="from_date" class="form-control" value="<?php echo $fromDate; ?>">
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label">শেষের তারিখ</label>
                                <input type="date" name="to_date" class="form-control" value="<?php echo $toDate; ?>">
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label">শ্রেণী</label>
                                <select name="class_id" class="form-select">
                                    <option value="">সকল শ্রেণী</option>
                                    <?php while ($class = $classesResult->fetch_assoc()): ?>
                                        <option value="<?php echo $class['id']; ?>" <?php echo ($classId == $class['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($class['class_name']); ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-3">
                                <label class="form-label">সেশন</label>
                                <select name="session_id" class="form-select">
                                    <option value="">সকল সেশন</option>
                                    <?php while ($session = $sessionsResult->fetch_assoc()): ?>
                                        <option value="<?php echo $session['id']; ?>" <?php echo ($sessionId == $session['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($session['session_name']); ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label">বিভাগ</label>
                                <select name="department_id" class="form-select">
                                    <option value="">সকল বিভাগ</option>
                                    <?php while ($department = $departmentsResult->fetch_assoc()): ?>
                                        <option value="<?php echo $department['id']; ?>" <?php echo ($departmentId == $department['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($department['department_name']); ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label">ফি ধরন</label>
                                <select name="fee_type" class="form-select">
                                    <option value="">সকল ফি</option>
                                    <?php while ($feeTypeRow = $feeTypesResult->fetch_assoc()): ?>
                                        <option value="<?php echo $feeTypeRow['fee_type']; ?>" <?php echo ($feeType == $feeTypeRow['fee_type']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($feeTypeRow['fee_type']); ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            
                            <?php if ($reportType === 'student_wise'): ?>
                            <div class="col-md-3">
                                <label class="form-label">শিক্ষার্থী</label>
                                <select name="student_id" class="form-select">
                                    <option value="">শিক্ষার্থী নির্বাচন করুন</option>
                                    <?php while ($student = $studentsResult->fetch_assoc()): ?>
                                        <option value="<?php echo $student['id']; ?>" <?php echo ($studentId == $student['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name'] . ' (' . $student['student_id'] . ')'); ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i> রিপোর্ট তৈরি করুন
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Office Copy -->
    <div class="memo-container">
        <div class="copy-label">অফিস কপি</div>

        <div class="memo-header">
            <div class="memo-title"><?php echo htmlspecialchars($schoolName); ?></div>
            <div class="memo-subtitle"><?php echo htmlspecialchars($schoolAddress); ?></div>
            <div style="font-size: 14px;">ফোন: <?php echo htmlspecialchars($schoolPhone); ?> | ইমেইল: <?php echo htmlspecialchars($schoolEmail); ?></div>
            <div style="font-size: 18px; font-weight: bold; margin-top: 10px; color: #007bff;">ফি হিসাব মেমো</div>
        </div>

        <div class="memo-info">
            <div>
                <strong>মেমো নং:</strong> <?php echo $reportNumber; ?><br>
                <strong>তারিখ:</strong> <?php echo $currentDate; ?><br>
                <strong>সময়:</strong> <?php echo $currentTime; ?>
            </div>
            <div>
                <strong>রিপোর্ট ধরন:</strong>
                <?php
                switch($reportType) {
                    case 'summary': echo 'সারসংক্ষেপ'; break;
                    case 'detailed': echo 'বিস্তারিত'; break;
                    case 'student_wise': echo 'শিক্ষার্থী ভিত্তিক'; break;
                    default: echo 'সারসংক্ষেপ';
                }
                ?><br>
                <strong>সময়কাল:</strong> <?php echo date('d/m/Y', strtotime($fromDate)) . ' - ' . date('d/m/Y', strtotime($toDate)); ?>
            </div>
        </div>

        <?php if ($reportType === 'student_wise' && !empty($studentId) && !empty($reportData)): ?>
            <div style="background: #f8f9fa; padding: 10px; margin-bottom: 15px; border-radius: 5px;">
                <strong>শিক্ষার্থীর তথ্য:</strong><br>
                নাম: <?php echo htmlspecialchars($reportData[0]['first_name'] . ' ' . $reportData[0]['last_name']); ?><br>
                রোল: <?php echo htmlspecialchars($reportData[0]['roll']); ?><br>
                শ্রেণী: <?php echo htmlspecialchars($reportData[0]['class_name'] ?? 'N/A'); ?><br>
                বিভাগ: <?php echo htmlspecialchars($reportData[0]['department_name'] ?? 'N/A'); ?><br>
                সেশন: <?php echo htmlspecialchars($reportData[0]['session_name'] ?? 'N/A'); ?>
            </div>
        <?php endif; ?>

        <table class="memo-table">
            <thead>
                <tr>
                    <th style="width: <?php echo ($reportType === 'student_wise') ? '8%' : '5%'; ?>; text-align: center;">ক্রম</th>
                    <?php if ($reportType !== 'student_wise'): ?>
                    <th style="width: 20%; text-align: center;">শিক্ষার্থী</th>
                    <th style="width: 10%; text-align: center;">রোল</th>
                    <th style="width: 10%; text-align: center;">শ্রেণী</th>
                    <?php endif; ?>
                    <th style="width: <?php echo ($reportType === 'student_wise') ? '20%' : '15%'; ?>; text-align: center;">ফি ধরন</th>
                    <th style="width: <?php echo ($reportType === 'student_wise') ? '18%' : '10%'; ?>; text-align: center;">মোট ফি</th>
                    <th style="width: <?php echo ($reportType === 'student_wise') ? '18%' : '10%'; ?>; text-align: center;">প্রদান</th>
                    <th style="width: <?php echo ($reportType === 'student_wise') ? '18%' : '10%'; ?>; text-align: center;">বকেয়া</th>
                    <th style="width: <?php echo ($reportType === 'student_wise') ? '10%' : '10%'; ?>; text-align: center;">তারিখ</th>
                    <th style="width: <?php echo ($reportType === 'student_wise') ? '8%' : '10%'; ?>; text-align: center;">স্ট্যাটাস</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($reportData)): ?>
                    <?php $serial = 1; ?>
                    <?php foreach ($reportData as $fee): ?>
                        <tr>
                            <td class="center"><?php echo $serial++; ?></td>
                            <?php if ($reportType !== 'student_wise'): ?>
                            <td><?php echo htmlspecialchars($fee['first_name'] . ' ' . $fee['last_name']); ?></td>
                            <td class="center"><?php echo htmlspecialchars($fee['roll']); ?></td>
                            <td class="center"><?php echo htmlspecialchars($fee['class_name'] ?? 'N/A'); ?></td>
                            <?php endif; ?>
                            <td><?php echo htmlspecialchars($fee['fee_type']); ?></td>
                            <td class="right">৳ <?php echo number_format($fee['amount'], 2); ?></td>
                            <td class="right">৳ <?php echo number_format($fee['calculated_paid'], 2); ?></td>
                            <td class="right">৳ <?php echo number_format($fee['calculated_due'], 2); ?></td>
                            <td class="center"><?php echo date('d/m/Y', strtotime($fee['due_date'])); ?></td>
                            <td class="center">
                                <?php
                                if ($fee['calculated_due'] <= 0) {
                                    echo '<span style="color: green; font-weight: bold;">পরিশোধিত</span>';
                                } elseif ($fee['calculated_paid'] > 0) {
                                    echo '<span style="color: orange; font-weight: bold;">আংশিক</span>';
                                } else {
                                    echo '<span style="color: red; font-weight: bold;">বকেয়া</span>';
                                }
                                ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="<?php echo ($reportType === 'student_wise') ? '7' : '10'; ?>" style="text-align: center; padding: 20px;">
                            কোন রেকর্ড পাওয়া যায়নি
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
            <tfoot>
                <tr class="memo-total">
                    <td colspan="<?php echo ($reportType === 'student_wise') ? '4' : '7'; ?>" style="text-align: right; font-weight: bold;">মোট:</td>
                    <td style="text-align: right; font-weight: bold;">
                        <div style="border-top: 2px solid #333; padding-top: 5px;">
                            মোট<br>
                            ৳ <?php echo number_format($totalAmount, 2); ?>
                        </div>
                    </td>
                    <td style="text-align: right; font-weight: bold;">
                        <div style="border-top: 2px solid #333; padding-top: 5px;">
                            প্রদান<br>
                            ৳ <?php echo number_format($totalPaid, 2); ?>
                        </div>
                    </td>
                    <td style="text-align: right; font-weight: bold;">
                        <div style="border-top: 2px solid #333; padding-top: 5px;">
                            বকেয়া<br>
                            ৳ <?php echo number_format($totalDue, 2); ?>
                        </div>
                    </td>
                    <td colspan="2"></td>
                </tr>
            </tfoot>
        </table>

        <div style="background: #e9ecef; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
            <div class="row">
                <div class="col-md-4">
                    <strong>মোট ফি:</strong><br>
                    <span style="font-size: 18px; color: #007bff;">৳ <?php echo number_format($totalAmount, 2); ?></span>
                </div>
                <div class="col-md-4">
                    <strong>মোট প্রদান:</strong><br>
                    <span style="font-size: 18px; color: #28a745;">৳ <?php echo number_format($totalPaid, 2); ?></span>
                </div>
                <div class="col-md-4">
                    <strong>মোট বকেয়া:</strong><br>
                    <span style="font-size: 18px; color: #dc3545;">৳ <?php echo number_format($totalDue, 2); ?></span>
                </div>
            </div>
        </div>

        <div class="memo-footer">
            <div class="signature-box">
                <div class="signature-line">হিসাবরক্ষক</div>
            </div>
            <div class="signature-box">
                <div class="signature-line">প্রধান শিক্ষক</div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 20px; font-size: 12px; color: #666;">
            এই মেমো কম্পিউটার দ্বারা তৈরি এবং স্বাক্ষরের প্রয়োজন নেই।<br>
            প্রিন্ট তারিখ: <?php echo date('d/m/Y h:i A'); ?>
        </div>
    </div>

    <!-- Customer Copy -->
    <div class="memo-container page-break">
        <div class="copy-label" style="background: #28a745;">গ্রাহক কপি</div>

        <div class="memo-header">
            <div class="memo-title"><?php echo htmlspecialchars($schoolName); ?></div>
            <div class="memo-subtitle"><?php echo htmlspecialchars($schoolAddress); ?></div>
            <div style="font-size: 14px;">ফোন: <?php echo htmlspecialchars($schoolPhone); ?> | ইমেইল: <?php echo htmlspecialchars($schoolEmail); ?></div>
            <div style="font-size: 18px; font-weight: bold; margin-top: 10px; color: #28a745;">ফি হিসাব মেমো</div>
        </div>

        <div class="memo-info">
            <div>
                <strong>মেমো নং:</strong> <?php echo $reportNumber; ?><br>
                <strong>তারিখ:</strong> <?php echo $currentDate; ?><br>
                <strong>সময়:</strong> <?php echo $currentTime; ?>
            </div>
            <div>
                <strong>রিপোর্ট ধরন:</strong>
                <?php
                switch($reportType) {
                    case 'summary': echo 'সারসংক্ষেপ'; break;
                    case 'detailed': echo 'বিস্তারিত'; break;
                    case 'student_wise': echo 'শিক্ষার্থী ভিত্তিক'; break;
                    default: echo 'সারসংক্ষেপ';
                }
                ?><br>
                <strong>সময়কাল:</strong> <?php echo date('d/m/Y', strtotime($fromDate)) . ' - ' . date('d/m/Y', strtotime($toDate)); ?>
            </div>
        </div>

        <?php if ($reportType === 'student_wise' && !empty($studentId) && !empty($reportData)): ?>
            <div style="background: #f8f9fa; padding: 10px; margin-bottom: 15px; border-radius: 5px;">
                <strong>শিক্ষার্থীর তথ্য:</strong><br>
                নাম: <?php echo htmlspecialchars($reportData[0]['first_name'] . ' ' . $reportData[0]['last_name']); ?><br>
                রোল: <?php echo htmlspecialchars($reportData[0]['roll']); ?><br>
                শ্রেণী: <?php echo htmlspecialchars($reportData[0]['class_name'] ?? 'N/A'); ?><br>
                বিভাগ: <?php echo htmlspecialchars($reportData[0]['department_name'] ?? 'N/A'); ?><br>
                সেশন: <?php echo htmlspecialchars($reportData[0]['session_name'] ?? 'N/A'); ?>
            </div>
        <?php endif; ?>

        <table class="memo-table">
            <thead>
                <tr>
                    <th style="width: <?php echo ($reportType === 'student_wise') ? '8%' : '5%'; ?>; text-align: center;">ক্রম</th>
                    <?php if ($reportType !== 'student_wise'): ?>
                    <th style="width: 20%; text-align: center;">শিক্ষার্থী</th>
                    <th style="width: 10%; text-align: center;">রোল</th>
                    <th style="width: 10%; text-align: center;">শ্রেণী</th>
                    <?php endif; ?>
                    <th style="width: <?php echo ($reportType === 'student_wise') ? '20%' : '15%'; ?>; text-align: center;">ফি ধরন</th>
                    <th style="width: <?php echo ($reportType === 'student_wise') ? '18%' : '10%'; ?>; text-align: center;">মোট ফি</th>
                    <th style="width: <?php echo ($reportType === 'student_wise') ? '18%' : '10%'; ?>; text-align: center;">প্রদান</th>
                    <th style="width: <?php echo ($reportType === 'student_wise') ? '18%' : '10%'; ?>; text-align: center;">বকেয়া</th>
                    <th style="width: <?php echo ($reportType === 'student_wise') ? '10%' : '10%'; ?>; text-align: center;">তারিখ</th>
                    <th style="width: <?php echo ($reportType === 'student_wise') ? '8%' : '10%'; ?>; text-align: center;">স্ট্যাটাস</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($reportData)): ?>
                    <?php $serial = 1; ?>
                    <?php foreach ($reportData as $fee): ?>
                        <tr>
                            <td class="center"><?php echo $serial++; ?></td>
                            <?php if ($reportType !== 'student_wise'): ?>
                            <td><?php echo htmlspecialchars($fee['first_name'] . ' ' . $fee['last_name']); ?></td>
                            <td class="center"><?php echo htmlspecialchars($fee['roll']); ?></td>
                            <td class="center"><?php echo htmlspecialchars($fee['class_name'] ?? 'N/A'); ?></td>
                            <?php endif; ?>
                            <td><?php echo htmlspecialchars($fee['fee_type']); ?></td>
                            <td class="right">৳ <?php echo number_format($fee['amount'], 2); ?></td>
                            <td class="right">৳ <?php echo number_format($fee['calculated_paid'], 2); ?></td>
                            <td class="right">৳ <?php echo number_format($fee['calculated_due'], 2); ?></td>
                            <td class="center"><?php echo date('d/m/Y', strtotime($fee['due_date'])); ?></td>
                            <td class="center">
                                <?php
                                if ($fee['calculated_due'] <= 0) {
                                    echo '<span style="color: green; font-weight: bold;">পরিশোধিত</span>';
                                } elseif ($fee['calculated_paid'] > 0) {
                                    echo '<span style="color: orange; font-weight: bold;">আংশিক</span>';
                                } else {
                                    echo '<span style="color: red; font-weight: bold;">বকেয়া</span>';
                                }
                                ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="<?php echo ($reportType === 'student_wise') ? '7' : '10'; ?>" style="text-align: center; padding: 20px;">
                            কোন রেকর্ড পাওয়া যায়নি
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
            <tfoot>
                <tr class="memo-total">
                    <td colspan="<?php echo ($reportType === 'student_wise') ? '4' : '7'; ?>" style="text-align: right; font-weight: bold;">মোট:</td>
                    <td style="text-align: right; font-weight: bold;">
                        <div style="border-top: 2px solid #333; padding-top: 5px;">
                            মোট<br>
                            ৳ <?php echo number_format($totalAmount, 2); ?>
                        </div>
                    </td>
                    <td style="text-align: right; font-weight: bold;">
                        <div style="border-top: 2px solid #333; padding-top: 5px;">
                            প্রদান<br>
                            ৳ <?php echo number_format($totalPaid, 2); ?>
                        </div>
                    </td>
                    <td style="text-align: right; font-weight: bold;">
                        <div style="border-top: 2px solid #333; padding-top: 5px;">
                            বকেয়া<br>
                            ৳ <?php echo number_format($totalDue, 2); ?>
                        </div>
                    </td>
                    <td colspan="2"></td>
                </tr>
            </tfoot>
        </table>

        <div style="background: #e9ecef; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
            <div class="row">
                <div class="col-md-4">
                    <strong>মোট ফি:</strong><br>
                    <span style="font-size: 18px; color: #007bff;">৳ <?php echo number_format($totalAmount, 2); ?></span>
                </div>
                <div class="col-md-4">
                    <strong>মোট প্রদান:</strong><br>
                    <span style="font-size: 18px; color: #28a745;">৳ <?php echo number_format($totalPaid, 2); ?></span>
                </div>
                <div class="col-md-4">
                    <strong>মোট বকেয়া:</strong><br>
                    <span style="font-size: 18px; color: #dc3545;">৳ <?php echo number_format($totalDue, 2); ?></span>
                </div>
            </div>
        </div>

        <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 5px; margin-bottom: 20px;">
            <strong>গুরুত্বপূর্ণ নোট:</strong><br>
            • এই মেমো আপনার ফি হিসাবের সারসংক্ষেপ<br>
            • যেকোনো অসঙ্গতি থাকলে অবিলম্বে অফিসে যোগাযোগ করুন<br>
            • বকেয়া ফি যত তাড়াতাড়ি সম্ভব পরিশোধ করুন<br>
            • পেমেন্টের সময় এই মেমো সাথে আনুন
        </div>

        <div class="memo-footer">
            <div class="signature-box">
                <div class="signature-line">গ্রহীতার স্বাক্ষর</div>
            </div>
            <div class="signature-box">
                <div class="signature-line">অভিভাবকের স্বাক্ষর</div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 20px; font-size: 12px; color: #666;">
            এই মেমো কম্পিউটার দ্বারা তৈরি এবং স্বাক্ষরের প্রয়োজন নেই।<br>
            প্রিন্ট তারিখ: <?php echo date('d/m/Y h:i A'); ?><br>
            <strong>যেকোনো সমস্যার জন্য যোগাযোগ করুন: <?php echo htmlspecialchars($schoolPhone); ?></strong>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
