<?php
// Check PHP configuration for buffering issues
header('Content-Type: text/html; charset=UTF-8');

// Disable all output buffering
while (ob_get_level()) {
    ob_end_clean();
}

echo "<!DOCTYPE html>";
echo "<html><head><title>PHP Configuration Check</title></head><body>";
echo "<h1>PHP Configuration Check for Buffering Issues</h1>";

echo "<h2>🔍 Output Buffering Settings:</h2>";
echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
echo "<tr><th>Setting</th><th>Value</th><th>Status</th></tr>";

// Check output buffering
$output_buffering = ini_get('output_buffering');
echo "<tr><td>output_buffering</td><td>" . ($output_buffering ? $output_buffering : 'Off') . "</td>";
echo "<td style='color: " . ($output_buffering ? 'red' : 'green') . "'>" . ($output_buffering ? '❌ ON (Problem!)' : '✅ OFF (Good)') . "</td></tr>";

// Check implicit flush
$implicit_flush = ini_get('implicit_flush');
echo "<tr><td>implicit_flush</td><td>" . ($implicit_flush ? 'On' : 'Off') . "</td>";
echo "<td style='color: " . ($implicit_flush ? 'green' : 'red') . "'>" . ($implicit_flush ? '✅ ON (Good)' : '❌ OFF (Problem!)') . "</td></tr>";

// Check zlib compression
$zlib_compression = ini_get('zlib.output_compression');
echo "<tr><td>zlib.output_compression</td><td>" . ($zlib_compression ? 'On' : 'Off') . "</td>";
echo "<td style='color: " . ($zlib_compression ? 'red' : 'green') . "'>" . ($zlib_compression ? '❌ ON (Problem!)' : '✅ OFF (Good)') . "</td></tr>";

// Check current buffer level
$buffer_level = ob_get_level();
echo "<tr><td>Current Buffer Level</td><td>" . $buffer_level . "</td>";
echo "<td style='color: " . ($buffer_level > 0 ? 'red' : 'green') . "'>" . ($buffer_level > 0 ? '❌ ' . $buffer_level . ' (Problem!)' : '✅ 0 (Good)') . "</td></tr>";

echo "</table>";

echo "<h2>🌐 Server Information:</h2>";
echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";
echo "<tr><td>Server Software</td><td>" . $_SERVER['SERVER_SOFTWARE'] . "</td></tr>";
echo "<tr><td>PHP Version</td><td>" . PHP_VERSION . "</td></tr>";
echo "<tr><td>PHP SAPI</td><td>" . php_sapi_name() . "</td></tr>";
echo "<tr><td>Memory Limit</td><td>" . ini_get('memory_limit') . "</td></tr>";
echo "<tr><td>Max Execution Time</td><td>" . ini_get('max_execution_time') . "</td></tr>";
echo "</table>";

echo "<h2>🔧 Recommended Fixes:</h2>";
echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 5px;'>";

if ($output_buffering) {
    echo "<p style='color: red;'>❌ <strong>output_buffering is ON</strong> - This is likely causing the title bar buffering!</p>";
    echo "<p>Fix: Add <code>output_buffering = Off</code> to php.ini</p>";
}

if (!$implicit_flush) {
    echo "<p style='color: red;'>❌ <strong>implicit_flush is OFF</strong> - This can cause buffering issues!</p>";
    echo "<p>Fix: Add <code>implicit_flush = On</code> to php.ini</p>";
}

if ($zlib_compression) {
    echo "<p style='color: red;'>❌ <strong>zlib.output_compression is ON</strong> - This can cause buffering!</p>";
    echo "<p>Fix: Add <code>zlib.output_compression = Off</code> to php.ini</p>";
}

if ($buffer_level > 0) {
    echo "<p style='color: red;'>❌ <strong>Active output buffers detected!</strong></p>";
    echo "<p>Current buffer level: " . $buffer_level . "</p>";
}

echo "</div>";

echo "<h2>📁 PHP.ini Location:</h2>";
echo "<p><strong>Loaded Configuration File:</strong> " . php_ini_loaded_file() . "</p>";

$additional_ini = php_ini_scanned_files();
if ($additional_ini) {
    echo "<p><strong>Additional .ini files:</strong> " . $additional_ini . "</p>";
}

echo "<h2>🧪 Real-time Test:</h2>";
echo "<div id='test-area'>";
echo "<p>Testing output buffering in real-time...</p>";

// Force flush
flush();

echo "<p>If you see this text immediately without delay, buffering is working correctly.</p>";

// Test with small delay
for ($i = 1; $i <= 5; $i++) {
    echo "<p>Test line $i - " . date('H:i:s') . "</p>";
    flush();
    usleep(500000); // 0.5 second delay
}

echo "</div>";

echo "<h2>🔄 Actions:</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='minimal-index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Test Minimal Page</a>";
echo "<a href='index.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Test Main Page</a>";
echo "<a href='javascript:location.reload()' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Refresh This Page</a>";
echo "</div>";

echo "</body></html>";

// Final flush
flush();
if (function_exists('fastcgi_finish_request')) {
    fastcgi_finish_request();
}
?>
