<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database Connection
require_once 'includes/dbh.inc.php';

echo "<h1>Admin Login Debugging Tool</h1>";

// Admin credentials to test
$username = "admin";
$password = isset($_GET['password']) ? $_GET['password'] : "admin123";
$userType = "admin";

echo "<h3>Testing login for:</h3>";
echo "<p>Username: " . htmlspecialchars($username) . "</p>";
echo "<p>User Type: " . htmlspecialchars($userType) . "</p>";
echo "<p>Password: " . htmlspecialchars($password) . " (only visible for debugging)</p>";

// Database connection check
echo "<h3>Database Connection:</h3>";
if ($conn->connect_error) {
    echo "<p style='color:red;'>Connection failed: " . $conn->connect_error . "</p>";
} else {
    echo "<p style='color:green;'>Database connection successful!</p>";
}

// Check if user exists
echo "<h3>User Check:</h3>";
$sql = "SELECT * FROM users WHERE username=? AND user_type=?";
$stmt = $conn->prepare($sql);

if (!$stmt) {
    echo "<p style='color:red;'>Prepare failed: " . $conn->error . "</p>";
    exit;
}

$stmt->bind_param("ss", $username, $userType);

if (!$stmt->execute()) {
    echo "<p style='color:red;'>Execute failed: " . $stmt->error . "</p>";
    exit;
}

$result = $stmt->get_result();

if ($row = $result->fetch_assoc()) {
    echo "<p style='color:green;'>User found in database!</p>";
    echo "<p>User ID: " . $row['id'] . "</p>";
    echo "<p>Username: " . $row['username'] . "</p>";
    echo "<p>User Type: " . $row['user_type'] . "</p>";
    
    // Check password
    echo "<h3>Password Check:</h3>";
    $pwdCheck = password_verify($password, $row['password']);
    
    if ($pwdCheck) {
        echo "<p style='color:green;'>Password is correct!</p>";
        echo "<p>Login would succeed and redirect to admin/dashboard.php</p>";
    } else {
        echo "<p style='color:red;'>Password is incorrect!</p>";
        echo "<p>Stored password hash: " . $row['password'] . "</p>";
        echo "<p>Password algorithm info: </p>";
        echo "<pre>" . print_r(password_get_info($row['password']), true) . "</pre>";
    }
} else {
    echo "<p style='color:red;'>No user found with username: " . htmlspecialchars($username) . " and type: " . htmlspecialchars($userType) . "</p>";
    
    // Try to find the username without user_type to help debugging
    $checkUsername = "SELECT * FROM users WHERE username=?";
    $checkStmt = $conn->prepare($checkUsername);
    $checkStmt->bind_param("s", $username);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();
    
    if ($checkResult->num_rows > 0) {
        echo "<p>However, the username \"" . htmlspecialchars($username) . "\" exists with different user type(s):</p>";
        echo "<ul>";
        while ($checkRow = $checkResult->fetch_assoc()) {
            echo "<li>User Type: " . $checkRow['user_type'] . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>Username \"" . htmlspecialchars($username) . "\" does not exist in the database at all.</p>";
    }
}

// Check session related settings
echo "<h3>Session Settings:</h3>";
echo "<p>session.cookie_secure: " . ini_get('session.cookie_secure') . "</p>";
echo "<p>session.use_strict_mode: " . ini_get('session.use_strict_mode') . "</p>";
echo "<p>session.cookie_httponly: " . ini_get('session.cookie_httponly') . "</p>";
echo "<p>session.use_only_cookies: " . ini_get('session.use_only_cookies') . "</p>";

echo "<h3>Actions:</h3>";
echo "<p><a href='fix_admin.php'>Fix Admin User</a> | <a href='index.php'>Go to Login Page</a></p>";
echo "<p><a href='clear_session.php'>Clear Session Data</a></p>";

$stmt->close();
$conn->close();
?> 