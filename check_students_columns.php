<?php
// Database Connection
require_once 'includes/dbh.inc.php';

echo "<h1>Students Table Columns</h1>";

// Check if students table exists
$tableCheck = $conn->query("SHOW TABLES LIKE 'students'");
if ($tableCheck->num_rows == 0) {
    echo "<p style='color:red'>The students table does not exist!</p>";
} else {
    // Get all columns from the students table
    $columnsQuery = "SHOW COLUMNS FROM students";
    $columnsResult = $conn->query($columnsQuery);
    
    if ($columnsResult && $columnsResult->num_rows > 0) {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        while ($column = $columnsResult->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . ($column['Default'] === NULL ? 'NULL' : $column['Default']) . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        // Check specifically for department_id column
        $deptIdCheck = $conn->query("SHOW COLUMNS FROM students LIKE 'department_id'");
        if ($deptIdCheck->num_rows > 0) {
            echo "<p style='color:green'>The department_id column exists in the students table.</p>";
        } else {
            echo "<p style='color:red'>The department_id column does NOT exist in the students table!</p>";
        }
    } else {
        echo "<p style='color:red'>Error retrieving columns: " . $conn->error . "</p>";
    }
}

// Close connection
$conn->close();
?>
