<?php
session_start();
require_once '../includes/dbh.inc.php';
require_once '../includes/bkash_config.php';
require_once '../includes/bkash_functions.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Create bKash payments table
try {
    // Drop existing table if it exists
    $dropTableQuery = "DROP TABLE IF EXISTS bkash_payments";
    $conn->query($dropTableQuery);

    // Create table without foreign key constraint
    $createTableQuery = "CREATE TABLE IF NOT EXISTS bkash_payments (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        fee_id INT(11) NOT NULL,
        payment_id VARCHAR(100) NOT NULL,
        trx_id VARCHAR(100) NULL,
        amount DECIMAL(10,2) NOT NULL,
        status VARCHAR(50) NOT NULL,
        payer_reference VARCHAR(100) NULL,
        payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";

    if ($conn->query($createTableQuery)) {
        $_SESSION['success'] = 'বিকাশ পেমেন্ট টেবিল সফলভাবে তৈরি করা হয়েছে!';
    } else {
        $_SESSION['error'] = 'বিকাশ পেমেন্ট টেবিল তৈরি করতে সমস্যা হয়েছে: ' . $conn->error;
    }
} catch (Exception $e) {
    $_SESSION['error'] = 'বিকাশ পেমেন্ট টেবিল তৈরি করতে সমস্যা হয়েছে: ' . $e->getMessage();
}

// Redirect back to dashboard
header("Location: bkash_dashboard.php");
exit();
?>
