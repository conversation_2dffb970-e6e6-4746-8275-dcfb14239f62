<?php
// Fee management with login bypass for testing
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Bypass login for testing
if (isset($_GET['bypass']) && $_GET['bypass'] === 'test') {
    $_SESSION['userId'] = 999;
    $_SESSION['userType'] = 'admin';
    $_SESSION['username'] = 'test_admin';
    echo "<div style='background: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; border-radius: 5px;'>";
    echo "⚠️ <strong>Testing Mode:</strong> Login bypassed for testing purposes";
    echo "</div>";
}

require_once '../includes/dbh.inc.php';

// Check database connection
if ($conn->connect_error) {
    die("<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border-radius: 5px;'>❌ Database connection failed: " . $conn->connect_error . "</div>");
}

echo "<div style='background: #d1ecf1; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
echo "✅ Database connected successfully";
echo "</div>";

// Check if user is logged in
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    echo "<!DOCTYPE html><html><head><title>Login Required</title></head><body>";
    echo "<h1>Login Required</h1>";
    echo "<p>You need to login as admin to access this page.</p>";
    echo "<p><a href='../login.php'>Login Here</a> OR <a href='?bypass=test'>Bypass for Testing</a></p>";
    echo "</body></html>";
    exit();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_fee'])) {
    echo "<h2>🔄 Processing Fee Addition...</h2>";
    
    $studentIds = isset($_POST['student_ids']) ? $_POST['student_ids'] : [];
    $feeType = isset($_POST['fee_type']) ? $_POST['fee_type'] : '';
    $amount = isset($_POST['amount']) ? floatval($_POST['amount']) : 0;
    $dueDate = isset($_POST['due_date']) ? $_POST['due_date'] : date('Y-m-d');
    
    echo "<div style='background: #f0f8ff; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3>📊 Form Data Received:</h3>";
    echo "<ul>";
    echo "<li><strong>Student IDs:</strong> " . (empty($studentIds) ? "None" : implode(', ', $studentIds)) . "</li>";
    echo "<li><strong>Fee Type:</strong> " . htmlspecialchars($feeType) . "</li>";
    echo "<li><strong>Amount:</strong> " . $amount . "</li>";
    echo "<li><strong>Due Date:</strong> " . $dueDate . "</li>";
    echo "</ul>";
    echo "</div>";
    
    if (!empty($studentIds) && !empty($feeType) && $amount > 0) {
        echo "<div style='background: #e7f3ff; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>🔄 Starting Fee Addition Process...</h4>";
        echo "<p>Processing " . count($studentIds) . " students...</p>";
        echo "</div>";

        $successCount = 0;
        $errorCount = 0;

        foreach ($studentIds as $studentId) {
            $studentId = intval($studentId);
            echo "<div style='border-left: 3px solid #007bff; padding-left: 10px; margin: 10px 0;'>";
            echo "<h5>Processing Student ID: $studentId</h5>";

            // Get student info
            $studentQuery = "SELECT s.*, CONCAT(s.first_name, ' ', s.last_name) as full_name
                           FROM students s WHERE s.id = $studentId";
            echo "<p><strong>Query:</strong> $studentQuery</p>";

            $studentResult = $conn->query($studentQuery);

            if ($studentResult && $studentResult->num_rows > 0) {
                $student = $studentResult->fetch_assoc();
                echo "<p style='color: blue;'>📋 Student found: " . $student['full_name'] . "</p>";
                echo "<p>Session ID: " . ($student['session_id'] ?? 'NULL') . ", Class ID: " . ($student['class_id'] ?? 'NULL') . "</p>";

                // Check if fee already exists
                $checkQuery = "SELECT id FROM fees WHERE student_id = $studentId AND fee_type = '$feeType' AND due_date = '$dueDate'";
                echo "<p><strong>Duplicate Check:</strong> $checkQuery</p>";

                $checkResult = $conn->query($checkQuery);
                if ($checkResult && $checkResult->num_rows > 0) {
                    echo "<p style='color: orange;'>⚠️ Fee already exists for this student</p>";
                    $errorCount++;
                } else {
                    // Insert fee
                    $sessionId = $student['session_id'] ? $student['session_id'] : 1;
                    $classId = $student['class_id'] ? $student['class_id'] : 1;

                    $sql = "INSERT INTO fees (student_id, session_id, class_id, fee_type, amount, due_date, status, created_at)
                            VALUES ($studentId, $sessionId, $classId, '$feeType', $amount, '$dueDate', 'unpaid', NOW())";

                    echo "<p><strong>Insert Query:</strong> $sql</p>";

                    if ($conn->query($sql)) {
                        $insertId = $conn->insert_id;
                        echo "<p style='color: green;'>✅ Fee added successfully! Fee ID: $insertId for: " . $student['full_name'] . "</p>";
                        $successCount++;
                    } else {
                        echo "<p style='color: red;'>❌ SQL Error: " . $conn->error . "</p>";
                        echo "<p style='color: red;'>❌ Failed Query: $sql</p>";
                        $errorCount++;
                    }
                }
            } else {
                echo "<p style='color: red;'>❌ Student not found with ID: $studentId</p>";
                if ($conn->error) {
                    echo "<p style='color: red;'>❌ SQL Error: " . $conn->error . "</p>";
                }
                $errorCount++;
            }
            echo "</div>";
        }
        
        echo "<div style='background: #d4edda; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h3>📈 Summary:</h3>";
        echo "<ul>";
        echo "<li><strong>Successful:</strong> $successCount</li>";
        echo "<li><strong>Errors:</strong> $errorCount</li>";
        echo "</ul>";
        echo "</div>";

        // Redirect to prevent resubmission
        if ($successCount > 0) {
            echo "<script>";
            echo "setTimeout(function() {";
            echo "  window.location.href = '?success=" . $successCount . "&errors=" . $errorCount . "';";
            echo "}, 3000);";
            echo "</script>";
            echo "<p><em>Page will refresh in 3 seconds...</em></p>";
        }

    } else {
        echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h3>❌ Validation Errors:</h3>";
        echo "<ul>";
        if (empty($studentIds)) echo "<li>No students selected</li>";
        if (empty($feeType)) echo "<li>No fee type selected</li>";
        if ($amount <= 0) echo "<li>Invalid amount</li>";
        echo "</ul>";
        echo "</div>";
    }
}

// Show success message from redirect
if (isset($_GET['success'])) {
    $successCount = intval($_GET['success']);
    $errorCount = intval($_GET['errors']);
    echo "<div style='background: #d4edda; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3>✅ Operation Completed!</h3>";
    echo "<ul>";
    echo "<li><strong>Successfully added:</strong> $successCount fees</li>";
    if ($errorCount > 0) {
        echo "<li><strong>Errors:</strong> $errorCount</li>";
    }
    echo "</ul>";
    echo "</div>";
}

// Get data for form
$sessionsQuery = "SELECT * FROM sessions ORDER BY id DESC";
$sessionsResult = $conn->query($sessionsQuery);

$classesQuery = "SELECT * FROM classes ORDER BY class_name";
$classesResult = $conn->query($classesQuery);

$studentsQuery = "SELECT s.*, CONCAT(s.first_name, ' ', s.last_name) as full_name, c.class_name 
                  FROM students s 
                  LEFT JOIN classes c ON s.class_id = c.id 
                  ORDER BY s.first_name, s.last_name LIMIT 50";
$studentsResult = $conn->query($studentsQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <title>Fee Management Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: Arial, sans-serif; }
        .form-section { background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; }
    </style>
</head>
<body>

<div class="container mt-4">
    <h1><i class="fas fa-money-bill-wave me-2"></i>Fee Management Test</h1>
    
    <div class="form-section">
        <h2><i class="fas fa-plus-circle me-2"></i>Add Fee</h2>
        
        <form method="post" action="">
            <input type="hidden" name="add_fee" value="1">
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label class="form-label">Fee Type <span class="text-danger">*</span></label>
                    <select class="form-select" name="fee_type" required>
                        <option value="">Choose Fee Type</option>
                        <option value="tuition">Tuition Fee</option>
                        <option value="admission">Admission Fee</option>
                        <option value="exam">Exam Fee</option>
                        <option value="library">Library Fee</option>
                        <option value="transport">Transport Fee</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Amount (৳) <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" name="amount" min="0" step="0.01" value="1000" required>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Due Date <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" name="due_date" value="<?= date('Y-m-d', strtotime('+30 days')) ?>" required>
                </div>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Select Students <span class="text-danger">*</span></label>
                <div class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                    <?php if ($studentsResult && $studentsResult->num_rows > 0): ?>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="select_all">
                            <label class="form-check-label" for="select_all">
                                <strong>Select All Students</strong>
                            </label>
                        </div>
                        <hr>
                        <?php while ($student = $studentsResult->fetch_assoc()): ?>
                            <div class="form-check mb-2">
                                <input class="form-check-input student-checkbox" type="checkbox" 
                                       name="student_ids[]" value="<?= $student['id'] ?>" 
                                       id="student_<?= $student['id'] ?>">
                                <label class="form-check-label" for="student_<?= $student['id'] ?>">
                                    <?= htmlspecialchars($student['full_name']) ?> 
                                    (ID: <?= $student['id'] ?>) 
                                    - <?= htmlspecialchars($student['class_name'] ?? 'No Class') ?>
                                </label>
                            </div>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <p class="text-muted">No students found</p>
                    <?php endif; ?>
                </div>
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-plus-circle me-1"></i> Add Fee
            </button>
        </form>
    </div>
    
    <!-- Recent Fees -->
    <div class="form-section">
        <h2><i class="fas fa-list me-2"></i>Recent Fees</h2>
        <?php
        $recentFeesQuery = "SELECT f.*, CONCAT(s.first_name, ' ', s.last_name) as student_name, c.class_name 
                           FROM fees f 
                           LEFT JOIN students s ON f.student_id = s.id 
                           LEFT JOIN classes c ON f.class_id = c.id 
                           ORDER BY f.id DESC LIMIT 10";
        $recentFeesResult = $conn->query($recentFeesQuery);
        
        if ($recentFeesResult && $recentFeesResult->num_rows > 0):
        ?>
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Student</th>
                        <th>Class</th>
                        <th>Fee Type</th>
                        <th>Amount</th>
                        <th>Due Date</th>
                        <th>Status</th>
                        <th>Created</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while ($fee = $recentFeesResult->fetch_assoc()): ?>
                        <tr>
                            <td><?= $fee['id'] ?></td>
                            <td><?= htmlspecialchars($fee['student_name'] ?? 'N/A') ?></td>
                            <td><?= htmlspecialchars($fee['class_name'] ?? 'N/A') ?></td>
                            <td><?= htmlspecialchars($fee['fee_type']) ?></td>
                            <td>৳<?= number_format($fee['amount'], 2) ?></td>
                            <td><?= $fee['due_date'] ?></td>
                            <td>
                                <span class="badge bg-<?= $fee['status'] === 'paid' ? 'success' : 'warning' ?>">
                                    <?= ucfirst($fee['status']) ?>
                                </span>
                            </td>
                            <td><?= $fee['created_at'] ?></td>
                        </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        <?php else: ?>
            <p class="text-muted">No fees found</p>
        <?php endif; ?>
    </div>
    
    <div class="mt-4">
        <a href="fee_management.php" class="btn btn-success">Original Fee Management</a>
        <a href="error-check.php" class="btn btn-info">Error Check</a>
        <a href="../login.php" class="btn btn-warning">Login</a>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
// Select all functionality
document.getElementById('select_all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.student-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Update select all when individual checkboxes change
document.querySelectorAll('.student-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        const allCheckboxes = document.querySelectorAll('.student-checkbox');
        const checkedCheckboxes = document.querySelectorAll('.student-checkbox:checked');
        const selectAll = document.getElementById('select_all');
        
        selectAll.checked = allCheckboxes.length === checkedCheckboxes.length;
    });
});
</script>

</body>
</html>
