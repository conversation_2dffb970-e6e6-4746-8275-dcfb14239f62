<?php
session_start();
require_once '../includes/dbh.inc.php';
require_once '../includes/bkash_config.php';
require_once '../includes/bkash_functions.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Get students
$studentsQuery = "SELECT s.*, c.class_name 
                 FROM students s
                 LEFT JOIN classes c ON s.class_id = c.id
                 ORDER BY s.first_name, s.last_name";
$students = $conn->query($studentsQuery);

// Get fee types
$feeTypesQuery = "SELECT * FROM fee_types ORDER BY name";
$feeTypes = $conn->query($feeTypesQuery);

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $studentId = isset($_POST['student_id']) ? intval($_POST['student_id']) : 0;
    $feeType = isset($_POST['fee_type']) ? $_POST['fee_type'] : '';
    $amount = isset($_POST['amount']) ? floatval($_POST['amount']) : 0;
    $dueDate = isset($_POST['due_date']) ? $_POST['due_date'] : date('Y-m-d');
    
    // Validate input
    if ($studentId <= 0 || empty($feeType) || $amount <= 0) {
        $_SESSION['error'] = 'সমস্ত প্রয়োজনীয় ফিল্ড পূরণ করুন!';
    } else {
        // Check if fee already exists
        $checkQuery = "SELECT id FROM fees WHERE student_id = ? AND fee_type = ? AND due_date = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param('iss', $studentId, $feeType, $dueDate);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            // Fee already exists, use existing fee
            $feeId = $result->fetch_assoc()['id'];
            header("Location: bkash_payment.php?fee_id=$feeId");
            exit();
        } else {
            // Create new fee
            $paymentStatus = 'due';
            $insertQuery = "INSERT INTO fees (student_id, fee_type, amount, paid, due_date, payment_status) 
                           VALUES (?, ?, ?, 0, ?, ?)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param('isdss', $studentId, $feeType, $amount, $dueDate, $paymentStatus);
            
            if ($stmt->execute()) {
                $feeId = $conn->insert_id;
                header("Location: bkash_payment.php?fee_id=$feeId");
                exit();
            } else {
                $_SESSION['error'] = 'ফি তৈরি করতে সমস্যা: ' . $conn->error;
            }
        }
    }
}

// Include header
include_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Include sidebar -->
        <?php include_once 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-money-bill-wave me-2"></i> বিকাশ পেমেন্ট ফর্ম</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="bkash_dashboard.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i> ড্যাশবোর্ডে ফিরে যান
                        </a>
                    </div>
                </div>
            </div>
            
            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i> <?= $_SESSION['error'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>
            
            <div class="row">
                <div class="col-md-8 mx-auto">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-plus-circle me-2"></i> নতুন বিকাশ পেমেন্ট তৈরি করুন</h5>
                        </div>
                        <div class="card-body">
                            <form action="" method="POST">
                                <div class="mb-3">
                                    <label for="student_id" class="form-label">শিক্ষার্থী নির্বাচন করুন <span class="text-danger">*</span></label>
                                    <select class="form-select" id="student_id" name="student_id" required>
                                        <option value="">শিক্ষার্থী নির্বাচন করুন</option>
                                        <?php if ($students && $students->num_rows > 0): ?>
                                            <?php while ($student = $students->fetch_assoc()): ?>
                                                <option value="<?= $student['id'] ?>">
                                                    <?= htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) ?> 
                                                    (<?= $student['student_id'] ?>) - 
                                                    <?= htmlspecialchars($student['class_name'] ?? 'N/A') ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="fee_type" class="form-label">ফি টাইপ নির্বাচন করুন <span class="text-danger">*</span></label>
                                    <select class="form-select" id="fee_type" name="fee_type" required>
                                        <option value="">ফি টাইপ নির্বাচন করুন</option>
                                        <?php if ($feeTypes && $feeTypes->num_rows > 0): ?>
                                            <?php while ($feeType = $feeTypes->fetch_assoc()): ?>
                                                <option value="<?= htmlspecialchars($feeType['name']) ?>" data-amount="<?= $feeType['amount'] ?? 0 ?>">
                                                    <?= htmlspecialchars($feeType['name']) ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="amount" class="form-label">পরিমাণ (৳) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="amount" name="amount" min="1" step="0.01" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="due_date" class="form-label">শেষ তারিখ <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="due_date" name="due_date" value="<?= date('Y-m-d') ?>" required>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-arrow-right me-2"></i> বিকাশ পেমেন্টে যান
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-8 mx-auto">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> বিকাশ পেমেন্ট সম্পর্কে তথ্য</h5>
                        </div>
                        <div class="card-body">
                            <p>বিকাশ পেমেন্ট গেটওয়ে ব্যবহার করে আপনি সহজেই শিক্ষার্থীদের ফি সংগ্রহ করতে পারেন। এই পেজে আপনি:</p>
                            <ul>
                                <li>শিক্ষার্থী নির্বাচন করুন</li>
                                <li>ফি টাইপ নির্বাচন করুন</li>
                                <li>পরিমাণ নির্ধারণ করুন</li>
                                <li>শেষ তারিখ নির্ধারণ করুন</li>
                            </ul>
                            <p>তারপর "বিকাশ পেমেন্টে যান" বাটনে ক্লিক করুন। এটি আপনাকে বিকাশ পেমেন্ট পেজে নিয়ে যাবে যেখানে আপনি পেমেন্ট প্রসেস সম্পন্ন করতে পারবেন।</p>
                            
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i> <strong>দ্রষ্টব্য:</strong> বিকাশ পেমেন্ট গেটওয়ে ব্যবহার করতে আপনাকে অবশ্যই বিকাশ মার্চেন্ট অ্যাকাউন্ট এবং API ক্রেডেনশিয়ালস সেটআপ করতে হবে।
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-fill amount when fee type is selected
        const feeTypeSelect = document.getElementById('fee_type');
        const amountInput = document.getElementById('amount');
        
        feeTypeSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption && selectedOption.dataset.amount) {
                amountInput.value = selectedOption.dataset.amount;
            } else {
                amountInput.value = '';
            }
        });
    });
</script>

<?php
// Include footer
include_once 'includes/footer.php';
?>
