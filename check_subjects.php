<?php
// Include database connection
require_once "includes/dbh.inc.php";

// Check subjects table
echo "<h2>Subjects in Database</h2>";
$result = $conn->query("SELECT * FROM subjects");
if ($result && $result->num_rows > 0) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Name</th><th>Code</th><th>Department ID</th><th>Category</th><th>Active</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['subject_name'] . "</td>";
        echo "<td>" . $row['subject_code'] . "</td>";
        echo "<td>" . $row['department_id'] . "</td>";
        echo "<td>" . $row['category'] . "</td>";
        echo "<td>" . ($row['is_active'] ? 'Yes' : 'No') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>No subjects found in the database.</p>";
}

// Check subject_departments table
echo "<h2>Subject-Department Mappings</h2>";
$result = $conn->query("SELECT sd.*, s.subject_name, d.department_name 
                        FROM subject_departments sd
                        LEFT JOIN subjects s ON sd.subject_id = s.id
                        LEFT JOIN departments d ON sd.department_id = d.id");
if ($result && $result->num_rows > 0) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Subject ID</th><th>Subject Name</th><th>Department ID</th><th>Department Name</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['subject_id'] . "</td>";
        echo "<td>" . $row['subject_name'] . "</td>";
        echo "<td>" . $row['department_id'] . "</td>";
        echo "<td>" . $row['department_name'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>No subject-department mappings found.</p>";
}

// Check department_subject_types table if it exists
echo "<h2>Department Subject Types</h2>";
$tableExists = $conn->query("SHOW TABLES LIKE 'department_subject_types'")->num_rows > 0;
if ($tableExists) {
    $result = $conn->query("SELECT dst.*, s.subject_name, d.department_name, dst.subject_type 
                            FROM department_subject_types dst
                            LEFT JOIN subjects s ON dst.subject_id = s.id
                            LEFT JOIN departments d ON dst.department_id = d.id");
    if ($result && $result->num_rows > 0) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Subject ID</th><th>Subject Name</th><th>Department ID</th><th>Department Name</th><th>Subject Type</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['subject_id'] . "</td>";
            echo "<td>" . $row['subject_name'] . "</td>";
            echo "<td>" . $row['department_id'] . "</td>";
            echo "<td>" . $row['department_name'] . "</td>";
            echo "<td>" . $row['subject_type'] . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>No department subject types found.</p>";
    }
} else {
    echo "<p>The department_subject_types table does not exist.</p>";
}

// Close connection
$conn->close();
?>
