<?php
// Disable error display for production
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// Set headers early
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: 0');

// Start session
session_start();

// Log request for debugging
$studentId = isset($_GET['student_id']) ? intval($_GET['student_id']) : 0;
error_log("Student dues request for ID: $studentId");

// Basic validation
if ($studentId <= 0) {
    echo json_encode([
        'success' => false,
        'error' => 'Invalid student ID'
    ]);
    exit;
}

// Include database connection
try {
    require_once '../../includes/dbh.inc.php';
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Database connection error'
    ]);
    exit;
}

// Check database connection
if (!isset($conn) || $conn->connect_error) {
    echo json_encode([
        'success' => false,
        'error' => 'Database connection is not available'
    ]);
    exit;
}

try {
    // Get student information
    $studentQuery = "SELECT s.*, c.class_name, ss.session_name 
                    FROM students s
                    LEFT JOIN classes c ON s.class_id = c.id
                    LEFT JOIN sessions ss ON s.session_id = ss.id
                    WHERE s.id = ?";
    
    $stmt = $conn->prepare($studentQuery);
    if (!$stmt) {
        throw new Exception("Prepare failed: " . $conn->error);
    }
    
    $stmt->bind_param('i', $studentId);
    $stmt->execute();
    $studentResult = $stmt->get_result();
    
    if ($studentResult->num_rows === 0) {
        echo json_encode([
            'success' => false,
            'error' => 'Student not found'
        ]);
        exit;
    }
    
    $student = $studentResult->fetch_assoc();
    
    // Get all due fees for the student
    $feesQuery = "SELECT f.*, c.class_name, ss.session_name 
                 FROM fees f
                 LEFT JOIN students s ON f.student_id = s.id
                 LEFT JOIN classes c ON s.class_id = c.id
                 LEFT JOIN sessions ss ON s.session_id = ss.id
                 WHERE f.student_id = ? AND (f.payment_status = 'due' OR f.payment_status = 'partial')
                 ORDER BY f.due_date ASC";
    
    $stmt = $conn->prepare($feesQuery);
    if (!$stmt) {
        throw new Exception("Prepare fees query failed: " . $conn->error);
    }
    
    $stmt->bind_param('i', $studentId);
    $stmt->execute();
    $feesResult = $stmt->get_result();
    
    $fees = [];
    $totalDue = 0;
    
    while ($fee = $feesResult->fetch_assoc()) {
        $dueAmount = $fee['amount'] - $fee['paid'];
        $totalDue += $dueAmount;
        
        $fees[] = [
            'id' => $fee['id'],
            'fee_type' => $fee['fee_type'],
            'amount' => $fee['amount'],
            'paid' => $fee['paid'],
            'due' => $dueAmount,
            'due_date' => $fee['due_date'],
            'payment_status' => $fee['payment_status'],
            'class_name' => $fee['class_name'],
            'session_name' => $fee['session_name']
        ];
    }
    
    // Prepare response
    $response = [
        'success' => true,
        'student' => [
            'id' => $student['id'],
            'name' => $student['first_name'] . ' ' . $student['last_name'],
            'student_id' => $student['student_id'],
            'roll' => $student['roll'],
            'class_name' => $student['class_name'],
            'session_name' => $student['session_name']
        ],
        'fees' => $fees,
        'total_due' => $totalDue,
        'fees_count' => count($fees)
    ];
    
    // Send response
    echo json_encode($response);
    
} catch (Exception $e) {
    // Log the error
    error_log("Error in get_student_dues.php: " . $e->getMessage());
    
    // Send error response
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
}
