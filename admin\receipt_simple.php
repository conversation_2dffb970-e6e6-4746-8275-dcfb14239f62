<?php
session_start();
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$receiptNo = $_GET['receipt_no'] ?? '';
$payment = null;
$errorMessage = '';

if (empty($receiptNo)) {
    header("Location: payment_search.php");
    exit();
}

try {
    // Step 1: Get payment info
    $paymentQuery = "SELECT * FROM fee_payments WHERE receipt_no = ? LIMIT 1";
    $stmt = $conn->prepare($paymentQuery);
    $stmt->bind_param("s", $receiptNo);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        $errorMessage = 'রিসিপ্ট পাওয়া যায়নি!';
    } else {
        $payment = $result->fetch_assoc();
        
        // Initialize default values
        $payment['fee_type'] = 'Unknown Fee';
        $payment['student_name'] = 'Unknown Student';
        $payment['roll_no'] = 'N/A';
        $payment['class_name'] = 'N/A';
        
        // Step 2: Get fee and student info
        try {
            $feeQuery = "SELECT fee_type, student_id FROM fees WHERE id = ? LIMIT 1";
            $feeStmt = $conn->prepare($feeQuery);
            $feeStmt->bind_param("i", $payment['fee_id']);
            $feeStmt->execute();
            $feeResult = $feeStmt->get_result();
            
            if ($feeResult->num_rows > 0) {
                $feeData = $feeResult->fetch_assoc();
                $payment['fee_type'] = $feeData['fee_type'];
                
                // Get student info
                $studentQuery = "SELECT first_name, last_name, roll_no, student_id, class_id FROM students WHERE id = ? LIMIT 1";
                $studentStmt = $conn->prepare($studentQuery);
                $studentStmt->bind_param("i", $feeData['student_id']);
                $studentStmt->execute();
                $studentResult = $studentStmt->get_result();
                
                if ($studentResult->num_rows > 0) {
                    $studentData = $studentResult->fetch_assoc();
                    $payment['student_name'] = trim($studentData['first_name'] . ' ' . $studentData['last_name']);
                    $payment['roll_no'] = $studentData['roll_no'] ?? $studentData['student_id'] ?? 'N/A';
                    
                    // Get class name
                    if (!empty($studentData['class_id'])) {
                        $classQuery = "SELECT class_name FROM classes WHERE id = ? LIMIT 1";
                        $classStmt = $conn->prepare($classQuery);
                        $classStmt->bind_param("i", $studentData['class_id']);
                        $classStmt->execute();
                        $classResult = $classStmt->get_result();
                        
                        if ($classResult->num_rows > 0) {
                            $classData = $classResult->fetch_assoc();
                            $payment['class_name'] = $classData['class_name'];
                        }
                    }
                }
            }
        } catch (Exception $e) {
            // Keep default values
        }
    }
} catch (Exception $e) {
    $errorMessage = 'ডাটাবেস এরর: ' . $e->getMessage();
}

if (!empty($errorMessage)) {
    echo "<div style='text-align: center; padding: 50px;'>";
    echo "<h3>সমস্যা!</h3>";
    echo "<p>" . htmlspecialchars($errorMessage) . "</p>";
    echo "<a href='payment_search.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ফিরে যান</a>";
    echo "</div>";
    exit();
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>রিসিপ্ট - <?= htmlspecialchars($receiptNo) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: 'Hind Siliguri', Arial, sans-serif; 
            background-color: #f8f9fa;
        }
        .receipt-container { 
            max-width: 800px; 
            margin: 20px auto; 
            background: white; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.1); 
            overflow: hidden;
        }
        .receipt-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .receipt-body {
            padding: 30px;
        }
        .info-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .info-row:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: bold;
            color: #495057;
        }
        .info-value {
            color: #212529;
        }
        .amount-section {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            margin: 30px 0;
        }
        .amount-display {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .receipt-footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 2px dashed #dee2e6;
        }
        .print-section {
            background: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        @media print {
            body { background: white; margin: 0; padding: 0; }
            .no-print { display: none !important; }
            .receipt-container { box-shadow: none; margin: 0; }
        }
        .status-badge {
            background: #28a745;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Control Panel -->
    <div class="print-section no-print">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <h4><i class="fas fa-receipt me-2"></i>পেমেন্ট রিসিপ্ট</h4>
                <div>
                    <button onclick="window.print()" class="btn btn-primary me-2">
                        <i class="fas fa-print me-2"></i>প্রিন্ট করুন
                    </button>
                    <a href="payment_search.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>ফিরে যান
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Receipt Content -->
    <div class="receipt-container">
        <!-- Header -->
        <div class="receipt-header">
            <h2 class="mb-2">জাফর আহমদ ফতেহ আলী ওয়াকফ কলেজ</h2>
            <p class="mb-1">ঢাকা, বাংলাদেশ</p>
            <p class="mb-3">ফোন: ০১৭১২৩৪৫৬৭৮</p>
            <div class="status-badge">পেমেন্ট সম্পন্ন</div>
        </div>

        <!-- Body -->
        <div class="receipt-body">
            <!-- Receipt Info -->
            <div class="info-section">
                <h5 class="mb-3"><i class="fas fa-info-circle me-2"></i>রিসিপ্ট তথ্য</h5>
                <div class="info-row">
                    <span class="info-label">রিসিপ্ট নং:</span>
                    <span class="info-value"><?= htmlspecialchars($payment['receipt_no']) ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">পেমেন্ট তারিখ:</span>
                    <span class="info-value"><?= date('d/m/Y', strtotime($payment['payment_date'])) ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">পেমেন্ট মাধ্যম:</span>
                    <span class="info-value">
                        <?= $payment['payment_method'] === 'cash' ? 'নগদ' : htmlspecialchars($payment['payment_method']) ?>
                    </span>
                </div>
            </div>

            <!-- Student Info -->
            <div class="info-section">
                <h5 class="mb-3"><i class="fas fa-user-graduate me-2"></i>শিক্ষার্থী তথ্য</h5>
                <div class="info-row">
                    <span class="info-label">নাম:</span>
                    <span class="info-value"><?= htmlspecialchars($payment['student_name']) ?></span>
                </div>
                <?php if ($payment['roll_no'] !== 'N/A'): ?>
                <div class="info-row">
                    <span class="info-label">রোল নং:</span>
                    <span class="info-value"><?= htmlspecialchars($payment['roll_no']) ?></span>
                </div>
                <?php endif; ?>
                <?php if ($payment['class_name'] !== 'N/A'): ?>
                <div class="info-row">
                    <span class="info-label">শ্রেণী:</span>
                    <span class="info-value"><?= htmlspecialchars($payment['class_name']) ?></span>
                </div>
                <?php endif; ?>
            </div>

            <!-- Fee Info -->
            <div class="info-section">
                <h5 class="mb-3"><i class="fas fa-money-bill-wave me-2"></i>ফি তথ্য</h5>
                <div class="info-row">
                    <span class="info-label">ফি ধরন:</span>
                    <span class="info-value"><?= htmlspecialchars($payment['fee_type']) ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">পরিশোধিত পরিমাণ:</span>
                    <span class="info-value">৳<?= number_format($payment['amount'], 2) ?></span>
                </div>
            </div>

            <!-- Amount Display -->
            <div class="amount-section">
                <div class="amount-display">৳<?= number_format($payment['amount'], 2) ?></div>
                <p class="mb-0">পরিশোধিত পরিমাণ</p>
            </div>

            <?php if (!empty($payment['notes'])): ?>
            <!-- Notes -->
            <div class="info-section">
                <h5 class="mb-3"><i class="fas fa-sticky-note me-2"></i>নোট</h5>
                <p class="mb-0"><?= htmlspecialchars($payment['notes']) ?></p>
            </div>
            <?php endif; ?>
        </div>

        <!-- Footer -->
        <div class="receipt-footer">
            <p class="mb-2"><strong>ধন্যবাদ!</strong></p>
            <p class="mb-1">এই রিসিপ্টটি সংরক্ষণ করুন</p>
            <small class="text-muted">প্রিন্ট তারিখ: <?= date('d/m/Y H:i:s') ?></small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
