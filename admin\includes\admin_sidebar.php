<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'dashboard.php') ? 'active' : ''; ?>" href="dashboard.php">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    ড্যাশবোর্ড
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'students.php') ? 'active' : ''; ?>" href="students.php">
                    <i class="fas fa-user-graduate me-2"></i>
                    শিক্ষার্থী
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'teachers.php') ? 'active' : ''; ?>" href="teachers.php">
                    <i class="fas fa-chalkboard-teacher me-2"></i>
                    শিক্ষক
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'classes.php') ? 'active' : ''; ?>" href="classes.php">
                    <i class="fas fa-school me-2"></i>
                    শ্রেণী
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'subjects.php') ? 'active' : ''; ?>" href="subjects.php">
                    <i class="fas fa-book me-2"></i>
                    বিষয়
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'attendance.php') ? 'active' : ''; ?>" href="attendance.php">
                    <i class="fas fa-calendar-check me-2"></i>
                    উপস্থিতি
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'exams.php' || basename($_SERVER['PHP_SELF']) == 'manage_exams.php' || basename($_SERVER['PHP_SELF']) == 'add_exam.php' || basename($_SERVER['PHP_SELF']) == 'edit_exam.php') ? 'active' : ''; ?>" href="manage_exams.php">
                    <i class="fas fa-file-alt me-2"></i>
                    পরীক্ষা
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'results.php') ? 'active' : ''; ?>" href="results.php">
                    <i class="fas fa-chart-bar me-2"></i>
                    ফলাফল
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'student_shortcodes.php') ? 'active' : ''; ?>" href="student_shortcodes.php">
                    <i class="fas fa-hashtag me-2"></i>
                    শিক্ষার্থী সর্টকোড
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'generate_id_cards.php') ? 'active' : ''; ?>" href="generate_id_cards.php">
                    <i class="fas fa-id-card me-2"></i>
                    শিক্ষার্থী আইডি কার্ড
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'notices.php') ? 'active' : ''; ?>" href="notices.php">
                    <i class="fas fa-bullhorn me-2"></i>
                    নোটিশ
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'settings.php') ? 'active' : ''; ?>" href="settings.php">
                    <i class="fas fa-cog me-2"></i>
                    সেটিংস
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'upload_content.php') ? 'active' : ''; ?>" href="../upload_content.php">
                    <i class="fas fa-upload me-2"></i>
                    কনটেন্ট আপলোড
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'subject_csv_upload.php') ? 'active' : ''; ?>" href="subject_csv_upload.php">
                    <i class="fas fa-file-csv me-2"></i>
                    বিষয় CSV আপলোড
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'csv_upload.php') ? 'active' : ''; ?>" href="csv_upload.php">
                    <i class="fas fa-upload me-2"></i>
                    CSV আপলোড
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'update_features.php') ? 'active' : ''; ?>" href="../update_features.php">
                    <i class="fas fa-cogs me-2"></i>
                    বৈশিষ্ট্য আপডেট
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'update_about.php') ? 'active' : ''; ?>" href="../update_about.php">
                    <i class="fas fa-info-circle me-2"></i>
                    আমাদের সম্পর্কে আপডেট
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'manage_gb_members.php') ? 'active' : ''; ?>" href="manage_gb_members.php">
                    <i class="fas fa-users me-2"></i>
                    পরিচালনা বোর্ড
                </a>
            </li>
        </ul>

        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>রিপোর্ট</span>
        </h6>
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'attendance_report.php') ? 'active' : ''; ?>" href="attendance_report.php">
                    <i class="fas fa-clipboard-list me-2"></i>
                    উপস্থিতি রিপোর্ট
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'result_report.php') ? 'active' : ''; ?>" href="result_report.php">
                    <i class="fas fa-chart-line me-2"></i>
                    ফলাফল রিপোর্ট
                </a>
            </li>

        </ul>
    </div>
</nav>
