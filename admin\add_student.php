<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Define upload directory for profile photos
$upload_dir = "../uploads/profile_photos/";

// Create directory if it doesn't exist
if (!file_exists($upload_dir)) {
    mkdir($upload_dir, 0777, true);
}

// Check if role column exists in users table
$check_role_column = $conn->query("SHOW COLUMNS FROM users LIKE 'role'");
if ($check_role_column->num_rows == 0) {
    $conn->query("ALTER TABLE users ADD COLUMN role VARCHAR(20) NOT NULL DEFAULT 'student'");
}

// Check if role column exists in students table
$check_student_role_column = $conn->query("SHOW COLUMNS FROM students LIKE 'role'");
if ($check_student_role_column->num_rows == 0) {
    $conn->query("ALTER TABLE students ADD COLUMN role VARCHAR(50) NULL");
}

// Check if roll_number column exists in students table
$check_roll_number_column = $conn->query("SHOW COLUMNS FROM students LIKE 'roll_number'");
if ($check_roll_number_column->num_rows == 0) {
    $conn->query("ALTER TABLE students ADD COLUMN roll_number VARCHAR(20) NULL");
}

// Check if guardian and parent columns exist in the students table and add if they don't
$additional_columns = [
    // Guardian columns
    "guardian_name" => "VARCHAR(100)",
    "guardian_relation" => "VARCHAR(50)",
    "guardian_phone" => "VARCHAR(20)",
    "guardian_email" => "VARCHAR(100)",
    "guardian_address" => "TEXT",
    "guardian_occupation" => "VARCHAR(100)",

    // Father's information
    "father_name" => "VARCHAR(100)",
    "father_phone" => "VARCHAR(20)",
    "father_email" => "VARCHAR(100)",
    "father_occupation" => "VARCHAR(100)",
    "father_income" => "VARCHAR(50)",

    // Mother's information
    "mother_name" => "VARCHAR(100)",
    "mother_phone" => "VARCHAR(20)",
    "mother_email" => "VARCHAR(100)",
    "mother_occupation" => "VARCHAR(100)",
    "mother_income" => "VARCHAR(50)"
];

foreach ($additional_columns as $column => $type) {
    $check_column = $conn->query("SHOW COLUMNS FROM students LIKE '$column'");
    if ($check_column->num_rows == 0) {
        $conn->query("ALTER TABLE students ADD $column $type");
    }
}

// Check if profile_photo column exists in students table
$check_column = $conn->query("SHOW COLUMNS FROM students LIKE 'profile_photo'");
if ($check_column->num_rows == 0) {
    $conn->query("ALTER TABLE students ADD profile_photo VARCHAR(255)");
}

// Fetch sessions for dropdown
$sessionsQuery = $conn->query("SELECT id, session_name as name FROM sessions ORDER BY session_name");
$sessionList = [];
while ($row = $sessionsQuery->fetch_assoc()) {
    $sessionList[$row['id']] = $row['name'];
}

// Fetch departments for dropdown
$departmentsQuery = $conn->query("SELECT id, department_name FROM departments ORDER BY department_name");
$departmentList = [];
while ($row = $departmentsQuery->fetch_assoc()) {
    $departmentList[$row['id']] = $row['department_name'];
}

// Fetch classes for dropdown (prevent duplicates)
$classesQuery = $conn->query("SELECT DISTINCT id, class_name FROM classes ORDER BY class_name");
$classList = [];
$seenClasses = [];
while ($row = $classesQuery->fetch_assoc()) {
    // Prevent duplicate class names
    if (!in_array($row['class_name'], $seenClasses)) {
        $classList[$row['id']] = $row['class_name'];
        $seenClasses[] = $row['class_name'];
    }
}

$errorMessage = '';
$successMessage = '';

// Check for CSV upload messages from session
if (isset($_SESSION['csv_success'])) {
    $successMessage = $_SESSION['csv_success'];
    unset($_SESSION['csv_success']);
}
if (isset($_SESSION['csv_error'])) {
    $errorMessage = $_SESSION['csv_error'];
    unset($_SESSION['csv_error']);
}

// Helper function to fix date formats
function fixDateFormat($date) {
    if (empty($date)) return $date;

    // Remove any extra spaces
    $date = trim($date);

    // Handle Excel date formats like "15-Jan-2000", "20-Mar-2001"
    if (preg_match('/^(\d{1,2})-([A-Za-z]{3})-(\d{2,4})$/', $date, $matches)) {
        $day = $matches[1];
        $month = $matches[2];
        $year = $matches[3];

        // Convert month name to number
        $months = [
            'Jan' => '01', 'Feb' => '02', 'Mar' => '03', 'Apr' => '04',
            'May' => '05', 'Jun' => '06', 'Jul' => '07', 'Aug' => '08',
            'Sep' => '09', 'Oct' => '10', 'Nov' => '11', 'Dec' => '12'
        ];

        if (isset($months[$month])) {
            // Handle 2-digit years
            if (strlen($year) == 2) {
                $year = (intval($year) < 50) ? '20' . $year : '19' . $year;
            }
            return sprintf('%04d-%02d-%02d', $year, $months[$month], $day);
        }
    }

    // Try different date formats and convert to YYYY-MM-DD
    $formats = [
        'd-m-y',    // 15-01-00
        'd-m-Y',    // 15-01-2000
        'd/m/y',    // 15/01/00
        'd/m/Y',    // 15/01/2000
        'm-d-y',    // 01-15-00
        'm-d-Y',    // 01-15-2000
        'm/d/y',    // 01/15/00
        'm/d/Y',    // 01/15/2000
        'Y-m-d',    // 2000-01-15 (already correct)
        'Y/m/d',    // 2000/01/15
    ];

    foreach ($formats as $format) {
        $dateObj = DateTime::createFromFormat($format, $date);
        if ($dateObj !== false) {
            // Handle 2-digit years
            $year = $dateObj->format('Y');
            if ($year < 1950) {
                $dateObj->modify('+100 years');
            }
            return $dateObj->format('Y-m-d');
        }
    }

    // If no format matches, return original
    return $date;
}

// Handle CSV template download
if (isset($_GET['download_template'])) {
    $filename = 'student_template.csv';

    // CSV headers
    $headers = [
        'student_id', 'roll_number', 'first_name', 'last_name', 'email', 'phone', 'address',
        'dob', 'gender', 'batch', 'admission_date', 'department_id', 'class_id', 'session_id',
        'username', 'password', 'guardian_name', 'guardian_relation', 'guardian_phone',
        'guardian_email', 'guardian_address', 'guardian_occupation', 'father_name',
        'father_phone', 'father_email', 'father_occupation', 'father_income',
        'mother_name', 'mother_phone', 'mother_email', 'mother_occupation', 'mother_income'
    ];

    // Sample data with both Bengali and English support (FIXED FORMAT)
    $sample_data = [
        'STD-123456', '001', 'রহিম', 'আহমেদ', '<EMAIL>', '01712345678',
        'ঢাকা, বাংলাদেশ', '2000-01-15', 'Male', '2024', '2024-01-01', '1', '1', '1',
        'rahim123', 'password123', 'করিম আহমেদ', 'Father', '01712345679',
        '<EMAIL>', 'ঢাকা, বাংলাদেশ', 'ব্যবসায়ী', 'করিম আহমেদ',
        '01712345679', '<EMAIL>', 'ব্যবসায়ী', '50000', 'ফাতেমা বেগম',
        '01712345680', '<EMAIL>', 'গৃহিণী', '0'
    ];

    // Add second sample with English data (FIXED FORMAT)
    $sample_data_2 = [
        'STD-123457', '002', 'John', 'Doe', '<EMAIL>', '01812345678',
        'Dhaka, Bangladesh', '2001-03-20', 'Male', '2024', '2024-01-01', '1', '1', '1',
        'john123', 'password123', 'Robert Doe', 'Father', '01812345679',
        '<EMAIL>', 'Dhaka, Bangladesh', 'Engineer', 'Robert Doe',
        '01812345679', '<EMAIL>', 'Engineer', '60000', 'Mary Doe',
        '01812345680', '<EMAIL>', 'Teacher', '30000'
    ];

    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');

    $output = fopen('php://output', 'w');

    // Add UTF-8 BOM for proper Excel support
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

    // Write headers
    fputcsv($output, $headers);

    // Write sample data (Bengali)
    fputcsv($output, $sample_data);

    // Write sample data (English)
    fputcsv($output, $sample_data_2);

    fclose($output);
    exit();
}

// Handle Quick Test CSV
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['quick_test'])) {
    $test_csv_path = __DIR__ . '/simple_test.csv';
    if (file_exists($test_csv_path)) {
        // Simulate file upload
        $_FILES['csv_file'] = [
            'name' => 'simple_test.csv',
            'tmp_name' => $test_csv_path,
            'error' => UPLOAD_ERR_OK,
            'size' => filesize($test_csv_path),
            'type' => 'text/csv'
        ];
        $_POST['upload_csv'] = true;
    } else {
        $_SESSION['csv_error'] = 'টেস্ট ফাইল পাওয়া যায়নি।';
        header("Location: " . $_SERVER['PHP_SELF']);
        exit();
    }
}

// Handle CSV upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['upload_csv'])) {
    // Debug: Check if file was uploaded
    if (!isset($_FILES['csv_file'])) {
        $errorMessage = 'কোন ফাইল আপলোড করা হয়নি।';
    } elseif ($_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
        // Handle different upload errors
        switch ($_FILES['csv_file']['error']) {
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                $errorMessage = 'ফাইল সাইজ অনেক বড়। ছোট ফাইল ব্যবহার করুন।';
                break;
            case UPLOAD_ERR_PARTIAL:
                $errorMessage = 'ফাইল আংশিকভাবে আপলোড হয়েছে। আবার চেষ্টা করুন।';
                break;
            case UPLOAD_ERR_NO_FILE:
                $errorMessage = 'কোন ফাইল নির্বাচন করা হয়নি।';
                break;
            case UPLOAD_ERR_NO_TMP_DIR:
                $errorMessage = 'সার্ভার ত্রুটি: টেম্পোরারি ডিরেক্টরি নেই।';
                break;
            case UPLOAD_ERR_CANT_WRITE:
                $errorMessage = 'সার্ভার ত্রুটি: ফাইল লিখতে পারছে না।';
                break;
            default:
                $errorMessage = 'ফাইল আপলোড করতে সমস্যা হয়েছে।';
                break;
        }
    } elseif ($_FILES['csv_file']['size'] == 0) {
        $errorMessage = 'ফাইলটি খালি। বৈধ CSV ফাইল আপলোড করুন।';
    } else {
        $file_extension = strtolower(pathinfo($_FILES['csv_file']['name'], PATHINFO_EXTENSION));

        if ($file_extension !== 'csv') {
            $errorMessage = 'অবৈধ ফাইল টাইপ। শুধুমাত্র CSV ফাইল অনুমোদিত।';
        } else {
            $csv_file = $_FILES['csv_file']['tmp_name'];

            // Read file content and handle UTF-8 BOM
            $content = file_get_contents($csv_file);
            if (substr($content, 0, 3) === "\xEF\xBB\xBF") {
                $content = substr($content, 3); // Remove UTF-8 BOM
            }

            // Create temporary file with cleaned content
            $temp_file = tempnam(sys_get_temp_dir(), 'csv_upload');
            file_put_contents($temp_file, $content);

            $handle = fopen($temp_file, 'r');

            if ($handle !== FALSE) {
                $headers = fgetcsv($handle); // Read headers
                $success_count = 0;
                $error_count = 0;
                $duplicate_count = 0;
                $errors = [];
                $duplicates = [];

                // Debug: Check if headers were read properly
                if (empty($headers)) {
                    $errorMessage = 'CSV ফাইলে কোন হেডার পাওয়া যায়নি। ফাইলটি সঠিক কিনা চেক করুন।';
                    fclose($handle);
                    unlink($temp_file);
                    return;
                }

                // Clean BOM from first header if present
                if (!empty($headers[0])) {
                    $headers[0] = preg_replace('/^\xEF\xBB\xBF/', '', $headers[0]);
                    $headers[0] = trim($headers[0], "\xEF\xBB\xBF\x00..\x20"); // Remove BOM and whitespace
                }

                $conn->begin_transaction();

                try {
                    $row_number = 1; // Start from 1 (header row)
                    $total_rows_processed = 0;

                    // Debug mode - uncomment to see detailed processing
                    $debug_mode = false; // Set to true for debugging

                    while (($data = fgetcsv($handle)) !== FALSE) {
                        $row_number++;
                        $total_rows_processed++;

                        if ($debug_mode) {
                            error_log("Processing row $row_number with " . count($data) . " columns");
                            error_log("Row data: " . implode(' | ', array_slice($data, 0, 16)));
                        }

                        // Skip empty rows
                        if (empty(array_filter($data))) {
                            if ($debug_mode) {
                                error_log("Skipping empty row $row_number");
                            }
                            continue;
                        }

                        // Ensure minimum data length (we only need first few mandatory fields)
                        if (count($data) < 11) { // Minimum required fields count (reduced from 12 to 11)
                            $error_count++;
                            $errors[] = "সারি $row_number: অপর্যাপ্ত ডেটা (কমপক্ষে ১১টি কলাম প্রয়োজন, পাওয়া গেছে " . count($data) . "টি)";
                            continue;
                        }

                        // Map CSV data to variables with proper trimming and null handling
                        $student_id = !empty(trim($data[0])) ? trim($data[0]) : '';
                        $roll_number = !empty(trim($data[1])) ? trim($data[1]) : null;
                        $first_name = !empty(trim($data[2])) ? trim($data[2]) : '';
                        $last_name = !empty(trim($data[3])) ? trim($data[3]) : '';
                        $email = !empty(trim($data[4])) ? trim($data[4]) : null;
                        $phone = !empty(trim($data[5])) ? trim($data[5]) : '';
                        $address = !empty(trim($data[6])) ? trim($data[6]) : '';
                        $dob = !empty(trim($data[7])) ? trim($data[7]) : '';
                        $gender = !empty(trim($data[8])) ? trim($data[8]) : '';
                        $batch = !empty(trim($data[9])) ? trim($data[9]) : '';
                        $admission_date = !empty(trim($data[10])) ? trim($data[10]) : '';
                        $department_id = !empty(trim($data[11])) ? intval($data[11]) : 0;
                        $class_id = !empty(trim($data[12])) ? intval($data[12]) : null;
                        $session_id = !empty(trim($data[13])) ? intval($data[13]) : null;
                        $username = !empty(trim($data[14])) ? trim($data[14]) : '';
                        $password = !empty(trim($data[15])) ? trim($data[15]) : '';

                        // Auto-fix phone number (add 01 prefix if missing)
                        if (!empty($phone)) {
                            // Remove any spaces or special characters
                            $phone = preg_replace('/[^0-9]/', '', $phone);

                            // Add 01 prefix if missing and length is 9
                            if (!preg_match('/^01/', $phone) && strlen($phone) == 9) {
                                $phone = '01' . $phone;
                            }

                            // Handle 10-digit numbers starting with 1 (add 0 prefix)
                            if (preg_match('/^1[3-9]\d{8}$/', $phone)) {
                                $phone = '0' . $phone;
                            }
                        }

                        // Auto-fix date formats
                        if (!empty($dob)) {
                            $dob = fixDateFormat($dob);
                        }
                        if (!empty($admission_date)) {
                            $admission_date = fixDateFormat($admission_date);
                        }

                        // Guardian and parent info (optional) - handle safely
                        $guardian_name = (isset($data[16]) && !empty(trim($data[16]))) ? trim($data[16]) : null;
                        $guardian_relation = (isset($data[17]) && !empty(trim($data[17]))) ? trim($data[17]) : null;
                        $guardian_phone = (isset($data[18]) && !empty(trim($data[18]))) ? trim($data[18]) : null;
                        $guardian_email = (isset($data[19]) && !empty(trim($data[19]))) ? trim($data[19]) : null;
                        $guardian_address = (isset($data[20]) && !empty(trim($data[20]))) ? trim($data[20]) : null;
                        $guardian_occupation = (isset($data[21]) && !empty(trim($data[21]))) ? trim($data[21]) : null;
                        $father_name = (isset($data[22]) && !empty(trim($data[22]))) ? trim($data[22]) : null;
                        $father_phone = (isset($data[23]) && !empty(trim($data[23]))) ? trim($data[23]) : null;
                        $father_email = (isset($data[24]) && !empty(trim($data[24]))) ? trim($data[24]) : null;
                        $father_occupation = (isset($data[25]) && !empty(trim($data[25]))) ? trim($data[25]) : null;
                        $father_income = (isset($data[26]) && !empty(trim($data[26]))) ? trim($data[26]) : null;
                        $mother_name = (isset($data[27]) && !empty(trim($data[27]))) ? trim($data[27]) : null;
                        $mother_phone = (isset($data[28]) && !empty(trim($data[28]))) ? trim($data[28]) : null;
                        $mother_email = (isset($data[29]) && !empty(trim($data[29]))) ? trim($data[29]) : null;
                        $mother_occupation = (isset($data[30]) && !empty(trim($data[30]))) ? trim($data[30]) : null;
                        $mother_income = (isset($data[31]) && !empty(trim($data[31]))) ? trim($data[31]) : null;

                        // Validate ONLY mandatory fields
                        $mandatory_fields = [
                            'student_id' => $student_id,
                            'first_name' => $first_name,
                            'last_name' => $last_name,
                            'phone' => $phone,
                            'address' => $address,
                            'dob' => $dob,
                            'gender' => $gender,
                            'batch' => $batch,
                            'admission_date' => $admission_date,
                            'department_id' => $department_id,
                            'username' => $username,
                            'password' => $password
                        ];

                        $missing_fields = [];
                        foreach ($mandatory_fields as $field_name => $field_value) {
                            if (empty($field_value)) {
                                $missing_fields[] = $field_name;
                            }
                        }

                        if (!empty($missing_fields)) {
                            $error_count++;
                            // Debug: Show actual values for troubleshooting
                            $debug_info = [];
                            foreach ($mandatory_fields as $field_name => $field_value) {
                                $debug_info[] = "$field_name: '" . (empty($field_value) ? 'EMPTY' : substr($field_value, 0, 20)) . "'";
                            }
                            $errors[] = "সারি $row_number: প্রয়োজনীয় ফিল্ড অনুপস্থিত (" . implode(', ', $missing_fields) . ") - Student ID: '$student_id'<br><small>ডিবাগ: " . implode(', ', array_slice($debug_info, 0, 6)) . "</small>";
                            continue;
                        }

                        // Debug: Log successful validation
                        // error_log("Row $row_number validated successfully for student: $student_id");

                        // Additional validation for department_id (must be greater than 0)
                        if ($department_id <= 0) {
                            $error_count++;
                            $errors[] = "সারি $row_number: বৈধ department_id প্রয়োজন (পাওয়া গেছে: '$department_id')";
                            continue;
                        }

                        // Check if student ID already exists (DUPLICATE CHECK)
                        $check_id = $conn->prepare("SELECT id FROM students WHERE student_id = ?");
                        $check_id->bind_param("s", $student_id);
                        $check_id->execute();
                        if ($check_id->get_result()->num_rows > 0) {
                            $duplicate_count++;
                            $duplicates[] = "সারি $row_number: শিক্ষার্থী আইডি '$student_id' ইতিমধ্যে বিদ্যমান (স্কিপ করা হয়েছে)";
                            continue; // Skip duplicate, don't count as error
                        }

                        // Check if username already exists
                        $check_username = $conn->prepare("SELECT id FROM users WHERE username = ?");
                        $check_username->bind_param("s", $username);
                        $check_username->execute();
                        if ($check_username->get_result()->num_rows > 0) {
                            $error_count++;
                            $errors[] = "সারি $row_number: ইউজারনেম '$username' ইতিমধ্যে বিদ্যমান";
                            continue;
                        }

                        // Insert into users table
                        $user_stmt = $conn->prepare("INSERT INTO users (username, password, role) VALUES (?, ?, 'student')");
                        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                        $user_stmt->bind_param("ss", $username, $hashed_password);
                        $user_stmt->execute();
                        $user_id = $conn->insert_id;

                        // Prepare student data
                        $values = [
                            'user_id' => $user_id,
                            'student_id' => $student_id,
                            'roll_number' => $roll_number,
                            'first_name' => $first_name,
                            'last_name' => $last_name,
                            'email' => $email,
                            'phone' => $phone,
                            'address' => $address,
                            'dob' => $dob,
                            'gender' => $gender,
                            'batch' => $batch,
                            'admission_date' => $admission_date,
                            'department_id' => $department_id,
                            'class_id' => $class_id,
                            'session_id' => $session_id,
                            'profile_photo' => null,
                            'guardian_name' => $guardian_name,
                            'guardian_relation' => $guardian_relation,
                            'guardian_phone' => $guardian_phone,
                            'guardian_email' => $guardian_email,
                            'guardian_address' => $guardian_address,
                            'guardian_occupation' => $guardian_occupation,
                            'father_name' => $father_name,
                            'father_phone' => $father_phone,
                            'father_email' => $father_email,
                            'father_occupation' => $father_occupation,
                            'father_income' => $father_income,
                            'mother_name' => $mother_name,
                            'mother_phone' => $mother_phone,
                            'mother_email' => $mother_email,
                            'mother_occupation' => $mother_occupation,
                            'mother_income' => $mother_income,
                            'role' => null
                        ];

                        // Create the columns and placeholders parts of the query
                        $columns = implode(', ', array_keys($values));

                        $placeholders = [];
                        foreach ($values as $value) {
                            if ($value === NULL) {
                                $placeholders[] = "NULL";
                            } else {
                                $placeholders[] = "'" . $conn->real_escape_string($value) . "'";
                            }
                        }
                        $placeholderString = implode(', ', $placeholders);

                        // Insert student
                        $insertQuery = "INSERT INTO students ($columns) VALUES ($placeholderString)";
                        if ($conn->query($insertQuery)) {
                            $success_count++;
                        } else {
                            $error_count++;
                            $errors[] = "সারি $row_number: ডেটাবেস ত্রুটি - " . $conn->error;
                        }
                    }

                    $conn->commit();

                    // Prepare success message
                    $message_parts = [];
                    if ($success_count > 0) {
                        $message_parts[] = "$success_count জন শিক্ষার্থী সফলভাবে যোগ করা হয়েছে";
                    }
                    if ($duplicate_count > 0) {
                        $message_parts[] = "$duplicate_count টি ডুপ্লিকেট ডেটা স্কিপ করা হয়েছে";
                    }
                    if ($error_count > 0) {
                        $message_parts[] = "$error_count টি ত্রুটি হয়েছে";
                    }

                    if (!empty($message_parts)) {
                        $successMessage = implode(", ", $message_parts) . "। (মোট $total_rows_processed টি সারি প্রসেস করা হয়েছে)";
                    } else {
                        if ($total_rows_processed == 0) {
                            $errorMessage = "CSV ফাইলে কোন ডেটা পাওয়া যায়নি। ফাইলটি সঠিক কিনা চেক করুন।";
                        } else {
                            $errorMessage = "CSV ফাইল প্রসেস করা হয়েছে কিন্তু কোন বৈধ ডেটা পাওয়া যায়নি। (মোট $total_rows_processed টি সারি প্রসেস করা হয়েছে)";
                        }
                    }

                    // Prepare error message
                    $all_messages = array_merge($duplicates, $errors);
                    if (!empty($all_messages)) {
                        $errorMessage = "বিস্তারিত:<br>" . implode("<br>", array_slice($all_messages, 0, 15));
                        if (count($all_messages) > 15) {
                            $errorMessage .= "<br>... এবং আরও " . (count($all_messages) - 15) . " টি বার্তা";
                        }
                    }

                    // Store messages in session and redirect to avoid form resubmission
                    if (!empty($successMessage)) {
                        $_SESSION['csv_success'] = $successMessage;
                    }
                    if (!empty($errorMessage)) {
                        $_SESSION['csv_error'] = $errorMessage;
                    }

                    header("Location: " . $_SERVER['PHP_SELF']);
                    exit();

                } catch (Exception $e) {
                    $conn->rollback();
                    $_SESSION['csv_error'] = 'CSV আপলোড করতে সমস্যা হয়েছে: ' . $e->getMessage();
                    fclose($handle);
                    unlink($temp_file);
                    header("Location: " . $_SERVER['PHP_SELF']);
                    exit();
                }

                fclose($handle);
                unlink($temp_file); // Clean up temporary file
            } else {
                $_SESSION['csv_error'] = 'CSV ফাইল পড়তে সমস্যা হয়েছে।';
                header("Location: " . $_SERVER['PHP_SELF']);
                exit();
            }
        }
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_POST['upload_csv']) && !isset($_POST['download_template'])) {
    // Validate form input
    $required_fields = ['student_id', 'first_name', 'last_name', 'phone', 'address',
                       'dob', 'gender', 'batch', 'admission_date', 'department_id',
                       'username', 'password'];

    $missing_fields = [];
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            $missing_fields[] = $field;
        }
    }

    if (!empty($missing_fields)) {
        $errorMessage = 'সমস্ত প্রয়োজনীয় ফিল্ড পূরণ করুন। ইমেইল, সেশন, ক্লাস, প্রোফাইল ছবি এবং অভিভাবকের তথ্য বাদে অন্যান্য সব ফিল্ড প্রয়োজনীয়।';
    } else {
        // Validate student ID is unique
        $check_id = $conn->prepare("SELECT id FROM students WHERE student_id = ? AND id != ?");
        $student_id = $_POST['student_id'];
        $id = 0; // for new student
        $check_id->bind_param("si", $student_id, $id);
        $check_id->execute();
        $id_result = $check_id->get_result();

        if ($id_result->num_rows > 0) {
            $errorMessage = 'শিক্ষার্থী আইডি ইতিমধ্যে ব্যবহৃত হয়েছে। দয়া করে অন্য একটি চেষ্টা করুন।';
        } else {
            // Validate username is unique
            $check_username = $conn->prepare("SELECT id FROM users WHERE username = ?");
            $username = $_POST['username'];
            $check_username->bind_param("s", $username);
            $check_username->execute();
            $username_result = $check_username->get_result();

            if ($username_result->num_rows > 0) {
                $errorMessage = 'ইউজারনেম ইতিমধ্যে ব্যবহৃত হয়েছে। দয়া করে অন্য একটি চেষ্টা করুন।';
            } else {
                // Process file upload if a file is selected
                $profile_photo_path = null;
                if (isset($_FILES['profile_photo']) && $_FILES['profile_photo']['error'] === UPLOAD_ERR_OK) {
                    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
                    $file_extension = strtolower(pathinfo($_FILES['profile_photo']['name'], PATHINFO_EXTENSION));

                    if (!in_array($file_extension, $allowed_extensions)) {
                        $errorMessage = 'অবৈধ ফাইল টাইপ। শুধুমাত্র JPG, JPEG, PNG, এবং GIF ফাইল অনুমোদিত।';
                    } elseif ($_FILES['profile_photo']['size'] > 5000000) { // 5MB max
                        $errorMessage = 'ফাইল সাইজ খুব বড়। সর্বাধিক ফাইল সাইজ 5MB।';
                    } else {
                        $file_name = uniqid() . '.' . $file_extension;
                        $target_file = $upload_dir . $file_name;

                        if (move_uploaded_file($_FILES['profile_photo']['tmp_name'], $target_file)) {
                            $profile_photo_path = 'uploads/profile_photos/' . $file_name;
                        } else {
                            $errorMessage = 'ফাইল আপলোড করতে সমস্যা হয়েছে। দয়া করে আবার চেষ্টা করুন।';
                        }
                    }
                }

                if (empty($errorMessage)) {
                    try {
                        $conn->begin_transaction();

                        // Insert into users table
                        $user_stmt = $conn->prepare("INSERT INTO users (username, password, role) VALUES (?, ?, 'student')");
                        $hashed_password = password_hash($_POST['password'], PASSWORD_DEFAULT);
                        $user_stmt->bind_param("ss", $_POST['username'], $hashed_password);
                        $user_stmt->execute();
                        $user_id = $conn->insert_id;

                        // Insert into students table
                        $student_id = $_POST['student_id'];
                        $roll_number = !empty($_POST['roll_number']) ? $_POST['roll_number'] : null;
                        $first_name = $_POST['first_name'];
                        $last_name = $_POST['last_name'];
                        $email = !empty($_POST['email']) ? $_POST['email'] : null;
                        $phone = $_POST['phone'];
                        $address = $_POST['address'];
                        $dob = $_POST['dob'];
                        $gender = $_POST['gender'];
                        $batch = $_POST['batch'];
                        $admission_date = $_POST['admission_date'];
                        $department_id = $_POST['department_id'];
                        $class_id = !empty($_POST['class_id']) ? $_POST['class_id'] : null;
                        $session_id = !empty($_POST['session_id']) ? $_POST['session_id'] : null;
                        // Guardian information
                        $guardian_name = !empty($_POST['guardian_name']) ? $_POST['guardian_name'] : null;
                        $guardian_relation = !empty($_POST['guardian_relation']) ? $_POST['guardian_relation'] : null;
                        $guardian_phone = !empty($_POST['guardian_phone']) ? $_POST['guardian_phone'] : null;
                        $guardian_email = !empty($_POST['guardian_email']) ? $_POST['guardian_email'] : null;
                        $guardian_address = !empty($_POST['guardian_address']) ? $_POST['guardian_address'] : null;
                        $guardian_occupation = !empty($_POST['guardian_occupation']) ? $_POST['guardian_occupation'] : null;

                        // Father's information
                        $father_name = !empty($_POST['father_name']) ? $_POST['father_name'] : null;
                        $father_phone = !empty($_POST['father_phone']) ? $_POST['father_phone'] : null;
                        $father_email = !empty($_POST['father_email']) ? $_POST['father_email'] : null;
                        $father_occupation = !empty($_POST['father_occupation']) ? $_POST['father_occupation'] : null;
                        $father_income = !empty($_POST['father_income']) ? $_POST['father_income'] : null;

                        // Mother's information
                        $mother_name = !empty($_POST['mother_name']) ? $_POST['mother_name'] : null;
                        $mother_phone = !empty($_POST['mother_phone']) ? $_POST['mother_phone'] : null;
                        $mother_email = !empty($_POST['mother_email']) ? $_POST['mother_email'] : null;
                        $mother_occupation = !empty($_POST['mother_occupation']) ? $_POST['mother_occupation'] : null;
                        $mother_income = !empty($_POST['mother_income']) ? $_POST['mother_income'] : null;

                        $student_role = !empty($_POST['student_role']) ? $_POST['student_role'] : null;

                        // Instead of using the complex bind_param function directly, let's use a direct insert approach
                        // This avoids the parameter binding issues

                        // First, create an array of the values
                        $values = [
                            'user_id' => $user_id,
                            'student_id' => $student_id,
                            'roll_number' => $roll_number,
                            'first_name' => $first_name,
                            'last_name' => $last_name,
                            'email' => $email,
                            'phone' => $phone,
                            'address' => $address,
                            'dob' => $dob,
                            'gender' => $gender,
                            'batch' => $batch,
                            'admission_date' => $admission_date,
                            'department_id' => $department_id,
                            'class_id' => $class_id,
                            'session_id' => $session_id,
                            'profile_photo' => $profile_photo_path,
                            // Guardian information
                            'guardian_name' => $guardian_name,
                            'guardian_relation' => $guardian_relation,
                            'guardian_phone' => $guardian_phone,
                            'guardian_email' => $guardian_email,
                            'guardian_address' => $guardian_address,
                            'guardian_occupation' => $guardian_occupation,
                            // Father's information
                            'father_name' => $father_name,
                            'father_phone' => $father_phone,
                            'father_email' => $father_email,
                            'father_occupation' => $father_occupation,
                            'father_income' => $father_income,
                            // Mother's information
                            'mother_name' => $mother_name,
                            'mother_phone' => $mother_phone,
                            'mother_email' => $mother_email,
                            'mother_occupation' => $mother_occupation,
                            'mother_income' => $mother_income,
                            'role' => $student_role
                        ];

                        // Create the columns and placeholders parts of the query
                        $columns = implode(', ', array_keys($values));

                        // Create placeholders with proper escaping
                        $placeholders = [];
                        foreach ($values as $key => $value) {
                            // Skip NULL values
                            if ($value === NULL) {
                                $placeholders[] = "NULL";
                            } else {
                                // Quote string values
                                $placeholders[] = "'" . $conn->real_escape_string($value) . "'";
                            }
                        }
                        $placeholderString = implode(', ', $placeholders);

                        // Create and execute the direct INSERT query
                        $insertQuery = "INSERT INTO students ($columns) VALUES ($placeholderString)";
                        $insertResult = $conn->query($insertQuery);

                        if (!$insertResult) {
                            throw new Exception("Database error: " . $conn->error);
                        }

                        $conn->commit();

                        // Store the newly created student ID for subject selection
                        $_SESSION['new_student_id'] = $student_id;
                        $_SESSION['new_student_name'] = $first_name . ' ' . $last_name;
                        $_SESSION['new_student_db_id'] = $conn->insert_id;

                        $successMessage = 'শিক্ষার্থী সফলভাবে যোগ করা হয়েছে। এখন আপনি শিক্ষার্থীর বিষয় নির্বাচন করতে পারেন।';

                        // Reset POST data after successful submission
                        $_POST = [];
                    } catch (Exception $e) {
                        $conn->rollback();
                        $errorMessage = 'শিক্ষার্থী যোগ করতে সমস্যা হয়েছে: ' . $e->getMessage();
                    }
                }
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>Add Student - College Management System</title>


    <style>
        .profile-photo-preview {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #ccc;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #adb5bd;
            overflow: hidden;
            margin: 0 auto 1rem auto;
        }
        .profile-photo-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .photo-label {
            display: block;
            text-align: center;
            margin-bottom: 1rem;
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>এডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> স্টাফ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="student_subject_selection_list.php">
                            <i class="fas fa-list-check me-2"></i> বিষয় নির্বাচন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sessions.php">
                            <i class="fas fa-calendar-alt me-2"></i> সেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-file-pdf me-2"></i> রিপোর্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>নতুন শিক্ষার্থী যোগ করুন</h2>
                        <p class="text-muted">একটি নতুন শিক্ষার্থীর রেকর্ড তৈরি করুন অথবা CSV ফাইল আপলোড করুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="students.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>শিক্ষার্থী তালিকায় ফিরে যান
                        </a>
                    </div>
                </div>

                <!-- CSV Upload Section -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-file-csv me-2"></i>CSV ফাইল আপলোড করুন
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <p class="text-muted mb-3">
                                    একসাথে একাধিক শিক্ষার্থী যোগ করতে CSV ফাইল আপলোড করুন।
                                    প্রথমে টেমপ্লেট ডাউনলোড করে সঠিক ফরম্যাটে ডেটা প্রস্তুত করুন।
                                </p>

                                <form method="POST" enctype="multipart/form-data" class="mb-3">
                                    <div class="row align-items-end">
                                        <div class="col-md-8">
                                            <label for="csv_file" class="form-label">CSV ফাইল নির্বাচন করুন</label>
                                            <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                                            <small class="form-text text-muted">শুধুমাত্র .csv ফাইল অনুমোদিত</small>
                                        </div>
                                        <div class="col-md-4">
                                            <button type="submit" name="upload_csv" class="btn btn-success me-2">
                                                <i class="fas fa-upload me-2"></i>আপলোড করুন
                                            </button>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-12">
                                            <div class="alert alert-success">
                                                <strong>✅ সমস্যা সমাধান:</strong> আপনার CSV ফাইল সঠিক কিন্তু তারিখ ফরম্যাট সমস্যা আছে।
                                                <strong>সমাধান:</strong> নিচের "দ্রুত টেস্ট" বাটন ক্লিক করুন অথবা নতুন টেমপ্লেট ডাউনলোড করুন।
                                            </div>
                                            <form method="POST" style="display: inline;">
                                                <button type="submit" name="quick_test" class="btn btn-warning me-2">
                                                    <i class="fas fa-rocket me-2"></i>দ্রুত টেস্ট (২ জন শিক্ষার্থী)
                                                </button>
                                            </form>
                                            <a href="?download_template=1" class="btn btn-info">
                                                <i class="fas fa-download me-2"></i>নতুন টেমপ্লেট ডাউনলোড
                                            </a>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h6>টেমপ্লেট ডাউনলোড করুন</h6>
                                    <p class="text-muted small">সঠিক ফরম্যাটের জন্য নমুনা CSV ফাইল ডাউনলোড করুন</p>
                                    <a href="?download_template=1" class="btn btn-outline-primary mb-2">
                                        <i class="fas fa-download me-2"></i>CSV টেমপ্লেট
                                    </a>
                                    <br>
                                    <a href="csv_helper.php" class="btn btn-outline-info btn-sm me-2">
                                        <i class="fas fa-info-circle me-1"></i>ID তালিকা দেখুন
                                    </a>
                                    <a href="csv_debug.php" class="btn btn-outline-danger btn-sm me-2">
                                        <i class="fas fa-search me-1"></i>CSV ডিবাগ
                                    </a>
                                    <a href="test_upload.php" class="btn btn-outline-warning btn-sm">
                                        <i class="fas fa-bug me-1"></i>আপলোড টেস্ট
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- CSV Format Instructions -->
                        <div class="mt-4">
                            <h6>CSV ফাইল নির্দেশনা:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="small text-muted">
                                        <li><strong>প্রয়োজনীয় ফিল্ড:</strong> student_id, first_name, last_name, phone, address, dob, gender, batch, admission_date, department_id, username, password</li>
                                        <li><strong>ঐচ্ছিক ফিল্ড:</strong> roll_number, email, class_id, session_id, guardian ও parent তথ্য</li>
                                        <li><strong>তারিখ:</strong> YYYY-MM-DD (যেমন: 2000-01-15) - অন্য ফরম্যাট স্বয়ংক্রিয়ভাবে কনভার্ট হবে</li>
                                        <li>CSV ফাইলের প্রথম সারিতে কলামের নাম থাকতে হবে</li>
                                        <li><strong>বাংলা ও ইংরেজি উভয় সাপোর্টেড</strong></li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="small text-muted">
                                        <li>Gender: Male, Female, Other</li>
                                        <li>Department ID অবশ্যই বৈধ সংখ্যা হতে হবে</li>
                                        <li><strong>ডুপ্লিকেট Student ID স্বয়ংক্রিয়ভাবে স্কিপ হবে</strong></li>
                                        <li>Username অবশ্যই ইউনিক হতে হবে</li>
                                        <li><strong>ফোন নম্বর:</strong> 01 দিয়ে শুরু হতে হবে (স্বয়ংক্রিয় সংশোধন)</li>
                                        <li>ঐচ্ছিক ফিল্ড খালি রাখা যাবে</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Common Issues and Solutions -->
                        <div class="mt-4">
                            <h6 class="text-warning">সাধারণ সমস্যা ও সমাধান:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="alert alert-warning alert-sm">
                                        <strong>সমস্যা:</strong> "প্রয়োজনীয় ফিল্ড অনুপস্থিত"<br>
                                        <strong>সমাধান:</strong> সব মেন্ডেটরি ফিল্ড পূরণ করুন। টেমপ্লেট ডাউনলোড করে দেখুন।
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="alert alert-info alert-sm">
                                        <strong>সমস্যা:</strong> তারিখ ফরম্যাট ত্রুটি<br>
                                        <strong>সমাধান:</strong> YYYY-MM-DD ব্যবহার করুন (যেমন: 2000-01-15)
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>ত্রুটি!</strong> <?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>সফল!</strong> <?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>

                    <?php if (isset($_SESSION['new_student_id'])): ?>
                    <!-- Subject Selection Option -->
                    <div class="card mb-4 animate__animated animate__fadeIn">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-book-open me-2"></i>বিষয় নির্বাচন</h5>
                        </div>
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h5><?php echo $_SESSION['new_student_name']; ?> এর জন্য বিষয় নির্বাচন করুন</h5>
                                    <p class="text-muted mb-0">শিক্ষার্থীর বিভাগ অনুযায়ী বিষয় নির্বাচন করতে নিচের বাটনে ক্লিক করুন।</p>
                                </div>
                                <div class="col-md-4 text-end">
                                    <a href="student_subject_selection.php?id=<?php echo $_SESSION['new_student_id']; ?>" class="btn btn-primary">
                                        <i class="fas fa-plus-circle me-2"></i>বিষয় যোগ করুন
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php
                    // Clear the session variables after displaying
                    unset($_SESSION['new_student_id']);
                    unset($_SESSION['new_student_name']);
                    unset($_SESSION['new_student_db_id']);
                    endif;
                    ?>
                <?php endif; ?>

                <!-- Manual Entry Section -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user-plus me-2"></i>ম্যানুয়াল এন্ট্রি
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="add_student.php" enctype="multipart/form-data">
                            <div class="row">
                                <!-- Academic Settings -->
                                <div class="col-12 mb-4">
                                    <h4 class="card-title mb-3">একাডেমিক সেটিংস</h4>
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="session_id" class="form-label">সেশন</label>
                                            <select class="form-select" id="session_id" name="session_id">
                                                <option value="">সেশন নির্বাচন করুন</option>
                                                <?php foreach ($sessionList as $id => $name): ?>
                                                    <option value="<?php echo $id; ?>" <?php echo (isset($_POST['session_id']) && $_POST['session_id'] == $id) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($name); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="department_id" class="form-label">বিভাগ*</label>
                                            <select class="form-select" id="department_id" name="department_id" required>
                                                <option value="">বিভাগ নির্বাচন করুন</option>
                                                <?php foreach ($departmentList as $id => $name): ?>
                                                    <option value="<?php echo $id; ?>" <?php echo (isset($_POST['department_id']) && $_POST['department_id'] == $id) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($name); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <small class="form-text text-muted">শিক্ষার্থী যোগ করার পর বিষয় নির্বাচন করতে পারবেন।</small>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="class_id" class="form-label">ক্লাস</label>
                                            <div class="input-group">
                                                <select class="form-select" id="class_id" name="class_id">
                                                    <option value="">ক্লাস নির্বাচন করুন</option>
                                                    <?php foreach ($classList as $id => $name): ?>
                                                        <option value="<?php echo $id; ?>" <?php echo (isset($_POST['class_id']) && $_POST['class_id'] == $id) ? 'selected' : ''; ?>>
                                                            <?php echo htmlspecialchars($name); ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                                <a href="classes.php" class="btn btn-outline-secondary" title="ক্লাস ব্যবস্থাপনা">
                                                    <i class="fas fa-external-link-alt"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Student Details -->
                                <div class="col-md-6 mb-4">
                                    <h4 class="card-title mb-3">শিক্ষার্থীর তথ্য</h4>

                                    <div class="mb-3">
                                        <label for="student_id" class="form-label">শিক্ষার্থী আইডি*</label>
                                        <input type="text" class="form-control" id="student_id" name="student_id" value="<?php echo isset($_POST['student_id']) ? htmlspecialchars($_POST['student_id']) : 'STD-' . mt_rand(100000, 999999); ?>" required>
                                        <small class="form-text text-muted">সিস্টেম স্বয়ংক্রিয়ভাবে একটি ৬ ডিজিটের রেন্ডম আইডি তৈরি করেছে। প্রয়োজনে পরিবর্তন করুন।</small>
                                    </div>

                                    <div class="mb-3">
                                        <label for="roll_number" class="form-label">রোল নম্বর</label>
                                        <input type="text" class="form-control" id="roll_number" name="roll_number" value="<?php echo isset($_POST['roll_number']) ? htmlspecialchars($_POST['roll_number']) : ''; ?>">
                                        <small class="form-text text-muted">শিক্ষার্থীর ক্লাস রোল নম্বর</small>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="first_name" class="form-label">নামের প্রথম অংশ*</label>
                                            <input type="text" class="form-control" id="first_name" name="first_name" value="<?php echo isset($_POST['first_name']) ? htmlspecialchars($_POST['first_name']) : ''; ?>" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="last_name" class="form-label">নামের শেষ অংশ*</label>
                                            <input type="text" class="form-control" id="last_name" name="last_name" value="<?php echo isset($_POST['last_name']) ? htmlspecialchars($_POST['last_name']) : ''; ?>" required>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="email" class="form-label">ইমেইল</label>
                                        <input type="email" class="form-control" id="email" name="email" value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                                    </div>

                                    <div class="mb-3">
                                        <label for="phone" class="form-label">ফোন নম্বর*</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="address" class="form-label">ঠিকানা*</label>
                                        <textarea class="form-control" id="address" name="address" rows="3" required><?php echo isset($_POST['address']) ? htmlspecialchars($_POST['address']) : ''; ?></textarea>
                                    </div>
                                </div>

                                <!-- Academic Details -->
                                <div class="col-md-6 mb-4">
                                    <h4 class="card-title mb-3">একাডেমিক তথ্য</h4>

                                    <div class="mb-3">
                                        <label for="dob" class="form-label">জন্ম তারিখ*</label>
                                        <input type="date" class="form-control" id="dob" name="dob" value="<?php echo isset($_POST['dob']) ? htmlspecialchars($_POST['dob']) : ''; ?>" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="gender" class="form-label">লিঙ্গ*</label>
                                        <select class="form-select" id="gender" name="gender" required>
                                            <option value="">নির্বাচন করুন</option>
                                            <option value="Male" <?php echo (isset($_POST['gender']) && $_POST['gender'] === 'Male') ? 'selected' : ''; ?>>পুরুষ</option>
                                            <option value="Female" <?php echo (isset($_POST['gender']) && $_POST['gender'] === 'Female') ? 'selected' : ''; ?>>মহিলা</option>
                                            <option value="Other" <?php echo (isset($_POST['gender']) && $_POST['gender'] === 'Other') ? 'selected' : ''; ?>>অন্যান্য</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="batch" class="form-label">ব্যাচ*</label>
                                        <input type="text" class="form-control" id="batch" name="batch" value="<?php echo isset($_POST['batch']) ? htmlspecialchars($_POST['batch']) : ''; ?>" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="admission_date" class="form-label">ভর্তির তারিখ*</label>
                                        <input type="date" class="form-control" id="admission_date" name="admission_date" value="<?php echo isset($_POST['admission_date']) ? htmlspecialchars($_POST['admission_date']) : date('Y-m-d'); ?>" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="student_role" class="form-label">শিক্ষার্থী রোল</label>
                                        <div class="input-group">
                                            <select class="form-select" id="student_role" name="student_role">
                                                <option value="">নির্বাচন করুন</option>
                                                <?php
                                                // Check if student_roles table exists
                                                $tableCheck = $conn->query("SHOW TABLES LIKE 'student_roles'");
                                                if ($tableCheck->num_rows > 0) {
                                                    // Get roles from the database
                                                    $rolesQuery = $conn->query("SELECT role_name, role_name_bn FROM student_roles ORDER BY role_name");
                                                    if ($rolesQuery && $rolesQuery->num_rows > 0) {
                                                        while ($role = $rolesQuery->fetch_assoc()) {
                                                            $selected = (isset($_POST['student_role']) && $_POST['student_role'] === $role['role_name']) ? 'selected' : '';
                                                            echo '<option value="' . htmlspecialchars($role['role_name']) . '" ' . $selected . '>' . htmlspecialchars($role['role_name_bn']) . '</option>';
                                                        }
                                                    } else {
                                                        // Fallback to default roles if no roles in database
                                                        $defaultRoles = [
                                                            ['Class Captain', 'ক্লাস ক্যাপ্টেন'],
                                                            ['Class Representative', 'ক্লাস প্রতিনিধি'],
                                                            ['Class Monitor', 'ক্লাস মনিটর'],
                                                            ['Student Council Member', 'ছাত্র সংসদ সদস্য'],
                                                            ['Regular Student', 'সাধারণ শিক্ষার্থী']
                                                        ];
                                                        foreach ($defaultRoles as $role) {
                                                            $selected = (isset($_POST['student_role']) && $_POST['student_role'] === $role[0]) ? 'selected' : '';
                                                            echo '<option value="' . htmlspecialchars($role[0]) . '" ' . $selected . '>' . htmlspecialchars($role[1]) . '</option>';
                                                        }
                                                    }
                                                } else {
                                                    // Fallback to default roles if table doesn't exist
                                                    $defaultRoles = [
                                                        ['Class Captain', 'ক্লাস ক্যাপ্টেন'],
                                                        ['Class Representative', 'ক্লাস প্রতিনিধি'],
                                                        ['Class Monitor', 'ক্লাস মনিটর'],
                                                        ['Student Council Member', 'ছাত্র সংসদ সদস্য'],
                                                        ['Regular Student', 'সাধারণ শিক্ষার্থী']
                                                    ];
                                                    foreach ($defaultRoles as $role) {
                                                        $selected = (isset($_POST['student_role']) && $_POST['student_role'] === $role[0]) ? 'selected' : '';
                                                        echo '<option value="' . htmlspecialchars($role[0]) . '" ' . $selected . '>' . htmlspecialchars($role[1]) . '</option>';
                                                    }
                                                }
                                                ?>
                                            </select>
                                            <a href="manage_student_roles.php" class="btn btn-outline-secondary" title="রোল কাস্টমাইজ করুন">
                                                <i class="fas fa-cog"></i>
                                            </a>
                                        </div>
                                        <small class="form-text text-muted">শিক্ষার্থীর বিশেষ ভূমিকা নির্বাচন করুন (যদি থাকে)। কাস্টম রোল যোগ করতে <a href="manage_student_roles.php">এখানে ক্লিক করুন</a>।</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Account Information -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h4 class="card-title mb-3">অ্যাকাউন্ট তথ্য</h4>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="username" class="form-label">ইউজারনেম*</label>
                                    <input type="text" class="form-control" id="username" name="username" value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label">পাসওয়ার্ড*</label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                </div>
                            </div>

                            <!-- Parents Information -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h4 class="card-title mb-3">পিতা-মাতার তথ্য</h4>
                                </div>

                                <!-- Father's Information -->
                                <div class="col-12 mb-3">
                                    <h5 class="card-subtitle mb-2 text-primary">পিতার তথ্য</h5>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="father_name" class="form-label">পিতার নাম</label>
                                    <input type="text" class="form-control" id="father_name" name="father_name" value="<?php echo isset($_POST['father_name']) ? htmlspecialchars($_POST['father_name']) : ''; ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="father_occupation" class="form-label">পেশা</label>
                                    <input type="text" class="form-control" id="father_occupation" name="father_occupation" value="<?php echo isset($_POST['father_occupation']) ? htmlspecialchars($_POST['father_occupation']) : ''; ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="father_phone" class="form-label">ফোন নম্বর</label>
                                    <input type="tel" class="form-control" id="father_phone" name="father_phone" value="<?php echo isset($_POST['father_phone']) ? htmlspecialchars($_POST['father_phone']) : ''; ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="father_email" class="form-label">ইমেইল</label>
                                    <input type="email" class="form-control" id="father_email" name="father_email" value="<?php echo isset($_POST['father_email']) ? htmlspecialchars($_POST['father_email']) : ''; ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="father_income" class="form-label">মাসিক আয়</label>
                                    <input type="text" class="form-control" id="father_income" name="father_income" value="<?php echo isset($_POST['father_income']) ? htmlspecialchars($_POST['father_income']) : ''; ?>">
                                </div>

                                <!-- Mother's Information -->
                                <div class="col-12 mb-3 mt-3">
                                    <h5 class="card-subtitle mb-2 text-primary">মাতার তথ্য</h5>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="mother_name" class="form-label">মাতার নাম</label>
                                    <input type="text" class="form-control" id="mother_name" name="mother_name" value="<?php echo isset($_POST['mother_name']) ? htmlspecialchars($_POST['mother_name']) : ''; ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="mother_occupation" class="form-label">পেশা</label>
                                    <input type="text" class="form-control" id="mother_occupation" name="mother_occupation" value="<?php echo isset($_POST['mother_occupation']) ? htmlspecialchars($_POST['mother_occupation']) : ''; ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="mother_phone" class="form-label">ফোন নম্বর</label>
                                    <input type="tel" class="form-control" id="mother_phone" name="mother_phone" value="<?php echo isset($_POST['mother_phone']) ? htmlspecialchars($_POST['mother_phone']) : ''; ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="mother_email" class="form-label">ইমেইল</label>
                                    <input type="email" class="form-control" id="mother_email" name="mother_email" value="<?php echo isset($_POST['mother_email']) ? htmlspecialchars($_POST['mother_email']) : ''; ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="mother_income" class="form-label">মাসিক আয়</label>
                                    <input type="text" class="form-control" id="mother_income" name="mother_income" value="<?php echo isset($_POST['mother_income']) ? htmlspecialchars($_POST['mother_income']) : ''; ?>">
                                </div>
                            </div>

                            <!-- Guardian Information -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h4 class="card-title mb-3">অভিভাবকের তথ্য</h4>
                                    <p class="text-muted">যদি অভিভাবক পিতা বা মাতা ছাড়া অন্য কেউ হয়, তাহলে এই তথ্য পূরণ করুন</p>
                                    <div class="mb-3">
                                        <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="copyFatherInfo()">
                                            <i class="fas fa-copy me-1"></i> পিতার তথ্য কপি করুন
                                        </button>
                                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="copyMotherInfo()">
                                            <i class="fas fa-copy me-1"></i> মাতার তথ্য কপি করুন
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="guardian_name" class="form-label">নাম</label>
                                    <input type="text" class="form-control" id="guardian_name" name="guardian_name" value="<?php echo isset($_POST['guardian_name']) ? htmlspecialchars($_POST['guardian_name']) : ''; ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="guardian_relation" class="form-label">সম্পর্ক</label>
                                    <select class="form-select" id="guardian_relation" name="guardian_relation">
                                        <option value="">নির্বাচন করুন</option>
                                        <option value="Father" <?php echo (isset($_POST['guardian_relation']) && $_POST['guardian_relation'] === 'Father') ? 'selected' : ''; ?>>বাবা</option>
                                        <option value="Mother" <?php echo (isset($_POST['guardian_relation']) && $_POST['guardian_relation'] === 'Mother') ? 'selected' : ''; ?>>মা</option>
                                        <option value="Brother" <?php echo (isset($_POST['guardian_relation']) && $_POST['guardian_relation'] === 'Brother') ? 'selected' : ''; ?>>ভাই</option>
                                        <option value="Sister" <?php echo (isset($_POST['guardian_relation']) && $_POST['guardian_relation'] === 'Sister') ? 'selected' : ''; ?>>বোন</option>
                                        <option value="Uncle" <?php echo (isset($_POST['guardian_relation']) && $_POST['guardian_relation'] === 'Uncle') ? 'selected' : ''; ?>>চাচা/মামা</option>
                                        <option value="Aunt" <?php echo (isset($_POST['guardian_relation']) && $_POST['guardian_relation'] === 'Aunt') ? 'selected' : ''; ?>>চাচী/মামী</option>
                                        <option value="Other" <?php echo (isset($_POST['guardian_relation']) && $_POST['guardian_relation'] === 'Other') ? 'selected' : ''; ?>>অন্যান্য</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="guardian_phone" class="form-label">ফোন নম্বর</label>
                                    <input type="tel" class="form-control" id="guardian_phone" name="guardian_phone" value="<?php echo isset($_POST['guardian_phone']) ? htmlspecialchars($_POST['guardian_phone']) : ''; ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="guardian_email" class="form-label">ইমেইল</label>
                                    <input type="email" class="form-control" id="guardian_email" name="guardian_email" value="<?php echo isset($_POST['guardian_email']) ? htmlspecialchars($_POST['guardian_email']) : ''; ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="guardian_address" class="form-label">ঠিকানা</label>
                                    <div class="mb-2 form-check">
                                        <input type="checkbox" class="form-check-input" id="same_address" onclick="copyAddress()">
                                        <label class="form-check-label" for="same_address">শিক্ষার্থীর ঠিকানার সাথে একই</label>
                                    </div>
                                    <textarea class="form-control" id="guardian_address" name="guardian_address" rows="3"><?php echo isset($_POST['guardian_address']) ? htmlspecialchars($_POST['guardian_address']) : ''; ?></textarea>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="guardian_occupation" class="form-label">পেশা</label>
                                    <input type="text" class="form-control" id="guardian_occupation" name="guardian_occupation" value="<?php echo isset($_POST['guardian_occupation']) ? htmlspecialchars($_POST['guardian_occupation']) : ''; ?>">
                                </div>
                            </div>

                            <!-- Profile Photo -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h4 class="card-title mb-3">প্রোফাইল ছবি</h4>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="profile-photo-preview" id="photoPreview">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <label for="profile_photo" class="photo-label">ছবি আপলোড করুন</label>
                                    <input type="file" class="form-control" id="profile_photo" name="profile_photo" accept="image/*" onchange="previewImage(this)">
                                    <small class="form-text text-muted">সর্বাধিক ফাইল সাইজ: 5MB। অনুমোদিত ফরম্যাট: JPG, JPEG, PNG, GIF</small>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i> শিক্ষার্থী যোগ করার পর, আপনি তার জন্য বিষয় নির্বাচন করতে পারবেন।
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <button type="reset" class="btn btn-secondary">রিসেট</button>
                                    <button type="submit" class="btn btn-primary">শিক্ষার্থী যোগ করুন</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // Function to update class options based on selected department - removed

        // Initialize class options on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Add form validation
            const form = document.querySelector('form');
            form.addEventListener('submit', function(event) {
                let isValid = true;
                let errorMessage = '';

                // Required fields validation
                const requiredFields = [
                    { id: 'student_id', label: 'শিক্ষার্থী আইডি' },
                    { id: 'first_name', label: 'নামের প্রথম অংশ' },
                    { id: 'last_name', label: 'নামের শেষ অংশ' },
                    { id: 'phone', label: 'ফোন নম্বর' },
                    { id: 'address', label: 'ঠিকানা' },
                    { id: 'dob', label: 'জন্ম তারিখ' },
                    { id: 'gender', label: 'লিঙ্গ' },
                    { id: 'batch', label: 'ব্যাচ' },
                    { id: 'admission_date', label: 'ভর্তির তারিখ' },
                    { id: 'department_id', label: 'বিভাগ' },
                    { id: 'username', label: 'ইউজারনেম' },
                    { id: 'password', label: 'পাসওয়ার্ড' }
                ];

                // Check all required fields
                requiredFields.forEach(field => {
                    const input = document.getElementById(field.id);
                    if (!input.value.trim()) {
                        isValid = false;
                        errorMessage += `${field.label} প্রয়োজনীয়।<br>`;
                        input.classList.add('is-invalid');
                    } else {
                        input.classList.remove('is-invalid');
                    }
                });

                // Email validation - optional but must be valid if provided
                const emailInput = document.getElementById('email');
                const emailValue = emailInput.value.trim();
                if (emailValue && !isValidEmail(emailValue)) {
                    isValid = false;
                    errorMessage += 'সঠিক ইমেইল প্রদান করুন।<br>';
                    emailInput.classList.add('is-invalid');
                } else {
                    emailInput.classList.remove('is-invalid');
                }

                // Phone validation
                const phoneInput = document.getElementById('phone');
                const phoneValue = phoneInput.value.trim();
                if (phoneValue && !isValidPhone(phoneValue)) {
                    isValid = false;
                    errorMessage += 'সঠিক ফোন নম্বর প্রদান করুন।<br>';
                    phoneInput.classList.add('is-invalid');
                }

                // Guardian email validation if provided
                const guardianEmailInput = document.getElementById('guardian_email');
                const guardianEmailValue = guardianEmailInput.value.trim();
                if (guardianEmailValue && !isValidEmail(guardianEmailValue)) {
                    isValid = false;
                    errorMessage += 'অভিভাবকের সঠিক ইমেইল প্রদান করুন।<br>';
                    guardianEmailInput.classList.add('is-invalid');
                } else {
                    guardianEmailInput.classList.remove('is-invalid');
                }

                // If validation fails, prevent form submission and show error
                if (!isValid) {
                    event.preventDefault();
                    const errorContainer = document.createElement('div');
                    errorContainer.className = 'alert alert-danger';
                    errorContainer.innerHTML = errorMessage;

                    // Remove any existing error alerts
                    const existingAlerts = document.querySelectorAll('.alert-danger');
                    existingAlerts.forEach(alert => alert.remove());

                    // Insert error message at the top of the form
                    form.parentNode.insertBefore(errorContainer, form);

                    // Scroll to top of the form
                    window.scrollTo(0, 0);
                }
            });
        });

        // Function to preview profile photo before upload
        function previewImage(input) {
            const preview = document.getElementById('photoPreview');

            if (input.files && input.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    preview.innerHTML = '<img src="' + e.target.result + '" alt="Profile Preview">';
                }

                reader.readAsDataURL(input.files[0]);
            } else {
                preview.innerHTML = '<i class="fas fa-user"></i>';
            }
        }

        // Function to copy student's address to guardian address
        function copyAddress() {
            if (document.getElementById('same_address').checked) {
                document.getElementById('guardian_address').value = document.getElementById('address').value;
            } else {
                document.getElementById('guardian_address').value = '';
            }
        }

        // Function to copy father's information to guardian information
        function copyFatherInfo() {
            document.getElementById('guardian_name').value = document.getElementById('father_name').value;
            document.getElementById('guardian_relation').value = 'Father';
            document.getElementById('guardian_phone').value = document.getElementById('father_phone').value;
            document.getElementById('guardian_email').value = document.getElementById('father_email').value;
            document.getElementById('guardian_occupation').value = document.getElementById('father_occupation').value;
        }

        // Function to copy mother's information to guardian information
        function copyMotherInfo() {
            document.getElementById('guardian_name').value = document.getElementById('mother_name').value;
            document.getElementById('guardian_relation').value = 'Mother';
            document.getElementById('guardian_phone').value = document.getElementById('mother_phone').value;
            document.getElementById('guardian_email').value = document.getElementById('mother_email').value;
            document.getElementById('guardian_occupation').value = document.getElementById('mother_occupation').value;
        }

        // Helper validation functions
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function isValidPhone(phone) {
            // Basic phone validation for Bangladesh numbers
            const phoneRegex = /^(\+?88)?0?1[3-9]\d{8}$/;
            return phoneRegex.test(phone);
        }
    </script>
</body>
</html>