<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get filter parameters
$departmentFilter = isset($_GET['department']) ? $_GET['department'] : '';
$sessionFilter = isset($_GET['session']) ? $_GET['session'] : '';
$searchTerm = isset($_GET['search']) ? $_GET['search'] : '';
$subjectFilter = isset($_GET['subject']) ? $_GET['subject'] : '';

// Pagination setup
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$itemsPerPage = 10;
$offset = ($page - 1) * $itemsPerPage;

// Build the query
$whereClause = "WHERE ss.student_id IS NOT NULL";
$params = [];
$types = "";

if (!empty($searchTerm)) {
    $whereClause .= " AND (s.student_id LIKE ? OR s.first_name LIKE ? OR s.last_name LIKE ?)";
    $searchParam = "%$searchTerm%";
    $params = [$searchParam, $searchParam, $searchParam];
    $types = "sss";
}

if (!empty($departmentFilter)) {
    $whereClause .= " AND s.department_id = ?";
    $params[] = $departmentFilter;
    $types .= "i";
}

if (!empty($sessionFilter)) {
    $whereClause .= " AND s.session_id = ?";
    $params[] = $sessionFilter;
    $types .= "i";
}

if (!empty($subjectFilter)) {
    $whereClause .= " AND sbj.id = ?";
    $params[] = $subjectFilter;
    $types .= "i";
}

// Get total count for pagination
$countQuery = "SELECT COUNT(DISTINCT s.id) as total
               FROM students s
               JOIN student_subjects ss ON s.id = ss.student_id
               JOIN subjects sbj ON ss.subject_id = sbj.id
               LEFT JOIN departments d ON s.department_id = d.id
               LEFT JOIN sessions ses ON s.session_id = ses.id
               $whereClause";

$stmt = $conn->prepare($countQuery);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$totalResult = $stmt->get_result();
$totalRows = $totalResult->fetch_assoc()['total'];
$totalPages = ceil($totalRows / $itemsPerPage);

// Get students with their selected subjects
$query = "SELECT DISTINCT s.id, s.student_id, s.first_name, s.last_name, s.email,
          s.phone, d.department_name, ses.session_name,
          (SELECT GROUP_CONCAT(sbj.subject_name SEPARATOR ', ')
           FROM student_subjects ss2
           JOIN subjects sbj ON ss2.subject_id = sbj.id
           WHERE ss2.student_id = s.id AND ss2.category = 'required') AS required_subjects,
          (SELECT GROUP_CONCAT(sbj.subject_name SEPARATOR ', ')
           FROM student_subjects ss2
           JOIN subjects sbj ON ss2.subject_id = sbj.id
           WHERE ss2.student_id = s.id AND ss2.category = 'optional') AS optional_subjects,
          (SELECT GROUP_CONCAT(sbj.subject_name SEPARATOR ', ')
           FROM student_subjects ss2
           JOIN subjects sbj ON ss2.subject_id = sbj.id
           WHERE ss2.student_id = s.id AND ss2.category = 'fourth') AS fourth_subjects
          FROM students s
          JOIN student_subjects ss ON s.id = ss.student_id
          JOIN subjects sbj ON ss.subject_id = sbj.id
          LEFT JOIN departments d ON s.department_id = d.id
          LEFT JOIN sessions ses ON s.session_id = ses.id
          $whereClause
          GROUP BY s.id
          ORDER BY s.first_name, s.last_name
          LIMIT ? OFFSET ?";

$params[] = $itemsPerPage;
$params[] = $offset;
$types .= "ii";

$stmt = $conn->prepare($query);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$students = $stmt->get_result();

// Get departments for filter
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Get sessions for filter
$sessionsQuery = "SELECT * FROM sessions ORDER BY session_name";
$sessions = $conn->query($sessionsQuery);

// Get subjects for filter
$subjectsQuery = "SELECT * FROM subjects ORDER BY subject_name";
$subjects = $conn->query($subjectsQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>নির্বাচিত বিষয়কৃত ছাত্র/ছাত্রীর তালিকা - কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .search-container {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .table-actions {
            white-space: nowrap;
        }

        /* Styling for the student subjects table */
        .student-subjects-table {
            border-collapse: separate;
            border-spacing: 0;
        }

        .student-info-row {
            background-color: #f8f9fa;
            position: relative;
            cursor: pointer;
        }

        .student-info-row:hover {
            background-color: #e9ecef;
        }

        .student-info-row.table-active {
            background-color: #e2e6ea;
        }

        .student-info-row:after {
            content: '\f078';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-size: 0.8rem;
            transition: transform 0.3s ease;
        }

        .student-info-row.table-active:after {
            transform: translateY(-50%) rotate(180deg);
        }

        .subject-row {
            background-color: #ffffff;
            border-bottom: 1px solid #dee2e6;
        }

        .subject-details {
            padding: 0.75rem 1.5rem;
        }

        .subject-categories {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .subject-category {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.25rem;
        }

        .badge {
            padding: 0.35em 0.65em;
            font-weight: 500;
            border-radius: 4px;
        }

        .badge.bg-primary {
            background: linear-gradient(135deg, #4361ee 0%, #4895ef 100%) !important;
        }

        .badge.bg-info {
            background: linear-gradient(135deg, #0ea5e9 0%, #38bdf8 100%) !important;
        }

        .badge.bg-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%) !important;
        }

        .subject-list {
            font-size: 0.9rem;
        }

        /* Column visibility control */
        .column-toggle-btn {
            margin-right: 0.5rem;
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        .column-toggle-dropdown {
            padding: 1rem;
            min-width: 200px;
        }

        .column-toggle-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .column-toggle-item label {
            margin-left: 0.5rem;
            margin-bottom: 0;
            cursor: pointer;
        }

        /* Hidden columns */
        .hide-column {
            display: none;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .subject-categories {
                flex-direction: column;
            }

            .search-container {
                flex-wrap: wrap;
            }

            /* Auto-hide less important columns on mobile */
            .column-email, .column-phone {
                display: none;
            }
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> কোর্স
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="departments.php">
                            <i class="fas fa-building me-2"></i> বিভাগ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-file-pdf me-2"></i> রিপোর্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>নির্বাচিত বিষয়কৃত ছাত্র/ছাত্রীর তালিকা</h2>
                        <p class="text-muted">বিষয় নির্বাচন করেছে এমন শিক্ষার্থীদের দেখুন এবং ফিল্টার করুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="students.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>শিক্ষার্থী তালিকায় ফিরে যান
                        </a>
                    </div>
                </div>

                <!-- Search and Filter -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" action="subject_selected_students.php">
                            <div class="search-container">
                                <div class="flex-grow-1">
                                    <input type="text" name="search" class="form-control" placeholder="আইডি বা নাম দিয়ে খুঁজুন" value="<?php echo htmlspecialchars($searchTerm); ?>">
                                </div>
                                <div>
                                    <select name="department" class="form-select">
                                        <option value="">সব বিভাগ</option>
                                        <?php if ($departments && $departments->num_rows > 0): ?>
                                            <?php while ($dept = $departments->fetch_assoc()): ?>
                                                <option value="<?php echo $dept['id']; ?>" <?php echo ($departmentFilter == $dept['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $dept['department_name']; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div>
                                    <select name="session" class="form-select">
                                        <option value="">সব সেশন</option>
                                        <?php if ($sessions && $sessions->num_rows > 0): ?>
                                            <?php while ($session = $sessions->fetch_assoc()): ?>
                                                <option value="<?php echo $session['id']; ?>" <?php echo ($sessionFilter == $session['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $session['session_name']; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div>
                                    <select name="subject" class="form-select">
                                        <option value="">সব বিষয়</option>
                                        <?php if ($subjects && $subjects->num_rows > 0): ?>
                                            <?php while ($subject = $subjects->fetch_assoc()): ?>
                                                <option value="<?php echo $subject['id']; ?>" <?php echo ($subjectFilter == $subject['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $subject['subject_name']; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>খুঁজুন
                                    </button>
                                    <?php if (!empty($searchTerm) || !empty($departmentFilter) || !empty($sessionFilter) || !empty($subjectFilter)): ?>
                                        <a href="subject_selected_students.php" class="btn btn-secondary ms-2">রিসেট</a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Students Table -->
                <div class="card">
                    <div class="card-body">
                        <!-- View Toggle and Column Control -->
                        <div class="d-flex justify-content-between mb-3">
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle column-toggle-btn" type="button" id="columnToggleDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-columns me-1"></i> কলাম নিয়ন্ত্রণ
                                </button>
                                <div class="dropdown-menu column-toggle-dropdown" aria-labelledby="columnToggleDropdown">
                                    <div class="column-toggle-item">
                                        <input type="checkbox" class="form-check-input" id="toggle-student-id" checked>
                                        <label for="toggle-student-id">শিক্ষার্থী আইডি</label>
                                    </div>
                                    <div class="column-toggle-item">
                                        <input type="checkbox" class="form-check-input" id="toggle-name" checked>
                                        <label for="toggle-name">নাম</label>
                                    </div>
                                    <div class="column-toggle-item">
                                        <input type="checkbox" class="form-check-input" id="toggle-email" checked>
                                        <label for="toggle-email">ইমেইল</label>
                                    </div>
                                    <div class="column-toggle-item">
                                        <input type="checkbox" class="form-check-input" id="toggle-phone" checked>
                                        <label for="toggle-phone">ফোন</label>
                                    </div>
                                    <div class="column-toggle-item">
                                        <input type="checkbox" class="form-check-input" id="toggle-department" checked>
                                        <label for="toggle-department">বিভাগ</label>
                                    </div>
                                    <div class="column-toggle-item">
                                        <input type="checkbox" class="form-check-input" id="toggle-session" checked>
                                        <label for="toggle-session">সেশন</label>
                                    </div>
                                    <div class="column-toggle-item">
                                        <input type="checkbox" class="form-check-input" id="toggle-subjects" checked>
                                        <label for="toggle-subjects">নির্বাচিত বিষয়সমূহ</label>
                                    </div>
                                    <hr>
                                    <div class="d-flex justify-content-between">
                                        <button class="btn btn-sm btn-outline-primary" id="show-all-columns">সব দেখান</button>
                                        <button class="btn btn-sm btn-outline-secondary" id="hide-all-columns">সব লুকান</button>
                                    </div>
                                </div>
                            </div>

                            <div class="btn-group" role="group" aria-label="View Options">
                                <button type="button" class="btn btn-outline-primary view-option" data-view="grid">
                                    <i class="fas fa-th-large"></i>
                                </button>
                                <button type="button" class="btn btn-outline-primary active view-option" data-view="list">
                                    <i class="fas fa-list"></i>
                                </button>
                                <button type="button" class="btn btn-outline-primary view-option" data-view="detail">
                                    <i class="fas fa-th-list"></i>
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover student-subjects-table">
                                <thead class="table-light">
                                    <tr>
                                        <th class="column-student-id">শিক্ষার্থী আইডি</th>
                                        <th class="column-name">নাম</th>
                                        <th class="column-email">ইমেইল</th>
                                        <th class="column-phone">ফোন</th>
                                        <th class="column-department">বিভাগ</th>
                                        <th class="column-session">সেশন</th>
                                        <th class="column-subjects" style="width: 40%;">নির্বাচিত বিষয়সমূহ</th>
                                        <th class="column-actions">অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($students && $students->num_rows > 0): ?>
                                        <?php while ($student = $students->fetch_assoc()): ?>
                                            <tr class="student-info-row">
                                                <td class="column-student-id"><?php echo htmlspecialchars($student['student_id'] ?? ''); ?></td>
                                                <td class="column-name"><?php echo htmlspecialchars(($student['first_name'] ?? '') . ' ' . ($student['last_name'] ?? '')); ?></td>
                                                <td class="column-email"><?php echo htmlspecialchars($student['email'] ?? ''); ?></td>
                                                <td class="column-phone"><?php echo htmlspecialchars($student['phone'] ?? ''); ?></td>
                                                <td class="column-department"><?php echo htmlspecialchars($student['department_name'] ?? 'N/A'); ?></td>
                                                <td class="column-session"><?php echo htmlspecialchars($student['session_name'] ?? 'N/A'); ?></td>
                                                <td class="column-subjects">
                                                    <div class="subject-categories">
                                                        <?php if (!empty($student['required_subjects'])): ?>
                                                        <div class="subject-category">
                                                            <span class="badge bg-primary">আবশ্যিক</span>
                                                            <span class="subject-list"><?php echo htmlspecialchars($student['required_subjects'] ?? ''); ?></span>
                                                        </div>
                                                        <?php endif; ?>

                                                        <?php if (!empty($student['optional_subjects'])): ?>
                                                        <div class="subject-category">
                                                            <span class="badge bg-info">ঐচ্ছিক</span>
                                                            <span class="subject-list"><?php echo htmlspecialchars($student['optional_subjects'] ?? ''); ?></span>
                                                        </div>
                                                        <?php endif; ?>

                                                        <?php if (!empty($student['fourth_subjects'])): ?>
                                                        <div class="subject-category">
                                                            <span class="badge bg-warning">৪র্থ</span>
                                                            <span class="subject-list"><?php echo htmlspecialchars($student['fourth_subjects'] ?? ''); ?></span>
                                                        </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                                <td class="column-actions table-actions">
                                                    <a href="view_student.php?id=<?php echo $student['id']; ?>" class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="student_subject_selection.php?id=<?php echo $student['id']; ?>" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-edit"></i> বিষয় সম্পাদনা
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="8" class="text-center">কোন শিক্ষার্থী খুঁজে পাওয়া যায়নি</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                            <nav>
                                <ul class="pagination justify-content-center">
                                    <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($searchTerm); ?>&department=<?php echo urlencode($departmentFilter); ?>&session=<?php echo urlencode($sessionFilter); ?>&subject=<?php echo urlencode($subjectFilter); ?>">
                                            পূর্ববর্তী
                                        </a>
                                    </li>

                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                        <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($searchTerm); ?>&department=<?php echo urlencode($departmentFilter); ?>&session=<?php echo urlencode($sessionFilter); ?>&subject=<?php echo urlencode($subjectFilter); ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>

                                    <li class="page-item <?php echo ($page >= $totalPages) ? 'disabled' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($searchTerm); ?>&department=<?php echo urlencode($departmentFilter); ?>&session=<?php echo urlencode($sessionFilter); ?>&subject=<?php echo urlencode($subjectFilter); ?>">
                                            পরবর্তী
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get view toggle buttons
            const viewOptions = document.querySelectorAll('.view-option');
            const tableContainer = document.querySelector('.table-responsive');

            // Current active view
            let currentView = 'list'; // Default view

            // Add click event to all view options
            viewOptions.forEach(button => {
                button.addEventListener('click', function() {
                    const viewType = this.getAttribute('data-view');

                    // Update active button
                    viewOptions.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    // Handle view change
                    changeView(viewType);
                });
            });

            // Function to change the view
            function changeView(viewType) {
                if (viewType === currentView) return;

                currentView = viewType;

                // You can add custom view logic here based on the viewType
                // For now, we'll just show an alert
                if (viewType === 'grid') {
                    alert('গ্রিড ভিউ বৈশিষ্ট্য বর্তমানে বিকাশের অধীনে আছে।');
                } else if (viewType === 'detail') {
                    alert('বিস্তারিত ভিউ বৈশিষ্ট্য বর্তমানে বিকাশের অধীনে আছে।');
                }

                // Default to list view for now
                // In the future, you can implement actual view changes here
            }

            // Add toggle functionality for subject rows
            const studentRows = document.querySelectorAll('.student-info-row');

            // Initially hide all subject rows
            document.querySelectorAll('.subject-row').forEach(row => {
                row.classList.add('d-none');
            });

            studentRows.forEach(row => {
                row.addEventListener('click', function(e) {
                    // Don't toggle if clicking on action buttons
                    if (e.target.closest('.table-actions') || e.target.closest('a')) {
                        return;
                    }

                    // Toggle the next row (subject row)
                    const subjectRow = this.nextElementSibling;
                    if (subjectRow && subjectRow.classList.contains('subject-row')) {
                        subjectRow.classList.toggle('d-none');

                        // Toggle highlight on student row
                        this.classList.toggle('table-active');
                    }
                });
            });

            // Column visibility control
            const columnToggles = {
                'student-id': document.getElementById('toggle-student-id'),
                'name': document.getElementById('toggle-name'),
                'email': document.getElementById('toggle-email'),
                'phone': document.getElementById('toggle-phone'),
                'department': document.getElementById('toggle-department'),
                'session': document.getElementById('toggle-session'),
                'subjects': document.getElementById('toggle-subjects')
            };

            // Function to toggle column visibility
            function toggleColumnVisibility(columnName, isVisible) {
                const columnElements = document.querySelectorAll(`.column-${columnName}`);
                columnElements.forEach(element => {
                    if (isVisible) {
                        element.classList.remove('hide-column');
                    } else {
                        element.classList.add('hide-column');
                    }
                });
            }

            // Add event listeners to checkboxes
            Object.keys(columnToggles).forEach(columnName => {
                const checkbox = columnToggles[columnName];
                if (checkbox) {
                    checkbox.addEventListener('change', function() {
                        toggleColumnVisibility(columnName, this.checked);
                    });
                }
            });

            // Show all columns button
            document.getElementById('show-all-columns').addEventListener('click', function() {
                Object.keys(columnToggles).forEach(columnName => {
                    const checkbox = columnToggles[columnName];
                    if (checkbox) {
                        checkbox.checked = true;
                        toggleColumnVisibility(columnName, true);
                    }
                });
            });

            // Hide all columns button (except name and subjects)
            document.getElementById('hide-all-columns').addEventListener('click', function() {
                Object.keys(columnToggles).forEach(columnName => {
                    const checkbox = columnToggles[columnName];
                    if (checkbox && columnName !== 'name' && columnName !== 'subjects') {
                        checkbox.checked = false;
                        toggleColumnVisibility(columnName, false);
                    }
                });
            });
        });
    </script>
</body>
</html>