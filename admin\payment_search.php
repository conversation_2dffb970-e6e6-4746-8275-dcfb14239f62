<?php
session_start();
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Initialize variables
$payments = [];
$errorMessage = '';
$searchTerm = $_GET['search'] ?? '';
$debugInfo = [];

try {
    // Check if fee_payments table exists
    $tableCheck = $conn->query("SHOW TABLES LIKE 'fee_payments'");
    
    if ($tableCheck->num_rows == 0) {
        $errorMessage = 'fee_payments টেবিল পাওয়া যায়নি। প্রথমে সেটআপ চালান।';
    } else {
        $debugInfo[] = "fee_payments table exists";
        
        // Build the query step by step
        $baseQuery = "SELECT fp.id, fp.receipt_no, fp.amount, fp.payment_date, fp.payment_method, fp.notes, fp.fee_id";
        $fromClause = " FROM fee_payments fp";
        $whereClause = "";
        $orderClause = " ORDER BY fp.created_at DESC LIMIT 20";
        
        $params = [];
        $types = '';
        
        if (!empty($searchTerm)) {
            $whereClause = " WHERE fp.receipt_no LIKE ?";
            $params[] = "%$searchTerm%";
            $types .= 's';
        }
        
        $finalQuery = $baseQuery . $fromClause . $whereClause . $orderClause;
        $debugInfo[] = "Query: " . $finalQuery;
        
        if (!empty($params)) {
            $stmt = $conn->prepare($finalQuery);
            $stmt->bind_param($types, ...$params);
            $stmt->execute();
            $result = $stmt->get_result();
        } else {
            $result = $conn->query($finalQuery);
        }
        
        if ($result) {
            $payments = $result->fetch_all(MYSQLI_ASSOC);
            $debugInfo[] = "Found " . count($payments) . " payments";
            
            // Now get additional info for each payment
            foreach ($payments as &$payment) {
                $payment['student_name'] = 'Unknown Student';
                $payment['fee_type'] = 'Unknown Fee';
                $payment['roll_no'] = 'N/A';
                $payment['class_name'] = 'N/A';
                
                try {
                    // Get fee info
                    $feeQuery = "SELECT fee_type, student_id FROM fees WHERE id = ?";
                    $feeStmt = $conn->prepare($feeQuery);
                    $feeStmt->bind_param("i", $payment['fee_id']);
                    $feeStmt->execute();
                    $feeResult = $feeStmt->get_result();
                    
                    if ($feeResult->num_rows > 0) {
                        $feeData = $feeResult->fetch_assoc();
                        $payment['fee_type'] = $feeData['fee_type'];
                        
                        // Get student info
                        $studentQuery = "SELECT first_name, last_name, roll_no, student_id, class_id FROM students WHERE id = ?";
                        $studentStmt = $conn->prepare($studentQuery);
                        $studentStmt->bind_param("i", $feeData['student_id']);
                        $studentStmt->execute();
                        $studentResult = $studentStmt->get_result();
                        
                        if ($studentResult->num_rows > 0) {
                            $studentData = $studentResult->fetch_assoc();
                            $payment['student_name'] = trim($studentData['first_name'] . ' ' . $studentData['last_name']);
                            $payment['roll_no'] = $studentData['roll_no'] ?? $studentData['student_id'] ?? 'N/A';
                            
                            // Get class name
                            if (!empty($studentData['class_id'])) {
                                $classQuery = "SELECT class_name FROM classes WHERE id = ?";
                                $classStmt = $conn->prepare($classQuery);
                                $classStmt->bind_param("i", $studentData['class_id']);
                                $classStmt->execute();
                                $classResult = $classStmt->get_result();
                                if ($classResult->num_rows > 0) {
                                    $classData = $classResult->fetch_assoc();
                                    $payment['class_name'] = $classData['class_name'];
                                }
                            }
                        }
                    }
                } catch (Exception $e) {
                    $debugInfo[] = "Error getting info for payment " . $payment['id'] . ": " . $e->getMessage();
                }
            }
            
            // If searching, also search by student info
            if (!empty($searchTerm) && count($payments) == 0) {
                $debugInfo[] = "No payments found by receipt, searching by student info";
                
                // Search by student name or roll
                $studentSearchQuery = "SELECT s.id as student_id FROM students s WHERE s.first_name LIKE ? OR s.last_name LIKE ? OR s.roll_no LIKE ? OR s.student_id LIKE ?";
                $searchParam = "%$searchTerm%";
                $studentStmt = $conn->prepare($studentSearchQuery);
                $studentStmt->bind_param("ssss", $searchParam, $searchParam, $searchParam, $searchParam);
                $studentStmt->execute();
                $studentResult = $studentStmt->get_result();
                
                if ($studentResult->num_rows > 0) {
                    $studentIds = [];
                    while ($row = $studentResult->fetch_assoc()) {
                        $studentIds[] = $row['student_id'];
                    }
                    
                    if (!empty($studentIds)) {
                        $placeholders = str_repeat('?,', count($studentIds) - 1) . '?';
                        $feeSearchQuery = "SELECT id FROM fees WHERE student_id IN ($placeholders)";
                        $feeStmt = $conn->prepare($feeSearchQuery);
                        $feeStmt->bind_param(str_repeat('i', count($studentIds)), ...$studentIds);
                        $feeStmt->execute();
                        $feeResult = $feeStmt->get_result();
                        
                        if ($feeResult->num_rows > 0) {
                            $feeIds = [];
                            while ($row = $feeResult->fetch_assoc()) {
                                $feeIds[] = $row['id'];
                            }
                            
                            if (!empty($feeIds)) {
                                $placeholders = str_repeat('?,', count($feeIds) - 1) . '?';
                                $paymentSearchQuery = "SELECT fp.* FROM fee_payments fp WHERE fp.fee_id IN ($placeholders) ORDER BY fp.created_at DESC LIMIT 20";
                                $paymentStmt = $conn->prepare($paymentSearchQuery);
                                $paymentStmt->bind_param(str_repeat('i', count($feeIds)), ...$feeIds);
                                $paymentStmt->execute();
                                $paymentResult = $paymentStmt->get_result();
                                
                                if ($paymentResult->num_rows > 0) {
                                    $payments = $paymentResult->fetch_all(MYSQLI_ASSOC);
                                    $debugInfo[] = "Found " . count($payments) . " payments by student search";
                                    
                                    // Get additional info for these payments too
                                    foreach ($payments as &$payment) {
                                        $payment['student_name'] = 'Unknown Student';
                                        $payment['fee_type'] = 'Unknown Fee';
                                        $payment['roll_no'] = 'N/A';
                                        $payment['class_name'] = 'N/A';
                                        
                                        try {
                                            // Get fee info
                                            $feeQuery = "SELECT fee_type, student_id FROM fees WHERE id = ?";
                                            $feeStmt = $conn->prepare($feeQuery);
                                            $feeStmt->bind_param("i", $payment['fee_id']);
                                            $feeStmt->execute();
                                            $feeResult = $feeStmt->get_result();
                                            
                                            if ($feeResult->num_rows > 0) {
                                                $feeData = $feeResult->fetch_assoc();
                                                $payment['fee_type'] = $feeData['fee_type'];
                                                
                                                // Get student info
                                                $studentQuery = "SELECT first_name, last_name, roll_no, student_id, class_id FROM students WHERE id = ?";
                                                $studentStmt = $conn->prepare($studentQuery);
                                                $studentStmt->bind_param("i", $feeData['student_id']);
                                                $studentStmt->execute();
                                                $studentResult = $studentStmt->get_result();
                                                
                                                if ($studentResult->num_rows > 0) {
                                                    $studentData = $studentResult->fetch_assoc();
                                                    $payment['student_name'] = trim($studentData['first_name'] . ' ' . $studentData['last_name']);
                                                    $payment['roll_no'] = $studentData['roll_no'] ?? $studentData['student_id'] ?? 'N/A';
                                                    
                                                    // Get class name
                                                    if (!empty($studentData['class_id'])) {
                                                        $classQuery = "SELECT class_name FROM classes WHERE id = ?";
                                                        $classStmt = $conn->prepare($classQuery);
                                                        $classStmt->bind_param("i", $studentData['class_id']);
                                                        $classStmt->execute();
                                                        $classResult = $classStmt->get_result();
                                                        if ($classResult->num_rows > 0) {
                                                            $classData = $classResult->fetch_assoc();
                                                            $payment['class_name'] = $classData['class_name'];
                                                        }
                                                    }
                                                }
                                            }
                                        } catch (Exception $e) {
                                            $debugInfo[] = "Error getting info for payment " . $payment['id'] . ": " . $e->getMessage();
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } else {
            $debugInfo[] = "Query failed: " . $conn->error;
        }
    }
} catch (Exception $e) {
    $errorMessage = 'ডাটাবেস এরর: ' . $e->getMessage();
    $debugInfo[] = "Exception: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পেমেন্ট সার্চ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', Arial, sans-serif;
            background-color: #f8f9fa;
        }
        .payment-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 20px;
            border-left: 4px solid #007bff;
        }
        .receipt-badge {
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .amount-display {
            font-size: 20px;
            font-weight: bold;
            color: #28a745;
        }
        .student-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-search me-2"></i>পেমেন্ট সার্চ</h2>
                    <div>
                        <a href="fix_database.php" class="btn btn-warning me-2">
                            <i class="fas fa-wrench me-2"></i>ডাটাবেস ঠিক করুন
                        </a>
                        <a href="fee_management.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>ফিরে যান
                        </a>
                    </div>
                </div>

                <?php if (!empty($errorMessage)): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?= htmlspecialchars($errorMessage) ?>
                    <br><small><a href="fix_database.php" class="alert-link">ডাটাবেস ঠিক করুন</a></small>
                </div>
                <?php endif; ?>

                <!-- Search -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-8">
                                <input type="text" class="form-control" name="search" 
                                       value="<?= htmlspecialchars($searchTerm) ?>" 
                                       placeholder="নাম, রোল নং, রিসিপ্ট নং দিয়ে খুঁজুন...">
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>খুঁজুন
                                </button>
                                <a href="payment_search.php" class="btn btn-outline-secondary ms-1">
                                    <i class="fas fa-refresh"></i>
                                </a>
                                <a href="?debug=1<?= !empty($searchTerm) ? '&search=' . urlencode($searchTerm) : '' ?>" class="btn btn-outline-info ms-1">
                                    <i class="fas fa-bug"></i>
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Debug Info -->
                <?php if (isset($_GET['debug'])): ?>
                <div class="alert alert-info">
                    <h6>ডিবাগ তথ্য:</h6>
                    <ul>
                        <?php foreach ($debugInfo as $info): ?>
                        <li><?= htmlspecialchars($info) ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <?php if (!empty($payments)): ?>
                    <p><strong>প্রথম পেমেন্টের ডেটা:</strong></p>
                    <pre><?= print_r($payments[0], true) ?></pre>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <!-- Payments -->
                <div class="row">
                    <?php if (empty($payments)): ?>
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-receipt fa-3x mb-3 text-muted"></i>
                            <h4>কোন পেমেন্ট পাওয়া যায়নি</h4>
                            <?php if (empty($errorMessage)): ?>
                            <p>প্রথমে <a href="fix_database.php">ডাটাবেস ঠিক করুন</a> এবং নমুনা ডেটা যোগ করুন</p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php else: ?>
                    <?php foreach ($payments as $payment): ?>
                    <div class="col-md-6 col-lg-4">
                        <div class="payment-card">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span class="receipt-badge"><?= htmlspecialchars($payment['receipt_no'] ?? 'N/A') ?></span>
                                <small class="text-muted"><?= date('d/m/Y', strtotime($payment['payment_date'])) ?></small>
                            </div>
                            
                            <div class="student-info">
                                <h6 class="mb-1">
                                    <i class="fas fa-user me-2"></i><?= htmlspecialchars($payment['student_name']) ?>
                                </h6>
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">রোল:</small><br>
                                        <strong><?= htmlspecialchars($payment['roll_no']) ?></strong>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">ক্লাস:</small><br>
                                        <strong><?= htmlspecialchars($payment['class_name']) ?></strong>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-2">
                                <strong>ফি ধরন:</strong>
                                <span class="text-info"><?= htmlspecialchars($payment['fee_type']) ?></span>
                            </div>
                            
                            <div class="mb-3">
                                <strong>পরিমাণ:</strong>
                                <span class="amount-display">৳<?= number_format($payment['amount'], 2) ?></span>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <a href="receipt_simple.php?receipt_no=<?= urlencode($payment['receipt_no']) ?>"
                                   target="_blank" class="btn btn-success">
                                    <i class="fas fa-receipt me-2"></i>রিসিপ্ট দেখুন
                                </a>
                                <a href="receipt_print.php?receipt_no=<?= urlencode($payment['receipt_no']) ?>"
                                   target="_blank" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-print me-2"></i>প্রিন্ট ভার্সন
                                </a>
                            </div>
                            
                            <?php if (!empty($payment['notes'])): ?>
                            <div class="mt-2">
                                <small class="text-muted">নোট: <?= htmlspecialchars($payment['notes']) ?></small>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <!-- Stats -->
                <?php if (!empty($payments)): ?>
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-primary"><?= count($payments) ?></h3>
                                <p class="mb-0">পেমেন্ট পাওয়া গেছে</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-success">৳<?= number_format(array_sum(array_column($payments, 'amount')), 2) ?></h3>
                                <p class="mb-0">মোট পরিমাণ</p>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
