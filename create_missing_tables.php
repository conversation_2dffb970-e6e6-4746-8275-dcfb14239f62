<?php
// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "college_management";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Create teachers table
$teachersTableQuery = "CREATE TABLE IF NOT EXISTS teachers (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    teacher_id VARCHAR(20) NOT NULL UNIQUE,
    first_name VARCHAR(50) NOT NULL,
    last_name VA<PERSON>HAR(50) NOT NULL,
    email VARCHAR(100) NULL,
    phone VARCHAR(20) NULL,
    gender ENUM('male', 'female', 'other') NOT NULL,
    dob DATE NULL,
    address TEXT NULL,
    city VARCHAR(50) NULL,
    state VARCHAR(50) NULL,
    postal_code VARCHAR(20) NULL,
    country VARCHAR(50) NULL DEFAULT 'Bangladesh',
    joining_date DATE NULL,
    profile_photo VARCHAR(255) NULL,
    department_id INT(11) NULL,
    user_id INT(11) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
)";

if ($conn->query($teachersTableQuery)) {
    echo "Teachers table created successfully!<br>";
} else {
    echo "Error creating teachers table: " . $conn->error . "<br>";
}

// Create staff table
$staffTableQuery = "CREATE TABLE IF NOT EXISTS staff (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    staff_id VARCHAR(20) NOT NULL UNIQUE,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) NULL,
    phone VARCHAR(20) NULL,
    gender ENUM('male', 'female', 'other') NOT NULL,
    dob DATE NULL,
    address TEXT NULL,
    city VARCHAR(50) NULL,
    state VARCHAR(50) NULL,
    postal_code VARCHAR(20) NULL,
    country VARCHAR(50) NULL DEFAULT 'Bangladesh',
    joining_date DATE NULL,
    profile_photo VARCHAR(255) NULL,
    department_id INT(11) NULL,
    user_id INT(11) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
)";

if ($conn->query($staffTableQuery)) {
    echo "Staff table created successfully!<br>";
} else {
    echo "Error creating staff table: " . $conn->error . "<br>";
}

echo "<p>All tables have been created. <a href='admin/dashboard.php'>Go to Admin Dashboard</a></p>";

$conn->close();
?> 