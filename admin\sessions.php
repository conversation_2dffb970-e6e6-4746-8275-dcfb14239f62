<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$errorMessage = "";
$successMessage = "";

// Check if we're editing a session
$editSession = null;
if (isset($_GET['edit'])) {
    $sessionId = $_GET['edit'];
    $editQuery = "SELECT * FROM sessions WHERE id = ?";
    $stmt = $conn->prepare($editQuery);
    $stmt->bind_param("i", $sessionId);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result->num_rows > 0) {
        $editSession = $result->fetch_assoc();
    } else {
        $errorMessage = "সেশন খুঁজে পাওয়া যায়নি";
    }
}

// Handle session update
if (isset($_POST['update_session'])) {
    $sessionId = $_POST['session_id'];
    $sessionName = trim($_POST['session_name']);
    $startDate = $_POST['start_date'];
    $endDate = $_POST['end_date'];

    if (empty($sessionName) || empty($startDate) || empty($endDate)) {
        $errorMessage = "সকল ফিল্ড আবশ্যক!";
    } else {
        // Check if session name already exists for other sessions
        $checkQuery = "SELECT 1 FROM sessions WHERE session_name = ? AND id != ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param("si", $sessionName, $sessionId);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $errorMessage = "এই সেশন নামটি ইতিমধ্যে বিদ্যমান!";
        } else {
            // Update session
            $updateQuery = "UPDATE sessions SET session_name = ?, start_date = ?, end_date = ? WHERE id = ?";
            $stmt = $conn->prepare($updateQuery);
            $stmt->bind_param("sssi", $sessionName, $startDate, $endDate, $sessionId);

            if ($stmt->execute()) {
                $successMessage = "সেশন সফলভাবে আপডেট করা হয়েছে!";
                // Redirect to remove the edit parameter
                header("Location: sessions.php?success=updated");
                exit();
            } else {
                $errorMessage = "সেশন আপডেট করতে সমস্যা: " . $conn->error;
            }
        }
    }
}

// Handle session creation
if (isset($_POST['add_session'])) {
    $sessionName = trim($_POST['session_name']);
    $startDate = $_POST['start_date'];
    $endDate = $_POST['end_date'];

    if (empty($sessionName) || empty($startDate) || empty($endDate)) {
        $errorMessage = "সকল ফিল্ড আবশ্যক!";
    } else {
        // Check if session already exists
        $checkQuery = "SELECT 1 FROM sessions WHERE session_name = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param("s", $sessionName);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $errorMessage = "এই সেশন ইতিমধ্যে বিদ্যমান!";
        } else {
            // Insert new session
            $insertQuery = "INSERT INTO sessions (session_name, start_date, end_date) VALUES (?, ?, ?)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param("sss", $sessionName, $startDate, $endDate);

            if ($stmt->execute()) {
                $successMessage = "সেশন সফলভাবে যোগ করা হয়েছে!";
            } else {
                $errorMessage = "সেশন যোগ করতে সমস্যা: " . $conn->error;
            }
        }
    }
}

// Handle session deletion
if (isset($_GET['delete'])) {
    $sessionId = $_GET['delete'];

    // Check if session is being used by students
    $checkQuery = "SELECT 1 FROM students WHERE session_id = ?";
    $stmt = $conn->prepare($checkQuery);
    $stmt->bind_param("i", $sessionId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $errorMessage = "এই সেশন মুছা যাবে না কারণ এটি শিক্ষার্থীদের জন্য বরাদ্দ করা আছে!";
    } else {
        $deleteQuery = "DELETE FROM sessions WHERE id = ?";
        $stmt = $conn->prepare($deleteQuery);
        $stmt->bind_param("i", $sessionId);

        if ($stmt->execute()) {
            $successMessage = "সেশন সফলভাবে মুছে ফেলা হয়েছে!";
        } else {
            $errorMessage = "সেশন মুছতে সমস্যা: " . $conn->error;
        }
    }
}

// Check if we have a success message from redirect
if (isset($_GET['success']) && $_GET['success'] === 'updated') {
    $successMessage = "সেশন সফলভাবে আপডেট করা হয়েছে!";
}

// Get all sessions
$sessionsQuery = "SELECT * FROM sessions ORDER BY start_date DESC";
$sessions = $conn->query($sessionsQuery);

// Create sessions table if it doesn't exist
$createTableQuery = "CREATE TABLE IF NOT EXISTS sessions (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    session_name VARCHAR(50) NOT NULL UNIQUE,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
$conn->query($createTableQuery);

// Create classes table if it doesn't exist
$createClassesQuery = "CREATE TABLE IF NOT EXISTS classes (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    class_name VARCHAR(50) NOT NULL,
    department_id INT(11) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_class_dept (class_name, department_id)
)";
$conn->query($createClassesQuery);

// Add session_id to students table if it doesn't exist
$checkColumnQuery = "SHOW COLUMNS FROM students LIKE 'session_id'";
$columnExists = $conn->query($checkColumnQuery);
if ($columnExists->num_rows === 0) {
    $addColumnQuery = "ALTER TABLE students ADD COLUMN session_id INT(11) NULL";
    $conn->query($addColumnQuery);
}

// Add class_id to students table if it doesn't exist
$checkClassColumnQuery = "SHOW COLUMNS FROM students LIKE 'class_id'";
$classColumnExists = $conn->query($checkClassColumnQuery);
if ($classColumnExists->num_rows === 0) {
    $addClassColumnQuery = "ALTER TABLE students ADD COLUMN class_id INT(11) NULL";
    $conn->query($addClassColumnQuery);
}

// Add department_id to students table if it doesn't exist
$checkDeptColumnQuery = "SHOW COLUMNS FROM students LIKE 'department_id'";
$deptColumnExists = $conn->query($checkDeptColumnQuery);
if ($deptColumnExists->num_rows === 0) {
    $addDeptColumnQuery = "ALTER TABLE students ADD COLUMN department_id INT(11) NULL";
    $conn->query($addDeptColumnQuery);
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>সেশন ব্যবস্থাপনা - কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="sessions.php">
                            <i class="fas fa-calendar-alt me-2"></i> সেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-file-pdf me-2"></i> রিপোর্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>সেশন ব্যবস্থাপনা</h2>
                        <p class="text-muted">শিক্ষাবর্ষ যোগ করুন, দেখুন এবং পরিচালনা করুন</p>
                    </div>
                </div>

                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $errorMessage; ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo $successMessage; ?>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- Add/Edit Session Form -->
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0"><?php echo isset($editSession) ? 'সেশন সম্পাদনা করুন' : 'নতুন সেশন যোগ করুন'; ?></h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="<?php echo isset($editSession) ? 'sessions.php?edit=' . $editSession['id'] : 'sessions.php'; ?>">
                                    <?php if (isset($editSession)): ?>
                                        <input type="hidden" name="session_id" value="<?php echo $editSession['id']; ?>">
                                    <?php endif; ?>

                                    <div class="mb-3">
                                        <label for="session_name" class="form-label">সেশনের নাম*</label>
                                        <input type="text" class="form-control" id="session_name" name="session_name"
                                               value="<?php echo isset($editSession) ? htmlspecialchars($editSession['session_name']) : ''; ?>" required>
                                        <small class="form-text text-muted">উদাহরণ: ২০২৩-২০২৪, বসন্ত ২০২৩, ইত্যাদি</small>
                                    </div>
                                    <div class="mb-3">
                                        <label for="start_date" class="form-label">শুরুর তারিখ*</label>
                                        <input type="date" class="form-control" id="start_date" name="start_date"
                                               value="<?php echo isset($editSession) ? $editSession['start_date'] : ''; ?>" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="end_date" class="form-label">শেষের তারিখ*</label>
                                        <input type="date" class="form-control" id="end_date" name="end_date"
                                               value="<?php echo isset($editSession) ? $editSession['end_date'] : ''; ?>" required>
                                    </div>

                                    <?php if (isset($editSession)): ?>
                                        <button type="submit" name="update_session" class="btn btn-primary">আপডেট করুন</button>
                                        <a href="sessions.php" class="btn btn-secondary">বাতিল করুন</a>
                                    <?php else: ?>
                                        <button type="submit" name="add_session" class="btn btn-primary">সেশন যোগ করুন</button>
                                    <?php endif; ?>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Sessions List -->
                    <div class="col-md-8 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">সেশন তালিকা</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>সেশনের নাম</th>
                                                <th>শুরুর তারিখ</th>
                                                <th>শেষের তারিখ</th>
                                                <th>অ্যাকশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($sessions && $sessions->num_rows > 0): ?>
                                                <?php while ($session = $sessions->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($session['session_name']); ?></td>
                                                        <td><?php echo date('d/m/Y', strtotime($session['start_date'])); ?></td>
                                                        <td><?php echo date('d/m/Y', strtotime($session['end_date'])); ?></td>
                                                        <td>
                                                            <a href="sessions.php?edit=<?php echo $session['id']; ?>"
                                                               class="btn btn-warning btn-sm">
                                                                <i class="fas fa-edit"></i> সম্পাদনা
                                                            </a>
                                                            <a href="sessions.php?delete=<?php echo $session['id']; ?>"
                                                               class="btn btn-danger btn-sm"
                                                               onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই সেশনটি মুছে ফেলতে চান?')">
                                                                <i class="fas fa-trash"></i> মুছুন
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="4" class="text-center">কোন সেশন পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Department Grouping -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">বিভাগ অনুযায়ী গ্রুপিং</h5>
                            </div>
                            <div class="card-body">
                                <h6 class="mb-3">বিভাগ এবং সেশন অনুযায়ী শিক্ষার্থী</h6>

                                <?php
                                // Get department and session counts
                                $deptSessionQuery = "SELECT d.department_name as department, se.session_name, COUNT(*) as count
                                                  FROM students s
                                                  LEFT JOIN sessions se ON s.session_id = se.id
                                                  LEFT JOIN departments d ON s.department_id = d.id
                                                  GROUP BY d.department_name, se.session_name
                                                  ORDER BY d.department_name, se.session_name";
                                $deptSessionResults = $conn->query($deptSessionQuery);

                                $departments = [];
                                $sessions = [];
                                $data = [];

                                if ($deptSessionResults && $deptSessionResults->num_rows > 0) {
                                    while ($row = $deptSessionResults->fetch_assoc()) {
                                        $dept = $row['department'] ?? 'অনির্ধারিত';
                                        $sess = $row['session_name'] ?? 'অনির্ধারিত';

                                        if (!in_array($dept, $departments)) {
                                            $departments[] = $dept;
                                        }

                                        if (!in_array($sess, $sessions) && $sess != 'অনির্ধারিত') {
                                            $sessions[] = $sess;
                                        }

                                        $data[$dept][$sess] = $row['count'];
                                    }

                                    // Display data in table
                                    echo '<div class="table-responsive">';
                                    echo '<table class="table table-bordered">';

                                    // Header row
                                    echo '<thead class="table-light"><tr><th>বিভাগ</th>';
                                    foreach ($sessions as $sess) {
                                        echo '<th>' . htmlspecialchars($sess) . '</th>';
                                    }
                                    echo '<th>অনির্ধারিত</th>';
                                    echo '<th>মোট</th></tr></thead>';

                                    // Data rows
                                    echo '<tbody>';
                                    foreach ($departments as $dept) {
                                        echo '<tr>';
                                        echo '<td>' . htmlspecialchars($dept) . '</td>';

                                        $deptTotal = 0;
                                        foreach ($sessions as $sess) {
                                            $count = $data[$dept][$sess] ?? 0;
                                            $deptTotal += $count;
                                            echo '<td>' . $count . '</td>';
                                        }

                                        // Unassigned column
                                        $unassigned = $data[$dept]['অনির্ধারিত'] ?? 0;
                                        $deptTotal += $unassigned;
                                        echo '<td>' . $unassigned . '</td>';

                                        // Total column
                                        echo '<td><strong>' . $deptTotal . '</strong></td>';

                                        echo '</tr>';
                                    }
                                    echo '</tbody>';

                                    echo '</table>';
                                    echo '</div>';
                                } else {
                                    echo '<div class="alert alert-info">গ্রুপিং এর জন্য কোন শিক্ষার্থীর তথ্য পাওয়া যায়নি।</div>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>