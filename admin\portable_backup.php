<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Create backup directory if it doesn't exist
$backupDir = '../backups';
if (!file_exists($backupDir)) {
    mkdir($backupDir, 0777, true);
}

// Function to create portable (non-encrypted) database backup
function createPortableBackup($conn, $backupDir) {
    global $dbname;

    // Ensure timezone is set
    date_default_timezone_set('Asia/Dhaka');

    $tables = array();
    $result = $conn->query("SHOW TABLES");

    while ($row = $result->fetch_row()) {
        $tables[] = $row[0];
    }

    $backupFile = $backupDir . '/' . $dbname . '_portable_' . date("Y-m-d_H-i-s") . '.sql';
    $output = "-- Portable Database Backup for $dbname - " . date("Y-m-d H:i:s") . "\n";
    $output .= "-- This backup can be used on any computer\n\n";

    foreach ($tables as $table) {
        try {
            // Check if table exists
            $tableExistsQuery = "SHOW TABLES LIKE '$table'";
            $tableExists = $conn->query($tableExistsQuery);

            if ($tableExists && $tableExists->num_rows > 0) {
                // Get table structure
                $result = $conn->query("SHOW CREATE TABLE `$table`");
                if ($result && $row = $result->fetch_row()) {
                    $output .= "\n\n" . $row[1] . ";\n\n";

                    // Get table data
                    $dataResult = $conn->query("SELECT * FROM `$table`");
                    if ($dataResult) {
                        $numFields = $dataResult->field_count;

                        while ($row = $dataResult->fetch_row()) {
                            $output .= "INSERT INTO `$table` VALUES(";

                            for ($i = 0; $i < $numFields; $i++) {
                                if (isset($row[$i])) {
                                    // Escape special characters
                                    $row[$i] = str_replace("\n", "\\n", addslashes($row[$i]));
                                    $output .= '"' . $row[$i] . '"';
                                } else {
                                    $output .= 'NULL';
                                }

                                if ($i < ($numFields - 1)) {
                                    $output .= ',';
                                }
                            }

                            $output .= ");\n";
                        }
                    }

                    $output .= "\n\n";
                }
            }
        } catch (Exception $exception) {
            // Skip this table and continue with others
            // Optionally log the error: error_log("Error backing up table $table: " . $exception->getMessage());
            continue;
        }
    }

    // Save the backup file
    if (file_put_contents($backupFile, $output)) {
        return basename($backupFile);
    } else {
        return false;
    }
}

// Handle backup creation
if (isset($_POST['create_backup'])) {
    $backupFile = createPortableBackup($conn, $backupDir);

    if ($backupFile) {
        $successMessage = "পোর্টেবল ব্যাকআপ সফলভাবে তৈরি করা হয়েছে: $backupFile";
    } else {
        $errorMessage = "ব্যাকআপ তৈরি করতে সমস্যা হয়েছে।";
    }
}

// Handle backup download
if (isset($_GET['download']) && !empty($_GET['download'])) {
    $backupFile = $backupDir . '/' . $_GET['download'];

    if (file_exists($backupFile)) {
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . basename($backupFile) . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        header('Content-Length: ' . filesize($backupFile));
        readfile($backupFile);
        exit;
    } else {
        $errorMessage = "ব্যাকআপ ফাইল পাওয়া যায়নি।";
    }
}

// Set timezone to Asia/Dhaka (Bangladesh)
date_default_timezone_set('Asia/Dhaka');

// Get list of portable backup files
function getPortableBackupFiles($backupDir) {
    $files = array();

    if (is_dir($backupDir)) {
        $dirHandle = opendir($backupDir);

        while (($file = readdir($dirHandle)) !== false) {
            if (strpos($file, '_portable_') !== false && pathinfo($file, PATHINFO_EXTENSION) === 'sql') {
                $files[] = $file;
            }
        }

        closedir($dirHandle);
        rsort($files); // Sort by newest first
    }

    return $files;
}

$portableBackupFiles = getPortableBackupFiles($backupDir);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>পোর্টেবল ব্যাকআপ - কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include_once '../includes/admin_sidebar.php'; ?>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>পোর্টেবল ডাটাবেজ ব্যাকআপ</h2>
                        <p class="text-muted">যেকোনো কম্পিউটারে ব্যবহার করা যাবে এমন ব্যাকআপ তৈরি করুন</p>
                    </div>
                </div>

                <?php if (isset($errorMessage)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $errorMessage; ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($successMessage)): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo $successMessage; ?>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-12">
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">পোর্টেবল ব্যাকআপ তৈরি করুন</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>সতর্কতা:</strong> পোর্টেবল ব্যাকআপ ফাইলগুলি এনক্রিপ্ট করা হয় না, তাই যেকোনো কম্পিউটারে এগুলি ব্যবহার করা যাবে। এটি সুবিধাজনক, কিন্তু নিরাপত্তার জন্য সতর্কতা অবলম্বন করুন।
                                </div>

                                <form method="POST" action="portable_backup.php">
                                    <button type="submit" name="create_backup" class="btn btn-primary">
                                        <i class="fas fa-download me-2"></i>পোর্টেবল ব্যাকআপ তৈরি করুন
                                    </button>
                                </form>

                                <hr>

                                <h5>পোর্টেবল ব্যাকআপ ফাইল তালিকা</h5>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>ফাইল নাম</th>
                                                <th>তারিখ</th>
                                                <th>সাইজ</th>
                                                <th>অ্যাকশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (empty($portableBackupFiles)): ?>
                                                <tr>
                                                    <td colspan="4" class="text-center">কোন পোর্টেবল ব্যাকআপ ফাইল পাওয়া যায়নি</td>
                                                </tr>
                                            <?php else: ?>
                                                <?php foreach ($portableBackupFiles as $file): ?>
                                                    <tr>
                                                        <td><?php echo $file; ?></td>
                                                        <td>
                                                            <?php
                                                            // Extract date from filename instead of using filemtime
                                                            preg_match('/portable_(\d{4}-\d{2}-\d{2})_(\d{2})-(\d{2})-(\d{2})/', $file, $matches);
                                                            if (count($matches) >= 5) {
                                                                $date = $matches[1];
                                                                $hour = $matches[2];
                                                                $min = $matches[3];
                                                                $sec = $matches[4];

                                                                // Format date in Bangla style
                                                                $dateObj = new DateTime("$date $hour:$min:$sec");
                                                                echo $dateObj->format('F j, Y, g:i a');
                                                            } else {
                                                                // Fallback to filemtime if pattern doesn't match
                                                                echo date("F j, Y, g:i a", filemtime($backupDir . '/' . $file));
                                                            }
                                                            ?>
                                                        </td>
                                                        <td><?php echo number_format(round(filesize($backupDir . '/' . $file) / 1024, 2), 2); ?> KB</td>
                                                        <td>
                                                            <a href="portable_backup.php?download=<?php echo $file; ?>" class="btn btn-sm btn-primary">
                                                                <i class="fas fa-download"></i> ডাউনলোড
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0">অন্য কম্পিউটারে ব্যবহার করার নির্দেশাবলী</h5>
                            </div>
                            <div class="card-body">
                                <ol>
                                    <li>উপরে "পোর্টেবল ব্যাকআপ তৈরি করুন" বাটনে ক্লিক করে একটি পোর্টেবল ব্যাকআপ তৈরি করুন।</li>
                                    <li>তৈরি হওয়া ব্যাকআপ ফাইলটি ডাউনলোড করুন।</li>
                                    <li>ব্যাকআপ ফাইলটি পেন ড্রাইভ বা অন্য কোন মাধ্যমে অন্য কম্পিউটারে নিয়ে যান।</li>
                                    <li>অন্য কম্পিউটারে একই ডাটাবেজ নাম (<strong><?php echo $dbname; ?></strong>) সহ একটি খালি ডাটাবেজ তৈরি করুন।</li>
                                    <li>phpMyAdmin বা অন্য কোন MySQL ক্লায়েন্ট ব্যবহার করে ব্যাকআপ ফাইলটি ইম্পোর্ট করুন।</li>
                                </ol>

                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>phpMyAdmin দিয়ে ইম্পোর্ট করার নির্দেশাবলী:</h6>
                                    <ol>
                                        <li>ব্রাউজারে <code>http://localhost/phpmyadmin/</code> ওপেন করুন</li>
                                        <li>বাম পাশে ডাটাবেজ লিস্ট থেকে <strong><?php echo $dbname; ?></strong> সিলেক্ট করুন</li>
                                        <li>উপরের মেনু থেকে "Import" ট্যাবে ক্লিক করুন</li>
                                        <li>"Choose File" বাটনে ক্লিক করে আপনার ডাউনলোড করা ব্যাকআপ ফাইলটি সিলেক্ট করুন</li>
                                        <li>পেজের নিচে "Go" বাটনে ক্লিক করুন</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <a href="database_backup.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>ডাটাবেজ ব্যাকআপ পেজে ফিরে যান
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
