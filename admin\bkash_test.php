<?php
session_start();
require_once '../includes/dbh.inc.php';
require_once '../includes/bkash_config.php';
require_once '../includes/bkash_functions.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Create bKash payments table
createBkashPaymentsTable($conn);

// Include header
include_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Include sidebar -->
        <?php include_once 'includes/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">বিকাশ পেমেন্ট টেস্ট</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="fees.php" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> ফিরে যান
                    </a>
                </div>
            </div>

            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">বিকাশ পেমেন্ট গেটওয়ে টেস্ট</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle me-2"></i> বিকাশ পেমেন্ট গেটওয়ে সেটআপ</h5>
                        <p>বিকাশ পেমেন্ট গেটওয়ে ব্যবহার করতে আপনাকে নিম্নলিখিত পদক্ষেপগুলি সম্পন্ন করতে হবে:</p>
                        <ol>
                            <li>বিকাশ মার্চেন্ট অ্যাকাউন্ট তৈরি করুন</li>
                            <li>বিকাশ পেমেন্ট গেটওয়ে API ক্রেডেনশিয়ালস সংগ্রহ করুন</li>
                            <li>API ক্রেডেনশিয়ালস <code>includes/bkash_config.php</code> ফাইলে আপডেট করুন</li>
                        </ol>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="card-title mb-0">বিকাশ কনফিগারেশন</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th>মোড</th>
                                            <td><?= BKASH_SANDBOX ? 'স্যান্ডবক্স (টেস্ট)' : 'প্রোডাকশন (লাইভ)' ?></td>
                                        </tr>
                                        <tr>
                                            <th>API ভার্সন</th>
                                            <td><?= BKASH_VERSION ?></td>
                                        </tr>
                                        <tr>
                                            <th>APP KEY</th>
                                            <td>
                                                <?php if (BKASH_APP_KEY === 'your_app_key'): ?>
                                                    <span class="text-danger">কনফিগার করা হয়নি</span>
                                                <?php else: ?>
                                                    <span class="text-success">কনফিগার করা হয়েছে</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>APP SECRET</th>
                                            <td>
                                                <?php if (BKASH_APP_SECRET === 'your_app_secret'): ?>
                                                    <span class="text-danger">কনফিগার করা হয়নি</span>
                                                <?php else: ?>
                                                    <span class="text-success">কনফিগার করা হয়েছে</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>USERNAME</th>
                                            <td>
                                                <?php if (BKASH_USERNAME === 'your_username'): ?>
                                                    <span class="text-danger">কনফিগার করা হয়নি</span>
                                                <?php else: ?>
                                                    <span class="text-success">কনফিগার করা হয়েছে</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>PASSWORD</th>
                                            <td>
                                                <?php if (BKASH_PASSWORD === 'your_password'): ?>
                                                    <span class="text-danger">কনফিগার করা হয়নি</span>
                                                <?php else: ?>
                                                    <span class="text-success">কনফিগার করা হয়েছে</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="card-title mb-0">বিকাশ টোকেন টেস্ট</h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <button id="testTokenBtn" class="btn btn-primary">
                                            <i class="fas fa-key me-2"></i> টোকেন টেস্ট করুন
                                        </button>
                                    </div>

                                    <div id="tokenResult" class="mt-3 d-none">
                                        <div class="alert alert-info">
                                            <h6><i class="fas fa-spinner fa-spin me-2"></i> টোকেন টেস্ট চলছে...</h6>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card mt-3">
                                <div class="card-header bg-light">
                                    <h6 class="card-title mb-0">ডাটাবেস স্ট্যাটাস</h6>
                                </div>
                                <div class="card-body">
                                    <?php
                                    // Check if bkash_payments table exists
                                    $checkTableQuery = "SHOW TABLES LIKE 'bkash_payments'";
                                    $tableExists = $conn->query($checkTableQuery)->num_rows > 0;
                                    ?>

                                    <?php if ($tableExists): ?>
                                        <div class="alert alert-success">
                                            <i class="fas fa-check-circle me-2"></i> বিকাশ পেমেন্ট টেবিল সফলভাবে তৈরি করা হয়েছে!
                                        </div>
                                    <?php else: ?>
                                        <div class="alert alert-danger">
                                            <i class="fas fa-exclamation-triangle me-2"></i> বিকাশ পেমেন্ট টেবিল তৈরি করা হয়নি!
                                        </div>
                                    <?php endif; ?>

                                    <div class="d-grid gap-2 mt-3">
                                        <a href="bkash_init.php" class="btn btn-success">
                                            <i class="fas fa-database me-2"></i> ডাটাবেস সেটআপ করুন
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="card-title mb-0">বিকাশ পেমেন্ট গেটওয়ে কনফিগারেশন আপডেট</h6>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i> বিকাশ পেমেন্ট গেটওয়ে ব্যবহার করতে আপনাকে <code>includes/bkash_config.php</code> ফাইলে API ক্রেডেনশিয়ালস আপডেট করতে হবে।
                                    </div>

                                    <p>নিম্নলিখিত ফাইলটি সম্পাদনা করুন:</p>
                                    <pre class="bg-light p-3 rounded"><code>includes/bkash_config.php</code></pre>

                                    <p>নিম্নলিখিত লাইনগুলি আপডেট করুন:</p>
                                    <pre class="bg-light p-3 rounded"><code>define('BKASH_APP_KEY', 'your_app_key'); // Replace with your actual app key
define('BKASH_APP_SECRET', 'your_app_secret'); // Replace with your actual app secret
define('BKASH_USERNAME', 'your_username'); // Replace with your actual username
define('BKASH_PASSWORD', 'your_password'); // Replace with your actual password</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const testTokenBtn = document.getElementById('testTokenBtn');
        const tokenResult = document.getElementById('tokenResult');

        testTokenBtn.addEventListener('click', function() {
            // Show loading message
            tokenResult.classList.remove('d-none');
            tokenResult.innerHTML = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-spinner fa-spin me-2"></i> টোকেন টেস্ট চলছে...</h6>
                </div>
            `;

            // Make AJAX request to test token
            fetch('bkash_ajax_handler.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=testToken'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    tokenResult.innerHTML = `
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i> টোকেন সফলভাবে জেনারেট করা হয়েছে!</h6>
                            <p class="mb-0">টোকেন: ${data.token.substring(0, 20)}...</p>
                        </div>
                    `;
                } else {
                    tokenResult.innerHTML = `
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i> টোকেন জেনারেট করতে সমস্যা হয়েছে!</h6>
                            <p class="mb-0">ত্রুটি: ${data.message}</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                tokenResult.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i> টোকেন টেস্ট করতে সমস্যা হয়েছে!</h6>
                        <p class="mb-0">ত্রুটি: ${error.message}</p>
                    </div>
                `;
            });
        });
    });
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
