// Template Edit JavaScript

// Calculate and update marks summary
function updateMarksSummary() {
    const totalMarks = parseFloat(document.getElementById('default_total_marks').value) || 0;
    const cqMarks = parseFloat(document.getElementById('default_cq_marks').value) || 0;
    const mcqMarks = parseFloat(document.getElementById('default_mcq_marks').value) || 0;
    const practicalMarks = parseFloat(document.getElementById('default_practical_marks').value) || 0;
    
    const hasCq = document.getElementById('has_cq').checked;
    const hasMcq = document.getElementById('has_mcq').checked;
    const hasPractical = document.getElementById('has_practical').checked;
    
    // Calculate percentages
    const cqPercentage = totalMarks > 0 ? Math.round((cqMarks / totalMarks) * 100) : 0;
    const mcqPercentage = totalMarks > 0 ? Math.round((mcqMarks / totalMarks) * 100) : 0;
    const practicalPercentage = totalMarks > 0 ? Math.round((practicalMarks / totalMarks) * 100) : 0;
    
    // Calculate total sum
    const totalSum = cqMarks + mcqMarks + practicalMarks;
    const isValid = Math.abs(totalSum - totalMarks) < 0.01;
    
    // Update display
    document.getElementById('cqPercentage').textContent = hasCq ? `${cqPercentage}%` : 'N/A';
    document.getElementById('cqMarks').textContent = hasCq ? `${cqMarks} নম্বর` : 'নেই';
    
    document.getElementById('mcqPercentage').textContent = hasMcq ? `${mcqPercentage}%` : 'N/A';
    document.getElementById('mcqMarks').textContent = hasMcq ? `${mcqMarks} নম্বর` : 'নেই';
    
    document.getElementById('practicalPercentage').textContent = hasPractical ? `${practicalPercentage}%` : 'N/A';
    document.getElementById('practicalMarks').textContent = hasPractical ? `${practicalMarks} নম্বর` : 'নেই';
    
    document.getElementById('totalMarks').textContent = totalMarks;
    
    // Update status
    const statusElement = document.getElementById('totalStatus');
    const submitBtn = document.getElementById('submitBtn');
    
    if (isValid) {
        statusElement.textContent = 'সঠিক';
        statusElement.className = 'text-success';
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>আপডেট করুন';
    } else {
        statusElement.textContent = `ত্রুটি: যোগফল ${totalSum}`;
        statusElement.className = 'text-danger';
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>মার্কস ঠিক করুন';
    }
    
    // Update card visibility based on checkboxes
    const cards = document.querySelectorAll('.marks-card');
    cards[0].style.opacity = hasCq ? '1' : '0.5'; // CQ card
    cards[1].style.opacity = hasMcq ? '1' : '0.5'; // MCQ card
    cards[2].style.opacity = hasPractical ? '1' : '0.5'; // Practical card
}

// Auto-adjust marks when checkbox states change
function autoAdjustMarks() {
    const totalMarks = parseFloat(document.getElementById('default_total_marks').value) || 100;
    const hasCq = document.getElementById('has_cq').checked;
    const hasMcq = document.getElementById('has_mcq').checked;
    const hasPractical = document.getElementById('has_practical').checked;
    
    // Count enabled components
    let enabledCount = 0;
    if (hasCq) enabledCount++;
    if (hasMcq) enabledCount++;
    if (hasPractical) enabledCount++;
    
    if (enabledCount === 0) return;
    
    // Auto-distribute marks
    if (enabledCount === 1) {
        // Single component gets all marks
        if (hasCq) document.getElementById('default_cq_marks').value = totalMarks;
        if (hasMcq) document.getElementById('default_mcq_marks').value = totalMarks;
        if (hasPractical) document.getElementById('default_practical_marks').value = totalMarks;
    } else if (enabledCount === 2) {
        // Two components: 70-30 distribution
        if (hasCq && hasMcq) {
            document.getElementById('default_cq_marks').value = Math.round(totalMarks * 0.7);
            document.getElementById('default_mcq_marks').value = Math.round(totalMarks * 0.3);
        } else if (hasCq && hasPractical) {
            document.getElementById('default_cq_marks').value = Math.round(totalMarks * 0.7);
            document.getElementById('default_practical_marks').value = Math.round(totalMarks * 0.3);
        } else if (hasMcq && hasPractical) {
            document.getElementById('default_mcq_marks').value = Math.round(totalMarks * 0.7);
            document.getElementById('default_practical_marks').value = Math.round(totalMarks * 0.3);
        }
    } else if (enabledCount === 3) {
        // Three components: 60-30-10 distribution
        document.getElementById('default_cq_marks').value = Math.round(totalMarks * 0.6);
        document.getElementById('default_mcq_marks').value = Math.round(totalMarks * 0.3);
        document.getElementById('default_practical_marks').value = Math.round(totalMarks * 0.1);
    }
    
    // Set disabled components to 0
    if (!hasCq) document.getElementById('default_cq_marks').value = 0;
    if (!hasMcq) document.getElementById('default_mcq_marks').value = 0;
    if (!hasPractical) document.getElementById('default_practical_marks').value = 0;
    
    updateMarksSummary();
}

// Validate form before submission
function validateForm() {
    const totalMarks = parseFloat(document.getElementById('default_total_marks').value) || 0;
    const cqMarks = parseFloat(document.getElementById('default_cq_marks').value) || 0;
    const mcqMarks = parseFloat(document.getElementById('default_mcq_marks').value) || 0;
    const practicalMarks = parseFloat(document.getElementById('default_practical_marks').value) || 0;
    
    const totalSum = cqMarks + mcqMarks + practicalMarks;
    const isValid = Math.abs(totalSum - totalMarks) < 0.01;
    
    if (!isValid) {
        alert(`মার্কস বিতরণে সমস্যা আছে। মোট নম্বর: ${totalMarks}, যোগফল: ${totalSum}`);
        return false;
    }
    
    const classLevel = document.getElementById('class_level').value.trim();
    const className = document.getElementById('class_level_name').value.trim();
    
    if (!classLevel || !className) {
        alert('টেমপ্লেট আইডি এবং নাম অবশ্যই পূরণ করতে হবে।');
        return false;
    }
    
    return true;
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to all input fields
    const marksInputs = document.querySelectorAll('.marks-input, #default_total_marks');
    marksInputs.forEach(input => {
        input.addEventListener('input', updateMarksSummary);
    });
    
    // Add event listeners to checkboxes
    const checkboxes = document.querySelectorAll('#has_cq, #has_mcq, #has_practical');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            // Enable/disable corresponding input
            const inputId = this.id.replace('has_', 'default_') + '_marks';
            const input = document.getElementById(inputId);
            if (input) {
                input.disabled = !this.checked;
                if (!this.checked) {
                    input.value = 0;
                }
            }
            updateMarksSummary();
        });
    });
    
    // Add form validation
    document.getElementById('templateForm').addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
        }
    });
    
    // Add auto-adjust button
    const marksSummary = document.querySelector('.marks-summary');
    const autoAdjustBtn = document.createElement('button');
    autoAdjustBtn.type = 'button';
    autoAdjustBtn.className = 'btn btn-outline-primary btn-sm mt-2';
    autoAdjustBtn.innerHTML = '<i class="fas fa-magic me-1"></i>অটো-অ্যাডজাস্ট';
    autoAdjustBtn.onclick = autoAdjustMarks;
    marksSummary.appendChild(autoAdjustBtn);
    
    // Initial calculation
    updateMarksSummary();
    
    // Set initial disabled states
    checkboxes.forEach(checkbox => {
        const inputId = checkbox.id.replace('has_', 'default_') + '_marks';
        const input = document.getElementById(inputId);
        if (input) {
            input.disabled = !checkbox.checked;
        }
    });
});

// Add some visual enhancements
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects to form controls
    const formControls = document.querySelectorAll('.form-control');
    formControls.forEach(control => {
        control.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        control.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
    
    // Add loading state to submit button
    const form = document.getElementById('templateForm');
    const submitBtn = document.getElementById('submitBtn');
    
    form.addEventListener('submit', function() {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>আপডেট করা হচ্ছে...';
    });
});

// Add CSS for focused state
const style = document.createElement('style');
style.textContent = `
    .focused {
        transform: translateY(-2px);
        transition: transform 0.2s ease;
    }
    
    .marks-card {
        transition: all 0.3s ease;
    }
    
    .marks-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    }
    
    .form-control:disabled {
        background-color: #f8f9fa;
        opacity: 0.6;
    }
    
    .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
`;
document.head.appendChild(style);
