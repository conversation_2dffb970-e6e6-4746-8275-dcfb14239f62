<?php
require_once '../includes/dbh.inc.php';

echo "<h2>Fix Class 1-2 Column Issues</h2>";

// Check students table structure
echo "<h3>1. Checking Students Table Structure</h3>";
$studentsColumns = [];
try {
    $result = $conn->query("DESCRIBE students");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $studentsColumns[] = $row['Field'];
        }
        echo "<p style='color: green;'>✅ Students table columns: " . implode(', ', $studentsColumns) . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking students table: " . $e->getMessage() . "</p>";
}

// Determine correct column names
echo "<h3>2. Determining Correct Column Names</h3>";
$nameColumn = 'id'; // fallback
$rollColumn = 'id'; // fallback

// Check for name column variations
if (in_array('name', $studentsColumns)) {
    $nameColumn = 'name';
    echo "<p style='color: green;'>✅ Using 'name' column for student names</p>";
} elseif (in_array('student_name', $studentsColumns)) {
    $nameColumn = 'student_name';
    echo "<p style='color: green;'>✅ Using 'student_name' column for student names</p>";
} elseif (in_array('first_name', $studentsColumns)) {
    $nameColumn = 'first_name';
    echo "<p style='color: green;'>✅ Using 'first_name' column for student names</p>";
} else {
    echo "<p style='color: orange;'>⚠️ No standard name column found, using 'id' as fallback</p>";
}

// Check for roll number column variations
if (in_array('roll_number', $studentsColumns)) {
    $rollColumn = 'roll_number';
    echo "<p style='color: green;'>✅ Using 'roll_number' column for roll numbers</p>";
} elseif (in_array('roll', $studentsColumns)) {
    $rollColumn = 'roll';
    echo "<p style='color: green;'>✅ Using 'roll' column for roll numbers</p>";
} elseif (in_array('student_id', $studentsColumns)) {
    $rollColumn = 'student_id';
    echo "<p style='color: green;'>✅ Using 'student_id' column for roll numbers</p>";
} else {
    echo "<p style='color: orange;'>⚠️ No standard roll column found, using 'id' as fallback</p>";
}

// Test query with correct columns
echo "<h3>3. Testing Query with Correct Columns</h3>";
try {
    $testQuery = "SELECT s.id, s.$nameColumn as student_name, s.$rollColumn as roll_number, s.session, c.class_name
                  FROM students s
                  LEFT JOIN classes c ON s.class_id = c.id
                  WHERE c.class_name IN ('ONE', 'TWO', 'ক্লাস ১', 'ক্লাস ২', '১', '২', '1', '2')
                  ORDER BY c.class_name, CAST(COALESCE(s.$rollColumn, s.id) AS UNSIGNED)
                  LIMIT 5";
    
    $testResult = $conn->query($testQuery);
    
    if ($testResult && $testResult->num_rows > 0) {
        echo "<p style='color: green;'>✅ Query successful! Found " . $testResult->num_rows . " students</p>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Name</th><th>Roll</th><th>Session</th><th>Class</th></tr>";
        while ($row = $testResult->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars($row['student_name'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($row['roll_number'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($row['session'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($row['class_name'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ Query successful but no students found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Query failed: " . $e->getMessage() . "</p>";
}

// Generate corrected PHP code
echo "<h3>4. Generated Corrected PHP Code</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;'>";
echo "<strong>Corrected Student Query:</strong><br>";
echo htmlspecialchars('$studentQuery = "SELECT s.id, s.' . $nameColumn . ' as student_name, s.' . $rollColumn . ' as roll_number, s.session, c.class_name
                       FROM students s
                       LEFT JOIN classes c ON s.class_id = c.id
                       WHERE c.class_name IN (\'ONE\', \'TWO\', \'ক্লাস ১\', \'ক্লাস ২\', \'১\', \'২\', \'1\', \'2\')
                       ORDER BY c.class_name, CAST(COALESCE(s.' . $rollColumn . ', s.id) AS UNSIGNED)";');
echo "<br><br>";
echo "<strong>Corrected Data Access:</strong><br>";
echo htmlspecialchars('$name = $student[\'student_name\'] ?? \'নাম নেই\';
$roll = $student[\'roll_number\'] ?? \'N/A\';');
echo "</div>";

// Auto-fix option
echo "<h3>5. Auto-Fix Class 1-2 Page</h3>";
if (isset($_POST['auto_fix'])) {
    try {
        // Read the current file
        $filePath = 'class_exam_primary_lower_1_2.php';
        $content = file_get_contents($filePath);
        
        if ($content !== false) {
            // Create backup
            $backupPath = 'class_exam_primary_lower_1_2_backup_' . date('Y_m_d_H_i_s') . '.php';
            file_put_contents($backupPath, $content);
            echo "<p style='color: green;'>✅ Backup created: $backupPath</p>";
            
            // Apply fixes
            $correctedQuery = 'SELECT s.id, s.' . $nameColumn . ' as student_name, s.' . $rollColumn . ' as roll_number, s.session, c.class_name
                               FROM students s
                               LEFT JOIN classes c ON s.class_id = c.id
                               WHERE c.class_name IN (\'ONE\', \'TWO\', \'ক্লাস ১\', \'ক্লাস ২\', \'১\', \'২\', \'1\', \'2\')
                               ORDER BY c.class_name, CAST(COALESCE(s.' . $rollColumn . ', s.id) AS UNSIGNED)';
            
            // Replace the problematic query
            $pattern = '/\$studentQuery = "SELECT s\.\*, c\.class_name.*?ORDER BY.*?";/s';
            $replacement = '$studentQuery = "' . $correctedQuery . '";';
            $content = preg_replace($pattern, $replacement, $content);
            
            // Replace data access
            $content = str_replace(
                '$name = $student[\'name\'] ?? $student[\'first_name\'] ?? $student[\'student_name\'] ?? \'নাম নেই\';',
                '$name = $student[\'student_name\'] ?? \'নাম নেই\';',
                $content
            );
            $content = str_replace(
                '$roll = $student[\'roll_number\'] ?? $student[\'id\'] ?? \'N/A\';',
                '$roll = $student[\'roll_number\'] ?? \'N/A\';',
                $content
            );
            
            // Save the corrected file
            if (file_put_contents($filePath, $content)) {
                echo "<p style='color: green;'>✅ File successfully corrected!</p>";
                echo "<p><a href='class_exam_primary_lower_1_2.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Fixed Page</a></p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to save corrected file</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Could not read original file</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Auto-fix failed: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<form method='POST'>";
    echo "<button type='submit' name='auto_fix' value='1' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Auto-Fix Class 1-2 Page</button>";
    echo "</form>";
}

echo "<br><a href='class_exam_primary_lower_1_2.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Current Page</a>";
echo " <a href='dashboard.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>Dashboard</a>";

$conn->close();
?>
