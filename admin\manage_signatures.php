<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Include database connection
require_once '../includes/dbh.inc.php';

// Initialize variables
$success_message = '';
$error_message = '';

// Create signatures directory if it doesn't exist
$signatures_dir = '../uploads/signatures/';
if (!file_exists($signatures_dir)) {
    mkdir($signatures_dir, 0777, true);
}

// Handle institution logo upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['upload_logo'])) {
    // Check file type
    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
    $file_extension = strtolower(pathinfo($_FILES['institution_logo']['name'], PATHINFO_EXTENSION));

    if ($_FILES['institution_logo']['error'] !== 0) {
        $error_message = 'কোন ফাইল নির্বাচন করা হয়নি বা আপলোড করতে সমস্যা হয়েছে।';
    } else if (!in_array($file_extension, $allowed_extensions)) {
        $error_message = 'অবৈধ ফাইল টাইপ। শুধুমাত্র JPG, JPEG, PNG, এবং GIF ফাইল অনুমোদিত।';
    } else if ($_FILES['institution_logo']['size'] > 2000000) { // 2MB max
        $error_message = 'ফাইল সাইজ খুব বড়। সর্বাধিক ফাইল সাইজ 2MB।';
    } else {
        $file_name = 'institution_logo.png';
        $target_file = $signatures_dir . $file_name;

        // Delete existing file if it exists
        if (file_exists($target_file)) {
            unlink($target_file);
        }

        // Upload new file
        if (move_uploaded_file($_FILES['institution_logo']['tmp_name'], $target_file)) {
            $success_message = 'প্রতিষ্ঠানের লোগো সফলভাবে আপলোড করা হয়েছে।';
        } else {
            $error_message = 'ফাইল আপলোড করতে সমস্যা হয়েছে। দয়া করে আবার চেষ্টা করুন।';
        }
    }
}

// Handle logo deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_logo'])) {
    $file_name = 'institution_logo.png';
    $target_file = $signatures_dir . $file_name;

    if (file_exists($target_file)) {
        if (unlink($target_file)) {
            $success_message = 'প্রতিষ্ঠানের লোগো সফলভাবে মুছে ফেলা হয়েছে।';
        } else {
            $error_message = 'লোগো মুছতে সমস্যা হয়েছে। দয়া করে আবার চেষ্টা করুন।';
        }
    } else {
        $error_message = 'লোগো ফাইল পাওয়া যায়নি।';
    }
}

// Handle signature upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['upload_signature'])) {
    $signature_type = $_POST['signature_type'];
    $signature_name = isset($_POST['signature_name']) ? trim($_POST['signature_name']) : '';

    // Validate signature type
    $allowed_types = ['controller', 'principal', 'convener'];
    if (!in_array($signature_type, $allowed_types)) {
        $error_message = 'অবৈধ স্বাক্ষর প্রকার।';
    } else if (empty($signature_name)) {
        $error_message = 'স্বাক্ষরকারীর নাম প্রদান করুন।';
    } else if (isset($_FILES['signature_image']) && $_FILES['signature_image']['error'] === 0) {
        // Check file type
        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
        $file_extension = strtolower(pathinfo($_FILES['signature_image']['name'], PATHINFO_EXTENSION));

        if (!in_array($file_extension, $allowed_extensions)) {
            $error_message = 'অবৈধ ফাইল টাইপ। শুধুমাত্র JPG, JPEG, PNG, এবং GIF ফাইল অনুমোদিত।';
        } else if ($_FILES['signature_image']['size'] > 2000000) { // 2MB max
            $error_message = 'ফাইল সাইজ খুব বড়। সর্বাধিক ফাইল সাইজ 2MB।';
        } else {
            $file_name = $signature_type . '_signature.png';
            $target_file = $signatures_dir . $file_name;

            // Delete existing file if it exists
            if (file_exists($target_file)) {
                unlink($target_file);
            }

            // Save signature name to a text file
            $name_file = $signatures_dir . $signature_type . '_name.txt';
            file_put_contents($name_file, $signature_name);

            // Upload new file
            if (move_uploaded_file($_FILES['signature_image']['tmp_name'], $target_file)) {
                $success_message = 'স্বাক্ষর সফলভাবে আপলোড করা হয়েছে।';
            } else {
                $error_message = 'ফাইল আপলোড করতে সমস্যা হয়েছে। দয়া করে আবার চেষ্টা করুন।';
            }
        }
    } else {
        $error_message = 'কোন ফাইল নির্বাচন করা হয়নি বা আপলোড করতে সমস্যা হয়েছে।';
    }
}

// Handle signature deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_signature'])) {
    $signature_type = $_POST['signature_type'];

    // Validate signature type
    $allowed_types = ['controller', 'principal', 'convener'];
    if (!in_array($signature_type, $allowed_types)) {
        $error_message = 'অবৈধ স্বাক্ষর প্রকার।';
    } else {
        $file_name = $signature_type . '_signature.png';
        $target_file = $signatures_dir . $file_name;
        $name_file = $signatures_dir . $signature_type . '_name.txt';

        $success = true;

        // Delete signature image
        if (file_exists($target_file)) {
            if (!unlink($target_file)) {
                $success = false;
            }
        }

        // Delete name file
        if (file_exists($name_file)) {
            if (!unlink($name_file)) {
                $success = false;
            }
        }

        if ($success) {
            $success_message = 'স্বাক্ষর সফলভাবে মুছে ফেলা হয়েছে।';
        } else {
            $error_message = 'স্বাক্ষর মুছতে সমস্যা হয়েছে। দয়া করে আবার চেষ্টা করুন।';
        }
    }
}

// Check if signatures and logo exist
$controller_signature_exists = file_exists($signatures_dir . 'controller_signature.png');
$principal_signature_exists = file_exists($signatures_dir . 'principal_signature.png');
$convener_signature_exists = file_exists($signatures_dir . 'convener_signature.png');
$institution_logo_exists = file_exists($signatures_dir . 'institution_logo.png');

// Get signature names
$controller_name = '';
$principal_name = '';
$convener_name = '';

if (file_exists($signatures_dir . 'controller_name.txt')) {
    $controller_name = file_get_contents($signatures_dir . 'controller_name.txt');
}
if (file_exists($signatures_dir . 'principal_name.txt')) {
    $principal_name = file_get_contents($signatures_dir . 'principal_name.txt');
}
if (file_exists($signatures_dir . 'convener_name.txt')) {
    $convener_name = file_get_contents($signatures_dir . 'convener_name.txt');
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include('includes/global-head.php'); ?>
    <title>স্বাক্ষর ব্যবস্থাপনা</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .signature-preview {
            border: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 20px;
            text-align: center;
            border-radius: 5px;
            background-color: #f9f9f9;
            min-height: 100px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .signature-preview img {
            max-width: 200px;
            max-height: 80px;
            margin-bottom: 10px;
        }
        .signature-actions {
            margin-top: 10px;
        }
        .signature-card {
            page-break-inside: avoid;
        }
        .signature-display {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 20px 0;
        }
        .signature-display img {
            max-width: 150px;
            max-height: 60px;
            margin-bottom: 0;
            position: relative;
            top: 10px;
        }
        .signature-display .signature-name {
            border-top: 1px solid #3498db;
            padding-top: 5px;
            margin-top: 0;
            font-weight: 500;
            color: #2c3e50;
            font-size: 14px;
            width: 200px;
            text-align: center;
        }
        @media print {
            .no-print {
                display: none !important;
            }
            .container-fluid {
                width: 100%;
                padding: 0;
                margin: 0;
            }
            .signature-card {
                break-inside: avoid;
                page-break-inside: avoid;
            }
            body {
                background-color: white !important;
            }
            .card {
                border: none !important;
                box-shadow: none !important;
            }
            .card-header {
                background-color: white !important;
                color: black !important;
                border-bottom: 1px solid #ddd !important;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <?php include('includes/sidebar.php'); ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom no-print">
                    <h1 class="h2">স্বাক্ষর ব্যবস্থাপনা</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="student_exam_attendance.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-clipboard-check"></i> শিক্ষার্থী হাজিরা পত্র
                            </a>
                            <button type="button" class="btn btn-sm btn-success" onclick="window.print()">
                                <i class="fas fa-print"></i> প্রিন্ট করুন
                            </button>
                            <button type="button" class="btn btn-sm btn-info" onclick="exportToPDF()">
                                <i class="fas fa-file-pdf"></i> PDF হিসেবে সংরক্ষণ করুন
                            </button>
                        </div>
                    </div>
                </div>

                <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i> <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <!-- Signature Management Forms - No Print -->
                <div class="row no-print">
                    <!-- Institution Logo -->
                    <div class="col-md-4 mb-4">
                        <div class="card signature-card">
                            <div class="card-header">
                                <h5 class="mb-0">প্রতিষ্ঠানের লোগো</h5>
                            </div>
                            <div class="card-body">
                                <div class="signature-preview">
                                    <?php if ($institution_logo_exists): ?>
                                        <img src="<?php echo $signatures_dir; ?>institution_logo.png?v=<?php echo time(); ?>" alt="প্রতিষ্ঠানের লোগো" style="max-width: 100px; max-height: 100px;">
                                        <div class="text-success">লোগো আপলোড করা আছে</div>
                                    <?php else: ?>
                                        <i class="fas fa-building fa-3x text-muted mb-2"></i>
                                        <div class="text-muted">কোন লোগো আপলোড করা হয়নি</div>
                                    <?php endif; ?>
                                </div>

                                <form method="POST" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="institution_logo" class="form-label">লোগো আপলোড করুন</label>
                                        <input type="file" class="form-control" id="institution_logo" name="institution_logo" accept="image/*" required>
                                        <div class="form-text">সর্বাধিক ফাইল সাইজ: 2MB</div>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <button type="submit" name="upload_logo" class="btn btn-primary">
                                            <i class="fas fa-upload me-1"></i> আপলোড করুন
                                        </button>
                                        <?php if ($institution_logo_exists): ?>
                                        <button type="submit" name="delete_logo" class="btn btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই লোগো মুছে ফেলতে চান?');">
                                            <i class="fas fa-trash-alt me-1"></i> মুছুন
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Controller Signature -->
                    <div class="col-md-4 mb-4">
                        <div class="card signature-card">
                            <div class="card-header">
                                <h5 class="mb-0">পরীক্ষা নিয়ন্ত্রকের স্বাক্ষর</h5>
                            </div>
                            <div class="card-body">
                                <div class="signature-preview">
                                    <?php if ($controller_signature_exists): ?>
                                        <img src="<?php echo $signatures_dir; ?>controller_signature.png?v=<?php echo time(); ?>" alt="পরীক্ষা নিয়ন্ত্রকের স্বাক্ষর">
                                        <div class="text-success">স্বাক্ষর আপলোড করা আছে</div>
                                    <?php else: ?>
                                        <i class="fas fa-signature fa-3x text-muted mb-2"></i>
                                        <div class="text-muted">কোন স্বাক্ষর আপলোড করা হয়নি</div>
                                    <?php endif; ?>
                                </div>

                                <form method="POST" enctype="multipart/form-data">
                                    <input type="hidden" name="signature_type" value="controller">
                                    <div class="mb-3">
                                        <label for="controller_signature" class="form-label">স্বাক্ষর আপলোড করুন</label>
                                        <input type="file" class="form-control" id="controller_signature" name="signature_image" accept="image/*" required>
                                        <div class="form-text">সর্বাধিক ফাইল সাইজ: 2MB</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="controller_name" class="form-label">স্বাক্ষরকারীর নাম ও পদবী</label>
                                        <input type="text" class="form-control" id="controller_name" name="signature_name" value="<?php echo htmlspecialchars($controller_name); ?>" required>
                                        <div class="form-text">উদাহরণ: মোঃ আবদুল করিম, পরীক্ষা নিয়ন্ত্রক</div>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <button type="submit" name="upload_signature" class="btn btn-primary">
                                            <i class="fas fa-upload me-1"></i> আপলোড করুন
                                        </button>
                                        <?php if ($controller_signature_exists): ?>
                                        <button type="submit" name="delete_signature" class="btn btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই স্বাক্ষর মুছে ফেলতে চান?');">
                                            <i class="fas fa-trash-alt me-1"></i> মুছুন
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Principal Signature -->
                    <div class="col-md-4 mb-4">
                        <div class="card signature-card">
                            <div class="card-header">
                                <h5 class="mb-0">প্রতিষ্ঠান প্রধানের স্বাক্ষর</h5>
                            </div>
                            <div class="card-body">
                                <div class="signature-preview">
                                    <?php if ($principal_signature_exists): ?>
                                        <img src="<?php echo $signatures_dir; ?>principal_signature.png?v=<?php echo time(); ?>" alt="প্রতিষ্ঠান প্রধানের স্বাক্ষর">
                                        <div class="text-success">স্বাক্ষর আপলোড করা আছে</div>
                                    <?php else: ?>
                                        <i class="fas fa-signature fa-3x text-muted mb-2"></i>
                                        <div class="text-muted">কোন স্বাক্ষর আপলোড করা হয়নি</div>
                                    <?php endif; ?>
                                </div>

                                <form method="POST" enctype="multipart/form-data">
                                    <input type="hidden" name="signature_type" value="principal">
                                    <div class="mb-3">
                                        <label for="principal_signature" class="form-label">স্বাক্ষর আপলোড করুন</label>
                                        <input type="file" class="form-control" id="principal_signature" name="signature_image" accept="image/*" required>
                                        <div class="form-text">সর্বাধিক ফাইল সাইজ: 2MB</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="principal_name" class="form-label">স্বাক্ষরকারীর নাম ও পদবী</label>
                                        <input type="text" class="form-control" id="principal_name" name="signature_name" value="<?php echo htmlspecialchars($principal_name); ?>" required>
                                        <div class="form-text">উদাহরণ: মোঃ আবদুল করিম, প্রতিষ্ঠান প্রধান</div>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <button type="submit" name="upload_signature" class="btn btn-primary">
                                            <i class="fas fa-upload me-1"></i> আপলোড করুন
                                        </button>
                                        <?php if ($principal_signature_exists): ?>
                                        <button type="submit" name="delete_signature" class="btn btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই স্বাক্ষর মুছে ফেলতে চান?');">
                                            <i class="fas fa-trash-alt me-1"></i> মুছুন
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Convener Signature -->
                    <div class="col-md-4 mb-4">
                        <div class="card signature-card">
                            <div class="card-header">
                                <h5 class="mb-0">আহবায়কের স্বাক্ষর</h5>
                            </div>
                            <div class="card-body">
                                <div class="signature-preview">
                                    <?php if ($convener_signature_exists): ?>
                                        <img src="<?php echo $signatures_dir; ?>convener_signature.png?v=<?php echo time(); ?>" alt="আহবায়কের স্বাক্ষর">
                                        <div class="text-success">স্বাক্ষর আপলোড করা আছে</div>
                                    <?php else: ?>
                                        <i class="fas fa-signature fa-3x text-muted mb-2"></i>
                                        <div class="text-muted">কোন স্বাক্ষর আপলোড করা হয়নি</div>
                                    <?php endif; ?>
                                </div>

                                <form method="POST" enctype="multipart/form-data">
                                    <input type="hidden" name="signature_type" value="convener">
                                    <div class="mb-3">
                                        <label for="convener_signature" class="form-label">স্বাক্ষর আপলোড করুন</label>
                                        <input type="file" class="form-control" id="convener_signature" name="signature_image" accept="image/*" required>
                                        <div class="form-text">সর্বাধিক ফাইল সাইজ: 2MB</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="convener_name" class="form-label">স্বাক্ষরকারীর নাম ও পদবী</label>
                                        <input type="text" class="form-control" id="convener_name" name="signature_name" value="<?php echo htmlspecialchars($convener_name); ?>" required>
                                        <div class="form-text">উদাহরণ: মোঃ আবদুল করিম, আহবায়ক</div>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <button type="submit" name="upload_signature" class="btn btn-primary">
                                            <i class="fas fa-upload me-1"></i> আপলোড করুন
                                        </button>
                                        <?php if ($convener_signature_exists): ?>
                                        <button type="submit" name="delete_signature" class="btn btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই স্বাক্ষর মুছে ফেলতে চান?');">
                                            <i class="fas fa-trash-alt me-1"></i> মুছুন
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Printable Signature Display -->
                <div id="printable-content">
                    <div class="row">
                        <div class="col-12 text-center mb-4">
                            <h3>স্বাক্ষর সমূহ</h3>
                            <p>স্কুল ম্যানেজমেন্ট সিস্টেম</p>
                        </div>
                    </div>

                    <div class="row">
                        <?php if ($principal_signature_exists): ?>
                        <div class="col-md-6 mb-5">
                            <div class="card signature-card">
                                <div class="card-header">
                                    <h5 class="mb-0 text-center">প্রতিষ্ঠান প্রধানের স্বাক্ষর</h5>
                                </div>
                                <div class="card-body">
                                    <div class="signature-display">
                                        <img src="<?php echo $signatures_dir; ?>principal_signature.png?v=<?php echo time(); ?>" alt="প্রতিষ্ঠান প্রধানের স্বাক্ষর">
                                        <div class="signature-name"><?php echo !empty($principal_name) ? $principal_name : 'প্রতিষ্ঠান প্রধানের স্বাক্ষর'; ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if ($convener_signature_exists): ?>
                        <div class="col-md-6 mb-5">
                            <div class="card signature-card">
                                <div class="card-header">
                                    <h5 class="mb-0 text-center">আহবায়কের স্বাক্ষর</h5>
                                </div>
                                <div class="card-body">
                                    <div class="signature-display">
                                        <img src="<?php echo $signatures_dir; ?>convener_signature.png?v=<?php echo time(); ?>" alt="আহবায়কের স্বাক্ষর">
                                        <div class="signature-name"><?php echo !empty($convener_name) ? $convener_name : 'আহবায়কের স্বাক্ষর'; ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if ($controller_signature_exists): ?>
                        <div class="col-md-6 mb-5">
                            <div class="card signature-card">
                                <div class="card-header">
                                    <h5 class="mb-0 text-center">পরীক্ষা নিয়ন্ত্রকের স্বাক্ষর</h5>
                                </div>
                                <div class="card-body">
                                    <div class="signature-display">
                                        <img src="<?php echo $signatures_dir; ?>controller_signature.png?v=<?php echo time(); ?>" alt="পরীক্ষা নিয়ন্ত্রকের স্বাক্ষর">
                                        <div class="signature-name"><?php echo !empty($controller_name) ? $controller_name : 'পরীক্ষা নিয়ন্ত্রকের স্বাক্ষর'; ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <script>
        function exportToPDF() {
            const element = document.getElementById('printable-content');

            const opt = {
                margin: [10, 10, 10, 10],
                filename: 'স্বাক্ষর_সমূহ.pdf',
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: { scale: 2 },
                jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
            };

            // Generate PDF
            html2pdf().set(opt).from(element).save();
        }
    </script>
</body>
</html>
