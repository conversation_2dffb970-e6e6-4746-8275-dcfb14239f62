<?php
// Initialize variables
$message = '';
$error = '';

// Define variables for features and about section
$features_title = "আমাদের বৈশিষ্ট্যসমূহ";
$features_subtitle = "ZFAW স্কুল ম্যানেজমেন্ট সিস্টেমের প্রধান বৈশিষ্ট্যসমূহ";
$about_title = "আমাদের সম্পর্কে";
$about_text1 = "ZFAW স্কুল ম্যানেজমেন্ট সিস্টেম হল একটি সম্পূর্ণ শিক্ষা প্রতিষ্ঠান ব্যবস্থাপনা সমাধান। এটি শিক্ষার্থী, শিক্ষক, কোর্স, ফলাফল, ফি এবং অন্যান্য সকল কার্যক্রম সহজে পরিচালনা করতে সাহায্য করে।";
$about_text2 = "আমাদের সিস্টেম ব্যবহার করে আপনি আপনার শিক্ষা প্রতিষ্ঠানের সকল কার্যক্রম সহজে এবং দক্ষতার সাথে পরিচালনা করতে পারবেন।";
$about_button_text = "আরও জানুন";
$about_button_link = "about.php";

// Default number of features
$feature_count = 6;

// Define default feature items
$features = [
    [
        'icon' => 'fas fa-user-graduate',
        'title' => 'শিক্ষার্থী ব্যবস্থাপনা',
        'description' => 'শিক্ষার্থীদের তথ্য সংরক্ষণ, হাজিরা, ফলাফল এবং অন্যান্য কার্যক্রম সহজেই পরিচালনা করুন।'
    ],
    [
        'icon' => 'fas fa-chalkboard-teacher',
        'title' => 'শিক্ষক ব্যবস্থাপনা',
        'description' => 'শিক্ষকদের তথ্য, ক্লাস রুটিন, বেতন এবং অন্যান্য কার্যক্রম সহজেই পরিচালনা করুন।'
    ],
    [
        'icon' => 'fas fa-book',
        'title' => 'বিষয় ব্যবস্থাপনা',
        'description' => 'বিভিন্ন শ্রেণির বিষয়সমূহ, সিলেবাস এবং পাঠ্যক্রম সহজেই পরিচালনা করুন।'
    ],
    [
        'icon' => 'fas fa-chart-line',
        'title' => 'ফলাফল ব্যবস্থাপনা',
        'description' => 'পরীক্ষার ফলাফল প্রস্তুত, প্রকাশ এবং বিশ্লেষণ সহজেই করুন।'
    ],
    [
        'icon' => 'fas fa-money-bill-wave',
        'title' => 'ফি ব্যবস্থাপনা',
        'description' => 'শিক্ষার্থীদের বেতন, ফি সংগ্রহ এবং হিসাব সহজেই পরিচালনা করুন।'
    ],
    [
        'icon' => 'fas fa-id-card',
        'title' => 'আইডি কার্ড ও সার্টিফিকেট',
        'description' => 'শিক্ষার্থীদের আইডি কার্ড এবং সার্টিফিকেট সহজেই তৈরি করুন।'
    ],
    [
        'icon' => 'fas fa-bell',
        'title' => 'নোটিফিকেশন সিস্টেম',
        'description' => 'শিক্ষার্থী, অভিভাবক এবং শিক্ষকদের জন্য অটোমেটিক নোটিফিকেশন সিস্টেম।'
    ],
    [
        'icon' => 'fas fa-calendar-alt',
        'title' => 'ইভেন্ট ক্যালেন্ডার',
        'description' => 'স্কুলের সকল ইভেন্ট, ছুটি এবং পরীক্ষার সময়সূচী সহজেই পরিচালনা করুন।'
    ],
    [
        'icon' => 'fas fa-bus',
        'title' => 'পরিবহন ব্যবস্থাপনা',
        'description' => 'স্কুল বাস এবং পরিবহন ব্যবস্থা সহজেই পরিচালনা করুন।'
    ],
    [
        'icon' => 'fas fa-book-reader',
        'title' => 'লাইব্রেরি ব্যবস্থাপনা',
        'description' => 'লাইব্রেরির বই, জার্নাল এবং অন্যান্য রিসোর্স সহজেই পরিচালনা করুন।'
    ]
];

// Load current values from index.php
if (file_exists('index.php')) {
    $index_content = file_get_contents('index.php');

    // Extract features section title and subtitle
    if (preg_match('/<h2 class="fw-bold">([^<]*)<\/h2>\s*<p class="text-muted">([^<]*)<\/p>/s', $index_content, $matches)) {
        $features_title = $matches[1];
        $features_subtitle = $matches[2];
    }

    // Extract about section title
    if (preg_match('/<h2 class="fw-bold mb-4">([^<]*)<\/h2>/s', $index_content, $matches)) {
        $about_title = $matches[1];
    }

    // Extract about section text
    if (preg_match('/<h2 class="fw-bold mb-4">[^<]*<\/h2>\s*<p>([^<]*)<\/p>\s*<p>([^<]*)<\/p>/s', $index_content, $matches)) {
        $about_text1 = $matches[1];
        $about_text2 = $matches[2];
    }

    // Extract about button text and link
    if (preg_match('/<a href="([^"]*)" class="btn btn-primary mt-3">([^<]*)<i class="fas fa-arrow-right ms-2"><\/i><\/a>/s', $index_content, $matches)) {
        $about_button_link = $matches[1];
        $about_button_text = trim($matches[2]);
    }

    // Extract feature items
    $feature_pattern = '/<div class="feature-box">\s*<div class="feature-icon">\s*<i class="([^"]*)">\s*<\/i>\s*<\/div>\s*<h4>([^<]*)<\/h4>\s*<p>([^<]*)<\/p>\s*<\/div>/s';
    if (preg_match_all($feature_pattern, $index_content, $matches, PREG_SET_ORDER)) {
        $features_from_index = [];
        foreach ($matches as $match) {
            $features_from_index[] = [
                'icon' => $match[1],
                'title' => $match[2],
                'description' => $match[3]
            ];
        }

        // Update feature count and features array
        $feature_count = count($features_from_index);

        // Only replace the features array if we found items
        if ($feature_count > 0) {
            $features = $features_from_index;
        }
    }
}

// Check if form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Process features section
    $features_title = $_POST['features_title'] ?? $features_title;
    $features_subtitle = $_POST['features_subtitle'] ?? $features_subtitle;

    // Process about section
    $about_title = $_POST['about_title'] ?? $about_title;
    $about_text1 = $_POST['about_text1'] ?? $about_text1;
    $about_text2 = $_POST['about_text2'] ?? $about_text2;
    $about_button_text = $_POST['about_button_text'] ?? $about_button_text;
    $about_button_link = $_POST['about_button_link'] ?? $about_button_link;

    // Process feature count
    if (isset($_POST['feature_count']) && is_numeric($_POST['feature_count'])) {
        $feature_count = intval($_POST['feature_count']);
        // Limit to a reasonable range (1-10)
        $feature_count = max(1, min(10, $feature_count));
    }

    // Process feature items
    $features = [];
    for ($i = 0; $i < $feature_count; $i++) {
        if (isset($_POST["feature_icon_$i"]) && isset($_POST["feature_title_$i"]) && isset($_POST["feature_description_$i"])) {
            $features[] = [
                'icon' => $_POST["feature_icon_$i"],
                'title' => $_POST["feature_title_$i"],
                'description' => $_POST["feature_description_$i"]
            ];
        }
    }

    // Process about image upload
    if (isset($_FILES['about_image']) && $_FILES['about_image']['error'] == 0) {
        $allowed = ['jpg', 'jpeg', 'png', 'gif'];
        $filename = $_FILES['about_image']['name'];
        $filetype = pathinfo($filename, PATHINFO_EXTENSION);

        if (in_array(strtolower($filetype), $allowed)) {
            $new_filename = 'about-image.' . $filetype;
            if (!file_exists('img')) {
                mkdir('img', 0777, true);
            }
            if (move_uploaded_file($_FILES['about_image']['tmp_name'], 'img/' . $new_filename)) {
                $message .= "আমাদের সম্পর্কে সেকশনের ছবি সফলভাবে আপলোড হয়েছে।<br>";
            } else {
                $error .= "আমাদের সম্পর্কে সেকশনের ছবি আপলোড করতে সমস্যা হয়েছে।<br>";
            }
        } else {
            $error .= "আমাদের সম্পর্কে সেকশনের ছবির জন্য শুধুমাত্র JPG, JPEG, PNG এবং GIF ফাইল গ্রহণযোগ্য।<br>";
        }
    }

    // Update index.php with new content
    if (file_exists('index.php')) {
        $index_content = file_get_contents('index.php');

        // Update features section title and subtitle
        $index_content = preg_replace('/<h2 class="fw-bold">([^<]*)<\/h2>\s*<p class="text-muted">([^<]*)<\/p>/s',
            '<h2 class="fw-bold">' . $features_title . '</h2>' . "\n" .
            '            <p class="text-muted">' . $features_subtitle . '</p>',
            $index_content);

        // Update about section title
        $index_content = preg_replace('/<h2 class="fw-bold mb-4">([^<]*)<\/h2>/s',
            '<h2 class="fw-bold mb-4">' . $about_title . '</h2>',
            $index_content);

        // Update about section text
        $index_content = preg_replace('/<h2 class="fw-bold mb-4">[^<]*<\/h2>\s*<p>([^<]*)<\/p>\s*<p>([^<]*)<\/p>/s',
            '<h2 class="fw-bold mb-4">' . $about_title . '</h2>' . "\n" .
            '                <p>' . $about_text1 . '</p>' . "\n" .
            '                <p>' . $about_text2 . '</p>',
            $index_content);

        // Update about button text and link
        $index_content = preg_replace('/<a href="([^"]*)" class="btn btn-primary mt-3">([^<]*)<i class="fas fa-arrow-right ms-2"><\/i><\/a>/s',
            '<a href="' . $about_button_link . '" class="btn btn-primary mt-3">' . $about_button_text . ' <i class="fas fa-arrow-right ms-2"></i></a>',
            $index_content);

        // Update feature items
        $feature_boxes_html = '';

        // Calculate column width based on number of features
        $column_width = 4; // Default is 4 (3 items per row)
        if ($feature_count == 1) {
            $column_width = 12; // 1 item per row
        } else if ($feature_count == 2) {
            $column_width = 6; // 2 items per row
        } else if ($feature_count > 6) {
            $column_width = 3; // 4 items per row for more than 6 features
        }

        foreach ($features as $feature) {
            $feature_boxes_html .= '            <div class="col-md-' . $column_width . '">
                <div class="feature-box">
                    <div class="feature-icon">
                        <i class="' . $feature['icon'] . '"></i>
                    </div>
                    <h4>' . $feature['title'] . '</h4>
                    <p>' . $feature['description'] . '</p>
                </div>
            </div>' . "\n";
        }

        // Replace all feature boxes
        $index_content = preg_replace('/<div class="row g-4">(.*?)<\/div>\s*<\/section>/s',
            '<div class="row g-4">' . "\n" . $feature_boxes_html . '        </div>
    </section>',
            $index_content);

        // Save the updated index.php file
        if (file_put_contents('index.php', $index_content)) {
            $message .= "সকল তথ্য সফলভাবে আপডেট করা হয়েছে।<br>";
        } else {
            $error .= "ফাইল আপডেট করতে সমস্যা হয়েছে।<br>";
        }
    } else {
        $error .= "index.php ফাইল পাওয়া যায়নি।<br>";
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>বৈশিষ্ট্য ও সম্পর্কে আপডেট</title>

    <!-- Bootstrap CSS -->
    

    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: #f8f9fa;
        }

        .form-container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-top: 30px;
            margin-bottom: 30px;
        }

        .section-title {
            border-left: 4px solid #006A4E;
            padding-left: 10px;
            margin-bottom: 20px;
            color: #006A4E;
        }

        .form-label {
            font-weight: 500;
        }

        .preview-image {
            max-width: 150px;
            max-height: 150px;
            border-radius: 5px;
            margin-top: 10px;
            border: 1px solid #ddd;
        }

        .btn-primary {
            background-color: #006A4E;
            border-color: #006A4E;
        }

        .btn-primary:hover {
            background-color: #00563B;
            border-color: #00563B;
        }

        .alert {
            border-radius: 5px;
        }

        .feature-item {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border: 1px solid #e9ecef;
        }

        .feature-item:hover {
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .icon-preview {
            font-size: 2rem;
            color: #006A4E;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="form-container">
                    <h2 class="text-center mb-4">বৈশিষ্ট্য ও সম্পর্কে আপডেট করুন</h2>

                    <?php if (!empty($message)): ?>
                        <div class="alert alert-success">
                            <?php echo $message; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger">
                            <?php echo $error; ?>
                        </div>
                    <?php endif; ?>

                    <form action="" method="POST" enctype="multipart/form-data">
                        <!-- Features Section -->
                        <div class="mb-4">
                            <h4 class="section-title">বৈশিষ্ট্যসমূহ সেকশন</h4>

                            <div class="mb-3">
                                <label for="features_title" class="form-label">শিরোনাম</label>
                                <input type="text" class="form-control" id="features_title" name="features_title" value="<?php echo htmlspecialchars($features_title); ?>">
                            </div>

                            <div class="mb-3">
                                <label for="features_subtitle" class="form-label">উপশিরোনাম</label>
                                <input type="text" class="form-control" id="features_subtitle" name="features_subtitle" value="<?php echo htmlspecialchars($features_subtitle); ?>">
                            </div>

                            <div class="mb-3">
                                <label for="feature_count" class="form-label">বৈশিষ্ট্যসমূহের সংখ্যা</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="feature_count" name="feature_count" min="1" max="10" value="<?php echo $feature_count; ?>">
                                    <button type="button" class="btn btn-outline-primary" id="update_count_btn">আপডেট</button>
                                </div>
                                <small class="text-muted">১ থেকে ১০ এর মধ্যে একটি সংখ্যা নির্বাচন করুন</small>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">বৈশিষ্ট্যসমূহ</label>

                                <div id="features-container">
                                    <?php for ($i = 0; $i < $feature_count; $i++): ?>
                                        <div class="feature-item" id="feature-item-<?php echo $i; ?>">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <h5 class="mb-0">বৈশিষ্ট্য #<?php echo $i + 1; ?></h5>
                                                <?php if ($feature_count > 1): ?>
                                                <button type="button" class="btn btn-sm btn-outline-danger remove-feature" data-index="<?php echo $i; ?>">
                                                    <i class="fas fa-times"></i> মুছুন
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-2">
                                                    <label for="feature_icon_<?php echo $i; ?>" class="form-label">আইকন</label>
                                                    <input type="text" class="form-control" id="feature_icon_<?php echo $i; ?>" name="feature_icon_<?php echo $i; ?>" value="<?php echo htmlspecialchars($features[$i]['icon'] ?? ''); ?>">
                                                    <div class="icon-preview mt-2">
                                                        <i class="<?php echo htmlspecialchars($features[$i]['icon'] ?? ''); ?>"></i>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <label for="feature_title_<?php echo $i; ?>" class="form-label">শিরোনাম</label>
                                                    <input type="text" class="form-control" id="feature_title_<?php echo $i; ?>" name="feature_title_<?php echo $i; ?>" value="<?php echo htmlspecialchars($features[$i]['title'] ?? ''); ?>">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="feature_description_<?php echo $i; ?>" class="form-label">বিবরণ</label>
                                                    <textarea class="form-control" id="feature_description_<?php echo $i; ?>" name="feature_description_<?php echo $i; ?>" rows="2"><?php echo htmlspecialchars($features[$i]['description'] ?? ''); ?></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endfor; ?>
                                </div>

                                <div class="text-center mt-3">
                                    <button type="button" class="btn btn-success" id="add_feature_btn">
                                        <i class="fas fa-plus"></i> নতুন বৈশিষ্ট্য যোগ করুন
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- About Section -->
                        <div class="mb-4">
                            <h4 class="section-title">আমাদের সম্পর্কে সেকশন</h4>

                            <div class="mb-3">
                                <label for="about_title" class="form-label">শিরোনাম</label>
                                <input type="text" class="form-control" id="about_title" name="about_title" value="<?php echo htmlspecialchars($about_title); ?>">
                            </div>

                            <div class="mb-3">
                                <label for="about_text1" class="form-label">প্রথম অনুচ্ছেদ</label>
                                <textarea class="form-control" id="about_text1" name="about_text1" rows="3"><?php echo htmlspecialchars($about_text1); ?></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="about_text2" class="form-label">দ্বিতীয় অনুচ্ছেদ</label>
                                <textarea class="form-control" id="about_text2" name="about_text2" rows="3"><?php echo htmlspecialchars($about_text2); ?></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="about_button_text" class="form-label">বাটন টেক্সট</label>
                                <input type="text" class="form-control" id="about_button_text" name="about_button_text" value="<?php echo htmlspecialchars($about_button_text); ?>">
                            </div>

                            <div class="mb-3">
                                <label for="about_button_link" class="form-label">বাটন লিংক</label>
                                <input type="text" class="form-control" id="about_button_link" name="about_button_link" value="<?php echo htmlspecialchars($about_button_link); ?>">
                            </div>

                            <div class="mb-3">
                                <label for="about_image" class="form-label">আমাদের সম্পর্কে সেকশনের ছবি (600x400 পিক্সেল সাইজ সুপারিশ করা হয়)</label>
                                <input type="file" class="form-control" id="about_image" name="about_image">
                                <div class="mt-2">
                                    <img id="about_preview" class="preview-image" src="#" alt="আমাদের সম্পর্কে ছবি প্রিভিউ" style="display: none;">
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">বর্তমান ছবি: <a href="img/about-image.jpg" target="_blank">দেখুন</a></small>
                                </div>
                            </div>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg px-5">সংরক্ষণ করুন</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Function to show image preview
        function showImagePreview(input, previewId) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();

                reader.onload = function(e) {
                    document.getElementById(previewId).src = e.target.result;
                    document.getElementById(previewId).style.display = 'block';
                }

                reader.readAsDataURL(input.files[0]);
            }
        }

        // Set up event listeners for file inputs
        document.getElementById('about_image').addEventListener('change', function() {
            showImagePreview(this, 'about_preview');
        });

        // Update icon preview when icon input changes
        function setupIconPreviewListeners() {
            document.querySelectorAll('[id^="feature_icon_"]').forEach(function(input) {
                input.addEventListener('input', function() {
                    const iconPreview = this.parentElement.querySelector('.icon-preview i');
                    iconPreview.className = this.value;
                });
            });
        }

        // Setup initial icon preview listeners
        setupIconPreviewListeners();

        // Function to create a new feature item
        function createFeatureItem(index) {
            // Get the default values from the first feature in our predefined list
            const defaultFeatures = <?php echo json_encode($features); ?>;
            const defaultFeature = defaultFeatures[index % defaultFeatures.length] || {
                icon: 'fas fa-star',
                title: 'নতুন বৈশিষ্ট্য',
                description: 'এখানে বৈশিষ্ট্যের বিবরণ লিখুন।'
            };

            const featureItem = document.createElement('div');
            featureItem.className = 'feature-item';
            featureItem.id = `feature-item-${index}`;

            featureItem.innerHTML = `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h5 class="mb-0">বৈশিষ্ট্য #${index + 1}</h5>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-feature" data-index="${index}">
                        <i class="fas fa-times"></i> মুছুন
                    </button>
                </div>
                <div class="row">
                    <div class="col-md-2">
                        <label for="feature_icon_${index}" class="form-label">আইকন</label>
                        <input type="text" class="form-control" id="feature_icon_${index}" name="feature_icon_${index}" value="${defaultFeature.icon}">
                        <div class="icon-preview mt-2">
                            <i class="${defaultFeature.icon}"></i>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label for="feature_title_${index}" class="form-label">শিরোনাম</label>
                        <input type="text" class="form-control" id="feature_title_${index}" name="feature_title_${index}" value="${defaultFeature.title}">
                    </div>
                    <div class="col-md-6">
                        <label for="feature_description_${index}" class="form-label">বিবরণ</label>
                        <textarea class="form-control" id="feature_description_${index}" name="feature_description_${index}" rows="2">${defaultFeature.description}</textarea>
                    </div>
                </div>
            `;

            return featureItem;
        }

        // Function to update feature count
        function updateFeatureCount() {
            const countInput = document.getElementById('feature_count');
            let newCount = parseInt(countInput.value);

            // Validate count (1-10)
            newCount = Math.max(1, Math.min(10, newCount));
            countInput.value = newCount;

            const container = document.getElementById('features-container');
            const currentItems = container.querySelectorAll('.feature-item');
            const currentCount = currentItems.length;

            if (newCount > currentCount) {
                // Add new items
                for (let i = currentCount; i < newCount; i++) {
                    container.appendChild(createFeatureItem(i));
                }
            } else if (newCount < currentCount) {
                // Remove excess items
                for (let i = currentCount - 1; i >= newCount; i--) {
                    const item = document.getElementById(`feature-item-${i}`);
                    if (item) {
                        container.removeChild(item);
                    }
                }
            }

            // Update remove buttons (hide if only one item)
            updateRemoveButtons();

            // Setup icon preview listeners for new items
            setupIconPreviewListeners();
        }

        // Function to update remove buttons visibility
        function updateRemoveButtons() {
            const container = document.getElementById('features-container');
            const items = container.querySelectorAll('.feature-item');

            items.forEach(item => {
                const removeBtn = item.querySelector('.remove-feature');
                if (removeBtn) {
                    removeBtn.style.display = items.length > 1 ? 'block' : 'none';
                }
            });
        }

        // Function to add a new feature
        function addFeature() {
            const container = document.getElementById('features-container');
            const currentItems = container.querySelectorAll('.feature-item');
            const currentCount = currentItems.length;

            // Check if we've reached the maximum
            if (currentCount >= 10) {
                alert('সর্বাধিক ১০টি বৈশিষ্ট্য যোগ করা যাবে।');
                return;
            }

            // Add new item
            container.appendChild(createFeatureItem(currentCount));

            // Update feature count input
            document.getElementById('feature_count').value = currentCount + 1;

            // Update remove buttons
            updateRemoveButtons();

            // Setup icon preview listeners for new item
            setupIconPreviewListeners();
        }

        // Function to remove a feature
        function removeFeature(index) {
            const container = document.getElementById('features-container');
            const items = container.querySelectorAll('.feature-item');

            // Check if we have more than one item
            if (items.length <= 1) {
                alert('কমপক্ষে একটি বৈশিষ্ট্য থাকতে হবে।');
                return;
            }

            // Remove the item
            const itemToRemove = document.getElementById(`feature-item-${index}`);
            if (itemToRemove) {
                container.removeChild(itemToRemove);
            }

            // Renumber remaining items
            const remainingItems = container.querySelectorAll('.feature-item');
            remainingItems.forEach((item, i) => {
                item.id = `feature-item-${i}`;
                item.querySelector('h5').textContent = `বৈশিষ্ট্য #${i + 1}`;

                // Update input names and IDs
                const inputs = item.querySelectorAll('input, textarea');
                inputs.forEach(input => {
                    const name = input.name.replace(/\d+/, i);
                    const id = input.id.replace(/\d+/, i);
                    input.name = name;
                    input.id = id;
                });

                // Update remove button data-index
                const removeBtn = item.querySelector('.remove-feature');
                if (removeBtn) {
                    removeBtn.dataset.index = i;
                }
            });

            // Update feature count input
            document.getElementById('feature_count').value = remainingItems.length;

            // Update remove buttons
            updateRemoveButtons();
        }

        // Event listener for update count button
        document.getElementById('update_count_btn').addEventListener('click', updateFeatureCount);

        // Event listener for add feature button
        document.getElementById('add_feature_btn').addEventListener('click', addFeature);

        // Event delegation for remove feature buttons
        document.getElementById('features-container').addEventListener('click', function(e) {
            if (e.target.classList.contains('remove-feature') || e.target.parentElement.classList.contains('remove-feature')) {
                const button = e.target.classList.contains('remove-feature') ? e.target : e.target.parentElement;
                const index = parseInt(button.dataset.index);
                removeFeature(index);
            }
        });
    </script>
</body>
</html>
