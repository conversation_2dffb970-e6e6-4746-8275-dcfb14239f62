<?php
// Disable caching
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");

session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Include header
include_once 'includes/header.php';

// Get student ID from request
$studentId = isset($_GET['student_id']) ? intval($_GET['student_id']) : 0;

// Basic validation
if ($studentId <= 0) {
    echo '<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i> অবৈধ শিক্ষার্থী আইডি</div>';
    include_once 'includes/footer.php';
    exit;
}

// Get student information
$studentQuery = "SELECT s.*, c.class_name, ss.session_name, d.department_name
                FROM students s
                LEFT JOIN classes c ON s.class_id = c.id
                LEFT JOIN sessions ss ON s.session_id = ss.id
                LEFT JOIN departments d ON s.department_id = d.id
                WHERE s.id = ?";

$stmt = $conn->prepare($studentQuery);
$stmt->bind_param('i', $studentId);
$stmt->execute();
$studentResult = $stmt->get_result();

if ($studentResult->num_rows === 0) {
    echo '<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i> শিক্ষার্থী পাওয়া যায়নি</div>';
    include_once 'includes/footer.php';
    exit;
}

$student = $studentResult->fetch_assoc();
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">
        <i class="fas fa-money-bill-wave me-2"></i> শিক্ষার্থী বকেয়া পেমেন্ট
    </h1>

    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">ড্যাশবোর্ড</a></li>
        <li class="breadcrumb-item"><a href="fee_management.php">ফি ম্যানেজমেন্ট</a></li>
        <li class="breadcrumb-item active">শিক্ষার্থী বকেয়া পেমেন্ট</li>
    </ol>

    <?php
    // Display success message
    if (isset($_SESSION['success'])) {
        echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i> ' . $_SESSION['success'] . '
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
              </div>';
        unset($_SESSION['success']);
    }

    // Display error message
    if (isset($_SESSION['error'])) {
        echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i> ' . $_SESSION['error'] . '
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
              </div>';
        unset($_SESSION['error']);
    }
    ?>

    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-user-graduate me-1"></i>
                    শিক্ষার্থী তথ্য
                </div>
                <div>
                    <a href="fee_management.php" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i> ফিরে যান
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>নাম:</strong> <?= htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) ?></p>
                    <p><strong>রোল/আইডি:</strong> <?= htmlspecialchars($student['student_id']) ?></p>
                    <p><strong>শ্রেণী:</strong> <?= htmlspecialchars($student['class_name'] ?? 'N/A') ?></p>
                </div>
                <div class="col-md-6">
                    <p><strong>সেশন:</strong> <?= htmlspecialchars($student['session_name'] ?? 'N/A') ?></p>
                    <p><strong>বিভাগ:</strong> <?= htmlspecialchars($student['department_name'] ?? 'N/A') ?></p>
                    <p><strong>মোবাইল:</strong> <?= htmlspecialchars($student['phone'] ?? 'N/A') ?></p>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-file-invoice-dollar me-1"></i>
                    বকেয়া ফি তালিকা
                </div>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-primary" id="refreshDuesList">
                        <i class="fas fa-sync-alt me-1"></i> রিফ্রেশ
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <form id="student_dues_payment_form" method="POST">
                <input type="hidden" name="student_id" value="<?= $studentId ?>">

                <div id="dues_list_container">
                    <iframe src="get_student_dues_form.php?student_id=<?= $studentId ?>"
                            style="width: 100%; border: none; min-height: 500px;"
                            id="dues_list_iframe"></iframe>
                </div>

                <div id="payment_details_container" style="display: none;" class="mt-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-money-check-alt me-2"></i> পেমেন্ট বিবরণ
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="payment_date" class="form-label">পেমেন্ট তারিখ <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="payment_date" name="payment_date" value="<?= date('Y-m-d') ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="payment_method" class="form-label">পেমেন্ট পদ্ধতি <span class="text-danger">*</span></label>
                                    <select class="form-select" id="payment_method" name="payment_method" required>
                                        <option value="cash">নগদ</option>
                                        <option value="bkash">বিকাশ</option>
                                        <option value="nagad">নগদ (মোবাইল ব্যাংকিং)</option>
                                        <option value="rocket">রকেট</option>
                                        <option value="bank">ব্যাংক</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="receipt_no" class="form-label">রিসিপ্ট নং</label>
                                    <input type="text" class="form-control" id="receipt_no" name="receipt_no" placeholder="স্বয়ংক্রিয়ভাবে তৈরি হবে">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="notes" class="form-label">নোট</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="1"></textarea>
                                </div>
                            </div>
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i> পেমেন্ট যোগ করুন
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Refresh dues list
        const refreshButton = document.getElementById('refreshDuesList');
        const duesListIframe = document.getElementById('dues_list_iframe');

        if (refreshButton && duesListIframe) {
            refreshButton.addEventListener('click', function() {
                duesListIframe.src = duesListIframe.src;
            });
        }
    });
</script>

<?php
// Include footer
include_once 'includes/footer.php';
?>
