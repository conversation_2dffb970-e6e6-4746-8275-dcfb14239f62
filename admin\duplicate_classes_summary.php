<?php
require_once '../includes/dbh.inc.php';
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Duplicate Classes - Summary Report</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Hind Siliguri', sans-serif; }
        .status-card { border-left: 4px solid; }
        .status-success { border-left-color: #28a745; }
        .status-warning { border-left-color: #ffc107; }
        .status-info { border-left-color: #17a2b8; }
        .status-danger { border-left-color: #dc3545; }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            Duplicate Classes - Summary Report
                        </h4>
                    </div>
                    <div class="card-body">
                        
                        <!-- Current Status -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card status-card status-success">
                                    <div class="card-body">
                                        <h5 class="card-title text-success">
                                            <i class="fas fa-check-circle me-2"></i>Current Status
                                        </h5>
                                        <?php
                                        // Check for current duplicates
                                        $duplicatesQuery = "
                                            SELECT class_name, COUNT(*) as count
                                            FROM classes
                                            GROUP BY class_name
                                            HAVING COUNT(*) > 1
                                        ";
                                        $duplicatesResult = $conn->query($duplicatesQuery);
                                        $duplicateCount = $duplicatesResult ? $duplicatesResult->num_rows : 0;
                                        
                                        if ($duplicateCount == 0) {
                                            echo "<p class='text-success mb-0'>✅ No duplicate classes found!</p>";
                                        } else {
                                            echo "<p class='text-danger mb-0'>❌ $duplicateCount duplicate class names found!</p>";
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card status-card status-info">
                                    <div class="card-body">
                                        <h5 class="card-title text-info">
                                            <i class="fas fa-database me-2"></i>Database Protection
                                        </h5>
                                        <?php
                                        // Check for unique constraint
                                        $constraintQuery = "SHOW INDEX FROM classes WHERE Key_name = 'unique_class_name'";
                                        $constraintResult = $conn->query($constraintQuery);
                                        $hasConstraint = $constraintResult && $constraintResult->num_rows > 0;
                                        
                                        if ($hasConstraint) {
                                            echo "<p class='text-success mb-0'>✅ Unique constraint active!</p>";
                                        } else {
                                            echo "<p class='text-warning mb-0'>⚠️ No unique constraint found!</p>";
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Actions Taken -->
                        <div class="card status-card status-success mb-4">
                            <div class="card-body">
                                <h5 class="card-title text-success">
                                    <i class="fas fa-tools me-2"></i>Actions Taken
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Frontend Fixes:</h6>
                                        <ul class="list-unstyled">
                                            <li>✅ admin/add_student.php - Duplicate prevention</li>
                                            <li>✅ admin/students.php - Filter dropdown fix</li>
                                            <li>✅ admin/subjects.php - Class selection fix</li>
                                            <li>✅ admin/attendance.php - Dropdown fix</li>
                                            <li>✅ admin/generate_id_cards.php - Filter fix</li>
                                            <li>✅ exams.php - Class filter fix</li>
                                            <li>✅ admin/classes.php - Display fix</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Backend Fixes:</h6>
                                        <ul class="list-unstyled">
                                            <li>✅ SQL DISTINCT queries</li>
                                            <li>✅ PHP duplicate prevention logic</li>
                                            <li>✅ Array tracking ($seenClasses)</li>
                                            <li>✅ data_seek(0) pointer reset</li>
                                            <li>✅ Enhanced error handling</li>
                                            <li>✅ Database constraint protection</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Current Classes -->
                        <div class="card status-card status-info mb-4">
                            <div class="card-body">
                                <h5 class="card-title text-info">
                                    <i class="fas fa-list me-2"></i>Current Classes List
                                </h5>
                                <?php
                                $classesQuery = "SELECT id, class_name FROM classes ORDER BY class_name";
                                $classesResult = $conn->query($classesQuery);
                                
                                if ($classesResult && $classesResult->num_rows > 0) {
                                    echo "<div class='table-responsive'>";
                                    echo "<table class='table table-sm table-striped'>";
                                    echo "<thead><tr><th>ID</th><th>Class Name</th></tr></thead>";
                                    echo "<tbody>";
                                    
                                    while ($row = $classesResult->fetch_assoc()) {
                                        echo "<tr>";
                                        echo "<td>" . $row['id'] . "</td>";
                                        echo "<td>" . htmlspecialchars($row['class_name']) . "</td>";
                                        echo "</tr>";
                                    }
                                    
                                    echo "</tbody></table>";
                                    echo "</div>";
                                    echo "<p class='text-muted'>Total Classes: " . $classesResult->num_rows . "</p>";
                                } else {
                                    echo "<p class='text-warning'>No classes found.</p>";
                                }
                                ?>
                            </div>
                        </div>
                        
                        <!-- Technical Details -->
                        <div class="card status-card status-warning mb-4">
                            <div class="card-body">
                                <h5 class="card-title text-warning">
                                    <i class="fas fa-cog me-2"></i>Technical Implementation
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>SQL Level Prevention:</h6>
                                        <pre class="bg-light p-2 rounded"><code>SELECT DISTINCT id, class_name 
FROM classes 
ORDER BY class_name</code></pre>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>PHP Level Prevention:</h6>
                                        <pre class="bg-light p-2 rounded"><code>$seenClasses = [];
if (!in_array($class['class_name'], $seenClasses)) {
    $seenClasses[] = $class['class_name'];
    // Process class
}</code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Recommendations -->
                        <div class="card status-card status-info">
                            <div class="card-body">
                                <h5 class="card-title text-info">
                                    <i class="fas fa-lightbulb me-2"></i>Recommendations
                                </h5>
                                <ul>
                                    <li><strong>Database Level:</strong> Keep the unique constraint on class_name column</li>
                                    <li><strong>Application Level:</strong> Continue using DISTINCT queries and duplicate prevention logic</li>
                                    <li><strong>Monitoring:</strong> Regularly check for duplicates using the provided scripts</li>
                                    <li><strong>Testing:</strong> Test all class-related functionality after any database changes</li>
                                    <li><strong>Backup:</strong> Always backup database before running cleanup scripts</li>
                                </ul>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="text-center mt-4">
                            <a href="classes.php" class="btn btn-primary me-2">
                                <i class="fas fa-list me-2"></i>View Classes
                            </a>
                            <a href="remove_duplicate_classes.php" class="btn btn-warning me-2">
                                <i class="fas fa-broom me-2"></i>Remove Duplicates
                            </a>
                            <a href="add_unique_constraint.php" class="btn btn-info">
                                <i class="fas fa-shield-alt me-2"></i>Add Constraint
                            </a>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
