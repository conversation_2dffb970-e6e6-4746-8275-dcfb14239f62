<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'includes/dbh.inc.php';

echo "<h1>Creating Certificates Table</h1>";
echo "<pre>";

// Ensure connection is active
$conn = ensure_connection();

if ($conn) {
    echo "Database connection successful.\n";
    
    // Read the SQL file
    $sql_file = file_get_contents('create_certificates_table.sql');
    
    if ($sql_file === false) {
        echo "Error: Could not read the SQL file.\n";
        exit;
    }
    
    echo "SQL file read successfully.\n";
    
    // Split the SQL file into individual statements
    $statements = explode(';', $sql_file);
    
    // Execute each statement
    foreach ($statements as $statement) {
        $statement = trim($statement);
        
        // Skip empty statements
        if (empty($statement)) {
            continue;
        }
        
        echo "Executing: " . substr($statement, 0, 100) . "...\n";
        
        try {
            $result = $conn->query($statement);
            
            if ($result) {
                if ($result !== true) {
                    // For SELECT statements that return a result set
                    while ($row = $result->fetch_assoc()) {
                        foreach ($row as $key => $value) {
                            echo "$key: $value\n";
                        }
                    }
                    $result->free();
                }
                echo "Statement executed successfully.\n";
            } else {
                echo "Error executing statement: " . $conn->error . "\n";
            }
        } catch (Exception $e) {
            echo "Error: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    // Verify the table was created
    try {
        $result = $conn->query("DESCRIBE certificates");
        
        if ($result) {
            echo "Table structure for 'certificates':\n";
            echo "------------------------------------\n";
            echo "Field | Type | Null | Key | Default | Extra\n";
            echo "------------------------------------\n";
            
            while ($row = $result->fetch_assoc()) {
                echo "{$row['Field']} | {$row['Type']} | {$row['Null']} | {$row['Key']} | {$row['Default']} | {$row['Extra']}\n";
            }
            
            $result->free();
        } else {
            echo "Error verifying table: " . $conn->error . "\n";
        }
    } catch (Exception $e) {
        echo "Error verifying table: " . $e->getMessage() . "\n";
    }
    
    $conn->close();
    echo "Database connection closed.\n";
} else {
    echo "Error: Could not establish database connection.\n";
}

echo "</pre>";
echo "<p>Process completed. <a href='admin/certificates.php'>Go to Certificates Page</a></p>";
?>
