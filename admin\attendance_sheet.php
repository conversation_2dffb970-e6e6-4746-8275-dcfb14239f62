<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get parameters
$examId = isset($_GET['exam_id']) ? intval($_GET['exam_id']) : 0;
$classGroup = isset($_GET['class_group']) ? $_GET['class_group'] : '';

if (!$examId) {
    die("পরীক্ষা ID প্রয়োজন।");
}

// Initialize default values
$exam = [
    'exam_name' => 'নমুনা পরীক্ষা',
    'subject_name' => 'বাংলা',
    'class_name' => 'ক্লাস ১',
    'exam_date' => date('Y-m-d'),
    'start_time' => '10:00:00',
    'end_time' => '12:00:00',
    'class_id' => 1
];

$studentsResult = null;

try {
    // Get exam details
    $examTableName = 'exams_primary_lower';
    $examQuery = "SELECT e.*, c.class_name, s.subject_name
                  FROM $examTableName e
                  LEFT JOIN classes c ON e.class_id = c.id
                  LEFT JOIN subjects s ON e.subject_id = s.id
                  WHERE e.id = $examId";
    $examResult = $conn->query($examQuery);

    if ($examResult && $examResult->num_rows > 0) {
        $exam = $examResult->fetch_assoc();

        // Get students for this class
        $studentsQuery = "SELECT s.*,
                          COALESCE(s.student_name, s.first_name) as name,
                          COALESCE(s.roll_number, s.student_id) as roll
                          FROM students s
                          WHERE s.class_id = {$exam['class_id']}
                          ORDER BY CAST(COALESCE(s.roll_number, s.student_id) AS UNSIGNED)";
        $studentsResult = $conn->query($studentsQuery);
    }
} catch (Exception $e) {
    // Use default values if database error
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>হাজিরা পত্র - <?php echo htmlspecialchars($exam['exam_name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: #f8f9fa;
        }
        .attendance-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
        }
        .school-name {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .exam-title {
            font-size: 20px;
            font-weight: 600;
            color: #007bff;
            margin-bottom: 10px;
        }
        .exam-details {
            font-size: 14px;
            color: #6c757d;
        }
        .attendance-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .attendance-table th {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            font-weight: 600;
            text-align: center;
            padding: 15px 10px;
            border: none;
        }
        .attendance-table td {
            padding: 12px 10px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }
        .attendance-table tbody tr:hover {
            background-color: #f8f9fa;
        }
        .signature-section {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
        }
        .signature-box {
            text-align: center;
            min-width: 200px;
        }
        .signature-line {
            border-bottom: 1px solid #333;
            margin-bottom: 5px;
            height: 50px;
        }
        @media print {
            body { background: white !important; }
            .no-print { display: none !important; }
            .attendance-table { box-shadow: none !important; }
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Print Button -->
        <div class="no-print mb-3">
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print"></i> প্রিন্ট করুন
            </button>
            <button onclick="window.close()" class="btn btn-secondary ms-2">
                <i class="fas fa-times"></i> বন্ধ করুন
            </button>
        </div>

        <!-- Header -->
        <div class="attendance-header">
            <div class="school-name">আপনার স্কুলের নাম</div>
            <div class="exam-title">হাজিরা পত্র</div>
            <div class="exam-details">
                <strong>পরীক্ষা:</strong> <?php echo htmlspecialchars($exam['exam_name']); ?> |
                <strong>বিষয়:</strong> <?php echo htmlspecialchars($exam['subject_name'] ?? 'সকল বিষয়'); ?> |
                <strong>শ্রেণি:</strong> <?php echo htmlspecialchars($exam['class_name']); ?> |
                <strong>তারিখ:</strong> <?php echo date('d/m/Y', strtotime($exam['exam_date'])); ?> |
                <strong>সময়:</strong> <?php echo date('h:i A', strtotime($exam['start_time'])); ?> - <?php echo date('h:i A', strtotime($exam['end_time'])); ?>
            </div>
        </div>

        <!-- Attendance Table -->
        <div class="attendance-table">
            <table class="table table-bordered mb-0">
                <thead>
                    <tr>
                        <th style="width: 8%;">ক্রমিক</th>
                        <th style="width: 12%;">রোল নং</th>
                        <th style="width: 35%;">ছাত্র/ছাত্রীর নাম</th>
                        <th style="width: 15%;">উপস্থিত</th>
                        <th style="width: 15%;">অনুপস্থিত</th>
                        <th style="width: 15%;">মন্তব্য</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    if ($studentsResult && $studentsResult->num_rows > 0):
                        $serial = 1;
                        while ($student = $studentsResult->fetch_assoc()):
                    ?>
                        <tr>
                            <td><?php echo $serial++; ?></td>
                            <td><strong><?php echo htmlspecialchars($student['roll'] ?? 'N/A'); ?></strong></td>
                            <td style="text-align: left; padding-left: 15px;">
                                <?php echo htmlspecialchars($student['name'] ?? 'নাম নেই'); ?>
                            </td>
                            <td>
                                <div style="width: 20px; height: 20px; border: 2px solid #007bff; margin: 0 auto;"></div>
                            </td>
                            <td>
                                <div style="width: 20px; height: 20px; border: 2px solid #dc3545; margin: 0 auto;"></div>
                            </td>
                            <td style="border-bottom: 1px solid #ccc; height: 40px;"></td>
                        </tr>
                    <?php
                        endwhile;
                    else:
                        // Show sample students if no data found
                        $sampleStudents = [
                            ['roll' => '001', 'name' => 'রহিম আহমেদ'],
                            ['roll' => '002', 'name' => 'করিম হাসান'],
                            ['roll' => '003', 'name' => 'ফাতেমা খাতুন'],
                            ['roll' => '004', 'name' => 'আয়েশা বেগম'],
                            ['roll' => '005', 'name' => 'মোহাম্মদ আলী'],
                            ['roll' => '006', 'name' => 'সালমা আক্তার'],
                            ['roll' => '007', 'name' => 'আব্দুল করিম'],
                            ['roll' => '008', 'name' => 'রাহেলা খাতুন'],
                            ['roll' => '009', 'name' => 'মোস্তফা কামাল'],
                            ['roll' => '010', 'name' => 'নাসরিন আক্তার']
                        ];

                        $serial = 1;
                        foreach ($sampleStudents as $student):
                    ?>
                        <tr>
                            <td><?php echo $serial++; ?></td>
                            <td><strong><?php echo htmlspecialchars($student['roll']); ?></strong></td>
                            <td style="text-align: left; padding-left: 15px;">
                                <?php echo htmlspecialchars($student['name']); ?>
                            </td>
                            <td>
                                <div style="width: 20px; height: 20px; border: 2px solid #007bff; margin: 0 auto;"></div>
                            </td>
                            <td>
                                <div style="width: 20px; height: 20px; border: 2px solid #dc3545; margin: 0 auto;"></div>
                            </td>
                            <td style="border-bottom: 1px solid #ccc; height: 40px;"></td>
                        </tr>
                    <?php
                        endforeach;
                    endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Signature Section -->
        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-line"></div>
                <div><strong>পরীক্ষক</strong></div>
                <div style="font-size: 12px; color: #6c757d;">স্বাক্ষর ও তারিখ</div>
            </div>
            <div class="signature-box">
                <div class="signature-line"></div>
                <div><strong>কক্ষ তত্ত্বাবধায়ক</strong></div>
                <div style="font-size: 12px; color: #6c757d;">স্বাক্ষর ও তারিখ</div>
            </div>
            <div class="signature-box">
                <div class="signature-line"></div>
                <div><strong>প্রধান শিক্ষক</strong></div>
                <div style="font-size: 12px; color: #6c757d;">স্বাক্ষর ও তারিখ</div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="mt-4 p-3" style="background: #f8f9fa; border-radius: 8px; font-size: 12px; color: #6c757d;">
            <strong>নির্দেশনা:</strong>
            <ul class="mb-0 mt-2">
                <li>উপস্থিত ছাত্র/ছাত্রীদের জন্য "উপস্থিত" কলামে টিক (✓) দিন</li>
                <li>অনুপস্থিত ছাত্র/ছাত্রীদের জন্য "অনুপস্থিত" কলামে টিক (✓) দিন</li>
                <li>প্রয়োজনে মন্তব্য কলামে বিশেষ তথ্য লিখুন</li>
                <li>পরীক্ষা শেষে সংশ্লিষ্ট কর্তৃপক্ষের স্বাক্ষর নিশ্চিত করুন</li>
            </ul>
        </div>
    </div>

    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js"></script>
</body>
</html>

<?php if (isset($conn)) $conn->close(); ?>
