<?php
session_start();
require_once '../includes/dbh.inc.php';
require_once '../includes/bkash_config.php';
require_once '../includes/bkash_functions.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    echo json_encode([
        'status' => 'error',
        'errorMessage' => 'Unauthorized access'
    ]);
    exit();
}

// Check if action is provided
if (!isset($_POST['action'])) {
    echo json_encode([
        'status' => 'error',
        'errorMessage' => 'No action specified'
    ]);
    exit();
}

$action = $_POST['action'];

// Handle different actions
switch ($action) {
    case 'createPayment':
        handleCreatePayment();
        break;

    case 'executePayment':
        handleExecutePayment();
        break;

    case 'queryPayment':
        handleQueryPayment();
        break;

    case 'testToken':
        handleTestToken();
        break;

    default:
        echo json_encode([
            'status' => 'error',
            'errorMessage' => 'Invalid action'
        ]);
        break;
}

/**
 * Handle create payment request
 */
function handleCreatePayment() {
    global $conn;

    // Check required parameters
    if (!isset($_POST['token']) || !isset($_POST['amount']) || !isset($_POST['invoice']) || !isset($_POST['fee_id'])) {
        echo json_encode([
            'status' => 'error',
            'errorMessage' => 'Missing required parameters'
        ]);
        return;
    }

    $token = $_POST['token'];
    $amount = floatval($_POST['amount']);
    $invoice = $_POST['invoice'];
    $feeId = intval($_POST['fee_id']);

    // Create payment
    $response = bkashCreatePayment($token, $amount, $invoice);

    // Store payment ID in session for later use
    if (isset($response['paymentID'])) {
        $_SESSION['bkash_payment_id'] = $response['paymentID'];

        // Record initial payment in database
        recordBkashPayment($conn, $feeId, $amount, $response['paymentID'], '', 'Initiated');
    }

    // Return response
    echo json_encode($response);
}

/**
 * Handle execute payment request
 */
function handleExecutePayment() {
    global $conn;

    // Check required parameters
    if (!isset($_POST['token']) || !isset($_POST['fee_id']) || !isset($_SESSION['bkash_payment_id'])) {
        echo json_encode([
            'status' => 'error',
            'errorMessage' => 'Missing required parameters'
        ]);
        return;
    }

    $token = $_POST['token'];
    $paymentId = $_SESSION['bkash_payment_id'];
    $feeId = intval($_POST['fee_id']);

    // Execute payment
    $response = bkashExecutePayment($token, $paymentId);

    // Update payment status in database
    if (isset($response['trxID'])) {
        $status = $response['transactionStatus'] ?? 'Unknown';
        updateBkashPaymentStatus($conn, $paymentId, $response['trxID'], $status);

        // If payment is completed, update fee payment
        if ($status === 'Completed') {
            $amount = floatval($response['amount']);
            $trxId = $response['trxID'];
            updateFeePaymentAfterBkash($conn, $feeId, $amount, $trxId);
        }
    }

    // Return response
    echo json_encode($response);
}

/**
 * Handle query payment request
 */
function handleQueryPayment() {
    // Check required parameters
    if (!isset($_POST['token']) || !isset($_POST['payment_id'])) {
        echo json_encode([
            'status' => 'error',
            'errorMessage' => 'Missing required parameters'
        ]);
        return;
    }

    $token = $_POST['token'];
    $paymentId = $_POST['payment_id'];

    // Query payment
    $response = bkashQueryPayment($token, $paymentId);

    // Return response
    echo json_encode($response);
}

/**
 * Handle test token request
 */
function handleTestToken() {
    try {
        // Get token
        $response = bkashGrantToken();

        if (isset($response['id_token'])) {
            echo json_encode([
                'status' => 'success',
                'token' => $response['id_token']
            ]);
        } else {
            echo json_encode([
                'status' => 'error',
                'message' => 'Failed to get token: ' . ($response['errorMessage'] ?? 'Unknown error')
            ]);
        }
    } catch (Exception $e) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Exception: ' . $e->getMessage()
        ]);
    }
}
