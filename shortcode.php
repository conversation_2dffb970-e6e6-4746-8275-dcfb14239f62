<?php
require_once 'includes/dbh.inc.php';

// Initialize variables
$shortcode = '';
$student = null;
$error_message = '';
$success = false;

// Check if student_shortcodes table exists
$check_table_sql = "SHOW TABLES LIKE 'student_shortcodes'";
$table_exists = $conn->query($check_table_sql)->num_rows > 0;

// If table doesn't exist, set error message
if (!$table_exists) {
    $error_message = "সর্টকোড সিস্টেম এখনও সেটআপ করা হয়নি।";
}

// Check if form is submitted
if (isset($_GET['search']) && !empty($_GET['shortcode'])) {
    $shortcode = trim($_GET['shortcode']);

    if ($table_exists) {
        // Build query based on available tables and columns
        $select_fields = "sc.shortcode";
        $join_clause = "";

        // Check students table structure
        $check_students_columns = "SHOW COLUMNS FROM students";
        $students_columns_result = $conn->query($check_students_columns);
        $has_first_name = false;
        $has_last_name = false;
        $has_student_id = false;
        $has_class_id = false;
        $has_section = false;
        $has_phone = false;

        if ($students_columns_result) {
            while ($column = $students_columns_result->fetch_assoc()) {
                if ($column['Field'] == 'first_name') {
                    $has_first_name = true;
                    $select_fields .= ", s.first_name";
                }
                if ($column['Field'] == 'last_name') {
                    $has_last_name = true;
                    $select_fields .= ", s.last_name";
                }
                if ($column['Field'] == 'student_id') {
                    $has_student_id = true;
                    $select_fields .= ", s.student_id";
                }
                if ($column['Field'] == 'class_id') {
                    $has_class_id = true;
                }
                if ($column['Field'] == 'section') {
                    $has_section = true;
                    $select_fields .= ", s.section";
                }
                if ($column['Field'] == 'phone') {
                    $has_phone = true;
                    $select_fields .= ", s.phone";
                }
            }
        }

        // Check if classes table exists
        $check_classes_table = "SHOW TABLES LIKE 'classes'";
        $classes_table_exists = $conn->query($check_classes_table)->num_rows > 0;

        if ($classes_table_exists && $has_class_id) {
            $join_clause .= " LEFT JOIN classes c ON s.class_id = c.id";
            $select_fields .= ", c.class_name";
        }

        // Prepare and execute query
        $query = "SELECT $select_fields
                 FROM student_shortcodes sc
                 JOIN students s ON sc.student_id = s.id
                 $join_clause
                 WHERE sc.shortcode = ?";

        $stmt = $conn->prepare($query);
        $stmt->bind_param("s", $shortcode);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result && $result->num_rows > 0) {
            $student = $result->fetch_assoc();
            $success = true;
        } else {
            $error_message = "কোন শিক্ষার্থী পাওয়া যায়নি। সর্টকোড সঠিকভাবে লিখুন।";
        }
    } else {
        $error_message = "সর্টকোড সিস্টেম এখনও সেটআপ করা হয়নি।";
    }
}

// Get school info
$school_name = "আদর্শ বিদ্যালয়";
$school_address = "ঢাকা, বাংলাদেশ";

// Check if settings table exists
$check_settings_table = "SHOW TABLES LIKE 'settings'";
$settings_table_exists = $conn->query($check_settings_table)->num_rows > 0;

if ($settings_table_exists) {
    $settings_query = "SELECT school_name, address FROM settings LIMIT 1";
    $settings_result = $conn->query($settings_query);

    if ($settings_result && $settings_result->num_rows > 0) {
        $settings = $settings_result->fetch_assoc();
        $school_name = $settings['school_name'];
        $school_address = $settings['address'];
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>শিক্ষার্থী সর্টকোড - <?php echo $school_name; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        body {
            font-family: 'SolaimanLipi', Arial, sans-serif;
            background-color: #f8f9fa;
            padding-top: 20px;
            padding-bottom: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .school-name {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .school-address {
            font-size: 16px;
            color: #6c757d;
            margin-bottom: 15px;
        }
        .search-card {
            max-width: 600px;
            margin: 0 auto 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .result-card {
            max-width: 600px;
            margin: 0 auto;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .shortcode-badge {
            font-size: 1.2rem;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            background-color: #0d6efd;
            color: white;
            display: inline-block;
            margin-bottom: 15px;
        }
        .student-info {
            margin-bottom: 20px;
        }
        .info-label {
            font-weight: bold;
            color: #6c757d;
        }
        .info-value {
            font-weight: normal;
        }
        .search-form {
            padding: 20px;
        }
        .footer {
            text-align: center;
            margin-top: 50px;
            color: #6c757d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="school-name"><?php echo $school_name; ?></div>
            <div class="school-address"><?php echo $school_address; ?></div>
            <h2>শিক্ষার্থী সর্টকোড</h2>
        </div>

        <div class="card search-card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-search me-2"></i> শিক্ষার্থী খুঁজুন</h5>
            </div>
            <div class="card-body search-form">
                <form method="get" action="">
                    <div class="mb-3">
                        <label for="shortcode" class="form-label">সর্টকোড</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="shortcode" name="shortcode"
                                   placeholder="সর্টকোড লিখুন (যেমন: S001)" value="<?php echo htmlspecialchars($shortcode); ?>" required>
                            <button type="submit" name="search" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i> খুঁজুন
                            </button>
                        </div>
                        <div class="form-text">শিক্ষার্থীর সর্টকোড লিখে খুঁজুন</div>
                    </div>
                </form>
            </div>
        </div>

        <?php if (!empty($error_message)): ?>
            <div class="alert alert-danger text-center" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error_message; ?>
            </div>

            <?php if (!$table_exists): ?>
            <div class="card mt-4">
                <div class="card-body text-center">
                    <h5 class="card-title">সর্টকোড সিস্টেম সেটআপ করতে</h5>
                    <p>অ্যাডমিন প্যানেলে লগইন করে "শিক্ষার্থী সর্টকোড" মেনুতে যান এবং সর্টকোড তৈরি করুন।</p>
                    <a href="admin/student_shortcodes.php" class="btn btn-primary">
                        <i class="fas fa-cog me-2"></i>সর্টকোড সেটআপ করুন
                    </a>
                </div>
            </div>
            <?php endif; ?>
        <?php endif; ?>

        <?php if ($success && $student): ?>
            <div class="card result-card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-user-graduate me-2"></i> শিক্ষার্থীর তথ্য</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="shortcode-badge">
                            <i class="fas fa-hashtag me-1"></i> <?php echo htmlspecialchars($student['shortcode']); ?>
                        </div>
                    </div>

                    <div class="student-info">
                        <div class="row mb-3">
                            <div class="col-md-4 info-label">নাম:</div>
                            <div class="col-md-8 info-value">
                                <?php
                                $name = '';
                                if (isset($student['first_name'])) {
                                    $name .= $student['first_name'];
                                }
                                if (isset($student['last_name'])) {
                                    $name .= ' ' . $student['last_name'];
                                }
                                echo !empty($name) ? htmlspecialchars($name) : 'N/A';
                                ?>
                            </div>
                        </div>

                        <?php if (isset($student['student_id'])): ?>
                        <div class="row mb-3">
                            <div class="col-md-4 info-label">রোল নম্বর:</div>
                            <div class="col-md-8 info-value"><?php echo htmlspecialchars($student['student_id']); ?></div>
                        </div>
                        <?php endif; ?>

                        <?php if (isset($student['class_name'])): ?>
                        <div class="row mb-3">
                            <div class="col-md-4 info-label">শ্রেণী:</div>
                            <div class="col-md-8 info-value"><?php echo htmlspecialchars($student['class_name']); ?></div>
                        </div>
                        <?php endif; ?>

                        <?php if (isset($student['section'])): ?>
                        <div class="row mb-3">
                            <div class="col-md-4 info-label">সেকশন:</div>
                            <div class="col-md-8 info-value"><?php echo htmlspecialchars($student['section']); ?></div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="text-center mt-4">
                        <a href="index.php" class="btn btn-primary">
                            <i class="fas fa-home me-1"></i> হোমপেজে ফিরে যান
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <div class="footer">
            <p>&copy; <?php echo date('Y'); ?> <?php echo $school_name; ?>. সর্বস্বত্ব সংরক্ষিত।</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
