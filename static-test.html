<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <title>নিশাত এডুকেশন সেন্টার - Static</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            text-align: center;
        }
        .success {
            background: rgba(76, 175, 80, 0.3);
            border: 2px solid #4CAF50;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .test-item {
            background: rgba(255,255,255,0.2);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏫 নিশাত এডুকেশন সেন্টার</h1>
        
        <div class="success">
            ✅ <strong>Static HTML Test</strong><br>
            No PHP, No Server Processing
        </div>
        
        <div class="test-item">
            📊 <strong>Status:</strong> <span id="status">Loading...</span>
        </div>
        
        <div class="test-item">
            🕒 <strong>Load Time:</strong> <span id="time">Loading...</span>
        </div>
        
        <div class="test-item">
            🎯 <strong>Tab Icon Status:</strong> <span id="tab-status">Checking...</span>
        </div>
        
        <h2>🧪 Critical Test:</h2>
        <p><strong>Watch the browser tab!</strong></p>
        <p>If the loading icon stops spinning here, the problem is with PHP/XAMPP.</p>
        <p>If it continues spinning, the problem is with browser/system.</p>
    </div>

    <script>
        // Set content immediately
        document.getElementById('status').textContent = 'Loaded Successfully ✅';
        document.getElementById('time').textContent = new Date().toLocaleString();
        
        // Check tab status after a delay
        setTimeout(function() {
            document.getElementById('tab-status').textContent = 'Should be stopped now ✅';
            console.log('Static HTML loaded - tab icon should stop spinning');
        }, 1000);
        
        // Set final title
        document.title = 'নিশাত এডুকেশন সেন্টার - Static ✅';
    </script>
</body>
</html>
