<?php
session_start();
require_once 'includes/db_connection.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Initialize variables
$success_messages = [];
$error_messages = [];
$log_messages = [];
$subjects = [];
$classes = [];
$selected_subject = '';
$selected_class = '';
$selected_department = '';
$departments = [];

// Debug: Show all tables in database
$all_tables_sql = "SHOW TABLES";
$all_tables_result = $conn->query($all_tables_sql);
$log_messages[] = "ডাটাবেসে উপস্থিত টেবিলসমূহ:";
while ($table_row = $all_tables_result->fetch_row()) {
    $log_messages[] = "- " . $table_row[0];
}

// Check if subjects table exists
$check_subjects_table = $conn->query("SHOW TABLES LIKE 'subject'");
if ($check_subjects_table->num_rows == 0) {
    $check_subjects_table = $conn->query("SHOW TABLES LIKE 'subjects'");
    if ($check_subjects_table->num_rows == 0) {
        $log_messages[] = "সাবজেক্ট টেবিল পাওয়া যায়নি। নতুন টেবিল তৈরি করা হচ্ছে...";

        // Create subjects table
        $create_table_sql = "CREATE TABLE `subjects` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `subject_name` varchar(255) NOT NULL,
            `subject_code` varchar(50) NOT NULL UNIQUE,
            `is_active` tinyint(1) DEFAULT 1,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

        if ($conn->query($create_table_sql)) {
            $log_messages[] = "Created 'subjects' table successfully";
            $check_subjects_table = $conn->query("SHOW TABLES LIKE 'subjects'");

            // Insert some default subjects
            $insert_sql = "INSERT INTO `subjects` (`subject_name`, `subject_code`, `is_active`) VALUES
                ('Bangla', 'BAN-101', 1),
                ('English', 'ENG-101', 1),
                ('Mathematics', 'MAT-101', 1),
                ('Science', 'SCI-101', 1),
                ('Social Science', 'SOC-101', 1),
                ('Physics', 'PHY-101', 1),
                ('Chemistry', 'CHE-101', 1),
                ('Biology', 'BIO-101', 1),
                ('Accounting', 'ACC-101', 1),
                ('Business Studies', 'BUS-101', 1),
                ('Economics', 'ECO-101', 1),
                ('History', 'HIS-101', 1),
                ('Geography', 'GEO-101', 1)";

            if ($conn->query($insert_sql)) {
                $log_messages[] = "Added default subjects to the table";
            } else {
                $log_messages[] = "Failed to add default subjects: " . $conn->error;
            }
        } else {
            $error_messages[] = "Failed to create 'subjects' table: " . $conn->error;
        }
    } else {
        $log_messages[] = "subjects টেবিল পাওয়া গেছে।";
    }
} else {
    $log_messages[] = "subject টেবিল পাওয়া গেছে।";
}

if ($check_subjects_table->num_rows > 0) {
    // Check which table exists: 'subject' or 'subjects'
    $table_name = "";
    $check_subject_singular = $conn->query("SHOW TABLES LIKE 'subject'");
    if ($check_subject_singular->num_rows > 0) {
        $table_name = "subject";
        $log_messages[] = "Using 'subject' table";
    } else {
        $table_name = "subjects";
        $log_messages[] = "Using 'subjects' table";
    }

    // Get the structure of the subjects table
    $structure_sql = "DESCRIBE $table_name";
    $structure_result = $conn->query($structure_sql);
    $columns = [];
    $log_messages[] = "$table_name টেবিলের কলামসমূহ:";

    if ($structure_result) {
        while ($row = $structure_result->fetch_assoc()) {
            $columns[] = $row['Field'];
            $log_messages[] = "- " . $row['Field'] . " (" . $row['Type'] . ")";
        }

        // Try to find name-like and code-like columns
        $name_column = in_array('name', $columns) ? 'name' : (in_array('subject_name', $columns) ? 'subject_name' : 'title');
        $code_column = in_array('code', $columns) ? 'code' : (in_array('subject_code', $columns) ? 'subject_code' : 'id');

        $log_messages[] = "Using name column: $name_column";
        $log_messages[] = "Using code column: $code_column";

        // Try first with the detected columns
        $subject_sql = "SELECT id, $name_column as subject_name, $code_column as subject_code FROM $table_name ORDER BY $name_column";
        $log_messages[] = "SQL Query: $subject_sql";

        $subject_result = $conn->query($subject_sql);
        if ($subject_result && $subject_result->num_rows > 0) {
            while ($row = $subject_result->fetch_assoc()) {
                $subjects[] = $row;
            }
            $log_messages[] = "Loaded " . count($subjects) . " subjects successfully";
        } else {
            $log_messages[] = "Failed to load subjects with query: $subject_sql";
            $log_messages[] = "Error: " . $conn->error;

            // Try a simpler query as fallback
            $subject_sql = "SELECT id, $name_column as subject_name, id as subject_code FROM $table_name";
            $log_messages[] = "Trying fallback SQL Query: $subject_sql";

            $subject_result = $conn->query($subject_sql);
            if ($subject_result && $subject_result->num_rows > 0) {
                while ($row = $subject_result->fetch_assoc()) {
                    $subjects[] = $row;
                }
                $log_messages[] = "Loaded " . count($subjects) . " subjects with fallback query";
            } else {
                $error_messages[] = "সাবজেক্ট তথ্য লোড করতে সমস্যা হয়েছে। অনুগ্রহ করে আপনার ডাটাবেস চেক করুন।";
                $log_messages[] = "Error with fallback query: " . $conn->error;
            }
        }
    } else {
        $error_messages[] = "সাবজেক্ট টেবিলের স্ট্রাকচার পাওয়া যায়নি।";
        $log_messages[] = "Error getting table structure: " . $conn->error;
    }
}

// Get all classes
$check_classes_table = $conn->query("SHOW TABLES LIKE 'class'");
if ($check_classes_table->num_rows == 0) {
    $check_classes_table = $conn->query("SHOW TABLES LIKE 'classes'");
    if ($check_classes_table->num_rows == 0) {
        $log_messages[] = "ক্লাস টেবিল পাওয়া যায়নি। নতুন টেবিল তৈরি করা হচ্ছে...";

        // Create classes table
        $create_table_sql = "CREATE TABLE `classes` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `class_name` varchar(100) NOT NULL,
            `department_id` int(11) NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

        if ($conn->query($create_table_sql)) {
            $log_messages[] = "Created 'classes' table successfully";
            $check_classes_table = $conn->query("SHOW TABLES LIKE 'classes'");

            // Insert some default classes
            $insert_sql = "INSERT INTO `classes` (`class_name`) VALUES
                ('Class 6'),
                ('Class 7'),
                ('Class 8'),
                ('Class 9'),
                ('Class 10'),
                ('Class 11'),
                ('Class 12')";

            if ($conn->query($insert_sql)) {
                $log_messages[] = "Added default classes to the table";
            } else {
                $log_messages[] = "Failed to add default classes: " . $conn->error;
            }
        } else {
            $error_messages[] = "Failed to create 'classes' table: " . $conn->error;
        }
    } else {
        $log_messages[] = "classes টেবিল পাওয়া গেছে।";
    }
} else {
    $log_messages[] = "class টেবিল পাওয়া গেছে।";
}

if ($check_classes_table->num_rows > 0) {
    // Check which table exists: 'class' or 'classes'
    $table_name = "";
    $check_class_singular = $conn->query("SHOW TABLES LIKE 'class'");
    if ($check_class_singular->num_rows > 0) {
        $table_name = "class";
        $log_messages[] = "Using 'class' table";
    } else {
        $table_name = "classes";
        $log_messages[] = "Using 'classes' table";
    }

    // Get the structure of the classes table
    $structure_sql = "DESCRIBE $table_name";
    $structure_result = $conn->query($structure_sql);
    $columns = [];
    $log_messages[] = "$table_name টেবিলের কলামসমূহ:";

    if ($structure_result) {
        while ($row = $structure_result->fetch_assoc()) {
            $columns[] = $row['Field'];
            $log_messages[] = "- " . $row['Field'] . " (" . $row['Type'] . ")";
        }

        // Try to find name-like column
        $name_column = in_array('name', $columns) ? 'name' : (in_array('class_name', $columns) ? 'class_name' : 'title');

        $log_messages[] = "Using name column: $name_column";

        // Try first with the detected columns
        $class_sql = "SELECT id, $name_column as class_name FROM $table_name ORDER BY $name_column";
        $log_messages[] = "SQL Query: $class_sql";

        $class_result = $conn->query($class_sql);
        if ($class_result && $class_result->num_rows > 0) {
            while ($row = $class_result->fetch_assoc()) {
                $classes[] = $row;
            }
            $log_messages[] = "Loaded " . count($classes) . " classes successfully";
        } else {
            $log_messages[] = "Failed to load classes with query: $class_sql";
            $log_messages[] = "Error: " . $conn->error;

            // Try a simpler query as fallback
            $class_sql = "SELECT id, id as class_name FROM $table_name";
            $log_messages[] = "Trying fallback SQL Query: $class_sql";

            $class_result = $conn->query($class_sql);
            if ($class_result && $class_result->num_rows > 0) {
                while ($row = $class_result->fetch_assoc()) {
                    $classes[] = $row;
                }
                $log_messages[] = "Loaded " . count($classes) . " classes with fallback query";
            } else {
                $error_messages[] = "ক্লাস তথ্য লোড করতে সমস্যা হয়েছে। অনুগ্রহ করে আপনার ডাটাবেস চেক করুন।";
                $log_messages[] = "Error with fallback query: " . $conn->error;
            }
        }
    } else {
        $error_messages[] = "ক্লাস টেবিলের স্ট্রাকচার পাওয়া যায়নি।";
        $log_messages[] = "Error getting table structure: " . $conn->error;
    }
}

// Get all departments
$check_departments_table = $conn->query("SHOW TABLES LIKE 'department'");
if ($check_departments_table->num_rows == 0) {
    $check_departments_table = $conn->query("SHOW TABLES LIKE 'departments'");
    if ($check_departments_table->num_rows == 0) {
        $log_messages[] = "বিভাগ টেবিল পাওয়া যায়নি। নতুন টেবিল তৈরি করা হচ্ছে...";

        // Create departments table
        $create_table_sql = "CREATE TABLE `departments` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `department_name` varchar(255) NOT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

        if ($conn->query($create_table_sql)) {
            $log_messages[] = "Created 'departments' table successfully";
            $check_departments_table = $conn->query("SHOW TABLES LIKE 'departments'");

            // Insert some default departments
            $insert_sql = "INSERT INTO `departments` (`department_name`) VALUES
                ('Science'),
                ('Arts'),
                ('Commerce'),
                ('Humanities')";

            if ($conn->query($insert_sql)) {
                $log_messages[] = "Added default departments to the table";
            } else {
                $log_messages[] = "Failed to add default departments: " . $conn->error;
            }
        } else {
            $error_messages[] = "Failed to create 'departments' table: " . $conn->error;
        }
    } else {
        $log_messages[] = "departments টেবিল পাওয়া গেছে।";
    }
} else {
    $log_messages[] = "department টেবিল পাওয়া গেছে।";
}

if ($check_departments_table->num_rows > 0) {
    // Check which table exists: 'department' or 'departments'
    $table_name = "";
    $check_department_singular = $conn->query("SHOW TABLES LIKE 'department'");
    if ($check_department_singular->num_rows > 0) {
        $table_name = "department";
        $log_messages[] = "Using 'department' table";
    } else {
        $table_name = "departments";
        $log_messages[] = "Using 'departments' table";
    }

    // Get the structure of the departments table
    $structure_sql = "DESCRIBE $table_name";
    $structure_result = $conn->query($structure_sql);
    $columns = [];
    $log_messages[] = "$table_name টেবিলের কলামসমূহ:";

    if ($structure_result) {
        while ($row = $structure_result->fetch_assoc()) {
            $columns[] = $row['Field'];
            $log_messages[] = "- " . $row['Field'] . " (" . $row['Type'] . ")";
        }

        // Try to find name-like column
        $name_column = in_array('name', $columns) ? 'name' : (in_array('department_name', $columns) ? 'department_name' : 'title');

        $log_messages[] = "Using name column: $name_column";

        // Try first with the detected columns
        $department_sql = "SELECT id, $name_column as department_name FROM $table_name ORDER BY $name_column";
        $log_messages[] = "SQL Query: $department_sql";

        $department_result = $conn->query($department_sql);
        if ($department_result && $department_result->num_rows > 0) {
            while ($row = $department_result->fetch_assoc()) {
                $departments[] = $row;
            }
            $log_messages[] = "Loaded " . count($departments) . " departments successfully";
        } else {
            $log_messages[] = "Failed to load departments with query: $department_sql";
            $log_messages[] = "Error: " . $conn->error;

            // Try a simpler query as fallback
            $department_sql = "SELECT id, id as department_name FROM $table_name";
            $log_messages[] = "Trying fallback SQL Query: $department_sql";

            $department_result = $conn->query($department_sql);
            if ($department_result && $department_result->num_rows > 0) {
                while ($row = $department_result->fetch_assoc()) {
                    $departments[] = $row;
                }
                $log_messages[] = "Loaded " . count($departments) . " departments with fallback query";
            } else {
                $log_messages[] = "বিভাগ তথ্য লোড করতে সমস্যা হয়েছে। বিভাগ ফিল্টার অপশন দেখানো হবে না।";
                $log_messages[] = "Error with fallback query: " . $conn->error;
            }
        }
    } else {
        $log_messages[] = "বিভাগ টেবিলের স্ট্রাকচার পাওয়া যায়নি।";
        $log_messages[] = "Error getting table structure: " . $conn->error;
    }
}

// Check if student_subjects table exists
$check_student_subjects_table = $conn->query("SHOW TABLES LIKE 'student_subjects'");
if ($check_student_subjects_table->num_rows == 0) {
    $check_student_subjects_table = $conn->query("SHOW TABLES LIKE 'student_subject'");
    if ($check_student_subjects_table->num_rows == 0) {
        // Create student_subjects table if it doesn't exist
        $log_messages[] = "student_subjects টেবিল পাওয়া যায়নি। নতুন টেবিল তৈরি করা হচ্ছে...";

        $create_table_sql = "CREATE TABLE `student_subjects` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `student_id` int(11) NOT NULL,
            `subject_id` int(11) NOT NULL,
            `category` varchar(50) DEFAULT 'required',
            `session_id` int(11) DEFAULT 1,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `student_subject_unique` (`student_id`, `subject_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

        if ($conn->query($create_table_sql)) {
            $log_messages[] = "Created 'student_subjects' table successfully";
        } else {
            $error_messages[] = "Failed to create 'student_subjects' table: " . $conn->error;

            // Try with simpler SQL
            $create_table_sql = "CREATE TABLE `student_subjects` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `student_id` int(11) NOT NULL,
                `subject_id` int(11) NOT NULL,
                `category` varchar(50) DEFAULT 'required',
                `session_id` int(11) DEFAULT 1,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `student_subject_unique` (`student_id`, `subject_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

            if ($conn->query($create_table_sql)) {
                $log_messages[] = "Created 'student_subjects' table with simpler SQL successfully";
            } else {
                $error_messages[] = "Failed to create 'student_subjects' table with simpler SQL: " . $conn->error;
            }
        }
    } else {
        $log_messages[] = "student_subject টেবিল পাওয়া গেছে।";
    }
}

// Check which table exists: 'student_subjects' or 'student_subject'
$table_name = "";
$check_student_subjects_plural = $conn->query("SHOW TABLES LIKE 'student_subjects'");
if ($check_student_subjects_plural->num_rows > 0) {
    $table_name = "student_subjects";
    $log_messages[] = "Using 'student_subjects' table";
} else {
    $check_student_subjects_singular = $conn->query("SHOW TABLES LIKE 'student_subject'");
    if ($check_student_subjects_singular->num_rows > 0) {
        $table_name = "student_subject";
        $log_messages[] = "Using 'student_subject' table";
    } else {
        $error_messages[] = "student_subjects বা student_subject টেবিল পাওয়া যায়নি।";
    }
}

if (!empty($table_name)) {
    // Check if student_subjects table has category and session_id columns
    $check_category_sql = "SHOW COLUMNS FROM $table_name LIKE 'category'";
    $check_category_result = $conn->query($check_category_sql);
    $has_category = $check_category_result && $check_category_result->num_rows > 0;

    $check_session_sql = "SHOW COLUMNS FROM $table_name LIKE 'session_id'";
    $check_session_result = $conn->query($check_session_sql);
    $has_session = $check_session_result && $check_session_result->num_rows > 0;

    // Add category column if it doesn't exist
    if (!$has_category) {
        $add_category_sql = "ALTER TABLE $table_name ADD COLUMN category VARCHAR(50) DEFAULT 'required'";
        if ($conn->query($add_category_sql)) {
            $log_messages[] = "Added 'category' column to $table_name table";
        } else {
            $log_messages[] = "Failed to add 'category' column: " . $conn->error;
        }
    }

    // Add session_id column if it doesn't exist
    if (!$has_session) {
        $add_session_sql = "ALTER TABLE $table_name ADD COLUMN session_id INT DEFAULT 1";
        if ($conn->query($add_session_sql)) {
            $log_messages[] = "Added 'session_id' column to $table_name table";
        } else {
            $log_messages[] = "Failed to add 'session_id' column: " . $conn->error;
        }
    }
}

// Get current session
$session_id = 1; // Default session ID

$check_sessions_table = $conn->query("SHOW TABLES LIKE 'sessions'");
if ($check_sessions_table->num_rows == 0) {
    $log_messages[] = "সেশন টেবিল পাওয়া যায়নি। নতুন টেবিল তৈরি করা হচ্ছে...";

    // Create sessions table
    $create_table_sql = "CREATE TABLE `sessions` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `session_name` varchar(50) NOT NULL UNIQUE,
        `start_date` DATE NOT NULL,
        `end_date` DATE NOT NULL,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

    if ($conn->query($create_table_sql)) {
        $log_messages[] = "Created 'sessions' table successfully";

        // Add a default session
        $current_year = date('Y');
        $next_year = $current_year + 1;
        $insert_sql = "INSERT INTO `sessions` (`session_name`, `start_date`, `end_date`) VALUES ('$current_year-$next_year', '$current_year-01-01', '$next_year-12-31')";

        if ($conn->query($insert_sql)) {
            $log_messages[] = "Added default session to the table";
            $session_id = $conn->insert_id;
        } else {
            $log_messages[] = "Failed to add default session: " . $conn->error;
        }
    } else {
        $error_messages[] = "Failed to create 'sessions' table: " . $conn->error;
    }
} else {
    $session_sql = "SELECT id FROM sessions WHERE is_current = 1 LIMIT 1";
    $session_result = $conn->query($session_sql);

    if ($session_result && $session_result->num_rows > 0) {
        $session_id = $session_result->fetch_assoc()['id'];
    } else {
        $log_messages[] = "কোন সক্রিয় সেশন পাওয়া যায়নি। ডিফল্ট সেশন আইডি 1 ব্যবহার করা হবে।";

        // Add a default session if none exists
        $current_year = date('Y');
        $next_year = $current_year + 1;
        $insert_sql = "INSERT INTO `sessions` (`session_name`, `start_date`, `end_date`) VALUES ('$current_year-$next_year', '$current_year-01-01', '$next_year-12-31')";

        if ($conn->query($insert_sql)) {
            $log_messages[] = "Added default session to the table";
            $session_id = $conn->insert_id;
        }
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['assign_subject'])) {
        $subject_id = $_POST['subject_id'];
        $class_id = isset($_POST['class_id']) ? $_POST['class_id'] : 0;
        $department_id = isset($_POST['department_id']) ? $_POST['department_id'] : 0;

        $selected_subject = $subject_id;
        $selected_class = $class_id;
        $selected_department = $department_id;

        // Get subject details
        $subject_name = "Unknown Subject";
        if (!empty($subjects)) {
            foreach ($subjects as $subject) {
                if ($subject['id'] == $subject_id) {
                    $subject_name = $subject['subject_name'];
                    break;
                }
            }
        }

        // Check if students table exists
        $check_students_table = $conn->query("SHOW TABLES LIKE 'students'");
        if ($check_students_table->num_rows == 0) {
            $log_messages[] = "শিক্ষার্থী টেবিল পাওয়া যায়নি। নতুন টেবিল তৈরি করা হচ্ছে...";

            // Create students table
            $create_table_sql = "CREATE TABLE `students` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `first_name` varchar(255) NOT NULL,
                `last_name` varchar(255) DEFAULT NULL,
                `roll_number` varchar(50) DEFAULT NULL,
                `class_id` int(11) DEFAULT NULL,
                `department_id` int(11) DEFAULT NULL,
                `session_id` int(11) DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

            if ($conn->query($create_table_sql)) {
                $log_messages[] = "Created 'students' table successfully";
                $check_students_table = $conn->query("SHOW TABLES LIKE 'students'");

                // Add a sample student for testing
                $insert_sql = "INSERT INTO `students` (`first_name`, `last_name`, `roll_number`, `class_id`, `department_id`)
                    VALUES ('Sample', 'Student', '101', 1, 1)";

                if ($conn->query($insert_sql)) {
                    $log_messages[] = "Added a sample student to the table";
                } else {
                    $log_messages[] = "Failed to add sample student: " . $conn->error;
                }
            } else {
                $error_messages[] = "Failed to create 'students' table: " . $conn->error;
            }
        } else {
            // Get the structure of the students table
            $structure_sql = "DESCRIBE students";
            $structure_result = $conn->query($structure_sql);
            $columns = [];
            while ($row = $structure_result->fetch_assoc()) {
                $columns[] = $row['Field'];
            }

            // Check if required columns exist
            $has_class_id = in_array('class_id', $columns);
            $has_department_id = in_array('department_id', $columns);

            // Get students based on filters
            $students_sql = "SELECT id";

            // Check for name columns
            if (in_array('first_name', $columns) && in_array('last_name', $columns)) {
                $students_sql .= ", first_name, last_name";
            } elseif (in_array('name', $columns)) {
                $students_sql .= ", name as first_name, '' as last_name";
            } else {
                $students_sql .= ", 'Unknown' as first_name, 'Student' as last_name";
            }

            // Check for roll number column
            if (in_array('roll_number', $columns)) {
                $students_sql .= ", roll_number";
            } elseif (in_array('roll', $columns)) {
                $students_sql .= ", roll as roll_number";
            } else {
                $students_sql .= ", id as roll_number";
            }

            $students_sql .= " FROM students WHERE 1=1";
            $params = [];
            $types = "";

            if ($class_id > 0 && $has_class_id) {
                $students_sql .= " AND class_id = ?";
                $params[] = $class_id;
                $types .= "i";
            }

            if ($department_id > 0 && $has_department_id) {
                $students_sql .= " AND department_id = ?";
                $params[] = $department_id;
                $types .= "i";
            }

            $students_stmt = $conn->prepare($students_sql);

            if (!empty($params)) {
                $students_stmt->bind_param($types, ...$params);
            }

            $students_stmt->execute();
            $students_result = $students_stmt->get_result();

            if ($students_result->num_rows === 0) {
                $error_messages[] = "কোন শিক্ষার্থী পাওয়া যায়নি।";
            } else {
                // Start transaction
                $conn->begin_transaction();

                try {
                    // Get the student_subjects table name
                    $student_subjects_table = "";
                    $check_student_subjects_plural = $conn->query("SHOW TABLES LIKE 'student_subjects'");
                    if ($check_student_subjects_plural->num_rows > 0) {
                        $student_subjects_table = "student_subjects";
                    } else {
                        $check_student_subjects_singular = $conn->query("SHOW TABLES LIKE 'student_subject'");
                        if ($check_student_subjects_singular->num_rows > 0) {
                            $student_subjects_table = "student_subject";
                        } else {
                            $student_subjects_table = "student_subjects"; // Default to this if neither exists
                        }
                    }

                    $log_messages[] = "Using table: $student_subjects_table for inserting records";

                    // Insert each student into student_subjects
                    $insert_sql = "INSERT IGNORE INTO $student_subjects_table (student_id, subject_id, category, session_id) VALUES (?, ?, 'required', ?)";
                    $insert_stmt = $conn->prepare($insert_sql);

                    if (!$insert_stmt) {
                        $log_messages[] = "Error preparing insert statement: " . $conn->error;
                        $log_messages[] = "SQL: $insert_sql";

                        // Try without category and session_id
                        $insert_sql = "INSERT IGNORE INTO $student_subjects_table (student_id, subject_id) VALUES (?, ?)";
                        $insert_stmt = $conn->prepare($insert_sql);

                        if (!$insert_stmt) {
                            $log_messages[] = "Error preparing simplified insert statement: " . $conn->error;
                            throw new Exception("Could not prepare insert statement: " . $conn->error);
                        }
                    }

                    $assigned_count = 0;
                    $student_count = $students_result->num_rows;

                    while ($student = $students_result->fetch_assoc()) {
                        $student_id = $student['id'];

                        if (strpos($insert_sql, "category") !== false) {
                            // With category and session_id
                            $insert_stmt->bind_param("iii", $student_id, $subject_id, $session_id);
                        } else {
                            // Without category and session_id
                            $insert_stmt->bind_param("ii", $student_id, $subject_id);
                        }

                        if ($insert_stmt->execute()) {
                            if ($insert_stmt->affected_rows > 0) {
                                $assigned_count++;
                                $first_name = isset($student['first_name']) ? $student['first_name'] : '';
                                $last_name = isset($student['last_name']) ? $student['last_name'] : '';
                                $log_messages[] = "শিক্ষার্থী আইডি {$student['id']} ({$first_name} {$last_name}) - বিষয় বরাদ্দ করা হয়েছে";
                            } else {
                                $first_name = isset($student['first_name']) ? $student['first_name'] : '';
                                $last_name = isset($student['last_name']) ? $student['last_name'] : '';
                                $log_messages[] = "শিক্ষার্থী আইডি {$student['id']} ({$first_name} {$last_name}) - ইতিমধ্যে বিষয় বরাদ্দ করা আছে";
                            }
                        } else {
                            $log_messages[] = "শিক্ষার্থী আইডি {$student['id']} - সমস্যা: " . $insert_stmt->error;
                        }
                    }

                    // Commit transaction
                    $conn->commit();

                    if ($assigned_count > 0) {
                        $success_messages[] = "সফলভাবে $assigned_count জন শিক্ষার্থীকে '$subject_name' বিষয় বরাদ্দ করা হয়েছে।";
                    } else {
                        $success_messages[] = "সকল শিক্ষার্থীদের ইতিমধ্যে এই বিষয় বরাদ্দ করা আছে।";
                    }
                } catch (Exception $e) {
                    // Rollback on error
                    $conn->rollback();
                    $error_messages[] = "সমস্যা হয়েছে: " . $e->getMessage();
                }
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>বিষয় বরাদ্দ সমস্যা সমাধান - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-size: .875rem;
            background-color: #f8f9fa;
        }
        .main-content {
            margin-left: 240px;
            padding: 20px;
        }
        @media (max-width: 767.98px) {
            .main-content {
                margin-left: 0;
            }
        }
        .log-container {
            max-height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 20px;
        }
        .log-item {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .log-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <?php include('includes/header.php'); ?>

    <div class="container-fluid">
        <div class="row">
            <?php include('includes/sidebar.php'); ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">বিষয় বরাদ্দ সমস্যা সমাধান</h1>
                </div>

                <div class="alert alert-info mb-4">
                    <i class="fas fa-info-circle me-2"></i> এই পেজটি ব্যবহার করে আপনি নির্দিষ্ট বিষয়গুলি (যেমন Accounting, Chemistry ইত্যাদি) সকল শিক্ষার্থীদের জন্য বরাদ্দ করতে পারেন। এটি মার্কস এন্ট্রি সমস্যা সমাধান করবে।
                </div>

                <?php foreach ($error_messages as $message): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i> <?php echo $message; ?>
                </div>
                <?php endforeach; ?>

                <?php foreach ($success_messages as $message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i> <?php echo $message; ?>
                </div>
                <?php endforeach; ?>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5>বিষয় বরাদ্দ করুন</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="">
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="subject_id" class="form-label">বিষয় নির্বাচন করুন</label>
                                    <select class="form-select" id="subject_id" name="subject_id" required>
                                        <option value="">বিষয় নির্বাচন করুন</option>
                                        <?php foreach ($subjects as $subject): ?>
                                            <option value="<?php echo $subject['id']; ?>" <?php echo ($selected_subject == $subject['id']) ? 'selected' : ''; ?>>
                                                <?php echo $subject['subject_name'] . ' (' . $subject['subject_code'] . ')'; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="class_id" class="form-label">শ্রেণী নির্বাচন করুন (ঐচ্ছিক)</label>
                                    <select class="form-select" id="class_id" name="class_id">
                                        <option value="">সকল শ্রেণী</option>
                                        <?php foreach ($classes as $class): ?>
                                            <option value="<?php echo $class['id']; ?>" <?php echo ($selected_class == $class['id']) ? 'selected' : ''; ?>>
                                                <?php echo $class['class_name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="department_id" class="form-label">বিভাগ নির্বাচন করুন (ঐচ্ছিক)</label>
                                    <select class="form-select" id="department_id" name="department_id">
                                        <option value="">সকল বিভাগ</option>
                                        <?php foreach ($departments as $department): ?>
                                            <option value="<?php echo $department['id']; ?>" <?php echo ($selected_department == $department['id']) ? 'selected' : ''; ?>>
                                                <?php echo $department['department_name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="mb-3">
                                <button type="submit" name="assign_subject" class="btn btn-primary">
                                    <i class="fas fa-users me-1"></i> বিষয় বরাদ্দ করুন
                                </button>
                                <a href="marks_entry.php" class="btn btn-secondary ms-2">
                                    <i class="fas fa-arrow-left me-1"></i> মার্কস এন্ট্রি পেজে ফিরে যান
                                </a>
                            </div>
                        </form>

                        <?php if (!empty($log_messages)): ?>
                        <div class="log-container mt-4">
                            <h5>প্রক্রিয়াকরণের লগ:</h5>
                            <?php foreach ($log_messages as $log): ?>
                            <div class="log-item">
                                <?php echo $log; ?>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
