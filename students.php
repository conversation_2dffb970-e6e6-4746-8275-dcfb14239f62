<?php
session_start();
require_once 'includes/dbh.inc.php';

// No login required to view students

// Initialize variables
$searchResults = [];
$searchPerformed = false;
$errorMessage = '';
$successMessage = '';

// Get all departments for filter dropdown
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Get all classes for filter dropdown
$classesQuery = "SELECT * FROM classes ORDER BY class_name";
$classes = $conn->query($classesQuery);

// Get all subjects for filter dropdown
$subjectsQuery = "SELECT * FROM subjects ORDER BY subject_name";
$subjects = $conn->query($subjectsQuery);

// Process search form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $searchPerformed = true;
    $searchType = $_POST['search_type'] ?? '';
    $studentId = $_POST['student_id'] ?? '';
    $departmentId = $_POST['department_id'] ?? '';
    $classId = $_POST['class_id'] ?? '';
    $subjectId = $_POST['subject_id'] ?? '';

    // Base query for student information
    $baseQuery = "SELECT s.*,
                 d.department_name,
                 c.class_name,
                 ss.session_name
                 FROM students s
                 LEFT JOIN departments d ON s.department_id = d.id
                 LEFT JOIN classes c ON s.class_id = c.id
                 LEFT JOIN sessions ss ON s.session_id = ss.id
                 WHERE 1=1";

    $params = [];
    $types = "";

    // Add filters based on search type
    if (!empty($studentId)) {
        $baseQuery .= " AND s.student_id = ?";
        $params[] = $studentId;
        $types .= "s";
    }

    if (!empty($departmentId)) {
        $baseQuery .= " AND s.department_id = ?";
        $params[] = $departmentId;
        $types .= "i";
    }

    if (!empty($classId)) {
        $baseQuery .= " AND s.class_id = ?";
        $params[] = $classId;
        $types .= "i";
    }

    // Specific search based on search type
    switch ($searchType) {
        case 'basic':
            // Basic search already covered by base query
            break;

        case 'subjects':
            // Get session ID from form
            $sessionId = $_POST['session_id'] ?? '';

            // Query to get students with their subjects
            $baseQuery = "SELECT s.*,
                         d.department_name,
                         c.class_name,
                         ss.session_name,
                         GROUP_CONCAT(CONCAT(sub.subject_name, ' (', sub.subject_code, ')') SEPARATOR ', ') as subjects,
                         GROUP_CONCAT(ss2.category SEPARATOR ', ') as subject_categories
                         FROM students s
                         LEFT JOIN departments d ON s.department_id = d.id
                         LEFT JOIN classes c ON s.class_id = c.id
                         LEFT JOIN sessions ss ON s.session_id = ss.id
                         LEFT JOIN student_subjects ss2 ON s.id = ss2.student_id
                         LEFT JOIN subjects sub ON ss2.subject_id = sub.id
                         WHERE 1=1";

            $params = [];
            $types = "";

            // Add filters
            if (!empty($studentId)) {
                $baseQuery .= " AND s.student_id = ?";
                $params[] = $studentId;
                $types .= "s";
            }

            if (!empty($departmentId)) {
                $baseQuery .= " AND s.department_id = ?";
                $params[] = $departmentId;
                $types .= "i";
            }

            if (!empty($classId)) {
                $baseQuery .= " AND s.class_id = ?";
                $params[] = $classId;
                $types .= "i";
            }

            if (!empty($sessionId)) {
                $baseQuery .= " AND s.session_id = ?";
                $params[] = $sessionId;
                $types .= "i";
            }

            // Group by student to get all subjects per student
            $baseQuery .= " GROUP BY s.id";
            break;

        case 'exams':
            // Get session ID from form
            $sessionId = $_POST['session_id'] ?? '';

            // Student ID is required for exam results
            if (empty($studentId)) {
                $errorMessage = "শিক্ষার্থী আইডি প্রয়োজন। অনুগ্রহ করে শিক্ষার্থী আইডি দিন।";
                $searchPerformed = false;
                break;
            }

            $baseQuery = "SELECT s.*,
                         d.department_name,
                         c.class_name,
                         ss.session_name,
                         e.exam_name,
                         r.marks_obtained as marks,
                         r.grade
                         FROM students s
                         LEFT JOIN departments d ON s.department_id = d.id
                         LEFT JOIN classes c ON s.class_id = c.id
                         LEFT JOIN sessions ss ON s.session_id = ss.id
                         JOIN results r ON s.id = r.student_id
                         JOIN exams e ON r.exam_id = e.id
                         WHERE s.student_id = ?";

            $params = [$studentId];
            $types = "s";

            // Add filters
            if (!empty($departmentId)) {
                $baseQuery .= " AND s.department_id = ?";
                $params[] = $departmentId;
                $types .= "i";
            }

            if (!empty($classId)) {
                $baseQuery .= " AND s.class_id = ?";
                $params[] = $classId;
                $types .= "i";
            }

            if (!empty($sessionId)) {
                $baseQuery .= " AND s.session_id = ?";
                $params[] = $sessionId;
                $types .= "i";
            }

            // Subject filter removed as results table doesn't have subject_id

            // Order by exam date
            $baseQuery .= " ORDER BY e.exam_date DESC";
            break;

        case 'fees':
            $baseQuery = "SELECT s.*,
                         d.department_name,
                         c.class_name,
                         ss.session_name,
                         f.fee_type,
                         f.amount,
                         f.paid,
                         f.due_date,
                         f.payment_status
                         FROM students s
                         LEFT JOIN departments d ON s.department_id = d.id
                         LEFT JOIN classes c ON s.class_id = c.id
                         LEFT JOIN sessions ss ON s.session_id = ss.id
                         JOIN fees f ON s.id = f.student_id
                         WHERE 1=1";

            // Add filters
            if (!empty($studentId)) {
                $baseQuery .= " AND s.student_id = ?";
                $params[] = $studentId;
                $types .= "s";
            }

            if (!empty($departmentId)) {
                $baseQuery .= " AND s.department_id = ?";
                $params[] = $departmentId;
                $types .= "i";
            }

            if (!empty($classId)) {
                $baseQuery .= " AND s.class_id = ?";
                $params[] = $classId;
                $types .= "i";
            }
            break;
    }

    // Execute the query using a safer alternative approach
    if (!empty($params)) {
        // Use a different approach to avoid bind_param issues
        $finalQuery = $baseQuery;

        // Replace each ? with the actual value (properly escaped)
        foreach ($params as $param) {
            // Determine if it's a string or number
            if (is_numeric($param)) {
                // For numbers, no quotes needed
                $replacement = $param;
            } else {
                // For strings, escape and add quotes
                $replacement = "'" . $conn->real_escape_string($param) . "'";
            }

            // Replace the first ? with the value
            $pos = strpos($finalQuery, '?');
            if ($pos !== false) {
                $finalQuery = substr_replace($finalQuery, $replacement, $pos, 1);
            }
        }

        // Execute the final query
        $result = $conn->query($finalQuery);
    } else {
        // No parameters, just execute the query
        $result = $conn->query($baseQuery);
    }

    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $searchResults[] = $row;
        }
        $successMessage = "খুঁজে পাওয়া শিক্ষার্থীর সংখ্যা: " . count($searchResults);
    } else {
        $errorMessage = "কোন শিক্ষার্থী পাওয়া যায়নি। অনুগ্রহ করে অন্য ফিল্টার ব্যবহার করুন।";
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>শিক্ষার্থী অনুসন্ধান - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
        }
        .search-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: flex-end;
        }
        .search-container > div {
            min-width: 200px;
        }
        .search-options {
            margin-top: 20px;
        }
        .result-card {
            margin-bottom: 15px;
            border-radius: 10px;
            overflow: hidden;
        }
        .student-photo {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 50%;
        }
        .tab-content {
            padding: 20px 0;
        }
        .nav-tabs .nav-link {
            color: #495057;
        }
        .nav-tabs .nav-link.active {
            font-weight: bold;
            color: #0d6efd;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Include sidebar navigation -->
            <?php include 'includes/sidebar.php'; ?>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content py-4">
                <div class="row mb-4">
                    <div class="col">
                        <h2>শিক্ষার্থী অনুসন্ধান</h2>
                        <p class="text-muted">শিক্ষার্থীদের তথ্য খুঁজুন বিভিন্ন অপশন ব্যবহার করে</p>
                    </div>
                </div>

                <!-- Search Form -->
                <div class="card mb-4">
                    <div class="card-body">
                        <ul class="nav nav-tabs" id="searchTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab" aria-controls="basic" aria-selected="true">
                                    <i class="fas fa-search me-2"></i>সাধারণ অনুসন্ধান
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="subjects-tab" data-bs-toggle="tab" data-bs-target="#subjects" type="button" role="tab" aria-controls="subjects" aria-selected="false">
                                    <i class="fas fa-book me-2"></i>বিষয় অনুসন্ধান
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="exams-tab" data-bs-toggle="tab" data-bs-target="#exams" type="button" role="tab" aria-controls="exams" aria-selected="false">
                                    <i class="fas fa-file-alt me-2"></i>পরীক্ষার ফলাফল
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="fees-tab" data-bs-toggle="tab" data-bs-target="#fees" type="button" role="tab" aria-controls="fees" aria-selected="false">
                                    <i class="fas fa-money-bill-wave me-2"></i>বেতন তথ্য
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content" id="searchTabsContent">
                            <!-- Basic Search Tab -->
                            <div class="tab-pane fade show active" id="basic" role="tabpanel" aria-labelledby="basic-tab">
                                <form method="POST" action="">
                                    <input type="hidden" name="search_type" value="basic">
                                    <div class="search-container">
                                        <div class="flex-grow-1">
                                            <label for="student_id" class="form-label">শিক্ষার্থী আইডি</label>
                                            <input type="text" name="student_id" id="student_id" class="form-control" placeholder="শিক্ষার্থী আইডি দিন">
                                        </div>
                                        <div>
                                            <label for="department_id" class="form-label">বিভাগ</label>
                                            <select name="department_id" id="department_id" class="form-select">
                                                <option value="">সব বিভাগ</option>
                                                <?php if ($departments && $departments->num_rows > 0): ?>
                                                    <?php while ($dept = $departments->fetch_assoc()): ?>
                                                        <option value="<?php echo $dept['id']; ?>">
                                                            <?php echo $dept['department_name']; ?>
                                                        </option>
                                                    <?php endwhile; ?>
                                                <?php endif; ?>
                                            </select>
                                        </div>
                                        <div>
                                            <label for="class_id" class="form-label">ক্লাস</label>
                                            <select name="class_id" id="class_id" class="form-select">
                                                <option value="">সব ক্লাস</option>
                                                <?php if ($classes && $classes->num_rows > 0): ?>
                                                    <?php while ($class = $classes->fetch_assoc()): ?>
                                                        <option value="<?php echo $class['id']; ?>">
                                                            <?php echo $class['class_name']; ?>
                                                        </option>
                                                    <?php endwhile; ?>
                                                <?php endif; ?>
                                            </select>
                                        </div>
                                        <div>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search me-2"></i>খুঁজুন
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <!-- Subjects Search Tab -->
                            <div class="tab-pane fade" id="subjects" role="tabpanel" aria-labelledby="subjects-tab">
                                <form method="POST" action="">
                                    <input type="hidden" name="search_type" value="subjects">
                                    <div class="search-container">
                                        <div>
                                            <label for="student_id_subjects" class="form-label">শিক্ষার্থী আইডি</label>
                                            <input type="text" name="student_id" id="student_id_subjects" class="form-control" placeholder="শিক্ষার্থী আইডি দিন">
                                        </div>
                                        <div>
                                            <label for="department_id_subjects" class="form-label">বিভাগ</label>
                                            <select name="department_id" id="department_id_subjects" class="form-select">
                                                <option value="">সব বিভাগ</option>
                                                <?php
                                                // Reset the departments result pointer
                                                if ($departments) {
                                                    $departments->data_seek(0);
                                                    while ($dept = $departments->fetch_assoc()): ?>
                                                        <option value="<?php echo $dept['id']; ?>">
                                                            <?php echo $dept['department_name']; ?>
                                                        </option>
                                                    <?php endwhile;
                                                } ?>
                                            </select>
                                        </div>
                                        <div>
                                            <label for="class_id_subjects" class="form-label">ক্লাস</label>
                                            <select name="class_id" id="class_id_subjects" class="form-select">
                                                <option value="">সব ক্লাস</option>
                                                <?php
                                                // Reset the classes result pointer
                                                if ($classes) {
                                                    $classes->data_seek(0);
                                                    while ($class = $classes->fetch_assoc()): ?>
                                                        <option value="<?php echo $class['id']; ?>">
                                                            <?php echo $class['class_name']; ?>
                                                        </option>
                                                    <?php endwhile;
                                                } ?>
                                            </select>
                                        </div>
                                        <div>
                                            <?php
                                            // Get all sessions for filter dropdown
                                            $sessionsQuery = "SELECT * FROM sessions ORDER BY session_name";
                                            $sessions = $conn->query($sessionsQuery);
                                            ?>
                                            <label for="session_id_subjects" class="form-label">সেশন</label>
                                            <select name="session_id" id="session_id_subjects" class="form-select">
                                                <option value="">সব সেশন</option>
                                                <?php if ($sessions && $sessions->num_rows > 0): ?>
                                                    <?php while ($session = $sessions->fetch_assoc()): ?>
                                                        <option value="<?php echo $session['id']; ?>">
                                                            <?php echo $session['session_name']; ?>
                                                        </option>
                                                    <?php endwhile; ?>
                                                <?php endif; ?>
                                            </select>
                                        </div>
                                        <div>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search me-2"></i>খুঁজুন
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <!-- Exams Search Tab -->
                            <div class="tab-pane fade" id="exams" role="tabpanel" aria-labelledby="exams-tab">
                                <form method="POST" action="">
                                    <input type="hidden" name="search_type" value="exams">
                                    <div class="search-container">
                                        <div>
                                            <label for="student_id_exams" class="form-label">শিক্ষার্থী আইডি <span class="text-danger">*</span></label>
                                            <input type="text" name="student_id" id="student_id_exams" class="form-control" placeholder="শিক্ষার্থী আইডি দিন" required>
                                        </div>
                                        <div>
                                            <label for="department_id_exams" class="form-label">বিভাগ</label>
                                            <select name="department_id" id="department_id_exams" class="form-select">
                                                <option value="">সব বিভাগ</option>
                                                <?php
                                                // Reset the departments result pointer
                                                if ($departments) {
                                                    $departments->data_seek(0);
                                                    while ($dept = $departments->fetch_assoc()): ?>
                                                        <option value="<?php echo $dept['id']; ?>">
                                                            <?php echo $dept['department_name']; ?>
                                                        </option>
                                                    <?php endwhile;
                                                } ?>
                                            </select>
                                        </div>
                                        <div>
                                            <label for="class_id_exams" class="form-label">ক্লাস</label>
                                            <select name="class_id" id="class_id_exams" class="form-select">
                                                <option value="">সব ক্লাস</option>
                                                <?php
                                                // Reset the classes result pointer
                                                if ($classes) {
                                                    $classes->data_seek(0);
                                                    while ($class = $classes->fetch_assoc()): ?>
                                                        <option value="<?php echo $class['id']; ?>">
                                                            <?php echo $class['class_name']; ?>
                                                        </option>
                                                    <?php endwhile;
                                                } ?>
                                            </select>
                                        </div>
                                        <div>
                                            <?php
                                            // Get all sessions for filter dropdown
                                            $sessionsQuery = "SELECT * FROM sessions ORDER BY session_name";
                                            $sessions = $conn->query($sessionsQuery);
                                            ?>
                                            <label for="session_id_exams" class="form-label">সেশন</label>
                                            <select name="session_id" id="session_id_exams" class="form-select">
                                                <option value="">সব সেশন</option>
                                                <?php if ($sessions && $sessions->num_rows > 0): ?>
                                                    <?php while ($session = $sessions->fetch_assoc()): ?>
                                                        <option value="<?php echo $session['id']; ?>">
                                                            <?php echo $session['session_name']; ?>
                                                        </option>
                                                    <?php endwhile; ?>
                                                <?php endif; ?>
                                            </select>
                                        </div>

                                        <div>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search me-2"></i>খুঁজুন
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <!-- Fees Search Tab -->
                            <div class="tab-pane fade" id="fees" role="tabpanel" aria-labelledby="fees-tab">
                                <form method="POST" action="">
                                    <input type="hidden" name="search_type" value="fees">
                                    <div class="search-container">
                                        <div class="flex-grow-1">
                                            <label for="student_id_fees" class="form-label">শিক্ষার্থী আইডি</label>
                                            <input type="text" name="student_id" id="student_id_fees" class="form-control" placeholder="শিক্ষার্থী আইডি দিন">
                                        </div>
                                        <div>
                                            <label for="department_id_fees" class="form-label">বিভাগ</label>
                                            <select name="department_id" id="department_id_fees" class="form-select">
                                                <option value="">সব বিভাগ</option>
                                                <?php
                                                // Reset the departments result pointer
                                                if ($departments) {
                                                    $departments->data_seek(0);
                                                    while ($dept = $departments->fetch_assoc()): ?>
                                                        <option value="<?php echo $dept['id']; ?>">
                                                            <?php echo $dept['department_name']; ?>
                                                        </option>
                                                    <?php endwhile;
                                                } ?>
                                            </select>
                                        </div>
                                        <div>
                                            <label for="class_id_fees" class="form-label">ক্লাস</label>
                                            <select name="class_id" id="class_id_fees" class="form-select">
                                                <option value="">সব ক্লাস</option>
                                                <?php
                                                // Reset the classes result pointer
                                                if ($classes) {
                                                    $classes->data_seek(0);
                                                    while ($class = $classes->fetch_assoc()): ?>
                                                        <option value="<?php echo $class['id']; ?>">
                                                            <?php echo $class['class_name']; ?>
                                                        </option>
                                                    <?php endwhile;
                                                } ?>
                                            </select>
                                        </div>
                                        <div>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search me-2"></i>খুঁজুন
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search Results -->
                <?php if ($searchPerformed): ?>
                    <?php if (!empty($errorMessage)): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i><?php echo $errorMessage; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($successMessage)): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo $successMessage; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($searchResults)): ?>
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">অনুসন্ধান ফলাফল</h5>
                                    <div class="btn-group" role="group" aria-label="View Options">
                                        <button type="button" class="btn btn-light view-btn active" data-view="grid">
                                            <i class="fas fa-th-large"></i> গ্রীড
                                        </button>
                                        <button type="button" class="btn btn-light view-btn" data-view="list">
                                            <i class="fas fa-list"></i> লিস্ট
                                        </button>
                                        <button type="button" class="btn btn-light view-btn" data-view="table">
                                            <i class="fas fa-table"></i> টেবিল
                                        </button>
                                        <button type="button" class="btn btn-light view-btn" data-view="detail">
                                            <i class="fas fa-info-circle"></i> ডিটেল
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Grid View -->
                            <div class="card-body view-container" id="grid-view">
                                <div class="row">
                                    <?php foreach ($searchResults as $student): ?>
                                        <div class="col-md-4 mb-4">
                                            <div class="card h-100">
                                                <div class="card-header bg-light text-center">
                                                    <?php if (!empty($student['profile_photo'])): ?>
                                                        <img src="<?php echo $student['profile_photo']; ?>" alt="প্রোফাইল ছবি" class="student-photo my-2">
                                                    <?php else: ?>
                                                        <div class="student-photo bg-secondary text-white d-flex align-items-center justify-content-center mx-auto my-2">
                                                            <i class="fas fa-user fa-3x"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <h5 class="card-title mt-2"><?php echo $student['first_name'] . ' ' . $student['last_name']; ?></h5>
                                                </div>
                                                <div class="card-body">
                                                    <p class="card-text"><strong>শিক্ষার্থী আইডি:</strong> <?php echo $student['student_id']; ?></p>
                                                    <p class="card-text"><strong>ক্লাস:</strong> <?php echo $student['class_name'] ?? 'N/A'; ?></p>
                                                    <p class="card-text"><strong>বিভাগ:</strong> <?php echo $student['department_name'] ?? 'N/A'; ?></p>
                                                </div>
                                                <div class="card-footer bg-white text-center">
                                                    <a href="view_student.php?id=<?php echo $student['student_id']; ?>" class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye me-1"></i>বিস্তারিত দেখুন
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>

                            <!-- List View -->
                            <div class="card-body view-container" id="list-view" style="display: none;">
                                <?php foreach ($searchResults as $student): ?>
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <div class="row align-items-center">
                                                <div class="col-md-1 text-center">
                                                    <?php if (!empty($student['profile_photo'])): ?>
                                                        <img src="<?php echo $student['profile_photo']; ?>" alt="প্রোফাইল ছবি" class="rounded-circle" style="width: 50px; height: 50px; object-fit: cover;">
                                                    <?php else: ?>
                                                        <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                            <i class="fas fa-user"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="col-md-3">
                                                    <h6 class="mb-0"><?php echo $student['first_name'] . ' ' . $student['last_name']; ?></h6>
                                                    <small class="text-muted">আইডি: <?php echo $student['student_id']; ?></small>
                                                </div>
                                                <div class="col-md-2">
                                                    <p class="mb-0"><strong>ক্লাস:</strong> <?php echo $student['class_name'] ?? 'N/A'; ?></p>
                                                </div>
                                                <div class="col-md-2">
                                                    <p class="mb-0"><strong>বিভাগ:</strong> <?php echo $student['department_name'] ?? 'N/A'; ?></p>
                                                </div>
                                                <div class="col-md-2">
                                                    <p class="mb-0"><strong>সেশন:</strong> <?php echo $student['session_name'] ?? 'N/A'; ?></p>
                                                </div>
                                                <div class="col-md-2 text-end">
                                                    <a href="view_student.php?id=<?php echo $student['student_id']; ?>" class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye me-1"></i>বিস্তারিত
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <!-- Table View -->
                            <div class="card-body view-container" id="table-view" style="display: none;">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>ছবি</th>
                                                <th>নাম</th>
                                                <th>শিক্ষার্থী আইডি</th>
                                                <th>ক্লাস</th>
                                                <th>বিভাগ</th>
                                                <th>সেশন</th>
                                                <th>অ্যাকশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($searchResults as $student): ?>
                                                <tr>
                                                    <td>
                                                        <?php if (!empty($student['profile_photo'])): ?>
                                                            <img src="<?php echo $student['profile_photo']; ?>" alt="প্রোফাইল ছবি" class="rounded-circle" style="width: 40px; height: 40px; object-fit: cover;">
                                                        <?php else: ?>
                                                            <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                                <i class="fas fa-user"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?php echo $student['first_name'] . ' ' . $student['last_name']; ?></td>
                                                    <td><?php echo $student['student_id']; ?></td>
                                                    <td><?php echo $student['class_name'] ?? 'N/A'; ?></td>
                                                    <td><?php echo $student['department_name'] ?? 'N/A'; ?></td>
                                                    <td><?php echo $student['session_name'] ?? 'N/A'; ?></td>
                                                    <td>
                                                        <a href="view_student.php?id=<?php echo $student['student_id']; ?>" class="btn btn-sm btn-info">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Detail View -->
                            <div class="card-body view-container" id="detail-view" style="display: none;">
                                <?php foreach ($searchResults as $student): ?>
                                    <div class="card result-card mb-3">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-2 text-center">
                                                    <?php if (!empty($student['profile_photo'])): ?>
                                                        <img src="<?php echo $student['profile_photo']; ?>" alt="প্রোফাইল ছবি" class="student-photo">
                                                    <?php else: ?>
                                                        <div class="student-photo bg-secondary text-white d-flex align-items-center justify-content-center">
                                                            <i class="fas fa-user fa-3x"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="col-md-10">
                                                    <h4><?php echo $student['first_name'] . ' ' . $student['last_name']; ?></h4>
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <p><strong>শিক্ষার্থী আইডি:</strong> <?php echo $student['student_id']; ?></p>
                                                            <p><strong>বিভাগ:</strong> <?php echo $student['department_name'] ?? 'N/A'; ?></p>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <p><strong>ক্লাস:</strong> <?php echo $student['class_name'] ?? 'N/A'; ?></p>
                                                            <p><strong>সেশন:</strong> <?php echo $student['session_name'] ?? 'N/A'; ?></p>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <?php if (isset($student['subjects']) && !empty($student['subjects'])): ?>
                                                                <p><strong>বিষয়সমূহ:</strong></p>
                                                                <ul class="list-group mt-2">
                                                                    <?php
                                                                    $subjectsList = explode(', ', $student['subjects']);
                                                                    foreach($subjectsList as $index => $subjectItem): ?>
                                                                        <li class="list-group-item">
                                                                            <?php echo $subjectItem; ?>
                                                                        </li>
                                                                    <?php endforeach; ?>
                                                                </ul>
                                                            <?php endif; ?>

                                                            <?php if (isset($student['exam_name'])): ?>
                                                                <p><strong>পরীক্ষা:</strong> <?php echo $student['exam_name']; ?></p>

                                                                <p><strong>মার্কস:</strong> <?php echo $student['marks'] ?? 'N/A'; ?> (<?php echo $student['grade'] ?? 'N/A'; ?>)</p>
                                                            <?php endif; ?>

                                                            <?php if (isset($student['fee_type'])): ?>
                                                                <p><strong>ফি টাইপ:</strong> <?php echo $student['fee_type']; ?></p>
                                                                <p><strong>পরিমাণ:</strong> <?php echo $student['amount']; ?> টাকা</p>
                                                                <p><strong>পরিশোধিত:</strong> <?php echo $student['paid']; ?> টাকা</p>
                                                                <p><strong>স্ট্যাটাস:</strong>
                                                                    <?php
                                                                    $statusClass = '';
                                                                    $statusText = '';

                                                                    switch($student['payment_status']) {
                                                                        case 'paid':
                                                                            $statusClass = 'text-success';
                                                                            $statusText = 'পরিশোধিত';
                                                                            break;
                                                                        case 'partial':
                                                                            $statusClass = 'text-warning';
                                                                            $statusText = 'আংশিক পরিশোধিত';
                                                                            break;
                                                                        case 'due':
                                                                            $statusClass = 'text-danger';
                                                                            $statusText = 'বাকি';
                                                                            break;
                                                                    }
                                                                    ?>
                                                                    <span class="<?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                                                </p>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>

                                                    <div class="mt-3">
                                                        <a href="view_student.php?id=<?php echo $student['student_id']; ?>" class="btn btn-sm btn-info">
                                                            <i class="fas fa-eye me-1"></i>বিস্তারিত দেখুন
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Keep the active tab selected after form submission
        document.addEventListener('DOMContentLoaded', function() {
            <?php if ($searchPerformed && isset($_POST['search_type'])): ?>
                const searchType = '<?php echo $_POST['search_type']; ?>';
                const tabToActivate = document.getElementById(searchType + '-tab');
                const tabContent = document.getElementById(searchType);

                if (tabToActivate && tabContent) {
                    const tab = new bootstrap.Tab(tabToActivate);
                    tab.show();
                }
            <?php endif; ?>

            // View switching functionality
            const viewButtons = document.querySelectorAll('.view-btn');
            const viewContainers = document.querySelectorAll('.view-container');

            viewButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons
                    viewButtons.forEach(btn => btn.classList.remove('active'));

                    // Add active class to clicked button
                    this.classList.add('active');

                    // Get the view type from data attribute
                    const viewType = this.getAttribute('data-view');

                    // Hide all view containers
                    viewContainers.forEach(container => {
                        container.style.display = 'none';
                    });

                    // Show the selected view container
                    const selectedContainer = document.getElementById(viewType + '-view');
                    if (selectedContainer) {
                        selectedContainer.style.display = 'block';
                    }
                });
            });
        });
    </script>
</body>
</html>
