<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Check if fee ID is provided
if (!isset($_GET['fee_id'])) {
    $_SESSION['error'] = 'অবৈধ অনুরোধ!';
    header('Location: fees.php');
    exit();
}

$feeId = intval($_GET['fee_id']);
$errorMessage = isset($_GET['error_message']) ? $_GET['error_message'] : 'অজানা ত্রুটি';

// Get fee details
$feeQuery = "SELECT f.*, s.first_name, s.last_name, s.student_id as roll, c.class_name 
             FROM fees f
             JOIN students s ON f.student_id = s.id
             JOIN classes c ON s.class_id = c.id
             WHERE f.id = ?";
$stmt = $conn->prepare($feeQuery);
$stmt->bind_param('i', $feeId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    $_SESSION['error'] = 'ফি রেকর্ড খুঁজে পাওয়া যায়নি!';
    header('Location: fees.php');
    exit();
}

$fee = $result->fetch_assoc();

// Include header
include_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Include sidebar -->
        <?php include_once 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">বিকাশ পেমেন্ট ব্যর্থ</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="fees.php" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> ফি তালিকায় ফিরে যান
                    </a>
                </div>
            </div>
            
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i> দুঃখিত, আপনার পেমেন্ট ব্যর্থ হয়েছে!
            </div>
            
            <div class="row">
                <div class="col-md-8 mx-auto">
                    <div class="card">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">পেমেন্ট ব্যর্থতার বিবরণ</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <h6 class="text-muted">শিক্ষার্থী তথ্য</h6>
                                    <p><strong>নাম:</strong> <?= $fee['first_name'] . ' ' . $fee['last_name'] ?></p>
                                    <p><strong>রোল:</strong> <?= $fee['roll'] ?></p>
                                    <p><strong>শ্রেণী:</strong> <?= $fee['class_name'] ?></p>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-muted">ফি তথ্য</h6>
                                    <p><strong>ফি টাইপ:</strong> <?= $fee['fee_type'] ?></p>
                                    <p><strong>মোট পরিমাণ:</strong> ৳ <?= number_format($fee['amount'], 2) ?></p>
                                    <p><strong>বাকি পরিমাণ:</strong> ৳ <?= number_format($fee['amount'] - $fee['paid'], 2) ?></p>
                                </div>
                            </div>
                            
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-info-circle me-2"></i> ত্রুটির কারণ:</h6>
                                <p class="mb-0"><?= htmlspecialchars($errorMessage) ?></p>
                            </div>
                            
                            <div class="mt-4">
                                <h6>সম্ভাব্য সমাধান:</h6>
                                <ul>
                                    <li>আপনার বিকাশ অ্যাকাউন্টে পর্যাপ্ত ব্যালেন্স আছে কিনা তা নিশ্চিত করুন।</li>
                                    <li>আপনার বিকাশ অ্যাকাউন্টের ট্রানজেকশন লিমিট চেক করুন।</li>
                                    <li>আপনার ইন্টারনেট সংযোগ চেক করুন।</li>
                                    <li>কিছুক্ষণ পর আবার চেষ্টা করুন।</li>
                                    <li>সমস্যা অব্যাহত থাকলে প্রশাসকের সাথে যোগাযোগ করুন।</li>
                                </ul>
                            </div>
                        </div>
                        <div class="card-footer text-center">
                            <a href="bkash_payment.php?fee_id=<?= $feeId ?>" class="btn btn-primary">
                                <i class="fas fa-redo me-2"></i> আবার চেষ্টা করুন
                            </a>
                            <a href="fees.php" class="btn btn-secondary ms-2">
                                <i class="fas fa-list me-2"></i> ফি তালিকায় ফিরে যান
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<?php
// Include footer
include_once 'includes/footer.php';
?>
