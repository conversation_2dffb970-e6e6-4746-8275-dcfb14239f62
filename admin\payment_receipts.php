<?php
session_start();
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Initialize variables
$payments = [];
$paymentMethods = [];
$errorMessage = '';

try {
    // Search parameters
    $searchTerm = $_GET['search'] ?? '';
    $dateFrom = $_GET['date_from'] ?? '';
    $dateTo = $_GET['date_to'] ?? '';
    $paymentMethod = $_GET['payment_method'] ?? '';

    // Check if required tables exist
    $tablesExist = true;
    $requiredTables = ['fee_payments', 'fees', 'students'];

    foreach ($requiredTables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows == 0) {
            $tablesExist = false;
            $errorMessage = "Required table '$table' does not exist. Please run setup first.";
            break;
        }
    }

    if ($tablesExist) {
        // Build search query
        $whereConditions = [];
        $params = [];
        $types = '';

        if (!empty($searchTerm)) {
            $whereConditions[] = "(s.first_name LIKE ? OR s.last_name LIKE ? OR s.roll_no LIKE ? OR fp.receipt_no LIKE ? OR f.fee_type LIKE ?)";
            $searchParam = "%$searchTerm%";
            $params = array_merge($params, [$searchParam, $searchParam, $searchParam, $searchParam, $searchParam]);
            $types .= 'sssss';
        }

        if (!empty($dateFrom)) {
            $whereConditions[] = "fp.payment_date >= ?";
            $params[] = $dateFrom;
            $types .= 's';
        }

        if (!empty($dateTo)) {
            $whereConditions[] = "fp.payment_date <= ?";
            $params[] = $dateTo;
            $types .= 's';
        }

        if (!empty($paymentMethod)) {
            $whereConditions[] = "fp.payment_method = ?";
            $params[] = $paymentMethod;
            $types .= 's';
        }

        $whereClause = '';
        if (!empty($whereConditions)) {
            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        }

        // Get payments with student and fee details
        $query = "SELECT fp.*, f.fee_type, s.first_name, s.last_name, s.roll_no, s.student_id, c.class_name,
                  CONCAT(s.first_name, ' ', s.last_name) as full_name
                  FROM fee_payments fp
                  JOIN fees f ON fp.fee_id = f.id
                  JOIN students s ON f.student_id = s.id
                  LEFT JOIN classes c ON s.class_id = c.id
                  $whereClause
                  ORDER BY fp.created_at DESC
                  LIMIT 50";

        $stmt = $conn->prepare($query);
        if ($stmt) {
            if (!empty($params)) {
                $stmt->bind_param($types, ...$params);
            }
            $stmt->execute();
            $result = $stmt->get_result();
            if ($result) {
                $payments = $result->fetch_all(MYSQLI_ASSOC);
            }
        }

        // Get payment methods for filter
        $methodsQuery = "SELECT DISTINCT payment_method FROM fee_payments WHERE payment_method IS NOT NULL ORDER BY payment_method";
        $methodsResult = $conn->query($methodsQuery);
        if ($methodsResult) {
            $paymentMethods = $methodsResult->fetch_all(MYSQLI_ASSOC);
        }
    }
} catch (Exception $e) {
    $errorMessage = "Database error: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পেমেন্ট রিসিপ্ট ম্যানেজমেন্ট</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', Arial, sans-serif;
            background-color: #f8f9fa;
        }
        .payment-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .payment-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        .payment-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 15px 20px;
            border-radius: 10px 10px 0 0;
        }
        .payment-body {
            padding: 20px;
        }
        .receipt-badge {
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .amount-display {
            font-size: 24px;
            font-weight: bold;
            color: #28a745;
        }
        .search-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .student-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        .payment-method-badge {
            background: #17a2b8;
            color: white;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 11px;
        }
        .no-payments {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }
        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            margin-bottom: 20px;
        }
        .stats-number {
            font-size: 28px;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-receipt me-2"></i>পেমেন্ট রিসিপ্ট ম্যানেজমেন্ট</h2>
                    <div>
                        <a href="setup_sample_data.php" class="btn btn-info me-2">
                            <i class="fas fa-database me-2"></i>সেটআপ
                        </a>
                        <a href="fee_management.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>ফিরে যান
                        </a>
                    </div>
                </div>

                <?php if (!empty($errorMessage)): ?>
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>সমস্যা!</strong> <?= htmlspecialchars($errorMessage) ?>
                    <br><small>সমাধান: <a href="setup_sample_data.php" class="alert-link">সেটআপ চালান</a></small>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <!-- Search Section -->
                <div class="search-section">
                    <h5 class="mb-3"><i class="fas fa-search me-2"></i>পেমেন্ট খুঁজুন</h5>
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">নাম/রোল/রিসিপ্ট নং</label>
                            <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($searchTerm) ?>" placeholder="খুঁজুন...">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">শুরুর তারিখ</label>
                            <input type="date" class="form-control" name="date_from" value="<?= htmlspecialchars($dateFrom) ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">শেষ তারিখ</label>
                            <input type="date" class="form-control" name="date_to" value="<?= htmlspecialchars($dateTo) ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">পেমেন্ট মাধ্যম</label>
                            <select class="form-control" name="payment_method">
                                <option value="">সব</option>
                                <?php foreach ($paymentMethods as $method): ?>
                                <option value="<?= htmlspecialchars($method['payment_method']) ?>" <?= $paymentMethod === $method['payment_method'] ? 'selected' : '' ?>>
                                    <?= $method['payment_method'] === 'cash' ? 'নগদ' : htmlspecialchars($method['payment_method']) ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>খুঁজুন
                                </button>
                                <a href="payment_receipts.php" class="btn btn-outline-secondary ms-1">
                                    <i class="fas fa-refresh"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Stats -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number"><?= count($payments) ?></div>
                            <div class="text-muted">পেমেন্ট পাওয়া গেছে</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number">৳<?= number_format(array_sum(array_column($payments, 'amount')), 2) ?></div>
                            <div class="text-muted">মোট পরিমাণ</div>
                        </div>
                    </div>
                </div>

                <!-- Payment Cards -->
                <div class="row">
                    <?php if (empty($payments)): ?>
                    <div class="col-12">
                        <div class="no-payments">
                            <i class="fas fa-receipt fa-3x mb-3 text-muted"></i>
                            <h4>কোন পেমেন্ট পাওয়া যায়নি</h4>
                            <p>অনুসন্ধানের শর্ত পরিবর্তন করে আবার চেষ্টা করুন</p>
                        </div>
                    </div>
                    <?php else: ?>
                    <?php foreach ($payments as $payment): ?>
                    <div class="col-md-6 col-lg-4">
                        <div class="payment-card">
                            <div class="payment-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <span class="receipt-badge"><?= htmlspecialchars($payment['receipt_no'] ?? 'N/A') ?></span>
                                    </div>
                                    <div class="text-end">
                                        <small><?= date('d/m/Y', strtotime($payment['payment_date'])) ?></small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="payment-body">
                                <div class="student-info">
                                    <h6 class="mb-2">
                                        <i class="fas fa-user me-2"></i><?= htmlspecialchars($payment['full_name']) ?>
                                    </h6>
                                    <div class="row">
                                        <div class="col-6">
                                            <small class="text-muted">রোল নং:</small><br>
                                            <strong><?= htmlspecialchars($payment['roll_no'] ?? 'N/A') ?></strong>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">ক্লাস:</small><br>
                                            <strong><?= htmlspecialchars($payment['class_name'] ?? 'N/A') ?></strong>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <small class="text-muted">ফি ধরন:</small><br>
                                    <strong><?= htmlspecialchars($payment['fee_type']) ?></strong>
                                </div>
                                
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div class="amount-display">৳<?= number_format($payment['amount'], 2) ?></div>
                                    <span class="payment-method-badge">
                                        <?= $payment['payment_method'] === 'cash' ? 'নগদ' : htmlspecialchars($payment['payment_method']) ?>
                                    </span>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <a href="receipt_view.php?receipt_no=<?= urlencode($payment['receipt_no']) ?>"
                                       target="_blank" class="btn btn-success">
                                        <i class="fas fa-receipt me-2"></i>রিসিপ্ট দেখুন
                                    </a>
                                    <a href="receipt_final.php?receipt_no=<?= urlencode($payment['receipt_no']) ?>"
                                       target="_blank" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-print me-2"></i>প্রিন্ট ভার্সন
                                    </a>
                                </div>
                                
                                <?php if (!empty($payment['notes'])): ?>
                                <div class="mt-2">
                                    <small class="text-muted">নোট:</small><br>
                                    <small><?= htmlspecialchars($payment['notes']) ?></small>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
