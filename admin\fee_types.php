<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Check if amount column exists in fee_types table
$checkColumnQuery = "SHOW COLUMNS FROM fee_types LIKE 'amount'";
$columnResult = $conn->query($checkColumnQuery);

if ($columnResult->num_rows === 0) {
    // Add amount column to fee_types table
    $alterTableQuery = "ALTER TABLE fee_types ADD COLUMN amount DECIMAL(10,2) DEFAULT 0.00 AFTER description";
    $conn->query($alterTableQuery);
}

// Process fee type addition
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_fee_type'])) {
    $name = $_POST['name'] ?? '';
    $description = $_POST['description'] ?? '';
    $amount = isset($_POST['amount']) ? floatval($_POST['amount']) : 0;
    $isRecurring = isset($_POST['is_recurring']) ? 1 : 0;

    if (!empty($name)) {
        // Check if fee type already exists
        $checkQuery = "SELECT id FROM fee_types WHERE name = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param('s', $name);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $_SESSION['error'] = 'এই ফি টাইপ ইতিমধ্যে বিদ্যমান!';
        } else {
            // Create new fee type
            $insertQuery = "INSERT INTO fee_types (name, description, amount, is_recurring) VALUES (?, ?, ?, ?)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param('ssdi', $name, $description, $amount, $isRecurring);

            if ($stmt->execute()) {
                $_SESSION['success'] = 'ফি টাইপ সফলভাবে যোগ করা হয়েছে!';
            } else {
                $_SESSION['error'] = 'ফি টাইপ যোগ করতে সমস্যা: ' . $conn->error;
            }
        }
    } else {
        $_SESSION['error'] = 'ফি টাইপের নাম প্রদান করুন!';
    }
}

// Process fee type update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_fee_type'])) {
    $id = $_POST['id'] ?? 0;
    $name = $_POST['name'] ?? '';
    $description = $_POST['description'] ?? '';
    $amount = isset($_POST['amount']) ? floatval($_POST['amount']) : 0;
    $isRecurring = isset($_POST['is_recurring']) ? 1 : 0;

    if ($id > 0 && !empty($name)) {
        // Check if fee type name already exists for other fee types
        $checkQuery = "SELECT id FROM fee_types WHERE name = ? AND id != ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param('si', $name, $id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $_SESSION['error'] = 'এই ফি টাইপ নাম ইতিমধ্যে অন্য একটি ফি টাইপে ব্যবহৃত হয়েছে!';
        } else {
            // Update fee type
            $updateQuery = "UPDATE fee_types SET name = ?, description = ?, amount = ?, is_recurring = ? WHERE id = ?";
            $stmt = $conn->prepare($updateQuery);
            $stmt->bind_param('ssdii', $name, $description, $amount, $isRecurring, $id);

            if ($stmt->execute()) {
                $_SESSION['success'] = 'ফি টাইপ সফলভাবে আপডেট করা হয়েছে!';
            } else {
                $_SESSION['error'] = 'ফি টাইপ আপডেট করতে সমস্যা: ' . $conn->error;
            }
        }
    } else {
        $_SESSION['error'] = 'অবৈধ ফি টাইপ আইডি বা নাম!';
    }
}

// Process fee type deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_fee_type'])) {
    $id = $_POST['id'] ?? 0;

    if ($id > 0) {
        // Check if fee type is used in fees
        $checkQuery = "SELECT COUNT(*) as count FROM fees WHERE fee_type = (SELECT name FROM fee_types WHERE id = ?)";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param('i', $id);
        $stmt->execute();
        $result = $stmt->get_result();
        $count = $result->fetch_assoc()['count'];

        if ($count > 0) {
            $_SESSION['error'] = 'এই ফি টাইপ ইতিমধ্যে ব্যবহৃত হচ্ছে! আগে সম্পর্কিত ফি রেকর্ড মুছুন।';
        } else {
            // Delete fee type
            $deleteQuery = "DELETE FROM fee_types WHERE id = ?";
            $stmt = $conn->prepare($deleteQuery);
            $stmt->bind_param('i', $id);

            if ($stmt->execute()) {
                $_SESSION['success'] = 'ফি টাইপ সফলভাবে মুছে ফেলা হয়েছে!';
            } else {
                $_SESSION['error'] = 'ফি টাইপ মুছতে সমস্যা: ' . $conn->error;
            }
        }
    } else {
        $_SESSION['error'] = 'অবৈধ ফি টাইপ আইডি!';
    }
}

// Get all fee types
$feeTypesQuery = "SELECT * FROM fee_types ORDER BY name";
$feeTypesResult = $conn->query($feeTypesQuery);

// Include header
include_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Include sidebar -->
        <?php include_once 'includes/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-tags me-2"></i> ফি টাইপ ম্যানেজমেন্ট</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="fee_management.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i> ফি ম্যানেজমেন্ট
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addFeeTypeModal">
                            <i class="fas fa-plus-circle me-1"></i> নতুন ফি টাইপ
                        </button>
                    </div>
                </div>
            </div>

            <!-- Display success/error messages -->
            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $_SESSION['success'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $_SESSION['error'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>

            <!-- Fee Types Table -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i> ফি টাইপ তালিকা</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>আইডি</th>
                                    <th>নাম</th>
                                    <th>বিবরণ</th>
                                    <th>পরিমাণ (৳)</th>
                                    <th>পুনরাবৃত্তি</th>
                                    <th>তৈরির তারিখ</th>
                                    <th>পদক্ষেপ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($feeTypesResult && $feeTypesResult->num_rows > 0): ?>
                                    <?php while ($feeType = $feeTypesResult->fetch_assoc()): ?>
                                        <tr>
                                            <td><?= $feeType['id'] ?></td>
                                            <td><?= htmlspecialchars($feeType['name']) ?></td>
                                            <td><?= htmlspecialchars($feeType['description'] ?: '-') ?></td>
                                            <td>৳ <?= number_format($feeType['amount'] ?? 0, 2) ?></td>
                                            <td>
                                                <?php if ($feeType['is_recurring']): ?>
                                                    <span class="badge bg-success">হ্যাঁ</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">না</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= date('d/m/Y', strtotime($feeType['created_at'])) ?></td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-primary edit-fee-type-btn"
                                                        data-id="<?= $feeType['id'] ?>"
                                                        data-name="<?= htmlspecialchars($feeType['name']) ?>"
                                                        data-description="<?= htmlspecialchars($feeType['description']) ?>"
                                                        data-amount="<?= $feeType['amount'] ?? 0 ?>"
                                                        data-is-recurring="<?= $feeType['is_recurring'] ?>"
                                                        data-bs-toggle="modal" data-bs-target="#editFeeTypeModal">
                                                    <i class="fas fa-edit"></i> এডিট
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger delete-fee-type-btn"
                                                        data-id="<?= $feeType['id'] ?>"
                                                        data-name="<?= htmlspecialchars($feeType['name']) ?>"
                                                        data-bs-toggle="modal" data-bs-target="#deleteFeeTypeModal">
                                                    <i class="fas fa-trash"></i> মুছুন
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="6" class="text-center">কোন ফি টাইপ পাওয়া যায়নি</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Add Fee Type Modal -->
<div class="modal fade" id="addFeeTypeModal" tabindex="-1" aria-labelledby="addFeeTypeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addFeeTypeModalLabel"><i class="fas fa-plus-circle me-2"></i> নতুন ফি টাইপ যোগ করুন</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">নাম</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">বিবরণ</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="amount" class="form-label">পরিমাণ (৳)</label>
                        <input type="number" class="form-control" id="amount" name="amount" min="0" step="0.01" value="0">
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_recurring" name="is_recurring">
                        <label class="form-check-label" for="is_recurring">পুনরাবৃত্তিমূলক (মাসিক/বার্ষিক)</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="submit" name="add_fee_type" class="btn btn-primary">যোগ করুন</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Fee Type Modal -->
<div class="modal fade" id="editFeeTypeModal" tabindex="-1" aria-labelledby="editFeeTypeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editFeeTypeModalLabel"><i class="fas fa-edit me-2"></i> ফি টাইপ এডিট করুন</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <input type="hidden" id="edit_id" name="id">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">নাম</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">বিবরণ</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="edit_amount" class="form-label">পরিমাণ (৳)</label>
                        <input type="number" class="form-control" id="edit_amount" name="amount" min="0" step="0.01" value="0">
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="edit_is_recurring" name="is_recurring">
                        <label class="form-check-label" for="edit_is_recurring">পুনরাবৃত্তিমূলক (মাসিক/বার্ষিক)</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="submit" name="update_fee_type" class="btn btn-primary">আপডেট করুন</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Fee Type Modal -->
<div class="modal fade" id="deleteFeeTypeModal" tabindex="-1" aria-labelledby="deleteFeeTypeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteFeeTypeModalLabel"><i class="fas fa-trash me-2"></i> ফি টাইপ মুছুন</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <input type="hidden" id="delete_id" name="id">
                    <p>আপনি কি নিশ্চিত যে আপনি এই ফি টাইপ মুছতে চান?</p>
                    <p><strong>ফি টাইপ:</strong> <span id="delete_name"></span></p>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i> সতর্কতা: এই ফি টাইপ মুছে ফেললে এটি আর পুনরুদ্ধার করা যাবে না।
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="submit" name="delete_fee_type" class="btn btn-danger">মুছুন</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // Handle edit button click
        const editButtons = document.querySelectorAll('.edit-fee-type-btn');
        editButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');
                const description = this.getAttribute('data-description');
                const amount = this.getAttribute('data-amount');
                const isRecurring = this.getAttribute('data-is-recurring') === '1';

                document.getElementById('edit_id').value = id;
                document.getElementById('edit_name').value = name;
                document.getElementById('edit_description').value = description;
                document.getElementById('edit_amount').value = amount;
                document.getElementById('edit_is_recurring').checked = isRecurring;
            });
        });

        // Handle delete button click
        const deleteButtons = document.querySelectorAll('.delete-fee-type-btn');
        deleteButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');

                document.getElementById('delete_id').value = id;
                document.getElementById('delete_name').textContent = name;
            });
        });
    });
</script>

<?php
// Include footer
include_once 'includes/footer.php';
?>
