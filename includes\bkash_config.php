<?php
/**
 * bKash Payment Gateway Configuration
 * 
 * This file contains the configuration settings for the bKash payment gateway integration.
 * You need to obtain these credentials from b<PERSON>ash when you register as a merchant.
 */

// bKash API Configuration
define('BKASH_SANDBOX', true); // Set to false for production
define('BKASH_VERSION', 'v1.2.0-beta');
define('BKASH_APP_KEY', 'your_app_key'); // Replace with your actual app key
define('BKASH_APP_SECRET', 'your_app_secret'); // Replace with your actual app secret
define('BKASH_USERNAME', 'your_username'); // Replace with your actual username
define('BKASH_PASSWORD', 'your_password'); // Replace with your actual password

// bKash Script URLs
define('BKASH_SCRIPT_URL_SANDBOX', 'https://scripts.sandbox.bka.sh/versions/1.2.0-beta/checkout/bKash-checkout-sandbox.js');
define('BKASH_SCRIPT_URL_PRODUCTION', 'https://scripts.pay.bka.sh/versions/1.2.0-beta/checkout/bKash-checkout.js');

// bKash API Endpoints
define('BKASH_GRANT_TOKEN_URL', 'https://checkout.sandbox.bka.sh/v1.2.0-beta/checkout/token/grant');
define('BKASH_CREATE_PAYMENT_URL', 'https://checkout.sandbox.bka.sh/v1.2.0-beta/checkout/payment/create');
define('BKASH_EXECUTE_PAYMENT_URL', 'https://checkout.sandbox.bka.sh/v1.2.0-beta/checkout/payment/execute');
define('BKASH_QUERY_PAYMENT_URL', 'https://checkout.sandbox.bka.sh/v1.2.0-beta/checkout/payment/query');

// Callback URLs
define('BKASH_SUCCESS_URL', 'http://localhost/zfaw/admin/bkash_success.php');
define('BKASH_FAILED_URL', 'http://localhost/zfaw/admin/bkash_failed.php');

/**
 * Function to get the appropriate bKash script URL based on environment
 */
function getBkashScriptUrl() {
    return BKASH_SANDBOX ? BKASH_SCRIPT_URL_SANDBOX : BKASH_SCRIPT_URL_PRODUCTION;
}

/**
 * Function to get bKash API headers with authorization token
 */
function getBkashHeaders($token = null) {
    $headers = [
        'Content-Type: application/json',
        'Accept: application/json',
        'username: ' . BKASH_USERNAME,
        'password: ' . BKASH_PASSWORD
    ];
    
    if ($token) {
        $headers[] = 'authorization: ' . $token;
        $headers[] = 'x-app-key: ' . BKASH_APP_KEY;
    }
    
    return $headers;
}
