<?php
// Include database connection
require_once 'includes/dbh.inc.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Direct Database Insert</h1>";

// Get student information
$studentId = 'STD-601523';
$studentQuery = "SELECT s.*, d.department_name 
                FROM students s 
                LEFT JOIN departments d ON s.department_id = d.id 
                WHERE s.student_id = '$studentId'";
$studentResult = $conn->query($studentQuery);

if ($studentResult && $studentResult->num_rows > 0) {
    $student = $studentResult->fetch_assoc();
    $studentDbId = $student['id'];
    
    echo "<p>Student ID: {$student['student_id']}</p>";
    echo "<p>Student Database ID: {$studentDbId}</p>";
    echo "<p>Name: {$student['first_name']} {$student['last_name']}</p>";
    echo "<p>Department: {$student['department_name']} (ID: {$student['department_id']})</p>";
    
    // Get current session
    $currentSessionQuery = "SELECT id, session_name FROM sessions ORDER BY id DESC LIMIT 1";
    $currentSessionResult = $conn->query($currentSessionQuery);
    
    if ($currentSessionResult && $currentSessionResult->num_rows > 0) {
        $currentSession = $currentSessionResult->fetch_assoc();
        echo "<p>Current Session: {$currentSession['session_name']} (ID: {$currentSession['id']})</p>";
        
        // Drop and recreate student_subjects table
        echo "<h2>Recreating student_subjects Table</h2>";
        
        $dropTableQuery = "DROP TABLE IF EXISTS student_subjects";
        if ($conn->query($dropTableQuery)) {
            echo "<p>Dropped existing student_subjects table.</p>";
        } else {
            echo "<p>Error dropping table: " . $conn->error . "</p>";
            exit;
        }
        
        $createTableQuery = "CREATE TABLE student_subjects (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            student_id INT(11) NOT NULL,
            subject_id INT(11) NOT NULL,
            category VARCHAR(20) NOT NULL DEFAULT 'optional',
            session_id INT(11) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        
        if ($conn->query($createTableQuery)) {
            echo "<p>Created student_subjects table.</p>";
        } else {
            echo "<p>Error creating table: " . $conn->error . "</p>";
            exit;
        }
        
        // Get some subjects for testing
        $subjectsQuery = "SELECT id FROM subjects LIMIT 3";
        $subjectsResult = $conn->query($subjectsQuery);
        
        if ($subjectsResult && $subjectsResult->num_rows > 0) {
            $subjects = [];
            while ($subject = $subjectsResult->fetch_assoc()) {
                $subjects[] = $subject['id'];
            }
            
            echo "<p>Found " . count($subjects) . " subjects for testing.</p>";
            
            // Insert test subjects
            echo "<h2>Inserting Test Subjects</h2>";
            
            $categories = ['required', 'optional', 'fourth'];
            $success = true;
            
            foreach ($subjects as $index => $subjectId) {
                $category = $categories[$index % 3];
                $insertQuery = "INSERT INTO student_subjects (student_id, subject_id, category, session_id) 
                               VALUES ($studentDbId, $subjectId, '$category', {$currentSession['id']})";
                
                if ($conn->query($insertQuery)) {
                    echo "<p>Inserted subject ID {$subjectId} as {$category}.</p>";
                } else {
                    echo "<p>Error inserting subject ID {$subjectId}: " . $conn->error . "</p>";
                    $success = false;
                }
            }
            
            if ($success) {
                echo "<h2>All Subjects Inserted Successfully!</h2>";
                
                // Check the inserted data
                $checkQuery = "SELECT ss.*, s.subject_name, s.subject_code 
                              FROM student_subjects ss 
                              JOIN subjects s ON ss.subject_id = s.id 
                              WHERE ss.student_id = $studentDbId";
                $checkResult = $conn->query($checkQuery);
                
                if ($checkResult && $checkResult->num_rows > 0) {
                    echo "<h3>Inserted Subjects:</h3>";
                    echo "<table border='1' cellpadding='5'>";
                    echo "<tr><th>ID</th><th>Subject ID</th><th>Subject Name</th><th>Subject Code</th><th>Category</th><th>Session ID</th></tr>";
                    
                    while ($subject = $checkResult->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>{$subject['id']}</td>";
                        echo "<td>{$subject['subject_id']}</td>";
                        echo "<td>{$subject['subject_name']}</td>";
                        echo "<td>{$subject['subject_code']}</td>";
                        echo "<td>{$subject['category']}</td>";
                        echo "<td>{$subject['session_id']}</td>";
                        echo "</tr>";
                    }
                    
                    echo "</table>";
                } else {
                    echo "<p>No subjects found after insertion.</p>";
                }
            } else {
                echo "<h2>Some Subjects Failed to Insert</h2>";
            }
        } else {
            echo "<p>No subjects found for testing.</p>";
        }
    } else {
        echo "<p>No active session found.</p>";
    }
} else {
    echo "<p>Student not found with ID: $studentId</p>";
}

echo "<h2>Test Complete</h2>";
echo "<p><a href='admin/student_subject_selection.php?id=STD-601523'>Go to Student Subject Selection</a></p>";
?>
