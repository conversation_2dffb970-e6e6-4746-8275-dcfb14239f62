<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Initialize messages
$successMessage = '';
$errorMessage = '';

// Create subject_categories table if it doesn't exist
$tableQuery = "CREATE TABLE IF NOT EXISTS subject_categories (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    category_name VARCHAR(100) NOT NULL,
    description TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY(category_name)
)";

if (!$conn->query($tableQuery)) {
    $errorMessage = "টেবিল তৈরি করতে সমস্যা হয়েছে: " . $conn->error;
}

// Check if default categories exist, if not add them
$checkCategoriesQuery = "SELECT COUNT(*) as count FROM subject_categories";
$result = $conn->query($checkCategoriesQuery);
$categoryCount = $result->fetch_assoc()['count'];

if ($categoryCount == 0) {
    // Add default categories
    $defaultCategories = [
        ['required', 'আবশ্যিক বিষয়সমূহ'],
        ['optional', 'ঐচ্ছিক বিষয়সমূহ'],
        ['fourth', '৪র্থ বিষয়']
    ];
    
    $insertQuery = "INSERT INTO subject_categories (category_name, description) VALUES (?, ?)";
    $stmt = $conn->prepare($insertQuery);
    
    foreach ($defaultCategories as $category) {
        $stmt->bind_param("ss", $category[0], $category[1]);
        $stmt->execute();
    }
    
    $successMessage = "ডিফল্ট ক্যাটাগরি সফলভাবে যোগ করা হয়েছে।";
}

// Handle add category
if (isset($_POST['add_category'])) {
    $category_name = $_POST['category_name'];
    $description = $_POST['description'] ?? '';
    
    // Validate input
    if (empty($category_name)) {
        $errorMessage = "ক্যাটাগরির নাম অবশ্যই পূরণ করতে হবে!";
    } else {
        // Check if category already exists
        $checkQuery = "SELECT * FROM subject_categories WHERE category_name = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param("s", $category_name);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $errorMessage = "এই ক্যাটাগরি ইতিমধ্যে বিদ্যমান!";
        } else {
            // Insert new category
            $insertQuery = "INSERT INTO subject_categories (category_name, description) VALUES (?, ?)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param("ss", $category_name, $description);
            
            if ($stmt->execute()) {
                $successMessage = "নতুন ক্যাটাগরি সফলভাবে যোগ করা হয়েছে!";
            } else {
                $errorMessage = "ক্যাটাগরি যোগ করতে সমস্যা হয়েছে: " . $conn->error;
            }
        }
    }
}

// Handle update category
if (isset($_POST['update_category'])) {
    $category_id = $_POST['category_id'];
    $category_name = $_POST['category_name'];
    $description = $_POST['description'] ?? '';
    
    // Validate input
    if (empty($category_name) || empty($category_id)) {
        $errorMessage = "ক্যাটাগরির নাম এবং আইডি অবশ্যই পূরণ করতে হবে!";
    } else {
        // Check if category already exists with this name (excluding current category)
        $checkQuery = "SELECT * FROM subject_categories WHERE category_name = ? AND id != ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param("si", $category_name, $category_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $errorMessage = "এই ক্যাটাগরি নাম ইতিমধ্যে অন্য ক্যাটাগরির জন্য ব্যবহৃত হয়েছে!";
        } else {
            // Update category
            $updateQuery = "UPDATE subject_categories SET category_name = ?, description = ? WHERE id = ?";
            $stmt = $conn->prepare($updateQuery);
            $stmt->bind_param("ssi", $category_name, $description, $category_id);
            
            if ($stmt->execute()) {
                $successMessage = "ক্যাটাগরি সফলভাবে আপডেট করা হয়েছে!";
            } else {
                $errorMessage = "ক্যাটাগরি আপডেট করতে সমস্যা হয়েছে: " . $conn->error;
            }
        }
    }
}

// Handle delete category
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $category_id = $_GET['delete'];
    
    // Check if category is in use
    $checkUsageQuery = "SELECT COUNT(*) as count FROM subjects WHERE category = (SELECT category_name FROM subject_categories WHERE id = ?)";
    $stmt = $conn->prepare($checkUsageQuery);
    $stmt->bind_param("i", $category_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $usageCount = $result->fetch_assoc()['count'];
    
    if ($usageCount > 0) {
        $errorMessage = "এই ক্যাটাগরি $usageCount টি বিষয়ে ব্যবহৃত হচ্ছে। আগে এই বিষয়গুলির ক্যাটাগরি পরিবর্তন করুন।";
    } else {
        // Delete category
        $deleteQuery = "DELETE FROM subject_categories WHERE id = ?";
        $stmt = $conn->prepare($deleteQuery);
        $stmt->bind_param("i", $category_id);
        
        if ($stmt->execute()) {
            $successMessage = "ক্যাটাগরি সফলভাবে মুছে ফেলা হয়েছে!";
        } else {
            $errorMessage = "ক্যাটাগরি মুছতে সমস্যা হয়েছে: " . $conn->error;
        }
    }
}

// Get all categories
$categoriesQuery = "SELECT * FROM subject_categories ORDER BY id";
$categories = $conn->query($categoriesQuery);

// Get category usage counts
$categoryUsage = [];
$usageQuery = "SELECT category, COUNT(*) as count FROM subjects GROUP BY category";
$usageResult = $conn->query($usageQuery);

if ($usageResult && $usageResult->num_rows > 0) {
    while ($row = $usageResult->fetch_assoc()) {
        $categoryUsage[$row['category']] = $row['count'];
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>বিষয় ক্যাটাগরি - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .category-card {
            transition: all 0.3s ease;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .category-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 14px;
            padding: 5px 10px;
        }
        .category-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="subject_categories.php">
                            <i class="fas fa-tags me-2"></i> বিষয় ক্যাটাগরি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sessions.php">
                            <i class="fas fa-calendar-alt me-2"></i> সেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>বিষয় ক্যাটাগরি ব্যবস্থাপনা</h2>
                        <p class="text-muted">বিষয়ের ক্যাটাগরি যোগ করুন, সম্পাদনা করুন এবং ব্যবস্থাপনা করুন</p>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                            <i class="fas fa-plus-circle me-2"></i>নতুন ক্যাটাগরি
                        </button>
                    </div>
                </div>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i><?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Categories Display -->
                <div class="row">
                    <?php 
                    if ($categories && $categories->num_rows > 0):
                        $colors = ['primary', 'success', 'warning', 'info', 'danger', 'secondary'];
                        $icons = ['book', 'bookmark', 'graduation-cap', 'pencil-alt', 'calculator', 'atom', 'flask'];
                        $colorIndex = 0;
                        $iconIndex = 0;
                        
                        while ($category = $categories->fetch_assoc()):
                            $color = $colors[$colorIndex % count($colors)];
                            $icon = $icons[$iconIndex % count($icons)];
                            $colorIndex++;
                            $iconIndex++;
                            
                            $usageCount = $categoryUsage[$category['category_name']] ?? 0;
                    ?>
                        <div class="col-md-4">
                            <div class="card category-card">
                                <div class="card-body text-center">
                                    <span class="category-badge badge bg-<?php echo $color; ?>">
                                        <?php echo $usageCount; ?> বিষয়
                                    </span>
                                    <div class="category-icon bg-<?php echo $color; ?>-light text-<?php echo $color; ?>">
                                        <i class="fas fa-<?php echo $icon; ?> fa-2x"></i>
                                    </div>
                                    <h4 class="card-title"><?php echo htmlspecialchars($category['category_name']); ?></h4>
                                    <p class="card-text"><?php echo htmlspecialchars($category['description']); ?></p>
                                    <div class="mt-3">
                                        <button class="btn btn-sm btn-primary edit-category-btn" 
                                                data-id="<?php echo $category['id']; ?>"
                                                data-name="<?php echo htmlspecialchars($category['category_name']); ?>"
                                                data-description="<?php echo htmlspecialchars($category['description']); ?>"
                                                data-bs-toggle="modal" 
                                                data-bs-target="#editCategoryModal">
                                            <i class="fas fa-edit me-1"></i> সম্পাদনা
                                        </button>
                                        <?php if ($usageCount == 0): ?>
                                            <a href="subject_categories.php?delete=<?php echo $category['id']; ?>" 
                                               class="btn btn-sm btn-danger"
                                               onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই ক্যাটাগরি মুছতে চান?');">
                                                <i class="fas fa-trash me-1"></i> মুছুন
                                            </a>
                                        <?php else: ?>
                                            <button class="btn btn-sm btn-secondary" disabled title="এই ক্যাটাগরি ব্যবহৃত হচ্ছে">
                                                <i class="fas fa-trash me-1"></i> মুছুন
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php 
                        endwhile;
                    else:
                    ?>
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>কোন ক্যাটাগরি পাওয়া যায়নি। উপরের "নতুন ক্যাটাগরি" বাটনে ক্লিক করে ক্যাটাগরি যোগ করুন।
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Instructions Card -->
                <div class="card mt-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i>বিষয় ক্যাটাগরি সম্পর্কে তথ্য</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card mb-3">
                                    <div class="card-body">
                                        <h5 class="card-title text-primary"><i class="fas fa-book me-2"></i>আবশ্যিক বিষয়</h5>
                                        <p class="card-text">শিক্ষার্থীদের জন্য বাধ্যতামূলক বিষয়সমূহ। এই বিষয়গুলি সকল শিক্ষার্থীকে নিতে হবে।</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card mb-3">
                                    <div class="card-body">
                                        <h5 class="card-title text-success"><i class="fas fa-bookmark me-2"></i>ঐচ্ছিক বিষয়</h5>
                                        <p class="card-text">শিক্ষার্থীরা নির্বাচন করতে পারে এমন বিষয়সমূহ। এগুলি থেকে শিক্ষার্থীরা পছন্দ অনুযায়ী বিষয় নির্বাচন করতে পারে।</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card mb-3">
                                    <div class="card-body">
                                        <h5 class="card-title text-warning"><i class="fas fa-graduation-cap me-2"></i>৪র্থ বিষয়</h5>
                                        <p class="card-text">শিক্ষার্থীরা ৪র্থ বিষয় হিসেবে নির্বাচন করতে পারে এমন বিষয়সমূহ। প্রতি শিক্ষার্থী একটি ৪র্থ বিষয় নির্বাচন করতে পারে।</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>গুরুত্বপূর্ণ:</strong> একটি বিষয় বিভিন্ন বিভাগের জন্য ভিন্ন ভিন্ন ধরনের হতে পারে। উদাহরণস্বরূপ, গণিত বিজ্ঞান বিভাগের জন্য আবশ্যিক কিন্তু কলা বিভাগের জন্য ঐচ্ছিক হতে পারে।
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Category Modal -->
    <div class="modal fade" id="addCategoryModal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="addCategoryModalLabel"><i class="fas fa-plus-circle me-2"></i>নতুন ক্যাটাগরি যোগ করুন</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="subject_categories.php">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="category_name" class="form-label">ক্যাটাগরি নাম <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="category_name" name="category_name" required>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">বিবরণ</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>বাতিল করুন
                        </button>
                        <button type="submit" name="add_category" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>সংরক্ষণ করুন
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Category Modal -->
    <div class="modal fade" id="editCategoryModal" tabindex="-1" aria-labelledby="editCategoryModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="editCategoryModalLabel"><i class="fas fa-edit me-2"></i>ক্যাটাগরি সম্পাদনা করুন</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="subject_categories.php">
                    <div class="modal-body">
                        <input type="hidden" id="edit_category_id" name="category_id">
                        <div class="mb-3">
                            <label for="edit_category_name" class="form-label">ক্যাটাগরি নাম <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit_category_name" name="category_name" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_description" class="form-label">বিবরণ</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>বাতিল করুন
                        </button>
                        <button type="submit" name="update_category" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>আপডেট করুন
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Edit category modal data
        document.addEventListener('DOMContentLoaded', function() {
            const editButtons = document.querySelectorAll('.edit-category-btn');
            
            editButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const id = this.getAttribute('data-id');
                    const name = this.getAttribute('data-name');
                    const description = this.getAttribute('data-description');
                    
                    document.getElementById('edit_category_id').value = id;
                    document.getElementById('edit_category_name').value = name;
                    document.getElementById('edit_description').value = description;
                });
            });
            
            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert-dismissible');
                alerts.forEach(function(alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);
        });
    </script>
</body>
</html>
