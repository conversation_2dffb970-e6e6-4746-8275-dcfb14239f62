// Simple Exam Pattern JavaScript

// Hide all loaders and loading elements
function hideLoaders() {
    // Hide all possible loader elements
    const loaderSelectors = [
        '#page-loader', '.loader', '.loading-spinner', '.loader-spinner',
        '.loader-text', '[id*="loader"]', '[class*="loader"]', '[class*="loading"]',
        '.preloader', '.spinner', '[id*="preloader"]', '[class*="preloader"]', '[class*="spinner"]'
    ];

    loaderSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            element.style.display = 'none';
            element.style.opacity = '0';
            element.style.visibility = 'hidden';
            element.style.height = '0';
            element.style.width = '0';
            element.style.position = 'absolute';
            element.style.zIndex = '-9999';
            element.style.pointerEvents = 'none';
        });
    });

    // Ensure body is visible
    document.body.style.display = 'block';
    document.body.style.visibility = 'visible';
    document.body.style.opacity = '1';
}

// Calculate total sum of distribution components
function calculateSum() {
    // Check if all required elements exist
    const totalMarksElement = document.getElementById('total_marks');
    const cqMarksElement = document.getElementById('cq_marks');
    const mcqMarksElement = document.getElementById('mcq_marks');
    const practicalMarksElement = document.getElementById('practical_marks');
    const totalSumElement = document.getElementById('total-sum');

    // Progress elements
    const cqProgressElement = document.getElementById('cq-progress');
    const mcqProgressElement = document.getElementById('mcq-progress');
    const practicalProgressElement = document.getElementById('practical-progress');

    // Percentage display elements
    const cqPercentageElement = document.getElementById('cq-percentage');
    const mcqPercentageElement = document.getElementById('mcq-percentage');
    const practicalPercentageElement = document.getElementById('practical-percentage');

    // Toggle checkboxes
    const hasCqCheckbox = document.getElementById('has_cq');
    const hasMcqCheckbox = document.getElementById('has_mcq');
    const hasPracticalCheckbox = document.getElementById('has_practical');

    // Cards
    const mcqCard = document.getElementById('mcq-card');
    const practicalCard = document.getElementById('practical-card');

    if (!totalMarksElement || !cqMarksElement || !mcqMarksElement ||
        !practicalMarksElement || !totalSumElement) {
        console.error('One or more required elements not found');
        return;
    }

    let sum = 0;
    const totalMarks = parseFloat(totalMarksElement.value) || 100;

    // Get values from all distribution components, but only count enabled ones
    let cqMarks = 0;
    let mcqMarks = 0;
    let practicalMarks = 0;

    // Only count marks if the component is enabled (checkbox is checked)
    if (hasCqCheckbox && hasCqCheckbox.checked) {
        cqMarks = parseFloat(cqMarksElement.value) || 0;
    }

    if (hasMcqCheckbox && hasMcqCheckbox.checked) {
        mcqMarks = parseFloat(mcqMarksElement.value) || 0;
    }

    if (hasPracticalCheckbox && hasPracticalCheckbox.checked) {
        practicalMarks = parseFloat(practicalMarksElement.value) || 0;
    }

    // Calculate sum of enabled components only
    sum = cqMarks + mcqMarks + practicalMarks;

    // Debug information
    console.log('CQ Enabled:', hasCqCheckbox ? hasCqCheckbox.checked : 'N/A', 'Value:', cqMarks);
    console.log('MCQ Enabled:', hasMcqCheckbox ? hasMcqCheckbox.checked : 'N/A', 'Value:', mcqMarks);
    console.log('Practical Enabled:', hasPracticalCheckbox ? hasPracticalCheckbox.checked : 'N/A', 'Value:', practicalMarks);
    console.log('Total Sum:', sum, 'Total Marks:', totalMarks);

    // Update total sum display
    if (totalSumElement) {
        totalSumElement.textContent = sum.toFixed(0);
    }

    // Calculate percentages
    const cqPercentage = totalMarks > 0 ? (cqMarks / totalMarks) * 100 : 0;
    const mcqPercentage = totalMarks > 0 ? (mcqMarks / totalMarks) * 100 : 0;
    const practicalPercentage = totalMarks > 0 ? (practicalMarks / totalMarks) * 100 : 0;

    // Update percentage displays
    if (cqPercentageElement) cqPercentageElement.textContent = cqPercentage.toFixed(0) + '%';
    if (mcqPercentageElement) mcqPercentageElement.textContent = mcqPercentage.toFixed(0) + '%';
    if (practicalPercentageElement) practicalPercentageElement.textContent = practicalPercentage.toFixed(0) + '%';

    // Update progress bars
    if (cqProgressElement) {
        cqProgressElement.style.width = cqPercentage + '%';
    }

    if (mcqProgressElement) {
        mcqProgressElement.style.width = mcqPercentage + '%';
    }

    if (practicalProgressElement) {
        practicalProgressElement.style.width = practicalPercentage + '%';
    }

    // Update card visibility
    if (mcqCard) {
        if (hasMcqCheckbox && hasMcqCheckbox.checked) {
            mcqCard.style.display = 'block';
        } else {
            mcqCard.style.display = 'none';
        }
    }

    if (practicalCard) {
        if (hasPracticalCheckbox && hasPracticalCheckbox.checked) {
            practicalCard.style.display = 'block';
        } else {
            practicalCard.style.display = 'none';
        }
    }

    // Update total sum color based on whether it matches total marks
    if (totalSumElement) {
        if (Math.abs(sum - totalMarks) < 0.01) {
            totalSumElement.style.backgroundColor = '#4361ee';
        } else if (sum > totalMarks) {
            totalSumElement.style.backgroundColor = '#e74c3c';
        } else {
            totalSumElement.style.backgroundColor = '#f39c12';
        }
    }

    // Auto-adjust values if needed
    const formElement = document.querySelector('form.needs-validation');
    const submitButton = formElement ? formElement.querySelector('button[type="submit"]') : null;

    if (submitButton) {
        if (Math.abs(sum - totalMarks) < 0.01) {
            // Sum matches total marks, enable submit button
            submitButton.disabled = false;

            // Remove any error message
            const errorElement = document.getElementById('sum-error');
            if (errorElement) {
                errorElement.style.display = 'none';
            }
        } else {
            // Sum doesn't match total marks, disable submit button
            submitButton.disabled = true;

            // Show error message
            let errorElement = document.getElementById('sum-error');
            if (!errorElement) {
                errorElement = document.createElement('div');
                errorElement.id = 'sum-error';
                errorElement.className = 'alert alert-danger mt-3';
                const formGroup = document.querySelector('.marks-summary');
                if (formGroup) {
                    formGroup.appendChild(errorElement);
                }
            }

            errorElement.style.display = 'block';
            errorElement.innerHTML = `
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>ত্রুটি!</strong> সমস্ত উপাদানের যোগফল মোট মার্কসের সমান হতে হবে। বর্তমান যোগফল: ${sum}, মোট মার্কস: ${totalMarks}
                <button type="button" class="btn btn-sm btn-primary ms-3" id="auto-adjust-button">
                    <i class="fas fa-magic me-1"></i> অটো-অ্যাডজাস্ট করুন
                </button>
            `;

            // Add event listener to auto-adjust button
            const autoAdjustButton = document.getElementById('auto-adjust-button');
            if (autoAdjustButton) {
                autoAdjustButton.addEventListener('click', function() {
                    adjustAllComponents();
                    calculateSum();
                });
            }
        }
    }
}

// Toggle component function
function toggleComponent(checkbox) {
    const targetId = checkbox.dataset.target;
    const targetElement = document.getElementById(targetId);

    if (targetElement) {
        targetElement.disabled = !checkbox.checked;

        if (!checkbox.checked) {
            // If disabled, set value to 0
            targetElement.value = '0';
        } else if (targetElement.value === '0') {
            // If enabled and value is 0, set default value based on how many components are enabled
            const enabledComponents = document.querySelectorAll('.component-toggle:checked').length;

            // Get total marks
            const totalMarksElement = document.getElementById('total_marks');
            const totalMarks = parseFloat(totalMarksElement.value) || 100;

            if (enabledComponents === 1) {
                // If this is the only enabled component, set it to total marks
                targetElement.value = totalMarks;
            } else if (enabledComponents === 2) {
                // If there are 2 enabled components, distribute marks
                if (targetId === 'cq_marks') targetElement.value = Math.round(totalMarks * 0.7);
                if (targetId === 'mcq_marks') targetElement.value = Math.round(totalMarks * 0.3);
                if (targetId === 'practical_marks') targetElement.value = Math.round(totalMarks * 0.3);

                // Adjust other enabled component to make sum equal to total
                adjustOtherComponents(targetId);
            } else {
                // Default distribution for 3 components
                if (targetId === 'cq_marks') targetElement.value = Math.round(totalMarks * 0.6);
                if (targetId === 'mcq_marks') targetElement.value = Math.round(totalMarks * 0.3);
                if (targetId === 'practical_marks') targetElement.value = Math.round(totalMarks * 0.1);

                // Adjust other components to make sum equal to total
                adjustAllComponents();
            }
        }

        // Update calculations
        calculateSum();
    }
}

// Adjust other enabled components to make sum equal to total
function adjustOtherComponents(currentTargetId) {
    const totalMarksElement = document.getElementById('total_marks');
    const totalMarks = parseFloat(totalMarksElement.value) || 100;

    const cqMarksElement = document.getElementById('cq_marks');
    const mcqMarksElement = document.getElementById('mcq_marks');
    const practicalMarksElement = document.getElementById('practical_marks');

    const hasCqCheckbox = document.getElementById('has_cq');
    const hasMcqCheckbox = document.getElementById('has_mcq');
    const hasPracticalCheckbox = document.getElementById('has_practical');

    let currentValue = 0;
    if (currentTargetId === 'cq_marks' && !cqMarksElement.disabled) {
        currentValue = parseFloat(cqMarksElement.value) || 0;
    } else if (currentTargetId === 'mcq_marks' && !mcqMarksElement.disabled) {
        currentValue = parseFloat(mcqMarksElement.value) || 0;
    } else if (currentTargetId === 'practical_marks' && !practicalMarksElement.disabled) {
        currentValue = parseFloat(practicalMarksElement.value) || 0;
    }

    const remainingValue = totalMarks - currentValue;

    // Find the other enabled component and set its value
    if (currentTargetId !== 'cq_marks' && hasCqCheckbox.checked) {
        cqMarksElement.value = remainingValue;
    } else if (currentTargetId !== 'mcq_marks' && hasMcqCheckbox.checked) {
        mcqMarksElement.value = remainingValue;
    } else if (currentTargetId !== 'practical_marks' && hasPracticalCheckbox.checked) {
        practicalMarksElement.value = remainingValue;
    }
}

// Adjust all components to make sum equal to total
function adjustAllComponents() {
    const totalMarksElement = document.getElementById('total_marks');
    const totalMarks = parseFloat(totalMarksElement.value) || 100;

    const cqMarksElement = document.getElementById('cq_marks');
    const mcqMarksElement = document.getElementById('mcq_marks');
    const practicalMarksElement = document.getElementById('practical_marks');

    const hasCqCheckbox = document.getElementById('has_cq');
    const hasMcqCheckbox = document.getElementById('has_mcq');
    const hasPracticalCheckbox = document.getElementById('has_practical');

    // Count enabled components
    let enabledCount = 0;
    if (hasCqCheckbox.checked) enabledCount++;
    if (hasMcqCheckbox.checked) enabledCount++;
    if (hasPracticalCheckbox.checked) enabledCount++;

    if (enabledCount === 0) return;

    // Calculate values based on enabled components
    if (enabledCount === 1) {
        // If only one component is enabled, set it to total marks
        if (hasCqCheckbox.checked) cqMarksElement.value = totalMarks;
        if (hasMcqCheckbox.checked) mcqMarksElement.value = totalMarks;
        if (hasPracticalCheckbox.checked) practicalMarksElement.value = totalMarks;
    } else if (enabledCount === 2) {
        // If two components are enabled, distribute 70/30
        let firstValue = Math.round(totalMarks * 0.7);
        let secondValue = totalMarks - firstValue;

        if (hasCqCheckbox.checked && hasMcqCheckbox.checked) {
            cqMarksElement.value = firstValue;
            mcqMarksElement.value = secondValue;
        } else if (hasCqCheckbox.checked && hasPracticalCheckbox.checked) {
            cqMarksElement.value = firstValue;
            practicalMarksElement.value = secondValue;
        } else if (hasMcqCheckbox.checked && hasPracticalCheckbox.checked) {
            mcqMarksElement.value = firstValue;
            practicalMarksElement.value = secondValue;
        }
    } else if (enabledCount === 3) {
        // If all three components are enabled, distribute 60/30/10
        let cqValue = Math.round(totalMarks * 0.6);
        let mcqValue = Math.round(totalMarks * 0.3);
        let practicalValue = totalMarks - cqValue - mcqValue;

        cqMarksElement.value = cqValue;
        mcqMarksElement.value = mcqValue;
        practicalMarksElement.value = practicalValue;
    }
}

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    // Execute hideLoaders again
    hideLoaders();

    // Stop any existing intervals
    for (let i = 1; i < 9999; i++) {
        window.clearInterval(i);
    }

    // Add event listeners to all distribution components if they exist
    const cqMarksElement = document.getElementById('cq_marks');
    const mcqMarksElement = document.getElementById('mcq_marks');
    const practicalMarksElement = document.getElementById('practical_marks');
    const totalMarksElement = document.getElementById('total_marks');

    // Add event listeners to input fields
    if (cqMarksElement) cqMarksElement.addEventListener('input', calculateSum);
    if (mcqMarksElement) mcqMarksElement.addEventListener('input', calculateSum);
    if (practicalMarksElement) practicalMarksElement.addEventListener('input', calculateSum);
    if (totalMarksElement) totalMarksElement.addEventListener('input', calculateSum);

    // Add event listeners to toggle checkboxes
    const toggleCheckboxes = document.querySelectorAll('.component-toggle');
    toggleCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            toggleComponent(this);
        });
    });

    // Initialize component states based on edit data
    const hasCqCheckbox = document.getElementById('has_cq');
    const hasMcqCheckbox = document.getElementById('has_mcq');
    const hasPracticalCheckbox = document.getElementById('has_practical');

    // Set checkbox states based on PHP edit data
    if (hasCqCheckbox) {
        const cqMarksInput = document.getElementById('cq_marks');
        if (cqMarksInput && parseFloat(cqMarksInput.value) > 0) {
            hasCqCheckbox.checked = true;
        }
    }

    if (hasMcqCheckbox) {
        const mcqMarksInput = document.getElementById('mcq_marks');
        if (mcqMarksInput && parseFloat(mcqMarksInput.value) > 0) {
            hasMcqCheckbox.checked = true;
        }
    }

    if (hasPracticalCheckbox) {
        const practicalMarksInput = document.getElementById('practical_marks');
        if (practicalMarksInput && parseFloat(practicalMarksInput.value) > 0) {
            hasPracticalCheckbox.checked = true;
        }
    }

    // Initialize component states
    toggleCheckboxes.forEach(checkbox => {
        toggleComponent(checkbox);
    });

    // Calculate initial sum
    calculateSum();

    // Set form submission handler
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            // Get checkbox states
            const hasCqCheckbox = document.getElementById('has_cq');
            const hasMcqCheckbox = document.getElementById('has_mcq');
            const hasPracticalCheckbox = document.getElementById('has_practical');

            // Get input fields
            const cqMarksInput = document.getElementById('cq_marks');
            const mcqMarksInput = document.getElementById('mcq_marks');
            const practicalMarksInput = document.getElementById('practical_marks');

            // Set values to 0 for disabled components
            if (hasMcqCheckbox && !hasMcqCheckbox.checked && mcqMarksInput) {
                mcqMarksInput.disabled = false; // Temporarily enable to allow form submission
                mcqMarksInput.value = '0';
            }

            if (hasPracticalCheckbox && !hasPracticalCheckbox.checked && practicalMarksInput) {
                practicalMarksInput.disabled = false; // Temporarily enable to allow form submission
                practicalMarksInput.value = '0';
            }
        });
    }

    // Execute hideLoaders one more time after a short delay
    setTimeout(hideLoaders, 500);

    // Also execute on load
    window.addEventListener('load', function() {
        hideLoaders();
        // Execute one more time after a short delay
        setTimeout(hideLoaders, 500);
    });
});
