<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get parameters
$examId = isset($_GET['exam_id']) ? intval($_GET['exam_id']) : 0;
$classGroup = isset($_GET['class_group']) ? $_GET['class_group'] : '';

if (!$examId) {
    die("পরীক্ষা ID প্রয়োজন।");
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_marks'])) {
    $resultsTableName = 'results_primary_lower';
    
    // Create results table if not exists
    $createResultsTable = "CREATE TABLE IF NOT EXISTS $resultsTableName (
        id INT AUTO_INCREMENT PRIMARY KEY,
        exam_id INT NOT NULL,
        student_id INT NOT NULL,
        obtained_marks DECIMAL(5,2) NOT NULL DEFAULT 0,
        attendance ENUM('present', 'absent') DEFAULT 'present',
        remarks TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_exam_student (exam_id, student_id)
    )";
    $conn->query($createResultsTable);
    
    $success = true;
    $errors = [];
    
    foreach ($_POST['marks'] as $studentId => $markData) {
        $studentId = intval($studentId);
        $obtainedMarks = floatval($markData['obtained_marks']);
        $attendance = $markData['attendance'] === 'absent' ? 'absent' : 'present';
        $remarks = trim($markData['remarks']);
        
        // Insert or update marks
        $insertQuery = "INSERT INTO $resultsTableName (exam_id, student_id, obtained_marks, attendance, remarks) 
                        VALUES ($examId, $studentId, $obtainedMarks, '$attendance', '$remarks')
                        ON DUPLICATE KEY UPDATE 
                        obtained_marks = $obtainedMarks, 
                        attendance = '$attendance', 
                        remarks = '$remarks',
                        updated_at = NOW()";
        
        if (!$conn->query($insertQuery)) {
            $success = false;
            $errors[] = "ছাত্র/ছাত্রী ID $studentId এর জন্য মার্ক সেভ করতে সমস্যা: " . $conn->error;
        }
    }
    
    if ($success) {
        $successMessage = "সব মার্ক সফলভাবে সেভ করা হয়েছে!";
    }
}

// Initialize default values
$exam = [
    'exam_name' => 'নমুনা পরীক্ষা',
    'subject_name' => 'বাংলা',
    'class_name' => 'ক্লাস ১',
    'total_marks' => 100,
    'passing_marks' => 33,
    'class_id' => 1
];

try {
    // Get exam details
    $examTableName = 'exams_primary_lower';
    $examQuery = "SELECT e.*, c.class_name, s.subject_name
                  FROM $examTableName e
                  LEFT JOIN classes c ON e.class_id = c.id
                  LEFT JOIN subjects s ON e.subject_id = s.id
                  WHERE e.id = $examId";
    $examResult = $conn->query($examQuery);

    if ($examResult && $examResult->num_rows > 0) {
        $exam = $examResult->fetch_assoc();
    }
} catch (Exception $e) {
    // Use default values if database error
}

// Get students with existing marks
$studentsResult = null;
try {
    $studentsQuery = "SELECT s.*,
                      COALESCE(s.student_name, s.first_name) as name,
                      COALESCE(s.roll_number, s.student_id) as roll,
                      r.obtained_marks,
                      r.attendance,
                      r.remarks
                      FROM students s
                      LEFT JOIN results_primary_lower r ON s.id = r.student_id AND r.exam_id = $examId
                      WHERE s.class_id = {$exam['class_id']}
                      ORDER BY CAST(COALESCE(s.roll_number, s.student_id) AS UNSIGNED)";
    $studentsResult = $conn->query($studentsQuery);
} catch (Exception $e) {
    // Handle database error
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>মার্ক এনট্রি - <?php echo htmlspecialchars($exam['exam_name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            margin: 20px 0;
        }
        .header-section {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 30px;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }
        .marks-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .marks-table th {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            font-weight: 600;
            text-align: center;
            padding: 15px 10px;
            border: none;
        }
        .marks-table td {
            padding: 12px 10px;
            vertical-align: middle;
            border-bottom: 1px solid #dee2e6;
        }
        .mark-input {
            width: 80px;
            text-align: center;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 8px;
            font-weight: 600;
        }
        .mark-input:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        .attendance-select {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 8px;
        }
        .save-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            box-shadow: 0 5px 15px rgba(40,167,69,0.3);
            transition: all 0.3s ease;
        }
        .save-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40,167,69,0.4);
        }
        .student-row:hover {
            background-color: #f8f9fa;
        }
        .grade-display {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="main-container">
            <!-- Header -->
            <div class="header-section">
                <h2><i class="fas fa-edit me-3"></i>মার্ক এনট্রি</h2>
                <div class="mt-3">
                    <strong>পরীক্ষা:</strong> <?php echo htmlspecialchars($exam['exam_name']); ?> |
                    <strong>বিষয়:</strong> <?php echo htmlspecialchars($exam['subject_name'] ?? 'সকল বিষয়'); ?> |
                    <strong>শ্রেণি:</strong> <?php echo htmlspecialchars($exam['class_name']); ?> |
                    <strong>পূর্ণমান:</strong> <?php echo $exam['total_marks']; ?> |
                    <strong>পাশের নম্বর:</strong> <?php echo $exam['passing_marks']; ?>
                </div>
            </div>

            <div class="p-4">
                <!-- Success/Error Messages -->
                <?php if (isset($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Marks Entry Form -->
                <form method="POST" id="marksForm">
                    <div class="marks-table">
                        <table class="table table-bordered mb-0">
                            <thead>
                                <tr>
                                    <th style="width: 8%;">ক্রমিক</th>
                                    <th style="width: 12%;">রোল নং</th>
                                    <th style="width: 30%;">ছাত্র/ছাত্রীর নাম</th>
                                    <th style="width: 15%;">প্রাপ্ত নম্বর</th>
                                    <th style="width: 12%;">গ্রেড</th>
                                    <th style="width: 12%;">হাজিরা</th>
                                    <th style="width: 11%;">মন্তব্য</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                if ($studentsResult && $studentsResult->num_rows > 0):
                                    $serial = 1;
                                    while ($student = $studentsResult->fetch_assoc()): 
                                ?>
                                    <tr class="student-row">
                                        <td class="text-center"><?php echo $serial++; ?></td>
                                        <td class="text-center"><strong><?php echo htmlspecialchars($student['roll'] ?? 'N/A'); ?></strong></td>
                                        <td style="text-align: left; padding-left: 15px;">
                                            <?php echo htmlspecialchars($student['name'] ?? 'নাম নেই'); ?>
                                        </td>
                                        <td class="text-center">
                                            <input type="number" 
                                                   name="marks[<?php echo $student['id']; ?>][obtained_marks]" 
                                                   class="mark-input" 
                                                   min="0" 
                                                   max="<?php echo $exam['total_marks']; ?>" 
                                                   step="0.5"
                                                   value="<?php echo $student['obtained_marks'] ?? ''; ?>"
                                                   onchange="calculateGrade(this, <?php echo $exam['total_marks']; ?>, <?php echo $exam['passing_marks']; ?>)">
                                        </td>
                                        <td class="text-center">
                                            <span class="grade-display" id="grade_<?php echo $student['id']; ?>">
                                                <?php 
                                                if ($student['obtained_marks'] !== null) {
                                                    echo calculateGrade($student['obtained_marks'], $exam['total_marks'], $exam['passing_marks']);
                                                }
                                                ?>
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <select name="marks[<?php echo $student['id']; ?>][attendance]" class="attendance-select">
                                                <option value="present" <?php echo ($student['attendance'] ?? 'present') === 'present' ? 'selected' : ''; ?>>উপস্থিত</option>
                                                <option value="absent" <?php echo ($student['attendance'] ?? '') === 'absent' ? 'selected' : ''; ?>>অনুপস্থিত</option>
                                            </select>
                                        </td>
                                        <td>
                                            <input type="text" 
                                                   name="marks[<?php echo $student['id']; ?>][remarks]" 
                                                   class="form-control form-control-sm" 
                                                   value="<?php echo htmlspecialchars($student['remarks'] ?? ''); ?>"
                                                   placeholder="মন্তব্য">
                                        </td>
                                    </tr>
                                <?php 
                                    endwhile;
                                else: 
                                ?>
                                    <tr>
                                        <td colspan="7" class="text-center text-muted py-4">
                                            এই শ্রেণিতে কোন ছাত্র/ছাত্রী পাওয়া যায়নি।
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <?php if ($studentsResult && $studentsResult->num_rows > 0): ?>
                        <div class="text-center mt-4">
                            <button type="submit" name="save_marks" class="btn btn-success save-btn">
                                <i class="fas fa-save me-2"></i>সব মার্ক সেভ করুন
                            </button>
                            <a href="class_exam_primary_lower_1_2.php" class="btn btn-secondary ms-3">
                                <i class="fas fa-arrow-left me-2"></i>ফিরে যান
                            </a>
                        </div>
                    <?php endif; ?>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function calculateGrade(input, totalMarks, passingMarks) {
            const obtainedMarks = parseFloat(input.value) || 0;
            const percentage = (obtainedMarks / totalMarks) * 100;
            const studentId = input.name.match(/\[(\d+)\]/)[1];
            const gradeElement = document.getElementById('grade_' + studentId);
            
            let grade = '';
            let gradeClass = '';
            
            if (obtainedMarks < passingMarks) {
                grade = 'F';
                gradeClass = 'bg-danger text-white';
            } else if (percentage >= 80) {
                grade = 'A+';
                gradeClass = 'bg-success text-white';
            } else if (percentage >= 70) {
                grade = 'A';
                gradeClass = 'bg-primary text-white';
            } else if (percentage >= 60) {
                grade = 'A-';
                gradeClass = 'bg-info text-white';
            } else if (percentage >= 50) {
                grade = 'B';
                gradeClass = 'bg-warning text-dark';
            } else if (percentage >= 40) {
                grade = 'C';
                gradeClass = 'bg-secondary text-white';
            } else {
                grade = 'D';
                gradeClass = 'bg-danger text-white';
            }
            
            gradeElement.textContent = grade;
            gradeElement.className = 'grade-display ' + gradeClass;
        }

        // Calculate grades on page load
        document.addEventListener('DOMContentLoaded', function() {
            const markInputs = document.querySelectorAll('.mark-input');
            markInputs.forEach(input => {
                if (input.value) {
                    calculateGrade(input, <?php echo $exam['total_marks']; ?>, <?php echo $exam['passing_marks']; ?>);
                }
            });
        });

        // Form validation
        document.getElementById('marksForm').addEventListener('submit', function(e) {
            const markInputs = document.querySelectorAll('.mark-input');
            let hasError = false;
            
            markInputs.forEach(input => {
                const value = parseFloat(input.value);
                if (input.value && (value < 0 || value > <?php echo $exam['total_marks']; ?>)) {
                    hasError = true;
                    input.style.borderColor = '#dc3545';
                } else {
                    input.style.borderColor = '#e9ecef';
                }
            });
            
            if (hasError) {
                e.preventDefault();
                alert('অনুগ্রহ করে সঠিক নম্বর দিন (০ থেকে <?php echo $exam['total_marks']; ?> এর মধ্যে)।');
            }
        });
    </script>
</body>
</html>

<?php 
function calculateGrade($obtainedMarks, $totalMarks, $passingMarks) {
    if ($obtainedMarks < $passingMarks) return 'F';
    
    $percentage = ($obtainedMarks / $totalMarks) * 100;
    
    if ($percentage >= 80) return 'A+';
    if ($percentage >= 70) return 'A';
    if ($percentage >= 60) return 'A-';
    if ($percentage >= 50) return 'B';
    if ($percentage >= 40) return 'C';
    return 'D';
}

if (isset($conn)) $conn->close(); 
?>
