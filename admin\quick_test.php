<?php
session_start();
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

echo "<h2>দ্রুত টেস্ট - রিসিপ্ট সমস্যা সমাধান</h2>";

try {
    // Step 1: Check if we have necessary tables and data
    echo "<h3>ধাপ ১: ডাটাবেস চেক</h3>";
    
    $tables = ['students', 'classes', 'fees', 'fee_payments'];
    $allTablesExist = true;
    
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            $count = $conn->query("SELECT COUNT(*) as count FROM $table")->fetch_assoc()['count'];
            echo "<p>✅ $table টেবিল আছে ($count রেকর্ড)</p>";
        } else {
            echo "<p>❌ $table টেবিল নেই</p>";
            $allTablesExist = false;
        }
    }
    
    if (!$allTablesExist) {
        echo "<p style='color: red;'>প্রয়োজনীয় টেবিল নেই। প্রথমে টেবিল তৈরি করুন।</p>";
        exit;
    }
    
    // Step 2: Create sample data if needed
    echo "<h3>ধাপ ২: নমুনা ডেটা তৈরি</h3>";
    
    // Check if we have a class
    $classResult = $conn->query("SELECT COUNT(*) as count FROM classes");
    $classCount = $classResult->fetch_assoc()['count'];
    
    if ($classCount == 0) {
        $conn->query("INSERT INTO classes (class_name, description) VALUES ('একাদশ শ্রেণী', 'একাদশ শ্রেণীর শিক্ষার্থীরা')");
        echo "<p>✅ নমুনা ক্লাস তৈরি করা হয়েছে</p>";
    }
    
    // Get class ID
    $classData = $conn->query("SELECT id FROM classes LIMIT 1")->fetch_assoc();
    $classId = $classData['id'];
    
    // Check if we have a student
    $studentResult = $conn->query("SELECT COUNT(*) as count FROM students");
    $studentCount = $studentResult->fetch_assoc()['count'];
    
    if ($studentCount == 0) {
        $insertStudent = "INSERT INTO students (first_name, last_name, roll_no, class_id, student_id, email, phone, gender) 
                         VALUES ('মোহাম্মদ', 'রহিম উদ্দিন', '2024001', ?, 'S2024001', '<EMAIL>', '01712345678', 'male')";
        $stmt = $conn->prepare($insertStudent);
        $stmt->bind_param('i', $classId);
        $stmt->execute();
        echo "<p>✅ নমুনা স্টুডেন্ট তৈরি করা হয়েছে</p>";
    }
    
    // Get student data
    $studentData = $conn->query("SELECT s.*, c.class_name FROM students s LEFT JOIN classes c ON s.class_id = c.id LIMIT 1")->fetch_assoc();
    $studentId = $studentData['id'];
    
    // Check if we have a fee
    $feeResult = $conn->query("SELECT COUNT(*) as count FROM fees WHERE student_id = $studentId");
    $feeCount = $feeResult->fetch_assoc()['count'];
    
    if ($feeCount == 0) {
        $insertFee = "INSERT INTO fees (student_id, fee_type, amount, due_date, payment_status) 
                     VALUES (?, 'মাসিক বেতন', 1500.00, ?, 'due')";
        $stmt = $conn->prepare($insertFee);
        $dueDate = date('Y-m-d', strtotime('+30 days'));
        $stmt->bind_param('is', $studentId, $dueDate);
        $stmt->execute();
        echo "<p>✅ নমুনা ফি তৈরি করা হয়েছে</p>";
    }
    
    // Get fee data
    $feeData = $conn->query("SELECT * FROM fees WHERE student_id = $studentId LIMIT 1")->fetch_assoc();
    $feeId = $feeData['id'];
    
    // Step 3: Create a payment
    echo "<h3>ধাপ ৩: পেমেন্ট তৈরি</h3>";
    
    // Check if fee_payments table exists
    $checkTable = $conn->query("SHOW TABLES LIKE 'fee_payments'");
    if ($checkTable->num_rows == 0) {
        $createTable = "CREATE TABLE fee_payments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            fee_id INT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            payment_date DATE NOT NULL,
            payment_method VARCHAR(50) NOT NULL,
            receipt_no VARCHAR(50) DEFAULT NULL,
            notes TEXT DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (fee_id) REFERENCES fees(id) ON DELETE CASCADE
        )";
        $conn->query($createTable);
        echo "<p>✅ fee_payments টেবিল তৈরি করা হয়েছে</p>";
    }
    
    // Create a new payment
    $receiptNo = 'RCP-' . date('Ymd') . '-' . rand(1000, 9999);
    $insertPayment = "INSERT INTO fee_payments (fee_id, receipt_no, amount, payment_date, payment_method, notes) 
                     VALUES (?, ?, ?, ?, 'cash', 'টেস্ট পেমেন্ট - সমস্যা সমাধানের জন্য')";
    $stmt = $conn->prepare($insertPayment);
    $amount = 1500.00;
    $paymentDate = date('Y-m-d');
    $stmt->bind_param('isds', $feeId, $receiptNo, $amount, $paymentDate);
    
    if ($stmt->execute()) {
        echo "<p style='color: green;'>✅ নতুন পেমেন্ট সফলভাবে তৈরি হয়েছে!</p>";
        
        // Step 4: Display the data
        echo "<h3>ধাপ ৪: তৈরি করা ডেটা</h3>";
        echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #ddd; border-radius: 5px;'>";
        echo "<p><strong>রিসিপ্ট নং:</strong> $receiptNo</p>";
        echo "<p><strong>শিক্ষার্থী:</strong> " . $studentData['first_name'] . ' ' . $studentData['last_name'] . "</p>";
        echo "<p><strong>রোল নং:</strong> " . $studentData['roll_no'] . "</p>";
        echo "<p><strong>ক্লাস:</strong> " . $studentData['class_name'] . "</p>";
        echo "<p><strong>ফি ধরন:</strong> " . $feeData['fee_type'] . "</p>";
        echo "<p><strong>পরিমাণ:</strong> ৳" . number_format($amount, 2) . "</p>";
        echo "<p><strong>তারিখ:</strong> " . date('d/m/Y', strtotime($paymentDate)) . "</p>";
        echo "</div>";
        
        // Step 5: Test the receipt
        echo "<h3>ধাপ ৫: রিসিপ্ট টেস্ট</h3>";
        echo "<div style='text-align: center; margin: 20px 0;'>";
        echo "<a href='receipt_final.php?receipt_no=$receiptNo' target='_blank' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>মূল রিসিপ্ট দেখুন</a>";
        echo "<a href='receipt_final.php?receipt_no=$receiptNo&debug=1' target='_blank' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>ডিবাগ মোডে দেখুন</a>";
        echo "<a href='receipt_fixed.php?receipt_no=$receiptNo' target='_blank' style='background: #17a2b8; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>ঠিক করা রিসিপ্ট দেখুন</a>";
        echo "</div>";
        
        // Step 6: Verify the data
        echo "<h3>ধাপ ৬: ডেটা যাচাই</h3>";
        $verifyQuery = "SELECT fp.*, f.fee_type, s.first_name, s.last_name, s.roll_no, c.class_name 
                       FROM fee_payments fp 
                       JOIN fees f ON fp.fee_id = f.id 
                       JOIN students s ON f.student_id = s.id 
                       LEFT JOIN classes c ON s.class_id = c.id 
                       WHERE fp.receipt_no = ?";
        $verifyStmt = $conn->prepare($verifyQuery);
        $verifyStmt->bind_param('s', $receiptNo);
        $verifyStmt->execute();
        $verifyResult = $verifyStmt->get_result();
        
        if ($verifyResult->num_rows > 0) {
            $verifyData = $verifyResult->fetch_assoc();
            echo "<p style='color: green;'>✅ ডেটা সফলভাবে যাচাই করা হয়েছে!</p>";
            echo "<pre>" . print_r($verifyData, true) . "</pre>";
        } else {
            echo "<p style='color: red;'>❌ ডেটা যাচাইয়ে সমস্যা!</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ পেমেন্ট তৈরি করতে সমস্যা: " . $stmt->error . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<br><br><a href='fee_management.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ফি ম্যানেজমেন্টে ফিরে যান</a>";
?>
