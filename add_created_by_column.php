<?php
// Database Connection
require_once 'includes/dbh.inc.php';

echo "<h1>Add created_by Column to Results Table</h1>";

// Check if results table exists
$tableCheck = $conn->query("SHOW TABLES LIKE 'results'");
if ($tableCheck->num_rows == 0) {
    echo "<p style='color:red;'>Results table does not exist!</p>";
    exit;
}

// Check if created_by column already exists
$columnCheck = $conn->query("SHOW COLUMNS FROM results LIKE 'created_by'");
if ($columnCheck->num_rows > 0) {
    echo "<p style='color:orange;'>The 'created_by' column already exists in the results table.</p>";
} else {
    // Add the created_by column
    $alterQuery = "ALTER TABLE results ADD COLUMN created_by INT(11) NULL AFTER date";
    
    if ($conn->query($alterQuery)) {
        echo "<p style='color:green;'>Successfully added 'created_by' column to the results table!</p>";
    } else {
        echo "<p style='color:red;'>Error adding column: " . $conn->error . "</p>";
    }
}

// Show the updated table structure
echo "<h2>Updated Table Structure</h2>";
$columnsQuery = "SHOW COLUMNS FROM results";
$columnsResult = $conn->query($columnsQuery);

if ($columnsResult) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($column = $columnsResult->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>Error getting columns: " . $conn->error . "</p>";
}

echo "<p><a href='admin/marks_entry.php'>Go to Marks Entry Page</a></p>";

$conn->close();
?>
