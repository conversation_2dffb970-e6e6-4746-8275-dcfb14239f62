<?php
// This file contains the admin sidebar menu
// Include this file in all admin pages

// Get current page filename
$currentPage = basename($_SERVER['PHP_SELF']);
?>

<style>
.admin-sidebar {
    background-color: #343a40;
    color: #fff;
    overflow-y: auto;
    height: 100vh;
}
.admin-sidebar h3 {
    color: #ffffff;
    padding: 10px 15px;
    margin-bottom: 20px;
    font-size: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.admin-sidebar .nav-link {
    color: rgba(255, 255, 255, 0.75) !important;
    transition: all 0.3s ease;
}
.admin-sidebar .nav-link:hover {
    color: #ffffff !important;
    background-color: rgba(255, 255, 255, 0.1);
}
.admin-sidebar .nav-link.active {
    color: #ffffff !important;
    background-color: #007bff !important;
}
</style>

<div class="col-md-3 col-lg-2 d-md-block sidebar admin-sidebar">
    <div class="mb-4">
        <h3>অ্যাডমিন প্যানেল</h3>
    </div>
    <ul class="nav flex-column">
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage == 'dashboard.php') ? 'active' : ''; ?>" href="dashboard.php">
                <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage == 'update_all.php') ? 'active' : ''; ?>" href="../update_all.php">
                <i class="fas fa-edit me-2"></i> সকল তথ্য আপডেট
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage == 'students.php') ? 'active' : ''; ?>" href="students.php">
                <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage == 'teachers.php') ? 'active' : ''; ?>" href="teachers.php">
                <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage == 'staff.php') ? 'active' : ''; ?>" href="staff.php">
                <i class="fas fa-user-tie me-2"></i> কর্মচারী
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage == 'users.php') ? 'active' : ''; ?>" href="users.php">
                <i class="fas fa-users me-2"></i> ব্যবহারকারী
            </a>
        </li>

        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage == 'classes.php') ? 'active' : ''; ?>" href="classes.php">
                <i class="fas fa-chalkboard me-2"></i> ক্লাস
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage == 'departments.php') ? 'active' : ''; ?>" href="departments.php">
                <i class="fas fa-building me-2"></i> বিভাগ
            </a>
        </li>

        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage == 'exams.php') ? 'active' : ''; ?>" href="exams.php">
                <i class="fas fa-file-alt me-2"></i> পরীক্ষা
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage == 'results.php') ? 'active' : ''; ?>" href="results.php">
                <i class="fas fa-chart-bar me-2"></i> ফলাফল
            </a>
        </li>

        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage == 'database_backup.php') ? 'active' : ''; ?>" href="database_backup.php">
                <i class="fas fa-database me-2"></i> ডাটাবেজ ব্যাকআপ
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage == 'server_setup.php') ? 'active' : ''; ?>" href="server_setup.php">
                <i class="fas fa-server me-2"></i> সার্ভার সেটআপ
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage == 'upload_content.php') ? 'active' : ''; ?>" href="../upload_content.php">
                <i class="fas fa-upload me-2"></i> কনটেন্ট আপলোড
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage == 'subject_csv_upload.php') ? 'active' : ''; ?>" href="subject_csv_upload.php">
                <i class="fas fa-file-csv me-2"></i> বিষয় CSV আপলোড
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage == 'csv_upload.php') ? 'active' : ''; ?>" href="csv_upload.php">
                <i class="fas fa-upload me-2"></i> CSV আপলোড
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage == 'update_features.php') ? 'active' : ''; ?>" href="../update_features.php">
                <i class="fas fa-cogs me-2"></i> বৈশিষ্ট্য আপডেট
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage == 'update_about.php') ? 'active' : ''; ?>" href="../update_about.php">
                <i class="fas fa-info-circle me-2"></i> আমাদের সম্পর্কে আপডেট
            </a>
        </li>

        <li class="nav-item">
            <a class="nav-link" href="../includes/logout.inc.php">
                <i class="fas fa-sign-out-alt me-2"></i> লগআউট
            </a>
        </li>
    </ul>
</div>
