<?php
// Read the file
$file = file_get_contents('detailed_marks_entry.php');

// Find the first occurrence of the function
$firstPos = strpos($file, "function getStudentsByClass");
if ($firstPos === false) {
    echo "Function not found!\n";
    exit;
}

// Find the second occurrence
$secondPos = strpos($file, "function getStudentsByClass", $firstPos + 1);
if ($secondPos === false) {
    echo "No duplicate function found!\n";
    exit;
}

// Find the closing brace of the second function
$openBraces = 0;
$closingPos = $secondPos;
$inFunction = false;

// Start searching from the function declaration
for ($i = $secondPos; $i < strlen($file); $i++) {
    $char = $file[$i];
    
    if ($char === '{') {
        $openBraces++;
        $inFunction = true;
    } elseif ($char === '}') {
        $openBraces--;
        
        // When we find the matching closing brace
        if ($inFunction && $openBraces === 0) {
            $closingPos = $i + 1; // Include the closing brace
            break;
        }
    }
}

// Remove the second function
$newFile = substr($file, 0, $secondPos);
$newFile .= substr($file, $closingPos);

// Save the modified file
file_put_contents('detailed_marks_entry_fixed.php', $newFile);

echo "Fixed file saved as detailed_marks_entry_fixed.php\n";
?> 