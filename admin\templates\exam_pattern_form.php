<?php
// Get edit data if available
$edit_data = isset($edit_data) ? $edit_data : null;
?>

<!-- Inline CSS -->
<style>
/* Exam <PERSON> CSS */
.exam-type-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
}

.exam-type-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.exam-type-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.exam-type-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.exam-type-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    font-size: 16px;
}

.exam-type-icon {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: white;
}

.exam-type-icon.cq {
    background-color: #4361ee;
}

.exam-type-icon.mcq {
    background-color: #2ecc71;
}

.exam-type-icon.practical {
    background-color: #3498db;
}

.exam-type-toggle {
    position: relative;
}

.exam-type-body {
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.exam-type-marks {
    display: flex;
    align-items: center;
    gap: 10px;
}

.marks-input-container {
    flex: 1;
    position: relative;
}

.marks-input {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.marks-input:focus {
    border-color: #4361ee;
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
    outline: none;
}

.marks-input:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
}

.marks-label {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    font-weight: 500;
}

.exam-type-info {
    font-size: 13px;
    color: #666;
    margin-top: 5px;
}

.exam-type-card.disabled {
    opacity: 0.6;
}

.exam-type-card.disabled .exam-type-body {
    display: none;
}

/* Progress bar styles */
.marks-distribution {
    margin-top: 20px;
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
}

.marks-distribution-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.marks-distribution-cards {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.distribution-card {
    flex: 1;
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.distribution-card-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 5px;
}

.distribution-card-value {
    font-size: 24px;
    font-weight: 700;
}

.distribution-card.cq .distribution-card-value {
    color: #4361ee;
}

.distribution-card.mcq .distribution-card-value {
    color: #2ecc71;
}

.distribution-card.practical .distribution-card-value {
    color: #3498db;
}

.marks-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.marks-total-label {
    font-weight: 600;
    color: #333;
}

.marks-total-value {
    font-weight: 700;
    padding: 5px 15px;
    border-radius: 20px;
    color: white;
}

.marks-progress {
    height: 15px;
    background-color: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.marks-progress-bar {
    height: 100%;
    float: left;
}

.marks-progress-bar.cq {
    background-color: #4361ee;
}

.marks-progress-bar.mcq {
    background-color: #2ecc71;
}

.marks-progress-bar.practical {
    background-color: #3498db;
}

.marks-info {
    font-size: 13px;
    color: #666;
}

/* Form switch custom styles */
.form-check-input:checked {
    background-color: #4361ee;
    border-color: #4361ee;
}

.form-check-input:focus {
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
    border-color: #4361ee;
}

/* Responsive styles */
@media (max-width: 768px) {
    .marks-distribution-cards {
        flex-direction: column;
    }
}
</style>

<div class="card animate__animated animate__fadeInLeft shadow">
    <div class="card-header bg-primary text-white">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-<?php echo $edit_data ? 'edit' : 'plus-circle'; ?> me-2"></i>
                <?php echo $edit_data ? 'পরীক্ষার প্যাটার্ন আপডেট করুন' : 'নতুন পরীক্ষার প্যাটার্ন যোগ করুন'; ?>
            </h5>
            <?php if ($edit_data): ?>
                <a href="subject_exam_pattern.php" class="btn btn-sm btn-light">
                    <i class="fas fa-plus-circle me-1"></i> নতুন যোগ করুন
                </a>
            <?php endif; ?>
        </div>
    </div>
    <div class="card-body">
        <form method="POST" action="" class="needs-validation" novalidate>
            <?php if ($edit_data): ?>
                <input type="hidden" name="edit_id" value="<?php echo $edit_data['id']; ?>">
            <?php endif; ?>

            <div class="mb-3">
                <label for="subject_id" class="form-label">
                    <i class="fas fa-book me-1 text-primary"></i> বিষয় নির্বাচন করুন
                </label>
                <select class="form-select" id="subject_id" name="subject_id" required>
                    <option value="">বিষয় নির্বাচন করুন</option>
                    <?php if ($subjects && $subjects->num_rows > 0): ?>
                        <?php while ($subject = $subjects->fetch_assoc()): ?>
                            <option value="<?php echo $subject['id']; ?>" <?php echo ($edit_data && $edit_data['subject_id'] == $subject['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($subject['subject_name'] . ' (' . $subject['subject_code'] . ')'); ?>
                            </option>
                        <?php endwhile; ?>
                    <?php endif; ?>
                </select>
                <div class="invalid-feedback">অনুগ্রহ করে একটি বিষয় নির্বাচন করুন।</div>
            </div>

            <div class="mb-3">
                <label for="total_marks" class="form-label">
                    <i class="fas fa-calculator me-1 text-primary"></i> মোট মার্কস
                </label>
                <input type="number" class="form-control" id="total_marks" name="total_marks" value="<?php echo $edit_data ? $edit_data['total_marks'] : '100'; ?>" min="1" required>
                <div class="invalid-feedback">অনুগ্রহ করে মোট মার্কস দিন।</div>
            </div>

            <div class="card mb-4 border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-sliders-h me-2 text-primary"></i> পরীক্ষার ধরন</h6>
                </div>
                <div class="card-body">
                    <div class="exam-type-container">
                        <!-- CQ (Written) Exam Type -->
                        <div class="exam-type-card" id="cq-card">
                            <div class="exam-type-header">
                                <div class="exam-type-title">
                                    <div class="exam-type-icon cq">
                                        <i class="fas fa-pen"></i>
                                    </div>
                                    <span>সিকিউ (লিখিত)</span>
                                </div>
                                <div class="exam-type-toggle">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input component-toggle" type="checkbox" id="has_cq" name="has_cq" checked data-target="cq_marks">
                                        <label class="form-check-label" for="has_cq">সক্রিয়</label>
                                    </div>
                                </div>
                            </div>
                            <div class="exam-type-body">
                                <div class="exam-type-marks">
                                    <div class="marks-input-container">
                                        <input type="number" class="marks-input distribution-component" id="cq_marks" name="cq_marks" value="<?php echo $edit_data ? $edit_data['cq_marks'] : '70'; ?>" min="0" step="0.01">
                                        <span class="marks-label">মার্কস</span>
                                    </div>
                                </div>
                                <div class="exam-type-info">
                                    <i class="fas fa-info-circle me-1"></i> লিখিত পরীক্ষার মার্কস
                                </div>
                            </div>
                        </div>

                        <!-- MCQ Exam Type -->
                        <div class="exam-type-card" id="mcq-card">
                            <div class="exam-type-header">
                                <div class="exam-type-title">
                                    <div class="exam-type-icon mcq">
                                        <i class="fas fa-tasks"></i>
                                    </div>
                                    <span>এমসিকিউ (বহুনির্বাচনী)</span>
                                </div>
                                <div class="exam-type-toggle">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input component-toggle" type="checkbox" id="has_mcq" name="has_mcq" data-target="mcq_marks" <?php echo ($edit_data && $edit_data['has_mcq']) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="has_mcq">সক্রিয়</label>
                                    </div>
                                </div>
                            </div>
                            <div class="exam-type-body">
                                <div class="exam-type-marks">
                                    <div class="marks-input-container">
                                        <input type="number" class="marks-input distribution-component" id="mcq_marks" name="mcq_marks" value="<?php echo $edit_data ? $edit_data['mcq_marks'] : '30'; ?>" min="0" step="0.01" <?php echo ($edit_data && !$edit_data['has_mcq']) ? 'disabled' : ''; ?>>
                                        <span class="marks-label">মার্কস</span>
                                    </div>
                                </div>
                                <div class="exam-type-info">
                                    <i class="fas fa-info-circle me-1"></i> বহুনির্বাচনী প্রশ্নের মার্কস
                                </div>
                            </div>
                        </div>

                        <!-- Practical Exam Type -->
                        <div class="exam-type-card" id="practical-card">
                            <div class="exam-type-header">
                                <div class="exam-type-title">
                                    <div class="exam-type-icon practical">
                                        <i class="fas fa-flask"></i>
                                    </div>
                                    <span>ব্যবহারিক</span>
                                </div>
                                <div class="exam-type-toggle">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input component-toggle" type="checkbox" id="has_practical" name="has_practical" data-target="practical_marks" <?php echo ($edit_data && $edit_data['has_practical']) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="has_practical">সক্রিয়</label>
                                    </div>
                                </div>
                            </div>
                            <div class="exam-type-body">
                                <div class="exam-type-marks">
                                    <div class="marks-input-container">
                                        <input type="number" class="marks-input distribution-component" id="practical_marks" name="practical_marks" value="<?php echo $edit_data ? $edit_data['practical_marks'] : '0'; ?>" min="0" step="0.01" <?php echo ($edit_data && !$edit_data['has_practical']) ? 'disabled' : ''; ?>>
                                        <span class="marks-label">মার্কস</span>
                                    </div>
                                </div>
                                <div class="exam-type-info">
                                    <i class="fas fa-info-circle me-1"></i> ব্যবহারিক পরীক্ষার মার্কস
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="marks-distribution">
                <div class="marks-distribution-title">
                    <i class="fas fa-chart-pie me-2"></i> মার্কস বিতরণ সারাংশ
                </div>
                <div class="marks-distribution-cards">
                    <div class="distribution-card cq" id="cq-distribution-card">
                        <div class="distribution-card-title">সিকিউ (লিখিত)</div>
                        <div class="distribution-card-value" id="cq-percentage">70%</div>
                    </div>
                    <div class="distribution-card mcq d-none" id="mcq-distribution-card">
                        <div class="distribution-card-title">এমসিকিউ</div>
                        <div class="distribution-card-value" id="mcq-percentage">30%</div>
                    </div>
                    <div class="distribution-card practical d-none" id="practical-distribution-card">
                        <div class="distribution-card-title">ব্যবহারিক</div>
                        <div class="distribution-card-value" id="practical-percentage">0%</div>
                    </div>
                </div>
                <div class="marks-total">
                    <div class="marks-total-label">মোট যোগফল:</div>
                    <div class="marks-total-value bg-primary" id="total-sum">100</div>
                </div>
                <div class="marks-progress">
                    <div class="marks-progress-bar cq" id="cq-progress" style="width: 100%;"></div>
                    <div class="marks-progress-bar mcq" id="mcq-progress" style="width: 0%;"></div>
                    <div class="marks-progress-bar practical" id="practical-progress" style="width: 0%;"></div>
                </div>
                <div class="marks-info">
                    <i class="fas fa-info-circle me-1"></i> সমস্ত উপাদানের যোগফল মোট মার্কসের সমান হতে হবে
                </div>
            </div>

            <div class="mb-3 mt-4">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" <?php echo (!$edit_data || ($edit_data && $edit_data['is_active'] == 1)) ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="is_active">সক্রিয়</label>
                </div>
            </div>

            <div class="d-grid gap-2">
                <button type="submit" name="add_pattern" class="btn btn-primary btn-lg" id="submitButton">
                    <i class="fas fa-<?php echo $edit_data ? 'sync' : 'save'; ?> me-2"></i> <?php echo $edit_data ? 'আপডেট করুন' : 'সংরক্ষণ করুন'; ?>
                </button>

                <?php if ($edit_data): ?>
                    <a href="subject_exam_pattern.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i> বাতিল করুন
                    </a>
                <?php endif; ?>
            </div>
        </form>
    </div>
</div>

<!-- Inline JavaScript -->
<script>
// Calculate total sum of distribution components
function calculateSum() {
    // Check if all required elements exist
    const totalMarksElement = document.getElementById('total_marks');
    const cqMarksElement = document.getElementById('cq_marks');
    const mcqMarksElement = document.getElementById('mcq_marks');
    const practicalMarksElement = document.getElementById('practical_marks');
    const totalSumElement = document.getElementById('total-sum');

    // Progress elements
    const cqProgressElement = document.getElementById('cq-progress');
    const mcqProgressElement = document.getElementById('mcq-progress');
    const practicalProgressElement = document.getElementById('practical-progress');

    // Percentage display elements
    const cqPercentageElement = document.getElementById('cq-percentage');
    const mcqPercentageElement = document.getElementById('mcq-percentage');
    const practicalPercentageElement = document.getElementById('practical-percentage');

    // Toggle checkboxes
    const hasCqCheckbox = document.getElementById('has_cq');
    const hasMcqCheckbox = document.getElementById('has_mcq');
    const hasPracticalCheckbox = document.getElementById('has_practical');

    // Distribution cards
    const mcqDistributionCard = document.getElementById('mcq-distribution-card');
    const practicalDistributionCard = document.getElementById('practical-distribution-card');

    if (!totalMarksElement || !cqMarksElement || !mcqMarksElement ||
        !practicalMarksElement || !totalSumElement) {
        console.error('One or more required elements not found');
        return;
    }

    let sum = 0;
    const totalMarks = parseFloat(totalMarksElement.value) || 100;

    // Get values from all distribution components, but only count enabled ones
    let cqMarks = 0;
    let mcqMarks = 0;
    let practicalMarks = 0;

    // Only count marks if the component is enabled (checkbox is checked)
    if (hasCqCheckbox && hasCqCheckbox.checked) {
        cqMarks = parseFloat(cqMarksElement.value) || 0;
    }

    if (hasMcqCheckbox && hasMcqCheckbox.checked) {
        mcqMarks = parseFloat(mcqMarksElement.value) || 0;
    }

    if (hasPracticalCheckbox && hasPracticalCheckbox.checked) {
        practicalMarks = parseFloat(practicalMarksElement.value) || 0;
    }

    // Calculate sum of enabled components only
    sum = cqMarks + mcqMarks + practicalMarks;

    // Debug information
    console.log('CQ Enabled:', hasCqCheckbox ? hasCqCheckbox.checked : 'N/A', 'Value:', cqMarks);
    console.log('MCQ Enabled:', hasMcqCheckbox ? hasMcqCheckbox.checked : 'N/A', 'Value:', mcqMarks);
    console.log('Practical Enabled:', hasPracticalCheckbox ? hasPracticalCheckbox.checked : 'N/A', 'Value:', practicalMarks);
    console.log('Total Sum:', sum, 'Total Marks:', totalMarks);

    // Update total sum display
    if (totalSumElement) {
        totalSumElement.textContent = sum.toFixed(0);
    }

    // Calculate percentages
    const cqPercentage = totalMarks > 0 ? (cqMarks / totalMarks) * 100 : 0;
    const mcqPercentage = totalMarks > 0 ? (mcqMarks / totalMarks) * 100 : 0;
    const practicalPercentage = totalMarks > 0 ? (practicalMarks / totalMarks) * 100 : 0;

    // Update percentage displays
    if (cqPercentageElement) cqPercentageElement.textContent = cqPercentage.toFixed(0) + '%';
    if (mcqPercentageElement) mcqPercentageElement.textContent = mcqPercentage.toFixed(0) + '%';
    if (practicalPercentageElement) practicalPercentageElement.textContent = practicalPercentage.toFixed(0) + '%';

    // Update progress bars
    if (cqProgressElement) {
        cqProgressElement.style.width = cqPercentage + '%';
    }

    if (mcqProgressElement) {
        mcqProgressElement.style.width = mcqPercentage + '%';
    }

    if (practicalProgressElement) {
        practicalProgressElement.style.width = practicalPercentage + '%';
    }

    // Update distribution cards visibility
    if (mcqDistributionCard) {
        if (hasMcqCheckbox && hasMcqCheckbox.checked) {
            mcqDistributionCard.classList.remove('d-none');
        } else {
            mcqDistributionCard.classList.add('d-none');
        }
    }

    if (practicalDistributionCard) {
        if (hasPracticalCheckbox && hasPracticalCheckbox.checked) {
            practicalDistributionCard.classList.remove('d-none');
        } else {
            practicalDistributionCard.classList.add('d-none');
        }
    }

    // Update total sum color based on whether it matches total marks
    if (totalSumElement) {
        if (Math.abs(sum - totalMarks) < 0.01) {
            totalSumElement.classList.remove('bg-danger', 'bg-warning');
            totalSumElement.classList.add('bg-primary');
        } else if (sum > totalMarks) {
            totalSumElement.classList.remove('bg-primary', 'bg-warning');
            totalSumElement.classList.add('bg-danger');
        } else {
            totalSumElement.classList.remove('bg-primary', 'bg-danger');
            totalSumElement.classList.add('bg-warning');
        }
    }

    // Auto-adjust values if needed
    const formElement = document.querySelector('form.needs-validation');
    const submitButton = formElement ? formElement.querySelector('button[type="submit"]') : null;

    if (submitButton) {
        if (Math.abs(sum - totalMarks) < 0.01) {
            // Sum matches total marks, enable submit button
            submitButton.disabled = false;

            // Remove any error message
            const errorElement = document.getElementById('sum-error');
            if (errorElement) {
                errorElement.style.display = 'none';
            }
        } else {
            // Sum doesn't match total marks, disable submit button
            submitButton.disabled = true;

            // Show error message
            let errorElement = document.getElementById('sum-error');
            if (!errorElement) {
                errorElement = document.createElement('div');
                errorElement.id = 'sum-error';
                errorElement.className = 'alert alert-danger mt-3';
                const formGroup = document.querySelector('.marks-distribution');
                if (formGroup) {
                    formGroup.appendChild(errorElement);
                }
            }

            errorElement.style.display = 'block';
            errorElement.innerHTML = `
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>ত্রুটি!</strong> সমস্ত উপাদানের যোগফল মোট মার্কসের সমান হতে হবে। বর্তমান যোগফল: ${sum}, মোট মার্কস: ${totalMarks}
                <button type="button" class="btn btn-sm btn-primary ms-3" id="auto-adjust-button">
                    <i class="fas fa-magic me-1"></i> অটো-অ্যাডজাস্ট করুন
                </button>
            `;

            // Add event listener to auto-adjust button
            const autoAdjustButton = document.getElementById('auto-adjust-button');
            if (autoAdjustButton) {
                autoAdjustButton.addEventListener('click', function() {
                    adjustAllComponents();
                    calculateSum();
                });
            }
        }
    }
}

// Toggle component function
function toggleComponent(checkbox) {
    const targetId = checkbox.dataset.target;
    const targetElement = document.getElementById(targetId);
    const cardElement = document.getElementById(targetId.replace('_marks', '-card'));

    if (targetElement) {
        targetElement.disabled = !checkbox.checked;

        // Update card styles
        if (cardElement) {
            if (checkbox.checked) {
                cardElement.classList.remove('disabled');
            } else {
                cardElement.classList.add('disabled');
            }
        }

        if (!checkbox.checked) {
            // If disabled, set value to 0
            targetElement.value = '0';
        } else if (targetElement.value === '0') {
            // If enabled and value is 0, set default value based on how many components are enabled
            const enabledComponents = document.querySelectorAll('.component-toggle:checked').length;

            // Get total marks
            const totalMarksElement = document.getElementById('total_marks');
            const totalMarks = parseFloat(totalMarksElement.value) || 100;

            if (enabledComponents === 1) {
                // If this is the only enabled component, set it to total marks
                targetElement.value = totalMarks;
            } else if (enabledComponents === 2) {
                // If there are 2 enabled components, distribute marks
                if (targetId === 'cq_marks') targetElement.value = Math.round(totalMarks * 0.7);
                if (targetId === 'mcq_marks') targetElement.value = Math.round(totalMarks * 0.3);
                if (targetId === 'practical_marks') targetElement.value = Math.round(totalMarks * 0.3);

                // Adjust other enabled component to make sum equal to total
                adjustOtherComponents(targetId);
            } else {
                // Default distribution for 3 components
                if (targetId === 'cq_marks') targetElement.value = Math.round(totalMarks * 0.6);
                if (targetId === 'mcq_marks') targetElement.value = Math.round(totalMarks * 0.3);
                if (targetId === 'practical_marks') targetElement.value = Math.round(totalMarks * 0.1);

                // Adjust other components to make sum equal to total
                adjustAllComponents();
            }
        }

        // Update calculations
        calculateSum();
    }
}

// Adjust other enabled components to make sum equal to total
function adjustOtherComponents(currentTargetId) {
    const totalMarksElement = document.getElementById('total_marks');
    const totalMarks = parseFloat(totalMarksElement.value) || 100;

    const cqMarksElement = document.getElementById('cq_marks');
    const mcqMarksElement = document.getElementById('mcq_marks');
    const practicalMarksElement = document.getElementById('practical_marks');

    const hasCqCheckbox = document.getElementById('has_cq');
    const hasMcqCheckbox = document.getElementById('has_mcq');
    const hasPracticalCheckbox = document.getElementById('has_practical');

    let currentValue = 0;
    if (currentTargetId === 'cq_marks' && !cqMarksElement.disabled) {
        currentValue = parseFloat(cqMarksElement.value) || 0;
    } else if (currentTargetId === 'mcq_marks' && !mcqMarksElement.disabled) {
        currentValue = parseFloat(mcqMarksElement.value) || 0;
    } else if (currentTargetId === 'practical_marks' && !practicalMarksElement.disabled) {
        currentValue = parseFloat(practicalMarksElement.value) || 0;
    }

    const remainingValue = totalMarks - currentValue;

    // Find the other enabled component and set its value
    if (currentTargetId !== 'cq_marks' && hasCqCheckbox.checked) {
        cqMarksElement.value = remainingValue;
    } else if (currentTargetId !== 'mcq_marks' && hasMcqCheckbox.checked) {
        mcqMarksElement.value = remainingValue;
    } else if (currentTargetId !== 'practical_marks' && hasPracticalCheckbox.checked) {
        practicalMarksElement.value = remainingValue;
    }
}

// Adjust all components to make sum equal to total
function adjustAllComponents() {
    const totalMarksElement = document.getElementById('total_marks');
    const totalMarks = parseFloat(totalMarksElement.value) || 100;

    const cqMarksElement = document.getElementById('cq_marks');
    const mcqMarksElement = document.getElementById('mcq_marks');
    const practicalMarksElement = document.getElementById('practical_marks');

    const hasCqCheckbox = document.getElementById('has_cq');
    const hasMcqCheckbox = document.getElementById('has_mcq');
    const hasPracticalCheckbox = document.getElementById('has_practical');

    // Count enabled components
    let enabledCount = 0;
    if (hasCqCheckbox.checked) enabledCount++;
    if (hasMcqCheckbox.checked) enabledCount++;
    if (hasPracticalCheckbox.checked) enabledCount++;

    if (enabledCount === 0) return;

    // Calculate values based on enabled components
    if (enabledCount === 1) {
        // If only one component is enabled, set it to total marks
        if (hasCqCheckbox.checked) cqMarksElement.value = totalMarks;
        if (hasMcqCheckbox.checked) mcqMarksElement.value = totalMarks;
        if (hasPracticalCheckbox.checked) practicalMarksElement.value = totalMarks;
    } else if (enabledCount === 2) {
        // If two components are enabled, distribute 70/30
        let firstValue = Math.round(totalMarks * 0.7);
        let secondValue = totalMarks - firstValue;

        if (hasCqCheckbox.checked && hasMcqCheckbox.checked) {
            cqMarksElement.value = firstValue;
            mcqMarksElement.value = secondValue;
        } else if (hasCqCheckbox.checked && hasPracticalCheckbox.checked) {
            cqMarksElement.value = firstValue;
            practicalMarksElement.value = secondValue;
        } else if (hasMcqCheckbox.checked && hasPracticalCheckbox.checked) {
            mcqMarksElement.value = firstValue;
            practicalMarksElement.value = secondValue;
        }
    } else if (enabledCount === 3) {
        // If all three components are enabled, distribute 60/30/10
        let cqValue = Math.round(totalMarks * 0.6);
        let mcqValue = Math.round(totalMarks * 0.3);
        let practicalValue = totalMarks - cqValue - mcqValue;

        cqMarksElement.value = cqValue;
        mcqMarksElement.value = mcqValue;
        practicalMarksElement.value = practicalValue;
    }
}

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to all distribution components if they exist
    const cqMarksElement = document.getElementById('cq_marks');
    const mcqMarksElement = document.getElementById('mcq_marks');
    const practicalMarksElement = document.getElementById('practical_marks');
    const totalMarksElement = document.getElementById('total_marks');

    // Add event listeners to input fields
    if (cqMarksElement) cqMarksElement.addEventListener('input', calculateSum);
    if (mcqMarksElement) mcqMarksElement.addEventListener('input', calculateSum);
    if (practicalMarksElement) practicalMarksElement.addEventListener('input', calculateSum);
    if (totalMarksElement) totalMarksElement.addEventListener('input', calculateSum);

    // Add event listeners to toggle checkboxes
    const toggleCheckboxes = document.querySelectorAll('.component-toggle');
    toggleCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            toggleComponent(this);
        });
    });

    // Initialize component states
    toggleCheckboxes.forEach(checkbox => {
        toggleComponent(checkbox);
    });

    // Calculate initial sum
    calculateSum();

    // Set form submission handler
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            // Get checkbox states
            const hasCqCheckbox = document.getElementById('has_cq');
            const hasMcqCheckbox = document.getElementById('has_mcq');
            const hasPracticalCheckbox = document.getElementById('has_practical');

            // Get input fields
            const cqMarksInput = document.getElementById('cq_marks');
            const mcqMarksInput = document.getElementById('mcq_marks');
            const practicalMarksInput = document.getElementById('practical_marks');

            // Set values to 0 for disabled components
            if (hasMcqCheckbox && !hasMcqCheckbox.checked && mcqMarksInput) {
                mcqMarksInput.disabled = false; // Temporarily enable to allow form submission
                mcqMarksInput.value = '0';
            }

            if (hasPracticalCheckbox && !hasPracticalCheckbox.checked && practicalMarksInput) {
                practicalMarksInput.disabled = false; // Temporarily enable to allow form submission
                practicalMarksInput.value = '0';
            }

            // Log values for debugging
            console.log('Form submission:');
            console.log('CQ Enabled:', hasCqCheckbox ? hasCqCheckbox.checked : 'N/A', 'Value:', cqMarksInput ? cqMarksInput.value : 'N/A');
            console.log('MCQ Enabled:', hasMcqCheckbox ? hasMcqCheckbox.checked : 'N/A', 'Value:', mcqMarksInput ? mcqMarksInput.value : 'N/A');
            console.log('Practical Enabled:', hasPracticalCheckbox ? hasPracticalCheckbox.checked : 'N/A', 'Value:', practicalMarksInput ? practicalMarksInput.value : 'N/A');
        });
    }
});
</script>
