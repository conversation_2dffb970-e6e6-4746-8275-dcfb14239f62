/* Hind Siliguri Font */
@import url('https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap');

/* Noto Sans Bengali as fallback */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@300;400;500;600;700&display=swap');

/* Apply font to all elements */
body, h1, h2, h3, h4, h5, h6, p, span, div, table, th, td, input, select, textarea, button, a {
    font-family: 'Hind Siliguri', 'Noto Sans Bengali', sans-serif !important;
}

/* Font weights */
.font-light {
    font-weight: 300;
}

.font-regular {
    font-weight: 400;
}

.font-medium {
    font-weight: 500;
}

.font-semibold {
    font-weight: 600;
}

.font-bold {
    font-weight: 700;
}

/* Bengali specific styles */
.bn-text {
    line-height: 1.6;
    letter-spacing: 0.2px;
}

/* Improve readability for Bengali text */
p, .paragraph {
    line-height: 1.8;
    margin-bottom: 1rem;
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
    line-height: 1.4;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

/* Tables */
table {
    font-size: 1rem;
}

/* Form elements */
input, select, textarea, button {
    font-size: 1rem;
}

/* Responsive font sizes */
@media (max-width: 768px) {
    body {
        font-size: 0.95rem;
    }
    
    h1 {
        font-size: 1.8rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    h3 {
        font-size: 1.3rem;
    }
}
