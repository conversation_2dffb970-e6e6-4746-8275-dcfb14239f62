<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../config.php';
require_once '../includes/functions.php';

// Handle book operations
$successMessage = $errorMessage = '';

// Add new book
if (isset($_POST['add_book'])) {
    $title = sanitize_input($_POST['title']);
    $author = sanitize_input($_POST['author']);
    $isbn = sanitize_input($_POST['isbn']);
    $publication_year = sanitize_input($_POST['publication_year']);
    $category = sanitize_input($_POST['category']);
    $quantity = sanitize_input($_POST['quantity']);
    
    $sql = "INSERT INTO library_books (title, author, isbn, publication_year, category, quantity, available_quantity) 
            VALUES ('$title', '$author', '$isbn', '$publication_year', '$category', $quantity, $quantity)";
    
    if ($conn->query($sql) === TRUE) {
        $successMessage = "বই সফলভাবে যোগ করা হয়েছে।";
    } else {
        $errorMessage = "ত্রুটি: " . $conn->error;
    }
}

// Issue book
if (isset($_POST['issue_book'])) {
    $book_id = sanitize_input($_POST['book_id']);
    $issuer_type = sanitize_input($_POST['issuer_type']);
    $user_id = sanitize_input($_POST['user_id']);
    $issue_date = sanitize_input($_POST['issue_date']);
    $due_date = sanitize_input($_POST['due_date']);
    
    // Start transaction
    $conn->begin_transaction();
    
    try {
        // Insert into book_issues table
        $sql = "INSERT INTO book_issues (book_id, issuer_type, user_id, issue_date, due_date, returned) 
                VALUES ($book_id, '$issuer_type', $user_id, '$issue_date', '$due_date', 0)";
        $conn->query($sql);
        
        // Update available_quantity in library_books table
        $sql = "UPDATE library_books SET available_quantity = available_quantity - 1 
                WHERE id = $book_id AND available_quantity > 0";
        $conn->query($sql);
        
        if ($conn->affected_rows > 0) {
            $conn->commit();
            $successMessage = "বই সফলভাবে ইস্যু করা হয়েছে।";
        } else {
            throw new Exception("বই উপলব্ধ নেই");
        }
    } catch (Exception $e) {
        $conn->rollback();
        $errorMessage = "ত্রুটি: " . $e->getMessage();
    }
}

// Update book information
if (isset($_POST['update_book'])) {
    $id = sanitize_input($_POST['edit_id']);
    $title = sanitize_input($_POST['edit_title']);
    $author = sanitize_input($_POST['edit_author']);
    $isbn = sanitize_input($_POST['edit_isbn']);
    $publication_year = sanitize_input($_POST['edit_publication_year']);
    $category = sanitize_input($_POST['edit_category']);
    $quantity = sanitize_input($_POST['edit_quantity']);
    
    // Get current quantity and available quantity
    $sql = "SELECT quantity, available_quantity FROM library_books WHERE id = $id";
    $result = $conn->query($sql);
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $current_quantity = $row['quantity'];
        $current_available = $row['available_quantity'];
        
        // Calculate the difference between available and total
        $issued_books = $current_quantity - $current_available;
        $new_available = $quantity - $issued_books;
        
        // Make sure available doesn't go negative
        if ($new_available < 0) {
            $errorMessage = "ত্রুটি: অবশিষ্ট বইয়ের সংখ্যা ঋণাত্মক হতে পারে না।";
        } else {
            $sql = "UPDATE library_books SET 
                    title = '$title', 
                    author = '$author', 
                    isbn = '$isbn', 
                    publication_year = '$publication_year', 
                    category = '$category', 
                    quantity = $quantity,
                    available_quantity = $new_available
                    WHERE id = $id";
            
            if ($conn->query($sql) === TRUE) {
                $successMessage = "বই সফলভাবে আপডেট করা হয়েছে।";
            } else {
                $errorMessage = "ত্রুটি: " . $conn->error;
            }
        }
    } else {
        $errorMessage = "ত্রুটি: বই খুঁজে পাওয়া যায়নি।";
    }
}

// Delete book
if (isset($_GET['delete'])) {
    $id = sanitize_input($_GET['delete']);
    
    $sql = "DELETE FROM library_books WHERE id = $id";
    
    if ($conn->query($sql) === TRUE) {
        $successMessage = "বই সফলভাবে মুছে ফেলা হয়েছে।";
    } else {
        $errorMessage = "ত্রুটি: " . $conn->error;
    }
}

// Create library_books table if not exists
$sql = "CREATE TABLE IF NOT EXISTS library_books (
    id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    author VARCHAR(255) NOT NULL,
    isbn VARCHAR(50),
    publication_year INT(4),
    category VARCHAR(100),
    quantity INT(11) NOT NULL DEFAULT 1,
    available_quantity INT(11) NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($sql) !== TRUE) {
    $errorMessage = "ত্রুটি: লাইব্রেরি টেবিল তৈরি করতে সমস্যা হয়েছে - " . $conn->error;
}

// Create book_issues table if not exists
$sql = "CREATE TABLE IF NOT EXISTS book_issues (
    id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    book_id INT(11) UNSIGNED NOT NULL,
    issuer_type ENUM('student', 'teacher') NOT NULL,
    user_id INT(11) UNSIGNED NOT NULL,
    issue_date DATE NOT NULL,
    return_date DATE,
    due_date DATE NOT NULL,
    returned TINYINT(1) NOT NULL DEFAULT 0,
    fine_amount DECIMAL(10,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES library_books(id) ON DELETE CASCADE
)";

if ($conn->query($sql) !== TRUE) {
    $errorMessage = "ত্রুটি: বই ইস্যু টেবিল তৈরি করতে সমস্যা হয়েছে - " . $conn->error;
}

// Fetch all books
$sql = "SELECT * FROM library_books ORDER BY title ASC";
$result = $conn->query($sql);

// Get active sessions
$sessionsQuery = "SELECT * FROM sessions ORDER BY session_name";
$sessions = $conn->query($sessionsQuery);

// Get active classes
$classesQuery = "SELECT * FROM classes ORDER BY class_name";
$classes = $conn->query($classesQuery);

// Get active departments
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Fetch sessions, classes, departments for dropdown options
$sessions = $conn->query("SELECT * FROM sessions WHERE status = 1 ORDER BY id DESC");
$classes = $conn->query("SELECT * FROM classes WHERE status = 1 ORDER BY id DESC");
$departments = $conn->query("SELECT * FROM departments WHERE status = 1 ORDER BY id DESC");

if(isset($_POST['action'])) {
    $action = $_POST['action'];
    
    if($action == 'add_book') {
        // ... existing code ...
    }
    
    // Update the issue_book function to support both student and teacher
    if($action == 'issue_book') {
        $book_id = $_POST['book_id'] ?? '';
        $issuer_type = $_POST['issuer_type'] ?? '';
        $user_id = $_POST['user_id'] ?? '';
        $issue_date = $_POST['issue_date'] ?? date('Y-m-d');
        $due_date = $_POST['due_date'] ?? date('Y-m-d', strtotime('+7 days'));
        $note = $_POST['note'] ?? '';
        
        // Validation
        if(empty($book_id) || empty($issuer_type) || empty($user_id) || empty($issue_date) || empty($due_date)) {
            $_SESSION['error'] = "সমস্ত প্রয়োজনীয় ক্ষেত্র পূরণ করুন।";
            header('Location: library.php');
            exit();
        }
        
        // Check if book is available
        $check_book = $conn->query("SELECT available_quantity FROM library_books WHERE id = '$book_id'");
        if($check_book && $check_book->num_rows > 0) {
            $book_data = $check_book->fetch_assoc();
            if($book_data['available_quantity'] <= 0) {
                $_SESSION['error'] = "বই ইস্যু করার জন্য পর্যাপ্ত পরিমাণে উপলব্ধ নেই।";
                header('Location: library.php');
                exit();
            }
        } else {
            $_SESSION['error'] = "বই খুঁজে পাওয়া যায়নি।";
            header('Location: library.php');
            exit();
        }
        
        // Start transaction
        $conn->begin_transaction();
        
        try {
            // Insert into book_issues table
            $insert_issue = $conn->prepare("INSERT INTO book_issues (book_id, issuer_type, user_id, issue_date, due_date, note) 
                                          VALUES (?, ?, ?, ?, ?, ?)");
            $insert_issue->bind_param("isssss", $book_id, $issuer_type, $user_id, $issue_date, $due_date, $note);
            $insert_issue->execute();
            
            // Update book available quantity
            $update_book = $conn->prepare("UPDATE library_books SET available_quantity = available_quantity - 1 WHERE id = ?");
            $update_book->bind_param("i", $book_id);
            $update_book->execute();
            
            $conn->commit();
            $_SESSION['success'] = "বই সফলভাবে ইস্যু করা হয়েছে।";
        } catch (Exception $e) {
            $conn->rollback();
            $_SESSION['error'] = "বই ইস্যু করার সময় ত্রুটি: " . $e->getMessage();
        }
        
        header('Location: library.php');
        exit();
    }
    
    // ... existing code ...
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>লাইব্রেরি ম্যানেজমেন্ট | কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <style>
        .book-card {
            transition: all 0.3s;
            height: 100%;
        }
        .book-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        /* Custom Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1055;
            width: 100%;
            height: 100%;
            overflow-x: hidden;
            overflow-y: auto;
            outline: 0;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .modal-dialog {
            position: relative;
            margin: 1.75rem auto;
            max-width: 500px;
        }
        
        .modal-open {
            overflow: hidden;
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-home me-2"></i> ড্যাশবোর্ড
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="students.php">
                                <i class="fas fa-user-graduate me-2"></i> ছাত্র-ছাত্রী
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="teachers.php">
                                <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="staff.php">
                                <i class="fas fa-user-tie me-2"></i> স্টাফ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="subjects.php">
                                <i class="fas fa-book me-2"></i> বিষয়সমূহ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="exams.php">
                                <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="fees.php">
                                <i class="fas fa-money-bill-wave me-2"></i> ফি সমূহ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="library.php">
                                <i class="fas fa-book-reader me-2"></i> লাইব্রেরি
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="events.php">
                                <i class="fas fa-calendar-day me-2"></i> ইভেন্ট
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="fas fa-cog me-2"></i> সেটিংস
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="nav-link text-danger" href="../logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="fas fa-book-reader"></i> লাইব্রেরি ম্যানেজমেন্ট</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="document.getElementById('addBookModal').style.display='block'; document.body.classList.add('modal-open');">
                                <i class="fas fa-plus"></i> নতুন বই যোগ করুন
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="document.getElementById('issueBookModal').style.display='block'; document.body.classList.add('modal-open');">
                                <i class="fas fa-share"></i> বই ইস্যু করুন
                            </button>
                        </div>
                    </div>
                </div>

                <?php if (!empty($successMessage)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $successMessage; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if (!empty($errorMessage)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $errorMessage; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <!-- Library stats -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h5 class="card-title">মোট বই</h5>
                                <?php
                                $sql = "SELECT SUM(quantity) as total FROM library_books";
                                $total = $conn->query($sql)->fetch_assoc()['total'] ?? 0;
                                ?>
                                <h2 class="card-text"><?php echo $total; ?></h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h5 class="card-title">অবশিষ্ট বই</h5>
                                <?php
                                $sql = "SELECT SUM(available_quantity) as available FROM library_books";
                                $available = $conn->query($sql)->fetch_assoc()['available'] ?? 0;
                                ?>
                                <h2 class="card-text"><?php echo $available; ?></h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body">
                                <h5 class="card-title">ইস্যু করা বই</h5>
                                <?php
                                $sql = "SELECT COUNT(*) as issued FROM book_issues WHERE returned = 0";
                                $issued = $conn->query($sql)->fetch_assoc()['issued'] ?? 0;
                                ?>
                                <h2 class="card-text"><?php echo $issued; ?></h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <h5 class="card-title">ওভারডিউ বই</h5>
                                <?php
                                $sql = "SELECT COUNT(*) as overdue FROM book_issues WHERE returned = 0 AND due_date < CURDATE()";
                                $overdue = $conn->query($sql)->fetch_assoc()['overdue'] ?? 0;
                                ?>
                                <h2 class="card-text"><?php echo $overdue; ?></h2>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Books list -->
                <h4 class="mb-3">বইয়ের তালিকা</h4>
                
                <!-- Search and filter -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <input type="text" id="searchBooks" class="form-control" placeholder="বইয়ের নাম, লেখক বা আইএসবিএন দিয়ে সার্চ করুন...">
                    </div>
                    <div class="col-md-3">
                        <select id="categoryFilter" class="form-control">
                            <option value="">সব ক্যাটাগরি</option>
                            <?php
                            $sql = "SELECT DISTINCT category FROM library_books ORDER BY category ASC";
                            $categories = $conn->query($sql);
                            if ($categories->num_rows > 0) {
                                while($cat = $categories->fetch_assoc()) {
                                    echo '<option value="'.$cat['category'].'">'.$cat['category'].'</option>';
                                }
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select id="availabilityFilter" class="form-control">
                            <option value="">সব বই</option>
                            <option value="available">শুধু উপলব্ধ বই</option>
                            <option value="unavailable">শুধু অনুপলব্ধ বই</option>
                        </select>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>বইয়ের নাম</th>
                                <th>লেখক</th>
                                <th>আইএসবিএন</th>
                                <th>প্রকাশনা বছর</th>
                                <th>ক্যাটাগরি</th>
                                <th>মোট কপি</th>
                                <th>উপলব্ধ কপি</th>
                                <th>অ্যাকশন</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            if ($result->num_rows > 0) {
                                $count = 1;
                                while($row = $result->fetch_assoc()) {
                                    echo "<tr>";
                                    echo "<td>" . $count++ . "</td>";
                                    echo "<td>" . $row['title'] . "</td>";
                                    echo "<td>" . $row['author'] . "</td>";
                                    echo "<td>" . $row['isbn'] . "</td>";
                                    echo "<td>" . $row['publication_year'] . "</td>";
                                    echo "<td>" . $row['category'] . "</td>";
                                    echo "<td>" . $row['quantity'] . "</td>";
                                    echo "<td>" . $row['available_quantity'] . "</td>";
                                    echo "<td>
                                        <div class='btn-group'>
                                            <button type='button' class='btn btn-sm btn-primary' onclick='editBook(\"".$row['id']."\", \"".$row['title']."\", \"".$row['author']."\", \"".$row['isbn']."\", \"".$row['publication_year']."\", \"".$row['category']."\", \"".$row['quantity']."\")'>
                                            <i class='fas fa-edit'></i></button>
                                            <a href='library.php?delete=".$row['id']."' class='btn btn-sm btn-danger' onclick=\"return confirm('আপনি কি নিশ্চিত যে আপনি এই বইটি মুছতে চান?');\"><i class='fas fa-trash'></i></a>
                                        </div>
                                    </td>";
                                    echo "</tr>";
                                }
                            } else {
                                echo "<tr><td colspan='9' class='text-center'>কোন বই পাওয়া যায়নি</td></tr>";
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </main>
        </div>
    </div>

    <!-- Add Book Modal -->
    <div class="modal" id="addBookModal" style="display: none;">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">নতুন বই যোগ করুন</h5>
                    <button type="button" class="btn-close" onclick="document.getElementById('addBookModal').style.display='none'; document.body.classList.remove('modal-open');"></button>
                </div>
                <form action="" method="post">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="title" class="form-label">বইয়ের নাম *</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        <div class="mb-3">
                            <label for="author" class="form-label">লেখক *</label>
                            <input type="text" class="form-control" id="author" name="author" required>
                        </div>
                        <div class="mb-3">
                            <label for="isbn" class="form-label">আইএসবিএন</label>
                            <input type="text" class="form-control" id="isbn" name="isbn">
                        </div>
                        <div class="mb-3">
                            <label for="publication_year" class="form-label">প্রকাশনা বছর</label>
                            <input type="number" class="form-control" id="publication_year" name="publication_year" min="1900" max="<?php echo date('Y'); ?>">
                        </div>
                        <div class="mb-3">
                            <label for="category" class="form-label">ক্যাটাগরি</label>
                            <input type="text" class="form-control" id="category" name="category" list="categories">
                            <datalist id="categories">
                                <?php
                                $sql = "SELECT DISTINCT category FROM library_books WHERE category != '' ORDER BY category ASC";
                                $categories = $conn->query($sql);
                                if ($categories->num_rows > 0) {
                                    while($cat = $categories->fetch_assoc()) {
                                        echo '<option value="'.$cat['category'].'">';
                                    }
                                }
                                ?>
                            </datalist>
                        </div>
                        <div class="mb-3">
                            <label for="quantity" class="form-label">সংখ্যা *</label>
                            <input type="number" class="form-control" id="quantity" name="quantity" value="1" min="1" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="document.getElementById('addBookModal').style.display='none'; document.body.classList.remove('modal-open');">বাতিল করুন</button>
                        <button type="submit" name="add_book" class="btn btn-primary">যোগ করুন</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Issue Book Modal -->
    <div class="modal" id="issueBookModal" style="display: none;">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">বই ইস্যু করুন</h5>
                    <button type="button" class="btn-close" onclick="document.getElementById('issueBookModal').style.display='none'; document.body.classList.remove('modal-open');"></button>
                </div>
                <form action="" method="post">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="book_id" class="form-label">বই *</label>
                            <select class="form-control" id="book_id" name="book_id" required>
                                <option value="">বই নির্বাচন করুন</option>
                                <?php
                                $sql = "SELECT id, title, author FROM library_books WHERE available_quantity > 0 ORDER BY title ASC";
                                $books = $conn->query($sql);
                                if ($books->num_rows > 0) {
                                    while($book = $books->fetch_assoc()) {
                                        echo '<option value="'.$book['id'].'">'.$book['title'].' - '.$book['author'].'</option>';
                                    }
                                }
                                ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="issuer_type" class="form-label">ইস্যুকারী প্রকার</label>
                            <select class="form-control" id="issuer_type" name="issuer_type" required onchange="showUserSelection()">
                                <option value="">ইস্যুকারী প্রকার নির্বাচন করুন</option>
                                <option value="student">শিক্ষার্থী</option>
                                <option value="teacher">শিক্ষক</option>
                            </select>
                        </div>
                        
                        <!-- Student Selection (Initially Hidden) -->
                        <div id="student_selection" style="display:none;">
                            <div class="mb-3">
                                <label for="session_id" class="form-label">সেশন</label>
                                <select class="form-control" id="session_id" name="session_id" onchange="loadClasses()">
                                    <option value="">সেশন নির্বাচন করুন</option>
                                    <?php
                                    if ($sessions && $sessions->num_rows > 0) {
                                        while($session = $sessions->fetch_assoc()) {
                                            echo '<option value="'.$session['id'].'">'.$session['session_name'].'</option>';
                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="class_id" class="form-label">ক্লাস</label>
                                <select class="form-control" id="class_id" name="class_id" onchange="loadStudents()">
                                    <option value="">ক্লাস নির্বাচন করুন</option>
                                    <?php
                                    if ($classes && $classes->num_rows > 0) {
                                        while($class = $classes->fetch_assoc()) {
                                            echo '<option value="'.$class['id'].'">'.$class['class_name'].'</option>';
                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="department_id" class="form-label">বিভাগ</label>
                                <select class="form-control" id="department_id" name="department_id" onchange="loadStudents()">
                                    <option value="">বিভাগ নির্বাচন করুন</option>
                                    <?php
                                    if ($departments && $departments->num_rows > 0) {
                                        while($department = $departments->fetch_assoc()) {
                                            echo '<option value="'.$department['id'].'">'.$department['department_name'].'</option>';
                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="student_id" class="form-label">শিক্ষার্থী *</label>
                                <select class="form-control" id="student_id" name="user_id" required>
                                    <option value="">শিক্ষার্থী নির্বাচন করুন</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Teacher Selection (Initially Hidden) -->
                        <div id="teacher_selection" style="display:none;">
                            <div class="mb-3">
                                <label for="department_id_teacher" class="form-label">বিভাগ</label>
                                <select class="form-control" id="department_id_teacher" name="department_id_teacher" onchange="loadTeachers()">
                                    <option value="">বিভাগ নির্বাচন করুন</option>
                                    <?php
                                    if ($departments && $departments->num_rows > 0) {
                                        $departments->data_seek(0); // Reset pointer
                                        while($department = $departments->fetch_assoc()) {
                                            echo '<option value="'.$department['id'].'">'.$department['department_name'].'</option>';
                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="teacher_id" class="form-label">শিক্ষক *</label>
                                <select class="form-control" id="teacher_id" name="user_id">
                                    <option value="">শিক্ষক নির্বাচন করুন</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="issue_date" class="form-label">ইস্যু তারিখ *</label>
                            <input type="date" class="form-control" id="issue_date" name="issue_date" value="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="due_date" class="form-label">ফেরত তারিখ *</label>
                            <input type="date" class="form-control" id="due_date" name="due_date" value="<?php echo date('Y-m-d', strtotime('+15 days')); ?>" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="document.getElementById('issueBookModal').style.display='none'; document.body.classList.remove('modal-open');">বাতিল করুন</button>
                        <button type="submit" name="issue_book" class="btn btn-primary">ইস্যু করুন</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Book Modal -->
    <div class="modal" id="editBookModal" style="display: none;">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">বই সম্পাদনা করুন</h5>
                    <button type="button" class="btn-close" onclick="document.getElementById('editBookModal').style.display='none'; document.body.classList.remove('modal-open');"></button>
                </div>
                <form action="" method="post">
                    <div class="modal-body">
                        <input type="hidden" id="edit_id" name="edit_id">
                        <div class="mb-3">
                            <label for="edit_title" class="form-label">বইয়ের নাম *</label>
                            <input type="text" class="form-control" id="edit_title" name="edit_title" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_author" class="form-label">লেখক *</label>
                            <input type="text" class="form-control" id="edit_author" name="edit_author" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_isbn" class="form-label">আইএসবিএন</label>
                            <input type="text" class="form-control" id="edit_isbn" name="edit_isbn">
                        </div>
                        <div class="mb-3">
                            <label for="edit_publication_year" class="form-label">প্রকাশনা বছর</label>
                            <input type="number" class="form-control" id="edit_publication_year" name="edit_publication_year" min="1900" max="<?php echo date('Y'); ?>">
                        </div>
                        <div class="mb-3">
                            <label for="edit_category" class="form-label">ক্যাটাগরি</label>
                            <input type="text" class="form-control" id="edit_category" name="edit_category">
                        </div>
                        <div class="mb-3">
                            <label for="edit_quantity" class="form-label">সংখ্যা *</label>
                            <input type="number" class="form-control" id="edit_quantity" name="edit_quantity" min="1" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="document.getElementById('editBookModal').style.display='none'; document.body.classList.remove('modal-open');">বাতিল করুন</button>
                        <button type="submit" name="update_book" class="btn btn-primary">আপডেট করুন</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Make sure Bootstrap is properly loaded and available
        document.addEventListener('DOMContentLoaded', function() {
            // Direct bootstrap 5 modal initialization
            var myModals = {
                addBookModal: new bootstrap.Modal(document.getElementById('addBookModal')),
                issueBookModal: new bootstrap.Modal(document.getElementById('issueBookModal')),
                editBookModal: new bootstrap.Modal(document.getElementById('editBookModal'))
            };
            
            // Add Book button
            document.querySelector('button[data-bs-target="#addBookModal"]').onclick = function(e) {
                e.preventDefault();
                myModals.addBookModal.show();
            };

            // Issue Book button
            document.querySelector('button[data-bs-target="#issueBookModal"]').onclick = function(e) {
                e.preventDefault();
                myModals.issueBookModal.show();
            };
            
            // Edit Book buttons
            document.querySelectorAll('button[data-bs-target="#editBookModal"]').forEach(function(button) {
                button.onclick = function(e) {
                    e.preventDefault();
                    
                    // Populate the form
                    document.getElementById('edit_id').value = this.getAttribute('data-id');
                    document.getElementById('edit_title').value = this.getAttribute('data-title');
                    document.getElementById('edit_author').value = this.getAttribute('data-author');
                    document.getElementById('edit_isbn').value = this.getAttribute('data-isbn');
                    document.getElementById('edit_publication_year').value = this.getAttribute('data-year');
                    document.getElementById('edit_category').value = this.getAttribute('data-category');
                    document.getElementById('edit_quantity').value = this.getAttribute('data-quantity');
                    
                    // Show the modal
                    myModals.editBookModal.show();
                };
            });
        });

        // Search functionality
        document.getElementById('searchBooks').addEventListener('keyup', function() {
            const searchValue = this.value.toLowerCase();
            const rows = document.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const title = row.cells[1].innerText.toLowerCase();
                const author = row.cells[2].innerText.toLowerCase();
                const isbn = row.cells[3].innerText.toLowerCase();
                
                if (title.includes(searchValue) || author.includes(searchValue) || isbn.includes(searchValue)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });

        // Category filter
        document.getElementById('categoryFilter').addEventListener('change', function() {
            const categoryValue = this.value.toLowerCase();
            const rows = document.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                if (!categoryValue || row.cells[5].innerText.toLowerCase() === categoryValue) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });

        // Availability filter
        document.getElementById('availabilityFilter').addEventListener('change', function() {
            const availabilityValue = this.value;
            const rows = document.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                if (row.cells.length > 7) {
                    const available = parseInt(row.cells[7].innerText);
                    const total = parseInt(row.cells[6].innerText);
                    
                    if (availabilityValue === 'available' && available > 0) {
                        row.style.display = '';
                    } else if (availabilityValue === 'unavailable' && available === 0) {
                        row.style.display = '';
                    } else if (availabilityValue === '') {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                }
            });
        });

        // Add JavaScript function for editing books
        function editBook(id, title, author, isbn, year, category, quantity) {
            document.getElementById('edit_id').value = id;
            document.getElementById('edit_title').value = title;
            document.getElementById('edit_author').value = author;
            document.getElementById('edit_isbn').value = isbn;
            document.getElementById('edit_publication_year').value = year;
            document.getElementById('edit_category').value = category;
            document.getElementById('edit_quantity').value = quantity;
            
            document.getElementById('editBookModal').style.display = 'block';
            document.body.classList.add('modal-open');
        }
    </script>
</body>
</html> 