<?php
// Database connection
require_once 'includes/dbh.inc.php';

echo "<h2>Creating Subjects Tables</h2>";

// First, check if the database exists
echo "<p>Checking database connection...</p>";
if ($conn->connect_error) {
    die("<p>Connection failed: " . $conn->connect_error . "</p>");
} else {
    echo "<p>Database connection successful!</p>";
}

// Check if departments table exists
$checkDepartmentsTable = $conn->query("SHOW TABLES LIKE 'departments'");
if ($checkDepartmentsTable->num_rows == 0) {
    echo "<p>Error: The departments table does not exist. Please run create_essential_tables.php first.</p>";
    echo "<p><a href='create_essential_tables.php' class='btn btn-primary'>Create Essential Tables</a></p>";
    exit();
}

// Create subjects table without foreign key constraint
$subjectsTableQuery = "CREATE TABLE IF NOT EXISTS subjects (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    subject_name VARCHAR(100) NOT NULL,
    subject_code VARCHAR(20) NOT NULL,
    department_id INT(11) NULL,
    category VARCHAR(255) DEFAULT 'required',
    description TEXT,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($subjectsTableQuery)) {
    echo "<p>Subjects table created successfully!</p>";
    
    // Insert a sample subject
    $checkSubject = $conn->query("SELECT * FROM subjects LIMIT 1");
    if ($checkSubject && $checkSubject->num_rows == 0) {
        $insertSubject = $conn->query("INSERT INTO subjects (subject_name, subject_code, category, description, is_active) 
                                     VALUES ('Mathematics', 'MATH-101', 'required', 'Basic mathematics course', 1)");
        if ($insertSubject) {
            echo "<p>Sample subject created successfully!</p>";
        } else {
            echo "<p>Error creating sample subject: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Subjects already exist.</p>";
    }
} else {
    echo "<p>Error creating subjects table: " . $conn->error . "</p>";
}

// Create a mapping table for subjects to departments (many-to-many)
$subjectDeptTableQuery = "CREATE TABLE IF NOT EXISTS subject_departments (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    subject_id INT(11) NOT NULL,
    department_id INT(11) NOT NULL,
    UNIQUE KEY (subject_id, department_id)
)";

if ($conn->query($subjectDeptTableQuery)) {
    echo "<p>Subject departments mapping table created successfully!</p>";
    
    // Insert a sample mapping if subjects and departments exist
    $checkSubject = $conn->query("SELECT id FROM subjects LIMIT 1");
    $checkDepartment = $conn->query("SELECT id FROM departments LIMIT 1");
    
    if ($checkSubject && $checkSubject->num_rows > 0 && $checkDepartment && $checkDepartment->num_rows > 0) {
        $subjectId = $checkSubject->fetch_assoc()['id'];
        $departmentId = $checkDepartment->fetch_assoc()['id'];
        
        // Check if mapping already exists
        $checkMapping = $conn->query("SELECT * FROM subject_departments WHERE subject_id = $subjectId AND department_id = $departmentId");
        
        if ($checkMapping && $checkMapping->num_rows == 0) {
            $insertMapping = $conn->query("INSERT INTO subject_departments (subject_id, department_id) VALUES ($subjectId, $departmentId)");
            if ($insertMapping) {
                echo "<p>Sample subject-department mapping created successfully!</p>";
            } else {
                echo "<p>Error creating sample mapping: " . $conn->error . "</p>";
            }
        } else {
            echo "<p>Subject-department mapping already exists.</p>";
        }
    }
} else {
    echo "<p>Error creating subject departments mapping table: " . $conn->error . "</p>";
}

echo "<p><strong>All subjects tables have been created successfully!</strong></p>";
echo "<p>Note: Foreign key constraints have been omitted for simplicity. The tables will still work for basic functionality.</p>";
echo "<p><a href='index.php'>Return to homepage</a></p>";
echo "<p><a href='admin/dashboard.php'>Go to Admin Dashboard</a></p>";
echo "<p><a href='admin/subjects.php'>Go to Subjects Management</a></p>";

$conn->close();
?>
