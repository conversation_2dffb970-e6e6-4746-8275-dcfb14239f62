<?php
// Database Connection
require_once 'includes/dbh.inc.php';

// Check if gender column exists
$checkGender = $conn->query("SHOW COLUMNS FROM students LIKE 'gender'");
if ($checkGender && $checkGender->num_rows > 0) {
    echo "<p style='color:green'>The 'gender' column exists in the students table.</p>";
    
    // Show the column details
    $genderColumn = $checkGender->fetch_assoc();
    echo "<h3>Gender Column Details:</h3>";
    echo "<ul>";
    foreach ($genderColumn as $key => $value) {
        echo "<li><strong>$key:</strong> " . ($value === NULL ? 'NULL' : $value) . "</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color:red'>The 'gender' column does NOT exist in the students table.</p>";
    
    // Show all columns
    echo "<h3>Available columns in students table:</h3>";
    $allColumns = $conn->query("SHOW COLUMNS FROM students");
    if ($allColumns && $allColumns->num_rows > 0) {
        echo "<ul>";
        while ($column = $allColumns->fetch_assoc()) {
            echo "<li>" . $column['Field'] . " - " . $column['Type'] . "</li>";
        }
        echo "</ul>";
    }
}

// Close connection
$conn->close();
?>
