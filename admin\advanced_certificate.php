<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Initialize variables
$students = [];
$filteredStudents = false;
$errorMessage = '';
$successMessage = '';

// Check if tables exist and create them if they don't
$tables = ['sessions', 'departments', 'classes'];
foreach ($tables as $table) {
    $tableCheck = $conn->query("SHOW TABLES LIKE '$table'");
    if ($tableCheck->num_rows == 0) {
        switch ($table) {
            case 'sessions':
                $conn->query("CREATE TABLE sessions (
                    id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
                    session_name VARCHAR(100) NOT NULL,
                    start_date DATE,
                    end_date DATE,
                    is_active TINYINT(1) DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )");
                // Add some default sessions
                $conn->query("INSERT INTO sessions (session_name, start_date, end_date, is_active) VALUES
                    ('2023-2024', '2023-01-01', '2024-12-31', 1),
                    ('2022-2023', '2022-01-01', '2023-12-31', 0)");
                break;
            case 'departments':
                $conn->query("CREATE TABLE departments (
                    id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
                    department_name VARCHAR(100) NOT NULL,
                    department_code VARCHAR(20),
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )");
                // Add some default departments
                $conn->query("INSERT INTO departments (department_name, department_code) VALUES
                    ('Science', 'SCI'),
                    ('Arts', 'ARTS'),
                    ('Commerce', 'COM')");
                break;
            case 'classes':
                $conn->query("CREATE TABLE classes (
                    id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
                    class_name VARCHAR(100) NOT NULL,
                    class_code VARCHAR(20),
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )");
                // Add some default classes
                $conn->query("INSERT INTO classes (class_name, class_code) VALUES
                    ('Class 9', 'C9'),
                    ('Class 10', 'C10'),
                    ('Class 11', 'C11'),
                    ('Class 12', 'C12')");
                break;
        }
    }
}

// Get all sessions
$sessions = $conn->query("SELECT * FROM sessions ORDER BY session_name");

// Get all departments
$departments = $conn->query("SELECT * FROM departments ORDER BY department_name");

// Get all classes
$classes = $conn->query("SELECT * FROM classes ORDER BY class_name");

// Process filter form submission
if (isset($_POST['filter_students'])) {
    $whereConditions = [];
    $params = [];
    $types = '';

    // Filter by session
    if (!empty($_POST['session_id'])) {
        $whereConditions[] = "s.session_id = ?";
        $params[] = $_POST['session_id'];
        $types .= 'i';
    }

    // Filter by department
    if (!empty($_POST['department_id'])) {
        $whereConditions[] = "s.department_id = ?";
        $params[] = $_POST['department_id'];
        $types .= 'i';
    }

    // Filter by class
    if (!empty($_POST['class_id'])) {
        $whereConditions[] = "s.class_id = ?";
        $params[] = $_POST['class_id'];
        $types .= 'i';
    }

    // Filter by student ID
    if (!empty($_POST['student_id'])) {
        $whereConditions[] = "s.student_id LIKE ?";
        $params[] = '%' . $_POST['student_id'] . '%';
        $types .= 's';
    }

    // Filter by name
    if (!empty($_POST['student_name'])) {
        $whereConditions[] = "(s.first_name LIKE ? OR s.last_name LIKE ?)";
        $params[] = '%' . $_POST['student_name'] . '%';
        $params[] = '%' . $_POST['student_name'] . '%';
        $types .= 'ss';
    }

    // Filter by roll number
    if (!empty($_POST['roll_number'])) {
        $whereConditions[] = "s.roll_number LIKE ?";
        $params[] = '%' . $_POST['roll_number'] . '%';
        $types .= 's';
    }

    // Build the query
    $query = "SELECT s.*, d.department_name, c.class_name, ss.session_name
              FROM students s
              LEFT JOIN departments d ON s.department_id = d.id
              LEFT JOIN classes c ON s.class_id = c.id
              LEFT JOIN sessions ss ON s.session_id = ss.id";

    if (!empty($whereConditions)) {
        $query .= " WHERE " . implode(" AND ", $whereConditions);
    }

    $query .= " ORDER BY s.first_name, s.last_name";

    // Prepare and execute the query
    $stmt = $conn->prepare($query);

    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }

    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $students[] = $row;
        }
        $filteredStudents = true;
    } else {
        $errorMessage = "কোন শিক্ষার্থী পাওয়া যায়নি। অনুগ্রহ করে অন্য ফিল্টার ব্যবহার করুন।";
    }
}

// Process certificate generation
if (isset($_POST['generate_certificate']) && isset($_POST['selected_students'])) {
    $selectedStudents = $_POST['selected_students'];

    if (empty($selectedStudents)) {
        $errorMessage = "অনুগ্রহ করে কমপক্ষে একজন শিক্ষার্থী নির্বাচন করুন।";
    } else {
        // Redirect to certificate generation page with selected student IDs
        $studentIds = implode(',', $selectedStudents);
        header("Location: generate_certificates.php?student_ids=" . $studentIds);
        exit();
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>উন্নত সার্টিফিকেট - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    

    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
        }

        .filter-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .student-card {
            transition: all 0.3s ease;
            border-radius: 10px;
            overflow: hidden;
        }
        .student-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        .student-card .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        .select-all-container {
            margin-bottom: 15px;
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="certificates.php">
                            <i class="fas fa-certificate me-2"></i> সার্টিফিকেট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="simple_certificate.php">
                            <i class="fas fa-file-alt me-2"></i> সাধারণ সার্টিফিকেট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="upload_watermark.php">
                            <i class="fas fa-image me-2"></i> ওয়াটারমার্ক আপলোড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>উন্নত সার্টিফিকেট সিস্টেম</h2>
                        <p class="text-muted">শিক্ষার্থীদের ফিল্টার করে সার্টিফিকেট তৈরি করুন</p>
                    </div>
                </div>

                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Filter Card -->
                <div class="card filter-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>শিক্ষার্থী ফিল্টার করুন</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="session_id" class="form-label">সেশন</label>
                                    <select class="form-select" id="session_id" name="session_id">
                                        <option value="">সব সেশন</option>
                                        <?php if ($sessions && $sessions->num_rows > 0): ?>
                                            <?php while ($session = $sessions->fetch_assoc()): ?>
                                                <option value="<?php echo $session['id']; ?>" <?php echo (isset($_POST['session_id']) && $_POST['session_id'] == $session['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($session['session_name']); ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="department_id" class="form-label">বিভাগ</label>
                                    <select class="form-select" id="department_id" name="department_id">
                                        <option value="">সব বিভাগ</option>
                                        <?php if ($departments && $departments->num_rows > 0): ?>
                                            <?php while ($department = $departments->fetch_assoc()): ?>
                                                <option value="<?php echo $department['id']; ?>" <?php echo (isset($_POST['department_id']) && $_POST['department_id'] == $department['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($department['department_name']); ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="class_id" class="form-label">শ্রেণী</label>
                                    <select class="form-select" id="class_id" name="class_id">
                                        <option value="">সব শ্রেণী</option>
                                        <?php if ($classes && $classes->num_rows > 0): ?>
                                            <?php while ($class = $classes->fetch_assoc()): ?>
                                                <option value="<?php echo $class['id']; ?>" <?php echo (isset($_POST['class_id']) && $_POST['class_id'] == $class['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($class['class_name']); ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="student_id" class="form-label">শিক্ষার্থী আইডি</label>
                                    <input type="text" class="form-control" id="student_id" name="student_id" value="<?php echo isset($_POST['student_id']) ? htmlspecialchars($_POST['student_id']) : ''; ?>" placeholder="শিক্ষার্থী আইডি লিখুন">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="student_name" class="form-label">শিক্ষার্থীর নাম</label>
                                    <input type="text" class="form-control" id="student_name" name="student_name" value="<?php echo isset($_POST['student_name']) ? htmlspecialchars($_POST['student_name']) : ''; ?>" placeholder="শিক্ষার্থীর নাম লিখুন">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="roll_number" class="form-label">রোল নম্বর</label>
                                    <input type="text" class="form-control" id="roll_number" name="roll_number" value="<?php echo isset($_POST['roll_number']) ? htmlspecialchars($_POST['roll_number']) : ''; ?>" placeholder="রোল নম্বর লিখুন">
                                </div>
                            </div>
                            <div class="text-center mt-3">
                                <button type="submit" name="filter_students" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>শিক্ষার্থী খুঁজুন
                                </button>
                                <button type="reset" class="btn btn-secondary ms-2">
                                    <i class="fas fa-redo me-2"></i>রিসেট করুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Students List -->
                <?php if ($filteredStudents): ?>
                    <form method="POST" action="">
                        <div class="card">
                            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="fas fa-users me-2"></i>ফিল্টার করা শিক্ষার্থী (<?php echo count($students); ?>)</h5>
                                <button type="submit" name="generate_certificate" class="btn btn-light">
                                    <i class="fas fa-certificate me-2"></i>সার্টিফিকেট তৈরি করুন
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="select-all-container">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="select-all">
                                        <label class="form-check-label" for="select-all">
                                            সব নির্বাচন করুন
                                        </label>
                                    </div>
                                </div>

                                <div class="row">
                                    <?php foreach ($students as $student): ?>
                                        <div class="col-md-6 col-lg-4 mb-4">
                                            <div class="card student-card h-100">
                                                <div class="card-header d-flex justify-content-between align-items-center">
                                                    <div class="form-check">
                                                        <input class="form-check-input student-checkbox" type="checkbox" name="selected_students[]" value="<?php echo $student['id']; ?>" id="student-<?php echo $student['id']; ?>">
                                                        <label class="form-check-label" for="student-<?php echo $student['id']; ?>">
                                                            <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <div class="d-flex mb-3">
                                                        <?php if (!empty($student['profile_photo'])): ?>
                                                            <img src="<?php echo htmlspecialchars($student['profile_photo']); ?>" alt="Profile Photo" class="rounded-circle me-3" style="width: 60px; height: 60px; object-fit: cover;">
                                                        <?php else: ?>
                                                            <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center me-3" style="width: 60px; height: 60px; color: white;">
                                                                <i class="fas fa-user fa-2x"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                        <div>
                                                            <p class="mb-1"><strong>আইডি:</strong> <?php echo htmlspecialchars($student['student_id']); ?></p>
                                                            <p class="mb-1"><strong>রোল:</strong> <?php echo htmlspecialchars($student['roll_number']); ?></p>
                                                        </div>
                                                    </div>
                                                    <p class="mb-1"><strong>বিভাগ:</strong> <?php echo htmlspecialchars($student['department_name'] ?? 'N/A'); ?></p>
                                                    <p class="mb-1"><strong>শ্রেণী:</strong> <?php echo htmlspecialchars($student['class_name'] ?? 'N/A'); ?></p>
                                                    <p class="mb-0"><strong>সেশন:</strong> <?php echo htmlspecialchars($student['session_name'] ?? 'N/A'); ?></p>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <div class="card-footer text-center">
                                <button type="submit" name="generate_certificate" class="btn btn-primary btn-lg">
                                    <i class="fas fa-certificate me-2"></i>নির্বাচিত শিক্ষার্থীদের সার্টিফিকেট তৈরি করুন
                                </button>
                            </div>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        // Select all checkbox functionality
        document.getElementById('select-all').addEventListener('change', function() {
            var checkboxes = document.getElementsByClassName('student-checkbox');
            for (var i = 0; i < checkboxes.length; i++) {
                checkboxes[i].checked = this.checked;
            }
        });

        // Update select all checkbox when individual checkboxes change
        var studentCheckboxes = document.getElementsByClassName('student-checkbox');
        for (var i = 0; i < studentCheckboxes.length; i++) {
            studentCheckboxes[i].addEventListener('change', function() {
                var allChecked = true;
                for (var j = 0; j < studentCheckboxes.length; j++) {
                    if (!studentCheckboxes[j].checked) {
                        allChecked = false;
                        break;
                    }
                }
                document.getElementById('select-all').checked = allChecked;
            });
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
</body>
</html>
