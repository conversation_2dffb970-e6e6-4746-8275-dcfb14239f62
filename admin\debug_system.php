<?php
session_start();
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

echo "<h2>সিস্টেম ডিবাগিং</h2>";

try {
    echo "<h3>১. ডাটাবেস কানেকশন</h3>";
    if ($conn) {
        echo "<p style='color: green;'>✅ ডাটাবেস কানেকশন সফল</p>";
    } else {
        echo "<p style='color: red;'>❌ ডাটাবেস কানেকশন ব্যর্থ</p>";
    }

    echo "<h3>২. টেবিল চেক</h3>";
    $tables = ['students', 'classes', 'fees', 'fee_payments'];
    $tableStatus = [];
    
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            $count = $conn->query("SELECT COUNT(*) as count FROM $table")->fetch_assoc()['count'];
            echo "<p style='color: green;'>✅ $table টেবিল আছে ($count রেকর্ড)</p>";
            $tableStatus[$table] = true;
        } else {
            echo "<p style='color: red;'>❌ $table টেবিল নেই</p>";
            $tableStatus[$table] = false;
        }
    }

    echo "<h3>৩. নমুনা ডেটা তৈরি</h3>";
    
    if (!$tableStatus['classes']) {
        echo "<p>ক্লাস টেবিল তৈরি করছি...</p>";
        $createClasses = "CREATE TABLE classes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            class_name VARCHAR(100) NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $conn->query($createClasses);
        echo "<p style='color: green;'>✅ ক্লাস টেবিল তৈরি হয়েছে</p>";
    }

    if (!$tableStatus['students']) {
        echo "<p>স্টুডেন্ট টেবিল তৈরি করছি...</p>";
        $createStudents = "CREATE TABLE students (
            id INT AUTO_INCREMENT PRIMARY KEY,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            roll_no VARCHAR(20),
            student_id VARCHAR(20),
            class_id INT,
            email VARCHAR(100),
            phone VARCHAR(20),
            gender ENUM('male', 'female', 'other'),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (class_id) REFERENCES classes(id)
        )";
        $conn->query($createStudents);
        echo "<p style='color: green;'>✅ স্টুডেন্ট টেবিল তৈরি হয়েছে</p>";
    }

    if (!$tableStatus['fees']) {
        echo "<p>ফি টেবিল তৈরি করছি...</p>";
        $createFees = "CREATE TABLE fees (
            id INT AUTO_INCREMENT PRIMARY KEY,
            student_id INT NOT NULL,
            fee_type VARCHAR(100) NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            paid DECIMAL(10,2) DEFAULT 0,
            due_date DATE,
            payment_status ENUM('paid', 'partial', 'due') DEFAULT 'due',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE
        )";
        $conn->query($createFees);
        echo "<p style='color: green;'>✅ ফি টেবিল তৈরি হয়েছে</p>";
    }

    if (!$tableStatus['fee_payments']) {
        echo "<p>পেমেন্ট টেবিল তৈরি করছি...</p>";
        $createPayments = "CREATE TABLE fee_payments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            fee_id INT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            payment_date DATE NOT NULL,
            payment_method VARCHAR(50) NOT NULL,
            receipt_no VARCHAR(50) DEFAULT NULL,
            notes TEXT DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (fee_id) REFERENCES fees(id) ON DELETE CASCADE
        )";
        $conn->query($createPayments);
        echo "<p style='color: green;'>✅ পেমেন্ট টেবিল তৈরি হয়েছে</p>";
    }

    // Add sample data
    echo "<h3>৪. নমুনা ডেটা যোগ</h3>";
    
    // Check and add class
    $classCount = $conn->query("SELECT COUNT(*) as count FROM classes")->fetch_assoc()['count'];
    if ($classCount == 0) {
        $conn->query("INSERT INTO classes (class_name, description) VALUES ('একাদশ শ্রেণী', 'একাদশ শ্রেণীর শিক্ষার্থীরা')");
        echo "<p style='color: green;'>✅ নমুনা ক্লাস যোগ করা হয়েছে</p>";
    }

    // Check and add student
    $studentCount = $conn->query("SELECT COUNT(*) as count FROM students")->fetch_assoc()['count'];
    if ($studentCount == 0) {
        $classId = $conn->query("SELECT id FROM classes LIMIT 1")->fetch_assoc()['id'];
        $stmt = $conn->prepare("INSERT INTO students (first_name, last_name, roll_no, student_id, class_id, email, phone, gender) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $firstName = 'মোহাম্মদ';
        $lastName = 'রহিম উদ্দিন';
        $rollNo = '2024001';
        $studentId = 'S2024001';
        $email = '<EMAIL>';
        $phone = '01712345678';
        $gender = 'male';
        $stmt->bind_param("ssssisss", $firstName, $lastName, $rollNo, $studentId, $classId, $email, $phone, $gender);
        $stmt->execute();
        echo "<p style='color: green;'>✅ নমুনা স্টুডেন্ট যোগ করা হয়েছে</p>";
    }

    // Check and add fee
    $feeCount = $conn->query("SELECT COUNT(*) as count FROM fees")->fetch_assoc()['count'];
    if ($feeCount == 0) {
        $studentId = $conn->query("SELECT id FROM students LIMIT 1")->fetch_assoc()['id'];
        $stmt = $conn->prepare("INSERT INTO fees (student_id, fee_type, amount, due_date, payment_status) VALUES (?, ?, ?, ?, ?)");
        $feeType = 'মাসিক বেতন';
        $amount = 1500.00;
        $dueDate = date('Y-m-d', strtotime('+30 days'));
        $status = 'due';
        $stmt->bind_param("isdss", $studentId, $feeType, $amount, $dueDate, $status);
        $stmt->execute();
        echo "<p style='color: green;'>✅ নমুনা ফি যোগ করা হয়েছে</p>";
    }

    // Check and add payment
    $paymentCount = $conn->query("SELECT COUNT(*) as count FROM fee_payments")->fetch_assoc()['count'];
    if ($paymentCount == 0) {
        $feeId = $conn->query("SELECT id FROM fees LIMIT 1")->fetch_assoc()['id'];
        $receiptNo = 'RCP-' . date('Ymd') . '-0001';
        $stmt = $conn->prepare("INSERT INTO fee_payments (fee_id, receipt_no, amount, payment_date, payment_method, notes) VALUES (?, ?, ?, ?, ?, ?)");
        $amount = 1500.00;
        $paymentDate = date('Y-m-d');
        $method = 'cash';
        $notes = 'নমুনা পেমেন্ট - টেস্ট';
        $stmt->bind_param("isdsss", $feeId, $receiptNo, $amount, $paymentDate, $method, $notes);
        $stmt->execute();
        echo "<p style='color: green;'>✅ নমুনা পেমেন্ট যোগ করা হয়েছে</p>";
        
        // Update fee status
        $conn->query("UPDATE fees SET paid = $amount, payment_status = 'paid' WHERE id = $feeId");
    }

    echo "<h3>৫. সিস্টেম স্ট্যাটাস</h3>";
    $finalStats = [
        'classes' => $conn->query("SELECT COUNT(*) as count FROM classes")->fetch_assoc()['count'],
        'students' => $conn->query("SELECT COUNT(*) as count FROM students")->fetch_assoc()['count'],
        'fees' => $conn->query("SELECT COUNT(*) as count FROM fees")->fetch_assoc()['count'],
        'payments' => $conn->query("SELECT COUNT(*) as count FROM fee_payments")->fetch_assoc()['count']
    ];

    echo "<div style='background: #f0f8ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>সিস্টেম প্রস্তুত!</h4>";
    echo "<ul>";
    echo "<li>ক্লাস: {$finalStats['classes']} টি</li>";
    echo "<li>শিক্ষার্থী: {$finalStats['students']} জন</li>";
    echo "<li>ফি: {$finalStats['fees']} টি</li>";
    echo "<li>পেমেন্ট: {$finalStats['payments']} টি</li>";
    echo "</ul>";
    echo "</div>";

    echo "<h3>৬. টেস্ট লিংক</h3>";
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='payment_receipts_simple.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>পেমেন্ট রিসিপ্ট দেখুন</a>";
    echo "<a href='fee_management.php' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>ফি ম্যানেজমেন্ট</a>";
    echo "</div>";

    // Show sample receipt
    if ($finalStats['payments'] > 0) {
        $sampleReceipt = $conn->query("SELECT receipt_no FROM fee_payments LIMIT 1")->fetch_assoc();
        echo "<p style='text-align: center;'>";
        echo "<a href='receipt_view.php?receipt_no=" . urlencode($sampleReceipt['receipt_no']) . "' target='_blank' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>নমুনা রিসিপ্ট দেখুন</a>";
        echo "</p>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: 'Hind Siliguri', Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}
h2, h3 {
    color: #333;
}
p {
    margin: 10px 0;
}
</style>
