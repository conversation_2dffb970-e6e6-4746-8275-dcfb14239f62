<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$successMessage = '';
$errorMessage = '';

// Get student ID from URL
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: students.php");
    exit();
}

$studentId = $_GET['id'];

// Get student data with department and class information
$studentQuery = "SELECT s.*, d.department_name, c.class_name
                 FROM students s
                 LEFT JOIN departments d ON s.department_id = d.id
                 LEFT JOIN classes c ON s.class_id = c.id
                 WHERE s.student_id = ?";
$stmt = $conn->prepare($studentQuery);
$stmt->bind_param("s", $studentId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header("Location: students.php");
    exit();
}

$student = $result->fetch_assoc();

// Get student's database ID
$studentDbId = $student['id'];

// Get current session
$currentSessionQuery = "SELECT id, session_name FROM sessions ORDER BY id DESC LIMIT 1";
$currentSession = $conn->query($currentSessionQuery)->fetch_assoc();

// Handle subject selection submission
if (isset($_POST['submit_selection'])) {
    // Validate selection
    $requiredSubjectIds = isset($_POST['required_subjects']) ? $_POST['required_subjects'] : [];
    $optionalSubjectIds = isset($_POST['optional_subjects']) ? $_POST['optional_subjects'] : [];
    $fourthSubjectIds = isset($_POST['fourth_subjects']) ? $_POST['fourth_subjects'] : [];

    $totalSelected = count($requiredSubjectIds) + count($optionalSubjectIds) + count($fourthSubjectIds);

    if ($totalSelected < 1) {
        $errorMessage = "অন্তত একটি বিষয় নির্বাচন করুন।";
    } else {
        // For debugging
        $debugInfo = "Student ID: " . $studentId . "\n";
        $debugInfo .= "Student DB ID: " . $studentDbId . "\n";
        $debugInfo .= "Required subjects: " . implode(", ", $requiredSubjectIds) . "\n";
        $debugInfo .= "Optional subjects: " . implode(", ", $optionalSubjectIds) . "\n";
        $debugInfo .= "Fourth subjects: " . implode(", ", $fourthSubjectIds) . "\n";
        $debugInfo .= "Total selected: " . $totalSelected . "\n";

        error_log($debugInfo);

        // Use direct SQL queries with mysqli_query
        $mysqli = new mysqli($servername, $dbUsername, $dbPassword, $dbName);

        if ($mysqli->connect_error) {
            $errorMessage = "ডাটাবেস সংযোগ করতে সমস্যা হয়েছে: " . $mysqli->connect_error;
            error_log("Database connection error: " . $mysqli->connect_error);
        } else {
            // Create student_subjects table if it doesn't exist
            $createTableQuery = "CREATE TABLE IF NOT EXISTS student_subjects (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                student_id INT(11) NOT NULL,
                subject_id INT(11) NOT NULL,
                category VARCHAR(20) NOT NULL DEFAULT 'optional',
                session_id INT(11) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";

            if (!$mysqli->query($createTableQuery)) {
                $errorMessage = "টেবিল তৈরি করতে সমস্যা হয়েছে: " . $mysqli->error;
                error_log("Error creating table: " . $mysqli->error);
            } else {
                // Delete existing selections
                $deleteQuery = "DELETE FROM student_subjects WHERE student_id = $studentDbId";
                if (!$mysqli->query($deleteQuery)) {
                    $errorMessage = "আগের নির্বাচিত বিষয়গুলি মুছতে সমস্যা হয়েছে: " . $mysqli->error;
                    error_log("Error deleting: " . $mysqli->error);
                } else {
                    $success = true;

                    // Insert subjects one by one
                    foreach ($requiredSubjectIds as $subjectId) {
                        $insertQuery = "INSERT INTO student_subjects (student_id, subject_id, category, session_id)
                                       VALUES ($studentDbId, $subjectId, 'required', {$currentSession['id']})";
                        if (!$mysqli->query($insertQuery)) {
                            $errorMessage = "আবশ্যিক বিষয় যোগ করতে সমস্যা হয়েছে: " . $mysqli->error;
                            error_log("Error inserting required subject: " . $mysqli->error);
                            $success = false;
                            break;
                        }
                    }

                    if ($success) {
                        foreach ($optionalSubjectIds as $subjectId) {
                            $insertQuery = "INSERT INTO student_subjects (student_id, subject_id, category, session_id)
                                           VALUES ($studentDbId, $subjectId, 'optional', {$currentSession['id']})";
                            if (!$mysqli->query($insertQuery)) {
                                $errorMessage = "ঐচ্ছিক বিষয় যোগ করতে সমস্যা হয়েছে: " . $mysqli->error;
                                error_log("Error inserting optional subject: " . $mysqli->error);
                                $success = false;
                                break;
                            }
                        }
                    }

                    if ($success) {
                        foreach ($fourthSubjectIds as $subjectId) {
                            $insertQuery = "INSERT INTO student_subjects (student_id, subject_id, category, session_id)
                                           VALUES ($studentDbId, $subjectId, 'fourth', {$currentSession['id']})";
                            if (!$mysqli->query($insertQuery)) {
                                $errorMessage = "৪র্থ বিষয় যোগ করতে সমস্যা হয়েছে: " . $mysqli->error;
                                error_log("Error inserting fourth subject: " . $mysqli->error);
                                $success = false;
                                break;
                            }
                        }
                    }

                    if ($success) {
                        $successMessage = "বিষয় নির্বাচন সফলভাবে সম্পন্ন হয়েছে!";
                        error_log("Insert successful");

                        // Verify
                        $verifyQuery = "SELECT COUNT(*) as count FROM student_subjects WHERE student_id = $studentDbId";
                        $verifyResult = $mysqli->query($verifyQuery);
                        if ($verifyResult) {
                            $row = $verifyResult->fetch_assoc();
                            error_log("Verification: Found " . $row['count'] . " subjects for student ID: " . $studentDbId);
                        }
                    }
                }
            }

            $mysqli->close();
        }

        // Add debug info to the page
        $debugMessage = "<div style='margin-top: 20px; padding: 10px; background-color: #f8f9fa; border: 1px solid #ddd;'>";
        $debugMessage .= "<h5>ডিবাগিং তথ্য:</h5>";
        $debugMessage .= "<pre>" . htmlspecialchars($debugInfo) . "</pre>";

        if (isset($insertQuery)) {
            $debugMessage .= "<p>কোয়েরি: " . htmlspecialchars($insertQuery) . "</p>";
        }

        if (isset($errorMessage)) {
            $debugMessage .= "<p>ত্রুটি: " . htmlspecialchars($errorMessage) . "</p>";
        }

        $debugMessage .= "</div>";
    }
}

// Get student's department ID
$departmentId = null;
$departmentName = null;

if (isset($student['department_id'])) {
    $departmentId = $student['department_id'];

    // Get department name
    $departmentNameQuery = "SELECT department_name FROM departments WHERE id = ?";
    $stmt = $conn->prepare($departmentNameQuery);
    $stmt->bind_param("i", $departmentId);
    $stmt->execute();
    $departmentResult = $stmt->get_result();
    if ($departmentResult->num_rows > 0) {
        $departmentName = $departmentResult->fetch_assoc()['department_name'];
    }
} else {
    // Try to get department ID from department_name if available
    if (isset($student['department_name']) && !empty($student['department_name'])) {
        $departmentName = $student['department_name'];
        $departmentQuery = "SELECT id FROM departments WHERE department_name = ?";
        $stmt = $conn->prepare($departmentQuery);
        $stmt->bind_param("s", $departmentName);
        $stmt->execute();
        $departmentResult = $stmt->get_result();
        if ($departmentResult->num_rows > 0) {
            $departmentId = $departmentResult->fetch_assoc()['id'];
        }
    }
}

// If no department ID found, show error
if (!$departmentId) {
    $errorMessage = "শিক্ষার্থীর বিভাগ নির্ধারিত নেই। প্রথমে শিক্ষার্থীর বিভাগ নির্ধারণ করুন।";
    $noDepartmentAssigned = true;
} else {
    $noDepartmentAssigned = false;

    // Get all departments for dropdown
    $allDepartmentsQuery = "SELECT id, department_name FROM departments ORDER BY department_name";
    $allDepartments = $conn->query($allDepartmentsQuery);

    // Find the corresponding group ID for the student's department
    $groupId = null;
    $debugMessage = "<div class='alert alert-info mt-3'><h5>Debug Information:</h5>";

    // Special case for Business department - check first
    if ($departmentName && (
        $departmentName == "বাবসায়" ||
        $departmentName == "ব্যবসায়" ||
        $departmentName == "বানিজ্য" ||
        $departmentName == "Business" ||
        strpos($departmentName, "বাবসায়") !== false ||
        strpos($departmentName, "ব্যবসায়") !== false ||
        strpos($departmentName, "বানিজ্য") !== false ||
        strpos($departmentName, "Business") !== false
    )) {
        $debugMessage .= "Detected Business department: $departmentName<br>";

        // Look for business group with exact name "ব্যবসায়" first
        $businessGroupQuery = "SELECT id FROM groups WHERE group_name = 'ব্যবসায়' LIMIT 1";
        $businessGroupResult = $conn->query($businessGroupQuery);

        if ($businessGroupResult && $businessGroupResult->num_rows > 0) {
            $groupId = $businessGroupResult->fetch_assoc()['id'];
            $debugMessage .= "Found exact match for ব্যবসায় group with ID: $groupId<br>";
        } else {
            // Try with broader search
            $businessGroupQuery = "SELECT id FROM groups WHERE group_name LIKE '%ব্যবসায়%' OR group_name LIKE '%বাবসায়%' OR group_name LIKE '%বানিজ্য%' OR group_name LIKE '%Business%' LIMIT 1";
            $businessGroupResult = $conn->query($businessGroupQuery);

            if ($businessGroupResult && $businessGroupResult->num_rows > 0) {
                $groupId = $businessGroupResult->fetch_assoc()['id'];
                $debugMessage .= "Found business group with ID: $groupId using broader search<br>";
            }
        }
    } else {
        // For non-business departments, try to find a group with the same name as the department
        if ($departmentName) {
            $debugMessage .= "Looking for group matching department: $departmentName<br>";

            // Try exact match first
            $groupQuery = "SELECT id FROM groups WHERE group_name = ?";
            $stmt = $conn->prepare($groupQuery);
            $stmt->bind_param("s", $departmentName);
            $stmt->execute();
            $groupResult = $stmt->get_result();

            if ($groupResult->num_rows > 0) {
                $groupId = $groupResult->fetch_assoc()['id'];
                $debugMessage .= "Found exact match with group ID: $groupId<br>";
            } else {
                // If exact match fails, try a LIKE query for similar names
                $groupQuery = "SELECT id, group_name FROM groups WHERE group_name LIKE ?";
                $searchTerm = "%" . $departmentName . "%";
                $stmt = $conn->prepare($groupQuery);
                $stmt->bind_param("s", $searchTerm);
                $stmt->execute();
                $groupResult = $stmt->get_result();

                if ($groupResult->num_rows > 0) {
                    $groupData = $groupResult->fetch_assoc();
                    $groupId = $groupData['id'];
                    $debugMessage .= "Found similar group: " . $groupData['group_name'] . " with ID: $groupId<br>";
                }
            }
        }
    }

    // If no group found, check if this is a business department
    if (!$groupId) {
        if ($departmentName && (
            $departmentName == "বাবসায়" ||
            $departmentName == "ব্যবসায়" ||
            $departmentName == "বানিজ্য" ||
            $departmentName == "Business" ||
            strpos($departmentName, "বাবসায়") !== false ||
            strpos($departmentName, "ব্যবসায়") !== false ||
            strpos($departmentName, "বানিজ্য") !== false ||
            strpos($departmentName, "Business") !== false
        )) {
            // This is a business department but no matching group found
            // Let's create the group
            $createGroupQuery = "INSERT INTO groups (group_name) VALUES ('ব্যবসায়')";
            if ($conn->query($createGroupQuery)) {
                $groupId = $conn->insert_id;
                $debugMessage .= "Created new group 'ব্যবসায়' with ID: $groupId<br>";
            } else {
                $debugMessage .= "Error creating group: " . $conn->error . "<br>";
                // Fall back to department ID
                $groupId = $departmentId;
                $debugMessage .= "Using department ID as fallback: $groupId<br>";
            }
        } else {
            // For non-business departments, use department ID as fallback
            $groupId = $departmentId;
            $debugMessage .= "No matching group found. Using department ID as fallback: $groupId<br>";
        }
    }

    // Verify that the group has subjects
    $subjectCountQuery = "SELECT COUNT(*) as count FROM subject_groups WHERE group_id = ? AND is_applicable = 1";
    $stmt = $conn->prepare($subjectCountQuery);
    $stmt->bind_param("i", $groupId);
    $stmt->execute();
    $countResult = $stmt->get_result();
    $subjectCount = $countResult->fetch_assoc()['count'];

    $debugMessage .= "Group ID $groupId has $subjectCount applicable subjects<br>";

    if ($subjectCount == 0 && $departmentName && (
        $departmentName == "বাবসায়" ||
        $departmentName == "ব্যবসায়" ||
        $departmentName == "বানিজ্য" ||
        $departmentName == "Business" ||
        strpos($departmentName, "বাবসায়") !== false ||
        strpos($departmentName, "ব্যবসায়") !== false ||
        strpos($departmentName, "বানিজ্য") !== false ||
        strpos($departmentName, "Business") !== false
    )) {
        // For business department with no subjects, try to find any group with subjects
        $debugMessage .= "Business department has no subjects. Trying to find any group with subjects.<br>";

        $groupWithSubjectsQuery = "SELECT DISTINCT sg.group_id, g.group_name, COUNT(*) as subject_count
                                  FROM subject_groups sg
                                  JOIN groups g ON sg.group_id = g.id
                                  WHERE sg.is_applicable = 1
                                  GROUP BY sg.group_id
                                  ORDER BY subject_count DESC
                                  LIMIT 1";
        $groupWithSubjectsResult = $conn->query($groupWithSubjectsQuery);

        if ($groupWithSubjectsResult && $groupWithSubjectsResult->num_rows > 0) {
            $groupWithSubjects = $groupWithSubjectsResult->fetch_assoc();
            $groupId = $groupWithSubjects['group_id'];
            $debugMessage .= "Using group ID $groupId ({$groupWithSubjects['group_name']}) with {$groupWithSubjects['subject_count']} subjects as fallback<br>";
        } else {
            // No group with subjects found, we'll use all subjects later
            $debugMessage .= "No group with subjects found. Will use all subjects.<br>";
        }
    }

    $debugMessage .= "</div>";
    // Debug information is hidden by default
    // Uncomment the line below to show debug information on the page
    // $debugInfo = $debugMessage;

    // Get required subjects for student's department/group
    $requiredSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code
                             FROM subjects s
                             JOIN subject_groups sg ON s.id = sg.subject_id
                             WHERE sg.group_id = ? AND s.is_active = 1
                             AND sg.subject_type = 'required' AND sg.is_applicable = 1
                             GROUP BY s.id
                             ORDER BY s.subject_name";
    $stmt = $conn->prepare($requiredSubjectsQuery);
    $stmt->bind_param("i", $groupId);
    $stmt->execute();
    $requiredSubjects = $stmt->get_result();

    // For debugging (hidden)
    $debugMessage .= "<div class='alert alert-info mt-3'><h5>Required Subjects Query:</h5>";
    $debugMessage .= "Query: " . str_replace("?", $groupId, $requiredSubjectsQuery) . "<br>";
    $debugMessage .= "Found " . $requiredSubjects->num_rows . " required subjects</div>";
    // $debugInfo .= $debugMessage;

    // Check if we got any required subjects
    $requiredCount = $requiredSubjects->num_rows;

    // If no required subjects found, try to get subjects from all groups as a fallback
    if ($requiredCount == 0) {
        error_log("No required subjects found for group ID: $groupId. Trying fallback.");

        // Try to find a group specifically for business studies
        if ($departmentName == "বাবসায়" || $departmentName == "বানিজ্য" || $departmentName == "Business") {
            // Look for any business-related group
            $businessGroupQuery = "SELECT id FROM groups WHERE group_name LIKE '%বাবসায়%' OR group_name LIKE '%বানিজ্য%' OR group_name LIKE '%Business%' LIMIT 1";
            $businessGroupResult = $conn->query($businessGroupQuery);

            if ($businessGroupResult && $businessGroupResult->num_rows > 0) {
                $businessGroupId = $businessGroupResult->fetch_assoc()['id'];
                error_log("Using business group ID as fallback: " . $businessGroupId);

                // Get required subjects for business group
                $requiredSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code
                                         FROM subjects s
                                         JOIN subject_groups sg ON s.id = sg.subject_id
                                         WHERE sg.group_id = ? AND s.is_active = 1
                                         AND sg.subject_type = 'required' AND sg.is_applicable = 1
                                         GROUP BY s.id
                                         ORDER BY s.subject_name";
                $stmt = $conn->prepare($requiredSubjectsQuery);
                $stmt->bind_param("i", $businessGroupId);
                $stmt->execute();
                $requiredSubjects = $stmt->get_result();

                // Update group ID for other queries
                $groupId = $businessGroupId;
            } else {
                // If still no business group found, get all subjects as a last resort
                $requiredSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code
                                         FROM subjects s
                                         WHERE s.is_active = 1
                                         ORDER BY s.subject_name";
                $requiredSubjects = $conn->query($requiredSubjectsQuery);
                error_log("Using all subjects as fallback");
            }
        }
    }

    // Get optional subjects for student's department/group
    $optionalSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code
                             FROM subjects s
                             JOIN subject_groups sg ON s.id = sg.subject_id
                             WHERE sg.group_id = ? AND s.is_active = 1
                             AND sg.subject_type = 'optional' AND sg.is_applicable = 1
                             GROUP BY s.id
                             ORDER BY s.subject_name";
    $stmt = $conn->prepare($optionalSubjectsQuery);
    $stmt->bind_param("i", $groupId);
    $stmt->execute();
    $optionalSubjects = $stmt->get_result();

    // Check if we got any optional subjects
    $optionalCount = $optionalSubjects->num_rows;

    // If no optional subjects found, try to get all subjects as a fallback
    if ($optionalCount == 0) {
        error_log("No optional subjects found for group ID: $groupId. Using fallback.");
        $optionalSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code
                                 FROM subjects s
                                 WHERE s.is_active = 1 AND s.id NOT IN (
                                     SELECT subject_id FROM subject_groups
                                     WHERE group_id = ? AND subject_type = 'required' AND is_applicable = 1
                                 )
                                 ORDER BY s.subject_name";
        $stmt = $conn->prepare($optionalSubjectsQuery);
        $stmt->bind_param("i", $groupId);
        $stmt->execute();
        $optionalSubjects = $stmt->get_result();
    }

    // Get fourth subjects for student's department/group
    $fourthSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code
                           FROM subjects s
                           JOIN subject_groups sg ON s.id = sg.subject_id
                           WHERE sg.group_id = ? AND s.is_active = 1
                           AND sg.subject_type = 'fourth' AND sg.is_applicable = 1
                           GROUP BY s.id
                           ORDER BY s.subject_name";
    $stmt = $conn->prepare($fourthSubjectsQuery);
    $stmt->bind_param("i", $groupId);
    $stmt->execute();
    $fourthSubjects = $stmt->get_result();

    // Check if we got any fourth subjects
    $fourthCount = $fourthSubjects->num_rows;

    // If no fourth subjects found, try to get all subjects as a fallback
    if ($fourthCount == 0) {
        error_log("No fourth subjects found for group ID: $groupId. Using fallback.");
        $fourthSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code
                               FROM subjects s
                               WHERE s.is_active = 1 AND s.id NOT IN (
                                   SELECT subject_id FROM subject_groups
                                   WHERE group_id = ? AND (subject_type = 'required' OR subject_type = 'optional') AND is_applicable = 1
                               )
                               ORDER BY s.subject_name LIMIT 5";
        $stmt = $conn->prepare($fourthSubjectsQuery);
        $stmt->bind_param("i", $groupId);
        $stmt->execute();
        $fourthSubjects = $stmt->get_result();
    }
}

// Get student's selected subjects
$selectedSubjectsQuery = "SELECT ss.id, ss.category, s.id as subject_id, s.subject_name, s.subject_code
                         FROM student_subjects ss
                         JOIN subjects s ON ss.subject_id = s.id
                         WHERE ss.student_id = ?
                         ORDER BY FIELD(ss.category, 'required', 'optional', 'fourth'), s.subject_name";
$stmt = $conn->prepare($selectedSubjectsQuery);
$stmt->bind_param("i", $studentDbId);
$stmt->execute();
$selectedSubjects = $stmt->get_result();

// Create arrays to store selected subject IDs by category
$selectedSubjectIds = [];
$selectedRequiredIds = [];
$selectedOptionalIds = [];
$selectedFourthIds = [];

if ($selectedSubjects && $selectedSubjects->num_rows > 0) {
    while ($subject = $selectedSubjects->fetch_assoc()) {
        $selectedSubjectIds[] = $subject['subject_id'];

        if ($subject['category'] == 'required') {
            $selectedRequiredIds[] = $subject['subject_id'];
        } elseif ($subject['category'] == 'optional') {
            $selectedOptionalIds[] = $subject['subject_id'];
        } elseif ($subject['category'] == 'fourth') {
            $selectedFourthIds[] = $subject['subject_id'];
        }
    }
    // Reset result pointer
    $selectedSubjects->data_seek(0);
}

$selectionExists = ($selectedSubjects && $selectedSubjects->num_rows > 0);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>শিক্ষার্থী বিষয় নির্বাচন - কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --accent-color: #4895ef;
            --success-color: #4cc9f0;
            --warning-color: #f72585;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --border-radius: 12px;
            --box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Hind Siliguri', 'Noto Sans Bengali', sans-serif;
            background-color: #f0f2f5;
            color: var(--dark-color);
            line-height: 1.6;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Noto Sans Bengali', 'Hind Siliguri', sans-serif;
            font-weight: 600;
        }

        .sidebar {
            background: linear-gradient(135deg, #3a0ca3 0%, #4361ee 100%);
            color: white;
            min-height: 100vh;
            padding-top: 20px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.85);
            padding: 12px 20px;
            margin: 5px 10px;
            border-radius: 10px;
            transition: var(--transition);
            font-weight: 500;
        }

        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateX(5px);
        }

        .sidebar .nav-link.active {
            color: white;
            background: linear-gradient(90deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .sidebar .nav-link i {
            margin-right: 12px;
            font-size: 1.1rem;
            width: 24px;
            text-align: center;
        }

        .main-content {
            padding: 30px;
            background-color: #f0f2f5;
            min-height: 100vh;
        }

        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 30px;
            overflow: hidden;
            transition: var(--transition);
        }

        .card:hover {
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
            transform: translateY(-5px);
        }

        .card-header {
            border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
            font-weight: 600;
            padding: 1.2rem 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .card-body {
            padding: 1.5rem;
        }

        .btn {
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 500;
            transition: var(--transition);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
        }

        .btn-primary {
            background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
            border: none;
        }

        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            border: none;
        }

        .btn-info {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            border: none;
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f72585 0%, #b5179e 100%);
            border: none;
            color: white;
        }

        .subject-selection-container {
            margin-bottom: 40px;
        }

        .subject-card {
            margin-bottom: 20px;
            transition: all 0.3s ease;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            overflow: hidden;
            height: 100%;
            border: 2px solid transparent;
        }

        .subject-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .subject-card.selected {
            border-color: #2ecc71;
            background-color: rgba(46, 204, 113, 0.05);
        }

        .subject-card .card-body {
            padding: 1.5rem;
        }

        .subject-card .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .subject-card .card-text {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .subject-card .badge {
            padding: 0.5rem 0.8rem;
            font-weight: 500;
            border-radius: 8px;
            font-size: 0.8rem;
        }

        .selection-summary {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        }

        .counter-badge {
            font-size: 0.9rem;
            padding: 8px 15px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .student-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        }

        .student-info h4 {
            color: #343a40;
            margin-bottom: 20px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 15px;
            font-weight: 600;
        }

        .student-info-item {
            margin-bottom: 12px;
            padding: 8px 0;
            border-bottom: 1px dashed rgba(0,0,0,0.05);
        }

        .student-info-label {
            font-weight: 600;
            color: #495057;
            margin-right: 5px;
        }

        .action-buttons {
            margin-top: 30px;
            margin-bottom: 40px;
        }

        .department-selector {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        }

        .department-selector h4 {
            color: #343a40;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .form-select {
            padding: 12px;
            border-radius: 10px;
            border: 1px solid rgba(0,0,0,0.1);
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: var(--transition);
        }

        .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
        }

        .form-check-input {
            width: 1.2em;
            height: 1.2em;
            margin-top: 0.25em;
            cursor: pointer;
        }

        .form-check-label {
            padding-left: 0.5rem;
            cursor: pointer;
        }

        .alert {
            border-radius: 12px;
            padding: 1.2rem 1.5rem;
            margin-bottom: 25px;
            border: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        }

        .alert-info {
            background: linear-gradient(135deg, #4cc9f0 0%, #4895ef 100%);
            color: white;
        }

        .alert-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
        }

        .alert-danger {
            background: linear-gradient(135deg, #f72585 0%, #b5179e 100%);
            color: white;
        }

        .alert-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .table {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        }

        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
            padding: 15px;
        }

        .table td {
            padding: 15px;
            vertical-align: middle;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0,0,0,0.02);
        }

        .subject-type-header {
            position: relative;
            padding-left: 20px;
            margin-bottom: 25px;
        }

        .subject-type-header:before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 5px;
            border-radius: 5px;
        }

        .subject-type-header.required:before {
            background: linear-gradient(to bottom, #4361ee, #3a0ca3);
        }

        .subject-type-header.optional:before {
            background: linear-gradient(to bottom, #3498db, #2980b9);
        }

        .subject-type-header.fourth:before {
            background: linear-gradient(to bottom, #f39c12, #e67e22);
        }

        .no-department-message {
            text-align: center;
            padding: 50px 20px;
        }

        .no-department-message i {
            font-size: 4rem;
            color: #f72585;
            margin-bottom: 20px;
        }

        .no-department-message h3 {
            margin-bottom: 20px;
            font-weight: 600;
        }

        .no-department-message p {
            max-width: 600px;
            margin: 0 auto 30px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="groups.php">
                            <i class="fas fa-layer-group me-2"></i> গ্রুপ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sessions.php">
                            <i class="fas fa-calendar-alt me-2"></i> সেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4 animate__animated animate__fadeIn">
                    <div class="col">
                        <h2>শিক্ষার্থী বিষয় নির্বাচন</h2>
                        <p class="text-muted">
                            <strong><?php echo $student['first_name'] . ' ' . $student['last_name']; ?></strong> -
                            বিভাগ: <span class="badge bg-primary"><?php echo $departmentName ?? 'N/A'; ?></span>
                            শ্রেণি: <span class="badge bg-success"><?php echo $student['class_name'] ?? 'N/A'; ?></span>
                        </p>
                    </div>
                    <div class="col-auto">
                        <a href="student_subject_selection_list.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>শিক্ষার্থী তালিকায় ফিরে যান
                        </a>
                    </div>
                </div>

                <!-- Important Information -->
                <div class="row mb-4 animate__animated animate__fadeIn">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="fas fa-info-circle fa-lg"></i>
                                    </div>
                                    <h5 class="card-title mb-0">গুরুত্বপূর্ণ তথ্য</h5>
                                </div>
                            </div>
                            <div class="card-body">
                                <p class="mb-0">
                                    শিক্ষার্থী <strong><?php echo $student['first_name'] . ' ' . $student['last_name']; ?></strong>
                                    <strong><?php echo $departmentName; ?></strong> বিভাগের এবং
                                    <strong><?php echo $student['class_name'] ?? 'N/A'; ?></strong> শ্রেণির।
                                <?php if (isset($groupId) && $groupId != $departmentId): ?>
                                    বিষয় নির্বাচনের জন্য <strong><?php
                                    $groupNameQuery = "SELECT group_name FROM groups WHERE id = ?";
                                    $stmt = $conn->prepare($groupNameQuery);
                                    $stmt->bind_param("i", $groupId);
                                    $stmt->execute();
                                    $groupNameResult = $stmt->get_result();
                                    if ($groupNameResult->num_rows > 0) {
                                        echo $groupNameResult->fetch_assoc()['group_name'];
                                    } else {
                                        echo "Unknown";
                                    }
                                    ?></strong> গ্রুপের বিষয়সমূহ ব্যবহার করা হচ্ছে।
                                <?php endif; ?>
                                নিচে এই বিভাগের জন্য প্রযোজ্য বিষয়সমূহ দেখানো হয়েছে। আপনি প্রয়োজন অনুযায়ী বিষয় নির্বাচন করুন।</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Subject Selection Summary -->
                <?php
                $totalSelected = count($selectedRequiredIds) + count($selectedOptionalIds) + count($selectedFourthIds);
                ?>
                <div class="row mb-4 animate__animated animate__fadeIn">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h4 class="mb-0">নির্বাচিত বিষয়সমূহ: <span class="badge bg-dark"><?php echo $totalSelected; ?></span></h4>
                                    <div class="d-flex gap-3">
                                        <span class="badge bg-primary p-2">আবশ্যিক: <?php echo count($selectedRequiredIds); ?></span>
                                        <span class="badge bg-info p-2">ঐচ্ছিক: <?php echo count($selectedOptionalIds); ?></span>
                                        <span class="badge bg-warning p-2">৪র্থ বিষয়: <?php echo count($selectedFourthIds); ?>/1</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeInUp" role="alert">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-check-circle fa-2x"></i>
                            </div>
                            <div>
                                <h5 class="mb-0 fw-bold">সফল!</h5>
                                <p class="mb-0"><?php echo $successMessage; ?></p>
                            </div>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeInUp" role="alert">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                            <div>
                                <h5 class="mb-0 fw-bold">সমস্যা!</h5>
                                <p class="mb-0"><?php echo $errorMessage; ?></p>
                            </div>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Student Information -->
                <div class="student-info animate__animated animate__fadeIn">
                    <h4><i class="fas fa-user-graduate me-2"></i>শিক্ষার্থীর তথ্য</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="student-info-item">
                                <span class="student-info-label">আইডি:</span> <?php echo $student['student_id']; ?>
                            </div>
                            <div class="student-info-item">
                                <span class="student-info-label">নাম:</span> <?php echo $student['first_name'] . ' ' . $student['last_name']; ?>
                            </div>
                            <div class="student-info-item">
                                <span class="student-info-label">ইমেইল:</span> <?php echo $student['email'] ?? 'N/A'; ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="student-info-item">
                                <span class="student-info-label">বিভাগ:</span>
                                <span class="badge bg-primary"><?php echo $departmentName ?? ($student['department_name'] ?? 'N/A'); ?></span>
                            </div>
                            <?php if (isset($groupId) && $groupId != $departmentId): ?>
                            <div class="student-info-item">
                                <span class="student-info-label">গ্রুপ:</span>
                                <span class="badge bg-info"><?php
                                $groupNameQuery = "SELECT group_name FROM groups WHERE id = ?";
                                $stmt = $conn->prepare($groupNameQuery);
                                $stmt->bind_param("i", $groupId);
                                $stmt->execute();
                                $groupNameResult = $stmt->get_result();
                                if ($groupNameResult->num_rows > 0) {
                                    echo $groupNameResult->fetch_assoc()['group_name'];
                                } else {
                                    echo "Unknown";
                                }
                                ?></span>
                            </div>
                            <?php endif; ?>
                            <div class="student-info-item">
                                <span class="student-info-label">শ্রেণি:</span>
                                <span class="badge bg-success"><?php echo $student['class_name'] ?? ($student['class'] ?? 'N/A'); ?></span>
                            </div>
                            <div class="student-info-item">
                                <span class="student-info-label">ফোন:</span> <?php echo $student['phone'] ?? 'N/A'; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if ($selectionExists): ?>
                    <!-- Selected Subjects Summary -->
                    <div class="card mb-4 animate__animated animate__fadeIn">
                        <div class="card-header bg-gradient">
                            <div class="d-flex align-items-center">
                                <div class="me-3 bg-white rounded-circle p-2">
                                    <i class="fas fa-check-circle fa-lg text-success"></i>
                                </div>
                                <h5 class="card-title mb-0">বর্তমানে নির্বাচিত বিষয়সমূহ</h5>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>বিষয় কোড</th>
                                            <th>বিষয়ের নাম</th>
                                            <th>ক্যাটাগরি</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        // Create an array to store subjects by category
                                        $subjectsByCategory = [
                                            'required' => [],
                                            'optional' => [],
                                            'fourth' => []
                                        ];

                                        // Group subjects by category
                                        if ($selectedSubjects && $selectedSubjects->num_rows > 0) {
                                            $selectedSubjects->data_seek(0);
                                            while ($subject = $selectedSubjects->fetch_assoc()) {
                                                if (isset($subjectsByCategory[$subject['category']])) {
                                                    $subjectsByCategory[$subject['category']][] = $subject;
                                                }
                                            }
                                        }

                                        // Display subjects in order: required, optional, fourth
                                        foreach ($subjectsByCategory as $category => $subjects) {
                                            foreach ($subjects as $subject) {
                                        ?>
                                                <tr>
                                                    <td><?php echo $subject['subject_code']; ?></td>
                                                    <td><?php echo $subject['subject_name']; ?></td>
                                                    <td>
                                                        <?php if ($subject['category'] == 'required'): ?>
                                                            <span class="badge bg-primary">আবশ্যিক</span>
                                                        <?php elseif ($subject['category'] == 'optional'): ?>
                                                            <span class="badge bg-info">ঐচ্ছিক</span>
                                                        <?php elseif ($subject['category'] == 'fourth'): ?>
                                                            <span class="badge bg-warning">৪র্থ বিষয়</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                        <?php
                                            }
                                        }

                                        if ($selectedSubjects && $selectedSubjects->num_rows == 0): ?>
                                            <tr>
                                                <td colspan="3" class="text-center">কোন বিষয় নির্বাচন করা হয়নি</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Action Buttons -->
                <div class="action-buttons animate__animated animate__fadeIn">
                    <button class="btn btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#selectionForm" aria-expanded="false" aria-controls="selectionForm">
                        <i class="fas fa-edit me-2"></i>বিষয় নির্বাচন পরিবর্তন করুন
                    </button>
                    <a href="view_student.php?id=<?php echo $student['student_id']; ?>" class="btn btn-info ms-2">
                        <i class="fas fa-user me-2"></i>শিক্ষার্থীর প্রোফাইল দেখুন
                    </a>
                    <a href="edit_student.php?id=<?php echo $student['id']; ?>" class="btn btn-warning ms-2">
                        <i class="fas fa-user-edit me-2"></i>শিক্ষার্থীর তথ্য সম্পাদনা করুন
                    </a>
                </div>

                <!-- Subject Selection Form -->
                <div id="selectionForm" class="collapse <?php echo !$selectionExists ? 'show' : ''; ?> animate__animated animate__fadeIn">
                    <?php if (isset($noDepartmentAssigned) && $noDepartmentAssigned): ?>
                        <!-- No Department Assigned Message -->
                        <div class="card">
                            <div class="card-body no-department-message">
                                <i class="fas fa-exclamation-circle"></i>
                                <h3>শিক্ষার্থীর বিভাগ নির্ধারিত নেই</h3>
                                <p>বিষয় নির্বাচন করতে হলে প্রথমে শিক্ষার্থীর বিভাগ নির্ধারণ করতে হবে। শিক্ষার্থীর তথ্য সম্পাদনা করে বিভাগ নির্ধারণ করুন।</p>
                                <a href="edit_student.php?id=<?php echo $student['id']; ?>" class="btn btn-primary btn-lg">
                                    <i class="fas fa-user-edit me-2"></i>শিক্ষার্থীর তথ্য সম্পাদনা করুন
                                </a>
                            </div>
                        </div>
                    <?php else: ?>
                        <form method="POST" action="student_subject_selection.php?id=<?php echo $studentId; ?>" id="subject-selection-form">
                            <input type="hidden" name="student_id" value="<?php echo $studentId; ?>">
                            <!-- Department Information -->
                            <div class="card mb-4">
                                <div class="card-header bg-gradient">
                                    <div class="d-flex align-items-center">
                                        <div class="me-3 bg-white rounded-circle p-2">
                                            <i class="fas fa-university fa-lg text-primary"></i>
                                        </div>
                                        <h5 class="card-title mb-0">বিভাগ তথ্য</h5>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-info">
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <i class="fas fa-university fa-2x"></i>
                                            </div>
                                            <div>
                                                <h5 class="mb-1 fw-bold">বিভাগ: <strong><?php echo $departmentName; ?></strong></h5>
                                                <?php
                                                // Get group name
                                                $groupNameQuery = "SELECT group_name FROM groups WHERE id = ?";
                                                $stmt = $conn->prepare($groupNameQuery);
                                                $stmt->bind_param("i", $groupId);
                                                $stmt->execute();
                                                $groupNameResult = $stmt->get_result();
                                                $groupName = ($groupNameResult->num_rows > 0) ? $groupNameResult->fetch_assoc()['group_name'] : "Unknown";
                                                ?>
                                                <h6 class="mb-1 fw-bold">গ্রুপ: <strong><?php echo $groupName; ?></strong> (ID: <?php echo $groupId; ?>)</h6>
                                                <h6 class="mb-1 fw-bold">শ্রেণি: <strong><?php echo $student['class_name'] ?? ($student['class'] ?? 'N/A'); ?></strong></h6>
                                                <p class="mb-0">এই বিভাগের জন্য নির্ধারিত বিষয়সমূহ নিচে দেখানো হয়েছে।</p>

                                                <!-- Configuration Links -->
                                                <div class="mt-2">
                                                    <a href="configure_business_subjects.php" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-cog me-1"></i> বাবসায় বিষয় সেটিং করুন
                                                    </a>
                                                </div>

                                                <?php
                                                // Debug information - count subjects for this group
                                                $countQuery = "SELECT
                                                    SUM(CASE WHEN subject_type = 'required' AND is_applicable = 1 THEN 1 ELSE 0 END) as required_count,
                                                    SUM(CASE WHEN subject_type = 'optional' AND is_applicable = 1 THEN 1 ELSE 0 END) as optional_count,
                                                    SUM(CASE WHEN subject_type = 'fourth' AND is_applicable = 1 THEN 1 ELSE 0 END) as fourth_count
                                                    FROM subject_groups WHERE group_id = ?";
                                                $stmt = $conn->prepare($countQuery);
                                                $stmt->bind_param("i", $groupId);
                                                $stmt->execute();
                                                $countResult = $stmt->get_result();
                                                $counts = $countResult->fetch_assoc();
                                                ?>
                                                <div class="mt-2 small">
                                                    <span class="badge bg-primary">আবশ্যিক: <?php echo $counts['required_count']; ?></span>
                                                    <span class="badge bg-info ms-1">ঐচ্ছিক: <?php echo $counts['optional_count']; ?></span>
                                                    <span class="badge bg-warning ms-1">৪র্থ: <?php echo $counts['fourth_count']; ?></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Selection Counter -->
                            <div class="selection-summary mb-4">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h4 class="mb-0">নির্বাচিত বিষয়সমূহ: <span id="selectedCount" class="badge bg-dark">0</span></h4>
                                    </div>
                                    <div class="col-auto">
                                        <div class="d-flex gap-2">
                                            <span class="badge bg-primary counter-badge">আবশ্যিক: <span id="requiredCount">0</span></span>
                                            <span class="badge bg-info counter-badge">ঐচ্ছিক: <span id="optionalCount">0</span></span>
                                            <span class="badge bg-warning counter-badge">৪র্থ বিষয়: <span id="fourthCount">0</span>/1</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Required Subjects -->
                            <div class="subject-selection-container">
                                <h4 class="mb-4 subject-type-header required">আবশ্যিক বিষয়সমূহ</h4>
                                <div class="row">
                                    <?php
                                    // Special case for Business department
                                    $isBusinessDepartment = (
                                        $departmentName == "বাবসায়" ||
                                        $departmentName == "ব্যবসায়" ||
                                        $departmentName == "বানিজ্য" ||
                                        $departmentName == "Business" ||
                                        strpos($departmentName, "বাবসায়") !== false ||
                                        strpos($departmentName, "ব্যবসায়") !== false ||
                                        strpos($departmentName, "বানিজ্য") !== false ||
                                        strpos($departmentName, "Business") !== false
                                    );

                                    // If this is a business department but no required subjects found, try to get all subjects
                                    if ($isBusinessDepartment && (!$requiredSubjects || $requiredSubjects->num_rows == 0)) {
                                        $allSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code
                                                           FROM subjects s
                                                           WHERE s.is_active = 1
                                                           ORDER BY s.subject_name";
                                        $requiredSubjects = $conn->query($allSubjectsQuery);
                                        echo "<div class='alert alert-warning'>বাবসায় বিভাগের জন্য কোন আবশ্যিক বিষয় পাওয়া যায়নি। সকল বিষয় দেখানো হচ্ছে।</div>";
                                    }

                                    if ($requiredSubjects && $requiredSubjects->num_rows > 0):
                                        // Reset result pointer
                                        $requiredSubjects->data_seek(0);
                                        while ($subject = $requiredSubjects->fetch_assoc()):
                                    ?>
                                            <div class="col-md-4 col-lg-3 mb-4">
                                                <div class="card subject-card h-100">
                                                    <div class="card-body">
                                                        <div class="form-check">
                                                            <input class="form-check-input subject-checkbox required-subject"
                                                                   type="checkbox"
                                                                   name="required_subjects[]"
                                                                   value="<?php echo $subject['id']; ?>"
                                                                   id="subject<?php echo $subject['id']; ?>"
                                                                   data-category="required"
                                                                   <?php echo in_array($subject['id'], $selectedRequiredIds) ? 'checked' : ''; ?>
                                                                   <?php echo ($isBusinessDepartment) ? 'checked' : ''; ?>>
                                                            <label class="form-check-label" for="subject<?php echo $subject['id']; ?>">
                                                                <h5 class="card-title"><?php echo $subject['subject_name']; ?></h5>
                                                                <p class="card-text"><?php echo $subject['subject_code']; ?></p>
                                                                <span class="badge bg-primary">আবশ্যিক</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <div class="col-12">
                                            <div class="alert alert-warning">
                                                <i class="fas fa-exclamation-triangle me-2"></i>এই বিভাগের জন্য কোন আবশ্যিক বিষয় পাওয়া যায়নি
                                                <?php if ($isBusinessDepartment): ?>
                                                    <div class="mt-2">
                                                        <a href="../admin/configure_business_subjects.php" class="btn btn-sm btn-primary">
                                                            <i class="fas fa-cog me-1"></i> বিষয় সেটিং করুন
                                                        </a>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Optional Subjects -->
                            <div class="subject-selection-container">
                                <h4 class="mb-4 subject-type-header optional">ঐচ্ছিক বিষয়সমূহ</h4>
                                <div class="row">
                                    <?php
                                    // Special case for Business department
                                    $isBusinessDepartment = (
                                        $departmentName == "বাবসায়" ||
                                        $departmentName == "ব্যবসায়" ||
                                        $departmentName == "বানিজ্য" ||
                                        $departmentName == "Business" ||
                                        strpos($departmentName, "বাবসায়") !== false ||
                                        strpos($departmentName, "ব্যবসায়") !== false ||
                                        strpos($departmentName, "বানিজ্য") !== false ||
                                        strpos($departmentName, "Business") !== false
                                    );

                                    // If this is a business department but no optional subjects found, try to get all subjects
                                    if ($isBusinessDepartment && (!$optionalSubjects || $optionalSubjects->num_rows == 0)) {
                                        $allSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code
                                                           FROM subjects s
                                                           WHERE s.is_active = 1 AND s.id NOT IN (
                                                               SELECT subject_id FROM student_subjects WHERE student_id = ? AND category = 'required'
                                                           )
                                                           ORDER BY s.subject_name";
                                        $stmt = $conn->prepare($allSubjectsQuery);
                                        $stmt->bind_param("i", $studentDbId);
                                        $stmt->execute();
                                        $optionalSubjects = $stmt->get_result();
                                        echo "<div class='alert alert-warning'>বাবসায় বিভাগের জন্য কোন ঐচ্ছিক বিষয় পাওয়া যায়নি। সকল বিষয় দেখানো হচ্ছে।</div>";
                                    }

                                    if ($optionalSubjects && $optionalSubjects->num_rows > 0):
                                        // Reset result pointer
                                        $optionalSubjects->data_seek(0);
                                        while ($subject = $optionalSubjects->fetch_assoc()):
                                    ?>
                                            <div class="col-md-4 col-lg-3 mb-4">
                                                <div class="card subject-card h-100">
                                                    <div class="card-body">
                                                        <div class="form-check">
                                                            <input class="form-check-input subject-checkbox optional-subject"
                                                                   type="checkbox"
                                                                   name="optional_subjects[]"
                                                                   value="<?php echo $subject['id']; ?>"
                                                                   id="subject<?php echo $subject['id']; ?>"
                                                                   data-category="optional"
                                                                   <?php echo in_array($subject['id'], $selectedOptionalIds) ? 'checked' : ''; ?>>
                                                            <label class="form-check-label" for="subject<?php echo $subject['id']; ?>">
                                                                <h5 class="card-title"><?php echo $subject['subject_name']; ?></h5>
                                                                <p class="card-text"><?php echo $subject['subject_code']; ?></p>
                                                                <span class="badge bg-info">ঐচ্ছিক</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <div class="col-12">
                                            <div class="alert alert-warning">
                                                <i class="fas fa-exclamation-triangle me-2"></i>এই বিভাগের জন্য কোন ঐচ্ছিক বিষয় পাওয়া যায়নি
                                                <?php if ($isBusinessDepartment): ?>
                                                    <div class="mt-2">
                                                        <a href="../admin/configure_business_subjects.php" class="btn btn-sm btn-primary">
                                                            <i class="fas fa-cog me-1"></i> বিষয় সেটিং করুন
                                                        </a>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Fourth Subjects -->
                            <div class="subject-selection-container">
                                <h4 class="mb-4 subject-type-header fourth">৪র্থ বিষয়সমূহ</h4>
                                <div class="row">
                                    <?php
                                    // Special case for Business department
                                    $isBusinessDepartment = (
                                        $departmentName == "বাবসায়" ||
                                        $departmentName == "ব্যবসায়" ||
                                        $departmentName == "বানিজ্য" ||
                                        $departmentName == "Business" ||
                                        strpos($departmentName, "বাবসায়") !== false ||
                                        strpos($departmentName, "ব্যবসায়") !== false ||
                                        strpos($departmentName, "বানিজ্য") !== false ||
                                        strpos($departmentName, "Business") !== false
                                    );

                                    // If this is a business department but no fourth subjects found, try to get all subjects
                                    if ($isBusinessDepartment && (!$fourthSubjects || $fourthSubjects->num_rows == 0)) {
                                        $allSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code
                                                           FROM subjects s
                                                           WHERE s.is_active = 1 AND s.id NOT IN (
                                                               SELECT subject_id FROM student_subjects WHERE student_id = ? AND (category = 'required' OR category = 'optional')
                                                           )
                                                           ORDER BY s.subject_name LIMIT 5";
                                        $stmt = $conn->prepare($allSubjectsQuery);
                                        $stmt->bind_param("i", $studentDbId);
                                        $stmt->execute();
                                        $fourthSubjects = $stmt->get_result();
                                        echo "<div class='alert alert-warning'>বাবসায় বিভাগের জন্য কোন ৪র্থ বিষয় পাওয়া যায়নি। সকল বিষয় দেখানো হচ্ছে।</div>";
                                    }

                                    if ($fourthSubjects && $fourthSubjects->num_rows > 0):
                                        // Reset result pointer
                                        $fourthSubjects->data_seek(0);
                                        while ($subject = $fourthSubjects->fetch_assoc()):
                                    ?>
                                            <div class="col-md-4 col-lg-3 mb-4">
                                                <div class="card subject-card h-100">
                                                    <div class="card-body">
                                                        <div class="form-check">
                                                            <input class="form-check-input subject-checkbox fourth-subject"
                                                                   type="checkbox"
                                                                   name="fourth_subjects[]"
                                                                   value="<?php echo $subject['id']; ?>"
                                                                   id="subject<?php echo $subject['id']; ?>"
                                                                   data-category="fourth"
                                                                   <?php echo in_array($subject['id'], $selectedFourthIds) ? 'checked' : ''; ?>>
                                                            <label class="form-check-label" for="subject<?php echo $subject['id']; ?>">
                                                                <h5 class="card-title"><?php echo $subject['subject_name']; ?></h5>
                                                                <p class="card-text"><?php echo $subject['subject_code']; ?></p>
                                                                <span class="badge bg-warning">৪র্থ বিষয়</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <div class="col-12">
                                            <div class="alert alert-warning">
                                                <i class="fas fa-exclamation-triangle me-2"></i>এই বিভাগের জন্য কোন ৪র্থ বিষয় পাওয়া যায়নি
                                                <?php if ($isBusinessDepartment): ?>
                                                    <div class="mt-2">
                                                        <a href="../admin/configure_business_subjects.php" class="btn btn-sm btn-primary">
                                                            <i class="fas fa-cog me-1"></i> বিষয় সেটিং করুন
                                                        </a>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="d-grid gap-2 col-md-6 mx-auto mt-5 mb-5">
                                <div class="d-flex gap-2 justify-content-center">
                                    <a href="manual_subject_insert.php?id=<?php echo $studentId; ?>" class="btn btn-info btn-lg">
                                        <i class="fas fa-edit me-2"></i>ম্যানুয়াল বিষয় নির্বাচন
                                    </a>
                                    <button type="submit" name="submit_selection" class="btn btn-success btn-lg animate__animated animate__pulse animate__infinite animate__slower">
                                        <i class="fas fa-save me-2"></i>বিষয় নির্বাচন সংরক্ষণ করুন
                                    </button>
                                </div>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add animation to cards
            document.querySelectorAll('.subject-card').forEach((card, index) => {
                card.classList.add('animate__animated', 'animate__fadeIn');
                card.style.animationDelay = `${index * 0.05}s`;
            });

            // Get all subject checkboxes
            const subjectCheckboxes = document.querySelectorAll('.subject-checkbox');

            // Update counts on page load
            updateCounts();

            // Update card styles for selected subjects
            subjectCheckboxes.forEach(checkbox => {
                updateCardStyle(checkbox);

                checkbox.addEventListener('change', function() {
                    // Add animation when selecting/deselecting
                    const card = this.closest('.subject-card');

                    if (this.checked) {
                        // Play selection animation
                        card.classList.remove('animate__fadeIn');
                        card.classList.add('animate__pulse');

                        // Show toast notification
                        const subjectName = card.querySelector('.card-title').textContent;
                        const categoryText = this.dataset.category === 'required' ? 'আবশ্যিক' :
                                            (this.dataset.category === 'optional' ? 'ঐচ্ছিক' : '৪র্থ');

                        Swal.fire({
                            position: 'top-end',
                            icon: 'success',
                            title: `${subjectName} (${categoryText}) নির্বাচিত হয়েছে`,
                            showConfirmButton: false,
                            timer: 1500,
                            toast: true,
                            timerProgressBar: true
                        });
                    } else {
                        // Play deselection animation
                        card.classList.remove('animate__pulse');
                        card.classList.add('animate__headShake');

                        // Remove animation class after it completes
                        setTimeout(() => {
                            card.classList.remove('animate__headShake');
                        }, 1000);
                    }

                    updateCardStyle(this);
                    updateCounts();

                    // Fourth subject validation - allow only one
                    if (this.dataset.category === 'fourth' && this.checked) {
                        const fourthCheckboxes = document.querySelectorAll('.fourth-subject:checked');
                        if (fourthCheckboxes.length > 1) {
                            fourthCheckboxes.forEach(cb => {
                                if (cb !== this) {
                                    cb.checked = false;
                                    updateCardStyle(cb);

                                    // Show warning toast
                                    Swal.fire({
                                        position: 'top-end',
                                        icon: 'warning',
                                        title: 'শুধুমাত্র একটি ৪র্থ বিষয় নির্বাচন করা যাবে',
                                        showConfirmButton: false,
                                        timer: 2000,
                                        toast: true,
                                        timerProgressBar: true
                                    });
                                }
                            });
                        }
                    }
                });
            });

            function updateCardStyle(checkbox) {
                const card = checkbox.closest('.subject-card');
                if (checkbox.checked) {
                    card.classList.add('selected');
                } else {
                    card.classList.remove('selected');
                }
            }

            function updateCounts() {
                const requiredChecked = document.querySelectorAll('.required-subject:checked').length;
                const optionalChecked = document.querySelectorAll('.optional-subject:checked').length;
                const fourthChecked = document.querySelectorAll('.fourth-subject:checked').length;
                const totalChecked = requiredChecked + optionalChecked + fourthChecked;

                // Update count displays with animation
                updateCountWithAnimation('requiredCount', requiredChecked);
                updateCountWithAnimation('optionalCount', optionalChecked);
                updateCountWithAnimation('fourthCount', fourthChecked);
                updateCountWithAnimation('selectedCount', totalChecked);

                // Also update the summary at the top of the page
                const summaryTotal = document.querySelector('.card .badge.bg-dark');
                if (summaryTotal) {
                    summaryTotal.textContent = totalChecked;
                }

                const summaryRequired = document.querySelector('.card .badge.bg-primary');
                if (summaryRequired) {
                    summaryRequired.textContent = `আবশ্যিক: ${requiredChecked}`;
                }

                const summaryOptional = document.querySelector('.card .badge.bg-info');
                if (summaryOptional) {
                    summaryOptional.textContent = `ঐচ্ছিক: ${optionalChecked}`;
                }

                const summaryFourth = document.querySelector('.card .badge.bg-warning');
                if (summaryFourth) {
                    summaryFourth.textContent = `৪র্থ বিষয়: ${fourthChecked}/1`;
                }
            }

            function updateCountWithAnimation(elementId, newValue) {
                const element = document.getElementById(elementId);
                const currentValue = parseInt(element.textContent);

                if (currentValue !== newValue) {
                    // Add animation
                    element.classList.add('animate__animated', 'animate__bounceIn');

                    // Update value
                    element.textContent = newValue;

                    // Remove animation class after it completes
                    setTimeout(() => {
                        element.classList.remove('animate__animated', 'animate__bounceIn');
                    }, 1000);
                }
            }

            // Add form submission confirmation
            const form = document.querySelector('form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const totalChecked = document.querySelectorAll('.subject-checkbox:checked').length;

                    if (totalChecked === 0) {
                        Swal.fire({
                            icon: 'error',
                            title: 'কোন বিষয় নির্বাচন করা হয়নি!',
                            text: 'অন্তত একটি বিষয় নির্বাচন করুন।',
                            confirmButtonText: 'ঠিক আছে'
                        });
                    } else {
                        Swal.fire({
                            title: 'বিষয় নির্বাচন নিশ্চিত করুন',
                            text: `আপনি ${totalChecked}টি বিষয় নির্বাচন করেছেন। আপনি কি নিশ্চিত?`,
                            icon: 'question',
                            showCancelButton: true,
                            confirmButtonColor: '#2ecc71',
                            cancelButtonColor: '#d33',
                            confirmButtonText: 'হ্যাঁ, নিশ্চিত করুন',
                            cancelButtonText: 'না, বাতিল করুন'
                        }).then((result) => {
                            if (result.isConfirmed) {
                                // Show loading state
                                Swal.fire({
                                    title: 'সংরক্ষণ করা হচ্ছে...',
                                    html: 'অনুগ্রহ করে অপেক্ষা করুন...',
                                    allowOutsideClick: false,
                                    didOpen: () => {
                                        Swal.showLoading();
                                    }
                                });

                                // Show loading message
                                const loadingMessage = document.createElement('div');
                                loadingMessage.className = 'alert alert-info mt-3';
                                loadingMessage.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>বিষয় নির্বাচন সংরক্ষণ করা হচ্ছে...';
                                form.appendChild(loadingMessage);

                                // Submit form via AJAX
                                const formData = new FormData(form);

                                // Add student ID to form data
                                formData.append('student_id', '<?php echo $studentId; ?>');

                                console.log('Student ID:', '<?php echo $studentId; ?>');

                                fetch('save_subject_selection.php', {
                                    method: 'POST',
                                    body: formData
                                })
                                .then(response => response.json())
                                .then(data => {
                                    // Close loading dialog
                                    Swal.close();

                                    // Remove loading message
                                    loadingMessage.remove();

                                    if (data.success) {
                                        // Show success message
                                        Swal.fire({
                                            icon: 'success',
                                            title: 'সফল!',
                                            text: data.message,
                                            confirmButtonText: 'ঠিক আছে'
                                        }).then(() => {
                                            // Reload page
                                            window.location.reload();
                                        });
                                    } else {
                                        // Show error message
                                        Swal.fire({
                                            icon: 'error',
                                            title: 'ত্রুটি!',
                                            text: data.message,
                                            confirmButtonText: 'ঠিক আছে'
                                        });

                                        // Add debug info if available
                                        if (data.debug && data.debug.length > 0) {
                                            const debugInfo = document.createElement('div');
                                            debugInfo.className = 'mt-3 p-3 bg-light border';
                                            debugInfo.innerHTML = '<h5>ডিবাগিং তথ্য:</h5><pre>' + data.debug.join('\n') + '</pre>';
                                            form.appendChild(debugInfo);
                                        }
                                    }
                                })
                                .catch(error => {
                                    // Close loading dialog
                                    Swal.close();

                                    // Remove loading message
                                    loadingMessage.remove();

                                    // Show error
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'ত্রুটি!',
                                        text: 'Error: ' + error.message,
                                        confirmButtonText: 'ঠিক আছে'
                                    });
                                });
                            }
                        });
                    }
                });
            }

            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert-dismissible');
                alerts.forEach(function(alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);
        });
    </script>

    <?php if (isset($debugMessage)): ?>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <?php echo $debugMessage; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
</body>
</html>
