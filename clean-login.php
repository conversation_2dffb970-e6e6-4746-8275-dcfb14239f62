<?php
// Clean login page without header issues
ob_start();
session_start();

// Simple database connection without extra output
$servername = "127.0.0.1";
$username = "root";
$password = "";
$dbname = "zfaw";

$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    die("Connection failed");
}
$conn->set_charset("utf8mb4");

$error_message = "";

// Check if form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $username = htmlspecialchars(trim($_POST['username']));
    $password = $_POST['password'];
    $userType = isset($_POST['userType']) ? htmlspecialchars($_POST['userType']) : '';

    if (empty($username) || empty($password) || empty($userType)) {
        $error_message = "সব ফিল্ড পূরণ করুন।";
    } else {
        $sql = "SELECT * FROM users WHERE username = ? AND user_type = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ss", $username, $userType);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($row = $result->fetch_assoc()) {
            if (password_verify($password, $row['password'])) {
                $_SESSION['loggedin'] = true;
                $_SESSION['userId'] = $row['id'];
                $_SESSION['username'] = $row['username'];
                $_SESSION['userType'] = $row['user_type'];

                // Clear output buffer before redirect
                ob_end_clean();
                
                // Redirect based on user type
                switch($row['user_type']) {
                    case 'admin':
                        header("Location: admin/dashboard.php");
                        exit();
                    case 'teacher':
                        header("Location: teacher/dashboard.php");
                        exit();
                    case 'student':
                        header("Location: student/dashboard.php");
                        exit();
                    case 'staff':
                        header("Location: staff/dashboard.php");
                        exit();
                    default:
                        header("Location: index.php");
                        exit();
                }
            } else {
                $error_message = "ভুল পাসওয়ার্ড। দয়া করে আবার চেষ্টা করুন।";
            }
        } else {
            $error_message = "ইউজারনেম খুঁজে পাওয়া যায়নি।";
        }
        $stmt->close();
    }
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>লগইন - নিশাত এডুকেশন সেন্টার</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            backdrop-filter: blur(10px);
        }
        
        .school-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .school-header h1 {
            color: #006A4E;
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .school-header p {
            color: #666;
            font-size: 1rem;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .form-control {
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #006A4E;
            box-shadow: 0 0 0 0.2rem rgba(0, 106, 78, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #006A4E, #00563B);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-size: 18px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 106, 78, 0.4);
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            border: 1px solid #f5c6cb;
        }
        
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .back-link a {
            color: #006A4E;
            text-decoration: none;
            font-weight: 500;
        }
        
        .back-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="school-header">
            <h1>নিশাত এডুকেশন সেন্টার</h1>
            <p>চুয়াডাঙ্গা, বাংলাদেশ</p>
        </div>
        
        <h2 class="text-center mb-4" style="color: #333; font-weight: 600;">লগইন</h2>
        
        <?php if (!empty($error_message)): ?>
            <div class="error-message"><?php echo $error_message; ?></div>
        <?php endif; ?>
        
        <form method="post" action="">
            <div class="form-group">
                <label for="username" class="form-label">ইউজারনেম</label>
                <input type="text" id="username" name="username" class="form-control" required>
            </div>
            
            <div class="form-group">
                <label for="password" class="form-label">পাসওয়ার্ড</label>
                <input type="password" id="password" name="password" class="form-control" required>
            </div>
            
            <div class="form-group">
                <label for="userType" class="form-label">ব্যবহারকারীর ধরন</label>
                <select id="userType" name="userType" class="form-control" required>
                    <option value="">ব্যবহারকারীর ধরন নির্বাচন করুন</option>
                    <option value="admin">অ্যাডমিন</option>
                    <option value="teacher">শিক্ষক</option>
                    <option value="student">শিক্ষার্থী</option>
                    <option value="staff">কর্মচারী</option>
                </select>
            </div>
            
            <button type="submit" class="btn-login">লগইন</button>
        </form>
        
        <div class="back-link">
            <a href="index.php">← হোম পেজে ফিরে যান</a>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.title = 'লগইন - নিশাত এডুকেশন সেন্টার';
            console.log('Clean login page loaded successfully');
        });
    </script>
</body>
</html>
<?php
$content = ob_get_contents();
ob_end_clean();

header('Content-Type: text/html; charset=UTF-8');
header('Cache-Control: no-cache, must-revalidate');
header('Connection: close');
header('Content-Length: ' . strlen($content));

echo $content;

if (function_exists('fastcgi_finish_request')) {
    fastcgi_finish_request();
}
exit();
?>
