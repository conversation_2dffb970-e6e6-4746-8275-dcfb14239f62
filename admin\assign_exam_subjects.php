<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if necessary tables exist
$tables = ['exams', 'subjects', 'exam_subject_relations'];
$missingTables = [];
$createdTables = [];

foreach ($tables as $table) {
    $tableCheck = $conn->query("SHOW TABLES LIKE '$table'");
    if ($tableCheck->num_rows == 0) {
        $missingTables[] = $table;
    }
}

// Create exam_subject_relations table if it doesn't exist
$examSubjectRelationsCheck = $conn->query("SHOW TABLES LIKE 'exam_subject_relations'");
if ($examSubjectRelationsCheck->num_rows == 0) {
    $createExamSubjectRelationsTable = "CREATE TABLE IF NOT EXISTS exam_subject_relations (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        exam_id INT(11) NOT NULL,
        subject_id INT(11) NOT NULL,
        total_marks INT(11) DEFAULT 100,
        passing_marks INT(11) DEFAULT 33,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_exam_subject (exam_id, subject_id)
    )";

    if ($conn->query($createExamSubjectRelationsTable)) {
        $createdTables[] = 'exam_subject_relations';
    }
}

if (!empty($missingTables) && !in_array('exam_subject_relations', $createdTables)) {
    header("Location: exam_dashboard.php");
    exit();
}

// Handle form submissions
$successMessage = '';
$errorMessage = '';

// Get all exams
$examsQuery = "SELECT e.id, e.exam_name, e.exam_type, e.exam_date, e.total_marks,
              c.class_name, d.department_name, s.session_name
              FROM exams e
              LEFT JOIN classes c ON e.class_id = c.id
              LEFT JOIN departments d ON e.department_id = d.id
              LEFT JOIN sessions s ON e.session_id = s.id
              ORDER BY e.exam_date DESC";
$exams = $conn->query($examsQuery);

// Get all subjects
$subjectsQuery = "SELECT id, subject_name, subject_code FROM subjects WHERE is_active = 1 ORDER BY subject_name";
$subjects = $conn->query($subjectsQuery);

// Handle subject assignment
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['assign_subjects'])) {
    $examId = intval($_POST['exam_id']);
    $subjectIds = isset($_POST['subject_ids']) ? $_POST['subject_ids'] : [];
    $totalMarks = isset($_POST['total_marks']) ? intval($_POST['total_marks']) : 100;
    $passingMarks = isset($_POST['passing_marks']) ? intval($_POST['passing_marks']) : 33;

    // Check if "all" is selected
    $allSubjects = in_array('all', $subjectIds);

    if (empty($examId)) {
        $errorMessage = "পরীক্ষা নির্বাচন করুন!";
    } elseif (empty($subjectIds) && !$allSubjects) {
        $errorMessage = "কমপক্ষে একটি বিষয় নির্বাচন করুন!";
    } else {
        // Start transaction
        $conn->begin_transaction();

        try {
            // Delete existing subject relations for this exam
            $deleteQuery = "DELETE FROM exam_subject_relations WHERE exam_id = ?";
            $stmt = $conn->prepare($deleteQuery);
            $stmt->bind_param("i", $examId);
            $stmt->execute();

            // If "All Subjects" is selected
            if ($allSubjects) {
                // Get all active subjects
                $allSubjectsQuery = "SELECT id FROM subjects WHERE is_active = 1";
                $allSubjectsResult = $conn->query($allSubjectsQuery);

                if ($allSubjectsResult && $allSubjectsResult->num_rows > 0) {
                    $insertQuery = "INSERT INTO exam_subject_relations (exam_id, subject_id, total_marks, passing_marks) VALUES (?, ?, ?, ?)";
                    $stmt = $conn->prepare($insertQuery);

                    while ($subj = $allSubjectsResult->fetch_assoc()) {
                        $subjId = $subj['id'];
                        $stmt->bind_param("iiii", $examId, $subjId, $totalMarks, $passingMarks);
                        $stmt->execute();
                    }
                }
            }
            // If specific subjects are selected
            else {
                $insertQuery = "INSERT INTO exam_subject_relations (exam_id, subject_id, total_marks, passing_marks) VALUES (?, ?, ?, ?)";
                $stmt = $conn->prepare($insertQuery);

                foreach ($subjectIds as $subjectId) {
                    $stmt->bind_param("iiii", $examId, $subjectId, $totalMarks, $passingMarks);
                    $stmt->execute();
                }
            }

            // Commit transaction
            $conn->commit();

            $successMessage = "বিষয়সমূহ সফলভাবে অ্যাসাইন করা হয়েছে!";
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            $errorMessage = "বিষয় অ্যাসাইন করতে সমস্যা হয়েছে: " . $e->getMessage();
        }
    }
}

// Get exam details if exam_id is provided
$selectedExam = null;
$assignedSubjects = [];

if (isset($_GET['exam_id']) && !empty($_GET['exam_id'])) {
    $examId = intval($_GET['exam_id']);

    // Get exam details
    $examQuery = "SELECT e.*, c.class_name, d.department_name, s.session_name
                 FROM exams e
                 LEFT JOIN classes c ON e.class_id = c.id
                 LEFT JOIN departments d ON e.department_id = d.id
                 LEFT JOIN sessions s ON e.session_id = s.id
                 WHERE e.id = ?";
    $stmt = $conn->prepare($examQuery);
    $stmt->bind_param("i", $examId);
    $stmt->execute();
    $selectedExam = $stmt->get_result()->fetch_assoc();

    // Get assigned subjects
    $assignedSubjectsQuery = "SELECT esr.*, s.subject_name, s.subject_code
                             FROM exam_subject_relations esr
                             JOIN subjects s ON esr.subject_id = s.id
                             WHERE esr.exam_id = ?";
    $stmt = $conn->prepare($assignedSubjectsQuery);
    $stmt->bind_param("i", $examId);
    $stmt->execute();
    $assignedSubjectsResult = $stmt->get_result();

    while ($subject = $assignedSubjectsResult->fetch_assoc()) {
        $assignedSubjects[] = $subject;
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>পরীক্ষার বিষয় অ্যাসাইন - স্কুল ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css">

    <style>
        :root {
            --primary-color: #4361ee;
            --primary-hover: #3a56d4;
            --secondary-color: #6c757d;
            --success-color: #2ecc71;
            --success-hover: #27ae60;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --info-color: #3498db;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --border-radius: 10px;
            --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        body {
            background-color: #f8f9fa;
            font-family: 'Hind Siliguri', sans-serif;
            color: #333;
        }

        .main-content {
            padding: 25px;
        }

        /* Card Styles */
        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 30px;
            transition: var(--transition);
            overflow: hidden;
        }

        .card:hover {
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            transform: translateY(-5px);
        }

        .card-header {
            border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
            padding: 18px 25px;
            border-bottom: none;
        }

        .card-header.bg-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover)) !important;
        }

        .card-body {
            padding: 30px;
        }

        /* Form Controls */
        .form-control, .form-select {
            border-radius: 8px;
            padding: 12px 15px;
            border: 1px solid #e0e0e0;
            transition: var(--transition);
            font-size: 1rem;
            height: auto;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
        }

        /* Buttons */
        .btn {
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 500;
            transition: var(--transition);
            border: none;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .btn-success {
            background-color: var(--success-color);
        }

        .btn-success:hover {
            background-color: var(--success-hover);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            color: white;
        }

        /* Select2 Customization */
        .select2-container {
            width: 100% !important;
        }

        .select2-container .select2-selection--multiple {
            min-height: 50px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            padding: 5px;
        }

        .select2-container--bootstrap-5 .select2-selection {
            padding: 8px 12px;
        }

        .select2-selection__choice {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover)) !important;
            color: white !important;
            border: none !important;
            padding: 5px 10px !important;
            border-radius: 5px !important;
            margin: 3px !important;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .select2-selection__choice__remove {
            color: white !important;
            margin-right: 5px !important;
            font-weight: bold;
        }

        .select2-dropdown {
            border-radius: 8px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            overflow: hidden;
        }

        .select2-results__option {
            padding: 10px 15px;
            transition: var(--transition);
        }

        .select2-results__option--highlighted {
            background-color: var(--primary-color) !important;
        }

        /* Alerts */
        .alert {
            border-radius: 10px;
            padding: 18px 25px;
            margin-bottom: 25px;
            border: none;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .alert-success {
            background-color: rgba(46, 204, 113, 0.15);
            color: #27ae60;
        }

        .alert-danger {
            background-color: rgba(231, 76, 60, 0.15);
            color: #c0392b;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>পরীক্ষার বিষয় অ্যাসাইন</h2>
                        <p class="text-muted">পরীক্ষার জন্য বিষয়সমূহ নির্ধারণ করুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="exam_dashboard.php" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>পরীক্ষা ড্যাশবোর্ড
                        </a>
                    </div>
                </div>

                <?php if ($successMessage): ?>
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle me-2"></i><?php echo $successMessage; ?>
                    </div>
                <?php endif; ?>

                <?php if ($errorMessage): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $errorMessage; ?>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- Exam Selection Form -->
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">পরীক্ষা নির্বাচন করুন</h5>
                            </div>
                            <div class="card-body">
                                <form method="GET" action="">
                                    <div class="mb-3">
                                        <label for="exam_id" class="form-label">পরীক্ষা</label>
                                        <select class="form-select" id="exam_id" name="exam_id" required>
                                            <option value="">পরীক্ষা নির্বাচন করুন</option>
                                            <?php if ($exams && $exams->num_rows > 0): ?>
                                                <?php while ($exam = $exams->fetch_assoc()): ?>
                                                    <option value="<?php echo $exam['id']; ?>" <?php echo (isset($_GET['exam_id']) && $_GET['exam_id'] == $exam['id']) ? 'selected' : ''; ?>>
                                                        <?php
                                                            echo htmlspecialchars($exam['exam_name']) . ' - ' . htmlspecialchars($exam['exam_type']);
                                                            if (!empty($exam['class_name'])) {
                                                                echo ' (' . htmlspecialchars($exam['class_name']);
                                                                if (!empty($exam['department_name'])) {
                                                                    echo '/' . htmlspecialchars($exam['department_name']);
                                                                }
                                                                echo ')';
                                                            }
                                                        ?>
                                                    </option>
                                                <?php endwhile; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-search me-2"></i>পরীক্ষা দেখুন
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Subject Assignment Form -->
                    <div class="col-md-8 mb-4">
                        <?php if ($selectedExam): ?>
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">বিষয় অ্যাসাইন করুন</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-4">
                                        <h5>পরীক্ষার তথ্য</h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>পরীক্ষার নাম:</strong> <?php echo htmlspecialchars($selectedExam['exam_name']); ?></p>
                                                <p><strong>পরীক্ষার ধরন:</strong> <?php echo htmlspecialchars($selectedExam['exam_type']); ?></p>
                                                <p><strong>তারিখ:</strong> <?php echo date('d/m/Y', strtotime($selectedExam['exam_date'])); ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>শ্রেণী:</strong> <?php echo htmlspecialchars($selectedExam['class_name'] ?? 'N/A'); ?></p>
                                                <p><strong>বিভাগ:</strong> <?php echo htmlspecialchars($selectedExam['department_name'] ?? 'N/A'); ?></p>
                                                <p><strong>সেশন:</strong> <?php echo htmlspecialchars($selectedExam['session_name'] ?? 'N/A'); ?></p>
                                            </div>
                                        </div>
                                    </div>

                                    <form method="POST" action="">
                                        <input type="hidden" name="exam_id" value="<?php echo $selectedExam['id']; ?>">

                                        <div class="mb-3">
                                            <label for="subject_ids" class="form-label">বিষয়সমূহ নির্বাচন করুন</label>
                                            <select class="form-select" id="subject_ids" name="subject_ids[]" multiple required>
                                                <option value="all">সকল বিষয়</option>
                                                <?php if ($subjects && $subjects->num_rows > 0): ?>
                                                    <?php
                                                    // Reset the subjects result pointer
                                                    $subjects->data_seek(0);

                                                    // Create an array of assigned subject IDs for easy checking
                                                    $assignedSubjectIds = array_map(function($subject) {
                                                        return $subject['subject_id'];
                                                    }, $assignedSubjects);

                                                    while ($subject = $subjects->fetch_assoc()):
                                                    ?>
                                                        <option value="<?php echo $subject['id']; ?>" <?php echo in_array($subject['id'], $assignedSubjectIds) ? 'selected' : ''; ?>>
                                                            <?php echo htmlspecialchars($subject['subject_name']); ?>
                                                            <?php if (!empty($subject['subject_code'])): ?>
                                                                (<?php echo htmlspecialchars($subject['subject_code']); ?>)
                                                            <?php endif; ?>
                                                        </option>
                                                    <?php endwhile; ?>
                                                <?php endif; ?>
                                            </select>
                                            <div class="form-text">একাধিক বিষয় নির্বাচন করতে Ctrl/Cmd কী চেপে ধরুন। সকল বিষয় নির্বাচন করতে "সকল বিষয়" অপশন নির্বাচন করুন।</div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="total_marks" class="form-label">মোট নম্বর</label>
                                                <input type="number" class="form-control" id="total_marks" name="total_marks" value="100" min="1" required>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="passing_marks" class="form-label">পাস নম্বর</label>
                                                <input type="number" class="form-control" id="passing_marks" name="passing_marks" value="33" min="1" required>
                                            </div>
                                        </div>

                                        <div class="d-grid gap-2">
                                            <button type="submit" name="assign_subjects" class="btn btn-success">
                                                <i class="fas fa-save me-2"></i>বিষয় অ্যাসাইন করুন
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- Assigned Subjects Table -->
                            <?php if (!empty($assignedSubjects)): ?>
                                <div class="card mt-4">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0">অ্যাসাইন করা বিষয়সমূহ</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>#</th>
                                                        <th>বিষয়</th>
                                                        <th>কোড</th>
                                                        <th>মোট নম্বর</th>
                                                        <th>পাস নম্বর</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($assignedSubjects as $index => $subject): ?>
                                                        <tr>
                                                            <td><?php echo $index + 1; ?></td>
                                                            <td><?php echo htmlspecialchars($subject['subject_name']); ?></td>
                                                            <td><?php echo htmlspecialchars($subject['subject_code'] ?? 'N/A'); ?></td>
                                                            <td><?php echo $subject['total_marks']; ?></td>
                                                            <td><?php echo $subject['passing_marks']; ?></td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="card">
                                <div class="card-body text-center py-5">
                                    <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                                    <h4>পরীক্ষা নির্বাচন করুন</h4>
                                    <p class="text-muted">বিষয় অ্যাসাইন করতে বাম পাশ থেকে একটি পরীক্ষা নির্বাচন করুন।</p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script>
        $(document).ready(function() {
            // Initialize Select2
            $('#subject_ids').select2({
                theme: 'bootstrap-5',
                placeholder: 'বিষয়সমূহ নির্বাচন করুন',
                allowClear: true
            });

            // Handle "All Subjects" option
            $('#subject_ids').on('change', function() {
                var selectedValues = $(this).val();

                // If "all" is selected, deselect other options
                if (selectedValues && selectedValues.includes('all')) {
                    $(this).val(['all']).trigger('change');
                }
            });
        });
    </script>
</body>
</html>
