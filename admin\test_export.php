<?php
// Simple Export Test
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

$export = $_GET['export'] ?? '';

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>Export Test</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-4'>
    <h2>Export Functionality Test</h2>";

if (!empty($export)) {
    echo "<div class='alert alert-info'>
        <h4>Export Request Received!</h4>
        <p><strong>Export Type:</strong> " . htmlspecialchars($export) . "</p>
        <p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>
    </div>";
    
    // Test CSV Export
    if ($export === 'csv') {
        echo "<div class='alert alert-success'>Testing CSV Export...</div>";
        
        // Set headers
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename=test_export_' . date('Y-m-d_H-i-s') . '.csv');
        
        // Create output
        $output = fopen('php://output', 'w');
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF)); // UTF-8 BOM
        
        // Add headers
        fputcsv($output, ['আইডি', 'নাম', 'পরিমাণ', 'তারিখ']);
        
        // Add test data
        fputcsv($output, ['1', 'টেস্ট শিক্ষার্থী ১', '১০০০', date('d/m/Y')]);
        fputcsv($output, ['2', 'টেস্ট শিক্ষার্থী ২', '১৫০০', date('d/m/Y')]);
        fputcsv($output, ['3', 'টেস্ট শিক্ষার্থী ৩', '২০০০', date('d/m/Y')]);
        
        fclose($output);
        exit;
    }
    
    // Test Excel Export
    if ($export === 'excel') {
        echo "<div class='alert alert-success'>Testing Excel Export...</div>";
        
        // Set headers
        header('Content-Type: application/vnd.ms-excel; charset=utf-8');
        header('Content-Disposition: attachment; filename=test_export_' . date('Y-m-d_H-i-s') . '.xls');
        
        // Create output
        $output = fopen('php://output', 'w');
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF)); // UTF-8 BOM
        
        // Add headers
        fputcsv($output, ['আইডি', 'নাম', 'পরিমাণ', 'তারিখ']);
        
        // Add test data
        fputcsv($output, ['1', 'টেস্ট শিক্ষার্থী ১', '১০০০', date('d/m/Y')]);
        fputcsv($output, ['2', 'টেস্ট শিক্ষার্থী ২', '১৫০০', date('d/m/Y')]);
        fputcsv($output, ['3', 'টেস্ট শিক্ষার্থী ৩', '২০০০', date('d/m/Y')]);
        
        fclose($output);
        exit;
    }
    
} else {
    echo "<div class='alert alert-warning'>No export parameter provided</div>";
}

echo "
    <div class='card'>
        <div class='card-header'>
            <h5>Test Export Links</h5>
        </div>
        <div class='card-body'>
            <div class='row'>
                <div class='col-md-6'>
                    <h6>Direct Links:</h6>
                    <ul class='list-group'>
                        <li class='list-group-item'>
                            <a href='test_export.php?export=csv' class='btn btn-success btn-sm'>
                                <i class='fas fa-file-csv'></i> Test CSV Export
                            </a>
                        </li>
                        <li class='list-group-item'>
                            <a href='test_export.php?export=excel' class='btn btn-info btn-sm'>
                                <i class='fas fa-file-excel'></i> Test Excel Export
                            </a>
                        </li>
                    </ul>
                </div>
                <div class='col-md-6'>
                    <h6>JavaScript Test:</h6>
                    <button class='btn btn-primary btn-sm' onclick='testJsExport()'>
                        <i class='fas fa-code'></i> Test JS Export
                    </button>
                    <div id='jsResult' class='mt-2'></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class='mt-3'>
        <a href='fee_management.php' class='btn btn-secondary'>
            <i class='fas fa-arrow-left'></i> Back to Fee Management
        </a>
    </div>
</div>

<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
<script src='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js'></script>
<script>
function testJsExport() {
    const resultDiv = document.getElementById('jsResult');
    resultDiv.innerHTML = '<div class=\"alert alert-info\">Testing JavaScript export...</div>';
    
    // Test URL generation
    const testUrl = 'test_export.php?export=csv';
    console.log('Test URL:', testUrl);
    
    // Try to open
    const newWindow = window.open(testUrl, '_blank');
    
    if (newWindow) {
        resultDiv.innerHTML = '<div class=\"alert alert-success\">Export window opened successfully!</div>';
    } else {
        resultDiv.innerHTML = '<div class=\"alert alert-danger\">Failed to open export window. Check popup blocker.</div>';
    }
}
</script>
</body>
</html>";
?>
