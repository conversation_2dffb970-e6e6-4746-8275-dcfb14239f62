<?php
// Define site name and other constants
define('SITE_NAME', 'স্কুল ম্যানেজমেন্ট সিস্টেম');

/**
 * Function to sanitize input data
 * @param string $data Input data to sanitize
 * @return string Sanitized data
 */
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * Function to validate date format (YYYY-MM-DD)
 * @param string $date Date string to validate
 * @return bool True if valid, false otherwise
 */
function validate_date($date) {
    $format = 'Y-m-d';
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}

/**
 * Function to check if a string contains only Bengali characters
 * @param string $text Text to check
 * @return bool True if contains only Bengali, false otherwise
 */
function is_bengali($text) {
    return preg_match('/^[\p{Bengali}\s]+$/u', $text);
}

/**
 * Function to format date to Bengali format
 * @param string $date Date in Y-m-d format
 * @return string Formatted date
 */
function format_date($date) {
    if (empty($date)) return '';
    return date('d M, Y', strtotime($date));
}

/**
 * Generate a random password
 * @param int $length Password length
 * @return string Random password
 */
function generate_password($length = 8) {
    $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';
    $password = '';
    for ($i = 0; $i < $length; $i++) {
        $password .= $chars[rand(0, strlen($chars) - 1)];
    }
    return $password;
}

/**
 * Convert English numbers to Bengali numbers
 * @param string $number Number in English
 * @return string Number in Bengali
 */
function en_to_bn_number($number) {
    $bn = array("১", "২", "৩", "৪", "৫", "৬", "৭", "৮", "৯", "০");
    $en = array("1", "2", "3", "4", "5", "6", "7", "8", "9", "0");
    return str_replace($en, $bn, $number);
}

/**
 * Get current academic year
 * @return string Current academic year
 */
function get_current_academic_year() {
    $month = date('n');
    $year = date('Y');

    if ($month < 6) { // If before June, academic year is previous-current
        return ($year - 1) . '-' . $year;
    } else { // If June or later, academic year is current-next
        return $year . '-' . ($year + 1);
    }
}

/**
 * Convert grade points to letter grade
 * @param float $points Grade points
 * @return string Letter grade
 */
function get_letter_grade($points) {
    if ($points >= 5.0) return 'A+';
    if ($points >= 4.0) return 'A';
    if ($points >= 3.5) return 'A-';
    if ($points >= 3.0) return 'B+';
    if ($points >= 2.5) return 'B';
    if ($points >= 2.0) return 'C';
    if ($points >= 1.0) return 'D';
    return 'F';
}

/**
 * Calculate the GPA contribution from the fourth subject
 * @param float $fourth_subject_gpa The GPA achieved in the fourth subject
 * @param float $excess_point_limit The configured excess point limit (default 2)
 * @return float Additional GPA points to add to total
 */
function calculate_fourth_subject_contribution($fourth_subject_gpa, $excess_point_limit = 2) {
    global $conn;

    // If no connection provided, try to get the excess point limit from database
    if (!isset($excess_point_limit) && isset($conn)) {
        $query = "SELECT excess_point_limit FROM fourth_subject_config LIMIT 1";
        $result = mysqli_query($conn, $query);
        if ($result && mysqli_num_rows($result) > 0) {
            $row = mysqli_fetch_assoc($result);
            $excess_point_limit = floatval($row['excess_point_limit']);
        }
    }

    // If GPA is less than or equal to the limit, return 0
    if ($fourth_subject_gpa <= $excess_point_limit) {
        return 0;
    }

    // Otherwise, return the excess points
    return $fourth_subject_gpa - $excess_point_limit;
}

/**
 * Ensure database connection is active and return connection object
 * @return mysqli Database connection object
 */
function ensure_connection() {
    global $conn;

    if (!isset($conn) || $conn->connect_error) {
        // If connection is not set or has error, try to reconnect
        require_once __DIR__ . '/../../includes/dbh.inc.php';
    }

    return $conn;
}
?>