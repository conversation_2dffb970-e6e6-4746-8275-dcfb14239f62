<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বিষয় সেটআপ গাইড - অ্যাডমিন প্যানেল</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/admin-style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Include Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">📚 বিষয় সেটআপ গাইড</h1>
                    <div>
                        <a href="dashboard.php" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-1"></i> ড্যাশবোর্ডে ফিরুন
                        </a>
                    </div>
                </div>

                <!-- Problem Explanation -->
                <div class="alert alert-warning">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>সমস্যা কী ছিল?</h5>
                    <p>CSV আপলোডের সময় সব বিষয় 'optional' হিসেবে সেট হয়ে যাচ্ছিল। এর কারণ ছিল কোডে হার্ডকোড করা 'optional' ক্যাটাগরি।</p>
                </div>

                <!-- Solution Explanation -->
                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle me-2"></i>সমাধান কী করা হয়েছে?</h5>
                    <p>এখন CSV আপলোড কোড স্বয়ংক্রিয়ভাবে সঠিক ক্যাটাগরি নির্ধারণ করে:</p>
                    <ol>
                        <li>প্রথমে <code>department_subject_types</code> টেবিল চেক করে</li>
                        <li>তারপর <code>subjects</code> টেবিলের category চেক করে</li>
                        <li>সবশেষে default হিসেবে 'optional' সেট করে</li>
                    </ol>
                </div>

                <!-- Setup Steps -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5><i class="fas fa-cog me-2"></i>ধাপ ১: বিষয় তৈরি করুন</h5>
                            </div>
                            <div class="card-body">
                                <ol>
                                    <li><a href="subjects.php" class="btn btn-sm btn-outline-primary">বিষয় ম্যানেজমেন্ট</a> পেজে যান</li>
                                    <li>নতুন বিষয় যোগ করুন</li>
                                    <li>প্রতিটি বিষয়ের জন্য সঠিক category সেট করুন:
                                        <ul>
                                            <li><span class="badge bg-success">required</span> - আবশ্যিক বিষয়</li>
                                            <li><span class="badge bg-warning">optional</span> - ঐচ্ছিক বিষয়</li>
                                            <li><span class="badge bg-info">fourth</span> - চতুর্থ বিষয়</li>
                                        </ul>
                                    </li>
                                </ol>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5><i class="fas fa-link me-2"></i>ধাপ ২: বিভাগ-বিষয় সংযোগ</h5>
                            </div>
                            <div class="card-body">
                                <ol>
                                    <li>প্রতিটি বিভাগের জন্য বিষয়গুলো নির্ধারণ করুন</li>
                                    <li><code>department_subject_types</code> টেবিলে সঠিক mapping যুক্ত করুন</li>
                                    <li>প্রতিটি বিভাগে কোন বিষয় কী ধরনের তা নির্ধারণ করুন</li>
                                </ol>
                                <div class="alert alert-info mt-3">
                                    <small><strong>উদাহরণ:</strong> বিজ্ঞান বিভাগে গণিত 'required', কিন্তু কলা বিভাগে 'optional' হতে পারে।</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- CSV Upload Best Practices -->
                <div class="card mt-4">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-file-csv me-2"></i>CSV আপলোডের সর্বোত্তম অনুশীলন</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>✅ করণীয়:</h6>
                                <ul>
                                    <li>আপলোডের আগে বিষয় সেটআপ সম্পূর্ণ করুন</li>
                                    <li>Template ডাউনলোড করে ব্যবহার করুন</li>
                                    <li>Subject ID গুলো সঠিক আছে কিনা যাচাই করুন</li>
                                    <li>ছোট ব্যাচে আপলোড করুন</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>❌ এড়িয়ে চলুন:</h6>
                                <ul>
                                    <li>বিষয় সেটআপ ছাড়া CSV আপলোড</li>
                                    <li>ভুল Subject ID ব্যবহার</li>
                                    <li>একসাথে অনেক বড় ফাইল আপলোড</li>
                                    <li>ব্যাকআপ ছাড়া আপলোড</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-bolt me-2"></i>দ্রুত কার্যক্রম</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="subjects.php" class="btn btn-primary w-100 mb-2">
                                    <i class="fas fa-book me-2"></i>বিষয় ম্যানেজমেন্ট
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="subject_csv_upload.php" class="btn btn-success w-100 mb-2">
                                    <i class="fas fa-upload me-2"></i>বিষয় CSV আপলোড
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="fix_subject_categories.php" class="btn btn-warning w-100 mb-2">
                                    <i class="fas fa-tools me-2"></i>ক্যাটাগরি ঠিক করুন
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="students.php" class="btn btn-info w-100 mb-2">
                                    <i class="fas fa-users me-2"></i>শিক্ষার্থী তালিকা
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Technical Details -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-code me-2"></i>প্রযুক্তিগত বিবরণ</h5>
                    </div>
                    <div class="card-body">
                        <h6>আপডেট করা ফাইলসমূহ:</h6>
                        <ul>
                            <li><code>admin/student_subjects_upload.php</code> - উন্নত ক্যাটাগরি ডিটেকশন</li>
                            <li><code>admin/subject_csv_upload.php</code> - উন্নত ক্যাটাগরি ডিটেকশন</li>
                            <li><code>admin/fix_subject_categories.php</code> - বিদ্যমান ডেটা ঠিক করার টুল</li>
                        </ul>
                        
                        <h6 class="mt-3">ক্যাটাগরি নির্ধারণের ক্রম:</h6>
                        <ol>
                            <li><code>department_subject_types.subject_type</code> (প্রাথমিকতা)</li>
                            <li><code>subjects.category</code> (ফলব্যাক)</li>
                            <li><code>'optional'</code> (ডিফল্ট)</li>
                        </ol>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
