<?php
require_once '../includes/dbh.inc.php';

// Create governing_board_members table if not exists
$sql = "CREATE TABLE IF NOT EXISTS governing_board_members (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HAR(100) NOT NULL,
    position VARCHAR(100) NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    photo VARCHAR(255),
    bio TEXT,
    display_order INT DEFAULT 0,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($sql) === TRUE) {
    echo "Governing Board Members table created successfully!";
} else {
    echo "Error creating Governing Board Members table: " . $conn->error;
}

// Close connection
$conn->close();
?>
