<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if exam_types table exists
$checkExamTypesTable = $conn->query("SHOW TABLES LIKE 'exam_types'");
if ($checkExamTypesTable->num_rows == 0) {
    echo "Error: exam_types table does not exist. Please visit exam_types.php first.";
    exit();
}

// Check if "প্রাক-নির্বাচনী" already exists
$checkType = $conn->prepare("SELECT id FROM exam_types WHERE type_name = ?");
$typeName = "প্রাক-নির্বাচনী";
$checkType->bind_param("s", $typeName);
$checkType->execute();
$result = $checkType->get_result();

if ($result->num_rows > 0) {
    echo "Success: 'প্রাক-নির্বাচনী' type already exists in the database.";
} else {
    // Add "প্রাক-নির্বাচনী" type
    $insertType = $conn->prepare("INSERT INTO exam_types (type_name, description, is_active) VALUES (?, ?, ?)");
    $description = "প্রাক-নির্বাচনী পরীক্ষা";
    $isActive = 1;
    $insertType->bind_param("ssi", $typeName, $description, $isActive);
    
    if ($insertType->execute()) {
        echo "Success: 'প্রাক-নির্বাচনী' type has been added to the database.";
    } else {
        echo "Error: Failed to add 'প্রাক-নির্বাচনী' type. " . $conn->error;
    }
}

// Redirect back to exam_types.php after 3 seconds
header("refresh:3;url=exam_types.php");
?>
