<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$testResults = [];

// Test 1: Check if subjects table has required columns
$testResults['table_structure'] = [];
$columnsQuery = "DESCRIBE subjects";
$result = $conn->query($columnsQuery);
while ($column = $result->fetch_assoc()) {
    $testResults['table_structure'][] = $column['Field'];
}

// Test 2: Check if action button files exist
$testResults['files_exist'] = [
    'edit_subject.php' => file_exists('edit_subject.php'),
    'subject_assignment.php' => file_exists('subject_assignment.php'),
    'js/subjects.js' => file_exists('js/subjects.js'),
    'css/admin-style.css' => file_exists('css/admin-style.css')
];

// Test 3: Check sample subjects data
$testResults['sample_data'] = [];
$sampleQuery = "SELECT id, subject_name, subject_code, category, is_active FROM subjects LIMIT 5";
$result = $conn->query($sampleQuery);
while ($row = $result->fetch_assoc()) {
    $testResults['sample_data'][] = $row;
}

// Test 4: Check if category column has proper values
$categoryQuery = "SELECT category, COUNT(*) as count FROM subjects GROUP BY category";
$result = $conn->query($categoryQuery);
$testResults['category_distribution'] = [];
while ($row = $result->fetch_assoc()) {
    $testResults['category_distribution'][] = $row;
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বিষয় ফাংশনালিটি টেস্ট - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/admin-style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Include Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">🧪 বিষয় ফাংশনালিটি টেস্ট</h1>
                    <div>
                        <a href="subjects.php" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-1"></i> বিষয় তালিকায় ফিরুন
                        </a>
                    </div>
                </div>

                <!-- Test Results -->
                <div class="row">
                    <!-- Table Structure Test -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="fas fa-table me-2"></i>টেবিল গঠন পরীক্ষা</h5>
                            </div>
                            <div class="card-body">
                                <h6>subjects টেবিলের কলামসমূহ:</h6>
                                <ul class="list-group list-group-flush">
                                    <?php foreach ($testResults['table_structure'] as $column): ?>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <?php echo $column; ?>
                                            <?php if (in_array($column, ['category', 'description'])): ?>
                                                <span class="badge bg-success">নতুন</span>
                                            <?php else: ?>
                                                <span class="badge bg-primary">মূল</span>
                                            <?php endif; ?>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Files Existence Test -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0"><i class="fas fa-file-check me-2"></i>ফাইল অস্তিত্ব পরীক্ষা</h5>
                            </div>
                            <div class="card-body">
                                <h6>প্রয়োজনীয় ফাইলসমূহ:</h6>
                                <ul class="list-group list-group-flush">
                                    <?php foreach ($testResults['files_exist'] as $file => $exists): ?>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <?php echo $file; ?>
                                            <?php if ($exists): ?>
                                                <span class="badge bg-success"><i class="fas fa-check"></i> আছে</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger"><i class="fas fa-times"></i> নেই</span>
                                            <?php endif; ?>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Sample Data Test -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="fas fa-database me-2"></i>নমুনা ডেটা পরীক্ষা</h5>
                            </div>
                            <div class="card-body">
                                <?php if (count($testResults['sample_data']) > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>কোড</th>
                                                    <th>নাম</th>
                                                    <th>ধরন</th>
                                                    <th>অবস্থা</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($testResults['sample_data'] as $subject): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($subject['subject_code']); ?></td>
                                                        <td><?php echo htmlspecialchars($subject['subject_name']); ?></td>
                                                        <td>
                                                            <?php
                                                            $category = $subject['category'] ?? 'optional';
                                                            switch($category) {
                                                                case 'required': echo '<span class="badge bg-success">আবশ্যিক</span>'; break;
                                                                case 'fourth': echo '<span class="badge bg-info">চতুর্থ</span>'; break;
                                                                default: echo '<span class="badge bg-warning">ঐচ্ছিক</span>';
                                                            }
                                                            ?>
                                                        </td>
                                                        <td>
                                                            <?php if ($subject['is_active']): ?>
                                                                <span class="badge bg-success">সক্রিয়</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-danger">নিষ্ক্রিয়</span>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>কোন বিষয় ডেটা পাওয়া যায়নি
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Category Distribution Test -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>ক্যাটেগরি বিতরণ পরীক্ষা</h5>
                            </div>
                            <div class="card-body">
                                <?php if (count($testResults['category_distribution']) > 0): ?>
                                    <ul class="list-group list-group-flush">
                                        <?php foreach ($testResults['category_distribution'] as $cat): ?>
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                <?php
                                                $categoryName = '';
                                                switch($cat['category']) {
                                                    case 'required': $categoryName = 'আবশ্যিক'; break;
                                                    case 'fourth': $categoryName = 'চতুর্থ বিষয়'; break;
                                                    case 'optional': $categoryName = 'ঐচ্ছিক'; break;
                                                    default: $categoryName = $cat['category'] ?: 'অনির্ধারিত';
                                                }
                                                echo $categoryName;
                                                ?>
                                                <span class="badge bg-primary rounded-pill"><?php echo $cat['count']; ?></span>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                <?php else: ?>
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>কোন ক্যাটেগরি ডেটা পাওয়া যায়নি
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons Test -->
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0"><i class="fas fa-mouse-pointer me-2"></i>অ্যাকশন বাটন পরীক্ষা</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>পরীক্ষা করার জন্য:</h6>
                            <ol>
                                <li><a href="subjects.php" target="_blank">বিষয় তালিকা পেজে যান</a></li>
                                <li>যেকোনো বিষয়ের অ্যাকশন বাটনে ক্লিক করুন</li>
                                <li>নিশ্চিতকরণ ডায়ালগ দেখা যাচ্ছে কিনা পরীক্ষা করুন</li>
                                <li>এডিট এবং অ্যাসাইনমেন্ট বাটন সঠিক পেজে নিয়ে যাচ্ছে কিনা দেখুন</li>
                            </ol>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center p-3 border rounded">
                                    <a href="#" class="btn btn-sm btn-info action-btn" onclick="alert('এডিট বাটন কাজ করছে!'); return false;">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <p class="mt-2 mb-0 small">এডিট বাটন</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 border rounded">
                                    <a href="#" class="btn btn-sm btn-warning action-btn" onclick="return confirm('স্ট্যাটাস টগল করবেন?');">
                                        <i class="fas fa-times"></i>
                                    </a>
                                    <p class="mt-2 mb-0 small">স্ট্যাটাস টগল</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 border rounded">
                                    <a href="#" class="btn btn-sm btn-primary action-btn" onclick="alert('অ্যাসাইনমেন্ট বাটন কাজ করছে!'); return false;">
                                        <i class="fas fa-user-check"></i>
                                    </a>
                                    <p class="mt-2 mb-0 small">শিক্ষক অ্যাসাইনমেন্ট</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 border rounded">
                                    <a href="#" class="btn btn-sm btn-danger action-btn" onclick="return confirm('মুছে ফেলবেন?');">
                                        <i class="fas fa-trash-alt"></i>
                                    </a>
                                    <p class="mt-2 mb-0 small">ডিলিট বাটন</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Summary -->
                <div class="card mt-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-check-circle me-2"></i>সারসংক্ষেপ</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <h6><i class="fas fa-thumbs-up me-2"></i>সমাধান সম্পন্ন!</h6>
                            <p class="mb-2">নিম্নলিখিত সমস্যাগুলো সমাধান করা হয়েছে:</p>
                            <ul class="mb-0">
                                <li>✅ subjects টেবিলে category এবং description কলাম যুক্ত করা হয়েছে</li>
                                <li>✅ edit_subject.php ফাইল আপডেট করা হয়েছে</li>
                                <li>✅ subject_assignment.php ফাইল যাচাই করা হয়েছে</li>
                                <li>✅ অ্যাকশন বাটনের জন্য JavaScript যুক্ত করা হয়েছে</li>
                                <li>✅ CSS স্টাইলিং উন্নত করা হয়েছে</li>
                                <li>✅ নিশ্চিতকরণ ডায়ালগ যুক্ত করা হয়েছে</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
