<?php
require_once '../includes/dbh.inc.php';

echo "<h1>Running Complete Database Setup</h1>";

// Create users table if it doesn't exist
$usersTableQuery = "CREATE TABLE IF NOT EXISTS users (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    user_type ENUM('admin', 'teacher', 'student', 'staff') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($usersTableQuery)) {
    echo "users table created or already exists!<br>";
} else {
    echo "Error creating users table: " . $conn->error . "<br>";
}

// Create departments table if it doesn't exist
$departmentsTableQuery = "CREATE TABLE IF NOT EXISTS departments (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    department_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($departmentsTableQuery)) {
    echo "departments table created or already exists!<br>";
} else {
    echo "Error creating departments table: " . $conn->error . "<br>";
}

// Create sessions table if it doesn't exist
$sessionsTableQuery = "CREATE TABLE IF NOT EXISTS sessions (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    session_name VARCHAR(50) NOT NULL UNIQUE,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($sessionsTableQuery)) {
    echo "sessions table created or already exists!<br>";
} else {
    echo "Error creating sessions table: " . $conn->error . "<br>";
}

// Create classes table if it doesn't exist
$classesTableQuery = "CREATE TABLE IF NOT EXISTS classes (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    class_name VARCHAR(50) NOT NULL,
    department_id INT(11) NULL,
    section VARCHAR(20) DEFAULT 'A',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
)";

if ($conn->query($classesTableQuery)) {
    echo "classes table created or already exists!<br>";
} else {
    echo "Error creating classes table: " . $conn->error . "<br>";
}

// Create students table if it doesn't exist
$studentsTableQuery = "CREATE TABLE IF NOT EXISTS students (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id VARCHAR(20) NOT NULL UNIQUE,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) NULL,
    phone VARCHAR(20) NULL,
    gender ENUM('male', 'female', 'other') NOT NULL,
    dob DATE NULL,
    address TEXT NULL,
    city VARCHAR(50) NULL,
    state VARCHAR(50) NULL,
    postal_code VARCHAR(20) NULL,
    country VARCHAR(50) NULL DEFAULT 'Bangladesh',
    admission_date DATE NULL,
    profile_photo VARCHAR(255) NULL,
    department_id INT(11) NULL,
    class_id INT(11) NULL,
    session_id INT(11) NULL,
    user_id INT(11) NULL,
    batch VARCHAR(50) NULL,
    group_name VARCHAR(50) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
)";

if ($conn->query($studentsTableQuery)) {
    echo "students table created or already exists!<br>";
} else {
    echo "Error creating students table: " . $conn->error . "<br>";
}

// Create teachers table if it doesn't exist
$teachersTableQuery = "CREATE TABLE IF NOT EXISTS teachers (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    teacher_id VARCHAR(20) NOT NULL UNIQUE,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) NULL,
    phone VARCHAR(20) NULL,
    gender ENUM('male', 'female', 'other') NOT NULL,
    dob DATE NULL,
    address TEXT NULL,
    qualification VARCHAR(255) NULL,
    joining_date DATE NULL,
    profile_photo VARCHAR(255) NULL,
    department_id INT(11) NULL,
    user_id INT(11) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
)";

if ($conn->query($teachersTableQuery)) {
    echo "teachers table created or already exists!<br>";
} else {
    echo "Error creating teachers table: " . $conn->error . "<br>";
}

// Create staff table if it doesn't exist
$staffTableQuery = "CREATE TABLE IF NOT EXISTS staff (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    staff_id VARCHAR(20) NOT NULL UNIQUE,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) NULL,
    phone VARCHAR(20) NULL,
    gender ENUM('male', 'female', 'other') NOT NULL,
    dob DATE NULL,
    address TEXT NULL,
    position VARCHAR(100) NULL,
    joining_date DATE NULL,
    profile_photo VARCHAR(255) NULL,
    department_id INT(11) NULL,
    user_id INT(11) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
)";

if ($conn->query($staffTableQuery)) {
    echo "staff table created or already exists!<br>";
} else {
    echo "Error creating staff table: " . $conn->error . "<br>";
}

// Create subjects table if it doesn't exist
$subjectsTableQuery = "CREATE TABLE IF NOT EXISTS subjects (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    subject_name VARCHAR(100) NOT NULL,
    subject_code VARCHAR(20) NOT NULL,
    department_id INT(11) NULL,
    category VARCHAR(255) DEFAULT 'required',
    description TEXT,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE
)";

if ($conn->query($subjectsTableQuery)) {
    echo "subjects table created or already exists!<br>";
} else {
    echo "Error creating subjects table: " . $conn->error . "<br>";
}

// Create a mapping table for subjects to departments (many-to-many)
$subjectDeptTableQuery = "CREATE TABLE IF NOT EXISTS subject_departments (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    subject_id INT(11) NOT NULL,
    department_id INT(11) NOT NULL,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
    UNIQUE KEY (subject_id, department_id)
)";

if ($conn->query($subjectDeptTableQuery)) {
    echo "subject_departments table created or already exists!<br>";
} else {
    echo "Error creating subject_departments table: " . $conn->error . "<br>";
}

// Create student_subjects table if it doesn't exist
$studentSubjectsTableQuery = "CREATE TABLE IF NOT EXISTS student_subjects (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    subject_id INT(11) NOT NULL,
    category VARCHAR(50) NOT NULL,
    selection_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    session_id INT(11) NULL,
    UNIQUE KEY(student_id, subject_id),
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE
)";

if ($conn->query($studentSubjectsTableQuery)) {
    echo "student_subjects table created or already exists!<br>";
} else {
    echo "Error creating student_subjects table: " . $conn->error . "<br>";
}

// Create notices table
$noticesTableQuery = "CREATE TABLE IF NOT EXISTS notices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    target_audience ENUM('all', 'students', 'teachers', 'staff') NOT NULL DEFAULT 'all',
    expiry_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

if ($conn->query($noticesTableQuery)) {
    echo "notices table created or already exists!<br>";
} else {
    echo "Error creating notices table: " . $conn->error . "<br>";
}

// Create exams table
$examsTableQuery = "CREATE TABLE IF NOT EXISTS exams (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    exam_name VARCHAR(100) NOT NULL,
    subject_id INT(11) NULL,
    class_id INT(11) NULL,
    exam_date DATE NOT NULL,
    start_time TIME NULL,
    end_time TIME NULL,
    total_marks INT(11) NOT NULL,
    passing_marks INT(11) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE
)";

if ($conn->query($examsTableQuery)) {
    echo "exams table created or already exists!<br>";
} else {
    echo "Error creating exams table: " . $conn->error . "<br>";
}

// Create results table
$resultsTableQuery = "CREATE TABLE IF NOT EXISTS results (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    exam_id INT(11) NOT NULL,
    marks_obtained DECIMAL(10,2) NOT NULL,
    grade VARCHAR(10) NULL,
    remarks TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (exam_id) REFERENCES exams(id) ON DELETE CASCADE
)";

if ($conn->query($resultsTableQuery)) {
    echo "results table created or already exists!<br>";
} else {
    echo "Error creating results table: " . $conn->error . "<br>";
}



// Create certificates table
$certificatesTableQuery = "CREATE TABLE IF NOT EXISTS certificates (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    certificate_type VARCHAR(100) NOT NULL,
    issue_date DATE NOT NULL,
    certificate_number VARCHAR(50) NOT NULL UNIQUE,
    remarks TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE
)";

if ($conn->query($certificatesTableQuery)) {
    echo "certificates table created or already exists!<br>";
} else {
    echo "Error creating certificates table: " . $conn->error . "<br>";
}

// Create gb_members table (Governing Body Members)
$gbMembersTableQuery = "CREATE TABLE IF NOT EXISTS gb_members (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    position VARCHAR(100) NOT NULL,
    photo VARCHAR(255) NULL,
    bio TEXT NULL,
    contact_email VARCHAR(100) NULL,
    contact_phone VARCHAR(20) NULL,
    display_order INT(11) DEFAULT 0,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($gbMembersTableQuery)) {
    echo "gb_members table created or already exists!<br>";
} else {
    echo "Error creating gb_members table: " . $conn->error . "<br>";
}

// Create school_settings table
$schoolSettingsTableQuery = "CREATE TABLE IF NOT EXISTS school_settings (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($schoolSettingsTableQuery)) {
    echo "school_settings table created or already exists!<br>";
} else {
    echo "Error creating school_settings table: " . $conn->error . "<br>";
}

// Create subject_passing_config table
$subjectPassingConfigTableQuery = "CREATE TABLE IF NOT EXISTS subject_passing_config (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    subject_id INT(11) NOT NULL,
    passing_marks INT(11) NOT NULL DEFAULT 33,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
)";

if ($conn->query($subjectPassingConfigTableQuery)) {
    echo "subject_passing_config table created or already exists!<br>";
} else {
    echo "Error creating subject_passing_config table: " . $conn->error . "<br>";
}

// Create subject_exam_pattern table
$subjectExamPatternTableQuery = "CREATE TABLE IF NOT EXISTS subject_exam_pattern (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    subject_id INT(11) NOT NULL,
    component_name VARCHAR(100) NOT NULL,
    max_marks INT(11) NOT NULL,
    passing_marks INT(11) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
)";

if ($conn->query($subjectExamPatternTableQuery)) {
    echo "subject_exam_pattern table created or already exists!<br>";
} else {
    echo "Error creating subject_exam_pattern table: " . $conn->error . "<br>";
}

// Insert default admin user if not exists
$checkAdminQuery = "SELECT * FROM users WHERE username = 'admin' AND user_type = 'admin'";
$adminResult = $conn->query($checkAdminQuery);

if ($adminResult->num_rows == 0) {
    // Admin doesn't exist, create one
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $insertAdminQuery = "INSERT INTO users (username, password, user_type)
                        VALUES ('admin', '$adminPassword', 'admin')";

    if ($conn->query($insertAdminQuery)) {
        echo "Default admin user created successfully!<br>";
        echo "Username: admin<br>";
        echo "Password: admin123<br>";
        echo "<strong>Please change this password after first login!</strong><br>";
    } else {
        echo "Error creating default admin user: " . $conn->error . "<br>";
    }
} else {
    echo "Admin user already exists!<br>";
}

echo "<p>Complete database setup finished successfully.</p>";
echo "<a href='../admin/dashboard.php' class='btn btn-primary'>Go to Admin Dashboard</a>";
?>
