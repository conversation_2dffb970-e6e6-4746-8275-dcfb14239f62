<?php
// Debug console page
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Pragma: no-cache');
header('Expires: 0');
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>নিশাত এডুকেশন সেন্টার - ডিবাগ</title>
    
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1e1e1e;
            color: #ffffff;
            margin: 0;
            padding: 20px;
        }
        
        .console {
            background: #000;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 20px;
            height: 400px;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-info { color: #00ff00; }
        .log-warn { color: #ffff00; }
        .log-error { color: #ff0000; }
        .log-debug { color: #00ffff; }
        
        .stats {
            background: #2d2d2d;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            display: inline-block;
            margin-right: 20px;
            padding: 5px 10px;
            background: #444;
            border-radius: 3px;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <h1>🔧 ZFAW বাফার ডিবাগ কনসোল</h1>
    
    <div class="stats">
        <div class="stat-item">
            <strong>পেজ টাইটেল:</strong> <span id="currentTitle">-</span>
        </div>
        <div class="stat-item">
            <strong>লোড টাইম:</strong> <span id="loadTime">-</span>
        </div>
        <div class="stat-item">
            <strong>ব্লক করা ইন্টারভাল:</strong> <span id="blockedIntervals">-</span>
        </div>
        <div class="stat-item">
            <strong>ব্লক করা টাইমআউট:</strong> <span id="blockedTimeouts">-</span>
        </div>
    </div>
    
    <div style="margin-bottom: 20px;">
        <button class="btn" onclick="checkStatus()">স্ট্যাটাস চেক করুন</button>
        <button class="btn" onclick="fixBuffer()">বাফার ঠিক করুন</button>
        <button class="btn btn-danger" onclick="clearConsole()">কনসোল ক্লিয়ার করুন</button>
        <button class="btn" onclick="window.open('index.php', '_blank')">মূল পেজ খুলুন</button>
    </div>
    
    <div class="console" id="console">
        <div class="log-entry log-info">🚀 ডিবাগ কনসোল শুরু হয়েছে...</div>
    </div>

    <!-- Load the ultimate buffer fix -->
    <script src="js/ultimate-buffer-fix.js"></script>
    
    <script>
        let logCount = 0;
        
        function log(message, type = 'info') {
            const console = document.getElementById('console');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            console.appendChild(entry);
            console.scrollTop = console.scrollHeight;
            logCount++;
        }
        
        function updateStats() {
            document.getElementById('currentTitle').textContent = document.title;
            document.getElementById('loadTime').textContent = new Date().toLocaleString('bn-BD');
            
            if (window.ultimateBufferFix) {
                document.getElementById('blockedIntervals').textContent = window.ultimateBufferFix.blockedIntervals();
                document.getElementById('blockedTimeouts').textContent = window.ultimateBufferFix.blockedTimeouts();
            }
        }
        
        function checkStatus() {
            log('🔍 স্ট্যাটাস চেক করা হচ্ছে...', 'info');
            
            // Check title
            if (document.title === 'নিশাত এডুকেশন সেন্টার - ডিবাগ') {
                log('✅ টাইটেল সঠিক আছে', 'info');
            } else {
                log('❌ টাইটেল ভুল: ' + document.title, 'error');
            }
            
            // Check for active intervals
            let activeIntervals = 0;
            for (let i = 1; i <= 1000; i++) {
                try {
                    if (clearInterval(i)) activeIntervals++;
                } catch (e) {}
            }
            
            if (activeIntervals === 0) {
                log('✅ কোনো সক্রিয় ইন্টারভাল নেই', 'info');
            } else {
                log(`⚠️ ${activeIntervals} টি সক্রিয় ইন্টারভাল পাওয়া গেছে`, 'warn');
            }
            
            // Check ultimate buffer fix
            if (window.ultimateBufferFix) {
                log(`📊 ব্লক করা: ${window.ultimateBufferFix.blockedIntervals()} ইন্টারভাল, ${window.ultimateBufferFix.blockedTimeouts()} টাইমআউট`, 'debug');
            } else {
                log('❌ Ultimate Buffer Fix লোড হয়নি', 'error');
            }
            
            updateStats();
        }
        
        function fixBuffer() {
            log('🔧 বাফার ঠিক করা হচ্ছে...', 'info');
            
            if (window.ultimateBufferFix && window.ultimateBufferFix.reapply) {
                window.ultimateBufferFix.reapply();
                log('✅ Ultimate Buffer Fix পুনরায় প্রয়োগ করা হয়েছে', 'info');
            }
            
            // Force title fix
            document.title = 'নিশাত এডুকেশন সেন্টার - ডিবাগ';
            log('📝 টাইটেল ঠিক করা হয়েছে', 'info');
            
            updateStats();
        }
        
        function clearConsole() {
            document.getElementById('console').innerHTML = '<div class="log-entry log-info">🧹 কনসোল ক্লিয়ার করা হয়েছে</div>';
            logCount = 0;
        }
        
        // Override console methods to capture logs
        const originalConsoleLog = console.log;
        const originalConsoleWarn = console.warn;
        const originalConsoleError = console.error;
        
        console.log = function(...args) {
            log('📝 ' + args.join(' '), 'debug');
            originalConsoleLog.apply(console, args);
        };
        
        console.warn = function(...args) {
            log('⚠️ ' + args.join(' '), 'warn');
            originalConsoleWarn.apply(console, args);
        };
        
        console.error = function(...args) {
            log('❌ ' + args.join(' '), 'error');
            originalConsoleError.apply(console, args);
        };
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('📱 DOM লোড সম্পন্ন', 'info');
            updateStats();
            
            // Auto-check status after 2 seconds
            setTimeout(checkStatus, 2000);
        });
        
        window.addEventListener('load', function() {
            log('🎯 পেজ সম্পূর্ণ লোড হয়েছে', 'info');
            updateStats();
        });
        
        // Update stats every 5 seconds
        setInterval(updateStats, 5000);
    </script>
</body>
</html>
