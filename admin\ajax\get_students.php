<?php
session_start();
require_once '../../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Set content type to JSON
header('Content-Type: application/json');

// Get request parameters
$sessionId = isset($_GET['session_id']) ? (int)$_GET['session_id'] : 0;
$classId = isset($_GET['class_id']) ? (int)$_GET['class_id'] : 0;
$departmentId = isset($_GET['department_id']) ? (int)$_GET['department_id'] : 0;

// Get advanced filter parameters
$status = isset($_GET['status']) ? $_GET['status'] : 'all';
$group = isset($_GET['group']) ? $_GET['group'] : '';
$gender = isset($_GET['gender']) ? $_GET['gender'] : '';
$searchTerm = isset($_GET['search']) ? $_GET['search'] : '';

// Check if at least one filter is provided
if ($sessionId == 0 && $classId == 0 && $departmentId == 0) {
    echo json_encode(['success' => false, 'message' => 'No filter provided']);
    exit();
}

// Prepare query
$query = "SELECT s.id, s.first_name, s.last_name, s.student_id, s.gender,
          c.class_name, ss.session_name, d.department_name
          FROM students s
          LEFT JOIN classes c ON s.class_id = c.id
          LEFT JOIN sessions ss ON s.session_id = ss.id
          LEFT JOIN departments d ON s.department_id = d.id
          WHERE 1=1";

$params = [];
$types = "";

// Add basic filters
if ($sessionId > 0) {
    $query .= " AND s.session_id = ?";
    $params[] = $sessionId;
    $types .= "i";
}

if ($classId > 0) {
    $query .= " AND s.class_id = ?";
    $params[] = $classId;
    $types .= "i";
}

if ($departmentId > 0) {
    $query .= " AND s.department_id = ?";
    $params[] = $departmentId;
    $types .= "i";
}

// Add advanced filters
// Status and group filters are commented out as these columns don't exist in the students table
// If you need these filters, you'll need to add these columns to the students table first
/*
if ($status !== 'all') {
    $query .= " AND s.status = ?";
    $params[] = $status;
    $types .= "s";
}

if (!empty($group)) {
    $query .= " AND s.group = ?";
    $params[] = $group;
    $types .= "s";
}
*/

if (!empty($gender)) {
    $query .= " AND s.gender = ?";
    $params[] = $gender;
    $types .= "s";
}

if (!empty($searchTerm)) {
    $query .= " AND (s.first_name LIKE ? OR s.last_name LIKE ? OR s.student_id LIKE ? OR CONCAT(s.first_name, ' ', s.last_name) LIKE ?)";
    $searchParam = "%$searchTerm%";
    $params[] = $searchParam;
    $params[] = $searchParam;
    $params[] = $searchParam;
    $params[] = $searchParam;
    $types .= "ssss";
}

$query .= " ORDER BY s.first_name, s.last_name";

// Execute query
$stmt = $conn->prepare($query);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();

// Prepare response
$students = [];
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $students[] = [
            'id' => $row['id'],
            'first_name' => $row['first_name'],
            'last_name' => $row['last_name'],
            'student_id' => $row['student_id'],
            'class_name' => $row['class_name'],
            'session_name' => $row['session_name'],
            'gender' => $row['gender'] ?? ''
        ];
    }
}

// Check if we have any students
if (empty($students)) {
    // Return empty response
    echo json_encode(['success' => true, 'students' => []]);
} else {
    // Return response with students
    echo json_encode(['success' => true, 'students' => $students]);
}
