<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Add a sample subject for testing
$sampleSubjects = [
    ['name' => 'বাংলা', 'code' => 'BAN101', 'category' => 'required'],
    ['name' => 'ইংরেজি', 'code' => 'ENG101', 'category' => 'required'],
    ['name' => 'গণিত', 'code' => 'MAT101', 'category' => 'required'],
    ['name' => 'পদার্থবিজ্ঞান', 'code' => 'PHY101', 'category' => 'optional'],
    ['name' => 'রসায়ন', 'code' => 'CHE101', 'category' => 'optional']
];

$added = 0;
$errors = [];

foreach ($sampleSubjects as $subject) {
    // Check if subject already exists
    $checkQuery = "SELECT COUNT(*) as count FROM subjects WHERE subject_code = '{$subject['code']}'";
    $result = $conn->query($checkQuery);
    $row = $result->fetch_assoc();
    
    if ($row['count'] == 0) {
        // Insert new subject
        $insertQuery = "INSERT INTO subjects (subject_name, subject_code, category, description, is_active, created_at)
                       VALUES ('{$subject['name']}', '{$subject['code']}', '{$subject['category']}', 'নমুনা বিষয় - টেস্টিং এর জন্য', 1, NOW())";
        
        if ($conn->query($insertQuery)) {
            $added++;
        } else {
            $errors[] = "Error adding {$subject['name']}: " . $conn->error;
        }
    }
}

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>নমুনা বিষয় যুক্ত করুন - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>নমুনা বিষয় যুক্ত করা হয়েছে</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($added > 0): ?>
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check-circle me-2"></i>সফল!</h6>
                                <p><?php echo $added; ?>টি নমুনা বিষয় সফলভাবে যুক্ত করা হয়েছে।</p>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (count($errors) > 0): ?>
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>সমস্যা:</h6>
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo $error; ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>পরবর্তী পদক্ষেপ:</h6>
                            <p class="mb-2">এখন আপনি বিষয় তালিকায় গিয়ে action button গুলো টেস্ট করতে পারেন:</p>
                            <ul class="mb-0">
                                <li>✏️ <strong>Edit Button</strong>: বিষয় সম্পাদনা করুন</li>
                                <li>🔄 <strong>Toggle Button</strong>: বিষয়ের অবস্থা পরিবর্তন করুন</li>
                                <li>👨‍🏫 <strong>Assignment Button</strong>: শিক্ষক বরাদ্দ করুন</li>
                                <li>🗑️ <strong>Delete Button</strong>: বিষয় মুছে ফেলুন</li>
                            </ul>
                        </div>
                        
                        <div class="text-center">
                            <a href="subjects.php" class="btn btn-primary">
                                <i class="fas fa-list me-2"></i>বিষয় তালিকায় যান
                            </a>
                            <a href="dashboard.php" class="btn btn-secondary ms-2">
                                <i class="fas fa-home me-2"></i>ড্যাশবোর্ডে ফিরুন
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Current Subjects List -->
                <div class="card mt-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>বর্তমান বিষয় তালিকা</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        $allSubjectsQuery = "SELECT * FROM subjects ORDER BY subject_name";
                        $allSubjects = $conn->query($allSubjectsQuery);
                        ?>
                        
                        <?php if ($allSubjects && $allSubjects->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>বিষয় কোড</th>
                                            <th>বিষয়ের নাম</th>
                                            <th>ধরন</th>
                                            <th>অবস্থা</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($subject = $allSubjects->fetch_assoc()): ?>
                                            <tr>
                                                <td><strong><?php echo htmlspecialchars($subject['subject_code']); ?></strong></td>
                                                <td><?php echo htmlspecialchars($subject['subject_name']); ?></td>
                                                <td>
                                                    <?php
                                                    $category = $subject['category'] ?? 'optional';
                                                    switch($category) {
                                                        case 'required':
                                                            echo '<span class="badge bg-success">আবশ্যিক</span>';
                                                            break;
                                                        case 'fourth':
                                                            echo '<span class="badge bg-info">চতুর্থ বিষয়</span>';
                                                            break;
                                                        default:
                                                            echo '<span class="badge bg-warning">ঐচ্ছিক</span>';
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php if ($subject['is_active']): ?>
                                                        <span class="badge bg-success">সক্রিয়</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">নিষ্ক্রিয়</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>কোন বিষয় পাওয়া যায়নি।
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
