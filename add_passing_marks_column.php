<?php
require_once 'includes/dbh.inc.php';

echo "<h2>Adding passing_marks column to subject_marks table</h2>";

// First check if the column already exists
$checkColumnQuery = "SHOW COLUMNS FROM subject_marks LIKE 'passing_marks'";
$columnExists = $conn->query($checkColumnQuery);

if ($columnExists && $columnExists->num_rows > 0) {
    echo "<p>The 'passing_marks' column already exists in the subject_marks table.</p>";
} else {
    // Add the column
    $addColumnQuery = "ALTER TABLE subject_marks ADD COLUMN passing_marks INT(11) NOT NULL DEFAULT 0 AFTER marks_value";
    
    if ($conn->query($addColumnQuery)) {
        echo "<p style='color: green;'>Successfully added 'passing_marks' column to the subject_marks table.</p>";
    } else {
        echo "<p style='color: red;'>Error adding column: " . $conn->error . "</p>";
    }
}

// Display the updated table structure
echo "<h3>Updated Table Structure</h3>";
$result = $conn->query('DESCRIBE subject_marks');

if ($result) {
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "Error checking table structure: " . $conn->error;
}
?> 