<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if the announcements table exists
$tableExistsQuery = "SELECT COUNT(*) as count FROM information_schema.tables
                    WHERE table_schema = DATABASE()
                    AND table_name = 'announcements'";
$tableExistsResult = $conn->query($tableExistsQuery);
$tableExists = ($tableExistsResult && $tableExistsResult->fetch_assoc()['count'] > 0);

// Process form submission for adding new announcement
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] == 'add') {
        $title = $conn->real_escape_string($_POST['title']);
        $content = $conn->real_escape_string($_POST['content']);
        $target_group = $conn->real_escape_string($_POST['target_group']);
        $status = $conn->real_escape_string($_POST['status']);
        $created_by = $_SESSION['userId'];

        $insertQuery = "INSERT INTO announcements (title, content, target_group, status, created_by)
                        VALUES (?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($insertQuery);
        $stmt->bind_param("ssssi", $title, $content, $target_group, $status, $created_by);

        if ($stmt->execute()) {
            $success_message = "ঘোষণা সফলভাবে যোগ করা হয়েছে।";
        } else {
            $error_message = "ঘোষণা যোগ করতে সমস্যা হয়েছে: " . $conn->error;
        }
    }
    // Process form submission for editing announcement
    else if (isset($_POST['action']) && $_POST['action'] == 'edit') {
        $id = intval($_POST['id']);
        $title = $conn->real_escape_string($_POST['title']);
        $content = $conn->real_escape_string($_POST['content']);
        $target_group = $conn->real_escape_string($_POST['target_group']);
        $status = $conn->real_escape_string($_POST['status']);

        $updateQuery = "UPDATE announcements SET
                        title = ?,
                        content = ?,
                        target_group = ?,
                        status = ?
                        WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("ssssi", $title, $content, $target_group, $status, $id);

        if ($stmt->execute()) {
            $success_message = "ঘোষণা সফলভাবে আপডেট করা হয়েছে।";
        } else {
            $error_message = "ঘোষণা আপডেট করতে সমস্যা হয়েছে: " . $conn->error;
        }
    }
    // Process form submission for deleting announcement
    else if (isset($_POST['action']) && $_POST['action'] == 'delete') {
        $id = intval($_POST['id']);

        $deleteQuery = "DELETE FROM announcements WHERE id = ?";
        $stmt = $conn->prepare($deleteQuery);
        $stmt->bind_param("i", $id);

        if ($stmt->execute()) {
            $success_message = "ঘোষণা সফলভাবে মুছে ফেলা হয়েছে।";
        } else {
            $error_message = "ঘোষণা মুছতে সমস্যা হয়েছে: " . $conn->error;
        }
    }
}

// Get announcement by id for editing
$edit_id = isset($_GET['edit']) ? intval($_GET['edit']) : 0;
$announcement_to_edit = null;

if ($edit_id > 0 && $tableExists) {
    $editQuery = "SELECT * FROM announcements WHERE id = ?";
    $stmt = $conn->prepare($editQuery);
    $stmt->bind_param("i", $edit_id);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result->num_rows > 0) {
        $announcement_to_edit = $result->fetch_assoc();
    }
}

// Get all announcements
$result = null;
if ($tableExists) {
    try {
        $query = "SELECT * FROM announcements ORDER BY created_at DESC";
        $result = $conn->query($query);
    } catch (Exception $e) {
        $error_message = "ঘোষণা লোড করতে সমস্যা হয়েছে: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>ঘোষণা ব্যবস্থাপনা - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <?php include('includes/header.php'); ?>

    <div class="container-fluid">
        <div class="row">
            <?php include('includes/sidebar.php'); ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">ঘোষণা ব্যবস্থাপনা</h1>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAnnouncementModal">
                        <i class="fas fa-plus me-1"></i> নতুন ঘোষণা যোগ করুন
                    </button>
                </div>

                <!-- Quick Access Menu Bar -->
                <div class="quick-access-bar bg-white shadow-sm mb-4 rounded">
                    <div class="d-flex align-items-center px-3 py-2 border-bottom">
                        <h5 class="mb-0"><i class="fas fa-bolt text-warning me-2"></i>দ্রুত অ্যাক্সেস</h5>
                    </div>
                    <div class="p-2">
                        <div class="d-flex flex-wrap justify-content-center justify-content-md-start">
                            <a class="btn btn-sm btn-outline-primary m-1 d-flex align-items-center" href="#" data-bs-toggle="modal" data-bs-target="#addAnnouncementModal">
                                <i class="fas fa-plus me-1"></i> ঘোষণা যোগ করুন
                            </a>
                            <a class="btn btn-sm btn-outline-success m-1 d-flex align-items-center" href="notices.php">
                                <i class="fas fa-bullhorn me-1"></i> নোটিশ ম্যানেজ
                            </a>
                            <a class="btn btn-sm btn-outline-info m-1 d-flex align-items-center" href="fee_sms.php">
                                <i class="fas fa-comment-dollar me-1"></i> ফি এসএমএস
                            </a>
                            <a class="btn btn-sm btn-outline-warning m-1 d-flex align-items-center" href="student_shortcodes.php">
                                <i class="fas fa-hashtag me-1"></i> শিক্ষার্থী সর্টকোড
                            </a>
                            <a class="btn btn-sm btn-outline-danger m-1 d-flex align-items-center" href="generate_id_cards.php">
                                <i class="fas fa-id-card me-1"></i> আইডি কার্ড
                            </a>
                            <a class="btn btn-sm btn-outline-secondary m-1 d-flex align-items-center" href="dashboard.php">
                                <i class="fas fa-tachometer-alt me-1"></i> ড্যাশবোর্ড
                            </a>
                        </div>
                    </div>
                </div>

                <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if (!$tableExists): ?>
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle me-2 fs-4"></i>
                        <div>
                            <strong>টেবিল অনুপস্থিত!</strong> ঘোষণা টেবিল এখনো তৈরি করা হয়নি।
                            <div class="mt-2">
                                <a href="create_announcements_table.php" class="btn btn-sm btn-warning">
                                    <i class="fas fa-database me-1"></i> টেবিল তৈরি করুন
                                </a>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <!-- Announcements List -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-bullhorn me-2"></i>সকল ঘোষণা</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($result && $result->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th width="5%">#</th>
                                            <th width="20%">শিরোনাম</th>
                                            <th width="30%">বিষয়বস্তু</th>
                                            <th width="15%">লক্ষ্য গ্রুপ</th>
                                            <th width="10%">স্ট্যাটাস</th>
                                            <th width="10%">তারিখ</th>
                                            <th width="10%">মেয়াদ শেষ</th>
                                            <th width="10%">কার্যক্রম</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $i = 1;
                                        while ($announcement = $result->fetch_assoc()):
                                        ?>
                                        <tr>
                                            <td><?php echo $i++; ?></td>
                                            <td><?php echo htmlspecialchars($announcement['title']); ?></td>
                                            <td><?php echo mb_substr(htmlspecialchars($announcement['content']), 0, 100) . '...'; ?></td>
                                            <td>
                                                <?php
                                                $targetGroups = [
                                                    'all' => 'সকল',
                                                    'students' => 'শিক্ষার্থী',
                                                    'teachers' => 'শিক্ষক',
                                                    'staff' => 'কর্মচারী',
                                                    'admin' => 'অ্যাডমিন'
                                                ];
                                                $targetGroup = isset($announcement['target_group']) ? $announcement['target_group'] : 'all';
                                                echo $targetGroups[$targetGroup] ?? $targetGroup;
                                                ?>
                                            </td>
                                            <td>
                                                <span class="badge <?php echo (isset($announcement['status']) && $announcement['status'] == 'active') ? 'bg-success' : 'bg-danger'; ?>">
                                                    <?php echo (isset($announcement['status']) && $announcement['status'] == 'active') ? 'সক্রিয়' : 'নিষ্ক্রিয়'; ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('d/m/Y', strtotime($announcement['created_at'])); ?></td>
                                            <td>
                                                <?php if (isset($announcement['expire_date']) && !empty($announcement['expire_date'])): ?>
                                                    <?php echo date('d/m/Y', strtotime($announcement['expire_date'])); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">নির্ধারিত নয়</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <a href="?edit=<?php echo $announcement['id']; ?>" class="btn btn-sm btn-warning">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button class="btn btn-sm btn-danger delete-announcement" data-id="<?php echo $announcement['id']; ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-bullhorn fa-3x mb-3 text-muted"></i>
                                <p class="lead">কোন ঘোষণা নেই।</p>
                                <button class="btn btn-primary mt-3" data-bs-toggle="modal" data-bs-target="#addAnnouncementModal">
                                    <i class="fas fa-plus me-1"></i> নতুন ঘোষণা যোগ করুন
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add Announcement Modal -->
    <div class="modal fade" id="addAnnouncementModal" tabindex="-1" aria-labelledby="addAnnouncementModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addAnnouncementModalLabel">নতুন ঘোষণা যোগ করুন</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form method="post" action="">
                        <input type="hidden" name="action" value="add">
                        <div class="mb-3">
                            <label for="title" class="form-label">শিরোনাম</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        <div class="mb-3">
                            <label for="content" class="form-label">বিষয়বস্তু</label>
                            <textarea class="form-control" id="content" name="content" rows="5" required></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="target_group" class="form-label">লক্ষ্য গ্রুপ</label>
                                    <select class="form-select" id="target_group" name="target_group" required>
                                        <option value="all">সকল</option>
                                        <option value="students">শিক্ষার্থী</option>
                                        <option value="teachers">শিক্ষক</option>
                                        <option value="staff">কর্মচারী</option>
                                        <option value="admin">অ্যাডমিন</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">স্ট্যাটাস</label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="active">সক্রিয়</option>
                                        <option value="inactive">নিষ্ক্রিয়</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">সংরক্ষণ করুন</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Announcement Modal -->
    <?php if ($announcement_to_edit): ?>
    <div class="modal fade" id="editAnnouncementModal" tabindex="-1" aria-labelledby="editAnnouncementModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editAnnouncementModalLabel">ঘোষণা সম্পাদনা</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form method="post" action="">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="id" value="<?php echo $announcement_to_edit['id']; ?>">
                        <div class="mb-3">
                            <label for="edit_title" class="form-label">শিরোনাম</label>
                            <input type="text" class="form-control" id="edit_title" name="title" value="<?php echo htmlspecialchars($announcement_to_edit['title']); ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_content" class="form-label">বিষয়বস্তু</label>
                            <textarea class="form-control" id="edit_content" name="content" rows="5" required><?php echo htmlspecialchars($announcement_to_edit['content']); ?></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_target_group" class="form-label">লক্ষ্য গ্রুপ</label>
                                    <select class="form-select" id="edit_target_group" name="target_group" required>
                                        <option value="all" <?php echo ($announcement_to_edit['target_group'] == 'all') ? 'selected' : ''; ?>>সকল</option>
                                        <option value="students" <?php echo ($announcement_to_edit['target_group'] == 'students') ? 'selected' : ''; ?>>শিক্ষার্থী</option>
                                        <option value="teachers" <?php echo ($announcement_to_edit['target_group'] == 'teachers') ? 'selected' : ''; ?>>শিক্ষক</option>
                                        <option value="staff" <?php echo ($announcement_to_edit['target_group'] == 'staff') ? 'selected' : ''; ?>>কর্মচারী</option>
                                        <option value="admin" <?php echo ($announcement_to_edit['target_group'] == 'admin') ? 'selected' : ''; ?>>অ্যাডমিন</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_status" class="form-label">স্ট্যাটাস</label>
                                    <select class="form-select" id="edit_status" name="status" required>
                                        <option value="active" <?php echo ($announcement_to_edit['status'] == 'active') ? 'selected' : ''; ?>>সক্রিয়</option>
                                        <option value="inactive" <?php echo ($announcement_to_edit['status'] == 'inactive') ? 'selected' : ''; ?>>নিষ্ক্রিয়</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">আপডেট করুন</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Delete Announcement Form (Hidden) -->
    <form id="deleteForm" method="post" action="" style="display: none;">
        <input type="hidden" name="action" value="delete">
        <input type="hidden" name="id" id="delete_announcement_id">
    </form>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Auto show edit modal if edit ID is present
            <?php if ($announcement_to_edit): ?>
            var editModal = new bootstrap.Modal(document.getElementById('editAnnouncementModal'));
            editModal.show();
            <?php endif; ?>

            // Delete announcement
            $('.delete-announcement').click(function() {
                if (confirm('আপনি কি নিশ্চিত এই ঘোষণাটি মুছে ফেলতে চান?')) {
                    var id = $(this).data('id');
                    $('#delete_announcement_id').val(id);
                    $('#deleteForm').submit();
                }
            });

            // Auto-hide alerts after 3 seconds
            setTimeout(function() {
                $('.alert-success, .alert-danger').fadeOut('slow');
            }, 3000);
        });
    </script>
</body>
</html>