<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'includes/dbh.inc.php';

echo "<h1>Checking Certificates Table Structure</h1>";
echo "<pre>";

// Ensure connection is active
$conn = ensure_connection();

if ($conn) {
    echo "Database connection successful.\n\n";

    // Check if certificates table exists
    $result = $conn->query("SHOW TABLES LIKE 'certificates'");

    if ($result && $result->num_rows > 0) {
        echo "The 'certificates' table exists.\n\n";

        // Get table structure
        $result = $conn->query("DESCRIBE certificates");

        if ($result) {
            echo "Structure of 'certificates' table:\n";
            echo "-----------------------------\n";

            while ($row = $result->fetch_assoc()) {
                echo "{$row['Field']} | {$row['Type']} | {$row['Key']} | {$row['Default']}\n";
            }

            echo "\n";

            // Get sample data
            $result = $conn->query("SELECT * FROM certificates LIMIT 5");

            if ($result && $result->num_rows > 0) {
                echo "Sample data from 'certificates' table:\n";
                echo "-----------------------------\n";

                $fields = array();
                $data = array();

                // Get field names
                $fields_info = $result->fetch_fields();
                foreach ($fields_info as $field) {
                    $fields[] = $field->name;
                }

                echo implode(" | ", $fields) . "\n";
                echo str_repeat("-", 100) . "\n";

                // Get data
                while ($row = $result->fetch_assoc()) {
                    echo implode(" | ", $row) . "\n";
                }
            } else {
                echo "No data found in the 'certificates' table or error executing query.\n";
            }

            // Count certificates by type
            $result = $conn->query("SELECT certificate_type, COUNT(*) as count FROM certificates GROUP BY certificate_type");

            if ($result && $result->num_rows > 0) {
                echo "\nCertificate counts by type:\n";
                echo "-----------------------------\n";

                while ($row = $result->fetch_assoc()) {
                    echo "{$row['certificate_type']}: {$row['count']}\n";
                }
            }
        } else {
            echo "Error getting table structure: " . $conn->error . "\n";
        }
    } else {
        echo "The 'certificates' table does not exist!\n";
    }

    $conn->close();
    echo "\nDatabase connection closed.";
} else {
    echo "Error: Could not establish database connection.";
}

?>
