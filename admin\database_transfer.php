<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Create export directory if it doesn't exist
$exportDir = '../exports';
if (!file_exists($exportDir)) {
    mkdir($exportDir, 0777, true);
}

// Function to export database tables
function exportDatabaseTables($conn, $exportDir, $selectedTables = []) {
    global $dbname;
    
    // Get all tables if none selected
    if (empty($selectedTables)) {
        $tables = array();
        $result = $conn->query("SHOW TABLES");
        
        while ($row = $result->fetch_row()) {
            $tables[] = $row[0];
        }
    } else {
        $tables = $selectedTables;
    }
    
    // Create a ZIP archive
    $zipFile = $exportDir . '/' . $dbname . '_export_' . date("Y-m-d_H-i-s") . '.zip';
    $zip = new ZipArchive();
    
    if ($zip->open($zipFile, ZipArchive::CREATE) !== TRUE) {
        return false;
    }
    
    // Export each table to a separate SQL file
    foreach ($tables as $table) {
        try {
            // Check if table exists
            $tableExistsQuery = "SHOW TABLES LIKE '$table'";
            $tableExists = $conn->query($tableExistsQuery);
            
            if ($tableExists && $tableExists->num_rows > 0) {
                $tableFile = $exportDir . '/' . $table . '.sql';
                $output = "-- Table structure and data for $table\n\n";
                
                // Get table structure
                $result = $conn->query("SHOW CREATE TABLE `$table`");
                if ($result && $row = $result->fetch_row()) {
                    $output .= $row[1] . ";\n\n";
                    
                    // Get table data
                    $dataResult = $conn->query("SELECT * FROM `$table`");
                    if ($dataResult) {
                        $numFields = $dataResult->field_count;
                        
                        while ($row = $dataResult->fetch_row()) {
                            $output .= "INSERT INTO `$table` VALUES(";
                            
                            for ($i = 0; $i < $numFields; $i++) {
                                if (isset($row[$i])) {
                                    // Escape special characters
                                    $row[$i] = str_replace("\n", "\\n", addslashes($row[$i]));
                                    $output .= '"' . $row[$i] . '"';
                                } else {
                                    $output .= 'NULL';
                                }
                                
                                if ($i < ($numFields - 1)) {
                                    $output .= ',';
                                }
                            }
                            
                            $output .= ");\n";
                        }
                    }
                    
                    // Save table to file
                    file_put_contents($tableFile, $output);
                    
                    // Add file to ZIP archive
                    $zip->addFile($tableFile, $table . '.sql');
                }
            }
        } catch (Exception $e) {
            // Skip this table and continue with others
            continue;
        }
    }
    
    // Add a README file with instructions
    $readmeContent = "# Database Export Instructions\n\n";
    $readmeContent .= "This ZIP file contains SQL files for each table in the $dbname database.\n\n";
    $readmeContent .= "## Import Instructions:\n\n";
    $readmeContent .= "1. Create a new database named '$dbname' on your target server\n";
    $readmeContent .= "2. Import each SQL file using phpMyAdmin or MySQL command line\n";
    $readmeContent .= "3. Make sure to import tables in the correct order (tables with foreign keys should be imported after their referenced tables)\n\n";
    $readmeContent .= "## Using phpMyAdmin:\n\n";
    $readmeContent .= "1. Open phpMyAdmin and select your database\n";
    $readmeContent .= "2. Click on the 'Import' tab\n";
    $readmeContent .= "3. Choose the SQL file to import\n";
    $readmeContent .= "4. Click 'Go' to import the file\n";
    $readmeContent .= "5. Repeat for each table file\n\n";
    $readmeContent .= "## Using MySQL Command Line:\n\n";
    $readmeContent .= "```\n";
    $readmeContent .= "mysql -u username -p $dbname < table_name.sql\n";
    $readmeContent .= "```\n\n";
    $readmeContent .= "Export created on: " . date("Y-m-d H:i:s") . "\n";
    
    $readmeFile = $exportDir . '/README.md';
    file_put_contents($readmeFile, $readmeContent);
    $zip->addFile($readmeFile, 'README.md');
    
    // Close the ZIP archive
    $zip->close();
    
    // Clean up temporary files
    foreach ($tables as $table) {
        $tableFile = $exportDir . '/' . $table . '.sql';
        if (file_exists($tableFile)) {
            unlink($tableFile);
        }
    }
    unlink($readmeFile);
    
    return basename($zipFile);
}

// Function to get list of export files
function getExportFiles($exportDir) {
    $files = array();
    
    if (is_dir($exportDir)) {
        $dirHandle = opendir($exportDir);
        
        while (($file = readdir($dirHandle)) !== false) {
            if (strpos($file, '_export_') !== false && pathinfo($file, PATHINFO_EXTENSION) === 'zip') {
                $files[] = $file;
            }
        }
        
        closedir($dirHandle);
        rsort($files); // Sort by newest first
    }
    
    return $files;
}

// Handle export creation
if (isset($_POST['create_export'])) {
    $selectedTables = isset($_POST['tables']) ? $_POST['tables'] : [];
    $exportFile = exportDatabaseTables($conn, $exportDir, $selectedTables);
    
    if ($exportFile) {
        $successMessage = "ডাটাবেজ এক্সপোর্ট সফলভাবে তৈরি করা হয়েছে: $exportFile";
    } else {
        $errorMessage = "এক্সপোর্ট তৈরি করতে সমস্যা হয়েছে।";
    }
}

// Handle export download
if (isset($_GET['download']) && !empty($_GET['download'])) {
    $exportFile = $exportDir . '/' . $_GET['download'];
    
    if (file_exists($exportFile)) {
        header('Content-Description: File Transfer');
        header('Content-Type: application/zip');
        header('Content-Disposition: attachment; filename="' . basename($exportFile) . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        header('Content-Length: ' . filesize($exportFile));
        readfile($exportFile);
        exit;
    } else {
        $errorMessage = "এক্সপোর্ট ফাইল পাওয়া যায়নি।";
    }
}

// Handle export deletion
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $exportFile = $exportDir . '/' . $_GET['delete'];
    
    if (file_exists($exportFile) && unlink($exportFile)) {
        $successMessage = "এক্সপোর্ট ফাইল সফলভাবে মুছে ফেলা হয়েছে।";
    } else {
        $errorMessage = "এক্সপোর্ট ফাইল মুছতে সমস্যা হয়েছে।";
    }
}

// Get list of tables
$tables = array();
$result = $conn->query("SHOW TABLES");

while ($row = $result->fetch_row()) {
    $tables[] = $row[0];
}

// Get list of export files
$exportFiles = getExportFiles($exportDir);

// Handle import
$importMessage = '';
if (isset($_POST['import']) && isset($_FILES['import_file'])) {
    $uploadedFile = $_FILES['import_file'];
    
    if ($uploadedFile['error'] === UPLOAD_ERR_OK) {
        $tempFile = $uploadedFile['tmp_name'];
        $fileName = $uploadedFile['name'];
        
        // Check if it's a ZIP file
        if (pathinfo($fileName, PATHINFO_EXTENSION) === 'zip') {
            $extractDir = $exportDir . '/temp_' . time();
            mkdir($extractDir, 0777, true);
            
            $zip = new ZipArchive();
            if ($zip->open($tempFile) === TRUE) {
                $zip->extractTo($extractDir);
                $zip->close();
                
                // Import each SQL file
                $importedTables = 0;
                $failedTables = 0;
                
                $sqlFiles = glob($extractDir . '/*.sql');
                foreach ($sqlFiles as $sqlFile) {
                    $sql = file_get_contents($sqlFile);
                    
                    try {
                        if ($conn->multi_query($sql)) {
                            do {
                                // Free result
                                if ($result = $conn->store_result()) {
                                    $result->free();
                                }
                            } while ($conn->more_results() && $conn->next_result());
                            
                            $importedTables++;
                        } else {
                            $failedTables++;
                        }
                    } catch (Exception $e) {
                        $failedTables++;
                    }
                }
                
                // Clean up
                array_map('unlink', glob($extractDir . '/*'));
                rmdir($extractDir);
                
                if ($importedTables > 0) {
                    $importMessage = "<div class='alert alert-success'>$importedTables টেবিল সফলভাবে ইম্পোর্ট করা হয়েছে।";
                    if ($failedTables > 0) {
                        $importMessage .= " $failedTables টেবিল ইম্পোর্ট করতে ব্যর্থ হয়েছে।";
                    }
                    $importMessage .= "</div>";
                } else {
                    $importMessage = "<div class='alert alert-danger'>কোন টেবিল ইম্পোর্ট করা যায়নি।</div>";
                }
            } else {
                $importMessage = "<div class='alert alert-danger'>ZIP ফাইল খুলতে সমস্যা হয়েছে।</div>";
            }
        } else if (pathinfo($fileName, PATHINFO_EXTENSION) === 'sql') {
            // Direct SQL file import
            $sql = file_get_contents($tempFile);
            
            try {
                if ($conn->multi_query($sql)) {
                    do {
                        // Free result
                        if ($result = $conn->store_result()) {
                            $result->free();
                        }
                    } while ($conn->more_results() && $conn->next_result());
                    
                    $importMessage = "<div class='alert alert-success'>SQL ফাইল সফলভাবে ইম্পোর্ট করা হয়েছে।</div>";
                } else {
                    $importMessage = "<div class='alert alert-danger'>SQL ফাইল ইম্পোর্ট করতে ব্যর্থ হয়েছে।</div>";
                }
            } catch (Exception $e) {
                $importMessage = "<div class='alert alert-danger'>SQL ফাইল ইম্পোর্ট করতে ব্যর্থ হয়েছে: " . $e->getMessage() . "</div>";
            }
        } else {
            $importMessage = "<div class='alert alert-danger'>অসমর্থিত ফাইল ফরম্যাট। শুধুমাত্র ZIP বা SQL ফাইল আপলোড করুন।</div>";
        }
    } else {
        $importMessage = "<div class='alert alert-danger'>ফাইল আপলোড করতে সমস্যা হয়েছে।</div>";
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>ডাটাবেজ ট্রান্সফার - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include_once '../includes/admin_sidebar.php'; ?>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>ডাটাবেজ ট্রান্সফার</h2>
                        <p class="text-muted">ডাটাবেজ এক্সপোর্ট এবং ইম্পোর্ট করুন</p>
                    </div>
                </div>

                <?php if (isset($errorMessage)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $errorMessage; ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($successMessage)): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo $successMessage; ?>
                    </div>
                <?php endif; ?>
                
                <?php echo $importMessage; ?>

                <!-- Nav Tabs -->
                <ul class="nav nav-tabs mb-4" id="myTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="export-tab" data-bs-toggle="tab" data-bs-target="#export" type="button" role="tab" aria-controls="export" aria-selected="true">
                            <i class="fas fa-file-export me-2"></i>এক্সপোর্ট
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="import-tab" data-bs-toggle="tab" data-bs-target="#import" type="button" role="tab" aria-controls="import" aria-selected="false">
                            <i class="fas fa-file-import me-2"></i>ইম্পোর্ট
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="myTabContent">
                    <!-- Export Tab -->
                    <div class="tab-pane fade show active" id="export" role="tabpanel" aria-labelledby="export-tab">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">ডাটাবেজ এক্সপোর্ট করুন</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="database_transfer.php">
                                    <div class="mb-3">
                                        <label class="form-label">টেবিল নির্বাচন করুন (কোনটি নির্বাচন না করলে সব টেবিল এক্সপোর্ট হবে)</label>
                                        <div class="row">
                                            <?php foreach ($tables as $table): ?>
                                                <div class="col-md-4 mb-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="tables[]" value="<?php echo $table; ?>" id="table_<?php echo $table; ?>">
                                                        <label class="form-check-label" for="table_<?php echo $table; ?>">
                                                            <?php echo $table; ?>
                                                        </label>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <button type="button" class="btn btn-sm btn-secondary" id="selectAll">সব নির্বাচন করুন</button>
                                        <button type="button" class="btn btn-sm btn-secondary" id="deselectAll">সব বাতিল করুন</button>
                                    </div>
                                    
                                    <button type="submit" name="create_export" class="btn btn-primary">
                                        <i class="fas fa-file-export me-2"></i>এক্সপোর্ট তৈরি করুন
                                    </button>
                                </form>
                                
                                <hr>
                                
                                <h5>এক্সপোর্ট ফাইল তালিকা</h5>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>ফাইল নাম</th>
                                                <th>তারিখ</th>
                                                <th>সাইজ</th>
                                                <th>অ্যাকশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (empty($exportFiles)): ?>
                                                <tr>
                                                    <td colspan="4" class="text-center">কোন এক্সপোর্ট ফাইল পাওয়া যায়নি</td>
                                                </tr>
                                            <?php else: ?>
                                                <?php foreach ($exportFiles as $file): ?>
                                                    <tr>
                                                        <td><?php echo $file; ?></td>
                                                        <td><?php echo date("F j, Y, g:i a", filemtime($exportDir . '/' . $file)); ?></td>
                                                        <td><?php echo round(filesize($exportDir . '/' . $file) / 1024, 2); ?> KB</td>
                                                        <td>
                                                            <a href="database_transfer.php?download=<?php echo $file; ?>" class="btn btn-sm btn-primary">
                                                                <i class="fas fa-download"></i> ডাউনলোড
                                                            </a>
                                                            <a href="database_transfer.php?delete=<?php echo $file; ?>" class="btn btn-sm btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই এক্সপোর্ট ফাইলটি মুছতে চান?');">
                                                                <i class="fas fa-trash"></i> মুছুন
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Import Tab -->
                    <div class="tab-pane fade" id="import" role="tabpanel" aria-labelledby="import-tab">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">ডাটাবেজ ইম্পোর্ট করুন</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>সতর্কতা:</strong> ডাটাবেজ ইম্পোর্ট করলে বিদ্যমান ডাটা ওভাররাইট হতে পারে। ইম্পোর্ট করার আগে ব্যাকআপ নিয়ে রাখুন।
                                </div>
                                
                                <form method="POST" action="database_transfer.php" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="import_file" class="form-label">ইম্পোর্ট ফাইল নির্বাচন করুন (ZIP বা SQL)</label>
                                        <input type="file" class="form-control" id="import_file" name="import_file" accept=".zip,.sql" required>
                                        <div class="form-text">ZIP ফাইল (.zip) বা SQL ফাইল (.sql) আপলোড করুন</div>
                                    </div>
                                    
                                    <button type="submit" name="import" class="btn btn-primary">
                                        <i class="fas fa-file-import me-2"></i>ইম্পোর্ট করুন
                                    </button>
                                </form>
                                
                                <hr>
                                
                                <div class="card">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="card-title mb-0">ইম্পোর্ট নির্দেশাবলী</h5>
                                    </div>
                                    <div class="card-body">
                                        <h6>ZIP ফাইল ইম্পোর্ট করা:</h6>
                                        <ol>
                                            <li>এক্সপোর্ট ট্যাব থেকে তৈরি করা ZIP ফাইল আপলোড করুন</li>
                                            <li>সিস্টেম স্বয়ংক্রিয়ভাবে ZIP ফাইল এক্সট্র্যাক্ট করবে এবং প্রতিটি টেবিল ইম্পোর্ট করবে</li>
                                        </ol>
                                        
                                        <h6>SQL ফাইল ইম্পোর্ট করা:</h6>
                                        <ol>
                                            <li>পোর্টেবল ব্যাকআপ পেজ থেকে তৈরি করা SQL ফাইল আপলোড করুন</li>
                                            <li>সিস্টেম স্বয়ংক্রিয়ভাবে SQL ফাইল ইম্পোর্ট করবে</li>
                                        </ol>
                                        
                                        <div class="alert alert-info mt-3">
                                            <i class="fas fa-info-circle me-2"></i>
                                            <strong>টিপস:</strong> বড় ডাটাবেজের জন্য, phpMyAdmin ব্যবহার করে ইম্পোর্ট করা আরও ভাল হতে পারে।
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4">
                    <a href="database_backup.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>ডাটাবেজ ব্যাকআপ পেজে ফিরে যান
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Select/Deselect all tables
            document.getElementById('selectAll').addEventListener('click', function() {
                var checkboxes = document.querySelectorAll('input[name="tables[]"]');
                checkboxes.forEach(function(checkbox) {
                    checkbox.checked = true;
                });
            });
            
            document.getElementById('deselectAll').addEventListener('click', function() {
                var checkboxes = document.querySelectorAll('input[name="tables[]"]');
                checkboxes.forEach(function(checkbox) {
                    checkbox.checked = false;
                });
            });
        });
    </script>
</body>
</html>
