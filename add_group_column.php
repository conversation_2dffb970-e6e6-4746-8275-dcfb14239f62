<?php
// Connect to the database
$conn = new mysqli('localhost', 'root', '', 'zfaw');

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Check if group column exists
$result = $conn->query("SHOW COLUMNS FROM students LIKE 'group_name'");
if ($result->num_rows == 0) {
    // Add group column
    $sql = "ALTER TABLE students ADD COLUMN group_name VARCHAR(50) NULL AFTER class_id";
    if ($conn->query($sql) === TRUE) {
        echo "Group column added successfully to students table";
    } else {
        echo "Error adding group column: " . $conn->error;
    }
} else {
    echo "Group column already exists in students table";
}

$conn->close();
?>
