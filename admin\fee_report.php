<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Set default filter values
$fromDate = isset($_GET['from_date']) ? $_GET['from_date'] : date('Y-m-01'); // First day of current month
$toDate = isset($_GET['to_date']) ? $_GET['to_date'] : date('Y-m-t'); // Last day of current month
$classId = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;
$departmentId = isset($_GET['department_id']) ? intval($_GET['department_id']) : 0;
$sessionId = isset($_GET['session_id']) ? intval($_GET['session_id']) : 0;
$reportType = isset($_GET['report_type']) ? $_GET['report_type'] : 'collection';
$searchTerm = isset($_GET['search_term']) ? trim($_GET['search_term']) : '';
$searchType = isset($_GET['search_type']) ? $_GET['search_type'] : 'name';

// Get all classes for dropdown
$classesQuery = "SELECT * FROM classes ORDER BY class_name";
$classesResult = $conn->query($classesQuery);

// Get all departments for dropdown
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departmentsResult = $conn->query($departmentsQuery);

// Get all sessions for dropdown
$sessionsQuery = "SELECT * FROM sessions ORDER BY session_name DESC";
$sessionsResult = $conn->query($sessionsQuery);

// Prepare report data based on report type
$reportData = [];
$totalAmount = 0;
$totalPaid = 0;
$totalDue = 0;

if ($reportType === 'collection') {
    // Fee collection report
    $query = "SELECT f.id, f.student_id, f.fee_type,
              COALESCE(f.amount, 0) as amount, COALESCE(f.paid, 0) as paid, f.due_date, f.payment_status, f.created_at,
              s.first_name, s.last_name, s.student_id as roll, c.class_name, d.department_name, ss.session_name
              FROM fees f
              JOIN students s ON f.student_id = s.id
              LEFT JOIN classes c ON s.class_id = c.id
              LEFT JOIN departments d ON s.department_id = d.id
              LEFT JOIN sessions ss ON s.session_id = ss.id
              WHERE f.due_date BETWEEN ? AND ?";

    $params = [$fromDate, $toDate];
    $types = "ss";

    if ($classId > 0) {
        $query .= " AND s.class_id = ?";
        $params[] = $classId;
        $types .= "i";
    }

    if ($departmentId > 0) {
        $query .= " AND s.department_id = ?";
        $params[] = $departmentId;
        $types .= "i";
    }

    if ($sessionId > 0) {
        $query .= " AND s.session_id = ?";
        $params[] = $sessionId;
        $types .= "i";
    }

    // Add search term filter
    if (!empty($searchTerm)) {
        if ($searchType === 'name') {
            $query .= " AND (s.first_name LIKE ? OR s.last_name LIKE ? OR CONCAT(s.first_name, ' ', s.last_name) LIKE ?)";
            $params[] = "%$searchTerm%";
            $params[] = "%$searchTerm%";
            $params[] = "%$searchTerm%";
            $types .= "sss";
        } else if ($searchType === 'roll') {
            $query .= " AND s.student_id LIKE ?";
            $params[] = "%$searchTerm%";
            $types .= "s";
        } else if ($searchType === 'id') {
            $query .= " AND s.id = ?";
            $params[] = $searchTerm;
            $types .= "i";
        }
    }

    $query .= " ORDER BY f.due_date DESC";

    $stmt = $conn->prepare($query);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $reportData[] = $row;
            $totalAmount += ($row['amount'] ?? 0);
            $totalPaid += ($row['paid'] ?? 0);
            $totalDue += (($row['amount'] ?? 0) - ($row['paid'] ?? 0));
        }
    }
} elseif ($reportType === 'student') {
    // Student-wise fee report
    $query = "SELECT s.id, s.first_name, s.last_name, s.student_id as roll, c.class_name, d.department_name, ss.session_name,
              COALESCE(SUM(f.amount), 0) as total_amount, COALESCE(SUM(f.paid), 0) as total_paid
              FROM students s
              LEFT JOIN fees f ON s.id = f.student_id
              LEFT JOIN classes c ON s.class_id = c.id
              LEFT JOIN departments d ON s.department_id = d.id
              LEFT JOIN sessions ss ON s.session_id = ss.id
              WHERE (f.due_date BETWEEN ? AND ? OR f.due_date IS NULL)";

    $params = [$fromDate, $toDate];
    $types = "ss";

    if ($classId > 0) {
        $query .= " AND s.class_id = ?";
        $params[] = $classId;
        $types .= "i";
    }

    if ($departmentId > 0) {
        $query .= " AND s.department_id = ?";
        $params[] = $departmentId;
        $types .= "i";
    }

    if ($sessionId > 0) {
        $query .= " AND s.session_id = ?";
        $params[] = $sessionId;
        $types .= "i";
    }

    // Add search term filter
    if (!empty($searchTerm)) {
        if ($searchType === 'name') {
            $query .= " AND (s.first_name LIKE ? OR s.last_name LIKE ? OR CONCAT(s.first_name, ' ', s.last_name) LIKE ?)";
            $params[] = "%$searchTerm%";
            $params[] = "%$searchTerm%";
            $params[] = "%$searchTerm%";
            $types .= "sss";
        } else if ($searchType === 'roll') {
            $query .= " AND s.student_id LIKE ?";
            $params[] = "%$searchTerm%";
            $types .= "s";
        } else if ($searchType === 'id') {
            $query .= " AND s.id = ?";
            $params[] = $searchTerm;
            $types .= "i";
        }
    }

    $query .= " GROUP BY s.id ORDER BY s.first_name, s.last_name";

    $stmt = $conn->prepare($query);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $row['total_due'] = ($row['total_amount'] ?? 0) - ($row['total_paid'] ?? 0);
            $reportData[] = $row;
            $totalAmount += ($row['total_amount'] ?? 0);
            $totalPaid += ($row['total_paid'] ?? 0);
            $totalDue += ($row['total_due'] ?? 0);
        }
    }
} elseif ($reportType === 'class') {
    // Class-wise fee report
    $query = "SELECT c.id, c.class_name, COUNT(DISTINCT s.id) as student_count,
              COALESCE(SUM(f.amount), 0) as total_amount, COALESCE(SUM(f.paid), 0) as total_paid
              FROM classes c
              LEFT JOIN students s ON c.id = s.class_id
              LEFT JOIN fees f ON s.id = f.student_id
              LEFT JOIN sessions ss ON s.session_id = ss.id
              WHERE (f.due_date BETWEEN ? AND ? OR f.due_date IS NULL)";

    $params = [$fromDate, $toDate];
    $types = "ss";

    if ($departmentId > 0) {
        $query .= " AND s.department_id = ?";
        $params[] = $departmentId;
        $types .= "i";
    }

    if ($sessionId > 0) {
        $query .= " AND s.session_id = ?";
        $params[] = $sessionId;
        $types .= "i";
    }

    $query .= " GROUP BY c.id ORDER BY c.class_name";

    $stmt = $conn->prepare($query);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $row['total_due'] = ($row['total_amount'] ?? 0) - ($row['total_paid'] ?? 0);
            $reportData[] = $row;
            $totalAmount += ($row['total_amount'] ?? 0);
            $totalPaid += ($row['total_paid'] ?? 0);
            $totalDue += ($row['total_due'] ?? 0);
        }
    }
} elseif ($reportType === 'department') {
    // Department-wise fee report
    $query = "SELECT d.id, d.department_name, COUNT(DISTINCT s.id) as student_count,
              COALESCE(SUM(f.amount), 0) as total_amount, COALESCE(SUM(f.paid), 0) as total_paid
              FROM departments d
              LEFT JOIN students s ON d.id = s.department_id
              LEFT JOIN fees f ON s.id = f.student_id
              LEFT JOIN sessions ss ON s.session_id = ss.id
              WHERE (f.due_date BETWEEN ? AND ? OR f.due_date IS NULL)";

    $params = [$fromDate, $toDate];
    $types = "ss";

    if ($classId > 0) {
        $query .= " AND s.class_id = ?";
        $params[] = $classId;
        $types .= "i";
    }

    if ($sessionId > 0) {
        $query .= " AND s.session_id = ?";
        $params[] = $sessionId;
        $types .= "i";
    }

    $query .= " GROUP BY d.id ORDER BY d.department_name";

    $stmt = $conn->prepare($query);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $row['total_due'] = ($row['total_amount'] ?? 0) - ($row['total_paid'] ?? 0);
            $reportData[] = $row;
            $totalAmount += ($row['total_amount'] ?? 0);
            $totalPaid += ($row['total_paid'] ?? 0);
            $totalDue += ($row['total_due'] ?? 0);
        }
    }
}

// Include header
include_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Include sidebar -->
        <?php include_once 'includes/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-file-alt me-2"></i> ফি রিপোর্ট</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="fee_management.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i> ফি ম্যানেজমেন্ট
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="window.print()">
                            <i class="fas fa-print me-1"></i> প্রিন্ট
                        </button>
                    </div>
                </div>
            </div>

            <!-- Filter Form -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-filter me-2"></i> ফিল্টার</h5>
                </div>
                <div class="card-body">
                    <form method="get" action="" class="row g-3">
                        <!-- First Row -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="report_type" class="form-label">রিপোর্ট ধরন</label>
                                <select class="form-select" id="report_type" name="report_type">
                                    <option value="collection" <?= $reportType === 'collection' ? 'selected' : '' ?>>ফি সংগ্রহ</option>
                                    <option value="student" <?= $reportType === 'student' ? 'selected' : '' ?>>শিক্ষার্থী অনুযায়ী</option>
                                    <option value="class" <?= $reportType === 'class' ? 'selected' : '' ?>>শ্রেণী অনুযায়ী</option>
                                    <option value="department" <?= $reportType === 'department' ? 'selected' : '' ?>>বিভাগ অনুযায়ী</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="from_date" class="form-label">শুরুর তারিখ</label>
                                <input type="date" class="form-control" id="from_date" name="from_date" value="<?= $fromDate ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="to_date" class="form-label">শেষের তারিখ</label>
                                <input type="date" class="form-control" id="to_date" name="to_date" value="<?= $toDate ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="session_id" class="form-label">সেশন</label>
                                <select class="form-select" id="session_id" name="session_id">
                                    <option value="0">সকল সেশন</option>
                                    <?php if ($sessionsResult && $sessionsResult->num_rows > 0): ?>
                                        <?php while ($session = $sessionsResult->fetch_assoc()): ?>
                                            <option value="<?= $session['id'] ?>" <?= $sessionId === (int)$session['id'] ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($session['session_name']) ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>

                        <!-- Second Row -->
                        <div class="row">
                            <div class="col-md-3">
                                <label for="class_id" class="form-label">শ্রেণী</label>
                                <select class="form-select" id="class_id" name="class_id">
                                    <option value="0">সকল শ্রেণী</option>
                                    <?php if ($classesResult && $classesResult->num_rows > 0): ?>
                                        <?php while ($class = $classesResult->fetch_assoc()): ?>
                                            <option value="<?= $class['id'] ?>" <?= $classId === (int)$class['id'] ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($class['class_name']) ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="department_id" class="form-label">বিভাগ</label>
                                <select class="form-select" id="department_id" name="department_id">
                                    <option value="0">সকল বিভাগ</option>
                                    <?php if ($departmentsResult && $departmentsResult->num_rows > 0): ?>
                                        <?php while ($department = $departmentsResult->fetch_assoc()): ?>
                                            <option value="<?= $department['id'] ?>" <?= $departmentId === (int)$department['id'] ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($department['department_name']) ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                            <!-- Search Fields -->
                            <div class="col-md-4">
                                <div class="row">
                                    <div class="col-md-5">
                                        <label for="search_type" class="form-label">সার্চ টাইপ</label>
                                        <select class="form-select" id="search_type" name="search_type">
                                            <option value="name" <?= $searchType === 'name' ? 'selected' : '' ?>>নাম</option>
                                            <option value="roll" <?= $searchType === 'roll' ? 'selected' : '' ?>>রোল</option>
                                            <option value="id" <?= $searchType === 'id' ? 'selected' : '' ?>>আইডি</option>
                                        </select>
                                    </div>
                                    <div class="col-md-7">
                                        <label for="search_term" class="form-label">সার্চ টার্ম</label>
                                        <input type="text" class="form-control" id="search_term" name="search_term" value="<?= htmlspecialchars($searchTerm) ?>" placeholder="নাম/রোল/আইডি লিখুন...">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-1"></i> খুঁজুন
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Report Summary -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-money-bill me-2"></i> মোট পরিমাণ</h5>
                            <h3 class="card-text">৳ <?= number_format($totalAmount ?? 0, 2) ?></h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-check-circle me-2"></i> মোট পরিশোধিত</h5>
                            <h3 class="card-text">৳ <?= number_format($totalPaid ?? 0, 2) ?></h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-exclamation-circle me-2"></i> মোট বকেয়া</h5>
                            <h3 class="card-text">৳ <?= number_format($totalDue ?? 0, 2) ?></h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Report Table -->
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-table me-2"></i> রিপোর্ট তথ্য</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <?php if ($reportType === 'collection'): ?>
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>আইডি</th>
                                        <th>শিক্ষার্থী</th>
                                        <th>রোল</th>
                                        <th>শ্রেণী</th>
                                        <th>বিভাগ</th>
                                        <th>সেশন</th>
                                        <th>ফি ধরন</th>
                                        <th>পরিমাণ</th>
                                        <th>পরিশোধিত</th>
                                        <th>বকেয়া</th>
                                        <th>তারিখ</th>
                                        <th>স্ট্যাটাস</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($reportData)): ?>
                                        <?php foreach ($reportData as $fee): ?>
                                            <?php
                                                $dueAmount = ($fee['amount'] ?? 0) - ($fee['paid'] ?? 0);
                                                $statusClass = 'bg-danger text-white';
                                                if ($fee['payment_status'] === 'paid') {
                                                    $statusClass = 'bg-success text-white';
                                                } else if ($fee['payment_status'] === 'partial') {
                                                    $statusClass = 'bg-warning text-dark';
                                                }
                                            ?>
                                            <tr>
                                                <td><?= $fee['id'] ?></td>
                                                <td><?= htmlspecialchars($fee['first_name'] . ' ' . $fee['last_name']) ?></td>
                                                <td><?= htmlspecialchars($fee['roll']) ?></td>
                                                <td><?= htmlspecialchars($fee['class_name']) ?></td>
                                                <td><?= htmlspecialchars($fee['department_name']) ?></td>
                                                <td><?= htmlspecialchars($fee['session_name'] ?? 'অনির্দিষ্ট') ?></td>
                                                <td><?= htmlspecialchars($fee['fee_type']) ?></td>
                                                <td>৳ <?= number_format($fee['amount'] ?? 0, 2) ?></td>
                                                <td>৳ <?= number_format($fee['paid'] ?? 0, 2) ?></td>
                                                <td>৳ <?= number_format($dueAmount ?? 0, 2) ?></td>
                                                <td><?= date('d/m/Y', strtotime($fee['due_date'])) ?></td>
                                                <td><span class="badge <?= $statusClass ?>"><?= $fee['payment_status'] === 'due' ? 'বকেয়া' : ($fee['payment_status'] === 'partial' ? 'আংশিক' : 'পরিশোধিত') ?></span></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="11" class="text-center">কোন তথ্য পাওয়া যায়নি</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        <?php elseif ($reportType === 'student'): ?>
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>আইডি</th>
                                        <th>শিক্ষার্থী</th>
                                        <th>রোল</th>
                                        <th>শ্রেণী</th>
                                        <th>বিভাগ</th>
                                        <th>সেশন</th>
                                        <th>মোট পরিমাণ</th>
                                        <th>মোট পরিশোধিত</th>
                                        <th>মোট বকেয়া</th>
                                        <th>পদক্ষেপ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($reportData)): ?>
                                        <?php foreach ($reportData as $student): ?>
                                            <tr>
                                                <td><?= $student['id'] ?></td>
                                                <td><?= htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) ?></td>
                                                <td><?= htmlspecialchars($student['roll']) ?></td>
                                                <td><?= htmlspecialchars($student['class_name']) ?></td>
                                                <td><?= htmlspecialchars($student['department_name']) ?></td>
                                                <td><?= htmlspecialchars($student['session_name'] ?? 'অনির্দিষ্ট') ?></td>
                                                <td>৳ <?= number_format($student['total_amount'] ?? 0, 2) ?></td>
                                                <td>৳ <?= number_format($student['total_paid'] ?? 0, 2) ?></td>
                                                <td>৳ <?= number_format($student['total_due'] ?? 0, 2) ?></td>
                                                <td>
                                                    <a href="student_payment_receipt.php?student_id=<?= $student['id'] ?>" class="btn btn-sm btn-info">
                                                        <i class="fas fa-print"></i> রিসিপ্ট
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="9" class="text-center">কোন তথ্য পাওয়া যায়নি</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        <?php elseif ($reportType === 'class'): ?>
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>আইডি</th>
                                        <th>শ্রেণী</th>
                                        <th>শিক্ষার্থী সংখ্যা</th>
                                        <th>মোট পরিমাণ</th>
                                        <th>মোট পরিশোধিত</th>
                                        <th>মোট বকেয়া</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($reportData)): ?>
                                        <?php foreach ($reportData as $class): ?>
                                            <tr>
                                                <td><?= $class['id'] ?></td>
                                                <td><?= htmlspecialchars($class['class_name']) ?></td>
                                                <td><?= $class['student_count'] ?></td>
                                                <td>৳ <?= number_format($class['total_amount'] ?? 0, 2) ?></td>
                                                <td>৳ <?= number_format($class['total_paid'] ?? 0, 2) ?></td>
                                                <td>৳ <?= number_format($class['total_due'] ?? 0, 2) ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="6" class="text-center">কোন তথ্য পাওয়া যায়নি</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        <?php elseif ($reportType === 'department'): ?>
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>আইডি</th>
                                        <th>বিভাগ</th>
                                        <th>শিক্ষার্থী সংখ্যা</th>
                                        <th>মোট পরিমাণ</th>
                                        <th>মোট পরিশোধিত</th>
                                        <th>মোট বকেয়া</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($reportData)): ?>
                                        <?php foreach ($reportData as $department): ?>
                                            <tr>
                                                <td><?= $department['id'] ?></td>
                                                <td><?= htmlspecialchars($department['department_name']) ?></td>
                                                <td><?= $department['student_count'] ?></td>
                                                <td>৳ <?= number_format($department['total_amount'] ?? 0, 2) ?></td>
                                                <td>৳ <?= number_format($department['total_paid'] ?? 0, 2) ?></td>
                                                <td>৳ <?= number_format($department['total_due'] ?? 0, 2) ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="6" class="text-center">কোন তথ্য পাওয়া যায়নি</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<style>
    @media print {
        .sidebar, .navbar, .btn-toolbar, .card-header, form, .no-print {
            display: none !important;
        }

        main {
            width: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        .card {
            border: none !important;
        }

        .card-body {
            padding: 0 !important;
        }

        table {
            width: 100% !important;
        }
    }
</style>

<?php
// Include footer
include_once 'includes/footer.php';
?>

<script>
    // Update search term placeholder based on search type
    document.addEventListener('DOMContentLoaded', function() {
        const searchTypeSelect = document.getElementById('search_type');
        const searchTermInput = document.getElementById('search_term');

        // Function to update placeholder
        function updatePlaceholder() {
            const searchType = searchTypeSelect.value;

            if (searchType === 'name') {
                searchTermInput.placeholder = 'শিক্ষার্থীর নাম লিখুন...';
            } else if (searchType === 'roll') {
                searchTermInput.placeholder = 'রোল নম্বর লিখুন...';
            } else if (searchType === 'id') {
                searchTermInput.placeholder = 'আইডি নম্বর লিখুন...';
            }
        }

        // Set initial placeholder
        updatePlaceholder();

        // Update placeholder when search type changes
        searchTypeSelect.addEventListener('change', updatePlaceholder);
    });
</script>
