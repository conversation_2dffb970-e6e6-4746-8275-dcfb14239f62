@echo off
echo ===================================================
echo        কলেজ ম্যানেজমেন্ট সিস্টেম সার্ভার সেটআপ
echo ===================================================
echo.

echo সার্ভার সেটআপ শুরু হচ্ছে...
echo.

echo Apache কনফিগারেশন আপডেট করা হচ্ছে...
set APACHE_CONF=D:/xampp\apache\conf\httpd.conf

copy "%APACHE_CONF%" "%APACHE_CONF%.bak" >nul

powershell -Command "(Get-Content '%APACHE_CONF%') -replace 'Listen 80', 'Listen 0.0.0.0:80' | Set-Content '%APACHE_CONF%'"

powershell -Command "(Get-Content '%APACHE_CONF%') -replace '#ServerName www.example.com:80', 'ServerName localhost' | Set-Content '%APACHE_CONF%'"

echo Apache কনফিগারেশন আপডেট করা হয়েছে।
echo.

echo ফায়ারওয়াল রুল তৈরি করা হচ্ছে...
netsh advfirewall firewall add rule name="Apache HTTP Server" dir=in action=allow protocol=TCP localport=80 >nul
netsh advfirewall firewall add rule name="Apache HTTPS Server" dir=in action=allow protocol=TCP localport=443 >nul
netsh advfirewall firewall add rule name="MySQL Server" dir=in action=allow protocol=TCP localport=3306 >nul
echo ফায়ারওয়াল রুল তৈরি করা হয়েছে।
echo.

echo Apache সার্ভার রিস্টার্ট করা হচ্ছে...
net stop Apache2.4 >nul 2>&1
timeout /t 2 /nobreak >nul
net start Apache2.4 >nul 2>&1
echo Apache সার্ভার রিস্টার্ট করা হয়েছে।
echo.

