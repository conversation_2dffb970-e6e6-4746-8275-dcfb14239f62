<?php
require_once 'includes/dbh.inc.php';

// Check if exam_subject_relations table exists
$result = $conn->query("SHOW TABLES LIKE 'exam_subject_relations'");
if ($result->num_rows == 0) {
    // Create exam_subject_relations table
    $createTableQuery = "CREATE TABLE IF NOT EXISTS exam_subject_relations (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        exam_id INT(11) NOT NULL,
        subject_id INT(11) NOT NULL,
        total_marks INT(11) DEFAULT 100,
        passing_marks INT(11) DEFAULT 33,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (exam_id) REFERENCES exams(id) ON DELETE CASCADE,
        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
        UNIQUE KEY (exam_id, subject_id)
    )";
    
    if ($conn->query($createTableQuery)) {
        echo "exam_subject_relations table created successfully!";
    } else {
        echo "Error creating exam_subject_relations table: " . $conn->error;
    }
} else {
    echo "exam_subject_relations table already exists.";
}
?>
