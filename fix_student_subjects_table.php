<?php
// Include database connection
require_once 'includes/dbh.inc.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Fix Student Subjects Table</h1>";

// Check if student_subjects table exists
$tableExistsQuery = "SHOW TABLES LIKE 'student_subjects'";
$tableExists = $conn->query($tableExistsQuery)->num_rows > 0;

if (!$tableExists) {
    echo "<p>student_subjects table does not exist. Creating it now...</p>";
    
    // Create the table
    $createTableQuery = "CREATE TABLE student_subjects (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        student_id INT(11) NOT NULL,
        subject_id INT(11) NOT NULL,
        category ENUM('required', 'optional', 'fourth') NOT NULL DEFAULT 'optional',
        session_id INT(11) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        <PERSON>OR<PERSON><PERSON><PERSON> KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
        FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE
    )";
    
    if ($conn->query($createTableQuery)) {
        echo "<p>student_subjects table created successfully!</p>";
    } else {
        echo "<p>Error creating student_subjects table: " . $conn->error . "</p>";
    }
} else {
    echo "<p>student_subjects table already exists. Checking structure...</p>";
    
    // Check table structure
    $tableStructureQuery = "DESCRIBE student_subjects";
    $tableStructure = $conn->query($tableStructureQuery);
    
    if ($tableStructure) {
        echo "<h2>Current Table Structure</h2>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        $columns = [];
        while ($column = $tableStructure->fetch_assoc()) {
            $columns[$column['Field']] = $column;
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "<td>{$column['Extra']}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        // Check for required columns
        $requiredColumns = [
            'id' => 'INT(11) AUTO_INCREMENT PRIMARY KEY',
            'student_id' => 'INT(11) NOT NULL',
            'subject_id' => 'INT(11) NOT NULL',
            'category' => "ENUM('required', 'optional', 'fourth') NOT NULL DEFAULT 'optional'",
            'session_id' => 'INT(11) NOT NULL',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
        ];
        
        $alterTableQueries = [];
        
        foreach ($requiredColumns as $columnName => $columnDefinition) {
            if (!isset($columns[$columnName])) {
                echo "<p>Missing column: $columnName. Adding it...</p>";
                $alterTableQueries[] = "ALTER TABLE student_subjects ADD COLUMN $columnName $columnDefinition";
            }
        }
        
        // Check if category column has the correct type
        if (isset($columns['category']) && strpos($columns['category']['Type'], "enum('required','optional','fourth')") === false) {
            echo "<p>Category column has incorrect type. Modifying it...</p>";
            $alterTableQueries[] = "ALTER TABLE student_subjects MODIFY COLUMN category ENUM('required', 'optional', 'fourth') NOT NULL DEFAULT 'optional'";
        }
        
        // Execute alter table queries
        if (!empty($alterTableQueries)) {
            echo "<h2>Executing Table Modifications</h2>";
            
            foreach ($alterTableQueries as $query) {
                echo "<p>Executing: $query</p>";
                
                if ($conn->query($query)) {
                    echo "<p>Query executed successfully!</p>";
                } else {
                    echo "<p>Error executing query: " . $conn->error . "</p>";
                }
            }
        } else {
            echo "<p>Table structure is correct. No modifications needed.</p>";
        }
        
        // Check for foreign keys
        $foreignKeysQuery = "SELECT * FROM information_schema.KEY_COLUMN_USAGE 
                            WHERE TABLE_SCHEMA = DATABASE() 
                            AND TABLE_NAME = 'student_subjects' 
                            AND REFERENCED_TABLE_NAME IS NOT NULL";
        $foreignKeys = $conn->query($foreignKeysQuery);
        
        echo "<h2>Foreign Keys</h2>";
        
        if ($foreignKeys && $foreignKeys->num_rows > 0) {
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>Column</th><th>Referenced Table</th><th>Referenced Column</th></tr>";
            
            $existingForeignKeys = [];
            while ($fk = $foreignKeys->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$fk['COLUMN_NAME']}</td>";
                echo "<td>{$fk['REFERENCED_TABLE_NAME']}</td>";
                echo "<td>{$fk['REFERENCED_COLUMN_NAME']}</td>";
                echo "</tr>";
                
                $existingForeignKeys[$fk['COLUMN_NAME']] = true;
            }
            
            echo "</table>";
            
            // Check for missing foreign keys
            $requiredForeignKeys = [
                'student_id' => ['table' => 'students', 'column' => 'id'],
                'subject_id' => ['table' => 'subjects', 'column' => 'id'],
                'session_id' => ['table' => 'sessions', 'column' => 'id']
            ];
            
            $addForeignKeyQueries = [];
            
            foreach ($requiredForeignKeys as $columnName => $reference) {
                if (!isset($existingForeignKeys[$columnName])) {
                    echo "<p>Missing foreign key for column: $columnName. Adding it...</p>";
                    $addForeignKeyQueries[] = "ALTER TABLE student_subjects ADD FOREIGN KEY ($columnName) REFERENCES {$reference['table']}({$reference['column']}) ON DELETE CASCADE";
                }
            }
            
            // Execute add foreign key queries
            if (!empty($addForeignKeyQueries)) {
                echo "<h2>Adding Foreign Keys</h2>";
                
                foreach ($addForeignKeyQueries as $query) {
                    echo "<p>Executing: $query</p>";
                    
                    if ($conn->query($query)) {
                        echo "<p>Query executed successfully!</p>";
                    } else {
                        echo "<p>Error executing query: " . $conn->error . "</p>";
                    }
                }
            } else {
                echo "<p>All required foreign keys are present.</p>";
            }
        } else {
            echo "<p>No foreign keys found. Adding them...</p>";
            
            $addForeignKeyQueries = [
                "ALTER TABLE student_subjects ADD FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE",
                "ALTER TABLE student_subjects ADD FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE",
                "ALTER TABLE student_subjects ADD FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE"
            ];
            
            foreach ($addForeignKeyQueries as $query) {
                echo "<p>Executing: $query</p>";
                
                if ($conn->query($query)) {
                    echo "<p>Query executed successfully!</p>";
                } else {
                    echo "<p>Error executing query: " . $conn->error . "</p>";
                }
            }
        }
    } else {
        echo "<p>Error checking table structure: " . $conn->error . "</p>";
    }
}

// Check for any data in the table
$dataQuery = "SELECT COUNT(*) as count FROM student_subjects";
$dataResult = $conn->query($dataQuery);

if ($dataResult) {
    $dataCount = $dataResult->fetch_assoc()['count'];
    echo "<h2>Data Check</h2>";
    echo "<p>Found $dataCount records in the student_subjects table.</p>";
    
    if ($dataCount > 0) {
        // Check for any invalid references
        $invalidStudentQuery = "SELECT ss.* FROM student_subjects ss LEFT JOIN students s ON ss.student_id = s.id WHERE s.id IS NULL";
        $invalidStudentResult = $conn->query($invalidStudentQuery);
        
        if ($invalidStudentResult && $invalidStudentResult->num_rows > 0) {
            echo "<p>Found " . $invalidStudentResult->num_rows . " records with invalid student references. Cleaning up...</p>";
            
            $deleteInvalidStudentQuery = "DELETE ss FROM student_subjects ss LEFT JOIN students s ON ss.student_id = s.id WHERE s.id IS NULL";
            if ($conn->query($deleteInvalidStudentQuery)) {
                echo "<p>Invalid student references cleaned up successfully!</p>";
            } else {
                echo "<p>Error cleaning up invalid student references: " . $conn->error . "</p>";
            }
        } else {
            echo "<p>No invalid student references found.</p>";
        }
        
        $invalidSubjectQuery = "SELECT ss.* FROM student_subjects ss LEFT JOIN subjects s ON ss.subject_id = s.id WHERE s.id IS NULL";
        $invalidSubjectResult = $conn->query($invalidSubjectQuery);
        
        if ($invalidSubjectResult && $invalidSubjectResult->num_rows > 0) {
            echo "<p>Found " . $invalidSubjectResult->num_rows . " records with invalid subject references. Cleaning up...</p>";
            
            $deleteInvalidSubjectQuery = "DELETE ss FROM student_subjects ss LEFT JOIN subjects s ON ss.subject_id = s.id WHERE s.id IS NULL";
            if ($conn->query($deleteInvalidSubjectQuery)) {
                echo "<p>Invalid subject references cleaned up successfully!</p>";
            } else {
                echo "<p>Error cleaning up invalid subject references: " . $conn->error . "</p>";
            }
        } else {
            echo "<p>No invalid subject references found.</p>";
        }
        
        $invalidSessionQuery = "SELECT ss.* FROM student_subjects ss LEFT JOIN sessions s ON ss.session_id = s.id WHERE s.id IS NULL";
        $invalidSessionResult = $conn->query($invalidSessionQuery);
        
        if ($invalidSessionResult && $invalidSessionResult->num_rows > 0) {
            echo "<p>Found " . $invalidSessionResult->num_rows . " records with invalid session references. Cleaning up...</p>";
            
            $deleteInvalidSessionQuery = "DELETE ss FROM student_subjects ss LEFT JOIN sessions s ON ss.session_id = s.id WHERE s.id IS NULL";
            if ($conn->query($deleteInvalidSessionQuery)) {
                echo "<p>Invalid session references cleaned up successfully!</p>";
            } else {
                echo "<p>Error cleaning up invalid session references: " . $conn->error . "</p>";
            }
        } else {
            echo "<p>No invalid session references found.</p>";
        }
    }
} else {
    echo "<p>Error checking data: " . $conn->error . "</p>";
}

echo "<h2>Table Fix Complete</h2>";
echo "<p>The student_subjects table has been checked and fixed if necessary.</p>";
echo "<p><a href='admin/student_subject_selection.php?id=STD-601523'>Go to Student Subject Selection</a></p>";
?>
