/* Modern Styles for Subject Marks Distribution Page */

/* Font Settings */
@import url('https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@300;400;500;600;700&display=swap');

body, html, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, select, textarea, label, li, table, th, td {
    font-family: 'Hind Siliguri', 'Noto Sans Bengali', sans-serif !important;
}

/* Page Loading Indicator */
#page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.95);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease-in-out;
}

.loader-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loader-text {
    margin-top: 15px;
    font-size: 16px;
    color: #333;
    font-weight: 500;
}

body {
    background-color: #f8faff;
    background-image: linear-gradient(135deg, rgba(245, 247, 255, 0.5) 0%, rgba(233, 236, 255, 0.5) 100%);
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

body.loaded {
    opacity: 1;
}

/* Sidebar Styles */
.sidebar {
    background: linear-gradient(135deg, #3a7bd5 0%, #00d2ff 100%);
    color: white;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    width: 16.66%;
    overflow-y: auto;
    padding-top: 20px;
    padding-bottom: 60px;
    z-index: 100;
    box-shadow: 0 0 25px rgba(0, 0, 0, 0.1);
    border-radius: 0 15px 15px 0;
}

.sidebar .text-center h3 {
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    letter-spacing: 0.5px;
    position: relative;
    display: inline-block;
}

.sidebar .text-center h3:after {
    content: '';
    position: absolute;
    width: 50%;
    height: 3px;
    background: linear-gradient(to right, transparent, #fff, transparent);
    bottom: -10px;
    left: 25%;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.95);
    padding: 12px 20px;
    margin-bottom: 8px;
    border-radius: 10px;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    font-weight: 500;
    position: relative;
    overflow: hidden;
    margin-left: 5px;
    margin-right: 5px;
}

.sidebar .nav-link:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.5s ease;
}

.sidebar .nav-link:hover:before {
    left: 100%;
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    transform: translateX(5px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.sidebar .nav-link.active {
    background: linear-gradient(to right, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    color: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    font-weight: 600;
    border-left: 4px solid white;
}

.sidebar .nav-item {
    margin-bottom: 3px;
}

.sidebar .nav-link i {
    width: 24px;
    text-align: center;
    margin-right: 8px;
    font-size: 1.1em;
    transition: transform 0.3s ease;
}

.sidebar .nav-link:hover i {
    transform: scale(1.2);
}

.main-content {
    margin-left: 16.66%;
    padding: 30px;
    min-height: 100vh;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
    background: #ffffff;
    margin-bottom: 25px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 20px 25px;
    position: relative;
    overflow: hidden;
}

.card-header:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%);
}

.card-header.bg-primary {
    background: linear-gradient(135deg, #3a7bd5 0%, #00d2ff 100%) !important;
}

.card-header .card-title {
    font-weight: 600;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.card-body {
    padding: 25px;
}

.icon-circle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #3a7bd5 0%, #00d2ff 100%);
    border-radius: 50%;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Form Styles */
.form-control, .form-select {
    border-radius: 12px;
    padding: 14px 18px;
    border: 1px solid #e0e6ed;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
    font-size: 1rem;
}

.form-control:focus, .form-select:focus {
    border-color: #3a7bd5;
    box-shadow: 0 0 0 0.25rem rgba(58, 123, 213, 0.15);
    transform: translateY(-2px);
}

.form-label {
    font-weight: 500;
    margin-bottom: 10px;
    color: #495057;
    letter-spacing: 0.3px;
}

.input-group {
    border-radius: 12px;
    overflow: hidden;
}

.input-group-text {
    border: none;
    background-color: #f8f9fa;
    color: #6c757d;
    font-weight: 500;
}

/* Form Switch */
.form-check-input {
    cursor: pointer;
}

.form-switch .form-check-input {
    width: 3em;
    height: 1.5em;
    margin-top: 0.2em;
}

.form-switch .form-check-input:checked {
    background-color: #3a7bd5;
    border-color: #3a7bd5;
}

.form-switch .form-check-label {
    cursor: pointer;
    padding-left: 0.5em;
}

/* Status Toggle Form */
.status-toggle-form {
    margin: 0;
}

.status-toggle-form button {
    min-width: 90px;
    transition: all 0.3s ease;
}

.status-toggle-form button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.btn {
    border-radius: 12px;
    padding: 12px 24px;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    letter-spacing: 0.5px;
}

.btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: all 0.5s ease;
}

.btn:hover:before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #3a7bd5 0%, #00d2ff 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2d62aa 0%, #00b8e6 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(58, 123, 213, 0.3);
}

.btn-outline-primary {
    border: 2px solid #3a7bd5;
    color: #3a7bd5;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #3a7bd5 0%, #00d2ff 100%);
    border-color: transparent;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(58, 123, 213, 0.2);
}

.btn-lg {
    padding: 15px 30px;
    font-size: 1.1rem;
}

/* Table Styles */
.table {
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
    margin-bottom: 0;
}

.table th {
    background: linear-gradient(to right, #f8f9fa, #e9ecef);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    padding: 18px 15px;
    border-bottom: 2px solid #dee2e6;
    color: #495057;
}

.table td {
    padding: 18px 15px;
    vertical-align: middle;
    border-bottom: 1px solid #e9ecef;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.table tr:hover td {
    background-color: rgba(58, 123, 213, 0.05);
}

.table tr:last-child td {
    border-bottom: none;
}

.table .badge {
    padding: 8px 12px;
    font-weight: 500;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.table-responsive {
    border-radius: 15px;
    overflow: hidden;
}

.table-hover tbody tr {
    transition: all 0.3s ease;
}

.table-hover tbody tr:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    z-index: 1;
    position: relative;
}

/* Progress Bar */
.progress {
    height: 12px;
    border-radius: 6px;
    overflow: hidden;
    background-color: #e9ecef;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    margin: 10px 0;
}

.progress-bar {
    transition: width 0.6s ease;
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-size: 1rem 1rem;
    animation: progress-bar-stripes 1s linear infinite;
}

.progress-bar.bg-success {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
}

.progress-bar.bg-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
}

.progress-bar.bg-danger {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
}

@keyframes progress-bar-stripes {
    from { background-position: 1rem 0; }
    to { background-position: 0 0; }
}

/* Alert Styles */
.alert {
    border-radius: 15px;
    border: none;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.05);
    padding: 20px;
}

.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(32, 201, 151, 0.1));
    border-left: 4px solid #28a745;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(200, 35, 51, 0.1));
    border-left: 4px solid #dc3545;
}

.alert-info {
    background: linear-gradient(135deg, rgba(58, 123, 213, 0.1), rgba(0, 210, 255, 0.1));
    border-left: 4px solid #3a7bd5;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(253, 126, 20, 0.1));
    border-left: 4px solid #ffc107;
}

/* Modal Styles */
.modal-content {
    border-radius: 20px;
    border: none;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.modal-header {
    background: linear-gradient(135deg, #3a7bd5 0%, #00d2ff 100%);
    color: white;
    border-bottom: none;
    padding: 25px 30px;
}

.modal-header .modal-title {
    font-weight: 600;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    border-top: none;
    padding: 20px 30px;
    background-color: #f8f9fa;
}

.modal-dialog-centered {
    display: flex;
    align-items: center;
    min-height: calc(100% - 3.5rem);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInUp {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInLeft {
    from { transform: translateX(-30px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(30px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.slide-in-up {
    animation: slideInUp 0.5s ease-out forwards;
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out forwards;
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out forwards;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #3a7bd5 0%, #00d2ff 100%);
    border-radius: 10px;
    border: 2px solid #f1f1f1;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #2d62aa 0%, #00b8e6 100%);
}

/* Print Styles */
@media print {
    .sidebar, .btn-toolbar, .no-print {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }

    body {
        background: white !important;
    }
}
