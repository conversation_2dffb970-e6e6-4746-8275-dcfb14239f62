<?php
require_once 'includes/dbh.inc.php';

echo "<h2>Checking users in database</h2>";

$sql = "SELECT * FROM users";
$result = $conn->query($sql);

if ($result) {
    if ($result->num_rows > 0) {
        echo "<p>Found " . $result->num_rows . " users in the database:</p>";
        echo "<ul>";
        while ($row = $result->fetch_assoc()) {
            echo "<li>ID: " . $row['id'] . ", Username: " . $row['username'] . ", User Type: " . $row['user_type'] . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No users found in the database.</p>";
    }
} else {
    echo "<p>Error querying database: " . $conn->error . "</p>";
}

$conn->close();
?> 