<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if necessary tables exist
$tables = ['exams', 'exam_subject_relations', 'subjects', 'exam_routine'];
$missingTables = [];
$createdTables = [];

foreach ($tables as $table) {
    $tableCheck = $conn->query("SHOW TABLES LIKE '$table'");
    if ($tableCheck->num_rows == 0) {
        $missingTables[] = $table;
    }
}

// Create exam_routine table if it doesn't exist
$examRoutineCheck = $conn->query("SHOW TABLES LIKE 'exam_routine'");
if ($examRoutineCheck->num_rows == 0) {
    $createExamRoutineTable = "CREATE TABLE IF NOT EXISTS exam_routine (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        exam_id INT(11) NOT NULL,
        subject_id INT(11) NOT NULL,
        exam_date DATE NOT NULL,
        start_time TIME,
        end_time TIME,
        room_no VARCHAR(50),
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_exam_subject_date (exam_id, subject_id, exam_date)
    )";

    if ($conn->query($createExamRoutineTable)) {
        $createdTables[] = 'exam_routine';
    }
}

// Handle form submissions
$successMessage = '';
$errorMessage = '';

// Get all exams
$examsQuery = "SELECT e.id, e.exam_name, e.exam_type, e.exam_date, e.total_marks,
              c.class_name, d.department_name, s.session_name,
              (SELECT COUNT(*) FROM exam_subject_relations WHERE exam_id = e.id) as subject_count
              FROM exams e
              LEFT JOIN classes c ON e.class_id = c.id
              LEFT JOIN departments d ON e.department_id = d.id
              LEFT JOIN sessions s ON e.session_id = s.id
              ORDER BY e.exam_date DESC";
$exams = $conn->query($examsQuery);

// Handle routine creation/update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_routine'])) {
    $examId = intval($_POST['exam_id']);
    $subjectIds = isset($_POST['subject_id']) ? $_POST['subject_id'] : [];
    $examDates = isset($_POST['exam_date']) ? $_POST['exam_date'] : [];
    $startTimes = isset($_POST['start_time']) ? $_POST['start_time'] : [];
    $endTimes = isset($_POST['end_time']) ? $_POST['end_time'] : [];
    $roomNos = isset($_POST['room_no']) ? $_POST['room_no'] : [];
    $notes = isset($_POST['notes']) ? $_POST['notes'] : [];

    if (empty($examId)) {
        $errorMessage = "পরীক্ষা নির্বাচন করুন!";
    } elseif (empty($subjectIds)) {
        $errorMessage = "কমপক্ষে একটি বিষয় নির্বাচন করুন!";
    } else {
        // Start transaction
        $conn->begin_transaction();

        try {
            // Delete existing routine entries for this exam
            $deleteQuery = "DELETE FROM exam_routine WHERE exam_id = ?";
            $stmt = $conn->prepare($deleteQuery);
            $stmt->bind_param("i", $examId);
            $stmt->execute();

            // Insert new routine entries
            $insertQuery = "INSERT INTO exam_routine (exam_id, subject_id, exam_date, start_time, end_time, room_no, notes)
                           VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($insertQuery);

            for ($i = 0; $i < count($subjectIds); $i++) {
                $subjectId = intval($subjectIds[$i]);
                $examDate = $examDates[$i];
                $startTime = !empty($startTimes[$i]) ? $startTimes[$i] : null;
                $endTime = !empty($endTimes[$i]) ? $endTimes[$i] : null;
                $roomNo = $roomNos[$i];
                $note = $notes[$i];

                $stmt->bind_param("iisssss", $examId, $subjectId, $examDate, $startTime, $endTime, $roomNo, $note);
                $stmt->execute();
            }

            // Commit transaction
            $conn->commit();

            $successMessage = "পরীক্ষার রুটিন সফলভাবে সংরক্ষণ করা হয়েছে!";
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            $errorMessage = "রুটিন সংরক্ষণ করতে সমস্যা হয়েছে: " . $e->getMessage();
        }
    }
}

// Get exam details and subjects if exam_id is provided
$selectedExam = null;
$examSubjects = [];
$routineEntries = [];

// Check if institution logo exists
$hasLogo = false;
$logoUrl = '';
$logoPath = '../uploads/signatures/institution_logo.png';
if (file_exists($logoPath)) {
    $hasLogo = true;
    $logoUrl = $logoPath . '?v=' . time(); // Add timestamp to prevent caching
}

// If no logo exists, create a default logo path
$defaultLogoPath = '../assets/images/default_logo.png';
if (!$hasLogo && !file_exists($defaultLogoPath)) {
    // Create directory if it doesn't exist
    if (!file_exists('../assets/images')) {
        mkdir('../assets/images', 0777, true);
    }

    // Use a placeholder image
    $defaultLogoUrl = 'https://via.placeholder.com/80x80?text=ZFAW';
    file_put_contents($defaultLogoPath, file_get_contents($defaultLogoUrl));
}

// Set the logo URL to use
$logoToUse = $hasLogo ? $logoUrl : $defaultLogoPath;

if (isset($_GET['exam_id']) && !empty($_GET['exam_id'])) {
    $examId = intval($_GET['exam_id']);

    // Get exam details
    $examQuery = "SELECT e.*, c.class_name, d.department_name, s.session_name
                 FROM exams e
                 LEFT JOIN classes c ON e.class_id = c.id
                 LEFT JOIN departments d ON e.department_id = d.id
                 LEFT JOIN sessions s ON e.session_id = s.id
                 WHERE e.id = ?";
    $stmt = $conn->prepare($examQuery);
    $stmt->bind_param("i", $examId);
    $stmt->execute();
    $selectedExam = $stmt->get_result()->fetch_assoc();

    // Get assigned subjects
    $subjectsQuery = "SELECT esr.*, s.subject_name, s.subject_code
                     FROM exam_subject_relations esr
                     JOIN subjects s ON esr.subject_id = s.id
                     WHERE esr.exam_id = ?
                     ORDER BY s.subject_name";
    $stmt = $conn->prepare($subjectsQuery);
    $stmt->bind_param("i", $examId);
    $stmt->execute();
    $subjectsResult = $stmt->get_result();

    while ($subject = $subjectsResult->fetch_assoc()) {
        $examSubjects[] = $subject;
    }

    // Get existing routine entries
    $routineQuery = "SELECT er.*, s.subject_name, s.subject_code
                    FROM exam_routine er
                    JOIN subjects s ON er.subject_id = s.id
                    WHERE er.exam_id = ?
                    ORDER BY er.exam_date, er.start_time";
    $stmt = $conn->prepare($routineQuery);
    $stmt->bind_param("i", $examId);
    $stmt->execute();
    $routineResult = $stmt->get_result();

    while ($entry = $routineResult->fetch_assoc()) {
        $routineEntries[$entry['subject_id']] = $entry;
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>পরীক্ষার রুটিন - স্কুল ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Flatpickr for date/time picker -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css">

    <style>
        @media print {
            body {
                margin: 0;
                padding: 0;
                font-family: 'Hind Siliguri', Arial, sans-serif;
            }
            body * {
                visibility: hidden;
            }
            .print-section, .print-section * {
                visibility: visible;
            }
            .print-section {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                max-width: 100%;
                font-size: 10px;
                padding: 0;
                margin: 0;
                box-sizing: border-box;
            }

            /* Fix for top spacing issue */
            .date-wise-print {
                position: fixed;
                top: 0 !important;
                left: 0;
                right: 0;
                margin-top: 0 !important;
                padding-top: 0 !important;
            }
            .no-print {
                display: none !important;
            }

            /* Invoice-style table */
            .invoice-table {
                border-collapse: collapse;
                width: 100%;
                border: 2px solid #000;
                margin: 0;
                padding: 0;
                table-layout: fixed;
            }

            .invoice-table th {
                background-color: #e0e0e0;
                border: 1px solid #000;
                padding: 5px;
                font-size: 12px;
                text-align: center;
                font-weight: bold;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .invoice-table td {
                border: 1px solid #000;
                padding: 5px;
                font-size: 12px;
                vertical-align: middle;
            }

            .invoice-header {
                text-align: center;
                margin: 0 0 5px 0;
                padding: 0;
                border-bottom: 1px solid #000;
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            .invoice-header-logo {
                display: flex;
                justify-content: center;
                align-items: center;
                margin-bottom: 2px;
            }

            .invoice-header-logo img {
                height: 40px;
                width: auto;
                max-width: 40px;
                object-fit: contain;
            }

            .invoice-header h3 {
                margin: 0 0 2px 0;
                padding: 0;
                font-size: 16px;
                font-weight: bold;
            }

            .invoice-header h4 {
                margin: 0 0 2px 0;
                padding: 0;
                font-size: 14px;
                font-weight: bold;
                display: block !important;
            }

            .invoice-header p {
                margin: 0 0 2px 0;
                padding: 0;
                font-size: 11px;
                display: block !important;
            }

            .date-cell {
                text-align: center;
                width: 15%;
                font-weight: bold;
                font-size: 12px !important;
            }

            .subject-cell {
                width: 40%;
                font-size: 12px !important;
            }

            .time-cell {
                text-align: center;
                width: 25%;
                font-size: 12px !important;
            }

            .room-cell {
                text-align: center;
                width: 10%;
                font-size: 12px !important;
            }

            .notes-cell {
                width: 15%;
                font-size: 12px !important;
            }

            .day-name {
                font-size: 10px;
                color: #555;
                display: block;
                margin-top: 2px;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .time-separator {
                margin: 5px 0;
                border-top: 1px dashed #aaa;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .signature-row td {
                text-align: center;
                height: 40px;
                vertical-align: bottom;
                border-top: 2px solid #000;
                font-weight: bold;
                font-size: 12px;
                padding-bottom: 5px;
            }

            /* Hide the inactive print view */
            .subject-wise-print.inactive-print,
            .date-wise-print.inactive-print {
                display: none !important;
            }

            /* Show the active print view */
            .subject-wise-print.active-print,
            .date-wise-print.active-print {
                display: block !important;
            }

            /* Default page settings */
            @page {
                size: A4 landscape;
                margin: 0.3cm;
            }

            /* Landscape and portrait mode classes for JavaScript switching */
            .date-wise-print.landscape-mode,
            .date-wise-print.portrait-mode {
                width: 100%;
            }

            /* Adjust font sizes for better fit */
            .date-wise-print .invoice-header h3 {
                font-size: 14px;
                margin: 0 0 2px 0;
            }

            .date-wise-print .invoice-header h4 {
                font-size: 12px;
                margin: 0 0 2px 0;
            }

            .date-wise-print .invoice-header p {
                font-size: 10px;
                margin: 0 0 3px 0;
            }

            .date-wise-print .invoice-table th,
            .date-wise-print .invoice-table td {
                padding: 1px 2px;
                font-size: 9px !important;
                line-height: 1.1;
            }

            /* Adjust font sizes for portrait mode */
            .date-wise-print.portrait-mode .invoice-table th,
            .date-wise-print.portrait-mode .invoice-table td {
                font-size: 8px !important;
                padding: 1px;
            }

            /* Compact layout for better fit */
            .date-wise-print .invoice-table {
                margin-bottom: 5px;
            }

            .date-wise-print .day-name {
                font-size: 8px;
                margin-top: 1px;
            }

            .date-wise-print .time-separator {
                margin: 2px 0;
            }

            /* Reduce space between rows */
            .date-wise-print .invoice-table tr {
                page-break-inside: avoid;
            }

            /* Ensure signature row stays at bottom */
            .date-wise-print .signature-table {
                margin-top: 5px;
            }

            .date-wise-print .signature-row td {
                height: 30px;
                font-size: 10px !important;
            }

            /* Fix for Firefox */
            @-moz-document url-prefix() {
                .print-section {
                    width: 190mm;
                }
            }
        }

        .routine-table {
            border-collapse: collapse;
            width: 100%;
        }

        .routine-table th, .routine-table td {
            border: 1px solid #dee2e6;
            padding: 8px;
        }

        .routine-table th {
            background-color: #f8f9fa;
            text-align: center;
        }

        .routine-header {
            text-align: center;
            margin-bottom: 10px;
        }

        .routine-header .invoice-header-logo {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 5px;
        }

        .routine-header .invoice-header-logo img {
            height: 40px;
            width: auto;
            max-width: 40px;
            object-fit: contain;
        }

        .signature-row td {
            height: 60px;
            vertical-align: bottom;
            text-align: center;
        }

        /* Compact styles for date-wise view */
        .date-wise-print .day-name {
            font-size: 80%;
            color: #666;
            margin-top: 2px;
        }

        .form-control, .form-select {
            border-radius: 8px;
            padding: 10px 15px;
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #4361ee;
            box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>পরীক্ষার রুটিন</h2>
                        <p class="text-muted">পরীক্ষার জন্য বিষয়ভিত্তিক তারিখ ও সময়সূচি তৈরি করুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="exam_dashboard.php" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>পরীক্ষা ড্যাশবোর্ড
                        </a>
                    </div>
                </div>

                <?php if ($successMessage): ?>
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle me-2"></i><?php echo $successMessage; ?>
                    </div>
                <?php endif; ?>

                <?php if ($errorMessage): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $errorMessage; ?>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- Exam Selection Form -->
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">পরীক্ষা নির্বাচন করুন</h5>
                            </div>
                            <div class="card-body">
                                <form method="GET" action="">
                                    <div class="mb-3">
                                        <label for="exam_id" class="form-label">পরীক্ষা</label>
                                        <select class="form-select" id="exam_id" name="exam_id" required>
                                            <option value="">পরীক্ষা নির্বাচন করুন</option>
                                            <?php if ($exams && $exams->num_rows > 0): ?>
                                                <?php while ($exam = $exams->fetch_assoc()): ?>
                                                    <option value="<?php echo $exam['id']; ?>" <?php echo (isset($_GET['exam_id']) && $_GET['exam_id'] == $exam['id']) ? 'selected' : ''; ?>>
                                                        <?php
                                                            echo htmlspecialchars($exam['exam_name']) . ' - ' . htmlspecialchars($exam['exam_type']);
                                                            if (!empty($exam['class_name'])) {
                                                                echo ' (' . htmlspecialchars($exam['class_name']);
                                                                if (!empty($exam['department_name'])) {
                                                                    echo '/' . htmlspecialchars($exam['department_name']);
                                                                }
                                                                echo ')';
                                                            }

                                                            // Show subject count
                                                            if ($exam['subject_count'] > 0) {
                                                                echo ' - ' . $exam['subject_count'] . ' বিষয়';
                                                            }
                                                        ?>
                                                    </option>
                                                <?php endwhile; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-search me-2"></i>পরীক্ষা দেখুন
                                    </button>
                                </form>
                            </div>
                        </div>

                        <?php if ($selectedExam && !empty($examSubjects)): ?>
                            <div class="card mt-4">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">পরীক্ষার তথ্য</h5>
                                </div>
                                <div class="card-body">
                                    <p><strong>পরীক্ষার নাম:</strong> <?php echo htmlspecialchars($selectedExam['exam_name']); ?></p>
                                    <p><strong>পরীক্ষার ধরন:</strong> <?php echo htmlspecialchars($selectedExam['exam_type']); ?></p>
                                    <p><strong>শ্রেণী:</strong> <?php echo htmlspecialchars($selectedExam['class_name'] ?? 'সকল শ্রেণী'); ?></p>
                                    <p><strong>বিভাগ:</strong> <?php echo htmlspecialchars($selectedExam['department_name'] ?? 'সকল বিভাগ'); ?></p>
                                    <p><strong>সেশন:</strong> <?php echo htmlspecialchars($selectedExam['session_name'] ?? 'N/A'); ?></p>
                                    <p><strong>মোট বিষয়:</strong> <?php echo count($examSubjects); ?></p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Routine Creation Form -->
                    <div class="col-md-8 mb-4">
                        <?php if ($selectedExam && !empty($examSubjects)): ?>
                            <div class="card">
                                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">পরীক্ষার রুটিন তৈরি করুন</h5>
                                    <div>
                                        <button type="button" onclick="printByDate('portrait');" class="btn btn-sm btn-light me-2 no-print">
                                            <i class="fas fa-print me-1"></i> পোর্ট্রেট প্রিন্ট
                                        </button>
                                        <button type="button" onclick="printByDate('landscape');" class="btn btn-sm btn-light me-2 no-print">
                                            <i class="fas fa-print me-1"></i> ল্যান্ডস্কেপ প্রিন্ট
                                        </button>
                                        <button type="button" onclick="autoFillDates();" class="btn btn-sm btn-light no-print">
                                            <i class="fas fa-calendar-alt me-1"></i> তারিখ অটো ফিল
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <form method="POST" action="" id="routineForm">
                                        <input type="hidden" name="exam_id" value="<?php echo $selectedExam['id']; ?>">

                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th width="5%">#</th>
                                                        <th width="25%">বিষয়</th>
                                                        <th width="15%">তারিখ</th>
                                                        <th width="15%">শুরু</th>
                                                        <th width="15%">শেষ</th>
                                                        <th width="10%">কক্ষ নং</th>
                                                        <th width="15%">মন্তব্য</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($examSubjects as $index => $subject): ?>
                                                        <tr>
                                                            <td><?php echo $index + 1; ?></td>
                                                            <td>
                                                                <?php echo htmlspecialchars($subject['subject_name']); ?>
                                                                <?php if (!empty($subject['subject_code'])): ?>
                                                                    <small class="text-muted d-block"><?php echo htmlspecialchars($subject['subject_code']); ?></small>
                                                                <?php endif; ?>
                                                                <input type="hidden" name="subject_id[]" value="<?php echo $subject['subject_id']; ?>">
                                                            </td>
                                                            <td>
                                                                <input type="text" class="form-control date-picker" name="exam_date[]" value="<?php echo isset($routineEntries[$subject['subject_id']]) ? $routineEntries[$subject['subject_id']]['exam_date'] : ''; ?>" required>
                                                            </td>
                                                            <td>
                                                                <input type="text" class="form-control time-picker" name="start_time[]" value="<?php echo isset($routineEntries[$subject['subject_id']]) ? $routineEntries[$subject['subject_id']]['start_time'] : ''; ?>">
                                                            </td>
                                                            <td>
                                                                <input type="text" class="form-control time-picker" name="end_time[]" value="<?php echo isset($routineEntries[$subject['subject_id']]) ? $routineEntries[$subject['subject_id']]['end_time'] : ''; ?>">
                                                            </td>
                                                            <td>
                                                                <input type="text" class="form-control" name="room_no[]" value="<?php echo isset($routineEntries[$subject['subject_id']]) ? $routineEntries[$subject['subject_id']]['room_no'] : ''; ?>">
                                                            </td>
                                                            <td>
                                                                <input type="text" class="form-control" name="notes[]" value="<?php echo isset($routineEntries[$subject['subject_id']]) ? $routineEntries[$subject['subject_id']]['notes'] : ''; ?>">
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>

                                        <div class="d-grid gap-2 mt-3 no-print">
                                            <button type="submit" name="save_routine" class="btn btn-success">
                                                <i class="fas fa-save me-2"></i>রুটিন সংরক্ষণ করুন
                                            </button>
                                        </div>
                                    </form>

                                    <!-- Printable Routine - Subject-wise (Default) -->
                                    <div class="print-section mt-4 subject-wise-print">
                                        <div class="routine-header">
                                            <div class="invoice-header-logo">
                                                <img src="<?php echo $logoToUse; ?>" alt="প্রতিষ্ঠানের লোগো" onerror="this.src='../assets/images/default_logo.png'">
                                            </div>
                                            <h3 class="text-center mb-1"><?php echo htmlspecialchars($_SESSION['institutionName'] ?? 'স্কুল ম্যানেজমেন্ট সিস্টেম'); ?></h3>
                                            <h4 class="text-center mb-3"><?php echo htmlspecialchars($selectedExam['exam_name'] . ' - ' . $selectedExam['exam_type']); ?> রুটিন</h4>
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <p><strong>শ্রেণী:</strong> <?php echo htmlspecialchars($selectedExam['class_name'] ?? 'সকল শ্রেণী'); ?></p>
                                                    <p><strong>বিভাগ:</strong> <?php echo htmlspecialchars($selectedExam['department_name'] ?? 'সকল বিভাগ'); ?></p>
                                                </div>
                                                <div class="col-md-6 text-md-end">
                                                    <p><strong>সেশন:</strong> <?php echo htmlspecialchars($selectedExam['session_name'] ?? 'N/A'); ?></p>
                                                </div>
                                            </div>
                                        </div>

                                        <table class="routine-table">
                                            <thead>
                                                <tr>
                                                    <th width="5%">ক্রম</th>
                                                    <th width="25%">বিষয়</th>
                                                    <th width="15%">তারিখ</th>
                                                    <th width="20%">সময়</th>
                                                    <th width="15%">কক্ষ নং</th>
                                                    <th width="20%">মন্তব্য</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
                                                $counter = 1;
                                                foreach ($examSubjects as $subject):
                                                    $hasRoutine = isset($routineEntries[$subject['subject_id']]);
                                                ?>
                                                    <tr>
                                                        <td class="text-center"><?php echo $counter++; ?></td>
                                                        <td>
                                                            <?php echo htmlspecialchars($subject['subject_name']); ?>
                                                            <?php if (!empty($subject['subject_code'])): ?>
                                                                <small class="d-block">(<?php echo htmlspecialchars($subject['subject_code']); ?>)</small>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td class="text-center">
                                                            <?php
                                                            if ($hasRoutine && !empty($routineEntries[$subject['subject_id']]['exam_date'])) {
                                                                echo date('d/m/Y', strtotime($routineEntries[$subject['subject_id']]['exam_date']));
                                                            } else {
                                                                echo '-';
                                                            }
                                                            ?>
                                                        </td>
                                                        <td class="text-center">
                                                            <?php
                                                            if ($hasRoutine && !empty($routineEntries[$subject['subject_id']]['start_time']) && !empty($routineEntries[$subject['subject_id']]['end_time'])) {
                                                                echo date('h:i A', strtotime($routineEntries[$subject['subject_id']]['start_time'])) . ' - ' .
                                                                     date('h:i A', strtotime($routineEntries[$subject['subject_id']]['end_time']));
                                                            } else {
                                                                echo '-';
                                                            }
                                                            ?>
                                                        </td>
                                                        <td class="text-center">
                                                            <?php echo $hasRoutine ? htmlspecialchars($routineEntries[$subject['subject_id']]['room_no'] ?? '-') : '-'; ?>
                                                        </td>
                                                        <td>
                                                            <?php echo $hasRoutine ? htmlspecialchars($routineEntries[$subject['subject_id']]['notes'] ?? '') : ''; ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>

                                                <!-- Signature Row -->
                                                <tr class="signature-row">
                                                    <td colspan="2" class="text-center">প্রস্তুতকারী</td>
                                                    <td colspan="2" class="text-center">পরীক্ষা নিয়ন্ত্রক</td>
                                                    <td colspan="2" class="text-center">অধ্যক্ষ/প্রধান শিক্ষক</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- Printable Routine - Date-wise (Invoice Style) -->
                                    <div class="print-section date-wise-print" style="display: none; margin-top: 0; padding-top: 0;">
                                        <div class="invoice-header">
                                            <div class="invoice-header-logo">
                                                <img src="<?php echo $logoToUse; ?>" alt="প্রতিষ্ঠানের লোগো" onerror="this.src='../assets/images/default_logo.png'">
                                            </div>
                                            <h3><?php echo htmlspecialchars($_SESSION['institutionName'] ?? 'স্কুল ম্যানেজমেন্ট সিস্টেম'); ?></h3>
                                            <h4><?php echo htmlspecialchars($selectedExam['exam_name'] . ' - ' . $selectedExam['exam_type']); ?> রুটিন</h4>
                                            <p>
                                                <span><strong>শ্রেণী:</strong> <?php echo htmlspecialchars($selectedExam['class_name'] ?? 'সকল শ্রেণী'); ?></span> |
                                                <span><strong>বিভাগ:</strong> <?php echo htmlspecialchars($selectedExam['department_name'] ?? 'সকল বিভাগ'); ?></span> |
                                                <span><strong>সেশন:</strong> <?php echo htmlspecialchars($selectedExam['session_name'] ?? 'N/A'); ?></span>
                                            </p>
                                        </div>

                                        <table class="invoice-table">
                                            <colgroup>
                                                <col width="12%">
                                                <col width="38%">
                                                <col width="20%">
                                                <col width="10%">
                                                <col width="20%">
                                            </colgroup>
                                            <thead>
                                                <tr>
                                                    <th>তারিখ</th>
                                                    <th>বিষয়সমূহ</th>
                                                    <th>সময়</th>
                                                    <th>কক্ষ নং</th>
                                                    <th>মন্তব্য</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
                                                // Group subjects by date
                                                $dateGroups = [];

                                                foreach ($examSubjects as $subject) {
                                                    if (isset($routineEntries[$subject['subject_id']]) && !empty($routineEntries[$subject['subject_id']]['exam_date'])) {
                                                        $date = $routineEntries[$subject['subject_id']]['exam_date'];

                                                        if (!isset($dateGroups[$date])) {
                                                            $dateGroups[$date] = [];
                                                        }

                                                        $dateGroups[$date][] = [
                                                            'subject' => $subject,
                                                            'routine' => $routineEntries[$subject['subject_id']]
                                                        ];
                                                    }
                                                }

                                                // Sort dates
                                                ksort($dateGroups);

                                                // Translate day names to Bengali
                                                $dayTranslations = [
                                                    'Monday' => 'সোমবার',
                                                    'Tuesday' => 'মঙ্গলবার',
                                                    'Wednesday' => 'বুধবার',
                                                    'Thursday' => 'বৃহস্পতিবার',
                                                    'Friday' => 'শুক্রবার',
                                                    'Saturday' => 'শনিবার',
                                                    'Sunday' => 'রবিবার'
                                                ];

                                                foreach ($dateGroups as $date => $subjects):
                                                    $formattedDate = date('d/m/Y', strtotime($date));
                                                    $dayName = date('l', strtotime($date));
                                                    $bengaliDayName = $dayTranslations[$dayName] ?? $dayName;

                                                    // Group subjects by time
                                                    $timeGroups = [];
                                                    foreach ($subjects as $item) {
                                                        $startTime = !empty($item['routine']['start_time']) ? $item['routine']['start_time'] : '';
                                                        $endTime = !empty($item['routine']['end_time']) ? $item['routine']['end_time'] : '';
                                                        $timeKey = $startTime . '-' . $endTime;

                                                        if (!isset($timeGroups[$timeKey])) {
                                                            $timeGroups[$timeKey] = [];
                                                        }

                                                        $timeGroups[$timeKey][] = $item;
                                                    }
                                                ?>
                                                    <tr>
                                                        <td class="date-cell">
                                                            <?php echo $formattedDate; ?>
                                                            <span class="day-name"><?php echo $bengaliDayName; ?></span>
                                                        </td>
                                                        <td class="subject-cell">
                                                            <?php
                                                            $subjectGroups = [];
                                                            foreach ($timeGroups as $timeKey => $timeItems) {
                                                                $subjectNames = [];
                                                                foreach ($timeItems as $item) {
                                                                    $subjectName = $item['subject']['subject_name'];
                                                                    if (!empty($item['subject']['subject_code'])) {
                                                                        $subjectName .= ' [' . $item['subject']['subject_code'] . ']';
                                                                    }
                                                                    $subjectNames[] = $subjectName;
                                                                }
                                                                $subjectGroups[] = implode(', ', $subjectNames);
                                                            }
                                                            echo implode('<br>', $subjectGroups);
                                                            ?>
                                                        </td>
                                                        <td class="time-cell">
                                                            <?php
                                                            $timeList = [];
                                                            foreach ($timeGroups as $timeKey => $timeItems) {
                                                                $item = $timeItems[0]; // Take first item for time display
                                                                if (!empty($item['routine']['start_time'])) {
                                                                    $time = date('h:i A', strtotime($item['routine']['start_time']));
                                                                    if (!empty($item['routine']['end_time'])) {
                                                                        $time .= ' - ' . date('h:i A', strtotime($item['routine']['end_time']));
                                                                    }
                                                                    $timeList[] = $time;
                                                                }
                                                            }
                                                            echo implode('<br>', $timeList);
                                                            ?>
                                                        </td>
                                                        <td class="room-cell">
                                                            <?php
                                                            $roomGroups = [];
                                                            foreach ($timeGroups as $timeKey => $timeItems) {
                                                                $roomList = [];
                                                                foreach ($timeItems as $item) {
                                                                    if (!empty($item['routine']['room_no'])) {
                                                                        $roomList[$item['routine']['room_no']] = $item['routine']['room_no']; // Use as key to avoid duplicates
                                                                    }
                                                                }
                                                                $roomGroups[] = implode(', ', $roomList);
                                                            }
                                                            echo implode('<br>', $roomGroups);
                                                            ?>
                                                        </td>
                                                        <td class="notes-cell">
                                                            <?php
                                                            $notesGroups = [];
                                                            foreach ($timeGroups as $timeKey => $timeItems) {
                                                                $notesList = [];
                                                                foreach ($timeItems as $item) {
                                                                    if (!empty($item['routine']['notes'])) {
                                                                        $notesList[] = $item['routine']['notes'];
                                                                    }
                                                                }
                                                                $notesGroups[] = implode(', ', $notesList);
                                                            }
                                                            echo implode('<br>', $notesGroups);
                                                            ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>

                                                <?php if (empty($dateGroups)): ?>
                                                    <tr>
                                                        <td colspan="5" style="text-align:center; padding:10px;">কোন রুটিন এখনো সেট করা হয়নি</td>
                                                    </tr>
                                                <?php endif; ?>
                                            </tbody>
                                        </table>

                                        <!-- Signature Row -->
                                        <table class="invoice-table signature-table" style="margin-top:5px; border-top:none; border-left:none; border-right:none;">
                                            <tr class="signature-row">
                                                <td width="33%">প্রস্তুতকারী</td>
                                                <td width="33%">পরীক্ষা নিয়ন্ত্রক</td>
                                                <td width="34%">অধ্যক্ষ/প্রধান শিক্ষক</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        <?php elseif ($selectedExam && empty($examSubjects)): ?>
                            <div class="card">
                                <div class="card-body text-center py-5">
                                    <i class="fas fa-exclamation-circle fa-3x text-warning mb-3"></i>
                                    <h4>কোন বিষয় অ্যাসাইন করা হয়নি</h4>
                                    <p class="text-muted">এই পরীক্ষার জন্য কোন বিষয় অ্যাসাইন করা হয়নি। রুটিন তৈরি করতে আগে বিষয় অ্যাসাইন করুন।</p>
                                    <div class="mt-3">
                                        <a href="assign_exam_subjects.php?exam_id=<?php echo $selectedExam['id']; ?>" class="btn btn-primary">
                                            <i class="fas fa-book me-2"></i>বিষয় অ্যাসাইন করুন
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="card">
                                <div class="card-body text-center py-5">
                                    <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                                    <h4>পরীক্ষা নির্বাচন করুন</h4>
                                    <p class="text-muted">রুটিন তৈরি করতে বাম পাশ থেকে একটি পরীক্ষা নির্বাচন করুন।</p>
                                    <div class="mt-3">
                                        <a href="assign_exam_subjects.php" class="btn btn-primary">
                                            <i class="fas fa-book me-2"></i>পরীক্ষার বিষয় অ্যাসাইন করুন
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize date pickers
            flatpickr(".date-picker", {
                dateFormat: "Y-m-d",
                allowInput: true
            });

            // Initialize time pickers
            flatpickr(".time-picker", {
                enableTime: true,
                noCalendar: true,
                dateFormat: "H:i",
                time_24hr: true,
                allowInput: true
            });

            // Add keyboard shortcut for printing (Ctrl+P)
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'p') {
                    e.preventDefault();
                    printByDate('landscape');
                    return false;
                }
            });
        });

        // Auto-fill dates function
        function autoFillDates() {
            const startDate = prompt("শুরুর তারিখ লিখুন (YYYY-MM-DD ফরম্যাটে):", "<?php echo date('Y-m-d'); ?>");
            if (!startDate) return;

            try {
                let currentDate = new Date(startDate);
                const datePickers = document.querySelectorAll('.date-picker');

                datePickers.forEach((picker, index) => {
                    // Skip weekends (Saturday and Sunday)
                    while (currentDate.getDay() === 0 || currentDate.getDay() === 6) {
                        currentDate.setDate(currentDate.getDate() + 1);
                    }

                    // Format date as YYYY-MM-DD
                    const formattedDate = currentDate.toISOString().split('T')[0];
                    picker.value = formattedDate;

                    // Move to next day
                    currentDate.setDate(currentDate.getDate() + 1);
                });

                // Update Flatpickr instances
                datePickers.forEach(picker => {
                    picker._flatpickr.setDate(picker.value);
                });
            } catch (e) {
                alert("তারিখ ফরম্যাট সঠিক নয়। YYYY-MM-DD ফরম্যাটে লিখুন।");
            }
        }

        // Function to print by date
        function printByDate(orientation = 'landscape') {
            // Hide subject-wise view and show date-wise view
            document.querySelector('.subject-wise-print').style.display = 'none';
            document.querySelector('.date-wise-print').style.display = 'block';

            // Add classes for print media query
            document.querySelector('.subject-wise-print').classList.add('inactive-print');
            document.querySelector('.subject-wise-print').classList.remove('active-print');
            document.querySelector('.date-wise-print').classList.add('active-print');
            document.querySelector('.date-wise-print').classList.remove('inactive-print');

            // Set orientation by adding a style tag
            let orientationStyle = document.getElementById('print-orientation-style');
            if (!orientationStyle) {
                orientationStyle = document.createElement('style');
                orientationStyle.id = 'print-orientation-style';
                document.head.appendChild(orientationStyle);
            }

            if (orientation === 'portrait') {
                orientationStyle.innerHTML = '@page { size: A4 portrait; margin: 0.3cm; }';
                document.querySelector('.date-wise-print').classList.add('portrait-mode');
                document.querySelector('.date-wise-print').classList.remove('landscape-mode');
            } else {
                orientationStyle.innerHTML = '@page { size: A4 landscape; margin: 0.3cm; }';
                document.querySelector('.date-wise-print').classList.add('landscape-mode');
                document.querySelector('.date-wise-print').classList.remove('portrait-mode');
            }

            // Ensure the print section is at the top of the page
            document.querySelector('.date-wise-print').style.top = '0';
            document.querySelector('.date-wise-print').style.marginTop = '0';

            // Make sure exam name and class/session info are visible
            const examInfo = document.querySelector('.date-wise-print .invoice-header h4');
            const sessionInfo = document.querySelector('.date-wise-print .invoice-header p');
            if (examInfo) examInfo.style.display = 'block';
            if (sessionInfo) sessionInfo.style.display = 'block';

            // Check if there are any subjects in the routine
            const dateGroups = document.querySelectorAll('.date-wise-print .invoice-table tbody tr');
            if (dateGroups.length === 0 || (dateGroups.length === 1 && dateGroups[0].querySelector('td[colspan="5"]'))) {
                alert('কোন রুটিন এখনো সেট করা হয়নি। প্রথমে রুটিন সেট করুন।');

                // Reset display
                document.querySelector('.subject-wise-print').style.display = 'block';
                document.querySelector('.date-wise-print').style.display = 'none';
                return;
            }

            // Scroll to top to ensure print starts from the top
            window.scrollTo(0, 0);

            // Force browser to recognize layout changes before printing
            setTimeout(function() {
                // Apply additional styles to ensure content starts from top
                document.body.style.margin = '0';
                document.body.style.padding = '0';

                // Trigger print
                window.print();

                // Reset display after printing
                setTimeout(function() {
                    document.querySelector('.subject-wise-print').style.display = 'block';
                    document.querySelector('.date-wise-print').style.display = 'none';

                    // Reset classes
                    document.querySelector('.subject-wise-print').classList.remove('inactive-print');
                    document.querySelector('.subject-wise-print').classList.add('active-print');
                    document.querySelector('.date-wise-print').classList.remove('active-print');
                    document.querySelector('.date-wise-print').classList.add('inactive-print');

                    // Reset body styles
                    document.body.style.margin = '';
                    document.body.style.padding = '';
                }, 500);
            }, 100);
        }
    </script>
</body>
</html>
