<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'অননুমোদিত প্রবেশ']);
    exit();
}

require_once '../includes/dbh.inc.php';

// Set content type to JSON
header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('অবৈধ অনুরোধ পদ্ধতি');
    }
    
    if (!isset($_POST['id']) || !isset($_POST['status'])) {
        throw new Exception('প্রয়োজনীয় প্যারামিটার অনুপস্থিত');
    }
    
    $id = intval($_POST['id']);
    $status = intval($_POST['status']);
    
    // Validate status value
    if ($status !== 0 && $status !== 1) {
        throw new Exception('অবৈধ স্ট্যাটাস মান');
    }
    
    // Check if template exists
    $checkQuery = "SELECT id, class_level_name FROM class_level_templates WHERE id = ?";
    $stmt = $conn->prepare($checkQuery);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        throw new Exception('টেমপ্লেট পাওয়া যায়নি');
    }
    
    $template = $result->fetch_assoc();
    
    // Update template status
    $updateQuery = "UPDATE class_level_templates SET is_active = ?, updated_at = NOW() WHERE id = ?";
    $stmt = $conn->prepare($updateQuery);
    $stmt->bind_param("ii", $status, $id);
    
    if ($stmt->execute()) {
        $statusText = $status ? 'সক্রিয়' : 'নিষ্ক্রিয়';
        echo json_encode([
            'success' => true, 
            'message' => "টেমপ্লেট '{$template['class_level_name']}' সফলভাবে {$statusText} করা হয়েছে।"
        ]);
    } else {
        throw new Exception('ডেটাবেস আপডেট করতে সমস্যা হয়েছে');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false, 
        'message' => $e->getMessage()
    ]);
}
?>
