<!doctype html>
<html class="no-js" lang="en">
    <head>
        

        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />

        
        
        
    
        
        <script src="//a.fsdn.com/con/js/min/sf.sandiego-cmp-top.js?1745596971"></script>

        
    

        


            



        <script>
            /*global unescape, window, SF*/
            // Setup our namespace
            if (!window.SF) { window.SF = {}; }
            if (!window.net) { window.net = {}; }
            if (!window.net.sf) { window.net.sf = {}; }
            SF.Ads = {};
            SF.cdn = '//a.fsdn.com/con';
            SF.alluracdn = '//a.fsdn.com/allura/cdn/allura/nf';
            SF.deploy_time = '1745596971';
            SF.sandiego = true;
            SF.sandiego_chrome = true;
            SF.variant = 'sf';
            SF.fpid = 'df03fd2f-93d7-4bac-b917-6ed54b6ca623';
            SF.billboard_route = '/software/product/$slug/';
            
            SF.Breakpoints = {
              small: 0,
              medium: 640,
              leaderboard: 743,
              billboard: 985,
              large: 1053,
              xlarge: 1295,
              xxlarge: 1366
            };
            SF.initial_breakpoints_visible = {};
            for (var bp in SF.Breakpoints) {
                if (!SF.Breakpoints.hasOwnProperty(bp)) {
                    continue;
                }
                SF.initial_breakpoints_visible[bp] = !window.matchMedia || window.matchMedia('(min-width: ' + SF.Breakpoints[bp] + 'px)').matches;
            }
            
                
                SF.Ads.viewportWidth = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);
                SF.Ads.viewportHeight = Math.max(document.documentElement.clientHeight, window.innerHeight || 0);
            
        </script>

        
        <script>
            window.ID5EspConfig = {partnerId: 1787};
        </script>
        

        <script>
            bizx.uids.gather();
        </script>
        
            
    
    
    
    
    
    
    
    
    <link rel="stylesheet" href="//a.fsdn.com/con/css/lato.css?1745596971">

        
        
        
        <link rel="stylesheet" href="//a.fsdn.com/con/css/sandiego.css?1745596971">
        
        <link rel="stylesheet" href="//a.fsdn.com/con/css/disallow.css?1745596971">

        
        
        <meta name="description" content="Create QR Codes in PHP">
        <meta name="keywords" content="Usability, QR Code Generators,  Open Source, Open Source Software, Development, Community, Source Code, Secure,  Downloads, Free Software">
    
    <link rel="canonical" href="https://sourceforge.net/projects/phpqrcode/files/">
        <title>PHP QR Code -  Browse Files at SourceForge.net</title>
        <link rel="icon" sizes="180x180" href="//a.fsdn.com/con/img/sandiego/logo-180x180.png" type="image/png">
<link rel="icon" sizes="any" href="//a.fsdn.com/con/img/sandiego/svg/originals/sf-icon-orange-no_sf.svg" type="image/svg+xml">
<link rel="apple-touch-icon" sizes="180x180" href="//a.fsdn.com/con/img/sandiego/logo-180x180.png">
<link rel="mask-icon" href="//a.fsdn.com/con/img/sandiego/svg/originals/sf-icon-orange-no_sf.svg" color="#FF6600">
        
        <script>
            /*global unescape, window, console, jQuery, $, net, SF, bizx  */
            if (!window.SF) {
                window.SF = {};
            }SF.EU_country_codes = ["ME","KY","CH","MF","JE","IS","BE","MT","PF","GR","GG","MQ","YT","TC","FR","DE","HR","GP","BM","PL","SE","MS","LV","PN","CW","EE","VG","GI","IO","GF","LU","SI","CZ","SH","IE","IT","GS","WF","LI","GL","AX","ES","AT","HU","SK","AI","BG","NC","FK","TF","SX","GB","NL","DK","PM","LT","AW","PT","BL","FI","RO","NO","CY","RE"];
            SF.unknown_country_codes = ["","A1","A2","O1"];
        </script>
        
    
        
        <script src="//a.fsdn.com/con/js/min/sf.sandiego-head.js?1745596971"></script>

        
    


        <style>.n92053996079b3a857e56cc49b1cba1e6a8ae4fac { display: none !important; }</style>

        
<script>SF.adblock = true;</script>  
<script src="//a.fsdn.com/con/js/adpopup.js?1745596971"></script>



<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-6471043051383295" crossorigin="anonymous"></script>  

        

        
    <script>
        function initPiwik(){
            var _paq = window._paq = window._paq || [];
            _paq.push(['trackPageView', document.title, {
                    dimension1: 'phpqrcode',
                dimension2: 'pg_files',
                dimension3: SF.devicePixelRatio,
                
            }]);
            _paq.push(['enableLinkTracking']);

            (function() {
                var u="//analytics.slashdotmedia.com/";
                _paq.push(['setTrackerUrl', u+'sf.php']);
                _paq.push(['setSiteId', 39]);
                 
        // only execute if 'measurement' has been granted
        bizx.cmp.ifConsent({ purposes: ['measurement'], vendors: 'sdm'}, function() {
            var interval = 6 * 60 * 60 * 1000; // 6 hrs, expressed in ms
            var vid_date = new Date(localStorage.getItem('vid_date'));
            if (new Date() - vid_date >= interval) {
                var data = {firstparty_id: "df03fd2f-93d7-4bac-b917-6ed54b6ca623", do_not_sell: false, is_commercial_page: "False" };
                bizx.cmp.ifConsent({ purposes: ['ads'], vendors: 'sdm'}, function() {},
                    function(){
                        // no consent (opt-out)
                        data.do_not_sell = true;
                    },
                    function(){
                        //finally call api endpoint
                        // push promise to pwik and set it run if pwik is allowed to run based on it's own ifConsent check
                        _paq.push([ function() {
                            data.matomo_id = this.getVisitorId();
                            data.domain = "sourceforge.net";
                            $.ajax({
                                method: 'PUT',
                                url: '/p/sfapi/push_vid',
                                data:  JSON.stringify(data)
                            })
                            .done(function(response){
                                if(response.result) {
                                    localStorage.setItem('vid_date', new Date());
                                }
                            })
                            .fail(function(){
                                // Do nothing on failure
                            });
                        }]);
                    }
                    );
                }
            });
    
                var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
                g.type='text/javascript'; g.async=true; g.defer=true; g.src=u+'sf.js'; s.parentNode.insertBefore(g,s);
            })();
        }
        bizx.cmp.ifConsent({ purposes: ['storage', 'measurement'], vendors: 'sdm' }, initPiwik);
    </script>

        
        


        

        <script type="application/ld+json">
            {
                "@context": "http://schema.org",
                "@type": "WebSite",
                "name": "SourceForge",
                "url": "https://sourceforge.net/"
            }
        </script>
    </head>
    <body id="pg_files"
          class="
    user
  anonymous has-ads sandiego v-sf">
        

            
        <div id="busy-spinner"></div>
        
        
<div id="messages">
    <section class="message error notify-sticky  ">
        <div class="content">The "/phpqrcode.zip" file could not be found or is not available.  Please select another file.</div>
    </section>
</div>


        <div class="off-canvas position-right" id="offCanvas" data-off-canvas>
    <!-- Menu -->
    <ul class="header-nav-menulist">
        <li class="highlight search">
            
            
                
            
            
    <form method="get" action="/directory/" class="m-search-form">
    
        <input type="text" placeholder="Search for software or solutions" autocomplete="off" name="q" >
        
        <label >
            <input type="submit" class="bt" value="">
            


<svg  data-name="search" viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M1216 832q0-185-131.5-316.5t-316.5-131.5-316.5 131.5-131.5 316.5 131.5 316.5 316.5 131.5 316.5-131.5 131.5-316.5zm512 832q0 52-38 90t-90 38q-54 0-90-38l-343-342q-179 124-399 124-143 0-273.5-55.5t-225-150-150-225-55.5-273.5 55.5-273.5 150-225 225-150 273.5-55.5 273.5 55.5 225 150 150 225 55.5 273.5q0 220-124 399l343 343q37 37 37 90z"/></svg>
        </label>
    </form>
        </li>
        
        <li class="highlight"><a href="https://sourceforge.net/auth/">Join/Login</a></li>
        
        <li><a href="/software/">Business Software</a></li>
        <li><a href="/directory/">Open Source Software</a></li>
        <li><a  href="/software/vendors/" title="For Vendors">For Vendors</a></li>
        <li><a href="/blog/" title="Blog">Blog</a></li>
        <li><a href="/about">About</a></li>
        <li><a id="header-nav-more" data-toggle="header-nav-more header-nav-more-content" data-toggler=".toggled">More</a></li>
        <li>
            <ul id="header-nav-more-content" class="toggled" data-toggler=".toggled">
                
    

    
    
        <li><a href="/articles/">Articles</a></li>
    
    

    
 
                
                <li><a href="/create">Create</a></li>
                
                <li><a href="https://sourceforge.net/articles/category/sourceforge-podcast/">SourceForge Podcast</a></li>
                
                    <li><a href="https://sourceforge.net/p/forge/documentation/Docs%20Home/">Site Documentation</a></li>
                
                <li><a href="/user/newsletters">Subscribe to our Newsletter</a></li>
                <li><a href="/support">Support Request</a></li>
            </ul>
        </li>
    </ul>
</div>

        <div class="off-canvas-content" data-off-canvas-content>
            
                


<script>
    SF.linkout_icon = '<svg  data-name="sf-linkout-icon" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 300" style="enable-background:new 0 0 300 300;" xml:space="preserve"><polygon class="st0" points="243.2,243.2 56.8,243.2 56.8,56.8 123,56.8 123,9 9,9 9,291 291,291 291,179.4 243.2,179 "/><polygon class="st0" points="128.5,213 155,186.5 176,165.5 206.7,196.3 235.5,132.5 248.9,102.6 290.6,9.8 291,9 290.6,9.2 197.4,51.1 169.1,63.8 103.7,93.3 137,126.5 115.9,147.5 89.5,174 "/></svg>';
</script>


    <section class="sandiego l-header-nav-top show-for-large">
        <div class="row">
            <a href="/" title="Home" class="sf-logo">
                
    
    <img src="//a.fsdn.com/con/images/sandiego/sf-logo-full.svg"  alt="SourceForge logo" class="sf-logo-full"/>
            </a>
            <nav class="links">
                
                    <a href="/user/newsletters" title="Subscribe to our newsletter"><span class="newsletter-icon">


<svg  data-name="mmSF_11mail" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 42 42" enable-background="new 0 0 42 42" xml:space="preserve"><path fill="#FFFFFF" d="M0,6v30h42V6H0z M24.2,21.2c-0.8,0.8-2.3,2-3.2,2c-0.9,0-2.4-1.2-3.2-2L5.8,9.7h30.3L24.2,21.2z M13.7,21l-9.9,9.4V11.6L13.7,21z M15.7,23L15.7,23c0.5,0.5,2.9,2.9,5.3,2.9c2.4,0,4.8-2.4,5.2-2.8l0.1-0.1l9.8,9.4H5.8L15.7,23z M28.3,21l9.9-9.5v18.9L28.3,21z"/></svg></span></a>
                
                <a href="/software/vendors/" title="For Vendors">For Vendors</a>
                
                
                    <a href="/support" title="Help">Help</a>
                    <a href="/create/" title="Create">Create</a>
                

                
                <a href="/user/registration" title="Join" >Join</a>
                <a href="https://sourceforge.net/auth/" title="Login">Login</a>
                
            </nav>
        </div>
    </section>

<div class="l-header-nav sticky sandiego l-header-nav-collapse">

    <section class="sandiego l-header-nav-top hide-for-large">
        <div class="row">
            <a href="/" title="Home" class="sf-logo">
                
    
    <img src="//a.fsdn.com/con/images/sandiego/sf-logo-full.svg"  alt="SourceForge logo" class="sf-logo-full"/>
            </a>
            <div class="title-bar-right">
                <button type="button" aria-label="Toggle Main Menu" class="menu-icon" data-toggle="offCanvas"></button>
            </div>
        </div>
    </section>
    <section class="sandiego l-header-nav-bottom">
        <nav class="row">
            <a href="/" title="Home" class="sf-logo">
                
    
    <img src="//a.fsdn.com/con/images/sandiego/sf-logo-full.svg"  alt="SourceForge logo" class="sf-logo-full"/>
            </a>
            <div class="links">
                
    
        <div class="nav-dropdown">
            <a href="/software/">Business Software</a>
            
        </div>
        <div class="nav-dropdown">
            <a href="/directory/" title="Browse">Open Source Software</a>
            
        </div>
        <div class="nav-dropdown">
            <a href="https://sourceforge.net/articles/category/sourceforge-podcast/">SourceForge Podcast</a>
        </div>
        <div class="nav-dropdown">
            <a>Resources</a>
            <ul class="nav-dropdown-menu">
                  <li><a href="/articles/">Articles</a></li>
                  
                  <li><a href="/software/case-studies/">Case Studies</a></li>
                  
                  <li><a href="/blog/">Blog</a></li>
            </ul>
        </div>
    

                <div class="dev-menu-when-stuck">
                    Menu
                    <ul class="dev-menu-dropdown header-nav-menulist">
                        <li><a href="/support">Help</a></li>
                        <li><a href="/create">Create</a></li>
                        <li><a href="/user/registration/" title="Join" >Join</a></li>
                        <li><a href="https://sourceforge.net/auth/" title="Login">Login</a></li>
                    </ul>
                </div>
                <div class="search-toggle-when-stuck">
                    <a class="search-toggle">
                        


<svg  data-name="search" viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M1216 832q0-185-131.5-316.5t-316.5-131.5-316.5 131.5-131.5 316.5 131.5 316.5 316.5 131.5 316.5-131.5 131.5-316.5zm512 832q0 52-38 90t-90 38q-54 0-90-38l-343-342q-179 124-399 124-143 0-273.5-55.5t-225-150-150-225-55.5-273.5 55.5-273.5 150-225 225-150 273.5-55.5 273.5 55.5 225 150 150 225 55.5 273.5q0 220-124 399l343 343q37 37 37 90z"/></svg>
                    </a>
                </div>
            </div>

            <div class="search">
                
                
    
    
        <div class="main-nav-link">
            
            
            <a href="https://nexo.com/fixed-term-savings?utm_source=sourceforge&amp;utm_medium=fixed&amp;utm_campaign=sourceforge_mb_sponsorship_earn_q225" rel="nofollow" target="_blank" id="main-nav-badge-link" data-label="nexo-april">
                <img src="//a.fsdn.com/con/assets/maxnav/sourceforge/banner-300x32-cdfdb6c2.png"  srcset="//a.fsdn.com/con/assets/maxnav/sourceforge/banner-600x64-cdfe1c16.png 2x"  alt="" id="main-nav-image"/>
            </a>
        </div>
    


                
                    
                
                
                
                
    <form method="get" action="/directory/" class="m-search-form">
    
    <div class="typeahead__container">
      <div class="typeahead__field">
        <div class="typeahead__query">
        
        <input type="text" placeholder="Search for software or solutions" autocomplete="off" name="q" >
        
        </div>
        
        <label >
            <input type="submit" class="bt" value="">
            


<svg  data-name="search" viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M1216 832q0-185-131.5-316.5t-316.5-131.5-316.5 131.5-131.5 316.5 131.5 316.5 316.5 131.5 316.5-131.5 131.5-316.5zm512 832q0 52-38 90t-90 38q-54 0-90-38l-343-342q-179 124-399 124-143 0-273.5-55.5t-225-150-150-225-55.5-273.5 55.5-273.5 150-225 225-150 273.5-55.5 273.5 55.5 225 150 150 225 55.5 273.5q0 220-124 399l343 343q37 37 37 90z"/></svg>
        </label>
      </div>
    </div>
    
    </form>
                
            </div>
        </nav>
        
    </section>
    <div id="banner-sterling" class="sterling">
        
        
        


    


<div id="SF_ProjectFiles_728x90_A_wrapped" data-id="div-gpt-ad-1393435113147-0" class="draper   
visibility_rules
 v_970_billboard  v_728_leaderboard "> 
        
        
        
    

    <ins class="adsbygoogle" id="adsense-6713379559"
        
        style="display:inline-block;width:728px;height:90px"
        data-ad-client="ca-pub-6471043051383295"
        data-ad-slot="6713379559"></ins>
    <script>
        if (! SF.initial_breakpoints_visible.leaderboard) {
            document.getElementById('adsense-6713379559').style="display: none;";
        } else {
            $('#SF_ProjectFiles_728x90_A_wrapped').addClass('delivered').append('<span class="lbl-ad"></span>');
        }
        SF.Ads.skipStickyAds = true;
        (adsbygoogle = window.adsbygoogle || []).push({});  // jshint ignore:line
    </script></div>
        


    


<div id="SF_Mobile_320x50_A_wrapped" data-id="div-gpt-ad-1512154506943-0" class="draper   
"> 
        
        
    

    <ins class="adsbygoogle" id="adsense-6347150683"
        
        style="display:inline-block;width:320px;height:50px"
        data-ad-client="ca-pub-6471043051383295"
        data-ad-slot="6347150683"></ins>
    <script>
        if (! !SF.initial_breakpoints_visible.leaderboard) {
            document.getElementById('adsense-6347150683').style="display: none;";
        } else {
            $('#SF_Mobile_320x50_A_wrapped').addClass('delivered').append('<span class="lbl-ad"></span>');
        }
        SF.Ads.skipStickyAds = true;
        (adsbygoogle = window.adsbygoogle || []).push({});  // jshint ignore:line
    </script></div>
    </div>
</div>
            

            
                

                
                <div class="l-two-column-page">
                    <div class="l-content-column l-has-sidebar">
                        


<svg xmlns="http://www.w3.org/2000/svg" style="display:none">
<defs>

    
<symbol id="folder-o" viewBox="0 0 200.2 149.8"><g><g><path style="fill: #FFF" class="st0" d="M189,145.3c-29.5-0.1-59.4-0.1-88.9-0.1s-59.4,0-88.9,0.1c-4.4,0-5.8-1-6.7-3.1V7.6c1-2.3,2.3-3.1,5-3.1c6.3,0,12.9,0.1,20.9,0.1c6,0,11.9,0,17.9,0h7c2.3,0,3.9,0.7,5.4,2.5c2.1,2.5,4.3,5.1,6.4,7.5c2.7,3.1,5.6,6.4,8.2,9.7C78.4,28,82,29.7,87,29.7c18.4-0.1,37.1-0.1,55.1-0.1c15.4,0,31.3,0,46.9-0.1c4.4,0,5.9,1,6.7,3.1v109.5C194.8,144.2,193.4,145.3,189,145.3L189,145.3z"/><g><path d="M11.9,10.7L11.9,10.7c6.1,0,12.6,0.1,20.4,0.1c5.8,0,11.6,0,17.4,0h6.8c1,0,1.3,0.2,1.9,0.8c2.1,2.5,4.3,5,6.3,7.4c2.7,3.2,5.4,6.1,7.9,9.3c3.8,4.7,8.7,7,14.8,7h0.1c17.8-0.1,36-0.1,53.6-0.1c15,0,30.5,0,45.7-0.1h0.1c1.1,0,1.7,0.1,2,0.2v103.6c-0.4,0.1-1.1,0.2-2.1,0.2c-28.7-0.1-57.9-0.1-86.6-0.1s-57.9,0-86.7,0.1c-1.1,0-1.8-0.1-2.1-0.2V10.7C11.5,10.7,11.7,10.7,11.9,10.7 M11.9,1.9c-4.8,0-7.7,2.2-9.3,6.6c0,44.2,0,88.3,0,132.6c1.8,5.3,5.6,6.7,10.9,6.7c0,0,0,0,0.1,0c28.8-0.1,57.8-0.1,86.6-0.1s57.8,0,86.6,0.1c0,0,0,0,0.1,0c5.3,0,9.1-1.4,10.9-6.7c0-36,0-72.1,0-108.2c-1.8-5.3-5.6-6.6-10.8-6.6h-0.1c-33.1,0.1-66.1,0-99.2,0.1h-0.1c-3.5,0-5.8-1.1-8-3.8C74.8,17,69.8,11.6,65,5.9c-2.2-2.7-5.1-4-8.6-4l0,0c-8.1,0-16.2,0-24.3,0C25.5,2,18.7,1.9,11.9,1.9C12,1.9,12,1.9,11.9,1.9L11.9,1.9z"/><path d="M186.9,149.8L186.9,149.8c-28.9-0.1-58.3-0.1-86.7-0.1s-57.8,0-86.6,0.1h-0.1c-4.3,0-10.4-0.8-12.8-8.1l-0.1-0.3V8.2l0.1-0.3C2.6,2.7,6.4,0,11.9,0H12c2.2,0,4.5,0,6.7,0c4.4,0,9,0,13.5,0h24.3c4.1,0,7.5,1.6,10.1,4.7c2.7,3.2,5.5,6.3,8.2,9.4c2.1,2.4,4.2,4.8,6.3,7.2c1.8,2.3,3.6,3.1,6.5,3.1c16.7,0,33.5,0,49.7,0c16.3,0,33.1,0,49.6,0h0.1c4.3,0,10.3,0.8,12.7,8l0.1,0.3v108.9l-0.1,0.3C197.2,148.9,191.5,149.8,186.9,149.8z M100.2,145.8c28.4,0,57.8,0,86.6,0.1h0.1c5.1,0,7.6-1.4,9-5.1V33.2c-1.3-3.6-3.8-5-8.9-5c-16.7,0-33.5,0-49.7,0c-16.3,0-33.1,0-49.6,0h-0.1c-4.2,0-7-1.4-9.5-4.5c-2-2.3-4.1-4.7-6.2-7.1c-2.7-3.1-5.5-6.3-8.2-9.5c-1.9-2.3-4.1-3.3-7.1-3.3H32.2c-4.5,0.1-9.1,0-13.5,0c-2.2,0-4.5,0-6.7,0h-0.1c-3.7,0-6,1.6-7.3,5v131.9c1.3,3.7,3.9,5.1,9,5.1C42.4,145.8,71.8,145.8,100.2,145.8z M186.7,141c-28.7-0.1-58.1-0.1-86.6-0.1c-28.4,0-57.9,0-86.7,0.1c-1.1,0-2-0.1-2.6-0.3l-1.5-0.4V8.8h2.5c2.9,0,5.8,0,9,0c3.5,0,7.3,0.1,11.4,0.1h24.3c1.6,0,2.4,0.5,3.2,1.3l0.1,0.1c1,1.2,2,2.3,3,3.5c1.1,1.3,2.3,2.6,3.4,3.9c1,1.2,2,2.3,3,3.4c1.7,2,3.3,3.8,5,5.8c3.4,4.2,7.8,6.3,13.3,6.3h0.1c17.8-0.1,36-0.1,53.6-0.1c15,0,30.5,0,45.7-0.1h0.1c1,0,1.8,0.1,2.5,0.3l1.5,0.4v106.7l-1.5,0.4C188.6,140.9,187.8,141,186.7,141z M100.1,137c28.4,0,57.9,0,86.6,0.1c0.1,0,0.1,0,0.2,0V37c0,0-0.1,0-0.1,0c-15.3,0.1-30.8,0.1-45.8,0.1c-17.6,0-35.7,0-53.6,0.1h-0.1c-6.7,0-12.1-2.6-16.3-7.7c-1.6-1.9-3.2-3.8-4.9-5.7c-1-1.1-2-2.3-3-3.5c-1.1-1.3-2.2-2.6-3.3-3.9c-1-1.1-2-2.3-2.9-3.4c-0.1-0.1-0.1-0.1-0.1-0.1c0,0-0.1,0-0.3,0H32.2c-4.1,0-7.9,0-11.4-0.1c-2.6,0-5.1,0-7.6,0v124.5c0.1,0,0.1,0,0.2,0C42.2,137,71.7,137,100.1,137z"/></g></g></g></symbol>

    
<symbol id="info-circle" viewBox="0 0 200.2 200"><circle class="st0" style="fill: #fff" cx="99.8" cy="100" r="89.8"/><g><path style="fill:#000;" class="foo-bar" d="M109,0.6c24.2,2.2,45.1,11.7,62,28.9c22.5,22.9,32.1,50.6,28.4,82.5c-3.1,26.9-15.5,49.4-37,65.8c-32.9,25.1-69,29.1-106.3,11.3c-30-14.3-47.8-39-54.2-71.7c-0.7-3.3-1-6.7-1.5-10.1c-0.5-4.7-0.5-9.6,0-14.8C0.7,90.3,1,88.1,1.3,86c3.4-23,13.5-42.6,30.4-58.5C46,13.8,63,5.3,82.5,1.8C86,1.2,93,0.1,98.6,0.1C104.6,0.1,109,0.6,109,0.6z M183,99.9c-0.2-46.1-37.4-82.9-84-82.4C53.8,18,17.1,55.1,17.7,101c0.6,45.2,37.7,81.9,83.7,81.3C146.6,181.6,182.7,145,183,99.9z"/><path style="fill:#000;" d="M107,75c9.2,0,13.8,5.6,11.4,15.1c-1.7,6.9-4.4,13.6-6.8,20.4c-2.9,8.1-6,16.2-9,24.3c-0.6,1.6-1,3.3-1.2,4.9c-0.1,0.9,0.3,2.2,0.9,2.6c0.6,0.4,2.1,0.4,2.7-0.1c2.5-1.8,4.8-3.8,7-6c2.3-2.3,4.3-4.8,6.6-7.1c0.5-0.5,1.7-1.2,2.4-0.9c0.1,0.1,0.2,0.2,0.3,0.3c0.6,0.7,0.9,1.7,1,2.6c0.1,0.8-0.5,1.8-1,2.5c-7.8,11-17.8,19.3-31,22.9c-3,0.8-6.3,0.7-9.4,0.3c-4.1-0.5-7.4-4.3-7.2-8.4c0.2-4.5,1.1-8.6,2.6-12.8c4.5-12.6,9.4-25.1,14.2-37.6c0.9-2.3,2-4.5,2.6-6.9c0.3-1.2,0.2-3.1-0.5-3.9c-0.5-0.6-2.8-0.3-3.7,0.3c-2.3,1.6-4.3,3.5-6.3,5.4c-2.3,2.4-4.4,5-6.6,7.4c-0.9,1-2.1,2.1-3.6,0.9c-1.5-1.1-1.2-2.6-0.3-3.9c8-11.1,17.5-20.2,31.9-22.3c0.3,0,0.6-0.1,0.9-0.1C105.6,75,106.3,75,107,75z"/><path style="fill:#000;" d="M128.9,54.8c0,9.9-10.3,16.8-19.5,13.1c-5.4-2.2-8.1-8.5-6.3-14.8c2.1-7.3,9.7-11.7,17.1-10C125.6,44.4,128.9,48.8,128.9,54.8z"/></g></symbol>

    
<symbol id="info-circle-active" viewBox="0 0 200.2 200"><g><path d="M109,0.6c24.2,2.2,45.1,11.7,62,28.9c22.5,22.9,32.1,50.6,28.4,82.5c-3.1,26.9-15.5,49.4-37,65.8c-32.9,25.1-69,29.1-106.3,11.3c-30-14.3-47.8-39-54.2-71.7c-0.7-3.3-1-6.7-1.5-10.1c-0.5-4.7-0.5-9.6,0-14.8C0.7,90.3,1,88.1,1.3,86c3.4-23,13.5-42.6,30.4-58.5C46,13.8,63,5.3,82.5,1.8C86,1.2,93,0.1,98.6,0.1C104.6,0.1,109,0.6,109,0.6z"/><path class="st0" d="M107,75c9.2,0,13.8,5.6,11.4,15.1c-1.7,6.9-4.4,13.6-6.8,20.4c-2.9,8.1-6,16.2-9,24.3c-0.6,1.6-1,3.3-1.2,4.9c-0.1,0.9,0.3,2.2,0.9,2.6c0.6,0.4,2.1,0.4,2.7-0.1c2.5-1.8,4.8-3.8,7-6c2.3-2.3,4.3-4.8,6.6-7.1c0.5-0.5,1.7-1.2,2.4-0.9c0.1,0.1,0.2,0.2,0.3,0.3c0.6,0.7,0.9,1.7,1,2.6c0.1,0.8-0.5,1.8-1,2.5c-7.8,11-17.8,19.3-31,22.9c-3,0.8-6.3,0.7-9.4,0.3c-4.1-0.5-7.4-4.3-7.2-8.4c0.2-4.5,1.1-8.6,2.6-12.8c4.5-12.6,9.4-25.1,14.2-37.6c0.9-2.3,2-4.5,2.6-6.9c0.3-1.2,0.2-3.1-0.5-3.9c-0.5-0.6-2.8-0.3-3.7,0.3c-2.3,1.6-4.3,3.5-6.3,5.4c-2.3,2.4-4.4,5-6.6,7.4c-0.9,1-2.1,2.1-3.6,0.9c-1.5-1.1-1.2-2.6-0.3-3.9c8-11.1,17.5-20.2,31.9-22.3c0.3,0,0.6-0.1,0.9-0.1C105.6,75,106.3,75,107,75z"/><path class="st0" d="M128.9,54.8c0,9.9-10.3,16.8-19.5,13.1c-5.4-2.2-8.1-8.5-6.3-14.8c2.1-7.3,9.7-11.7,17.1-10C125.6,44.4,128.9,48.8,128.9,54.8z"/></g></symbol>

    
<symbol id="trash" viewBox="0 0 157 200"><polygon class="st0" style="fill:#FFFFFF;stroke:#000000;stroke-miterlimit:10;" points="26.2,54.2 130.7,54.2 120.7,188.8 36.8,188.8 "/><g><g><path d="M24.4,200c-0.5,0-0.9-0.4-1-0.9L14.7,60.3c0-0.5-0.5-0.9-1-0.9H6.6c-0.5,0-6.6-0.1-6.6-8.9v-7.9c0-0.5,0.4-0.9,0.9-0.9h155.2c0.5,0,0.9,0.4,0.9,0.9v7.9c0,9.2-8.3,8.7-8.8,8.7h-4.9c-0.5,0-0.9,0.4-1,0.9l-8.7,139c0,0.5-0.5,0.9-1,0.9H24.4z M83.1,181.6c0.5,0,0.9-0.4,0.9-0.9l2.5-119.7c0-0.5-0.4-0.9-0.9-0.9H71.3c-0.5,0-0.9,0.4-0.9,0.9l2.5,119.7c0,0.5,0.4,0.9,0.9,0.9H83.1z M40.6,180.6c0,0.5,0.5,0.9,1,0.9h9.3c0.5,0,0.9-0.4,0.9-0.9L49.2,60.9c0-0.5-0.4-0.9-0.9-0.9h-5.6c-10.8,0-8.8,18.1-8.8,15.5L40.6,180.6z M115.5,181.6c0.5,0,0.9-0.4,1-0.9l6.5-108c0-13.4-8.4-12.7-8.9-12.7h-5.3c-0.5,0-0.9,0.4-0.9,0.9l-2.5,119.7c0,0.5,0.4,0.9,0.9,0.9H115.5z"/></g><g><path d="M91.6,0c0.5,0,0.9,0.4,0.9,0.9v11.7c0,0.5,0.4,0.9,0.9,0.9h41.2c0.5,0,8.6,0.5,8.6,7.8v9.2c0,0.5-0.4,0.9-0.9,0.9H14.8c-0.5,0-0.9-0.4-0.9-0.9v-9.2c0-8.2,9.3-7.8,9.8-7.8H63c0.5,0,0.9-0.4,0.9-0.9l0.5-11.7c0-0.5,0.4-0.9,0.9-0.9L91.6,0z"/></g></g></symbol>

    
<symbol id="symlink" viewBox="0 0 12 16"> <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"> <g fill="#000000"> <path d="M8.5,1 L1,1 C0.45,1 0,1.45 0,2 L0,14 C0,14.55 0.45,15 1,15 L11,15 C11.55,15 12,14.55 12,14 L12,4.5 L8.5,1 L8.5,1 Z M11,14 L1,14 L1,2 L8,2 L11,5 L11,14 L11,14 Z M6,4.5 L10,7.5 L6,10.5 L6,8.5 C5.02,8.48 4.16,8.72 3.45,9.2 C2.74,9.68 2.26,10.45 2,11.5 C2.02,9.86 2.39,8.62 3.13,7.77 C3.86,6.93 4.82,6.5 6.01,6.5 L6.01,4.5 L6,4.5 Z" ></path> </g> </g></symbol>

    
<symbol id="ban" viewBox="0 0 1792 1792"><path d="M1440 893q0-161-87-295l-754 753q137 89 297 89 111 0 211.5-43.5t173.5-116.5 116-174.5 43-212.5zm-999 299l755-754q-135-91-300-91-148 0-273 73t-198 199-73 274q0 162 89 299zm1223-299q0 157-61 300t-163.5 246-245 164-298.5 61-298.5-61-245-164-163.5-246-61-300 61-299.5 163.5-245.5 245-164 298.5-61 298.5 61 245 164 163.5 245.5 61 299.5z"/></symbol>

    
<symbol id="sort" viewBox="0 0 1792 1792"><path d="M1408 1088q0 26-19 45l-448 448q-19 19-45 19t-45-19l-448-448q-19-19-19-45t19-45 45-19h896q26 0 45 19t19 45zm0-384q0 26-19 45t-45 19h-896q-26 0-45-19t-19-45 19-45l448-448q19-19 45-19t45 19l448 448q19 19 19 45z"/></symbol>

    
<symbol id="sort_up" viewBox="0 0 1792 1792"><path d="M1408 704q0 26-19 45t-45 19h-896q-26 0-45-19t-19-45 19-45l448-448q19-19 45-19t45 19l448 448q19 19 19 45"/></symbol>

    
<symbol id="sort_down" viewBox="0 0 1792 1792"><path d="M1408 1088q0 26-19 45l-448 448q-19 19-45 19t-45-19l-448-448q-19-19-19-45t19-45 45-19h896q26 0 45 19t19 45z"/></symbol>

</defs>
</svg>


<div class="project-body">



<div class="project-body">
    
    
    

    
    

    
<section class="project-masthead"> 
    
    

    
<div class="backdrop" style="box-sizing: content-box; padding-bottom: 24px"></div>

    <div class="content">
    
        
    <nav id="breadcrumbs" class="breadcrumbs rtl">
        <ul itemscope itemtype="http://schema.org/BreadcrumbList">
            
            <li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
            <a itemprop="item" href="/"><span itemprop="name">Home</span></a>
            <meta itemprop="position" content="1" />
            </li>
            
            <li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
                <a itemprop="item" href="/directory/"><span itemprop="name">Open Source Software</span></a>
                <meta itemprop="position" content="2" />
            </li>
            <li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
                <a itemprop="item" href="/directory/software-development/"><span itemprop="name">Software Development</span></a>
                <meta itemprop="position" content="3" />
            </li><li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
                <a itemprop="item" href="/directory/usability/"><span itemprop="name">Usability</span></a>
                <meta itemprop="position" content="4" />
            </li><li class="project" itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem"><a itemprop="item" href="/projects/phpqrcode/"><span itemprop="name">PHP QR Code</span></a><meta itemprop="position" content="5" />
            </li>
            
              <li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
                  <span itemprop="name">Files</span>
                  <meta itemprop="position" content="6" />
              </li>
            
        </ul>
    </nav>

    
    
    
        
                
                
<div class="overview">
    


<div class="project-icon   " >
    
    
    <img itemprop="image" alt="PHP QR Code" title="PHP QR Code" 
src="//a.fsdn.com/allura/p/phpqrcode/icon?1376470788"
    /></div>


    
    <div class="title "> 

        <meta itemprop="name" content="PHP QR Code"/>
        <h1  >PHP QR Code Files
        </h1>
        
        <h2 class="as-h3 summary">
            Create QR Codes in PHP
        </h2>
         
          
        
            
            
            <div class="as-h3 brought-by">
                
                Brought to you by:
                
                    <a href="/u/deltalab/profile/">deltalab</a>
                    
                
            </div>
            
        

        

        
    </div>


    

</div>

                
                  
    
    
            
            
        
    </div>
</section>


    
        
    <div id="top_nav"><div id="top_nav_admin">
        <ul class="dropdown">
            
            <li >
                <a href="/projects/phpqrcode/"
                >
                <span>Summary</span></a>
                
            </li>
            
            <li class="selected">
                <a href="/projects/phpqrcode/files/"
                >
                <span>Files</span></a>
                
            </li>
            
            <li >
                <a href="/projects/phpqrcode/reviews/"
                >
                <span>Reviews</span></a>
                
            </li>
            
            <li >
                <a href="/projects/phpqrcode/support"
                >
                <span>Support</span></a>
                
            </li>
            
            <li >
                <a href="/p/phpqrcode/code/"
                >
                <span>Code (SVN)</span></a>
                
            </li>
            
            <li >
                <a href="/p/phpqrcode/wiki/"
                >
                <span>Wiki</span></a>
                
            </li>
            
            <li >
                <a href="/p/phpqrcode/_list/tickets"
                >
                <span>Tickets ▾</span></a>
                
                <ul>
                    
                    <li>
                        <a href="/p/phpqrcode/bugs/"
                            >Bugs
                        </a>
                    </li>
                    
                    <li>
                        <a href="/p/phpqrcode/support-requests/"
                            >Support Requests
                        </a>
                    </li>
                    
                    <li>
                        <a href="/p/phpqrcode/patches/"
                            >Patches
                        </a>
                    </li>
                    
                    <li>
                        <a href="/p/phpqrcode/feature-requests/"
                            >Feature Requests
                        </a>
                    </li>
                    
                </ul>
                
            </li>
            
            <li >
                <a href="/p/phpqrcode/news/"
                >
                <span>News</span></a>
                
            </li>
            
            <li >
                <a href="/p/phpqrcode/discussion/"
                >
                <span>Discussion</span></a>
                
            </li>
            
            <li >
                <a href="https://www.paypal.com/cgi-bin/webscr?item_name=Donation+to+PHP+QR+Code&amp;cmd=_donations&amp;business=deltalab%40poczta.fm"
                data-external=true rel=nofollow>
                <span>Donate</span></a>
                
            </li>
            
            <li >
                <a href="/p/phpqrcode/git/"
                >
                <span>Code (GIT)</span></a>
                
            </li>
            
            
        </ul>
        
    </div></div>
    
    

    <article class="main-content full-bleed">
        <section class="">
            <div class="">
                
    <noscript>
        <p>The interactive file manager requires Javascript. Please enable it or use <a href="https://sourceforge.net/p/forge/documentation/Release%20Files%20for%20Download#scp">sftp or scp</a>.
        <br/>You may still <em>browse</em> the files here.</p>
    </noscript>

    <div id="files" class="row">
      
        <div class="download-bar column small-12">
            <div class="files-messages">
            <ul class="warning">


                


            </ul>
            </div>
        </div>


        <div class="files-toolbar btn-bar column small-12">
            <div class="btn-set">
            
            <a class="button green big-text download with-sub-label extra-wide" href="/projects/phpqrcode/files/latest/download" title="/releases/phpqrcode-2010100721_1.1.4.zip:  released on 2010-10-07 19:46:26 UTC">
                <img src="//a.fsdn.com/con/images/sandiego/sf-icon-black.svg"  alt="" class="sf-download-icon" />
                <span class="label">Download Latest Version</span>
                <span class="sub-label">phpqrcode-2010100721_1.1.4.zip (223.6 kB)</span>
            </a>
            <div id="get-updates">
    <img id="psp_newsletter_subscribe-icon" src="//a.fsdn.com/con/images/sandiego/sf_email_icon.svg"  alt="Email in envelope"  />
    
        <h4>Get an email when there's a new version of PHP QR Code</h4>
        <div id="get-updates-form">
            <form>
                <input type="email" name="email" placeholder="Enter your email address" value="">
                <a id="get-updates-button" class="button blue" data-open="psp-newsletter-modal">Next</a>
            </form>
        </div>
    
    </div>
    <div class="psp_newsletter_subscribe reveal" data-reveal id="psp-newsletter-modal" data-v-offset="0" data-ajax-url="/projects/phpqrcode/get_updates?source=Files">

    </div>
    <script>

    $('#get-updates-form input[type="email"]').on('focus', function(){
        if(SF.downloader) {
            SF.downloader.cancelRedirect();
        }
        SF.noRedirect =  true;
    });
    $('#get-updates-button').click(function(){
        if(SF.downloader) {
            SF.downloader.cancelRedirect();
        }
        SF.noRedirect =  true;
    });

    </script>
            </div>
            <span class="actions"><a href="/projects/phpqrcode/rss?path=/" class="button blue icon-only hollow rss" title="RSS feed for files" rel="nofollow">
<svg  data-name="feed" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1408.1818 1408" > <path d="m 384,1216 q 0,80 -56,136 -56,56 -136,56 -80,0 -136,-56 -56,-56 -56,-136 0,-80 56,-136 56,-56 136,-56 80,0 136,56 56,56 56,136 z m 512,123 q 2,28 -17,48 -18,21 -47,21 l -135,0 q -25,0 -43,-16.5 Q 636,1375 634,1350 612,1121 449.5,958.5 287,796 58,774 33,772 16.5,754 0,736 0,711 L 0,576 q 0,-29 21,-47 17,-17 43,-17 l 5,0 q 160,13 306,80.5 146,67.5 259,181.5 114,113 181.5,259 67.5,146 80.5,306 z m 512,2 q 2,27 -18,47 -18,20 -46,20 l -143,0 q -26,0 -44.5,-17.5 Q 1138,1373 1137,1348 1125,1133 1036,939.5 947,746 804.5,603.5 662,461 468.5,372 275,283 60,270 35,269 17.5,250.5 0,232 0,207 L 0,64 Q 0,36 20,18 38,0 64,0 l 3,0 Q 329,13 568.5,120 808,227 994,414 q 187,186 294,425.5 107,239.5 120,501.5 z" /></svg></a></span>
        </div>

        <div class="files-breadcrumb column small-12">
            
            Home
            
            
        </div>

        <table id="files_list">
            <col class="name-column">
            <col class="date-column">
            <col class="size-column">
            <col class="downloads-column">
            <thead>
                <tr>
                    <th title="The file or folder's name" id="files_name_h" class="first">Name</th>
                    <th title="The file or folder's last modified date" id="files_date_h" class="opt">Modified</th>
                    <th title="The file size" id="files_size_h" class="opt">Size</th>
                    <th title="The weekly download count" id="files_downloads_h" class="opt" ><span class="hide-for-medium">Info</span><span class="show-for-medium">Downloads / Week</span></th>
                </tr>
                
            </thead>
            <tbody>
                
                
                
                <tr title="releases" class="folder ">
                    <th scope="row" headers="files_name_h"><a href="/projects/phpqrcode/files/releases/"
                           title="Click to enter releases"><svg class="svgico"><use xlink:href="#folder-o"></use></svg>
                        <span class="name">releases</span></a>
                    </th>
                    <td headers="files_date_h" class="opt"><abbr title="2010-10-07 19:46:25 UTC">2010-10-07</abbr></td>
                    <td headers="files_size_h" class="opt"></td>
                    <td headers="files_downloads_h" class="opt">
                        <div class="status folder"><!-- populated by javascript --></div>
                        <div class="stats show-for-medium"><a href="/projects/phpqrcode/files/releases/stats/timeline" class="folder" title="1,564 weekly downloads" rel="nofollow">
                                    <span class="count">1,564</span><svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 18 14" class="fs-stats fs-sparklines folder">
    <title>1,564 weekly downloads</title>
    <polygon class=graph points="0,14 0.0,7.0 3.0,4.1 6.0,3.1 9.0,0.0 12.0,2.2 15.0,1.4 18.0,3.9 18,14" />
</svg></a></div>
                    </td>
                </tr>
            </tbody>
            <tfoot>
                <tr>
                    <td id="totals"><span class="label">Totals: </span>1 Item</td>
                    <td headers="files_date_h" class="opt">&nbsp;</td>
                    <td headers="files_size_h" class="opt"></td>
                    <td headers="files_downloads_h" class="opt " ><a href="/projects/phpqrcode/files/stats/timeline" rel="nofollow">1,564</a></td>
                </tr>
            </tfoot>
        </table>
        <div id="files-drawer" class="fs-widget fs-drawer consumer">
        </div>
    </div>

            </div>
        </section>
    </article>
</div>



    
        

<script>
    if (!SF.wireOutboundZoneTrackingComplete) {  
        $(SF.wireOutboundZoneTracking);
        $('body').append('<iframe src="https://c.sf-syn.com/conversion_outbound_tracker/sf" id="frame-zone-outbound" style="display: none;"></iframe>');
        SF.wireOutboundZoneTrackingComplete = true;
    }
</script>


        
            <div id="nels" class="small-12 columns nel-block">
                
                
    
    
    
    
     <div class="as-header">
        <div class="as-h2">Other Useful Business Software</div>
    </div>
    
    
        
        

    
    
        
    

    <div class="nel standard trunc-eligible  "
        data-id="19646" data-cid="15647">

        


<span id="24a60941-45a1-41cb-8b7b-97f8085600bc"></span>
<script>
    /* globals bizx */
    bizx.cmp.trackingPixel('publisher', ['storage', 'measurement'], '/directory/tp3/?b=81094&amp;c=15647&amp;z=78493&amp;cb=14e579a1cc', "ADVANCE Your Career: Unlimited Access to 10,000+ Courses", '24a60941-45a1-41cb-8b7b-97f8085600bc');
</script>


        
        <div class="application-image thumbnail"  data-dest="https://sourceforge.net/software/link?oaparams=2__bannerid=81094__zoneid=78493__cb=14e579a1cc__oadest=https%3A%2F%2Fimp.i384100.net%2Fc%2F160060%2F1320993%2F14726%3FsubId1%3Dcourseraplus" data-newtab="true" data-target="_blank" rel="nofollow">
            <img class="main-image" src="//a.fsdn.com/con/app/nel_img/19646" alt="ADVANCE Your Career: Unlimited Access to 10,000+ Courses Icon">
            
        </div>
        <div class="wrapper">
            <div class="heading">
                <div class="heading-main">
                    
                    <span data-dest="https://sourceforge.net/software/link?oaparams=2__bannerid=81094__zoneid=78493__cb=14e579a1cc__oadest=https%3A%2F%2Fimp.i384100.net%2Fc%2F160060%2F1320993%2F14726%3FsubId1%3Dcourseraplus" data-newtab="true" data-target="_blank" rel="nofollow" title="Find out more about ADVANCE Your Career: Unlimited Access to 10,000+ Courses">ADVANCE Your Career: Unlimited Access to 10,000+ Courses</span>

                    
                    <p class="teaser">Elevate your career with Coursera Plus. Access 10,000+ courses and certifications to help you grow professionally.</p>
                </div>
            </div>

            
            

            <div class="tiles">
                <div class="tile">
                    
                    <div class="description ">
                        <div class="description-inner">
                            With Coursera Plus, you gain unlimited access to 10,000+ courses, professional certificates, and skill-building programs from top universities and companies. Whether you’re aiming for a promotion, switching careers, or acquiring new skills, Coursera Plus offers the tools you need for career success. From business and technology to leadership and data science, get certified and boost your credentials at your own pace. Join today and start building the career you’ve always dreamed of.
                        </div>
                    </div>
                </div>

                
            </div>

        </div>

        
        <div class="download standard">
            
            

            

            
        
            
        
        <div class="button green wide sfdl sfdl-lite" data-target="_blank" data-newtab="true" data-dest="https://sourceforge.net/software/link?oaparams=2__bannerid=81094__zoneid=78493__cb=14e579a1cc__oadest=https%3A%2F%2Fimp.i384100.net%2Fc%2F160060%2F1320993%2F14726%3FsubId1%3Dcourseraplus"
                >Start for Free</div>
        </div>

        
    </div>


    
        
        

    
    
        
    

    <div class="nel standard trunc-eligible  "
        data-id="19651" data-cid="15653">

        


<span id="6c42bd9d-7056-498d-a49f-d6b04cc2f243"></span>
<script>
    /* globals bizx */
    bizx.cmp.trackingPixel('publisher', ['storage', 'measurement'], '/directory/tp3/?b=85061&amp;c=15653&amp;z=82460&amp;cb=99363eb71f', "Learn Generative AI and LLMs and Become an Expert TODAY", '6c42bd9d-7056-498d-a49f-d6b04cc2f243');
</script>


        
        <div class="application-image thumbnail"  data-dest="https://sourceforge.net/software/link?oaparams=2__bannerid=85061__zoneid=82460__cb=99363eb71f__oadest=https%3A%2F%2Fimp.i384100.net%2Fc%2F160060%2F2807178%2F14726" data-newtab="true" data-target="_blank" rel="nofollow">
            <img class="main-image" src="//a.fsdn.com/con/app/nel_img/19651" alt="Learn Generative AI and LLMs and Become an Expert TODAY Icon">
            
        </div>
        <div class="wrapper">
            <div class="heading">
                <div class="heading-main">
                    
                    <span data-dest="https://sourceforge.net/software/link?oaparams=2__bannerid=85061__zoneid=82460__cb=99363eb71f__oadest=https%3A%2F%2Fimp.i384100.net%2Fc%2F160060%2F2807178%2F14726" data-newtab="true" data-target="_blank" rel="nofollow" title="Find out more about Learn Generative AI and LLMs and Become an Expert TODAY">Learn Generative AI and LLMs and Become an Expert TODAY</span>

                    
                    <p class="teaser">Advance your career and learn how to build and deploy generative AI models with this expert-led course. Start mastering AI today!</p>
                </div>
            </div>

            
            

            <div class="tiles">
                <div class="tile">
                    
                    <div class="description ">
                        <div class="description-inner">
                            In today’s world, you MUST know how to use AI. Dive into the world of generative AI with the “Generative AI with LLMs” course on Coursera. Learn from top experts how to build, train, and deploy Large Language Models (LLMs) for practical applications. Whether you’re new to AI or looking to expand your expertise, this course offers in-depth knowledge and hands-on experience. Unlock the potential of cutting-edge technology to enhance your career.
                        </div>
                    </div>
                </div>

                
            </div>

        </div>

        
        <div class="download standard">
            
            

            

            
        
            
        
        <div class="button green wide sfdl sfdl-lite" data-target="_blank" data-newtab="true" data-dest="https://sourceforge.net/software/link?oaparams=2__bannerid=85061__zoneid=82460__cb=99363eb71f__oadest=https%3A%2F%2Fimp.i384100.net%2Fc%2F160060%2F2807178%2F14726"
                >Enroll Free Today</div>
        </div>

        
    </div>


    
            </div>
        
    


</div>

                    </div>
                    <div class="l-gutter">
                    </div>
                    <div class="l-side-column">
                        

<div id="files-sidebar" class="scroll-fixable" data-floor-compensate="145">
    <div class="sterling">
    


    
    


<div id="SF_ProjectFiles_300x250_A_wrapped" data-id="div-gpt-ad-1392147725721-0" class="draper multisize  
visibility_rules
 v_300_large "> 
        
        
    

    <ins class="adsbygoogle" id="adsense-4440346108"
        
        style="display:inline-block;width:300px;height:250px"
        data-ad-client="ca-pub-6471043051383295"
        data-ad-slot="4440346108"></ins>
    <script>
        if (! SF.initial_breakpoints_visible.large) {
            document.getElementById('adsense-4440346108').style="display: none;";
        } else {
            $('#SF_ProjectFiles_300x250_A_wrapped').addClass('delivered').append('<span class="lbl-ad"></span>');
        }
        SF.Ads.skipStickyAds = true;
        (adsbygoogle = window.adsbygoogle || []).push({});  // jshint ignore:line
    </script></div>
    


    
    


<div id="SF_Mobile_Multi_B_wrapped" data-id="div-gpt-ad-1512154653435-0" class="draper multisize  
"> 
        
        
    

    <ins class="adsbygoogle" id="adsense-2854095404"
        
        style="display:inline-block;width:300px;height:50px"
        data-ad-client="ca-pub-6471043051383295"
        data-ad-slot="2854095404"></ins>
    <script>
        if (! !SF.initial_breakpoints_visible.large) {
            document.getElementById('adsense-2854095404').style="display: none;";
        } else {
            $('#SF_Mobile_Multi_B_wrapped').addClass('delivered').append('<span class="lbl-ad"></span>');
        }
        SF.Ads.skipStickyAds = true;
        (adsbygoogle = window.adsbygoogle || []).push({});  // jshint ignore:line
    </script></div>
    </div>
            
<aside class="m-sidebar-widget m-project-list">
    <div class="as-header">Recommended Projects</div>

    <div class="body">
        <ul >
            
            <li class="item">
                
                
                <a href="/projects/phpqrclass/" title="PHP QR Code Class">
                    


<div class="project-icon  default-project-icon " >
    
    
    


<svg  data-name="default-icon" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 300" style="enable-background:new 0 0 300 300;" xml:space="preserve"><rect class="st0" width="300" height="300"/><g><path class="st1" d="M162.5,154.8c0-28.9-10.2-42-15.6-46.9c-1.1-1-2.7-0.1-2.6,1.3c1.1,16.3-19.4,20.3-19.4,45.9h0c0,0.1,0,0.1,0,0.2c0,15.6,11.8,28.3,26.3,28.3c14.5,0,26.3-12.7,26.3-28.3c0-0.1,0-0.1,0-0.2h0c0-7.2-2.7-14.1-5.5-19.3c-0.5-1-2.1-0.6-1.9,0.3C174.9,158.1,162.5,171.8,162.5,154.8z"/><g><path class="st1" d="M131.2,215.6c-0.7,0-1.3-0.3-1.8-0.7l-67.2-67.1c-1-1-1-2.6,0-3.6l70.9-70.9c0.5-0.5,1.1-0.7,1.8-0.7h20.4c1.2,0,2,0.8,2.3,1.6c0.3,0.7,0.3,1.9-0.5,2.7l-66.7,66.7c-1.3,1.3-1.3,3.5,0,4.9l52.7,52.7c1,1,1,2.6,0,3.6L133,214.9C132.5,215.4,131.9,215.6,131.2,215.6z"/></g><g><path class="st1" d="M144.7,227.4c-1.2,0-2-0.8-2.3-1.5c-0.3-0.7-0.3-1.9,0.5-2.7l66.7-66.7c0.7-0.6,1-1.5,1-2.4s-0.4-1.8-1-2.4l-52.7-52.7c-1-1-1-2.6,0-3.6l10.2-10.2c0.5-0.5,1.1-0.7,1.8-0.7c0.7,0,1.3,0.3,1.8,0.7l67,67.1c0.5,0.5,0.7,1.1,0.7,1.8s-0.3,1.3-0.7,1.8l-70.9,70.9c-0.5,0.5-1.1,0.7-1.8,0.7H144.7z"/></g></g></svg>
    </div>

                </a>
                <div class="pinfo-content recommended">
                    <a class="project-name" href="/projects/phpqrclass/" title="Learn more about PHP QR Code Class ">PHP QR Code Class</a>
                    <div class="summary">
                        A self-contained native-PHP class for producing QR Codes in image or text (console or document). Uses no other files or modules, less than 20KiB when minified.
                    </div>
                </div>
            </li>
            
            <li class="item">
                
                
                <a href="/projects/zint/" title="Zint Barcode Generator">
                    


<div class="project-icon   " >
    
    
    <img alt="Zint Barcode Generator" title="Zint Barcode Generator" 
src="//a.fsdn.com/allura/p/zint/icon?22dafada34e02ead8dc35bc5487f6cbdc4cbfb6b578214f9ba4cd03362145f3f?&amp;w=48"
    srcset="//a.fsdn.com/allura/p/zint/icon?22dafada34e02ead8dc35bc5487f6cbdc4cbfb6b578214f9ba4cd03362145f3f?&amp;w=72 1.5x
        " loading="lazy"/></div>

                </a>
                <div class="pinfo-content recommended">
                    <a class="project-name" href="/projects/zint/" title="Learn more about Zint Barcode Generator ">Zint Barcode Generator</a>
                    <div class="summary">
                        A barcode encoding library supporting over 50 symbologies.
                    </div>
                </div>
            </li>
            
            <li class="item">
                
                
                <a href="/projects/qrcodegenerator/" title="QR Code Generator">
                    


<div class="project-icon  default-project-icon " >
    
    
    


<svg  data-name="default-icon" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 300" style="enable-background:new 0 0 300 300;" xml:space="preserve"><rect class="st0" width="300" height="300"/><g><path class="st1" d="M162.5,154.8c0-28.9-10.2-42-15.6-46.9c-1.1-1-2.7-0.1-2.6,1.3c1.1,16.3-19.4,20.3-19.4,45.9h0c0,0.1,0,0.1,0,0.2c0,15.6,11.8,28.3,26.3,28.3c14.5,0,26.3-12.7,26.3-28.3c0-0.1,0-0.1,0-0.2h0c0-7.2-2.7-14.1-5.5-19.3c-0.5-1-2.1-0.6-1.9,0.3C174.9,158.1,162.5,171.8,162.5,154.8z"/><g><path class="st1" d="M131.2,215.6c-0.7,0-1.3-0.3-1.8-0.7l-67.2-67.1c-1-1-1-2.6,0-3.6l70.9-70.9c0.5-0.5,1.1-0.7,1.8-0.7h20.4c1.2,0,2,0.8,2.3,1.6c0.3,0.7,0.3,1.9-0.5,2.7l-66.7,66.7c-1.3,1.3-1.3,3.5,0,4.9l52.7,52.7c1,1,1,2.6,0,3.6L133,214.9C132.5,215.4,131.9,215.6,131.2,215.6z"/></g><g><path class="st1" d="M144.7,227.4c-1.2,0-2-0.8-2.3-1.5c-0.3-0.7-0.3-1.9,0.5-2.7l66.7-66.7c0.7-0.6,1-1.5,1-2.4s-0.4-1.8-1-2.4l-52.7-52.7c-1-1-1-2.6,0-3.6l10.2-10.2c0.5-0.5,1.1-0.7,1.8-0.7c0.7,0,1.3,0.3,1.8,0.7l67,67.1c0.5,0.5,0.7,1.1,0.7,1.8s-0.3,1.3-0.7,1.8l-70.9,70.9c-0.5,0.5-1.1,0.7-1.8,0.7H144.7z"/></g></g></svg>
    </div>

                </a>
                <div class="pinfo-content recommended">
                    <a class="project-name" href="/projects/qrcodegenerator/" title="Learn more about QR Code Generator ">QR Code Generator</a>
                    <div class="summary">
                        Standalone desktop application (offline) for QR code generation.  Batch generate. Custom dimensions.
                    </div>
                </div>
            </li>
            
            <li class="item">
                
                
                <a href="/projects/angular-flex-layout.mirror/" title="Angular Flex-Layout">
                    


<div class="project-icon   " >
    
    
    <img alt="Angular Flex-Layout" title="Angular Flex-Layout" 
src="//a.fsdn.com/allura/mirror/angular-flex-layout/icon?c0ca00890eea2d91e649686e3d323b4cbf1cd85e3520ffbd515cb7565da7b537?&amp;w=48"
    srcset="//a.fsdn.com/allura/mirror/angular-flex-layout/icon?c0ca00890eea2d91e649686e3d323b4cbf1cd85e3520ffbd515cb7565da7b537?&amp;w=72 1.5x
        ,
            //a.fsdn.com/allura/mirror/angular-flex-layout/icon?c0ca00890eea2d91e649686e3d323b4cbf1cd85e3520ffbd515cb7565da7b537?&amp;w=96 2x" loading="lazy"/></div>

                </a>
                <div class="pinfo-content recommended">
                    <a class="project-name" href="/projects/angular-flex-layout.mirror/" title="Learn more about Angular Flex-Layout ">Angular Flex-Layout</a>
                    <div class="summary">
                        Provides HTML UI layout for Angular applications
                    </div>
                </div>
            </li>
            
        </ul>
    </div>
</aside>

    <div class="sterling">
    


    


<div id="SF_ProjectFiles_300x250_B_wrapped" data-id="div-gpt-ad-1392148208789-0" class="draper medrec  
visibility_rules
 v_300_large "> 
        
        
    

    <ins class="adsbygoogle" id="adsense-1785390467"
        
        style="display:inline-block;width:300px;height:250px"
        data-ad-client="ca-pub-6471043051383295"
        data-ad-slot="1785390467"></ins>
    <script>
        if (! SF.initial_breakpoints_visible.large) {
            document.getElementById('adsense-1785390467').style="display: none;";
        } else {
            $('#SF_ProjectFiles_300x250_B_wrapped').addClass('delivered').append('<span class="lbl-ad"></span>');
        }
        SF.Ads.skipStickyAds = true;
        (adsbygoogle = window.adsbygoogle || []).push({});  // jshint ignore:line
    </script></div>
    


    
    


<div id="SF_Mobile_Multi_C_wrapped" data-id="div-gpt-ad-1512154755912-0" class="draper multisize  
"> 
        
        
    

    <ins class="adsbygoogle" id="adsense-6798916134"
        
        style="display:inline-block;width:300px;height:50px"
        data-ad-client="ca-pub-6471043051383295"
        data-ad-slot="6798916134"></ins>
    <script>
        if (! !SF.initial_breakpoints_visible.large) {
            document.getElementById('adsense-6798916134').style="display: none;";
        } else {
            $('#SF_Mobile_Multi_C_wrapped').addClass('delivered').append('<span class="lbl-ad"></span>');
        }
        SF.Ads.skipStickyAds = true;
        (adsbygoogle = window.adsbygoogle || []).push({});  // jshint ignore:line
    </script></div>
    </div>

    
        <div class="sterling" id="deals-widget">
            

        </div>
    
</div>


<script>
if (!SF.adblock && SF.initial_breakpoints_visible.large) {
    
    SF.Ads.scrollFixableEnabled = true;
    
}
</script>

                    </div>
                </div>
            

            
    <footer class="sandiego">
    <div class="as-row">
        <div class="footer-wrapper">
            <nav aria-label="Site Links" role="navigation">
                <section>
                    <div class="as-h2">SourceForge</div>
                    <ul>
                    
                        <li><a href="/create/" title="Create a Project">Create a Project</a></li>
                    
                        <li><a href="/directory/" title="Open Source Software Directory">Open Source Software</a></li>
                        <li><a href="/software/" title="Business Software Directory">Business Software</a></li>
                        
                            
                            <li><a href="/top" title="Top Open Source Projects">Top Downloaded Projects</a></li>
                        

                    </ul>
                </section>
            </nav>
            <nav aria-label="Company Links" role="navigation">
                <section>
                    <div class="as-h2">Company</div>
                    <ul>
                        <li><a href="/about">About</a></li>
                        <li><a href="/about/leadership" title="Open Source Software Directory">Team</a></li>
                        <li class="h-card">
                            <address>
                                <span class="p-name p-org">SourceForge Headquarters</span><br>
                                <span class="p-street-address">225 Broadway Suite 1600</span><br>
                                <span class="p-locality">San Diego, CA <span class="p-postal-code">92101</span></span><br>
                                <span class="p-tel">+****************</span><br>
                            </address>
                        </li>
                        <li id="social">
                            
<span></span>
<a href="https://twitter.com/sourceforge" class="twitter" rel="nofollow" target="_blank" title="SourceForge on X">


<svg  class="vertical-icon-fix" data-name="twitter" viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M1684 408q-67 98-162 167 1 14 1 42 0 130-38 259.5t-115.5 248.5-184.5 210.5-258 146-323 54.5q-271 0-496-145 35 4 78 4 225 0 401-138-105-2-188-64.5t-114-159.5q33 5 61 5 43 0 85-11-112-23-185.5-111.5t-73.5-205.5v-4q68 38 146 41-66-44-105-115t-39-154q0-88 44-163 121 149 294.5 238.5t371.5 99.5q-8-38-8-74 0-134 94.5-228.5t228.5-94.5q140 0 236 102 109-21 205-78-37 115-142 178 93-10 186-50z"/></svg></a>
<a href="https://fosstodon.org/@sourceforge" rel="me nofollow" target="_blank" title="SourceForge on Mastodon">


<svg  class="vertical-icon-fix" data-name="mastodon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M433 179.11c0-97.2-63.71-125.7-63.71-125.7-62.52-28.7-228.56-28.4-290.48 0 0 0-63.72 28.5-63.72 125.7 0 115.7-6.6 259.4 105.63 289.1 40.51 10.7 75.32 13 103.33 11.4 50.81-2.8 79.32-18.1 79.32-18.1l-1.7-36.9s-36.31 11.4-77.12 10.1c-40.41-1.4-83-4.4-89.63-54a102.54 102.54 0 0 1-.9-13.9c85.63 20.9 158.65 9.1 178.75 6.7 56.12-6.7 105-41.3 111.23-72.9 9.8-49.8 9-121.5 9-121.5zm-75.12 125.2h-46.63v-114.2c0-49.7-64-51.6-64 6.9v62.5h-46.33V197c0-58.5-64-56.6-64-6.9v114.2H90.19c0-122.1-5.2-147.9 18.41-175 25.9-28.9 79.82-30.8 103.83 6.1l11.6 19.5 11.6-19.5c24.11-37.1 78.12-34.8 103.83-6.1 23.71 27.3 18.4 53 18.4 175z"/></svg></a>
<a href="https://www.facebook.com/sourceforgenet/" class="facebook" rel="nofollow" target="_blank" title="SourceForge on Facebook">


<svg  data-name="facebook" viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M1343 12v264h-157q-86 0-116 36t-30 108v189h293l-39 296h-254v759h-306v-759h-255v-296h255v-218q0-***********.5t277-102.5q147 0 228 12z"/></svg></a>
<a href="https://www.linkedin.com/company/sourceforge.net" class="linkedin" rel="nofollow" target="_blank" title="SourceForge on LinkedIn">


<svg  data-name="linkedin" viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M477 625v991h-330v-991h330zm21-306q1 73-50.5 122t-135.5 49h-2q-82 0-132-49t-50-122q0-74 51.5-122.5t134.5-48.5 133 48.5 51 122.5zm1166 729v568h-329v-530q0-105-40.5-164.5t-126.5-59.5q-63 0-105.5 34.5t-63.5 85.5q-11 30-11 81v553h-329q2-399 2-647t-1-296l-1-48h329v144h-2q20-32 41-56t56.5-52 87-43.5 114.5-15.5q171 0 275 113.5t104 332.5z"/></svg></a>

<a href="/user/newsletters" rel=nofollow class="newsletter" title="Subscribe to our newsletter">


<svg  class="vertical-icon-fix" data-name="mmSF_11mail" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 42 42" enable-background="new 0 0 42 42" xml:space="preserve"><path fill="#FFFFFF" d="M0,6v30h42V6H0z M24.2,21.2c-0.8,0.8-2.3,2-3.2,2c-0.9,0-2.4-1.2-3.2-2L5.8,9.7h30.3L24.2,21.2z M13.7,21l-9.9,9.4V11.6L13.7,21z M15.7,23L15.7,23c0.5,0.5,2.9,2.9,5.3,2.9c2.4,0,4.8-2.4,5.2-2.8l0.1-0.1l9.8,9.4H5.8L15.7,23z M28.3,21l9.9-9.5v18.9L28.3,21z"/></svg></a>

<span></span>
                        </li>
                    </ul>
                </section>
            </nav>
             <nav aria-label="Resources Links" role="navigation">
                <section>
                    <div class="as-h2">Resources</div>
                    <ul>
                        
                            <li><a href="/support" title="Support Section">Support</a></li>
                            <li><a href="/p/forge/documentation/Docs%20Home/" title="Site Documentation">Site Documentation</a></li>
                        
                        <li><a href="https://fosstodon.org/@sourceforgestatus" title="Site Status" rel="me nofollow">Site Status</a></li>
                        <li><a href="/reviews" title="Reviews" rel="me nofollow">SourceForge Reviews</a></li>

                    </ul>
                </section>
            </nav>
            <section class="footer-logo">
                <a href="/" title="Home" class="sf-logo">
                    
    
    <img src="//a.fsdn.com/con/images/sandiego/sf-logo-full.svg"  alt="SourceForge logo" class="sf-logo-full"/>
                </a>
            </section>
        </div>
    </div>
    <section class="l-nav-bottom">
        <nav class="row">
            
                
            
        
    <div class="columns small-12 large-6 copyright-notice">
        &copy; 2025 Slashdot Media. All Rights Reserved.
    </div>
    <div class="columns large-6 links links-right">
        

    
    

    <a href="https://slashdotmedia.com/terms-of-use" target="_blank" title="Terms" rel="nofollow">Terms</a>
    <a href="https://slashdotmedia.com/privacy-statement/" target="_blank" title="Privacy" rel="nofollow">Privacy</a>

    
    
    

    
        <a href="https://slashdotmedia.com/opt-out-choices/" target="_blank" title="Opt Out" rel="nofollow">Opt Out</a>
    

    
        <a href="https://slashdotmedia.com/contact/" target="_blank" title="Advertise" rel="nofollow">Advertise</a>
    
    </div>
        </nav>
    </section>

    </footer>
    



            
    
        </div>
            
    
    <div id="newsletter-floating" class="sandiego newsletter-floating-new">
        <a class="close-button btn-closer" data-close aria-label="Close modal">
            <span aria-hidden="true">&times;</span>
        </a>
        <div class="as-h2">Want the latest updates on software, tech news, and AI?</div>
        <div class="row">
            <div class="column small-2 medium-2 large-2" style="padding-left: 0px; padding-right: 0px;"><img src="//a.fsdn.com/con/images/sandiego/sf_email_icon.svg"  alt="" /></div>
            <div class="as-h3 column small-10 medium-10 large-10">Get latest updates about software, tech news, and AI from SourceForge directly in your inbox once a month.</div>
        </div>
        <form action="/user/newsletters/" method="post">
            <div class="row">
              <input class="newsletter-input" type="email" placeholder="Enter your email address" autocomplete="off" name="newsletter_email"
                     value="">
              <input type="hidden" name="source" value="floating">
              <button type="submit" class="button blue newsletter-button">Submit</button>
            </div>
        </form>
    </div>
    

            

        
    
        <script src="//a.fsdn.com/con/js/min/sf.sandiego-base.js?1745596971"></script>

        
    

    <script>
        /* global Dropzone */
        Dropzone.options.blockthisForm = false;
    </script>
        
    
        <script src="//a.fsdn.com/con/js/min/sf.sandiego-foundation-base.js?1745596971"></script>

        
    


    
        <script src="//a.fsdn.com/con/js/min/sf.sandiego-files.js?1745596971"></script>

        
    


        

        <script>
            /* global Foundation */
            $(document).foundation();
            Foundation.Triggers.forceListenersNow();
        </script>


        
        <script>
            bizx.cmp.ifConsent({purposes:'all', vendors:'google-ads'}, function () {
                $('body').removeClass('no-ads-consent'); 
            },
            function () { 
                $('body').addClass('no-ads-consent');
            },
            null,
            function () { 
                $('body').addClass('no-ads-consent');
            });
        </script>

        <noscript><p><img src="https://analytics.slashdotmedia.com/index.php?idsite=39" style="border:0;" alt="" /></p></noscript>

        

        
        

        <script>

        function geturl(url, params) {
            params = Object
                .keys(params)
                .map(function(key) {
                    return key + "=" + encodeURIComponent(params[key]);
                })
                .join("&");
            return url + "?" + params;
        }

        
        function loadimg(params, dimension_prefix) {
            params = Object.assign({"idsite":1,"rec":1,"rand":54715,"dimension2":"pg_files","url":"https://sourceforge.net/projects/phpqrcode/files/","action_name":"PHP QR Code -  Browse Files at SourceForge.net"}, params);
            for (var key in params) {
                if (params.hasOwnProperty(key) && key.indexOf('dimension') === 0 && params[key] !== undefined) {
                    params[key] = (dimension_prefix||'') + params[key];
                }
            }
            params.rand = Math.floor(Math.random() * 100000);
            bizx.cmp.ifConsent('publisher', ['measurement'], function() {
                var url = geturl("//sourceforge.net/software/visit", params);
                if (!('sendBeacon' in navigator) || !navigator.sendBeacon(url)) {
                    var img = document.createElement('img');
                    img.src = url;
                    img.style = "border:0;position:absolute;top:0;";
                    img.alt = "";
                    document.body.appendChild(img);
                }
            });
        }

        var $typeaheads = $('.typeahead__container input[name=q]');
        $typeaheads.on('typeahead-item-clicked', function(ev, $typeahead, q, item){
            var groupIndex = ($typeahead.data('groups') || []).indexOf(item.group);
            if (groupIndex === 2 || groupIndex === 3) {
                loadimg({
                    'e_c': 'Search | Typeahead | ' + item.group,
                    'e_a': "Typeahead Click | q=" + q,
                    'e_n': item.href, 
                 }, 'typeahead_on_');
            }
        });
        </script>

        <script type="text/javascript">
            bizx.cmp.ifConsent({purposes: 'all', vendors: '6sense'}, async() => {
                
                (function(){var s = document.getElementsByTagName("script")[0];
                var b = document.createElement("script");
                b.type = "text/javascript";b.async = true;b.defer=true;b.id='6senseWebTag';
                b.src = "https://j.6sc.co/j/58729049-be80-466a-9abf-b3911430bbd8.js";
                s.parentNode.insertBefore(b, s);})();
            });
            </script>
<script type="text/x-handlebars-template" id="file-drawer-template">
<div class="drawer-container">

<form class="bp" action="{{files_url}}{{full_path}} method="put" id="file_properties_content">
    <table id="drawer_row">
        <col class="name-column">
        <col class="date-column">
        <col class="size-column">
        <col class="downloads-column">
        <tbody>
            <tr title="{{name}}">
                <td>
                {{#if authorized }}
                    <input type="text" class="title" name="name" value="{{name}}">
                {{else}}
                    <a href="{{file_title_url this}}" class="name">{{name}}</a>
                {{/if}}
                </td>
                <td class="files-date"></td>
                <td class="files-size"></td>
                <td class="files-downloads"></td>
            </tr>
            {{#if authorized}}
                <tr>
                    <td colspan="4" id="name_message" class="invalid hide"></td>
                </tr>
                {{#if d_type}}
                <tr>
                    <td colspan="4" class="cell-stage">
                        <input type="checkbox" name="stage" id="stage" value="1"  {{checked this.staged}} class="{{stage_onclick this}}" />
                        <label for="stage" title="Only release technicians will see this folder in the file browser." class="{{stage_onclick this}}">{{stage_message this staging_days}}</label>
                        <span title="Only release technicians will see this folder in the file browser.">
<svg  class="svgico" data-name="question-circle" viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M1024 1376v-192q0-14-9-23t-23-9h-192q-14 0-23 9t-9 23v192q0 14 9 23t23 9h192q14 0 23-9t9-23zm256-672q0-88-55.5-163t-138.5-116-170-41q-243 0-371 213-15 24 8 42l132 100q7 6 19 6 16 0 25-12 53-68 86-92 34-24 86-24 48 0 85.5 26t37.5 59q0 38-20 61t-68 45q-63 28-115.5 86.5t-52.5 125.5v36q0 14 9 23t23 9h192q14 0 23-9t9-23q0-19 21.5-49.5t54.5-49.5q32-18 49-28.5t46-35 44.5-48 28-60.5 12.5-81zm384 192q0 209-103 385.5t-279.5 279.5-385.5 103-385.5-103-279.5-279.5-103-385.5 103-385.5 279.5-279.5 385.5-103 385.5 103 279.5 279.5 103 385.5z"/></svg></span>
                        {{stage_date this}}
                    </td>
                </tr>
                {{/if}}
            {{/if}}
        </tbody>
    </table>

    <div id="file-details">
        <div id="file-meta" class="hide-for-medium">
            <div>
                <span>Modified:</span>
                <pre class="selectable">{{safeString date}}</pre>
            </div>
            {{#if f_type}}
            <div >
                <span>Size:</span>
                <pre class="selectable">{{size}}</pre>
            </div>
            {{/if}}
        </div>
        {{#if f_type}}
        <div id="file-info">
            {{stage_date this}}

            <div class="label">
                <span>SHA1:</span>
            </div>
            <div class="value"><pre class="selectable">{{sha1}}</pre></div>

            <div class="label">
                <span>MD5:</span>
            </div>
            <div class="value"><pre class="selectable">{{md5}}</pre></div>

            {{#if authorized}}
            <div class="label">
                <span>Download URL:</span>
            </div>
            <div class="value">
                {{#if not_downloadable}}
                    <pre class="selectable" title="This file will be ready for download shortly.">This file will be ready for download shortly.</pre>
                {{else}}
                <pre class="selectable" title="{{download_url}}">{{download_url}}</pre>
                {{/if}}
            </div>

    <div class="default">
    <div class="label">
    <span>Default Download For:</span>
    </div>
    <div class="value">
    <ul>
    {{#each platforms}}
    {{> platform}}
    {{/each}}

    {{#if authorized}}
    <li><a href="#select_all"  class="btn-select-all" title="Select all">Select all</a></li>
    {{/if}}
    </ul>
    </div>
    </div>


    <div class="label stay-inline">
    <label for="exclude_reports">Exclude Stats:</label>
    </div>
    <div class="value stay-inline">
    <span class="checkbox"><input type="checkbox" id="exclude_reports" name="exclude_reports" value="1" {{should_exclude_reports exclude_reports}}></span>
    </div>

    {{/if}}

    {{#if legacy_release_notes}}
    <div class="value no-label">
    <span><a href="{{legacy_release_notes}}">Release Notes</a></span>
    </div>
    {{/if}}
    </div>

    <div id="download-info">
    <div class="label">
    <span>Total Downloads:</span>
    </div>
    <div class="value">
    <span>{{download_display downloads}}</span>
    </div>

    {{#if authorized}}
    <div class="label">
    <span>Mirror Status:</span>
    </div>
    <div class="value">
    <span id="mirror_count">Loading ...</span>
    </div>
    {{/if}}

    </div>
    {{/if}}

{{#if authorized}}
    <hr />
    <p class="btn-set"> <a href="#" id="cancel" class="btn cancel button blue extra-wide hollow">Cancel</a><input type="submit" value="Save" class="button blue extra-wide"></p>
    {{/if}}
    </div>
    </form>
    
</div>
</script>

<script type="text/x-handlebars-template" id="platform-partial">
    
    <li>
    <label>
    {{#if authorized}}
    <input type="checkbox" name="default" value="{{value}}" {{_checked}}>
    <span title="{{title}}" class="platform-icon {{value}}">{{title}}</span>
    {{/if}}

{{#unless authorized}}
{{#unless skip}}
    <span title="{{title}}" class="platform-icon {{value}}">{{title}}</span>
    {{/unless}}
{{/unless}}
    </label>
    </li>
    
</script>

<script>
    net.sf.files = {"releases":{"name":"releases","path":"","download_url":"https://sourceforge.net/projects/phpqrcode/files/releases/download","url":"/projects/phpqrcode/files/releases/","full_path":"releases","type":"d","link":"","downloads":961774,"sha1":"","md5":"","default":"","download_label":"PHP QR Code source releases ","exclude_reports":false,"downloadable":false,"legacy_release_notes":null,"staged":false,"stage":0,"staging_days":3,"files_url":"/projects/phpqrcode/files/","explicitly_staged":false,"authorized":null}};
net.sf.staging_days = 3;
$(function ($) {
    $('#files').files();
    var tsConfig = {
        //sortForce: [[0, 0]],
        headers: {
            0: { sorter: 'name' },
            1: { sorter: 'abbrdate' },
            2: { sorter: 'filesize' },
            3: { sorter: 'download_count'}
        },
        textExtraction: function (node) {
            return node.innerHTML;
        }
    };

    if ($('#files_list tbody tr').length) {
        $('#files_list').tablesorter(tsConfig).on("sortEnd", SF.tablesorter_svg_update);
        $('#files_list tr th#parent_folder').unbind();
        $('#files_list thead tr th').not('.typesort').not('#parent_folder').addClass('usersortable').closest('tr').find('div').append('<svg class="svgico icon-sort"><use xlink:href="#sort"></use></svg>');
    }
});


</script>

        

<!-- CCM Tag -->
<script>
    (function () {
        bizx.cmp.ifConsent({ purposes: 'all', vendors: 'bombora'}, function () {
            /*global _ml:true, window */
            _ml = window._ml || {};
            
            _ml.eid = '771';
            _ml.fp = 'df03fd2f-93d7-4bac-b917-6ed54b6ca623';  
            var s = document.getElementsByTagName('script')[0], cd = new Date(), mltag = document.createElement('script');
            mltag.type = 'text/javascript';
            mltag.async = true;
            mltag.defer = true;
            mltag.src = '//ml314.com/tag.aspx?' + cd.getDate() + cd.getMonth() + cd.getFullYear();
            s.parentNode.insertBefore(mltag, s);
        });
    })();
</script>
<!-- End CCM Tag -->


        
        
<!-- Hubspot tracking -->


        


        
            
    
    <div id="overlay-blockthis-wrapper" style="display: none;">
        <div id="overlay-blockthis">
            <div class="as-h2 title">Thanks for helping keep SourceForge clean.</div>
            <a href="#" id="btn-blockthis-close">X</a>
            <form class="dropzone small-12" action="/api/instrumentation/gpt" id="blockthisForm" method="POST">
                <div class="row small-12">
                    <div class="column description small-12">
                        <input type="hidden" name="_visit_cookie" value="df03fd2f-93d7-4bac-b917-6ed54b6ca623"/>
                            <input type='hidden' name='timestamp' value='1745745256'/>
                            <input type='hidden' name='spinner' value='XeosOoLfq9PVgt2lAxA03sqkC99I'/>
                            <p class='n92053996079b3a857e56cc49b1cba1e6a8ae4fac'><label for='XfcOjYcOOw5LCk8OEw4VQw57CuE0xAS_DjMOowp8tBA'>You seem to have CSS turned off.
             Please don't fill out this field.</label><input id='XfcOjYcOOw5LCk8OEw4VQw57CuE0xAS_DjMOowp8tBA' name='XfMOjYcOOw5LCk8OEw4UJZmTCtcOIFUnDszTDmCEe' type=
             'text'/></p>
                            <p class='n92053996079b3a857e56cc49b1cba1e6a8ae4fac'><label for='XfcOjYcOOw5LCk8OEw4RQw57CuE0xAS_DjMOowp8tBA'>You seem to have CSS turned off.
             Please don't fill out this field.</label><input id='XfcOjYcOOw5LCk8OEw4RQw57CuE0xAS_DjMOowp8tBA' name='XfMOjYcOOw5LCk8OFw4UJZmTCtcOIFUnDszTDmCEe' type=
             'text'/></p>
                        Briefly describe the problem (required):
                        <input name="XccOva8OTw5TCmMKdwoUUw54GLsO0ZMOmwr9cDsOvwqw" type="text" required>
                    </div>
                </div>
                <div class="column small-12">
                    <div class="upload-text">Upload screenshot of ad (required):</div>
                    <div id='upload-it'>
                        <a href="#" id="upload-select-file">Select a file</a>, or drag & drop file here.
                    </div>
                    <div id="upload-it-placeholder"></div> 

                    <div class="dropzone-previews" style="display: none"></div>
                    <div class="dz-message" style="display: none"></div> 
                    
                    <div id="dropzone-preview-template" style="display: none">
                        <div class="dz-preview dz-file-preview">
                            <img data-dz-thumbnail src="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=" alt=""/>
                            <div class="dz-success-mark"><span>✔</span></div>
                            <div class="dz-error-mark"><span>✘</span></div>
                            <div class="dz-error-message"><span data-dz-errormessage></span></div>
                        </div>
                    </div>
                </div>
                <div class="column small-12">
                    <u>Screenshot instructions:</u>
                    
                    <a data-external target=_blank href="http://windows.microsoft.com/en-us/windows/take-screen-capture-print-screen#take-screen-capture-print-screen=windows-8">Windows</a>
                    
                </div>
                <div class="row small-12">
                    <div class="column large-5 small-6">
                        <p>
                            <u>Click URL instructions:</u><br>
                            Right-click on the ad, choose "Copy Link", then paste here &rarr;<br>
                            (This may not be possible with some types of ads)
                        </p>
                        <a class="more-info" href="https://sourceforge.net/p/forge/documentation/Report%20a%20problem%20with%20Ad%20content/" target="_blank">More information about our ad policies</a>
                    </div>
                    <div class="column large-7 small-6">
                        <p>Ad destination/click URL:
                        <input name="Xc8OoYsOJw5TCgcKrwoASw5tZKRUAw4LCvsKxfMK2Tw" type="url" required>
                        </p>
                        <textarea id="gpt-info" name="XfsOvb8OUw5bDmsKdJG1CZVjCukzCqmh_w45Vw6I"></textarea>
                        <input class="button" type="submit" id="btn-blockthis-submit" value="Submit Report">
                    </div>
                </div>
            </form>
        </div>
    </div>

        

        <script>
            bizx.cmp.ifConsent('', ['all'], function () {
                bizx.cmp.embedScript('//ads.pro-market.net/ads/scripts/site-143572.js');
            });
        </script><script>
            bizx.cmp.ifConsent('', ['all'], function () {
                try{(function(){ var cb = new Date().getTime(); var s = document.createElement("script"); s.defer = true; s.src = "//tag.crsspxl.com/s1.js?d=2396&cb="+cb; var s0 = document.getElementsByTagName('script')[0]; s0.parentNode.insertBefore(s, s0); })();}catch(e){}
            });
        </script>
    
    
<script type="text/javascript">
    bizx.cmp.ifConsent({ purposes: ['storage', 'measurement'], vendors: 'google-analytics'}, function () {
        /* jshint ignore:start */
        (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
                (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
            m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
        })(window,document,'script','//www.google-analytics.com/analytics.js','ga');
        /* jshint ignore:end */

        window.dataLayer = window.dataLayer || [];
        function gtag(){ window.dataLayer.push(arguments); }
        window.gtag = window.gtag || gtag;
        bizx.cmp.embedScript("https://www.googletagmanager.com/gtag/js");
        gtag('js', new Date());
        gtag('set', {
            'page_location': 'https://sourceforge.net/projects/phpqrcode/files/',
        });
    });
</script>
    <script>
        /*global ga, gtag */
        SF.devicePixelRatio = Math.round(window.getDevicePixelRatio()*10)/10;

        

        
            bizx.cmp.ifConsent({ purposes: ['storage', 'measurement'], vendors: 'google-analytics'}, function () {
                gtag('config', 'G-1H226E4E4L', {
                    
                    send_page_view: false,
                    'SF_Project_Shortname': 'phpqrcode', 
                    'SF_Page_Type': 'pg_files', 
                    user_properties: {
                        'SF_Logged_in': 'Logged Out', 
                    },
                    'SF_Ads_Disabled': 'No',   
                    'SF_Prebid_Load_Method': 'sync', 
                    'devicePixelRatio': SF.devicePixelRatio, 
                });
                gtag('event', 'page_view', { send_to: 'G-1H226E4E4L' });
            });
        
            
            
        
        
    </script>
    

        
        
        
             <script>
    $(function() {
        bizx.cmp.ifConsent({ purposes: 'all' , vendors: 'narrative'}, function() {
            var current_time = (new Date()).getTime();
            let imageUrl = "https://io.narrative.io/?companyId=2440&id=first_party%3Adf03fd2f-93d7-4bac-b917-6ed54b6ca623&id=site_name%3Asourceforge.net&id=url%3A%2Fprojects%2Fphpqrcode%2Ffiles%2F&id=pagetitle%3APHP+QR+Code+-++Browse+Files+at+SourceForge.net&id=vertical%3AOpen+Source+Software&id=product%3APHP+QR+Code&id=topics%3AUsability%2CQR+Code+Generators&id=programming_language%3APHP";
            imageUrl = imageUrl.replace(encodeURIComponent("$PAGE_TITLE"), document.title);  
            
            imageUrl = URL.parse(imageUrl);
            let hem = bizx.uids.getHem();
            if (hem) {
                imageUrl.searchParams.append('id', 'hem:' + hem);
            }
            imageUrl.searchParams.append('rand', current_time);

            var image = new Image();
            image.src = imageUrl.toString();
            image.style.display = "none";
            image.style.height = 0;
            image.style.width = 0;
            document.body.appendChild(image);
        });
    })
</script>
        

        
            
<script>
    $(function() {
        bizx.cmp.ifConsent({ purposes: 'all', vendors: 'xandr'}, function() {
            /*jshint ignore:start*/
            !function(e,i){if(!e.pixie){var n=e.pixie=function(e,i,a){n.actionQueue.push({action:e,actionValue:i,params:a})};n.actionQueue=[];var a=i.createElement("script");a.async=!0,a.src="//acdn.adnxs.com/dmp/up/pixie.js";var t=i.getElementsByTagName("head")[0];t.insertBefore(a,t.firstChild)}}(window,document);
            pixie('init', '48d4c5e5-03de-40f8-81ab-b370a8860afa');
            pixie('event', 'PageView');
            /*jshint ignore:end*/

        });
    });
</script>

        

        









<script>
        function gam(id){
            bizx.cmp.ifConsent({ purposes: 'all', vendors: 'google-ads'}, function () {
                bizx.cmp.embedScript(`https://pagead2.googlesyndication.com/pagead/js/pcd.js?${id}`,
                    true,  // async
                    'head', // location
                    null, // callback
                    false, // defer
                    {id: `google-pcd-tag-${id}`, 'data-audience-pixel': 'dc_iu=/41014381/DFPAudiencePixel;dc_seg=' + id});
            });
        }

        
            gam("8901705213");gam("8901712041");</script>

        
        








        
        
            
            
        
            
            
            
        
        
    </body>
</html>