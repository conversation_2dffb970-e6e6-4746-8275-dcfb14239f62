<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Create session_charges table if it doesn't exist
$createTableQuery = "CREATE TABLE IF NOT EXISTS session_charges (
    id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    session_id INT(11) NOT NULL,
    department_id INT(11) NULL,
    fee_type_id INT(11) NOT NULL,
    amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    is_active TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_session_fee (session_id, department_id, fee_type_id),
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (fee_type_id) REFERENCES fee_types(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
)";
$conn->query($createTableQuery);

// Process session charge addition
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_charge'])) {
    $sessionId = $_POST['session_id'] ?? 0;
    $departmentId = !empty($_POST['department_id']) ? $_POST['department_id'] : null;
    $feeTypeIds = $_POST['fee_type_ids'] ?? [];
    $amount = $_POST['amount'] ?? 0;

    // Convert Bengali numerals to English numerals
    $amount = str_replace(['০', '১', '২', '৩', '৪', '৫', '৬', '৭', '৮', '৯'],
                         ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'], $amount);
    $amount = floatval($amount);

    if ($sessionId > 0 && !empty($feeTypeIds)) {
        $successCount = 0;
        $existingCount = 0;
        $errorCount = 0;

        // Start transaction
        $conn->begin_transaction();

        try {
            // Check if session charge already exists and insert new ones
            $checkQuery = "SELECT id FROM session_charges WHERE session_id = ? AND fee_type_id = ? AND (department_id = ? OR (department_id IS NULL AND ? IS NULL))";
            $checkStmt = $conn->prepare($checkQuery);

            $insertQuery = "INSERT INTO session_charges (session_id, department_id, fee_type_id, amount) VALUES (?, ?, ?, ?)";
            $insertStmt = $conn->prepare($insertQuery);

            // Get fee type amounts
            $feeTypeAmountsQuery = "SELECT id, amount FROM fee_types WHERE id IN (" . implode(',', array_map('intval', $feeTypeIds)) . ")";
            $feeTypeAmountsResult = $conn->query($feeTypeAmountsQuery);
            $feeTypeAmounts = [];

            if ($feeTypeAmountsResult && $feeTypeAmountsResult->num_rows > 0) {
                while ($row = $feeTypeAmountsResult->fetch_assoc()) {
                    $feeTypeAmounts[$row['id']] = $row['amount'] ?? 0;
                }
            }

            foreach ($feeTypeIds as $feeTypeId) {
                // Skip invalid fee type IDs
                if (empty($feeTypeId)) continue;

                // Check if session charge already exists
                $checkStmt->bind_param('iiis', $sessionId, $feeTypeId, $departmentId, $departmentId);
                $checkStmt->execute();
                $result = $checkStmt->get_result();

                if ($result->num_rows > 0) {
                    $existingCount++;
                    continue;
                }

                // Use fee type amount if available, otherwise use the provided amount
                $chargeAmount = $amount;
                if (isset($feeTypeAmounts[$feeTypeId]) && $amount <= 0) {
                    $chargeAmount = $feeTypeAmounts[$feeTypeId];
                }

                // Skip if amount is zero or negative
                if ($chargeAmount <= 0) {
                    $errorCount++;
                    continue;
                }

                // Create new session charge
                $insertStmt->bind_param('iiid', $sessionId, $departmentId, $feeTypeId, $chargeAmount);

                if ($insertStmt->execute()) {
                    $successCount++;
                } else {
                    $errorCount++;
                }
            }

            // Commit transaction
            $conn->commit();

            // Set appropriate message
            if ($successCount > 0) {
                $_SESSION['success'] = $successCount . ' টি সেশন চার্জ সফলভাবে যোগ করা হয়েছে!';

                if ($existingCount > 0) {
                    $_SESSION['warning'] = $existingCount . ' টি সেশন চার্জ ইতিমধ্যে বিদ্যমান ছিল।';
                }

                if ($errorCount > 0) {
                    $_SESSION['error'] = $errorCount . ' টি সেশন চার্জ যোগ করতে সমস্যা হয়েছে।';
                }
            } else if ($existingCount > 0 && $errorCount == 0) {
                $_SESSION['warning'] = 'সকল সেশন চার্জ ইতিমধ্যে বিদ্যমান!';
            } else {
                $_SESSION['error'] = 'সেশন চার্জ যোগ করতে সমস্যা হয়েছে।';
            }
        } catch (Exception $e) {
            // Roll back transaction on error
            $conn->rollback();
            $_SESSION['error'] = 'সেশন চার্জ যোগ করতে সমস্যা: ' . $e->getMessage();
        }
    } else {
        $_SESSION['error'] = 'সকল প্রয়োজনীয় ফিল্ড পূরণ করুন!';
    }
}

// Process session charge update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_charge'])) {
    $chargeId = $_POST['charge_id'] ?? 0;
    $amount = $_POST['amount'] ?? 0;
    $isActive = isset($_POST['is_active']) ? 1 : 0;

    // Convert Bengali numerals to English numerals
    $amount = str_replace(['০', '১', '২', '৩', '৪', '৫', '৬', '৭', '৮', '৯'],
                         ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'], $amount);
    $amount = floatval($amount);

    if ($chargeId > 0 && $amount >= 0) {
        // Update session charge
        $updateQuery = "UPDATE session_charges SET amount = ?, is_active = ? WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param('dii', $amount, $isActive, $chargeId);

        if ($stmt->execute()) {
            $_SESSION['success'] = 'সেশন চার্জ সফলভাবে আপডেট করা হয়েছে!';
        } else {
            $_SESSION['error'] = 'সেশন চার্জ আপডেট করতে সমস্যা: ' . $conn->error;
        }
    } else {
        $_SESSION['error'] = 'অবৈধ চার্জ আইডি বা পরিমাণ!';
    }
}

// Process session charge deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_charge'])) {
    $chargeId = $_POST['charge_id'] ?? 0;

    if ($chargeId > 0) {
        // Delete session charge
        $deleteQuery = "DELETE FROM session_charges WHERE id = ?";
        $stmt = $conn->prepare($deleteQuery);
        $stmt->bind_param('i', $chargeId);

        if ($stmt->execute()) {
            $_SESSION['success'] = 'সেশন চার্জ সফলভাবে মুছে ফেলা হয়েছে!';
        } else {
            $_SESSION['error'] = 'সেশন চার্জ মুছতে সমস্যা: ' . $conn->error;
        }
    } else {
        $_SESSION['error'] = 'অবৈধ চার্জ আইডি!';
    }
}

// Get all sessions
$sessionsQuery = "SELECT id, session_name FROM sessions ORDER BY session_name DESC";
$sessions = $conn->query($sessionsQuery);

// Get all departments
$departmentsQuery = "SELECT id, department_name FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Get all fee types
$feeTypesQuery = "SELECT id, name, amount FROM fee_types ORDER BY name";
$feeTypes = $conn->query($feeTypesQuery);

// Get all session charges with session, department and fee type names
$chargesQuery = "SELECT sc.*, s.session_name, d.department_name, ft.name as fee_type_name
                FROM session_charges sc
                JOIN sessions s ON sc.session_id = s.id
                LEFT JOIN departments d ON sc.department_id = d.id
                JOIN fee_types ft ON sc.fee_type_id = ft.id
                ORDER BY s.session_name DESC, d.department_name, ft.name";
$charges = $conn->query($chargesQuery);

// Include header
include_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Include sidebar -->
        <?php include_once 'includes/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-money-bill-wave me-2"></i> সেশন চার্জ ম্যানেজমেন্ট</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="fee_management.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i> ফি ম্যানেজমেন্ট
                        </a>
                        <a href="apply_session_charges.php" class="btn btn-sm btn-outline-success">
                            <i class="fas fa-users me-1"></i> চার্জ প্রয়োগ করুন
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addChargeModal">
                            <i class="fas fa-plus-circle me-1"></i> নতুন সেশন চার্জ
                        </button>
                    </div>
                </div>
            </div>

            <!-- Display Messages -->
            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $_SESSION['success'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['warning'])): ?>
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <?= $_SESSION['warning'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['warning']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $_SESSION['error'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>

            <!-- Session Charges Table -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i> সেশন চার্জ তালিকা</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>আইডি</th>
                                    <th>সেশন</th>
                                    <th>বিভাগ</th>
                                    <th>ফি টাইপ</th>
                                    <th>পরিমাণ</th>
                                    <th>স্ট্যাটাস</th>
                                    <th>তৈরি তারিখ</th>
                                    <th>পদক্ষেপ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($charges && $charges->num_rows > 0): ?>
                                    <?php while ($charge = $charges->fetch_assoc()): ?>
                                        <tr>
                                            <td><?= $charge['id'] ?></td>
                                            <td><?= $charge['session_name'] ?></td>
                                            <td><?= $charge['department_name'] ? $charge['department_name'] : '<span class="text-muted">সকল বিভাগ</span>' ?></td>
                                            <td><?= $charge['fee_type_name'] ?></td>
                                            <td>৳ <?= number_format($charge['amount'], 2) ?></td>
                                            <td>
                                                <?php if ($charge['is_active']): ?>
                                                    <span class="badge bg-success">সক্রিয়</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">নিষ্ক্রিয়</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= date('d/m/Y', strtotime($charge['created_at'])) ?></td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-primary edit-charge-btn"
                                                        data-id="<?= $charge['id'] ?>"
                                                        data-session-id="<?= $charge['session_id'] ?>"
                                                        data-department-id="<?= $charge['department_id'] ?>"
                                                        data-fee-type-id="<?= $charge['fee_type_id'] ?>"
                                                        data-amount="<?= $charge['amount'] ?>"
                                                        data-is-active="<?= $charge['is_active'] ?>"
                                                        data-bs-toggle="modal" data-bs-target="#editChargeModal">
                                                    <i class="fas fa-edit"></i> এডিট
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger delete-charge-btn"
                                                        data-id="<?= $charge['id'] ?>"
                                                        data-session-name="<?= $charge['session_name'] ?>"
                                                        data-department-name="<?= $charge['department_name'] ? $charge['department_name'] : 'সকল বিভাগ' ?>"
                                                        data-fee-type-name="<?= $charge['fee_type_name'] ?>"
                                                        data-bs-toggle="modal" data-bs-target="#deleteChargeModal">
                                                    <i class="fas fa-trash-alt"></i> ডিলিট
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="8" class="text-center">কোন সেশন চার্জ রেকর্ড পাওয়া যায়নি</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Add Session Charge Modal -->
<div class="modal fade" id="addChargeModal" tabindex="-1" aria-labelledby="addChargeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addChargeModalLabel"><i class="fas fa-plus-circle me-2"></i> নতুন সেশন চার্জ যোগ করুন</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="session_id" class="form-label">সেশন</label>
                        <select class="form-select" id="session_id" name="session_id" required>
                            <option value="">সেশন নির্বাচন করুন</option>
                            <?php if ($sessions && $sessions->num_rows > 0): ?>
                                <?php while ($session = $sessions->fetch_assoc()): ?>
                                    <option value="<?= $session['id'] ?>"><?= $session['session_name'] ?></option>
                                <?php endwhile; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="department_id" class="form-label">বিভাগ <small class="text-muted">(ঐচ্ছিক)</small></label>
                        <select class="form-select" id="department_id" name="department_id">
                            <option value="">সকল বিভাগ</option>
                            <?php if ($departments && $departments->num_rows > 0): ?>
                                <?php
                                $departments->data_seek(0);
                                while ($department = $departments->fetch_assoc()):
                                ?>
                                    <option value="<?= $department['id'] ?>"><?= $department['department_name'] ?></option>
                                <?php endwhile; ?>
                            <?php endif; ?>
                        </select>
                        <div class="form-text">বিভাগ নির্বাচন না করলে, এই চার্জ সকল বিভাগের জন্য প্রযোজ্য হবে।</div>
                    </div>
                    <div class="mb-3">
                        <label for="fee_type_ids" class="form-label">ফি টাইপ <small class="text-muted">(একাধিক নির্বাচন করতে Ctrl/Cmd কী চেপে ধরুন)</small></label>
                        <select class="form-select" id="fee_type_ids" name="fee_type_ids[]" multiple required>
                            <?php if ($feeTypes && $feeTypes->num_rows > 0): ?>
                                <?php
                                $feeTypes->data_seek(0);
                                while ($feeType = $feeTypes->fetch_assoc()):
                                ?>
                                    <option value="<?= $feeType['id'] ?>" data-amount="<?= $feeType['amount'] ?? 0 ?>"><?= $feeType['name'] ?> (৳ <?= number_format($feeType['amount'] ?? 0, 2) ?>)</option>
                                <?php endwhile; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="amount" class="form-label">পরিমাণ (৳) <small class="text-muted">(ঐচ্ছিক)</small></label>
                        <input type="text" class="form-control" id="amount" name="amount" pattern="[০-৯0-9\.]+">
                        <div class="form-text">খালি রাখলে প্রতিটি ফি টাইপের ডিফল্ট পরিমাণ ব্যবহার করা হবে। একাধিক ফি টাইপ নির্বাচন করলে এই ফিল্ড ব্যবহার করা হবে না।</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="submit" name="add_charge" class="btn btn-primary">যোগ করুন</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Session Charge Modal -->
<div class="modal fade" id="editChargeModal" tabindex="-1" aria-labelledby="editChargeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editChargeModalLabel"><i class="fas fa-edit me-2"></i> সেশন চার্জ সম্পাদনা করুন</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <input type="hidden" id="edit_charge_id" name="charge_id">
                    <div class="mb-3">
                        <label for="edit_session_id" class="form-label">সেশন</label>
                        <input type="text" class="form-control" id="edit_session_name" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="edit_department_id" class="form-label">বিভাগ</label>
                        <input type="text" class="form-control" id="edit_department_name" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="edit_fee_type_id" class="form-label">ফি টাইপ</label>
                        <input type="text" class="form-control" id="edit_fee_type_name" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="edit_amount" class="form-label">পরিমাণ (৳)</label>
                        <input type="text" class="form-control" id="edit_amount" name="amount" pattern="[০-৯0-9\.]+" required>
                        <div class="form-text">বাংলা বা ইংরেজি সংখ্যা ব্যবহার করুন (উদাহরণ: ১০০ বা 100)</div>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="edit_is_active" name="is_active">
                        <label class="form-check-label" for="edit_is_active">সক্রিয়</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="submit" name="update_charge" class="btn btn-primary">আপডেট করুন</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Session Charge Modal -->
<div class="modal fade" id="deleteChargeModal" tabindex="-1" aria-labelledby="deleteChargeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteChargeModalLabel"><i class="fas fa-trash-alt me-2"></i> সেশন চার্জ মুছুন</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <input type="hidden" id="delete_charge_id" name="charge_id">
                    <p>আপনি কি নিশ্চিত যে আপনি এই সেশন চার্জ মুছতে চান?</p>
                    <p><strong>সেশন:</strong> <span id="delete_session_name"></span></p>
                    <p><strong>বিভাগ:</strong> <span id="delete_department_name"></span></p>
                    <p><strong>ফি টাইপ:</strong> <span id="delete_fee_type_name"></span></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="submit" name="delete_charge" class="btn btn-danger">মুছুন</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Store fee type amounts
        const feeTypeAmounts = {};

        // Get fee type amounts directly from the options
        function loadFeeTypeAmounts() {
            const feeTypeOptions = document.querySelectorAll('#fee_type_ids option');
            feeTypeOptions.forEach(option => {
                const id = option.value;
                const amount = option.getAttribute('data-amount');
                if (id) {
                    feeTypeAmounts[id] = amount ? parseFloat(amount) : 0;
                }
            });

            // Add event listener to fee type select
            const feeTypeSelect = document.getElementById('fee_type_ids');
            if (feeTypeSelect) {
                feeTypeSelect.addEventListener('change', updateAmount);

                // Initialize select2 if available
                if (typeof $.fn.select2 !== 'undefined') {
                    $(feeTypeSelect).select2({
                        placeholder: 'ফি টাইপ নির্বাচন করুন',
                        allowClear: true
                    });

                    // Initialize session and department select2
                    $('#session_id, #department_id').select2({
                        placeholder: 'নির্বাচন করুন',
                        allowClear: true
                    });
                }
            }
        }

        // Update amount field based on selected fee type
        function updateAmount() {
            const feeTypeSelect = document.getElementById('fee_type_ids');
            const amountField = document.getElementById('amount');

            // If only one fee type is selected, use its amount
            if (feeTypeSelect.selectedOptions.length === 1) {
                const feeTypeId = feeTypeSelect.value;
                if (feeTypeId && feeTypeAmounts[feeTypeId]) {
                    amountField.value = feeTypeAmounts[feeTypeId];
                }
            } else if (feeTypeSelect.selectedOptions.length > 1) {
                // If multiple fee types are selected, clear the amount field
                // The system will use each fee type's default amount
                amountField.value = '';
                amountField.placeholder = 'প্রতিটি ফি টাইপের ডিফল্ট পরিমাণ ব্যবহার করা হবে';
            } else {
                // If no fee type is selected, clear the amount field
                amountField.value = '';
                amountField.placeholder = '';
            }
        }

        // Load fee type amounts
        loadFeeTypeAmounts();

        // Edit session charge
        const editChargeBtns = document.querySelectorAll('.edit-charge-btn');
        editChargeBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const sessionId = this.getAttribute('data-session-id');
                const departmentId = this.getAttribute('data-department-id');
                const feeTypeId = this.getAttribute('data-fee-type-id');
                const amount = this.getAttribute('data-amount');
                const isActive = this.getAttribute('data-is-active') === '1';

                // Get session, department and fee type names
                const sessionName = this.closest('tr').querySelector('td:nth-child(2)').textContent;
                const departmentName = this.closest('tr').querySelector('td:nth-child(3)').textContent;
                const feeTypeName = this.closest('tr').querySelector('td:nth-child(4)').textContent;

                document.getElementById('edit_charge_id').value = id;
                document.getElementById('edit_session_name').value = sessionName;
                document.getElementById('edit_department_name').value = departmentName;
                document.getElementById('edit_fee_type_name').value = feeTypeName;
                document.getElementById('edit_amount').value = amount;
                document.getElementById('edit_is_active').checked = isActive;
            });
        });

        // Delete session charge
        const deleteChargeBtns = document.querySelectorAll('.delete-charge-btn');
        deleteChargeBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const sessionName = this.getAttribute('data-session-name');
                const departmentName = this.getAttribute('data-department-name');
                const feeTypeName = this.getAttribute('data-fee-type-name');

                document.getElementById('delete_charge_id').value = id;
                document.getElementById('delete_session_name').textContent = sessionName;
                document.getElementById('delete_department_name').textContent = departmentName;
                document.getElementById('delete_fee_type_name').textContent = feeTypeName;
            });
        });
    });
</script>

<?php
// Include footer
include_once 'includes/footer.php';
?>
