<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Initialize variables
$successMessage = "";
$errorMessage = "";
$examTableName = "exams_primary_lower";

// Get classes and subjects for form
$classesResult = null;
$subjectsResult = null;
$examsResult = null;

try {
    // Get classes 1-2 (reset result pointer)
    $classesQuery = "SELECT DISTINCT id, class_name FROM classes WHERE class_name IN ('ONE', 'TWO', 'ক্লাস ১', 'ক্লাস ২', '১', '২', '1', '2') ORDER BY class_name";
    $classesResult = $conn->query($classesQuery);

    // Get subjects for classes 1-2 (reset result pointer)
    $subjectsQuery = "SELECT DISTINCT s.id, s.subject_name, s.class_id FROM subjects s
                      INNER JOIN classes c ON s.class_id = c.id
                      WHERE c.class_name IN ('ONE', 'TWO', 'ক্লাস ১', 'ক্লাস ২', '১', '২', '1', '2')
                      ORDER BY s.subject_name";
    $subjectsResult = $conn->query($subjectsQuery);

    // Get recent exams
    $examsQuery = "SELECT * FROM $examTableName ORDER BY created_at DESC LIMIT 10";
    $examsResult = $conn->query($examsQuery);
} catch (Exception $e) {
    // Continue with null values
}

// Note: Student addition removed - use main students.php for adding new students
// This page only shows existing students filtered by class 1-2

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    // Removed add_student functionality - use main students.php instead

    if ($_POST['action'] === 'setup_subjects') {
        try {
            // Get class IDs for class 1 and 2
            $classQuery = "SELECT id, class_name FROM classes WHERE class_name IN ('ক্লাস ১', 'ক্লাস ২', '১', '২', '1', '2')";
            $classResult = $conn->query($classQuery);

            $subjectNames = ['বাংলা', 'ইংরেজি', 'গণিত'];
            $setupCount = 0;

            if ($classResult) {
                while ($class = $classResult->fetch_assoc()) {
                    foreach ($subjectNames as $subjectName) {
                        // Check if subject already exists
                        $checkQuery = "SELECT id FROM subjects WHERE subject_name = '$subjectName' AND class_id = {$class['id']}";
                        $checkResult = $conn->query($checkQuery);

                        if (!$checkResult || $checkResult->num_rows == 0) {
                            // Create subject
                            $insertSubjectQuery = "INSERT INTO subjects (subject_name, class_id, created_at) VALUES ('$subjectName', {$class['id']}, NOW())";
                            if ($conn->query($insertSubjectQuery)) {
                                $setupCount++;
                            }
                        }
                    }
                }
            }

            if ($setupCount > 0) {
                $successMessage = "$setupCount টি বিষয় সফলভাবে সেটআপ করা হয়েছে!";
            } else {
                $successMessage = "সব বিষয় ইতিমধ্যে সেটআপ করা আছে।";
            }
        } catch (Exception $e) {
            $errorMessage = "বিষয় সেটআপ করতে সমস্যা হয়েছে: " . $e->getMessage();
        }
    }
}

// Handle exam deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_exam'])) {
    try {
        $examId = $_POST['exam_id'] ?? '';

        if (!empty($examId)) {
            // Check if table exists
            $tableCheck = $conn->query("SHOW TABLES LIKE '$examTableName'");
            if ($tableCheck && $tableCheck->num_rows > 0) {
                $deleteQuery = "DELETE FROM $examTableName WHERE id = ?";
                $stmt = $conn->prepare($deleteQuery);
                $stmt->bind_param("i", $examId);

                if ($stmt->execute()) {
                    $successMessage = "পরীক্ষা সফলভাবে মুছে ফেলা হয়েছে।";
                    // Refresh exams list
                    $examsResult = $conn->query($examsQuery);
                } else {
                    $errorMessage = "পরীক্ষা মুছতে সমস্যা হয়েছে: " . $conn->error;
                }
            } else {
                $errorMessage = "পরীক্ষা টেবিল পাওয়া যায়নি।";
            }
        } else {
            $errorMessage = "পরীক্ষা ID পাওয়া যায়নি।";
        }
    } catch (Exception $e) {
        $errorMessage = "একটি সমস্যা হয়েছে: " . $e->getMessage();
    }
}

// Handle reset all exams
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reset_all_exams'])) {
    try {
        // Check if table exists
        $tableCheck = $conn->query("SHOW TABLES LIKE '$examTableName'");
        if ($tableCheck && $tableCheck->num_rows > 0) {
            $resetQuery = "DELETE FROM $examTableName";

            if ($conn->query($resetQuery)) {
                $successMessage = "সব পরীক্ষা সফলভাবে মুছে ফেলা হয়েছে।";
                // Refresh exams list
                $examsResult = $conn->query($examsQuery);
            } else {
                $errorMessage = "পরীক্ষা রিসেট করতে সমস্যা হয়েছে: " . $conn->error;
            }
        } else {
            $errorMessage = "পরীক্ষা টেবিল পাওয়া যায়নি।";
        }
    } catch (Exception $e) {
        $errorMessage = "একটি সমস্যা হয়েছে: " . $e->getMessage();
    }
}

// Handle exam creation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_exam'])) {
    try {
        $examName = $_POST['exam_name'] ?? '';
        $examType = $_POST['exam_type'] ?? '';
        $classId = $_POST['class_id'] ?? '';
        $examSession = $_POST['exam_session'] ?? '';
        $subjectId = $_POST['subject_id'] ?? '';
        $examDate = $_POST['exam_date'] ?? '';
        $startTime = $_POST['start_time'] ?? '';
        $endTime = $_POST['end_time'] ?? '';
        $totalMarks = $_POST['total_marks'] ?? 100;
        $passingMarks = $_POST['passing_marks'] ?? 33;
        $instructions = $_POST['instructions'] ?? '';
        $createdBy = $_SESSION['userId'];

        if (!empty($examName) && !empty($examType) && !empty($examDate) && !empty($examSession)) {
            // Check if table exists
            $tableCheck = $conn->query("SHOW TABLES LIKE '$examTableName'");
            if ($tableCheck && $tableCheck->num_rows > 0) {
                $insertQuery = "INSERT INTO $examTableName (exam_name, exam_type, class_id, session, subject_id, exam_date, start_time, end_time, total_marks, passing_marks, instructions, created_by)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                $stmt = $conn->prepare($insertQuery);
                $stmt->bind_param("ssissssiisi", $examName, $examType, $classId, $examSession, $subjectId, $examDate, $startTime, $endTime, $totalMarks, $passingMarks, $instructions, $createdBy);

                if ($stmt->execute()) {
                    $successMessage = "পরীক্ষা সফলভাবে তৈরি হয়েছে: $examName (সেশন: $examSession)";
                    // Refresh exams list
                    $examsResult = $conn->query($examsQuery);
                } else {
                    $errorMessage = "পরীক্ষা তৈরি করতে সমস্যা হয়েছে: " . $conn->error;
                }
            } else {
                $errorMessage = "পরীক্ষা টেবিল পাওয়া যায়নি। <a href='setup_class_1_2_database.php'>এখানে ক্লিক করে সেটআপ করুন</a>";
            }
        } else {
            $errorMessage = "সব প্রয়োজনীয় তথ্য পূরণ করুন (নাম, ধরন, তারিখ, সেশন)";
        }
    } catch (Exception $e) {
        $errorMessage = "একটি সমস্যা হয়েছে: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ক্লাস ১-২ পরীক্ষা ব্যবস্থাপনা</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: #f8f9fa;
        }
        .page-header {
            background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
            border-radius: 0 0 20px 20px;
        }
        .form-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #FF6B6B;
        }
        .btn-primary-custom {
            background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 bg-dark text-white min-vh-100">
                <div class="p-3">
                    <h5>মেনু</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link text-white" href="dashboard.php">
                                <i class="fas fa-home me-2"></i>ড্যাশবোর্ড
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" href="class_based_exam_dashboard.php">
                                <i class="fas fa-layer-group me-2"></i>ক্লাস ভিত্তিক পরীক্ষা
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white active" href="#">
                                <i class="fas fa-child me-2"></i>ক্লাস ১-২ পরীক্ষা
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <!-- Page Header -->
                <div class="page-header">
                    <div class="container">
                        <div class="row align-items-center">
                            <div class="col">
                                <h1 class="mb-2">ক্লাস ১-২ পরীক্ষা ব্যবস্থাপনা</h1>
                                <p class="mb-0 opacity-75">প্রাথমিক নিম্ন শ্রেণীর পরীক্ষা পরিচালনা</p>
                            </div>
                            <div class="col-auto">
                                <a href="class_based_exam_dashboard.php" class="btn btn-light">
                                    <i class="fas fa-arrow-left me-2"></i>ফিরে যান
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="container">
                    <!-- Success/Error Messages -->
                    <?php if (!empty($successMessage)): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i><?php echo $successMessage; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($errorMessage)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i><?php echo $errorMessage; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Statistics Row -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number">
                                    <?php
                                    try {
                                        $tableCheck = $conn->query("SHOW TABLES LIKE '$examTableName'");
                                        if ($tableCheck && $tableCheck->num_rows > 0) {
                                            $totalQuery = "SELECT COUNT(*) as count FROM $examTableName";
                                            $totalResult = $conn->query($totalQuery);
                                            echo $totalResult ? $totalResult->fetch_assoc()['count'] : 0;
                                        } else {
                                            echo "0";
                                        }
                                    } catch (Exception $e) {
                                        echo "0";
                                    }
                                    ?>
                                </div>
                                <div class="text-muted">মোট পরীক্ষা</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number text-success">
                                    <?php
                                    try {
                                        $tableCheck = $conn->query("SHOW TABLES LIKE '$examTableName'");
                                        if ($tableCheck && $tableCheck->num_rows > 0) {
                                            $upcomingQuery = "SELECT COUNT(*) as count FROM $examTableName WHERE exam_date >= CURDATE()";
                                            $upcomingResult = $conn->query($upcomingQuery);
                                            echo $upcomingResult ? $upcomingResult->fetch_assoc()['count'] : 0;
                                        } else {
                                            echo "0";
                                        }
                                    } catch (Exception $e) {
                                        echo "0";
                                    }
                                    ?>
                                </div>
                                <div class="text-muted">আসন্ন পরীক্ষা</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number text-info">
                                    <?php
                                    try {
                                        $tableCheck = $conn->query("SHOW TABLES LIKE '$examTableName'");
                                        if ($tableCheck && $tableCheck->num_rows > 0) {
                                            $completedQuery = "SELECT COUNT(*) as count FROM $examTableName WHERE exam_date < CURDATE()";
                                            $completedResult = $conn->query($completedQuery);
                                            echo $completedResult ? $completedResult->fetch_assoc()['count'] : 0;
                                        } else {
                                            echo "0";
                                        }
                                    } catch (Exception $e) {
                                        echo "0";
                                    }
                                    ?>
                                </div>
                                <div class="text-muted">সম্পন্ন পরীক্ষা</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number text-warning">
                                    <?php
                                    try {
                                        $tableCheck = $conn->query("SHOW TABLES LIKE '$examTableName'");
                                        if ($tableCheck && $tableCheck->num_rows > 0) {
                                            $todayQuery = "SELECT COUNT(*) as count FROM $examTableName WHERE exam_date = CURDATE()";
                                            $todayResult = $conn->query($todayQuery);
                                            echo $todayResult ? $todayResult->fetch_assoc()['count'] : 0;
                                        } else {
                                            echo "0";
                                        }
                                    } catch (Exception $e) {
                                        echo "0";
                                    }
                                    ?>
                                </div>
                                <div class="text-muted">আজকের পরীক্ষা</div>
                            </div>
                        </div>
                    </div>

                    <!-- Create New Exam Form -->
                    <div class="form-section">
                        <h3 class="mb-4"><i class="fas fa-plus-circle me-2"></i>নতুন পরীক্ষা তৈরি করুন</h3>
                        <form method="POST" id="createExamForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="exam_name" class="form-label">পরীক্ষার নাম *</label>
                                        <input type="text" class="form-control" id="exam_name" name="exam_name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="exam_type" class="form-label">পরীক্ষার ধরন *</label>
                                        <select class="form-select" id="exam_type" name="exam_type" required>
                                            <option value="">নির্বাচন করুন</option>
                                            <option value="সাপ্তাহিক পরীক্ষা">সাপ্তাহিক পরীক্ষা</option>
                                            <option value="মাসিক পরীক্ষা">মাসিক পরীক্ষা</option>
                                            <option value="ত্রৈমাসিক পরীক্ষা">ত্রৈমাসিক পরীক্ষা</option>
                                            <option value="অর্ধবার্ষিক পরীক্ষা">অর্ধবার্ষিক পরীক্ষা</option>
                                            <option value="বার্ষিক পরীক্ষা">বার্ষিক পরীক্ষা</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="class_id" class="form-label">ক্লাস *</label>
                                        <select class="form-select" id="class_id" name="class_id" required>
                                            <option value="">ক্লাস নির্বাচন করুন</option>
                                            <?php
                                            if ($classesResult && $classesResult->num_rows > 0) {
                                                // Reset result pointer to beginning
                                                $classesResult->data_seek(0);
                                                $seenClasses = [];
                                                while ($class = $classesResult->fetch_assoc()) {
                                                    // Prevent duplicates
                                                    if (!in_array($class['class_name'], $seenClasses)) {
                                                        echo "<option value='{$class['id']}'>{$class['class_name']}</option>";
                                                        $seenClasses[] = $class['class_name'];
                                                    }
                                                }
                                            }
                                            ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="exam_session" class="form-label">সেশন *</label>
                                        <select class="form-select" id="exam_session" name="exam_session" required>
                                            <option value="">সেশন নির্বাচন করুন</option>
                                            <?php
                                            $currentYear = date('Y');
                                            $nextYear = $currentYear + 1;
                                            $prevYear = $currentYear - 1;

                                            // Generate session options
                                            $sessions = [
                                                "$prevYear-$currentYear",
                                                "$currentYear-$nextYear",
                                                ($currentYear+1) . "-" . ($currentYear+2)
                                            ];

                                            foreach ($sessions as $session) {
                                                $selected = ($session === "$currentYear-$nextYear") ? 'selected' : '';
                                                echo "<option value='$session' $selected>$session</option>";
                                            }
                                            ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="subject_id" class="form-label">বিষয়</label>
                                        <select class="form-select" id="subject_id" name="subject_id">
                                            <option value="">বিষয় নির্বাচন করুন (ঐচ্ছিক)</option>
                                            <?php
                                            if ($subjectsResult && $subjectsResult->num_rows > 0) {
                                                // Reset result pointer to beginning
                                                $subjectsResult->data_seek(0);
                                                $seenSubjects = [];
                                                while ($subject = $subjectsResult->fetch_assoc()) {
                                                    // Prevent duplicates
                                                    if (!in_array($subject['subject_name'], $seenSubjects)) {
                                                        echo "<option value='{$subject['id']}'>{$subject['subject_name']}</option>";
                                                        $seenSubjects[] = $subject['subject_name'];
                                                    }
                                                }
                                            }
                                            ?>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="exam_date" class="form-label">পরীক্ষার তারিখ *</label>
                                        <input type="date" class="form-control" id="exam_date" name="exam_date" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="start_time" class="form-label">শুরুর সময়</label>
                                        <input type="time" class="form-control" id="start_time" name="start_time" value="10:00">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="end_time" class="form-label">শেষের সময়</label>
                                        <input type="time" class="form-control" id="end_time" name="end_time" value="12:00">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="total_marks" class="form-label">মোট নম্বর *</label>
                                        <input type="number" class="form-control" id="total_marks" name="total_marks" value="100" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="passing_marks" class="form-label">পাশের নম্বর *</label>
                                        <input type="number" class="form-control" id="passing_marks" name="passing_marks" value="33" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label for="instructions" class="form-label">নির্দেশনা</label>
                                        <textarea class="form-control" id="instructions" name="instructions" rows="3" placeholder="পরীক্ষার বিশেষ নির্দেশনা লিখুন..."></textarea>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" name="create_exam" class="btn btn-primary-custom">
                                <i class="fas fa-save me-2"></i>পরীক্ষা তৈরি করুন
                            </button>
                        </form>
                    </div>

                    <!-- Recent Exams List -->
                    <div class="form-section">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h3 class="mb-0"><i class="fas fa-list me-2"></i>সাম্প্রতিক পরীক্ষাসমূহ</h3>
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="resetAllExams()" title="সব পরীক্ষা মুছে ফেলুন">
                                <i class="fas fa-trash-alt me-1"></i>সব রিসেট করুন
                            </button>
                        </div>

                        <?php if ($examsResult && $examsResult->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>পরীক্ষার নাম</th>
                                            <th>ধরন</th>
                                            <th>সেশন</th>
                                            <th>তারিখ</th>
                                            <th>সময়</th>
                                            <th>নম্বর</th>
                                            <th>অবস্থা</th>
                                            <th>কার্যক্রম</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($exam = $examsResult->fetch_assoc()): ?>
                                            <tr>
                                                <td><strong><?php echo htmlspecialchars($exam['exam_name']); ?></strong></td>
                                                <td><?php echo htmlspecialchars($exam['exam_type']); ?></td>
                                                <td><span class="badge bg-info"><?php echo htmlspecialchars($exam['session'] ?? 'N/A'); ?></span></td>
                                                <td><?php echo date('d/m/Y', strtotime($exam['exam_date'])); ?></td>
                                                <td>
                                                    <?php
                                                    if ($exam['start_time'] && $exam['end_time']) {
                                                        echo date('H:i', strtotime($exam['start_time'])) . ' - ' . date('H:i', strtotime($exam['end_time']));
                                                    } else {
                                                        echo 'সময় নির্ধারিত নয়';
                                                    }
                                                    ?>
                                                </td>
                                                <td><?php echo $exam['total_marks']; ?> (পাশ: <?php echo $exam['passing_marks']; ?>)</td>
                                                <td>
                                                    <?php
                                                    $examDate = strtotime($exam['exam_date']);
                                                    $today = strtotime(date('Y-m-d'));

                                                    if ($examDate > $today) {
                                                        echo '<span class="badge bg-success">আসন্ন</span>';
                                                    } elseif ($examDate == $today) {
                                                        echo '<span class="badge bg-warning">আজ</span>';
                                                    } else {
                                                        echo '<span class="badge bg-info">সম্পন্ন</span>';
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button class="btn btn-sm btn-outline-primary" onclick="viewExam(<?php echo $exam['id']; ?>)">
                                                            <i class="fas fa-eye"></i> দেখুন
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-success" onclick="generateAttendance(<?php echo $exam['id']; ?>)">
                                                            <i class="fas fa-clipboard-list"></i> হাজিরা
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-warning" onclick="markEntry(<?php echo $exam['id']; ?>)">
                                                            <i class="fas fa-edit"></i> মার্ক এনট্রি
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-info" onclick="generateMarksheet(<?php echo $exam['id']; ?>)">
                                                            <i class="fas fa-file-alt"></i> মার্কশীট
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-secondary" onclick="generateTabulation(<?php echo $exam['id']; ?>)">
                                                            <i class="fas fa-table"></i> টেবুলেশন
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteExam(<?php echo $exam['id']; ?>, '<?php echo htmlspecialchars($exam['exam_name']); ?>')" title="পরীক্ষা মুছে ফেলুন">
                                                            <i class="fas fa-trash"></i> মুছুন
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                এখনো কোন পরীক্ষা তৈরি করা হয়নি। উপরের ফর্ম ব্যবহার করে নতুন পরীক্ষা তৈরি করুন।
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Setup Notice -->
                    <div class="form-section">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>নোট:</strong> যদি database টেবিল সেটআপ না থাকে, তাহলে
                            <a href="setup_class_1_2_database.php" class="alert-link">এখানে ক্লিক করে সেটআপ করুন</a>।
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Student Management Section -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-gradient-info text-white">
                        <h5 class="mb-0"><i class="fas fa-users me-2"></i>ছাত্র/ছাত্রী ব্যবস্থাপনা</h5>
                    </div>
                    <div class="card-body">
                        <!-- Notice for adding new students -->
                        <div class="alert alert-warning mb-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>নতুন ছাত্র/ছাত্রী যোগ করতে:</strong>
                            <a href="students.php" class="btn btn-primary btn-sm ms-2">
                                <i class="fas fa-user-plus me-1"></i>মূল ছাত্র/ছাত্রী পেজে যান
                            </a>
                            <br><small class="text-muted mt-1 d-block">এই পেজে শুধুমাত্র ক্লাস ১ ও ২ এর বিদ্যমান ছাত্র/ছাত্রীদের তালিকা দেখানো হয়।</small>
                        </div>

                        <!-- Student Management Tabs -->
                        <ul class="nav nav-tabs" id="studentTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="student-list-tab" data-bs-toggle="tab" data-bs-target="#student-list" type="button" role="tab">
                                    <i class="fas fa-list me-2"></i>ছাত্র/ছাত্রী তালিকা
                                </button>
                            </li>
                            <!-- Add student tab removed - use main students.php for adding new students -->
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="subjects-tab" data-bs-toggle="tab" data-bs-target="#subjects" type="button" role="tab">
                                    <i class="fas fa-book me-2"></i>বিষয় ব্যবস্থাপনা
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content mt-3" id="studentTabContent">
                            <!-- Student List Tab -->
                            <div class="tab-pane fade show active" id="student-list" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-primary">
                                            <tr>
                                                <th>ক্রমিক</th>
                                                <th>রোল নং</th>
                                                <th>নাম</th>
                                                <th>শ্রেণি</th>
                                                <th>সেশন</th>
                                                <th>যোগদানের তারিখ</th>
                                                <th>কার্যক্রম</th>
                                            </tr>
                                        </thead>
                                        <tbody id="studentTableBody">
                                            <?php
                                            try {
                                                $studentQuery = "SELECT s.id, s.name as student_name, s.roll_number as roll_number, s.session, c.class_name
                               FROM students s
                               LEFT JOIN classes c ON s.class_id = c.id
                               WHERE c.class_name IN ('ONE', 'TWO', 'ক্লাস ১', 'ক্লাস ২', '১', '২', '1', '2')
                               ORDER BY c.class_name, CAST(COALESCE(s.roll_number, s.id) AS UNSIGNED)";
                                                $studentResult = $conn->query($studentQuery);

                                                if ($studentResult && $studentResult->num_rows > 0) {
                                                    $serial = 1;
                                                    while ($student = $studentResult->fetch_assoc()) {
                                                        $name = $student['student_name'] ?? 'নাম নেই';
                                                        $roll = $student['roll_number'] ?? 'N/A';
                                                        $session = $student['session'] ?? 'N/A';
                                                        $joinDate = $student['created_at'] ?? $student['admission_date'] ?? date('Y-m-d');
                                                        echo "<tr>
                                                                <td>{$serial}</td>
                                                                <td><strong>{$roll}</strong></td>
                                                                <td>{$name}</td>
                                                                <td>{$student['class_name']}</td>
                                                                <td><span class='badge bg-info'>{$session}</span></td>
                                                                <td>" . date('d/m/Y', strtotime($joinDate)) . "</td>
                                                                <td>
                                                                    <button class='btn btn-sm btn-outline-primary' onclick='editStudent({$student['id']})'>
                                                                        <i class='fas fa-edit'></i> সম্পাদনা
                                                                    </button>
                                                                </td>
                                                              </tr>";
                                                        $serial++;
                                                    }
                                                } else {
                                                    echo "<tr><td colspan='7' class='text-center text-muted'>কোন ছাত্র/ছাত্রী পাওয়া যায়নি।</td></tr>";
                                                }
                                            } catch (Exception $e) {
                                                echo "<tr><td colspan='7' class='text-center text-muted'>ডেটা লোড করতে সমস্যা হয়েছে।</td></tr>";
                                            }
                                            ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Add Student Tab Removed -->
                            <!-- Use main students.php for adding new students -->
                            <div class="tab-pane fade" id="add-student" role="tabpanel">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>নতুন ছাত্র/ছাত্রী যোগ করতে:</strong>
                                    <a href="students.php" class="btn btn-primary btn-sm ms-2">
                                        <i class="fas fa-user-plus me-1"></i>মূল ছাত্র/ছাত্রী পেজে যান
                                    </a>
                                </div>
                                <p class="text-muted">এই পেজে শুধুমাত্র বিদ্যমান ছাত্র/ছাত্রীদের তালিকা দেখানো হয়। নতুন ছাত্র/ছাত্রী যোগ করতে মূল ছাত্র/ছাত্রী ব্যবস্থাপনা পেজ ব্যবহার করুন।</p>
                            </div>

                            <!-- Subjects Tab -->
                            <div class="tab-pane fade" id="subjects" role="tabpanel">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>স্বয়ংক্রিয় বিষয় সেটআপ:</strong> ক্লাস ১ ও ২ এর জন্য বাংলা, ইংরেজি ও গণিত বিষয় স্বয়ংক্রিয়ভাবে সেট করা হয়েছে।
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead class="table-success">
                                            <tr>
                                                <th>ক্রমিক</th>
                                                <th>বিষয়ের নাম</th>
                                                <th>শ্রেণি</th>
                                                <th>স্ট্যাটাস</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            $defaultSubjects = [
                                                ['name' => 'বাংলা', 'class' => 'ক্লাস ১ ও ২'],
                                                ['name' => 'ইংরেজি', 'class' => 'ক্লাস ১ ও ২'],
                                                ['name' => 'গণিত', 'class' => 'ক্লাস ১ ও ২']
                                            ];

                                            $serial = 1;
                                            foreach ($defaultSubjects as $subject) {
                                                echo "<tr>
                                                        <td>{$serial}</td>
                                                        <td><strong>{$subject['name']}</strong></td>
                                                        <td>{$subject['class']}</td>
                                                        <td><span class='badge bg-success'>সক্রিয়</span></td>
                                                      </tr>";
                                                $serial++;
                                            }
                                            ?>
                                        </tbody>
                                    </table>
                                </div>

                                <button type="button" class="btn btn-primary" onclick="setupSubjects()">
                                    <i class="fas fa-cog me-2"></i>বিষয় সেটআপ নিশ্চিত করুন
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Set minimum date to today
        document.getElementById('exam_date').min = new Date().toISOString().split('T')[0];

        // Auto-calculate passing marks
        document.getElementById('total_marks').addEventListener('input', function() {
            const totalMarks = parseInt(this.value);
            if (totalMarks > 0) {
                const passingMarks = Math.round(totalMarks * 0.33);
                document.getElementById('passing_marks').value = passingMarks;
            }
        });

        // View exam function
        function viewExam(examId) {
            alert('পরীক্ষা ID: ' + examId + '\nবিস্তারিত দেখার ফিচার শীঘ্রই যোগ করা হবে।');
        }

        // Attendance sheet function
        function generateAttendance(examId) {
            window.open('attendance_sheet.php?exam_id=' + examId + '&class_group=primary_lower', '_blank');
        }

        // Mark entry function
        function markEntry(examId) {
            window.location.href = 'mark_entry.php?exam_id=' + examId + '&class_group=primary_lower';
        }

        // Marksheet function
        function generateMarksheet(examId) {
            window.open('marksheet.php?exam_id=' + examId + '&class_group=primary_lower', '_blank');
        }

        // Tabulation function
        function generateTabulation(examId) {
            window.open('tabulation.php?exam_id=' + examId + '&class_group=primary_lower', '_blank');
        }

        // Edit student function
        function editStudent(studentId) {
            alert('ছাত্র/ছাত্রী ID: ' + studentId + '\nসম্পাদনা ফিচার শীঘ্রই যোগ করা হবে।');
        }

        // Setup subjects function
        function setupSubjects() {
            if (confirm('আপনি কি নিশ্চিত যে ক্লাস ১ ও ২ এর জন্য বাংলা, ইংরেজি ও গণিত বিষয় সেটআপ করতে চান?')) {
                // Create a form to submit
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'setup_subjects';
                form.appendChild(actionInput);

                document.body.appendChild(form);
                form.submit();
            }
        }

        // Add student form validation removed - no longer needed

        // Exam creation form validation
        document.getElementById('createExamForm').addEventListener('submit', function(e) {
            const examName = document.getElementById('exam_name').value.trim();
            const examType = document.getElementById('exam_type').value;
            const classId = document.getElementById('class_id').value;
            const examSession = document.getElementById('exam_session').value;
            const examDate = document.getElementById('exam_date').value;

            if (!examName || !examType || !classId || !examSession || !examDate) {
                e.preventDefault();
                alert('অনুগ্রহ করে সব প্রয়োজনীয় তথ্য পূরণ করুন (নাম, ধরন, ক্লাস, সেশন, তারিখ)।');
                return false;
            }

            // Check if exam date is not in the past
            const selectedDate = new Date(examDate);
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (selectedDate < today) {
                e.preventDefault();
                alert('পরীক্ষার তারিখ আজকের তারিখের আগে হতে পারে না।');
                return false;
            }
        });

        // Delete single exam function
        function deleteExam(examId, examName) {
            if (confirm(`আপনি কি নিশ্চিত যে "${examName}" পরীক্ষাটি মুছে ফেলতে চান?\n\nএই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                const examIdInput = document.createElement('input');
                examIdInput.type = 'hidden';
                examIdInput.name = 'exam_id';
                examIdInput.value = examId;

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'delete_exam';
                actionInput.value = '1';

                form.appendChild(examIdInput);
                form.appendChild(actionInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Reset all exams function
        function resetAllExams() {
            if (confirm('আপনি কি নিশ্চিত যে সব পরীক্ষা মুছে ফেলতে চান?\n\nএই কাজটি পূর্বাবস্থায় ফেরানো যাবে না এবং সব পরীক্ষার ডেটা হারিয়ে যাবে।')) {
                if (confirm('সর্বশেষ নিশ্চিতকরণ: সব পরীক্ষা মুছে ফেলা হবে। আপনি কি এগিয়ে যেতে চান?')) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.style.display = 'none';

                    const actionInput = document.createElement('input');
                    actionInput.type = 'hidden';
                    actionInput.name = 'reset_all_exams';
                    actionInput.value = '1';

                    form.appendChild(actionInput);
                    document.body.appendChild(form);
                    form.submit();
                }
            }
        }
    </script>
</body>
</html>

<?php if (isset($conn)) $conn->close(); ?>
