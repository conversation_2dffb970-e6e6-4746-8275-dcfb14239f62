<?php
// Include database connection
require_once "includes/dbh.inc.php";

// Get all teachers with department information
$teachersQuery = "SELECT t.*, d.department_name
                 FROM teachers t
                 LEFT JOIN departments d ON t.department_id = d.id
                 ORDER BY t.first_name, t.last_name";
$teachers = $conn->query($teachersQuery);

// Get departments for filter
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Handle department filter
$selectedDepartment = isset($_GET['department']) ? $_GET['department'] : '';
if (!empty($selectedDepartment)) {
    $teachersQuery = "SELECT t.*, d.department_name
                     FROM teachers t
                     LEFT JOIN departments d ON t.department_id = d.id
                     WHERE t.department_id = $selectedDepartment
                     ORDER BY t.first_name, t.last_name";
    $teachers = $conn->query($teachersQuery);
}

// Get teacher details if ID is provided
$teacherDetails = null;
if (isset($_GET['id'])) {
    $teacherId = $_GET['id'];
    $teacherDetailsQuery = "SELECT t.*, d.department_name
                           FROM teachers t
                           LEFT JOIN departments d ON t.department_id = d.id
                           WHERE t.id = $teacherId";
    $teacherDetailsResult = $conn->query($teacherDetailsQuery);
    if ($teacherDetailsResult && $teacherDetailsResult->num_rows > 0) {
        $teacherDetails = $teacherDetailsResult->fetch_assoc();
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>শিক্ষকবৃন্দ - ZFAW</title>

    <!-- Bootstrap CSS -->
    
    
    

    <style>
        body, html, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, select, textarea, label, li {
            font-family: 'Noto Sans Bengali', 'Hind Siliguri', 'Baloo Da 2', sans-serif;
        }

        h1, h2, h3 {
            font-family: 'Baloo Da 2', 'Hind Siliguri', sans-serif;
            font-weight: 600;
        }

        .header {
            background-color: #006A4E;
            color: white;
            padding: 40px 0;
        }

        .teacher-card {
            transition: transform 0.3s, box-shadow 0.3s;
            height: 100%;
            border: none;
            border-radius: 10px;
            overflow: hidden;
        }

        .teacher-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .teacher-img-container {
            width: 100%;
            height: 250px;
            overflow: hidden;
            border-radius: 50%;
            margin: 0 auto;
            max-width: 250px;
        }

        .teacher-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .teacher-img-sm {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 50%;
        }

        .teacher-details-img-container {
            width: 100%;
            max-width: 300px;
            height: 300px;
            overflow: hidden;
            border-radius: 50%;
            margin: 0 auto;
        }

        .teacher-details-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .teacher-info {
            padding: 20px;
        }

        .teacher-name {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .teacher-designation {
            color: #006A4E;
            font-weight: 500;
            margin-bottom: 10px;
        }

        .teacher-department {
            color: #666;
            font-size: 0.9rem;
        }

        .filter-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .section-title {
            position: relative;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }

        .section-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background-color: #006A4E;
        }

        .contact-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .contact-info i {
            color: #006A4E;
            width: 25px;
        }

        .teacher-details-section {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            padding: 30px;
            margin-bottom: 30px;
        }

        .teacher-details-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .teacher-details-info {
            margin-left: 20px;
        }

        .teacher-details-name {
            font-size: 1.8rem;
            margin-bottom: 5px;
        }

        .teacher-details-designation {
            color: #006A4E;
            font-size: 1.2rem;
            margin-bottom: 5px;
        }

        .teacher-details-department {
            color: #666;
        }

        .info-label {
            font-weight: 600;
            color: #555;
        }

        .back-btn {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">ZFAW</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">হোম</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">বিষয়সমূহ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="teachers.php">শিক্ষকবৃন্দ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">শিক্ষার্থীবৃন্দ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="login.php">লগইন</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-4">আমাদের শিক্ষকবৃন্দ</h1>
                    <p class="lead">আমাদের প্রতিষ্ঠানের অভিজ্ঞ ও যোগ্য শিক্ষকদের সাথে পরিচিত হোন</p>
                </div>
                <div class="col-md-4 text-end">
                    <i class="fas fa-chalkboard-teacher fa-5x text-white-50"></i>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container py-5">
        <?php if ($teacherDetails): ?>
            <!-- Teacher Details View -->
            <div class="back-btn">
                <a href="teachers.php" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i> সকল শিক্ষক দেখুন
                </a>
            </div>

            <div class="teacher-details-section">
                <div class="row">
                    <div class="col-md-4">
                        <div class="teacher-details-img-container">
                            <?php if (!empty($teacherDetails['profile_photo'])): ?>
                                <img src="<?php echo $teacherDetails['profile_photo']; ?>"
                                     alt="<?php echo $teacherDetails['first_name'] . ' ' . $teacherDetails['last_name']; ?>"
                                     class="teacher-details-img">
                            <?php else: ?>
                                <div class="d-flex align-items-center justify-content-center bg-light h-100">
                                    <i class="fas fa-user fa-7x text-secondary"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <h2 class="teacher-details-name"><?php echo $teacherDetails['first_name'] . ' ' . $teacherDetails['last_name']; ?></h2>
                        <p class="teacher-details-designation"><?php echo $teacherDetails['designation'] ?? 'শিক্ষক'; ?></p>
                        <p class="teacher-details-department"><?php echo $teacherDetails['department_name'] ?? 'বিভাগ নির্ধারিত নয়'; ?></p>

                        <div class="row mt-4">
                            <div class="col-md-6 mb-3">
                                <p><span class="info-label">শিক্ষক আইডি:</span> <?php echo $teacherDetails['teacher_id']; ?></p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <p><span class="info-label">বিষয়:</span> <?php echo $teacherDetails['subject'] ?? 'নির্ধারিত নয়'; ?></p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <p><span class="info-label">যোগদানের তারিখ:</span> <?php echo $teacherDetails['joining_date'] ? date('d F, Y', strtotime($teacherDetails['joining_date'])) : 'নির্ধারিত নয়'; ?></p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <p><span class="info-label">লিঙ্গ:</span>
                                    <?php
                                    if ($teacherDetails['gender'] == 'male') echo 'পুরুষ';
                                    elseif ($teacherDetails['gender'] == 'female') echo 'মহিলা';
                                    else echo 'অন্যান্য';
                                    ?>
                                </p>
                            </div>
                        </div>

                        <div class="contact-info">
                            <h4 class="mb-3">যোগাযোগ তথ্য</h4>
                            <?php if (!empty($teacherDetails['email'])): ?>
                                <p><i class="fas fa-envelope me-2"></i> <?php echo $teacherDetails['email']; ?></p>
                            <?php endif; ?>

                            <?php if (!empty($teacherDetails['phone'])): ?>
                                <p><i class="fas fa-phone me-2"></i> <?php echo $teacherDetails['phone']; ?></p>
                            <?php endif; ?>

                            <?php if (!empty($teacherDetails['address'])): ?>
                                <p><i class="fas fa-map-marker-alt me-2"></i> <?php echo $teacherDetails['address']; ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

        <?php else: ?>
            <!-- Teachers List View -->
            <div class="filter-section">
                <h2 class="section-title">শিক্ষক খুঁজুন</h2>
                <form action="" method="get" class="row g-3">
                    <div class="col-md-4">
                        <label for="department" class="form-label">বিভাগ অনুযায়ী</label>
                        <select class="form-select" id="department" name="department">
                            <option value="">সকল বিভাগ</option>
                            <?php if ($departments && $departments->num_rows > 0): ?>
                                <?php while ($dept = $departments->fetch_assoc()): ?>
                                    <option value="<?php echo $dept['id']; ?>" <?php echo $selectedDepartment == $dept['id'] ? 'selected' : ''; ?>>
                                        <?php echo $dept['department_name']; ?>
                                    </option>
                                <?php endwhile; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary">খুঁজুন</button>
                        <?php if (!empty($selectedDepartment)): ?>
                            <a href="teachers.php" class="btn btn-outline-secondary ms-2">ফিল্টার মুছুন</a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>

            <h2 class="section-title">আমাদের শিক্ষকবৃন্দ</h2>

            <div class="row g-4">
                <?php if ($teachers && $teachers->num_rows > 0): ?>
                    <?php while ($teacher = $teachers->fetch_assoc()): ?>
                        <div class="col-md-3">
                            <div class="card teacher-card">
                                <div class="teacher-img-container">
                                    <?php if (!empty($teacher['profile_photo'])): ?>
                                        <img src="<?php echo $teacher['profile_photo']; ?>"
                                             alt="<?php echo $teacher['first_name'] . ' ' . $teacher['last_name']; ?>"
                                             class="teacher-img">
                                    <?php else: ?>
                                        <div class="d-flex align-items-center justify-content-center bg-light h-100">
                                            <i class="fas fa-user fa-5x text-secondary"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="teacher-info">
                                    <h3 class="teacher-name"><?php echo $teacher['first_name'] . ' ' . $teacher['last_name']; ?></h3>
                                    <p class="teacher-designation"><?php echo $teacher['designation'] ?? 'শিক্ষক'; ?></p>
                                    <p class="teacher-department"><?php echo $teacher['department_name'] ?? 'বিভাগ নির্ধারিত নয়'; ?></p>
                                    <a href="teachers.php?id=<?php echo $teacher['id']; ?>" class="btn btn-outline-primary w-100">বিস্তারিত দেখুন</a>
                                </div>
                            </div>
                        </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <div class="col-12">
                        <div class="alert alert-info">
                            কোন শিক্ষক পাওয়া যায়নি।
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>ZFAW School Management System</h5>
                    <p>একটি সম্পূর্ণ শিক্ষা প্রতিষ্ঠান ব্যবস্থাপনা সমাধান</p>
                </div>
                <div class="col-md-3">
                    <h5>দ্রুত লিঙ্ক</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-white">হোম</a></li>
                        <li><a href="subjects.php" class="text-white">বিষয়সমূহ</a></li>
                        <li><a href="teachers.php" class="text-white">শিক্ষকবৃন্দ</a></li>
                        <li><a href="students.php" class="text-white">শিক্ষার্থীবৃন্দ</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>যোগাযোগ</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> ঢাকা, বাংলাদেশ</p>
                        <p><i class="fas fa-phone me-2"></i> +৮৮০১৭১২৩৪৫৬৭৮</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p class="mb-0">&copy; <?php echo date('Y'); ?> ZFAW School Management System. সর্বস্বত্ব সংরক্ষিত।</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Auto-submit form when department is changed
        document.getElementById('department').addEventListener('change', function() {
            this.form.submit();
        });
    </script>
</body>
</html>
