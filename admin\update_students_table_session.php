<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

echo "<!DOCTYPE html>
<html lang='bn'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Students Table Session Update</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap' rel='stylesheet'>
    <style>
        body { font-family: 'Hind Siliguri', sans-serif; background: #f8f9fa; }
        .container { margin-top: 50px; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='card'>
            <div class='card-header bg-primary text-white'>
                <h4><i class='fas fa-database'></i> Students Table Session Column Update</h4>
            </div>
            <div class='card-body'>";

try {
    // Check if session column exists
    $checkColumnQuery = "SHOW COLUMNS FROM students LIKE 'session'";
    $checkResult = $conn->query($checkColumnQuery);
    
    if ($checkResult && $checkResult->num_rows > 0) {
        echo "<p class='text-info'>✓ Session column already exists in students table</p>";
    } else {
        // Add session column
        $addColumnQuery = "ALTER TABLE students ADD COLUMN session VARCHAR(20) DEFAULT NULL AFTER class_id";
        if ($conn->query($addColumnQuery)) {
            echo "<p class='text-success'>✓ Session column added to students table successfully</p>";
        } else {
            echo "<p class='text-danger'>✗ Failed to add session column: " . $conn->error . "</p>";
        }
    }
    
    // Update existing students with current session
    $currentYear = date('Y');
    $nextYear = $currentYear + 1;
    $currentSession = "$currentYear-$nextYear";
    
    // Check if there are students without session
    $checkStudentsQuery = "SELECT COUNT(*) as count FROM students WHERE session IS NULL OR session = ''";
    $checkStudentsResult = $conn->query($checkStudentsQuery);
    
    if ($checkStudentsResult) {
        $row = $checkStudentsResult->fetch_assoc();
        $studentsWithoutSession = $row['count'];
        
        if ($studentsWithoutSession > 0) {
            // Update students without session
            $updateQuery = "UPDATE students SET session = '$currentSession' WHERE session IS NULL OR session = ''";
            if ($conn->query($updateQuery)) {
                echo "<p class='text-success'>✓ Updated $studentsWithoutSession students with current session ($currentSession)</p>";
            } else {
                echo "<p class='text-danger'>✗ Failed to update students session: " . $conn->error . "</p>";
            }
        } else {
            echo "<p class='text-info'>✓ All students already have session assigned</p>";
        }
    }
    
    // Show current students with sessions
    echo "<h5 class='mt-4'>Current Students with Sessions:</h5>";
    $studentsQuery = "SELECT s.*, c.class_name FROM students s 
                     LEFT JOIN classes c ON s.class_id = c.id 
                     WHERE c.class_name IN ('ক্লাস ১', 'ক্লাস ২') 
                     ORDER BY c.class_name, s.roll_number";
    $studentsResult = $conn->query($studentsQuery);
    
    if ($studentsResult && $studentsResult->num_rows > 0) {
        echo "<div class='table-responsive'>
                <table class='table table-striped table-sm'>
                    <thead class='table-primary'>
                        <tr>
                            <th>Roll</th>
                            <th>Name</th>
                            <th>Class</th>
                            <th>Session</th>
                        </tr>
                    </thead>
                    <tbody>";
        
        while ($student = $studentsResult->fetch_assoc()) {
            $name = $student['student_name'] ?? $student['first_name'] ?? 'N/A';
            $roll = $student['roll_number'] ?? $student['student_id'] ?? 'N/A';
            $session = $student['session'] ?? 'N/A';
            $class = $student['class_name'] ?? 'N/A';
            
            echo "<tr>
                    <td><strong>$roll</strong></td>
                    <td>$name</td>
                    <td>$class</td>
                    <td><span class='badge bg-info'>$session</span></td>
                  </tr>";
        }
        
        echo "</tbody></table></div>";
    } else {
        echo "<p class='text-muted'>No students found in Class 1-2</p>";
    }
    
    echo "<div class='alert alert-success mt-4'>
            <h5>Update Complete!</h5>
            <p>Students table has been updated with session column.</p>
            <p>All existing students have been assigned to current session: <strong>$currentSession</strong></p>
          </div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>
            <h5>Error!</h5>
            <p>Failed to update students table: " . $e->getMessage() . "</p>
          </div>";
}

echo "        <div class='mt-4'>
                <a href='class_exam_primary_lower_1_2.php' class='btn btn-primary'>
                    <i class='fas fa-arrow-left'></i> Back to Class 1-2 Dashboard
                </a>
            </div>
        </div>
    </div>
</div>
</body>
</html>";

if (isset($conn)) $conn->close();
?>
