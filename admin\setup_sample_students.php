<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

echo "<!DOCTYPE html>
<html lang='bn'>
<head>
    <meta charset='UTF-8'>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>নমুনা ছাত্র/ছাত্রী সেটআপ</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap' rel='stylesheet'>
    <style>
        body { font-family: 'Hind Siliguri', sans-serif; background: #f8f9fa; }
        .container { margin-top: 50px; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='card'>
            <div class='card-header bg-primary text-white'>
                <h4><i class='fas fa-users'></i> নমুনা ছাত্র/ছাত্রী সেটআপ</h4>
            </div>
            <div class='card-body'>";

try {
    // First ensure we have classes 1 and 2
    $classes = [
        ['name' => 'ক্লাস ১', 'section' => 'A'],
        ['name' => 'ক্লাস ২', 'section' => 'A']
    ];
    
    $classIds = [];
    
    foreach ($classes as $class) {
        // Check if class exists
        $checkClassQuery = "SELECT id FROM classes WHERE class_name = '{$class['name']}'";
        $checkResult = $conn->query($checkClassQuery);
        
        if ($checkResult && $checkResult->num_rows > 0) {
            $classIds[$class['name']] = $checkResult->fetch_assoc()['id'];
            echo "<p class='text-success'>✓ {$class['name']} ইতিমধ্যে আছে</p>";
        } else {
            // Create class
            $insertClassQuery = "INSERT INTO classes (class_name, section, created_at) VALUES ('{$class['name']}', '{$class['section']}', NOW())";
            if ($conn->query($insertClassQuery)) {
                $classIds[$class['name']] = $conn->insert_id;
                echo "<p class='text-success'>✓ {$class['name']} তৈরি করা হয়েছে</p>";
            } else {
                echo "<p class='text-danger'>✗ {$class['name']} তৈরি করতে সমস্যা</p>";
            }
        }
    }
    
    // Create subjects for each class
    $subjects = ['বাংলা', 'ইংরেজি', 'গণিত'];
    
    foreach ($classIds as $className => $classId) {
        foreach ($subjects as $subject) {
            $checkSubjectQuery = "SELECT id FROM subjects WHERE subject_name = '$subject' AND class_id = $classId";
            $checkResult = $conn->query($checkSubjectQuery);
            
            if (!$checkResult || $checkResult->num_rows == 0) {
                $insertSubjectQuery = "INSERT INTO subjects (subject_name, class_id, created_at) VALUES ('$subject', $classId, NOW())";
                if ($conn->query($insertSubjectQuery)) {
                    echo "<p class='text-info'>✓ $className এর জন্য $subject বিষয় তৈরি করা হয়েছে</p>";
                }
            } else {
                echo "<p class='text-muted'>- $className এর $subject বিষয় ইতিমধ্যে আছে</p>";
            }
        }
    }
    
    // Sample students data
    $sampleStudents = [
        // Class 1 students
        ['name' => 'রহিম আহমেদ', 'roll' => '001', 'class' => 'ক্লাস ১', 'father' => 'আব্দুল করিম', 'mother' => 'ফাতেমা বেগম'],
        ['name' => 'করিম হাসান', 'roll' => '002', 'class' => 'ক্লাস ১', 'father' => 'মোহাম্মদ আলী', 'mother' => 'রাহেলা খাতুন'],
        ['name' => 'ফাতেমা খাতুন', 'roll' => '003', 'class' => 'ক্লাস ১', 'father' => 'আব্দুর রহমান', 'mother' => 'সালমা আক্তার'],
        ['name' => 'আয়েশা বেগম', 'roll' => '004', 'class' => 'ক্লাস ১', 'father' => 'মোস্তফা কামাল', 'mother' => 'নাসরিন আক্তার'],
        ['name' => 'মোহাম্মদ আলী', 'roll' => '005', 'class' => 'ক্লাস ১', 'father' => 'আব্দুল হামিদ', 'mother' => 'রোকেয়া বেগম'],
        
        // Class 2 students
        ['name' => 'সালমা আক্তার', 'roll' => '001', 'class' => 'ক্লাস ২', 'father' => 'আব্দুল মজিদ', 'mother' => 'হাসিনা খাতুন'],
        ['name' => 'আব্দুল করিম', 'roll' => '002', 'class' => 'ক্লাস ২', 'father' => 'মোহাম্মদ হাসান', 'mother' => 'আমিনা বেগম'],
        ['name' => 'রাহেলা খাতুন', 'roll' => '003', 'class' => 'ক্লাস ২', 'father' => 'আব্দুস সালাম', 'mother' => 'জরিনা আক্তার'],
        ['name' => 'মোস্তফা কামাল', 'roll' => '004', 'class' => 'ক্লাস ২', 'father' => 'আব্দুর রশিদ', 'mother' => 'শাহিনা বেগম'],
        ['name' => 'নাসরিন আক্তার', 'roll' => '005', 'class' => 'ক্লাস ২', 'father' => 'মোহাম্মদ ইউসুফ', 'mother' => 'রুবিনা খাতুন']
    ];
    
    $addedCount = 0;
    
    foreach ($sampleStudents as $student) {
        $classId = $classIds[$student['class']];
        
        // Check if student already exists
        $checkStudentQuery = "SELECT id FROM students WHERE roll_number = '{$student['roll']}' AND class_id = $classId";
        $checkResult = $conn->query($checkStudentQuery);
        
        if (!$checkResult || $checkResult->num_rows == 0) {
            $insertStudentQuery = "INSERT INTO students (student_name, roll_number, class_id, father_name, mother_name, admission_date, created_at) 
                                  VALUES ('{$student['name']}', '{$student['roll']}', $classId, '{$student['father']}', '{$student['mother']}', CURDATE(), NOW())";
            
            if ($conn->query($insertStudentQuery)) {
                echo "<p class='text-success'>✓ {$student['class']} - {$student['name']} (রোল: {$student['roll']}) যোগ করা হয়েছে</p>";
                $addedCount++;
            } else {
                echo "<p class='text-danger'>✗ {$student['name']} যোগ করতে সমস্যা: " . $conn->error . "</p>";
            }
        } else {
            echo "<p class='text-muted'>- {$student['class']} - {$student['name']} (রোল: {$student['roll']}) ইতিমধ্যে আছে</p>";
        }
    }
    
    echo "<div class='alert alert-success mt-4'>
            <h5>সেটআপ সম্পন্ন!</h5>
            <p>মোট $addedCount জন নতুন ছাত্র/ছাত্রী যোগ করা হয়েছে।</p>
            <p>ক্লাস ১ ও ২ এর জন্য বাংলা, ইংরেজি ও গণিত বিষয় সেটআপ করা হয়েছে।</p>
          </div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>
            <h5>সমস্যা!</h5>
            <p>সেটআপ করতে সমস্যা হয়েছে: " . $e->getMessage() . "</p>
          </div>";
}

echo "        <div class='mt-4'>
                <a href='class_exam_primary_lower_1_2.php' class='btn btn-primary'>
                    <i class='fas fa-arrow-left'></i> ক্লাস ১-২ ড্যাশবোর্ডে ফিরে যান
                </a>
            </div>
        </div>
    </div>
</div>
</body>
</html>";

if (isset($conn)) $conn->close();
?>
