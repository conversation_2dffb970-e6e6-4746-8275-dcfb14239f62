<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Delete the watermark image if it exists
if (file_exists('../uploads/watermark.png')) {
    if (unlink('../uploads/watermark.png')) {
        $_SESSION['success_message'] = 'ওয়াটারমার্ক ছবি সফলভাবে মুছে ফেলা হয়েছে।';
    } else {
        $_SESSION['error_message'] = 'ওয়াটারমার্ক ছবি মুছতে সমস্যা হয়েছে। অনুগ্রহ করে আবার চেষ্টা করুন।';
    }
} else {
    $_SESSION['error_message'] = 'ওয়াটারমার্ক ছবি পাওয়া যায়নি।';
}

// Redirect back to the upload page
header("Location: upload_watermark.php");
exit();
?>
