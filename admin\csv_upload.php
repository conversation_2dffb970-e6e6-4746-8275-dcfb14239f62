<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Handle CSV template download
if (isset($_GET['download_template'])) {
    $filename = 'student_template_' . date('Y-m-d') . '.csv';

    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');

    $output = fopen('php://output', 'w');

    // Add UTF-8 BOM for proper Excel support
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

    // Headers (removed username/password for now)
    $headers = ['student_id', 'roll_number', 'first_name', 'last_name', 'email', 'phone', 'address', 'dob', 'gender', 'batch', 'admission_date', 'department_id', 'class_id', 'session_id', 'guardian_name', 'guardian_relation', 'guardian_phone', 'guardian_email', 'guardian_address', 'guardian_occupation', 'father_name', 'father_phone', 'father_email', 'father_occupation', 'father_income', 'mother_name', 'mother_phone', 'mother_email', 'mother_occupation', 'mother_income'];

    // Sample data (removed username/password)
    $sample_data = [
        'STD-123456', '001', 'রহিম', 'আহমেদ', '<EMAIL>', '01712345678',
        'ঢাকা, বাংলাদেশ', '2000-01-15', 'Male', '2024', '2024-01-01', '1', '1', '1',
        'করিম আহমেদ', 'Father', '01712345679', '<EMAIL>', 'ঢাকা, বাংলাদেশ', 'ব্যবসায়ী',
        'করিম আহমেদ', '01712345679', '<EMAIL>', 'ব্যবসায়ী', '50000',
        'ফাতেমা বেগম', '01712345680', '<EMAIL>', 'গৃহিণী', '0'
    ];

    $sample_data_2 = [
        'STD-123457', '002', 'John', 'Doe', '<EMAIL>', '01812345678',
        'Dhaka, Bangladesh', '2001-03-20', 'Male', '2024', '2024-01-01', '1', '1', '1',
        'Robert Doe', 'Father', '01812345679', '<EMAIL>', 'Dhaka, Bangladesh', 'Engineer',
        'Robert Doe', '01812345679', '<EMAIL>', 'Engineer', '60000',
        'Mary Doe', '01812345680', '<EMAIL>', 'Teacher', '30000'
    ];

    // Write headers
    fputcsv($output, $headers);

    // Write sample data
    fputcsv($output, $sample_data);
    fputcsv($output, $sample_data_2);

    fclose($output);
    exit();
}

$errorMessage = '';
$successMessage = '';
$preview_data = [];

// Check for messages from session
if (isset($_SESSION['csv_success'])) {
    $successMessage = $_SESSION['csv_success'];
    unset($_SESSION['csv_success']);
}
if (isset($_SESSION['csv_error'])) {
    $errorMessage = $_SESSION['csv_error'];
    unset($_SESSION['csv_error']);
}

// Helper function to fix date formats
function fixDateFormat($date) {
    if (empty($date)) return $date;
    
    // Remove any extra spaces
    $date = trim($date);
    
    // Handle Excel date formats like "15-Jan-2000", "20-Mar-2001"
    if (preg_match('/^(\d{1,2})-([A-Za-z]{3})-(\d{2,4})$/', $date, $matches)) {
        $day = $matches[1];
        $month = $matches[2];
        $year = $matches[3];
        
        // Convert month name to number
        $months = [
            'Jan' => '01', 'Feb' => '02', 'Mar' => '03', 'Apr' => '04',
            'May' => '05', 'Jun' => '06', 'Jul' => '07', 'Aug' => '08',
            'Sep' => '09', 'Oct' => '10', 'Nov' => '11', 'Dec' => '12'
        ];
        
        if (isset($months[$month])) {
            // Handle 2-digit years
            if (strlen($year) == 2) {
                $year = (intval($year) < 50) ? '20' . $year : '19' . $year;
            }
            return sprintf('%04d-%02d-%02d', $year, $months[$month], $day);
        }
    }
    
    // Try different date formats and convert to YYYY-MM-DD
    $formats = [
        'd-m-y', 'd-m-Y', 'd/m/y', 'd/m/Y',
        'm-d-y', 'm-d-Y', 'm/d/y', 'm/d/Y',
        'Y-m-d', 'Y/m/d'
    ];
    
    foreach ($formats as $format) {
        $dateObj = DateTime::createFromFormat($format, $date);
        if ($dateObj !== false) {
            $year = $dateObj->format('Y');
            if ($year < 1950) {
                $dateObj->modify('+100 years');
            }
            return $dateObj->format('Y-m-d');
        }
    }
    
    return $date;
}

// Handle CSV preview
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['preview_csv'])) {
    if (isset($_FILES['csv_file']) && $_FILES['csv_file']['error'] === UPLOAD_ERR_OK) {
        $file_extension = strtolower(pathinfo($_FILES['csv_file']['name'], PATHINFO_EXTENSION));
        
        if ($file_extension !== 'csv') {
            $errorMessage = 'অবৈধ ফাইল টাইপ। শুধুমাত্র CSV ফাইল অনুমোদিত।';
        } else {
            $csv_file = $_FILES['csv_file']['tmp_name'];
            
            // Read file content and handle UTF-8 BOM
            $content = file_get_contents($csv_file);
            if (substr($content, 0, 3) === "\xEF\xBB\xBF") {
                $content = substr($content, 3);
            }
            
            // Create temporary file with cleaned content
            $temp_file = tempnam(sys_get_temp_dir(), 'csv_preview');
            file_put_contents($temp_file, $content);
            
            $handle = fopen($temp_file, 'r');
            
            if ($handle !== FALSE) {
                $headers = fgetcsv($handle);
                
                // Clean BOM from first header if present
                if (!empty($headers[0])) {
                    $headers[0] = preg_replace('/^\xEF\xBB\xBF/', '', $headers[0]);
                    $headers[0] = trim($headers[0], "\xEF\xBB\xBF\x00..\x20");
                }
                
                $row_count = 0;
                while (($data = fgetcsv($handle)) !== FALSE && $row_count < 5) {
                    if (empty(array_filter($data))) continue;
                    
                    // Map data to variables
                    $student_id = !empty(trim($data[0])) ? trim($data[0]) : '';
                    $first_name = !empty(trim($data[2])) ? trim($data[2]) : '';
                    $last_name = !empty(trim($data[3])) ? trim($data[3]) : '';
                    $phone = !empty(trim($data[5])) ? trim($data[5]) : '';
                    $address = !empty(trim($data[6])) ? trim($data[6]) : '';
                    $dob = !empty(trim($data[7])) ? trim($data[7]) : '';
                    $gender = !empty(trim($data[8])) ? trim($data[8]) : '';
                    $batch = !empty(trim($data[9])) ? trim($data[9]) : '';
                    $admission_date = !empty(trim($data[10])) ? trim($data[10]) : '';
                    $department_id = !empty(trim($data[11])) ? intval($data[11]) : 0;
                    
                    // Auto-fix phone number
                    if (!empty($phone)) {
                        $phone = preg_replace('/[^0-9]/', '', $phone);
                        if (!preg_match('/^01/', $phone) && strlen($phone) == 9) {
                            $phone = '01' . $phone;
                        }
                        if (preg_match('/^1[3-9]\d{8}$/', $phone)) {
                            $phone = '0' . $phone;
                        }
                    }
                    
                    // Auto-fix date formats
                    if (!empty($dob)) {
                        $dob = fixDateFormat($dob);
                    }
                    if (!empty($admission_date)) {
                        $admission_date = fixDateFormat($admission_date);
                    }
                    
                    $mandatory_fields = [
                        'student_id' => $student_id,
                        'first_name' => $first_name,
                        'last_name' => $last_name,
                        'phone' => $phone,
                        'address' => $address,
                        'dob' => $dob,
                        'gender' => $gender,
                        'batch' => $batch,
                        'admission_date' => $admission_date,
                        'department_id' => $department_id
                    ];
                    
                    $missing_fields = [];
                    foreach ($mandatory_fields as $field_name => $field_value) {
                        if (empty($field_value)) {
                            $missing_fields[] = $field_name;
                        }
                    }
                    
                    $preview_data[] = [
                        'row' => $row_count + 2,
                        'data' => $mandatory_fields,
                        'missing' => $missing_fields,
                        'valid' => empty($missing_fields)
                    ];
                    
                    $row_count++;
                }
                
                fclose($handle);
                unlink($temp_file);
            } else {
                $errorMessage = 'CSV ফাইল পড়তে সমস্যা হয়েছে।';
            }
        }
    } else {
        $errorMessage = 'ফাইল আপলোড করতে সমস্যা হয়েছে।';
    }
}

// Handle CSV upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['upload_csv'])) {
    // Include the upload logic from add_student.php here
    // For now, redirect to add_student.php with the file
    if (isset($_FILES['csv_file']) && $_FILES['csv_file']['error'] === UPLOAD_ERR_OK) {
        // Store file in session and redirect
        $temp_path = tempnam(sys_get_temp_dir(), 'csv_upload_');
        move_uploaded_file($_FILES['csv_file']['tmp_name'], $temp_path);
        $_SESSION['csv_temp_file'] = $temp_path;
        $_SESSION['csv_filename'] = $_FILES['csv_file']['name'];
        
        header("Location: process_csv.php");
        exit();
    } else {
        $errorMessage = 'ফাইল আপলোড করতে সমস্যা হয়েছে।';
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>CSV আপলোড - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/hind-siliguri.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include('includes/sidebar.php'); ?>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">📁 CSV আপলোড</h1>
                    <div>
                        <a href="students.php" class="btn btn-outline-primary">
                            <i class="fas fa-list me-1"></i> শিক্ষার্থী তালিকা
                        </a>
                    </div>
                </div>
                
                <!-- Messages -->
                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>ত্রুটি!</strong> <?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>সফল!</strong> <?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Important Notice -->
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>নোট:</strong> বর্তমানে username এবং password ফিল্ড সাপোর্ট করা হয় না। শুধুমাত্র শিক্ষার্থীর মূল তথ্য আপলোড করা হবে।
                </div>

                <!-- Upload Form -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-upload me-2"></i>CSV ফাইল আপলোড</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="csv_file" class="form-label">CSV ফাইল নির্বাচন করুন</label>
                                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                                        <div class="form-text">শুধুমাত্র .csv ফাইল সাপোর্টেড</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid gap-2">
                                        <button type="submit" name="preview_csv" class="btn btn-info">
                                            <i class="fas fa-eye me-2"></i>প্রিভিউ দেখুন
                                        </button>
                                        <button type="submit" name="upload_csv" class="btn btn-success">
                                            <i class="fas fa-upload me-2"></i>আপলোড করুন
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                        
                        <div class="mt-3">
                            <a href="?download_template=1" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-download me-1"></i>টেমপ্লেট ডাউনলোড
                            </a>
                            <a href="csv_helper.php" class="btn btn-outline-info">
                                <i class="fas fa-info-circle me-1"></i>সহায়তা
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Preview Section -->
                <?php if (!empty($preview_data)): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-eye me-2"></i>CSV প্রিভিউ (প্রথম ৫ সারি)</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="table-dark">
                                    <tr>
                                        <th>সারি</th>
                                        <th>শিক্ষার্থী ID</th>
                                        <th>নাম</th>
                                        <th>ফোন</th>
                                        <th>ঠিকানা</th>
                                        <th>জন্ম তারিখ</th>
                                        <th>ব্যাচ</th>
                                        <th>স্ট্যাটাস</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($preview_data as $row): ?>
                                    <tr class="<?php echo $row['valid'] ? 'table-success' : 'table-danger'; ?>">
                                        <td><?php echo $row['row']; ?></td>
                                        <td><?php echo htmlspecialchars($row['data']['student_id']); ?></td>
                                        <td><?php echo htmlspecialchars($row['data']['first_name'] . ' ' . $row['data']['last_name']); ?></td>
                                        <td><?php echo htmlspecialchars($row['data']['phone']); ?></td>
                                        <td><?php echo htmlspecialchars(substr($row['data']['address'], 0, 30)) . (strlen($row['data']['address']) > 30 ? '...' : ''); ?></td>
                                        <td><?php echo htmlspecialchars($row['data']['dob']); ?></td>
                                        <td><?php echo htmlspecialchars($row['data']['batch']); ?></td>
                                        <td>
                                            <?php if ($row['valid']): ?>
                                                <span class="badge bg-success">✅ বৈধ</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">❌ অবৈধ</span>
                                                <br><small class="text-danger">অনুপস্থিত: <?php echo implode(', ', $row['missing']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="mt-3">
                            <div class="alert alert-info">
                                <strong>📊 সারসংক্ষেপ:</strong>
                                মোট <?php echo count($preview_data); ?> সারি প্রিভিউ করা হয়েছে।
                                <?php 
                                $valid_count = array_sum(array_column($preview_data, 'valid'));
                                $invalid_count = count($preview_data) - $valid_count;
                                ?>
                                <span class="badge bg-success"><?php echo $valid_count; ?> বৈধ</span>
                                <span class="badge bg-danger"><?php echo $invalid_count; ?> অবৈধ</span>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
