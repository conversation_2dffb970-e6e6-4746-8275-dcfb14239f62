<?php
// Read the file
$content = file_get_contents('detailed_marks_entry.php');
$lines = explode("\n", $content);

// Find all occurrences of the function declaration
$lineNumbers = [];
foreach ($lines as $i => $line) {
    if (strpos($line, 'function getStudentsByClass') !== false) {
        $lineNumbers[] = $i + 1; // Line numbers are 1-based
    }
}

echo "Found function declaration on lines: " . implode(", ", $lineNumbers) . "\n";

// Print a snippet around each line
foreach ($lineNumbers as $lineNum) {
    $start = max(1, $lineNum - 2);
    $end = min(count($lines), $lineNum + 5);
    
    echo "\nContext around line $lineNum:\n";
    for ($i = $start - 1; $i < $end; $i++) {
        echo ($i + 1) . ": " . $lines[$i] . "\n";
    }
}
?> 