<?php
// This script checks if an automatic backup is needed and triggers it
// It should be included in the admin dashboard pages

// Check if auto backup is configured
$configFile = '../config/backup_config.php';
if (file_exists($configFile)) {
    include $configFile;
    
    if (isset($config['auto_backup_enabled']) && $config['auto_backup_enabled']) {
        $lastBackupTime = isset($config['last_backup_time']) ? $config['last_backup_time'] : 0;
        $currentTime = time();
        $interval = isset($config['auto_backup_interval']) ? $config['auto_backup_interval'] : 'daily';
        
        $shouldBackup = false;
        
        switch ($interval) {
            case 'daily':
                // Check if last backup was more than 24 hours ago
                if (($currentTime - $lastBackupTime) > (24 * 60 * 60)) {
                    $shouldBackup = true;
                }
                break;
            case 'weekly':
                // Check if last backup was more than 7 days ago
                if (($currentTime - $lastBackupTime) > (7 * 24 * 60 * 60)) {
                    $shouldBackup = true;
                }
                break;
            case 'monthly':
                // Check if last backup was more than 30 days ago
                if (($currentTime - $lastBackupTime) > (30 * 24 * 60 * 60)) {
                    $shouldBackup = true;
                }
                break;
        }
        
        if ($shouldBackup) {
            // Trigger auto backup via AJAX
            echo '<script>
                document.addEventListener("DOMContentLoaded", function() {
                    // Create a hidden iframe to run the backup script
                    var iframe = document.createElement("iframe");
                    iframe.style.display = "none";
                    iframe.src = "../includes/auto_backup.php";
                    document.body.appendChild(iframe);
                    
                    // Remove iframe after backup is complete
                    iframe.onload = function() {
                        setTimeout(function() {
                            document.body.removeChild(iframe);
                        }, 5000);
                    };
                });
            </script>';
        }
    }
}
?>
