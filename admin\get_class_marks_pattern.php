<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    echo '<div class="alert alert-danger">অননুমোদিত প্রবেশ!</div>';
    exit();
}

require_once '../includes/dbh.inc.php';
require_once 'class_subjects_helper.php';

$classId = isset($_POST['class_id']) ? intval($_POST['class_id']) : 0;
$departmentId = isset($_POST['department_id']) && !empty($_POST['department_id']) ? intval($_POST['department_id']) : null;
$templateId = isset($_POST['template_id']) && !empty($_POST['template_id']) ? $_POST['template_id'] : null;

if (!$classId) {
    echo '<div class="alert alert-danger">ক্লাস আইডি প্রয়োজন!</div>';
    exit();
}

try {
    // Get template data if specified
    $templateData = null;
    if ($templateId) {
        $templateQuery = "SELECT * FROM class_level_templates WHERE class_level = ? AND is_active = 1";
        $stmt = $conn->prepare($templateQuery);
        $stmt->bind_param("s", $templateId);
        $stmt->execute();
        $templateResult = $stmt->get_result();
        $templateData = $templateResult->num_rows > 0 ? $templateResult->fetch_assoc() : null;
    }

    // Get class name
    $classQuery = "SELECT class_name FROM classes WHERE id = ?";
    $stmt = $conn->prepare($classQuery);
    $stmt->bind_param("i", $classId);
    $stmt->execute();
    $classResult = $stmt->get_result();
    $className = $classResult->num_rows > 0 ? $classResult->fetch_assoc()['class_name'] : 'অজানা ক্লাস';

    // Get department name if specified
    $departmentName = 'সকল বিভাগ';
    if ($departmentId) {
        $deptQuery = "SELECT department_name FROM departments WHERE id = ?";
        $stmt = $conn->prepare($deptQuery);
        $stmt->bind_param("i", $departmentId);
        $stmt->execute();
        $deptResult = $stmt->get_result();
        $departmentName = $deptResult->num_rows > 0 ? $deptResult->fetch_assoc()['department_name'] : 'অজানা বিভাগ';
    }

    // Get class subjects
    $classSubjects = getClassSubjects($classId, $departmentId);
    
    if (empty($classSubjects)) {
        echo '<div class="alert alert-warning">এই ক্লাসের জন্য কোন বিষয় কনফিগার করা হয়নি। প্রথমে বিষয় কনফিগার করুন।</div>';
        exit();
    }

    // Get existing exam patterns for these subjects
    $subjectIds = array_column($classSubjects, 'subject_id');
    $placeholders = str_repeat('?,', count($subjectIds) - 1) . '?';
    
    $patternsQuery = "SELECT * FROM subject_exam_pattern WHERE subject_id IN ($placeholders)";
    $stmt = $conn->prepare($patternsQuery);
    $stmt->bind_param(str_repeat('i', count($subjectIds)), ...$subjectIds);
    $stmt->execute();
    $patternsResult = $stmt->get_result();
    
    $existingPatterns = [];
    while ($pattern = $patternsResult->fetch_assoc()) {
        $existingPatterns[$pattern['subject_id']] = $pattern;
    }

    echo '<div class="alert alert-info">';
    echo '<h6><i class="fas fa-info-circle me-2"></i>ক্লাস: ' . htmlspecialchars($className) . ' - ' . htmlspecialchars($departmentName) . '</h6>';
    if ($templateData) {
        echo '<p class="mb-1"><strong>ব্যবহৃত টেমপ্লেট:</strong> ' . htmlspecialchars($templateData['class_level_name']) . '</p>';
        echo '<p class="mb-0"><small class="text-muted">' . htmlspecialchars($templateData['description']) . '</small></p>';
    } else {
        echo '<p class="mb-0">নিচের বিষয়গুলির জন্য মার্কস প্যাটার্ন নির্ধারণ করুন:</p>';
    }
    echo '</div>';

    echo '<div class="row">';
    
    foreach ($classSubjects as $subject) {
        $subjectId = $subject['subject_id'];
        $existingPattern = isset($existingPatterns[$subjectId]) ? $existingPatterns[$subjectId] : null;
        
        // Default values - use template if available, otherwise existing pattern, otherwise defaults
        if ($existingPattern) {
            // Use existing pattern
            $hasCq = $existingPattern['has_cq'];
            $hasMcq = $existingPattern['has_mcq'];
            $hasPractical = $existingPattern['has_practical'];
            $cqMarks = $existingPattern['cq_marks'];
            $mcqMarks = $existingPattern['mcq_marks'];
            $practicalMarks = $existingPattern['practical_marks'];
            $totalMarks = $existingPattern['total_marks'];
        } elseif ($templateData) {
            // Use template data
            $hasCq = $templateData['has_cq'];
            $hasMcq = $templateData['has_mcq'];
            $hasPractical = $templateData['has_practical'];
            $cqMarks = $templateData['default_cq_marks'];
            $mcqMarks = $templateData['default_mcq_marks'];
            $practicalMarks = $templateData['default_practical_marks'];
            $totalMarks = $templateData['default_total_marks'];
        } else {
            // Use system defaults
            $hasCq = 1;
            $hasMcq = 1;
            $hasPractical = 0;
            $cqMarks = 70;
            $mcqMarks = 30;
            $practicalMarks = 0;
            $totalMarks = 100;
        }
        
        $subjectTypeClass = '';
        switch ($subject['subject_type']) {
            case 'required':
                $subjectTypeClass = 'border-success';
                break;
            case 'optional':
                $subjectTypeClass = 'border-info';
                break;
            case 'fourth':
                $subjectTypeClass = 'border-warning';
                break;
        }
        
        echo '<div class="col-md-6 mb-4">';
        echo '<div class="card ' . $subjectTypeClass . ' marks-pattern-form">';
        echo '<div class="card-header">';
        echo '<h6 class="mb-0">';
        echo '<i class="fas fa-book me-2"></i>' . htmlspecialchars($subject['subject_name']);
        echo '<span class="badge bg-secondary ms-2">' . htmlspecialchars($subject['subject_code']) . '</span>';
        
        // Subject type badge
        $typeText = '';
        $typeBadgeClass = '';
        switch ($subject['subject_type']) {
            case 'required':
                $typeText = 'আবশ্যিক';
                $typeBadgeClass = 'bg-success';
                break;
            case 'optional':
                $typeText = 'ঐচ্ছিক';
                $typeBadgeClass = 'bg-info';
                break;
            case 'fourth':
                $typeText = 'চতুর্থ বিষয়';
                $typeBadgeClass = 'bg-warning';
                break;
        }
        echo '<span class="badge ' . $typeBadgeClass . ' ms-2">' . $typeText . '</span>';
        echo '</h6>';
        echo '</div>';
        
        echo '<div class="card-body">';
        echo '<input type="hidden" name="subject_id" value="' . $subjectId . '">';
        
        // Component toggles
        echo '<div class="row mb-3">';
        echo '<div class="col-md-4">';
        echo '<div class="form-check">';
        echo '<input class="form-check-input" type="checkbox" name="has_cq" id="has_cq_' . $subjectId . '" ' . ($hasCq ? 'checked' : '') . '>';
        echo '<label class="form-check-label" for="has_cq_' . $subjectId . '">সৃজনশীল প্রশ্ন</label>';
        echo '</div>';
        echo '</div>';
        
        echo '<div class="col-md-4">';
        echo '<div class="form-check">';
        echo '<input class="form-check-input" type="checkbox" name="has_mcq" id="has_mcq_' . $subjectId . '" ' . ($hasMcq ? 'checked' : '') . '>';
        echo '<label class="form-check-label" for="has_mcq_' . $subjectId . '">বহুনির্বাচনি</label>';
        echo '</div>';
        echo '</div>';
        
        echo '<div class="col-md-4">';
        echo '<div class="form-check">';
        echo '<input class="form-check-input" type="checkbox" name="has_practical" id="has_practical_' . $subjectId . '" ' . ($hasPractical ? 'checked' : '') . '>';
        echo '<label class="form-check-label" for="has_practical_' . $subjectId . '">ব্যবহারিক</label>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        
        // Marks inputs
        echo '<div class="row">';
        echo '<div class="col-md-3">';
        echo '<label class="form-label">সৃজনশীল নম্বর</label>';
        echo '<input type="number" class="form-control" name="cq_marks" value="' . $cqMarks . '" min="0" step="0.01" ' . (!$hasCq ? 'disabled' : '') . '>';
        echo '</div>';
        
        echo '<div class="col-md-3">';
        echo '<label class="form-label">বহুনির্বাচনি নম্বর</label>';
        echo '<input type="number" class="form-control" name="mcq_marks" value="' . $mcqMarks . '" min="0" step="0.01" ' . (!$hasMcq ? 'disabled' : '') . '>';
        echo '</div>';
        
        echo '<div class="col-md-3">';
        echo '<label class="form-label">ব্যবহারিক নম্বর</label>';
        echo '<input type="number" class="form-control" name="practical_marks" value="' . $practicalMarks . '" min="0" step="0.01" ' . (!$hasPractical ? 'disabled' : '') . '>';
        echo '</div>';
        
        echo '<div class="col-md-3">';
        echo '<label class="form-label">মোট নম্বর</label>';
        echo '<input type="number" class="form-control" name="total_marks" value="' . $totalMarks . '" min="1" step="0.01">';
        echo '</div>';
        echo '</div>';
        
        // Sum display
        echo '<div class="mt-3">';
        echo '<div class="alert alert-light">';
        echo '<strong>যোগফল: </strong><span class="marks-sum">' . ($cqMarks + $mcqMarks + $practicalMarks) . '</span>';
        echo ' / <span class="total-marks">' . $totalMarks . '</span>';
        echo '</div>';
        echo '</div>';
        
        echo '</div>'; // card-body
        echo '</div>'; // card
        echo '</div>'; // col
    }
    
    echo '</div>'; // row

    // Add JavaScript for dynamic calculation
    echo '<script>';
    echo 'document.querySelectorAll(".marks-pattern-form").forEach(form => {';
    echo '    const inputs = form.querySelectorAll("input[type=number]");';
    echo '    const checkboxes = form.querySelectorAll("input[type=checkbox]");';
    echo '    ';
    echo '    function updateSum() {';
    echo '        const cqMarks = parseFloat(form.querySelector("input[name=cq_marks]").value) || 0;';
    echo '        const mcqMarks = parseFloat(form.querySelector("input[name=mcq_marks]").value) || 0;';
    echo '        const practicalMarks = parseFloat(form.querySelector("input[name=practical_marks]").value) || 0;';
    echo '        const totalMarks = parseFloat(form.querySelector("input[name=total_marks]").value) || 0;';
    echo '        ';
    echo '        const hasCq = form.querySelector("input[name=has_cq]").checked;';
    echo '        const hasMcq = form.querySelector("input[name=has_mcq]").checked;';
    echo '        const hasPractical = form.querySelector("input[name=has_practical]").checked;';
    echo '        ';
    echo '        let sum = 0;';
    echo '        if (hasCq) sum += cqMarks;';
    echo '        if (hasMcq) sum += mcqMarks;';
    echo '        if (hasPractical) sum += practicalMarks;';
    echo '        ';
    echo '        const sumSpan = form.querySelector(".marks-sum");';
    echo '        const totalSpan = form.querySelector(".total-marks");';
    echo '        const alertDiv = sumSpan.closest(".alert");';
    echo '        ';
    echo '        sumSpan.textContent = sum.toFixed(2);';
    echo '        totalSpan.textContent = totalMarks.toFixed(2);';
    echo '        ';
    echo '        if (Math.abs(sum - totalMarks) < 0.01) {';
    echo '            alertDiv.className = "alert alert-success";';
    echo '        } else {';
    echo '            alertDiv.className = "alert alert-warning";';
    echo '        }';
    echo '    }';
    echo '    ';
    echo '    function toggleInput(checkbox) {';
    echo '        const name = checkbox.name.replace("has_", "") + "_marks";';
    echo '        const input = form.querySelector(`input[name="${name}"]`);';
    echo '        input.disabled = !checkbox.checked;';
    echo '        if (!checkbox.checked) input.value = 0;';
    echo '        updateSum();';
    echo '    }';
    echo '    ';
    echo '    inputs.forEach(input => input.addEventListener("input", updateSum));';
    echo '    checkboxes.forEach(checkbox => {';
    echo '        checkbox.addEventListener("change", () => toggleInput(checkbox));';
    echo '        toggleInput(checkbox);';
    echo '    });';
    echo '    ';
    echo '    updateSum();';
    echo '});';
    echo '</script>';

} catch (Exception $e) {
    echo '<div class="alert alert-danger">';
    echo '<i class="fas fa-exclamation-circle me-2"></i>ডেটা লোড করতে সমস্যা হয়েছে: ' . htmlspecialchars($e->getMessage());
    echo '</div>';
}
?>
