/* Hind Siliguri Font CSS for Admin Pages */

/* Import Hind Siliguri font from Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap');

/* Apply Hind Siliguri font to all elements */
body, html, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, select, textarea, label, li, table, th, td {
    font-family: 'Hind Siliguri', sans-serif !important;
}

/* Font weights */
.font-light {
    font-weight: 300 !important;
}

.font-regular {
    font-weight: 400 !important;
}

.font-medium {
    font-weight: 500 !important;
}

.font-semibold {
    font-weight: 600 !important;
}

.font-bold {
    font-weight: 700 !important;
}

/* Font sizes */
.text-xs {
    font-size: 0.75rem !important;
}

.text-sm {
    font-size: 0.875rem !important;
}

.text-base {
    font-size: 1rem !important;
}

.text-lg {
    font-size: 1.125rem !important;
}

.text-xl {
    font-size: 1.25rem !important;
}

.text-2xl {
    font-size: 1.5rem !important;
}

.text-3xl {
    font-size: 1.875rem !important;
}

.text-4xl {
    font-size: 2.25rem !important;
}

/* Specific elements */
.sidebar, .sidebar .nav-link {
    font-family: 'Hind Siliguri', sans-serif !important;
}

.main-content {
    font-family: 'Hind Siliguri', sans-serif !important;
}

.card-title, .card-text {
    font-family: 'Hind Siliguri', sans-serif !important;
}

.btn {
    font-family: 'Hind Siliguri', sans-serif !important;
}

.table {
    font-family: 'Hind Siliguri', sans-serif !important;
}

.form-control, .form-label {
    font-family: 'Hind Siliguri', sans-serif !important;
}

/* Font smoothing */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}
