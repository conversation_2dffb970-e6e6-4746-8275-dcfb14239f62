<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Create exam_types table if not exists
$sql = "CREATE TABLE IF NOT EXISTS exam_types (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    type_name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
$conn->query($sql);

// Check if there are any exam types, if not add some default ones
$checkExamTypes = $conn->query("SELECT COUNT(*) as count FROM exam_types");
$examTypesCount = $checkExamTypes->fetch_assoc()['count'];

if ($examTypesCount == 0) {
    // Add default exam types
    $defaultTypes = [
        ['অর্ধ-বার্ষিক পরীক্ষা', 'শিক্ষাবর্ষের মধ্যে অনুষ্ঠিত পরীক্ষা'],
        ['বার্ষিক পরীক্ষা', 'শিক্ষাবর্ষের শেষে অনুষ্ঠিত পরীক্ষা'],
        ['নির্বাচনী পরীক্ষা', 'পাবলিক পরীক্ষার প্রস্তুতি হিসেবে অনুষ্ঠিত পরীক্ষা'],
        ['প্রাক-নির্বাচনী পরীক্ষা', 'নির্বাচনী পরীক্ষার আগে অনুষ্ঠিত পরীক্ষা'],
        ['সাপ্তাহিক পরীক্ষা', 'প্রতি সপ্তাহে অনুষ্ঠিত পরীক্ষা'],
        ['মাসিক পরীক্ষা', 'প্রতি মাসে অনুষ্ঠিত পরীক্ষা']
    ];
    
    $insertStmt = $conn->prepare("INSERT INTO exam_types (type_name, description) VALUES (?, ?)");
    
    foreach ($defaultTypes as $type) {
        $insertStmt->bind_param("ss", $type[0], $type[1]);
        $insertStmt->execute();
    }
}

// Handle form submissions
$successMessage = '';
$errorMessage = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add new exam type
    if (isset($_POST['add_type'])) {
        $typeName = trim($_POST['type_name']);
        $description = trim($_POST['description'] ?? '');
        $isActive = isset($_POST['is_active']) ? 1 : 0;
        
        if (empty($typeName)) {
            $errorMessage = "পরীক্ষার ধরনের নাম আবশ্যক!";
        } else {
            // Check if type name already exists
            $checkQuery = "SELECT id FROM exam_types WHERE type_name = ?";
            $stmt = $conn->prepare($checkQuery);
            $stmt->bind_param("s", $typeName);
            $stmt->execute();
            $checkResult = $stmt->get_result();
            
            if ($checkResult->num_rows > 0) {
                $errorMessage = "এই পরীক্ষার ধরন ইতিমধ্যে বিদ্যমান!";
            } else {
                // Insert new exam type
                $insertQuery = "INSERT INTO exam_types (type_name, description, is_active) VALUES (?, ?, ?)";
                $stmt = $conn->prepare($insertQuery);
                $stmt->bind_param("ssi", $typeName, $description, $isActive);
                
                if ($stmt->execute()) {
                    $successMessage = "পরীক্ষার ধরন সফলভাবে যোগ করা হয়েছে!";
                } else {
                    $errorMessage = "পরীক্ষার ধরন যোগ করতে সমস্যা হয়েছে: " . $conn->error;
                }
            }
        }
    }
    
    // Update exam type
    if (isset($_POST['update_type'])) {
        $typeId = $_POST['type_id'];
        $typeName = trim($_POST['type_name']);
        $description = trim($_POST['description'] ?? '');
        $isActive = isset($_POST['is_active']) ? 1 : 0;
        
        if (empty($typeName)) {
            $errorMessage = "পরীক্ষার ধরনের নাম আবশ্যক!";
        } else {
            // Check if type name already exists for other types
            $checkQuery = "SELECT id FROM exam_types WHERE type_name = ? AND id != ?";
            $stmt = $conn->prepare($checkQuery);
            $stmt->bind_param("si", $typeName, $typeId);
            $stmt->execute();
            $checkResult = $stmt->get_result();
            
            if ($checkResult->num_rows > 0) {
                $errorMessage = "এই পরীক্ষার ধরন ইতিমধ্যে বিদ্যমান!";
            } else {
                // Update exam type
                $updateQuery = "UPDATE exam_types SET type_name = ?, description = ?, is_active = ? WHERE id = ?";
                $stmt = $conn->prepare($updateQuery);
                $stmt->bind_param("ssii", $typeName, $description, $isActive, $typeId);
                
                if ($stmt->execute()) {
                    $successMessage = "পরীক্ষার ধরন সফলভাবে আপডেট করা হয়েছে!";
                } else {
                    $errorMessage = "পরীক্ষার ধরন আপডেট করতে সমস্যা হয়েছে: " . $conn->error;
                }
            }
        }
    }
    
    // Delete exam type
    if (isset($_POST['delete_type'])) {
        $typeId = $_POST['type_id'];
        
        // Check if this exam type is used in any exams
        $checkQuery = "SELECT COUNT(*) as count FROM exams WHERE exam_type = (SELECT type_name FROM exam_types WHERE id = ?)";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param("i", $typeId);
        $stmt->execute();
        $checkResult = $stmt->get_result();
        $usageCount = $checkResult->fetch_assoc()['count'];
        
        if ($usageCount > 0) {
            $errorMessage = "এই পরীক্ষার ধরন $usageCount টি পরীক্ষায় ব্যবহৃত হয়েছে। আগে সেগুলি পরিবর্তন করুন।";
        } else {
            // Delete exam type
            $deleteQuery = "DELETE FROM exam_types WHERE id = ?";
            $stmt = $conn->prepare($deleteQuery);
            $stmt->bind_param("i", $typeId);
            
            if ($stmt->execute()) {
                $successMessage = "পরীক্ষার ধরন সফলভাবে মুছে ফেলা হয়েছে!";
            } else {
                $errorMessage = "পরীক্ষার ধরন মুছতে সমস্যা হয়েছে: " . $conn->error;
            }
        }
    }
}

// Get all exam types
$examTypesQuery = "SELECT * FROM exam_types ORDER BY type_name";
$examTypes = $conn->query($examTypesQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>পরীক্ষার ধরন - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>পরীক্ষার ধরন ব্যবস্থাপনা</h2>
                        <p class="text-muted">পরীক্ষার ধরন যোগ করুন, আপডেট করুন এবং দেখুন</p>
                    </div>
                </div>

                <?php if ($successMessage): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo $successMessage; ?>
                    </div>
                <?php endif; ?>

                <?php if ($errorMessage): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $errorMessage; ?>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- Add/Edit Form -->
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">নতুন পরীক্ষার ধরন যোগ করুন</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="">
                                    <div class="mb-3">
                                        <label for="type_name" class="form-label">পরীক্ষার ধরনের নাম*</label>
                                        <input type="text" class="form-control" id="type_name" name="type_name" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="description" class="form-label">বিবরণ</label>
                                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                    </div>
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" checked>
                                        <label class="form-check-label" for="is_active">সক্রিয়</label>
                                    </div>
                                    <button type="submit" name="add_type" class="btn btn-success">
                                        <i class="fas fa-plus-circle me-2"></i>যোগ করুন
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Exam Types Table -->
                    <div class="col-md-8 mb-4">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">পরীক্ষার ধরন তালিকা</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>নাম</th>
                                                <th>বিবরণ</th>
                                                <th>স্ট্যাটাস</th>
                                                <th>একশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($examTypes && $examTypes->num_rows > 0): ?>
                                                <?php while ($type = $examTypes->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($type['type_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($type['description'] ?? ''); ?></td>
                                                        <td>
                                                            <span class="badge <?php echo $type['is_active'] ? 'bg-success' : 'bg-danger'; ?>">
                                                                <?php echo $type['is_active'] ? 'সক্রিয়' : 'নিষ্ক্রিয়'; ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <button type="button" class="btn btn-sm btn-warning edit-type" 
                                                                data-id="<?php echo $type['id']; ?>"
                                                                data-name="<?php echo htmlspecialchars($type['type_name']); ?>"
                                                                data-description="<?php echo htmlspecialchars($type['description'] ?? ''); ?>"
                                                                data-active="<?php echo $type['is_active']; ?>"
                                                                data-bs-toggle="modal" data-bs-target="#editTypeModal">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-danger delete-type"
                                                                data-id="<?php echo $type['id']; ?>"
                                                                data-name="<?php echo htmlspecialchars($type['type_name']); ?>"
                                                                data-bs-toggle="modal" data-bs-target="#deleteTypeModal">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="4" class="text-center">কোন পরীক্ষার ধরন পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Type Modal -->
    <div class="modal fade" id="editTypeModal" tabindex="-1" aria-labelledby="editTypeModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="editTypeModalLabel">পরীক্ষার ধরন সম্পাদনা করুন</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="type_id" id="edit_type_id">
                        <div class="mb-3">
                            <label for="edit_type_name" class="form-label">পরীক্ষার ধরনের নাম*</label>
                            <input type="text" class="form-control" id="edit_type_name" name="type_name" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_description" class="form-label">বিবরণ</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="edit_is_active" name="is_active">
                            <label class="form-check-label" for="edit_is_active">সক্রিয়</label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                        <button type="submit" name="update_type" class="btn btn-warning">আপডেট করুন</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Type Modal -->
    <div class="modal fade" id="deleteTypeModal" tabindex="-1" aria-labelledby="deleteTypeModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteTypeModalLabel">পরীক্ষার ধরন মুছে ফেলুন</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>আপনি কি নিশ্চিত যে আপনি <span id="delete_type_name"></span> পরীক্ষার ধরন মুছতে চান?</p>
                    <p class="text-danger">এই কাজটি ফিরিয়ে নেওয়া যাবে না!</p>
                </div>
                <form method="POST" action="">
                    <input type="hidden" name="type_id" id="delete_type_id">
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                        <button type="submit" name="delete_type" class="btn btn-danger">মুছে ফেলুন</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Edit type modal
            const editButtons = document.querySelectorAll('.edit-type');
            editButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const id = this.getAttribute('data-id');
                    const name = this.getAttribute('data-name');
                    const description = this.getAttribute('data-description');
                    const active = this.getAttribute('data-active') === '1';
                    
                    document.getElementById('edit_type_id').value = id;
                    document.getElementById('edit_type_name').value = name;
                    document.getElementById('edit_description').value = description;
                    document.getElementById('edit_is_active').checked = active;
                });
            });
            
            // Delete type modal
            const deleteButtons = document.querySelectorAll('.delete-type');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const id = this.getAttribute('data-id');
                    const name = this.getAttribute('data-name');
                    
                    document.getElementById('delete_type_id').value = id;
                    document.getElementById('delete_type_name').textContent = name;
                });
            });
        });
    </script>
</body>
</html>
