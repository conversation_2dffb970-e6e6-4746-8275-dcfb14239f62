<?php
// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Get current page name
$current_page = basename($_SERVER['PHP_SELF']);
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>অ্যাডমিন প্যানেল</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- Dashboard Layout CSS -->
    <link rel="stylesheet" href="css/dashboard-layout.css">

    <!-- Modern Styles CSS -->
    <link rel="stylesheet" href="css/modern-styles.css">

    <?php if (isset($extra_css)): ?>
        <?php echo $extra_css; ?>
    <?php endif; ?>
</head>
<body>
    <!-- Page Loading Indicator -->
    <div id="page-loader">
        <div class="d-flex flex-column align-items-center">
            <div class="loader-spinner"></div>
            <div class="loader-text mt-3">পৃষ্ঠা লোড হচ্ছে...</div>
        </div>
    </div>

    <div class="dashboard-container">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-logo">
                <button class="sidebar-toggle btn btn-link text-white d-md-none" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <img src="../assets/images/logo.png" alt="Logo" class="d-none d-md-block">
                <span>শিক্ষা ব্যবস্থাপনা</span>
            </div>

            <div class="header-title">
                <?php echo isset($page_title) ? $page_title : 'অ্যাডমিন প্যানেল'; ?>
            </div>

            <div class="header-actions">
                <div class="dropdown">
                    <button class="btn btn-link text-white dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user-circle me-1"></i>
                        <span class="d-none d-md-inline">অ্যাডমিন</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i> প্রোফাইল</a></li>
                        <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i> সেটিংস</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../includes/logout.inc.php"><i class="fas fa-sign-out-alt me-2"></i> লগআউট</a></li>
                    </ul>
                </div>
            </div>
        </header>

        <!-- Sidebar -->
        <aside class="dashboard-sidebar">
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">মূল মেনু</div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'dashboard.php') ? 'active' : ''; ?>" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>ড্যাশবোর্ড</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'students.php') ? 'active' : ''; ?>" href="students.php">
                                <i class="fas fa-user-graduate"></i>
                                <span>শিক্ষার্থী</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'teachers.php') ? 'active' : ''; ?>" href="teachers.php">
                                <i class="fas fa-chalkboard-teacher"></i>
                                <span>শিক্ষক</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'classes.php') ? 'active' : ''; ?>" href="classes.php">
                                <i class="fas fa-school"></i>
                                <span>শ্রেণী</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'subjects.php') ? 'active' : ''; ?>" href="subjects.php">
                                <i class="fas fa-book"></i>
                                <span>বিষয়</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'attendance.php') ? 'active' : ''; ?>" href="attendance.php">
                                <i class="fas fa-calendar-check"></i>
                                <span>উপস্থিতি</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">পরীক্ষা ব্যবস্থাপনা</div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'manage_exams.php') ? 'active' : ''; ?>" href="manage_exams.php">
                                <i class="fas fa-file-alt"></i>
                                <span>পরীক্ষা</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'student_exam_attendance.php') ? 'active' : ''; ?>" href="student_exam_attendance.php">
                                <i class="fas fa-clipboard-check"></i>
                                <span>শিক্ষার্থী হাজিরা পত্র</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'subject_exam_pattern.php') ? 'active' : ''; ?>" href="subject_exam_pattern.php">
                                <i class="fas fa-sliders-h"></i>
                                <span>বিষয় পরীক্ষা প্যাটার্ন</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'subject_marks_distribution.php') ? 'active' : ''; ?>" href="subject_marks_distribution.php">
                                <i class="fas fa-chart-pie"></i>
                                <span>বিষয় মার্কস ডিস্ট্রিবিউশন</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'subject_minimum_pass.php') ? 'active' : ''; ?>" href="subject_minimum_pass.php">
                                <i class="fas fa-check-circle"></i>
                                <span>ন্যূনতম পাস মার্কস</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'results.php') ? 'active' : ''; ?>" href="results.php">
                                <i class="fas fa-chart-bar"></i>
                                <span>ফলাফল</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">সিস্টেম</div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'settings.php') ? 'active' : ''; ?>" href="settings.php">
                                <i class="fas fa-cog"></i>
                                <span>সেটিংস</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../includes/logout.inc.php">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>লগআউট</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="dashboard-content">
            <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeInUp shadow-sm" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle fa-2x me-3"></i>
                        <div>
                            <strong>সফল!</strong> <?php echo $success_message; ?>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeInUp shadow-sm" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                        <div>
                            <strong>ত্রুটি!</strong> <?php echo $error_message; ?>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <!-- Page content will be inserted here -->
            <?php if (isset($content)): ?>
                <?php echo $content; ?>
            <?php endif; ?>
        </main>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Dashboard Layout JS -->
    <script>
    // Fix for auto-reload issue
    if (window.history.replaceState) {
        window.history.replaceState(null, null, window.location.href);
    }

    // Page Loading Handler
    window.addEventListener('load', function() {
        // Hide loader and show content
        setTimeout(function() {
            const loader = document.getElementById('page-loader');
            if (loader) {
                loader.style.opacity = '0';
                document.body.classList.add('loaded');

                setTimeout(function() {
                    loader.style.display = 'none';
                }, 500);
            }
        }, 300);
    });

    document.addEventListener('DOMContentLoaded', function() {
        // Sidebar toggle for mobile
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', function() {
                document.querySelector('.dashboard-container').classList.toggle('sidebar-open');
            });
        }

        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl, {
                animation: true,
                delay: { show: 100, hide: 100 }
            });
        });
    });
    </script>

    <?php if (isset($extra_js)): ?>
        <?php echo $extra_js; ?>
    <?php endif; ?>
</body>
</html>
