<?php
// Database Connection with improved error handling and reconnection capability

// Connection parameters
$servername = "127.0.0.1"; // Changed from localhost to IP address
$username = "root";
$password = "";
$dbname = "zfaw"; // Database name

// Disable error display for production (only log errors)
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// Create connection with error handling
try {
    // Create connection
    $conn = new mysqli($servername, $username, $password, $dbname);

    // Check connection
    if ($conn->connect_error) {
        error_log("Database connection failed: " . $conn->connect_error);
        die("Database connection failed");
    }

    // Set charset to utf8mb4
    $conn->set_charset("utf8mb4");

} catch (Exception $e) {
    error_log("Database connection exception: " . $e->getMessage());
    die("Database connection failed");
}
// No closing PHP tag to prevent accidental output