<?php
// Include database connection
require_once "includes/dbh.inc.php";

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>বিষয় ক্যাটাগরি ফিক্স করা হচ্ছে</h1>";

// Check if subjects exist
$subjectsQuery = "SELECT COUNT(*) as count FROM subjects";
$result = $conn->query($subjectsQuery);
$subjectCount = $result->fetch_assoc()['count'];

echo "<p>মোট বিষয় সংখ্যা: $subjectCount</p>";

if ($subjectCount == 0) {
    // Add some demo subjects if none exist
    $demoSubjects = [
        ['বাংলা', 'BAN101', 'required'],
        ['ইংরেজি', 'ENG101', 'required'],
        ['গণিত', 'MATH101', 'required'],
        ['বিজ্ঞান', 'SCI101', 'required'],
        ['ইতিহাস', 'HIST101', 'optional'],
        ['ভূগোল', 'GEO101', 'optional'],
        ['অর্থনীতি', 'ECON101', 'optional'],
        ['রসায়ন', 'CHEM101', 'optional'],
        ['পদার্থবিজ্ঞান', 'PHY101', 'fourth'],
        ['জীববিজ্ঞান', 'BIO101', 'fourth'],
        ['কম্পিউটার', 'CSE101', 'fourth'],
        ['ধর্ম', 'REL101', 'optional']
    ];
    
    $insertSubjectQuery = "INSERT INTO subjects (subject_name, subject_code, category, is_active) VALUES (?, ?, ?, 1)";
    $stmt = $conn->prepare($insertSubjectQuery);
    
    foreach ($demoSubjects as $subject) {
        $stmt->bind_param("sss", $subject[0], $subject[1], $subject[2]);
        $stmt->execute();
        echo "<p>বিষয় যোগ করা হয়েছে: {$subject[0]} (ক্যাটাগরি: {$subject[2]})</p>";
    }
}

// Get the student info to find their department
$studentId = 'STD-601523'; // The student ID from the URL
$studentQuery = "SELECT s.id, s.department_id, d.department_name 
                FROM students s 
                JOIN departments d ON s.department_id = d.id 
                WHERE s.student_id = ?";
$stmt = $conn->prepare($studentQuery);
$stmt->bind_param("s", $studentId);
$stmt->execute();
$studentResult = $stmt->get_result();

if ($studentResult && $studentResult->num_rows > 0) {
    $studentInfo = $studentResult->fetch_assoc();
    $departmentId = $studentInfo['department_id'];
    $departmentName = $studentInfo['department_name'];
    
    echo "<h2>শিক্ষার্থীর বিভাগ: $departmentName (ID: $departmentId)</h2>";
    
    // Get all subjects
    $allSubjectsQuery = "SELECT id, subject_name, category FROM subjects WHERE is_active = 1";
    $allSubjects = $conn->query($allSubjectsQuery);
    
    if ($allSubjects && $allSubjects->num_rows > 0) {
        // First, ensure all subjects are in subject_departments table for this department
        $insertDeptSubjectQuery = "INSERT IGNORE INTO subject_departments (department_id, subject_id) VALUES (?, ?)";
        $stmt = $conn->prepare($insertDeptSubjectQuery);
        
        echo "<h3>বিভাগের সাথে বিষয় ম্যাপিং যোগ করা হচ্ছে</h3>";
        echo "<ul>";
        
        while ($subject = $allSubjects->fetch_assoc()) {
            $subjectId = $subject['id'];
            $stmt->bind_param("ii", $departmentId, $subjectId);
            $stmt->execute();
            echo "<li>বিষয় ম্যাপ করা হয়েছে: {$subject['subject_name']} (ID: $subjectId)</li>";
        }
        
        echo "</ul>";
        
        // Reset the result pointer
        $allSubjects->data_seek(0);
        
        // Now, ensure all subjects have correct type in department_subject_types table
        $insertTypeQuery = "INSERT INTO department_subject_types (department_id, subject_id, subject_type) 
                           VALUES (?, ?, ?) 
                           ON DUPLICATE KEY UPDATE subject_type = VALUES(subject_type)";
        $stmt = $conn->prepare($insertTypeQuery);
        
        echo "<h3>বিষয় ক্যাটাগরি সেট করা হচ্ছে</h3>";
        echo "<ul>";
        
        while ($subject = $allSubjects->fetch_assoc()) {
            $subjectId = $subject['id'];
            $subjectType = $subject['category'];
            $stmt->bind_param("iis", $departmentId, $subjectId, $subjectType);
            $stmt->execute();
            echo "<li>বিষয় ক্যাটাগরি সেট করা হয়েছে: {$subject['subject_name']} (ক্যাটাগরি: $subjectType)</li>";
        }
        
        echo "</ul>";
    }
    
    // Check what subjects are now available for each category
    echo "<h2>বিভাগের জন্য উপলব্ধ বিষয়সমূহ</h2>";
    
    // Required subjects
    $requiredQuery = "SELECT s.id, s.subject_name, s.subject_code, dst.subject_type
                     FROM subjects s
                     JOIN department_subject_types dst ON s.id = dst.subject_id
                     WHERE dst.department_id = ? AND dst.subject_type = 'required'
                     AND s.is_active = 1";
    $stmt = $conn->prepare($requiredQuery);
    $stmt->bind_param("i", $departmentId);
    $stmt->execute();
    $requiredSubjects = $stmt->get_result();
    
    echo "<h3>আবশ্যিক বিষয়সমূহ</h3>";
    if ($requiredSubjects && $requiredSubjects->num_rows > 0) {
        echo "<ul>";
        while ($subject = $requiredSubjects->fetch_assoc()) {
            echo "<li>{$subject['subject_name']} ({$subject['subject_code']})</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>কোন আবশ্যিক বিষয় পাওয়া যায়নি</p>";
    }
    
    // Optional subjects
    $optionalQuery = "SELECT s.id, s.subject_name, s.subject_code, dst.subject_type
                     FROM subjects s
                     JOIN department_subject_types dst ON s.id = dst.subject_id
                     WHERE dst.department_id = ? AND dst.subject_type = 'optional'
                     AND s.is_active = 1";
    $stmt = $conn->prepare($optionalQuery);
    $stmt->bind_param("i", $departmentId);
    $stmt->execute();
    $optionalSubjects = $stmt->get_result();
    
    echo "<h3>ঐচ্ছিক বিষয়সমূহ</h3>";
    if ($optionalSubjects && $optionalSubjects->num_rows > 0) {
        echo "<ul>";
        while ($subject = $optionalSubjects->fetch_assoc()) {
            echo "<li>{$subject['subject_name']} ({$subject['subject_code']})</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>কোন ঐচ্ছিক বিষয় পাওয়া যায়নি</p>";
    }
    
    // Fourth subjects
    $fourthQuery = "SELECT s.id, s.subject_name, s.subject_code, dst.subject_type
                   FROM subjects s
                   JOIN department_subject_types dst ON s.id = dst.subject_id
                   WHERE dst.department_id = ? AND dst.subject_type = 'fourth'
                   AND s.is_active = 1";
    $stmt = $conn->prepare($fourthQuery);
    $stmt->bind_param("i", $departmentId);
    $stmt->execute();
    $fourthSubjects = $stmt->get_result();
    
    echo "<h3>৪র্থ বিষয়সমূহ</h3>";
    if ($fourthSubjects && $fourthSubjects->num_rows > 0) {
        echo "<ul>";
        while ($subject = $fourthSubjects->fetch_assoc()) {
            echo "<li>{$subject['subject_name']} ({$subject['subject_code']})</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>কোন ৪র্থ বিষয় পাওয়া যায়নি</p>";
    }
    
    echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>সফলভাবে আপডেট করা হয়েছে!</h3>";
    echo "<p>বিষয় ক্যাটাগরি সঠিকভাবে সেট করা হয়েছে।</p>";
    echo "<p>এখন আপনি <a href='admin/student_subject_selection.php?id=$studentId'>শিক্ষার্থী বিষয় নির্বাচন</a> পেজে যেতে পারেন।</p>";
    echo "</div>";
} else {
    echo "<p>শিক্ষার্থী পাওয়া যায়নি (ID: $studentId)</p>";
}

// Close connection
$conn->close();
?>
