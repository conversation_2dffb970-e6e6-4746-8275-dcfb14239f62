<?php
// Initialize the session
session_start();

// Check if the user is logged in, if not then redirect to login page
if(!isset($_SESSION["userId"])){
    header("location: login.php");
    exit;
}

// Include database connection
require_once "includes/dbh.inc.php";

// Page title
$page_title = "ড্যাশবোর্ড";

// Get user information
$user_id = isset($_SESSION["userId"]) ? $_SESSION["userId"] : 0;
$user_name = isset($_SESSION["username"]) ? $_SESSION["username"] : "ব্যবহারকারী";
$user_email = isset($_SESSION["email"]) ? $_SESSION["email"] : "<EMAIL>";
$user_type = isset($_SESSION["userType"]) ? $_SESSION["userType"] : "student";

// Initialize empty array for notifications
$notifications = array();

// Check if notifications table exists
$check_notifications_table = $conn->query("SHOW TABLES LIKE 'notifications'");
$notifications_table_exists = $check_notifications_table->num_rows > 0;

// Get user's notifications if table exists
if ($notifications_table_exists) {
    $sql = "SELECT * FROM notifications WHERE user_id = ? OR user_id = 0 ORDER BY created_at DESC LIMIT 5";

    if($stmt = $conn->prepare($sql)){
        $stmt->bind_param("i", $user_id);

        if($stmt->execute()){
            $result = $stmt->get_result();

            while($row = $result->fetch_assoc()){
                $notifications[] = $row;
            }
        }

        $stmt->close();
    }
}

// Close connection
$conn->close();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title><?php echo $page_title; ?> - ZFAW</title>

    <!-- Bootstrap CSS -->


    <style>
        .dashboard-section {
            padding: 30px 0;
        }

        .dashboard-card {
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .dashboard-card .card-header {
            background-color: #007bff;
            color: white;
            padding: 15px 20px;
            border-bottom: none;
        }

        .dashboard-card .card-body {
            padding: 20px;
        }

        .user-info {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background-color: #007bff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            margin-right: 20px;
        }

        .user-details h4 {
            margin-bottom: 5px;
        }

        .user-details p {
            margin-bottom: 0;
            color: #6c757d;
        }

        .dashboard-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .stat-card {
            flex: 1;
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 0 5px;
            text-align: center;
        }

        .stat-card i {
            font-size: 24px;
            margin-bottom: 10px;
            color: #007bff;
        }

        .stat-card h5 {
            margin-bottom: 5px;
        }

        .stat-card p {
            margin-bottom: 0;
            color: #6c757d;
        }

        .course-card {
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .course-card:hover {
            transform: translateY(-5px);
        }

        .course-card img {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }

        .course-card .card-body {
            padding: 15px;
        }

        .course-card .card-title {
            margin-bottom: 10px;
            font-weight: bold;
        }

        .course-card .card-text {
            margin-bottom: 10px;
            color: #6c757d;
        }

        .course-info {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            color: #6c757d;
            font-size: 14px;
        }

        .notification-item {
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }

        .notification-icon i {
            color: #007bff;
        }

        .notification-content h6 {
            margin-bottom: 5px;
        }

        .notification-content p {
            margin-bottom: 0;
            color: #6c757d;
            font-size: 14px;
        }

        .notification-time {
            color: #6c757d;
            font-size: 12px;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu li a {
            display: block;
            padding: 10px 15px;
            border-radius: 5px;
            color: #212529;
            text-decoration: none;
            transition: all 0.3s;
        }

        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background-color: #007bff;
            color: white;
        }

        .sidebar-menu li a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">ZFAW</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">হোম</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">বিষয়সমূহ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">আমাদের সম্পর্কে</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">যোগাযোগ</a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <div class="dropdown">
                        <a class="btn btn-outline-light dropdown-toggle" href="#" role="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-2"></i> <?php echo htmlspecialchars($user_name); ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড</a></li>
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i> প্রোফাইল</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i> সেটিংস</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i> লগআউট</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Scrolling Notice Section (iframe solution with vertical alignment fix) -->
    <div style="width: 100%; height: 85px; position: relative; margin-bottom: 15px; display: flex; align-items: center; justify-content: center; overflow: hidden;">
        <iframe src="centered_notice.php" style="width: 100%; height: 85px; border: none; overflow: hidden; position: absolute; top: 0; left: 0;"></iframe>
    </div>

    <!-- Main Content -->
    <main>
        <section class="dashboard-section">
            <div class="container">
                <div class="row">
                    <!-- Sidebar -->
                    <div class="col-lg-3">
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h5><i class="fas fa-bars me-2"></i> মেনু</h5>
                            </div>
                            <div class="card-body">
                                <ul class="sidebar-menu">
                                    <li><a href="dashboard.php" class="active"><i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড</a></li>
                                    <li><a href="assignments.php"><i class="fas fa-tasks"></i> অ্যাসাইনমেন্ট</a></li>
                                    <li><a href="exams.php"><i class="fas fa-file-alt"></i> পরীক্ষাসমূহ</a></li>
                                    <li><a href="results.php"><i class="fas fa-chart-bar"></i> ফলাফল</a></li>
                                    <li><a href="profile.php"><i class="fas fa-user"></i> প্রোফাইল</a></li>
                                    <li><a href="settings.php"><i class="fas fa-cog"></i> সেটিংস</a></li>
                                    <li><a href="upload_content.php"><i class="fas fa-upload"></i> কনটেন্ট আপলোড</a></li>
                                    <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> লগআউট</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Main Content -->
                    <div class="col-lg-9">
                        <!-- Welcome Card -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h5><i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড</h5>
                            </div>
                            <div class="card-body">
                                <div class="user-info">
                                    <div class="user-avatar">
                                        <?php echo strtoupper(substr($user_name, 0, 1)); ?>
                                    </div>
                                    <div class="user-details">
                                        <h4>স্বাগতম, <?php echo htmlspecialchars($user_name); ?>!</h4>
                                        <p><?php echo htmlspecialchars($user_email); ?></p>
                                    </div>
                                </div>

                                <div class="dashboard-stats">
                                    <div class="stat-card">
                                        <i class="fas fa-book-open"></i>
                                        <h5>4</h5>
                                        <p>বিষয়</p>
                                    </div>
                                    <div class="stat-card">
                                        <i class="fas fa-tasks"></i>
                                        <h5>5</h5>
                                        <p>অ্যাসাইনমেন্ট</p>
                                    </div>
                                    <div class="stat-card">
                                        <i class="fas fa-file-alt"></i>
                                        <h5>3</h5>
                                        <p>পরীক্ষা</p>
                                    </div>
                                    <div class="stat-card">
                                        <i class="fas fa-certificate"></i>
                                        <h5>2</h5>
                                        <p>সার্টিফিকেট</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- বিষয়সমূহ -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h5><i class="fas fa-book-open me-2"></i> আমার বিষয়সমূহ</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i> আপনার বিষয়সমূহ দেখতে <a href="subject_selection.php">বিষয় নির্বাচন</a> পেজে যান।
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Notifications -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h5><i class="fas fa-bell me-2"></i> সর্বশেষ নোটিফিকেশন</h5>
                            </div>
                            <div class="card-body">
                                <?php if (count($notifications) > 0): ?>
                                    <?php foreach ($notifications as $notification): ?>
                                        <div class="notification-item d-flex align-items-start">
                                            <div class="notification-icon">
                                                <i class="fas fa-bell"></i>
                                            </div>
                                            <div class="notification-content flex-grow-1">
                                                <h6><?php echo htmlspecialchars($notification['title']); ?></h6>
                                                <p><?php echo htmlspecialchars($notification['message']); ?></p>
                                                <small class="notification-time"><?php echo date('d M Y, h:i A', strtotime($notification['created_at'])); ?></small>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i> কোন নোটিফিকেশন নেই।
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <h5>ZFAW</h5>
                    <p>উচ্চমানের শিক্ষা প্রদানের মাধ্যমে শিক্ষার্থীদের ভবিষ্যৎ গড়তে সাহায্য করা আমাদের লক্ষ্য।</p>
                </div>
                <div class="col-md-4 mb-3">
                    <h5>যোগাযোগ</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> ১২৩, মেইন রোড, ঢাকা</p>
                        <p><i class="fas fa-phone me-2"></i> +৮৮০১৭১২৩৪৫৬৭৮</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
                <div class="col-md-4 mb-3">
                    <h5>লিঙ্কসমূহ</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-white">হোম</a></li>
                        <li><a href="subjects.php" class="text-white">বিষয়সমূহ</a></li>
                        <li><a href="about.php" class="text-white">আমাদের সম্পর্কে</a></li>
                        <li><a href="contact.php" class="text-white">যোগাযোগ</a></li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?php echo date('Y'); ?> ZFAW. সর্বস্বত্ব সংরক্ষিত।</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
