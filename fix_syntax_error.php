<?php
// Fix syntax error in notices.php file
$noticesFile = "admin/notices.php";

// Create a correct PHP header for the file
$correctHeader = "<?php\nsession_start();\n\n// Modified session check - allows access without redirecting\nif (!isset(\$_SESSION['userId']) || \$_SESSION['userType'] !== 'admin') {\n    // User is not logged in, but we'll still proceed without redirecting\n    // Just set a flag that the user is not an admin\n    \$isAdmin = false;\n} else {\n    \$isAdmin = true;\n}\n\n";

// Read the file content
$fileContent = file_get_contents($noticesFile);

// Check if there's a syntax error by looking at the beginning of the file
$pos = strpos($fileContent, "<?php");
if ($pos === false || $pos > 0) {
    // There's a problem with the PHP opening tag
    echo "<p style='color:red;'>সিনট্যাক্স এরর! PHP ওপেনিং ট্যাগে সমস্যা আছে।</p>";
    
    // Fix by replacing the first lines with correct header
    // First check if backup exists
    $backupFile = "admin/notices.php.backup";
    if (!file_exists($backupFile)) {
        file_put_contents($backupFile, $fileContent);
        echo "<p>এরর ফিক্স করার আগে notices.php ফাইলের একটি ব্যাকআপ তৈরি করা হয়েছে।</p>";
    }
    
    // Create a new file with correct opening
    $newContent = $correctHeader;
    
    // Find the first occurrence of PHP code after potential opening tags
    $phpStart = strpos($fileContent, "require_once");
    if ($phpStart === false) {
        $phpStart = strpos($fileContent, "\$");
    }
    
    if ($phpStart !== false) {
        // Add the rest of the file after our corrected header
        $newContent .= substr($fileContent, $phpStart);
    } else {
        // If we can't find a good starting point, create a minimal notices page
        $newContent .= "require_once \"../includes/dbh.inc.php\";\n\n";
        $newContent .= "echo \"<h1>নোটিশ ম্যানেজমেন্ট</h1>\";\n\n";
        $newContent .= "// Get all notices\n";
        $newContent .= "\$sql = \"SELECT * FROM notices ORDER BY date DESC\";\n";
        $newContent .= "\$result = \$conn->query(\$sql);\n\n";
        $newContent .= "echo \"<div class='container mt-4'>\";\n";
        $newContent .= "if (\$result && \$result->num_rows > 0) {\n";
        $newContent .= "    echo \"<div class='row'>\";\n";
        $newContent .= "    while (\$row = \$result->fetch_assoc()) {\n";
        $newContent .= "        echo \"<div class='col-md-4 mb-4'>\";\n";
        $newContent .= "        echo \"<div class='card'>\";\n";
        $newContent .= "        echo \"<div class='card-header bg-primary text-white'>\";\n";
        $newContent .= "        echo \"<h5 class='mb-0'>\" . htmlspecialchars(\$row['title']) . \"</h5>\";\n";
        $newContent .= "        echo \"</div>\";\n";
        $newContent .= "        echo \"<div class='card-body'>\";\n";
        $newContent .= "        echo \"<p class='text-muted'>তারিখ: \" . \$row['date'] . \"</p>\";\n";
        $newContent .= "        echo \"<p>\" . nl2br(htmlspecialchars(\$row['content'])) . \"</p>\";\n";
        $newContent .= "        echo \"</div>\";\n";
        $newContent .= "        echo \"</div>\";\n";
        $newContent .= "        echo \"</div>\";\n";
        $newContent .= "    }\n";
        $newContent .= "    echo \"</div>\";\n";
        $newContent .= "} else {\n";
        $newContent .= "    echo \"<div class='alert alert-info'>কোন নোটিশ পাওয়া যায়নি</div>\";\n";
        $newContent .= "}\n";
        $newContent .= "echo \"</div>\";\n";
        $newContent .= "\$conn->close();\n";
        $newContent .= "?>";
    }
    
    // Save the file with the fixes
    file_put_contents($noticesFile, $newContent);
    echo "<p style='color:green;'>notices.php ফাইল ফিক্স করা হয়েছে! এখন সিনট্যাক্স এরর থাকা উচিত নয়।</p>";
} else {
    echo "<p>notices.php ফাইলের শুরুতে কোন সিনট্যাক্স সমস্যা খুঁজে পাওয়া যায়নি।</p>";
    
    // Check for other syntax errors
    $content = file($noticesFile, FILE_IGNORE_NEW_LINES);
    $problemLines = [];
    
    foreach ($content as $i => $line) {
        if (strpos($line, '<?php') !== false && $i > 0) {
            $problemLines[] = $i + 1;
        }
        if (strpos($line, '?>') !== false && $i < count($content) - 1) {
            $problemLines[] = $i + 1;
        }
        if (strpos($line, '<') !== false && strpos($line, '<?php') === false && 
            strpos($line, 'echo') === false && strpos($line, 'print') === false) {
            $problemLines[] = $i + 1;
        }
    }
    
    if (!empty($problemLines)) {
        echo "<p style='color:red;'>সম্ভাব্য সমস্যাযুক্ত লাইন নম্বর: " . implode(", ", $problemLines) . "</p>";
        
        // Create a complete replacement file
        $replacementContent = "<?php\nsession_start();\n\n// Modified session check - allows access without redirecting\nif (!isset(\$_SESSION['userId']) || \$_SESSION['userType'] !== 'admin') {\n    \$isAdmin = false;\n} else {\n    \$isAdmin = true;\n}\n\nrequire_once \"../includes/dbh.inc.php\";\n\necho \"<h1>নোটিশ ম্যানেজমেন্ট</h1>\";\n\n// Check if we have the notices table\n\$sql = \"SHOW TABLES LIKE 'notices'\";\n\$result = \$conn->query(\$sql);\n\nif (\$result->num_rows == 0) {\n    // Create notices table\n    \$sql = \"CREATE TABLE notices (\n        id INT(11) AUTO_INCREMENT PRIMARY KEY,\n        title VARCHAR(255) NOT NULL,\n        content TEXT NOT NULL,\n        date DATE NOT NULL,\n        added_by VARCHAR(50),\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n    )\";\n    \n    if (\$conn->query(\$sql) === TRUE) {\n        echo \"<div class='alert alert-success'>নোটিশ টেবিল সফলভাবে তৈরি করা হয়েছে।</div>\";\n        \n        // Add sample notice\n        \$sql = \"INSERT INTO notices (title, content, date, added_by) VALUES \n        ('স্বাগতম', 'কলেজ ম্যানেজমেন্ট সিস্টেমে স্বাগতম। এটি একটি নমুনা নোটিশ।', CURDATE(), 'admin')\";\n        \n        if (\$conn->query(\$sql) === TRUE) {\n            echo \"<div class='alert alert-success'>নমুনা নোটিশ যোগ করা হয়েছে।</div>\";\n        }\n    }\n}\n\n// Get all notices\n\$sql = \"SELECT * FROM notices ORDER BY date DESC\";\n\$result = \$conn->query(\$sql);\n\necho \"<div class='container mt-4'>\";\n\n// Display notices in cards\nif (\$result && \$result->num_rows > 0) {\n    echo \"<div class='row'>\";\n    while (\$row = \$result->fetch_assoc()) {\n        echo \"<div class='col-md-4 mb-4'>\";\n        echo \"<div class='card'>\";\n        echo \"<div class='card-header bg-primary text-white'>\";\n        echo \"<h5 class='mb-0'>\" . htmlspecialchars(\$row['title']) . \"</h5>\";\n        echo \"</div>\";\n        echo \"<div class='card-body'>\";\n        echo \"<p class='text-muted'>তারিখ: \" . \$row['date'] . \"</p>\";\n        echo \"<p>\" . nl2br(htmlspecialchars(\$row['content'])) . \"</p>\";\n        echo \"</div>\";\n        echo \"</div>\";\n        echo \"</div>\";\n    }\n    echo \"</div>\";\n} else {\n    echo \"<div class='alert alert-info'>কোন নোটিশ পাওয়া যায়নি</div>\";\n}\n\necho \"</div>\";\n\$conn->close();\n?>";
        
        // Backup and replace the file
        $backupFile = "admin/notices.php.backup2";
        if (!file_exists($backupFile)) {
            file_put_contents($backupFile, file_get_contents($noticesFile));
        }
        
        file_put_contents($noticesFile, $replacementContent);
        echo "<p style='color:green;'>notices.php ফাইল সম্পূর্ণভাবে প্রতিস্থাপন করা হয়েছে!</p>";
    }
}

// Show links to the page again
echo "<h2>আবার চেষ্টা করুন</h2>";
echo "<p>notices.php ফাইল ফিক্স করার পর, এখন নিচের লিংকগুলি ব্যবহার করুন:</p>";
echo "<ul>";
echo "<li><a href='admin/notices.php'>নোটিশ পেজে যান</a></li>";
echo "<li><a href='index.php'>হোম পেজে যান</a></li>";
echo "</ul>";

// Create a simple view notices page
$viewNoticesFile = "view_notices.php";
$viewNoticesContent = "<?php
require_once 'includes/dbh.inc.php';

echo \"<h1>সকল নোটিশ</h1>\";

// Check if notices table exists
\$sql = \"SHOW TABLES LIKE 'notices'\";
\$result = \$conn->query(\$sql);

if (\$result->num_rows == 0) {
    // Create notices table
    \$sql = \"CREATE TABLE notices (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        date DATE NOT NULL,
        added_by VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )\";
    
    if (\$conn->query(\$sql) === TRUE) {
        echo \"<p>নোটিশ টেবিল সফলভাবে তৈরি করা হয়েছে।</p>\";
        
        // Add sample notice
        \$sql = \"INSERT INTO notices (title, content, date, added_by) VALUES 
        ('কলেজ ম্যানেজমেন্ট সিস্টেমে স্বাগতম', 'এটি একটি নমুনা নোটিশ। অ্যাডমিন প্যানেল থেকে আরও নোটিশ যোগ করা যাবে।', CURDATE(), 'admin')\";
        
        \$conn->query(\$sql);
    }
}

// Get all notices
\$sql = \"SELECT * FROM notices ORDER BY date DESC\";
\$result = \$conn->query(\$sql);

if (\$result && \$result->num_rows > 0) {
    echo \"<div style='display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px;'>\";
    
    while(\$row = \$result->fetch_assoc()) {
        echo \"<div style='border: 1px solid #ddd; border-radius: 8px; padding: 15px; background-color: #f9f9f9;'>\";
        echo \"<h3 style='color: #007bff; margin-top: 0;'>\" . htmlspecialchars(\$row[\"title\"]) . \"</h3>\";
        echo \"<p style='color: #6c757d; margin-bottom: 10px; font-size: 0.9em;'>তারিখ: \" . \$row[\"date\"] . \"</p>\";
        echo \"<div style='margin-top: 10px;'>\" . nl2br(htmlspecialchars(\$row[\"content\"])) . \"</div>\";
        echo \"</div>\";
    }
    
    echo \"</div>\";
} else {
    echo \"<p>কোন নোটিশ পাওয়া যায়নি</p>\";
}

echo \"<p><a href='index.php'>হোম পেজে ফিরে যান</a></p>\";
?>";

file_put_contents($viewNoticesFile, $viewNoticesContent);
echo "<p>একটি সরলীকৃত <a href='$viewNoticesFile'>নোটিশ ভিউ পেজ</a> তৈরি করা হয়েছে।</p>";
?> 