<?php
require_once '../config.php';

echo "<h1>Students Table Test</h1>";

// Get student data
$query = "SELECT * FROM students LIMIT 5";
$result = $conn->query($query);

if ($result && $result->num_rows > 0) {
    echo "<h2>Sample Data:</h2>";
    echo "<table border='1'>";
    
    // Print headers
    $firstRow = $result->fetch_assoc();
    echo "<tr>";
    foreach (array_keys($firstRow) as $column) {
        echo "<th>" . htmlspecialchars($column) . "</th>";
    }
    echo "</tr>";
    
    // Print first row data
    echo "<tr>";
    foreach ($firstRow as $value) {
        echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
    }
    echo "</tr>";
    
    // Print remaining rows
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        foreach ($row as $value) {
            echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
        }
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>No data found or error: " . $conn->error . "</p>";
}

$conn->close();
?> 