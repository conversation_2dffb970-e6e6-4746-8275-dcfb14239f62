<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Check sessions table
$sessionsQuery = "SELECT * FROM sessions";
$sessionsResult = $conn->query($sessionsQuery);
$sessionsCount = $sessionsResult ? $sessionsResult->num_rows : 0;

// Check classes table
$classesQuery = "SELECT * FROM classes";
$classesResult = $conn->query($classesQuery);
$classesCount = $classesResult ? $classesResult->num_rows : 0;

// Check departments table
$departmentsQuery = "SELECT * FROM departments";
$departmentsResult = $conn->query($departmentsQuery);
$departmentsCount = $departmentsResult ? $departmentsResult->num_rows : 0;

// Include header
include_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Include sidebar -->
        <?php include_once 'includes/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">ডাটাবেস চেক</h1>
            </div>

            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">ডাটাবেস টেবিল তথ্য</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>টেবিল</th>
                                    <th>রেকর্ড সংখ্যা</th>
                                    <th>বিবরণ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>সেশন (sessions)</td>
                                    <td><?= $sessionsCount ?></td>
                                    <td>
                                        <?php if ($sessionsCount > 0): ?>
                                            <span class="badge bg-success">ডাটা আছে</span>
                                            <button class="btn btn-sm btn-info ms-2" type="button" data-bs-toggle="collapse" data-bs-target="#sessionsData">
                                                দেখুন
                                            </button>
                                        <?php else: ?>
                                            <span class="badge bg-danger">কোন ডাটা নেই</span>
                                            <a href="sessions.php" class="btn btn-sm btn-primary ms-2">সেশন যোগ করুন</a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td>শ্রেণী (classes)</td>
                                    <td><?= $classesCount ?></td>
                                    <td>
                                        <?php if ($classesCount > 0): ?>
                                            <span class="badge bg-success">ডাটা আছে</span>
                                            <button class="btn btn-sm btn-info ms-2" type="button" data-bs-toggle="collapse" data-bs-target="#classesData">
                                                দেখুন
                                            </button>
                                        <?php else: ?>
                                            <span class="badge bg-danger">কোন ডাটা নেই</span>
                                            <a href="classes.php" class="btn btn-sm btn-primary ms-2">শ্রেণী যোগ করুন</a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td>বিভাগ (departments)</td>
                                    <td><?= $departmentsCount ?></td>
                                    <td>
                                        <?php if ($departmentsCount > 0): ?>
                                            <span class="badge bg-success">ডাটা আছে</span>
                                            <button class="btn btn-sm btn-info ms-2" type="button" data-bs-toggle="collapse" data-bs-target="#departmentsData">
                                                দেখুন
                                            </button>
                                        <?php else: ?>
                                            <span class="badge bg-danger">কোন ডাটা নেই</span>
                                            <a href="departments.php" class="btn btn-sm btn-primary ms-2">বিভাগ যোগ করুন</a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Sessions Data -->
                    <div class="collapse mt-3" id="sessionsData">
                        <div class="card card-body">
                            <h5>সেশন তথ্য</h5>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead>
                                        <tr>
                                            <th>আইডি</th>
                                            <th>সেশন নাম</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        if ($sessionsResult && $sessionsResult->num_rows > 0) {
                                            $sessionsResult->data_seek(0);
                                            while ($session = $sessionsResult->fetch_assoc()) {
                                                echo "<tr>";
                                                echo "<td>{$session['id']}</td>";
                                                echo "<td>{$session['session_name']}</td>";
                                                echo "</tr>";
                                            }
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Classes Data -->
                    <div class="collapse mt-3" id="classesData">
                        <div class="card card-body">
                            <h5>শ্রেণী তথ্য</h5>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead>
                                        <tr>
                                            <th>আইডি</th>
                                            <th>শ্রেণী নাম</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        if ($classesResult && $classesResult->num_rows > 0) {
                                            $classesResult->data_seek(0);
                                            while ($class = $classesResult->fetch_assoc()) {
                                                echo "<tr>";
                                                echo "<td>{$class['id']}</td>";
                                                echo "<td>{$class['class_name']}</td>";
                                                echo "</tr>";
                                            }
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Departments Data -->
                    <div class="collapse mt-3" id="departmentsData">
                        <div class="card card-body">
                            <h5>বিভাগ তথ্য</h5>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead>
                                        <tr>
                                            <th>আইডি</th>
                                            <th>বিভাগ নাম</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        if ($departmentsResult && $departmentsResult->num_rows > 0) {
                                            $departmentsResult->data_seek(0);
                                            while ($department = $departmentsResult->fetch_assoc()) {
                                                echo "<tr>";
                                                echo "<td>{$department['id']}</td>";
                                                echo "<td>{$department['department_name']}</td>";
                                                echo "</tr>";
                                            }
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-3">
                <a href="fee_management.php" class="btn btn-secondary">ফিরে যান</a>
            </div>
        </main>
    </div>
</div>

<?php
// Include footer
include_once 'includes/footer.php';
?>
