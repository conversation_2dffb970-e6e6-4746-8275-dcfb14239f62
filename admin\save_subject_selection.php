<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Include database connection
require_once '../includes/dbh.inc.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Initialize response array
$response = [
    'success' => false,
    'message' => '',
    'debug' => []
];

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Debug all POST data
    $response['debug'][] = "POST data: " . print_r($_POST, true);

    // Get student ID
    $studentId = $_POST['student_id'] ?? '';
    $response['debug'][] = "Student ID from form: " . $studentId;

    if (empty($studentId)) {
        $response['message'] = "Student ID is required";
        echo json_encode($response);
        exit;
    }

    // Get student data
    $studentQuery = "SELECT s.*, d.department_name
                     FROM students s
                     LEFT JOIN departments d ON s.department_id = d.id
                     WHERE s.student_id = ?";
    $stmt = $conn->prepare($studentQuery);
    $stmt->bind_param("s", $studentId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        $response['message'] = "Student not found";
        echo json_encode($response);
        exit;
    }

    $student = $result->fetch_assoc();
    $studentDbId = $student['id'];
    $response['debug'][] = "Student database ID: " . $studentDbId;

    // Get current session
    $currentSessionQuery = "SELECT id, session_name FROM sessions ORDER BY id DESC LIMIT 1";
    $currentSessionResult = $conn->query($currentSessionQuery);

    if (!$currentSessionResult || $currentSessionResult->num_rows === 0) {
        $response['message'] = "No active session found";
        echo json_encode($response);
        exit;
    }

    $currentSession = $currentSessionResult->fetch_assoc();
    $response['debug'][] = "Current session ID: " . $currentSession['id'];

    // Validate selection
    $requiredSubjectIds = isset($_POST['required_subjects']) ? $_POST['required_subjects'] : [];
    $optionalSubjectIds = isset($_POST['optional_subjects']) ? $_POST['optional_subjects'] : [];
    $fourthSubjectIds = isset($_POST['fourth_subjects']) ? $_POST['fourth_subjects'] : [];

    $response['debug'][] = "Required subjects: " . implode(", ", $requiredSubjectIds);
    $response['debug'][] = "Optional subjects: " . implode(", ", $optionalSubjectIds);
    $response['debug'][] = "Fourth subjects: " . implode(", ", $fourthSubjectIds);

    $totalSelected = count($requiredSubjectIds) + count($optionalSubjectIds) + count($fourthSubjectIds);
    $response['debug'][] = "Total selected subjects: " . $totalSelected;

    if ($totalSelected < 1) {
        $response['message'] = "At least one subject must be selected";
        echo json_encode($response);
        exit;
    }

    // Create student_subjects table if it doesn't exist
    $createTableQuery = "CREATE TABLE IF NOT EXISTS student_subjects (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        student_id INT(11) NOT NULL,
        subject_id INT(11) NOT NULL,
        category VARCHAR(20) NOT NULL DEFAULT 'optional',
        session_id INT(11) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";

    if (!$conn->query($createTableQuery)) {
        $response['message'] = "Error creating table: " . $conn->error;
        echo json_encode($response);
        exit;
    }

    // Delete existing selections
    $deleteQuery = "DELETE FROM student_subjects WHERE student_id = ?";
    $stmt = $conn->prepare($deleteQuery);
    $stmt->bind_param("i", $studentDbId);

    if (!$stmt->execute()) {
        $response['message'] = "Error deleting existing selections: " . $stmt->error;
        echo json_encode($response);
        exit;
    }

    $response['debug'][] = "Deleted existing selections";

    // Insert subjects one by one
    $insertQuery = "INSERT INTO student_subjects (student_id, subject_id, category, session_id) VALUES (?, ?, ?, ?)";
    $stmt = $conn->prepare($insertQuery);

    if (!$stmt) {
        $response['message'] = "Error preparing insert statement: " . $conn->error;
        echo json_encode($response);
        exit;
    }

    $insertCount = 0;
    $errors = [];

    // Insert required subjects
    foreach ($requiredSubjectIds as $subjectId) {
        $category = 'required';
        $stmt->bind_param("iisi", $studentDbId, $subjectId, $category, $currentSession['id']);

        if ($stmt->execute()) {
            $insertCount++;
            $response['debug'][] = "Inserted required subject ID: " . $subjectId;
        } else {
            $errors[] = "Error inserting required subject ID " . $subjectId . ": " . $stmt->error;
        }
    }

    // Insert optional subjects
    foreach ($optionalSubjectIds as $subjectId) {
        $category = 'optional';
        $stmt->bind_param("iisi", $studentDbId, $subjectId, $category, $currentSession['id']);

        if ($stmt->execute()) {
            $insertCount++;
            $response['debug'][] = "Inserted optional subject ID: " . $subjectId;
        } else {
            $errors[] = "Error inserting optional subject ID " . $subjectId . ": " . $stmt->error;
        }
    }

    // Insert fourth subjects
    foreach ($fourthSubjectIds as $subjectId) {
        $category = 'fourth';
        $stmt->bind_param("iisi", $studentDbId, $subjectId, $category, $currentSession['id']);

        if ($stmt->execute()) {
            $insertCount++;
            $response['debug'][] = "Inserted fourth subject ID: " . $subjectId;
        } else {
            $errors[] = "Error inserting fourth subject ID " . $subjectId . ": " . $stmt->error;
        }
    }

    // Check if all subjects were inserted
    if ($insertCount === $totalSelected) {
        $response['success'] = true;
        $response['message'] = "বিষয় নির্বাচন সফলভাবে সম্পন্ন হয়েছে!";
    } else {
        $response['message'] = "Not all subjects were inserted. " . implode(" ", $errors);
    }

    // Verify the subjects were saved
    $verifyQuery = "SELECT COUNT(*) as count FROM student_subjects WHERE student_id = ?";
    $stmt = $conn->prepare($verifyQuery);
    $stmt->bind_param("i", $studentDbId);
    $stmt->execute();
    $verifyResult = $stmt->get_result()->fetch_assoc();

    $response['debug'][] = "Verification: Found " . $verifyResult['count'] . " subjects for student ID: " . $studentDbId;

    if ($verifyResult['count'] != $totalSelected) {
        $response['debug'][] = "WARNING: Verification count doesn't match selected count";
    }
} else {
    $response['message'] = "Invalid request method";
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);
?>
