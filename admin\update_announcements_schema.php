<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Process the update
$messages = [];
$errors = [];

// Check if announcements table exists
$tableCheck = "SHOW TABLES LIKE 'announcements'";
$tableResult = $conn->query($tableCheck);

if ($tableResult->num_rows == 0) {
    // Create announcements table if it doesn't exist
    $createTable = "CREATE TABLE IF NOT EXISTS announcements (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        target_group ENUM('all', 'students', 'teachers', 'staff', 'admin') DEFAULT 'all',
        status ENUM('active', 'inactive') DEFAULT 'active',
        expire_date DATE NULL,
        created_by INT(11),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($createTable)) {
        $messages[] = "ঘোষণা টেবিল সফলভাবে তৈরি করা হয়েছে।";
    } else {
        $errors[] = "ঘোষণা টেবিল তৈরি করতে সমস্যা: " . $conn->error;
    }
} else {
    $messages[] = "ঘোষণা টেবিল বিদ্যমান।";
    
    // Check if target_group column exists
    $targetGroupCheck = "SHOW COLUMNS FROM announcements LIKE 'target_group'";
    $targetGroupResult = $conn->query($targetGroupCheck);
    
    if ($targetGroupResult->num_rows == 0) {
        // Add target_group column if it doesn't exist
        $addTargetGroup = "ALTER TABLE announcements 
                          ADD COLUMN target_group ENUM('all', 'students', 'teachers', 'staff', 'admin') 
                          DEFAULT 'all' AFTER content";
        
        if ($conn->query($addTargetGroup)) {
            $messages[] = "target_group কলাম সফলভাবে যোগ করা হয়েছে।";
        } else {
            $errors[] = "target_group কলাম যোগ করতে সমস্যা: " . $conn->error;
        }
    } else {
        $messages[] = "target_group কলাম বিদ্যমান।";
    }
    
    // Check if status column exists
    $statusCheck = "SHOW COLUMNS FROM announcements LIKE 'status'";
    $statusResult = $conn->query($statusCheck);
    
    if ($statusResult->num_rows == 0) {
        // Add status column if it doesn't exist
        $addStatus = "ALTER TABLE announcements 
                     ADD COLUMN status ENUM('active', 'inactive') 
                     DEFAULT 'active' AFTER target_group";
        
        if ($conn->query($addStatus)) {
            $messages[] = "status কলাম সফলভাবে যোগ করা হয়েছে।";
        } else {
            $errors[] = "status কলাম যোগ করতে সমস্যা: " . $conn->error;
        }
    } else {
        $messages[] = "status কলাম বিদ্যমান।";
    }
    
    // Check if expire_date column exists
    $expireDateCheck = "SHOW COLUMNS FROM announcements LIKE 'expire_date'";
    $expireDateResult = $conn->query($expireDateCheck);
    
    if ($expireDateResult->num_rows == 0) {
        // Add expire_date column if it doesn't exist
        $addExpireDate = "ALTER TABLE announcements 
                        ADD COLUMN expire_date DATE NULL AFTER status";
        
        if ($conn->query($addExpireDate)) {
            $messages[] = "expire_date (মেয়াদ শেষ) কলাম সফলভাবে যোগ করা হয়েছে।";
        } else {
            $errors[] = "expire_date কলাম যোগ করতে সমস্যা: " . $conn->error;
        }
    } else {
        $messages[] = "expire_date কলাম বিদ্যমান।";
    }
    
    // Update existing records if needed
    $updateRecords = "UPDATE announcements SET target_group = 'all', status = 'active' 
                     WHERE target_group IS NULL OR status IS NULL";
    
    if ($conn->query($updateRecords)) {
        $messages[] = "বিদ্যমান রেকর্ডগুলি সফলভাবে আপডেট করা হয়েছে।";
    }
}

$success = count($errors) === 0;
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>ঘোষণা টেবিল আপডেট - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .update-card {
            max-width: 800px;
            margin: 50px auto;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        .update-header {
            background-color: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #eee;
            border-radius: 10px 10px 0 0;
        }
        .update-body {
            padding: 30px;
        }
        .update-footer {
            background-color: #f8f9fa;
            padding: 20px;
            border-top: 1px solid #eee;
            border-radius: 0 0 10px 10px;
        }
        .log-item {
            padding: 10px 15px;
            border-left: 3px solid #28a745;
            background-color: #f8f9fa;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        .log-item.error {
            border-left-color: #dc3545;
        }
        .log-icon {
            margin-right: 10px;
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <?php include('includes/header.php'); ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include('includes/sidebar.php'); ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="card update-card">
                    <div class="update-header">
                        <h4>
                            <i class="fas fa-database me-2"></i>
                            ঘোষণা টেবিল আপডেট
                        </h4>
                    </div>
                    <div class="update-body">
                        <div class="mb-4">
                            <?php if ($success): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    ঘোষণা টেবিল সফলভাবে আপডেট করা হয়েছে।
                                </div>
                            <?php else: ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    ঘোষণা টেবিল আপডেট করতে কিছু সমস্যা হয়েছে।
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <h5 class="mb-3">প্রসেস লগ:</h5>
                        <div class="log-container">
                            <?php foreach ($messages as $message): ?>
                                <div class="log-item">
                                    <i class="fas fa-check-circle text-success log-icon"></i>
                                    <?php echo $message; ?>
                                </div>
                            <?php endforeach; ?>
                            
                            <?php foreach ($errors as $error): ?>
                                <div class="log-item error">
                                    <i class="fas fa-times-circle text-danger log-icon"></i>
                                    <?php echo $error; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <div class="update-footer">
                        <a href="announcements.php" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>
                            ঘোষণা পেজে ফিরে যান
                        </a>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 