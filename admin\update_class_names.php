<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

echo "<!DOCTYPE html>
<html lang='bn'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Class Names Update</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap' rel='stylesheet'>
    <style>
        body { font-family: 'Hind Siliguri', sans-serif; background: #f8f9fa; }
        .container { margin-top: 50px; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='card'>
            <div class='card-header bg-primary text-white'>
                <h4><i class='fas fa-edit'></i> Class Names Update to ONE & TWO</h4>
            </div>
            <div class='card-body'>";

try {
    // Show current classes
    echo "<h5>Current Classes:</h5>";
    $currentClassesQuery = "SELECT * FROM classes ORDER BY id";
    $currentResult = $conn->query($currentClassesQuery);
    
    if ($currentResult && $currentResult->num_rows > 0) {
        echo "<div class='table-responsive mb-4'>
                <table class='table table-striped table-sm'>
                    <thead class='table-secondary'>
                        <tr><th>ID</th><th>Current Name</th><th>Action</th></tr>
                    </thead>
                    <tbody>";
        
        while ($class = $currentResult->fetch_assoc()) {
            echo "<tr>
                    <td>{$class['id']}</td>
                    <td><strong>{$class['class_name']}</strong></td>
                    <td>";
            
            // Determine what to update to
            $newName = '';
            if (in_array($class['class_name'], ['১', '1', 'ক্লাস ১'])) {
                $newName = 'ONE';
            } elseif (in_array($class['class_name'], ['২', '2', 'ক্লাস ২'])) {
                $newName = 'TWO';
            }
            
            if ($newName) {
                echo "<span class='text-success'>Will update to: <strong>$newName</strong></span>";
            } else {
                echo "<span class='text-muted'>No change needed</span>";
            }
            
            echo "</td></tr>";
        }
        
        echo "</tbody></table></div>";
    }
    
    // Update class names
    $updates = [
        ['old' => ['১', '1', 'ক্লাস ১'], 'new' => 'ONE'],
        ['old' => ['২', '2', 'ক্লাস ২'], 'new' => 'TWO']
    ];
    
    echo "<h5>Update Results:</h5>";
    
    foreach ($updates as $update) {
        $oldNames = "'" . implode("', '", $update['old']) . "'";
        $newName = $update['new'];
        
        // Check if any classes need updating
        $checkQuery = "SELECT COUNT(*) as count FROM classes WHERE class_name IN ($oldNames)";
        $checkResult = $conn->query($checkQuery);
        
        if ($checkResult) {
            $row = $checkResult->fetch_assoc();
            $count = $row['count'];
            
            if ($count > 0) {
                // Update the classes
                $updateQuery = "UPDATE classes SET class_name = '$newName' WHERE class_name IN ($oldNames)";
                if ($conn->query($updateQuery)) {
                    echo "<p class='text-success'>✓ Updated $count classes to <strong>$newName</strong></p>";
                } else {
                    echo "<p class='text-danger'>✗ Failed to update to $newName: " . $conn->error . "</p>";
                }
            } else {
                echo "<p class='text-info'>ℹ No classes found to update to <strong>$newName</strong></p>";
            }
        }
    }
    
    // Show updated classes
    echo "<h5 class='mt-4'>Updated Classes:</h5>";
    $updatedClassesQuery = "SELECT * FROM classes ORDER BY id";
    $updatedResult = $conn->query($updatedClassesQuery);
    
    if ($updatedResult && $updatedResult->num_rows > 0) {
        echo "<div class='table-responsive'>
                <table class='table table-striped table-sm'>
                    <thead class='table-success'>
                        <tr><th>ID</th><th>Updated Name</th><th>Status</th></tr>
                    </thead>
                    <tbody>";
        
        while ($class = $updatedResult->fetch_assoc()) {
            $status = in_array($class['class_name'], ['ONE', 'TWO']) ? 
                     "<span class='badge bg-success'>Updated</span>" : 
                     "<span class='badge bg-secondary'>Unchanged</span>";
            
            echo "<tr>
                    <td>{$class['id']}</td>
                    <td><strong>{$class['class_name']}</strong></td>
                    <td>$status</td>
                  </tr>";
        }
        
        echo "</tbody></table></div>";
    }
    
    // Check students table for consistency
    echo "<h5 class='mt-4'>Students in Updated Classes:</h5>";
    $studentsQuery = "SELECT s.*, c.class_name FROM students s 
                     LEFT JOIN classes c ON s.class_id = c.id 
                     WHERE c.class_name IN ('ONE', 'TWO') 
                     ORDER BY c.class_name, s.roll_number";
    $studentsResult = $conn->query($studentsQuery);
    
    if ($studentsResult && $studentsResult->num_rows > 0) {
        echo "<div class='table-responsive'>
                <table class='table table-striped table-sm'>
                    <thead class='table-info'>
                        <tr><th>Roll</th><th>Name</th><th>Class</th><th>Session</th></tr>
                    </thead>
                    <tbody>";
        
        while ($student = $studentsResult->fetch_assoc()) {
            $name = $student['student_name'] ?? $student['first_name'] ?? 'N/A';
            $roll = $student['roll_number'] ?? $student['student_id'] ?? 'N/A';
            $session = $student['session'] ?? 'N/A';
            $class = $student['class_name'] ?? 'N/A';
            
            echo "<tr>
                    <td><strong>$roll</strong></td>
                    <td>$name</td>
                    <td><span class='badge bg-primary'>$class</span></td>
                    <td><span class='badge bg-info'>$session</span></td>
                  </tr>";
        }
        
        echo "</tbody></table></div>";
    } else {
        echo "<p class='text-muted'>No students found in ONE/TWO classes</p>";
    }
    
    echo "<div class='alert alert-success mt-4'>
            <h5>Update Complete!</h5>
            <p>Class names have been updated to ONE and TWO format.</p>
            <p>This will eliminate duplicate options in dropdowns.</p>
          </div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>
            <h5>Error!</h5>
            <p>Failed to update class names: " . $e->getMessage() . "</p>
          </div>";
}

echo "        <div class='mt-4'>
                <a href='class_exam_primary_lower_1_2.php' class='btn btn-primary'>
                    <i class='fas fa-arrow-left'></i> Back to Class 1-2 Dashboard
                </a>
            </div>
        </div>
    </div>
</div>
</body>
</html>";

if (isset($conn)) $conn->close();
?>
