<?php
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Create staff table if it doesn't exist
$staffTableQuery = "CREATE TABLE IF NOT EXISTS staff (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    staff_id VARCHAR(20) NOT NULL UNIQUE,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) NULL,
    phone VARCHAR(20) NULL,
    gender ENUM('male', 'female', 'other') NOT NULL,
    dob DATE NULL,
    address TEXT NULL,
    city VARCHAR(50) NULL,
    state VARCHAR(50) NULL,
    postal_code VARCHAR(20) NULL,
    country VARCHAR(50) NULL DEFAULT 'Bangladesh',
    joining_date DATE NULL,
    role VARCHAR(100) NULL,
    profile_photo VARCHAR(255) NULL,
    department_id INT(11) NULL,
    user_id INT(11) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
$conn->query($staffTableQuery);

// Add department_id column if it doesn't exist
$checkDeptIdColumnQuery = "SHOW COLUMNS FROM staff LIKE 'department_id'";
$deptIdColumnResult = $conn->query($checkDeptIdColumnQuery);
if ($deptIdColumnResult->num_rows == 0) {
    $addDeptIdColumnQuery = "ALTER TABLE staff ADD COLUMN department_id INT(11) NULL";
    $conn->query($addDeptIdColumnQuery);
}

// Add role column if it doesn't exist
$checkRoleColumnQuery = "SHOW COLUMNS FROM staff LIKE 'role'";
$roleColumnResult = $conn->query($checkRoleColumnQuery);
if ($roleColumnResult->num_rows == 0) {
    $addRoleColumnQuery = "ALTER TABLE staff ADD COLUMN role VARCHAR(100) NULL";
    $conn->query($addRoleColumnQuery);
}

// Add joining_date column if it doesn't exist
$checkJoiningDateColumnQuery = "SHOW COLUMNS FROM staff LIKE 'joining_date'";
$joiningDateColumnResult = $conn->query($checkJoiningDateColumnQuery);
if ($joiningDateColumnResult->num_rows == 0) {
    $addJoiningDateColumnQuery = "ALTER TABLE staff ADD COLUMN joining_date DATE NULL";
    $conn->query($addJoiningDateColumnQuery);
}

// Handle success and error messages
$success_msg = '';
$error_msg = '';

// Handle staff addition
if (isset($_POST['add_staff'])) {
    $staffId = $conn->real_escape_string($_POST['staff_id']);
    $firstName = $conn->real_escape_string($_POST['first_name']);
    $lastName = $conn->real_escape_string($_POST['last_name']);
    $email = $conn->real_escape_string($_POST['email'] ?? '');
    $phone = $conn->real_escape_string($_POST['phone'] ?? '');
    $gender = $conn->real_escape_string($_POST['gender'] ?? 'male');
    $departmentId = $_POST['department_id'] ?? null;
    $role = $conn->real_escape_string($_POST['role'] ?? '');
    $joiningDate = $conn->real_escape_string($_POST['joining_date'] ?? '');

    // Handle profile photo upload
    $profilePhoto = '';
    if (isset($_FILES['profile_photo']) && $_FILES['profile_photo']['error'] == 0) {
        $allowed = ['jpg', 'jpeg', 'png', 'gif'];
        $filename = $_FILES['profile_photo']['name'];
        $fileExt = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

        // Check if the file extension is allowed
        if (in_array($fileExt, $allowed)) {
            // Create unique filename
            $newFilename = 'staff_' . time() . '_' . uniqid() . '.' . $fileExt;
            $uploadDir = '../uploads/staff/';

            // Create directory if it doesn't exist
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            $uploadPath = $uploadDir . $newFilename;

            // Move the uploaded file
            if (move_uploaded_file($_FILES['profile_photo']['tmp_name'], $uploadPath)) {
                $profilePhoto = 'uploads/staff/' . $newFilename;
            } else {
                $error_msg = "ছবি আপলোড করতে সমস্যা হয়েছে";
            }
        } else {
            $error_msg = "অনুমোদিত ফাইল টাইপ: jpg, jpeg, png, gif";
        }
    }

    if (empty($staffId) || empty($firstName) || empty($lastName)) {
        $error_msg = "স্টাফ আইডি, নাম এবং পদবি আবশ্যক";
    } else {
        if (empty($error_msg)) { // Only proceed if there are no errors
            $insertQuery = "INSERT INTO staff (staff_id, first_name, last_name, email, phone, gender, department_id, role, joining_date, profile_photo)
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param("ssssssisss", $staffId, $firstName, $lastName, $email, $phone, $gender, $departmentId, $role, $joiningDate, $profilePhoto);

            if ($stmt->execute()) {
                $success_msg = "স্টাফ সফলভাবে যোগ করা হয়েছে";
            } else {
                $error_msg = "স্টাফ যোগ করতে সমস্যা হয়েছে: " . $conn->error;
            }
        }
    }
}

// Handle staff deletion
if (isset($_GET['delete'])) {
    $staffId = $_GET['delete'];
    $deleteQuery = "DELETE FROM staff WHERE id = ?";
    $stmt = $conn->prepare($deleteQuery);
    $stmt->bind_param("i", $staffId);

    if ($stmt->execute()) {
        $success_msg = "স্টাফ সফলভাবে মুছে ফেলা হয়েছে";
    } else {
        $error_msg = "স্টাফ মুছে ফেলতে সমস্যা হয়েছে: " . $conn->error;
    }
}

// Handle search functionality
$search = isset($_GET['search']) ? $conn->real_escape_string($_GET['search']) : '';
$searchCondition = '';

if (!empty($search)) {
    $searchCondition = " WHERE s.staff_id LIKE '%$search%' OR
                         s.first_name LIKE '%$search%' OR
                         s.last_name LIKE '%$search%' OR
                         s.email LIKE '%$search%' OR
                         s.phone LIKE '%$search%' OR
                         s.role LIKE '%$search%' OR
                         d.department_name LIKE '%$search%'";
}

// Get all staff with department information
$staffQuery = "SELECT s.*, d.department_name
               FROM staff s
               LEFT JOIN departments d ON s.department_id = d.id
               $searchCondition
               ORDER BY s.first_name, s.last_name";
$staffMembers = $conn->query($staffQuery);

// Get all departments for the dropdown
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>স্টাফ ব্যবস্থাপনা | অ্যাডমিন ড্যাশবোর্ড</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css">
    <style>
        :root {
            --primary-color: #4e73df;
            --secondary-color: #858796;
            --success-color: #1cc88a;
            --info-color: #36b9cc;
            --warning-color: #f6c23e;
            --danger-color: #e74a3b;
            --light-color: #f8f9fc;
            --dark-color: #5a5c69;
        }

        body {
            background-color: #f8f9fc;
            font-family: 'Hind Siliguri', sans-serif;
        }

        /* Sidebar Styles */
        .sidebar {
            height: 100vh;
            background: linear-gradient(180deg, #4e73df 10%, #224abe 100%);
            position: fixed;
            padding-top: 20px;
            transition: all 0.3s;
            z-index: 1;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }

        .sidebar .sidebar-brand {
            padding: 1rem;
            text-align: center;
        }

        .sidebar a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            padding: 1rem;
            display: block;
            transition: all 0.3s;
            border-left: 3px solid transparent;
        }

        .sidebar a:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
            border-left: 3px solid #fff;
        }

        .sidebar a.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.2);
            border-left: 3px solid #fff;
        }

        .sidebar-divider {
            border-top: 1px solid rgba(255, 255, 255, 0.15);
            margin: 0 1rem 1rem;
        }

        /* Content Area */
        .content {
            margin-left: 220px;
            padding: 1.5rem;
            transition: all 0.3s;
        }

        /* Card Styles */
        .card {
            border: none;
            border-radius: 0.35rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            margin-bottom: 1.5rem;
        }

        .card-header {
            background: linear-gradient(180deg, #4e73df 0%, #3a66d6 100%);
            color: white;
            padding: 0.75rem 1.25rem;
            border-radius: 0.35rem 0.35rem 0 0;
            border: none;
        }

        /* Form Styles */
        .form-control, .form-select {
            border-radius: 0.35rem;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            border: 1px solid #d1d3e2;
        }

        .form-control:focus, .form-select:focus {
            border-color: #bac8f3;
            box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
        }

        .btn-primary {
            background: linear-gradient(180deg, #4e73df 0%, #3a66d6 100%);
            border: none;
            border-radius: 0.35rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
        }

        .btn-primary:hover {
            background: linear-gradient(180deg, #3a66d6 0%, #224abe 100%);
            transform: translateY(-1px);
        }

        /* Table Styles */
        .table {
            border-radius: 0.35rem;
            overflow: hidden;
        }

        .table thead th {
            background-color: #f8f9fc;
            border-bottom: 2px solid #e3e6f0;
            font-weight: 600;
            color: #5a5c69;
        }

        .table-hover tbody tr:hover {
            background-color: #f2f4ff;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
                width: 100%;
            }
            .content {
                margin-left: 0;
            }
            .sidebar a {
                display: inline-block;
                width: auto;
                border-left: none;
                border-bottom: 3px solid transparent;
            }
            .sidebar a:hover, .sidebar a.active {
                border-left: none;
                border-bottom: 3px solid #fff;
            }
        }

        /* Custom Styles */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e3e6f0;
        }

        .profile-img-container {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
            box-shadow: 0 0.15rem 0.5rem 0 rgba(58, 59, 69, 0.15);
        }

        .action-buttons .btn {
            border-radius: 50%;
            width: 32px;
            height: 32px;
            padding: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.25rem;
        }

        .search-box {
            position: relative;
            margin-bottom: 1rem;
        }

        .search-box .form-control {
            padding-left: 2.5rem;
            border-radius: 2rem;
        }

        .search-box .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #d1d3e2;
        }
    </style></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar">
                <div class="sidebar-brand">
                    <h4 class="text-white mb-0">অ্যাডমিন প্যানেল</h4>
                </div>
                <div class="sidebar-divider"></div>
                <a href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড</a>
                <a href="students.php"><i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী</a>
                <a href="teachers.php"><i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক</a>
                <a href="staff.php" class="active"><i class="fas fa-user-tie me-2"></i> স্টাফ</a>
                <a href="sessions.php"><i class="fas fa-calendar-alt me-2"></i> সেশন</a>
                <a href="classes.php"><i class="fas fa-chalkboard me-2"></i> ক্লাস</a>
                <a href="departments.php"><i class="fas fa-building me-2"></i> বিভাগ</a>
                <div class="sidebar-divider"></div>
                <a href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i> লগআউট</a>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 content">
                <div class="page-header">
                    <h2 class="mb-0">স্টাফ ব্যবস্থাপনা</h2>
                    <div class="d-flex">
                        <div class="search-box">
                            <form method="GET" action="staff.php">
                                <div class="input-group">
                                    <span class="search-icon"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" name="search" placeholder="স্টাফ খুঁজুন..." value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>">
                                    <button type="submit" class="btn btn-primary"><i class="fas fa-search"></i></button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Success and Error Messages -->
                <?php if (!empty($success_msg)): ?>
                    <div class="alert alert-success alert-dismissible fade show shadow-sm" role="alert">
                        <i class="fas fa-check-circle me-2"></i> <?php echo $success_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error_msg)): ?>
                    <div class="alert alert-danger alert-dismissible fade show shadow-sm" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- Add Staff Form -->
                    <div class="col-md-4 mb-4">
                        <div class="card shadow-sm">
                            <div class="card-header">
                                <h5 class="card-title mb-0"><i class="fas fa-user-plus me-2"></i>নতুন স্টাফ যোগ করুন</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="staff.php" enctype="multipart/form-data">
                                    <div class="row">
                                        <div class="col-md-12 mb-3">
                                            <div class="form-floating">
                                                <input type="text" class="form-control" id="staff_id" name="staff_id" placeholder="স্টাফ আইডি" required>
                                                <label for="staff_id">স্টাফ আইডি*</label>
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <div class="form-floating">
                                                <input type="text" class="form-control" id="first_name" name="first_name" placeholder="নাম" required>
                                                <label for="first_name">নাম*</label>
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <div class="form-floating">
                                                <input type="text" class="form-control" id="last_name" name="last_name" placeholder="পদবি" required>
                                                <label for="last_name">পদবি*</label>
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <div class="form-floating">
                                                <input type="email" class="form-control" id="email" name="email" placeholder="ইমেইল">
                                                <label for="email">ইমেইল</label>
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <div class="form-floating">
                                                <input type="text" class="form-control" id="phone" name="phone" placeholder="ফোন নম্বর">
                                                <label for="phone">ফোন নম্বর</label>
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <div class="form-floating">
                                                <select class="form-select" id="gender" name="gender" aria-label="লিঙ্গ">
                                                    <option value="male">পুরুষ</option>
                                                    <option value="female">মহিলা</option>
                                                    <option value="other">অন্যান্য</option>
                                                </select>
                                                <label for="gender">লিঙ্গ</label>
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <div class="form-floating">
                                                <select class="form-select" id="department_id" name="department_id" aria-label="বিভাগ">
                                                    <option value="">বিভাগ নির্বাচন করুন</option>
                                                    <?php if ($departments && $departments->num_rows > 0): ?>
                                                        <?php while ($dept = $departments->fetch_assoc()): ?>
                                                            <option value="<?php echo $dept['id']; ?>">
                                                                <?php echo htmlspecialchars($dept['department_name']); ?>
                                                            </option>
                                                        <?php endwhile; ?>
                                                    <?php endif; ?>
                                                </select>
                                                <label for="department_id">বিভাগ</label>
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <div class="form-floating">
                                                <input type="text" class="form-control" id="role" name="role" placeholder="পদবি/ভূমিকা">
                                                <label for="role">পদবি/ভূমিকা</label>
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <div class="form-floating">
                                                <input type="date" class="form-control" id="joining_date" name="joining_date">
                                                <label for="joining_date">যোগদানের তারিখ</label>
                                            </div>
                                        </div>

                                        <div class="col-md-12 mb-3">
                                            <label for="profile_photo" class="form-label">প্রোফাইল ছবি</label>
                                            <input type="file" class="form-control" id="profile_photo" name="profile_photo" accept="image/*">
                                            <div class="form-text">অনুমোদিত ফাইল টাইপ: jpg, jpeg, png, gif</div>
                                        </div>

                                        <div class="col-md-12 mb-3">
                                            <div id="image-preview" class="mt-2 d-none text-center">
                                                <img src="" alt="Image Preview" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-grid gap-2">
                                        <button type="submit" name="add_staff" class="btn btn-primary">
                                            <i class="fas fa-user-plus me-2"></i>স্টাফ যোগ করুন
                                        </button>
                                    </div>
                                </form>

                                <script>
                                    // Image preview script
                                    document.getElementById('profile_photo').addEventListener('change', function(e) {
                                        const preview = document.getElementById('image-preview');
                                        const previewImg = preview.querySelector('img');
                                        const file = e.target.files[0];

                                        if (file) {
                                            const reader = new FileReader();
                                            reader.onload = function(e) {
                                                previewImg.src = e.target.result;
                                                preview.classList.remove('d-none');
                                            }
                                            reader.readAsDataURL(file);
                                        } else {
                                            preview.classList.add('d-none');
                                        }
                                    });
                                </script>
                            </div>
                        </div>
                    </div>

                    <!-- Staff List -->
                    <div class="col-md-8 mb-4">
                        <div class="card shadow-sm">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0"><i class="fas fa-user-tie me-2"></i>স্টাফ তালিকা</h5>
                                <span class="badge bg-primary rounded-pill"><?php echo $staffMembers->num_rows; ?> জন স্টাফ</span>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>স্টাফ আইডি</th>
                                                <th>ছবি</th>
                                                <th>নাম</th>
                                                <th>বিভাগ</th>
                                                <th>পদবি</th>
                                                <th>ফোন</th>
                                                <th>অ্যাকশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($staffMembers && $staffMembers->num_rows > 0): ?>
                                                <?php while ($staff = $staffMembers->fetch_assoc()): ?>
                                                    <tr>
                                                        <td>
                                                            <span class="badge bg-light text-dark">
                                                                <?php echo htmlspecialchars($staff['staff_id']); ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <?php if (!empty($staff['profile_photo'])): ?>
                                                                <div class="profile-img-container">
                                                                    <img src="../<?php echo $staff['profile_photo']; ?>" alt="<?php echo htmlspecialchars($staff['first_name']); ?>" style="width: 100%; height: 100%; object-fit: cover;">
                                                                </div>
                                                            <?php else: ?>
                                                                <div class="profile-img-container d-flex align-items-center justify-content-center bg-light">
                                                                    <i class="fas fa-user text-secondary"></i>
                                                                </div>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <div class="fw-bold"><?php echo htmlspecialchars($staff['first_name']); ?></div>
                                                            <div class="text-muted small"><?php echo htmlspecialchars($staff['last_name']); ?></div>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($staff['department_name'] ?? 'N/A'); ?></td>
                                                        <td><?php echo htmlspecialchars($staff['role'] ?? 'N/A'); ?></td>
                                                        <td><?php echo htmlspecialchars($staff['phone'] ?? 'N/A'); ?></td>
                                                        <td>
                                                            <div class="action-buttons">
                                                                <a href="edit_staff.php?id=<?php echo $staff['id']; ?>" class="btn btn-warning" title="সম্পাদনা করুন">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                                <a href="staff.php?delete=<?php echo $staff['id']; ?>" class="btn btn-danger" title="মুছে ফেলুন" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই স্টাফকে মুছে ফেলতে চান?')">
                                                                    <i class="fas fa-trash"></i>
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="7" class="text-center py-4">
                                                        <div class="text-muted">
                                                            <i class="fas fa-info-circle me-2"></i>কোন স্টাফ পাওয়া যায়নি
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white py-4 mt-auto shadow-sm">
        <div class="container-fluid">
            <div class="d-flex align-items-center justify-content-between small">
                <div class="text-muted">কপিরাইট &copy; আপনার স্কুল/কলেজ <?php echo date('Y'); ?></div>
                <div>
                    <a href="#" class="text-decoration-none">গোপনীয়তা নীতি</a>
                    &middot;
                    <a href="#" class="text-decoration-none">ব্যবহারের শর্তাবলী</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS for enhanced UI interactions -->
    <script>
        // Add animation to alerts
        document.addEventListener('DOMContentLoaded', function() {
            // Fade out alerts after 5 seconds
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(function(alert) {
                    alert.classList.add('fade');
                    setTimeout(function() {
                        alert.remove();
                    }, 500);
                });
            }, 5000);

            // Add hover effect to table rows
            const tableRows = document.querySelectorAll('tbody tr');
            tableRows.forEach(function(row) {
                row.addEventListener('mouseenter', function() {
                    this.classList.add('shadow-sm');
                    this.style.transform = 'translateY(-2px)';
                    this.style.transition = 'all 0.3s';
                });

                row.addEventListener('mouseleave', function() {
                    this.classList.remove('shadow-sm');
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>