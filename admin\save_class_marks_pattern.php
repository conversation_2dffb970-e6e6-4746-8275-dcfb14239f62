<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

require_once '../includes/dbh.inc.php';
require_once 'class_subjects_helper.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
    exit();
}

$classId = isset($input['class_id']) ? intval($input['class_id']) : 0;
$departmentId = isset($input['department_id']) && !empty($input['department_id']) ? intval($input['department_id']) : null;
$patterns = isset($input['patterns']) ? $input['patterns'] : [];

if (!$classId) {
    echo json_encode(['success' => false, 'message' => 'Class ID is required']);
    exit();
}

if (empty($patterns)) {
    echo json_encode(['success' => false, 'message' => 'No patterns provided']);
    exit();
}

try {
    // Start transaction
    $conn->begin_transaction();

    // Validate that all subjects belong to the specified class
    $classSubjects = getClassSubjects($classId, $departmentId);
    $validSubjectIds = array_column($classSubjects, 'subject_id');

    $savedCount = 0;
    $errors = [];

    foreach ($patterns as $pattern) {
        $subjectId = intval($pattern['subject_id']);
        
        // Validate subject belongs to class
        if (!in_array($subjectId, $validSubjectIds)) {
            $errors[] = "Subject ID $subjectId does not belong to the specified class";
            continue;
        }

        // Validate pattern data
        $hasCq = isset($pattern['has_cq']) ? ($pattern['has_cq'] ? 1 : 0) : 0;
        $hasMcq = isset($pattern['has_mcq']) ? ($pattern['has_mcq'] ? 1 : 0) : 0;
        $hasPractical = isset($pattern['has_practical']) ? ($pattern['has_practical'] ? 1 : 0) : 0;
        
        $cqMarks = floatval($pattern['cq_marks'] ?? 0);
        $mcqMarks = floatval($pattern['mcq_marks'] ?? 0);
        $practicalMarks = floatval($pattern['practical_marks'] ?? 0);
        $totalMarks = floatval($pattern['total_marks'] ?? 100);

        // Validate marks
        if ($totalMarks <= 0) {
            $errors[] = "Total marks must be greater than 0 for subject ID $subjectId";
            continue;
        }

        // Calculate sum of enabled components
        $sum = 0;
        if ($hasCq) $sum += $cqMarks;
        if ($hasMcq) $sum += $mcqMarks;
        if ($hasPractical) $sum += $practicalMarks;

        // Check if sum matches total (with small tolerance for floating point)
        if (abs($sum - $totalMarks) > 0.01) {
            $errors[] = "Sum of marks components ($sum) does not match total marks ($totalMarks) for subject ID $subjectId";
            continue;
        }

        // Check if at least one component is enabled
        if (!$hasCq && !$hasMcq && !$hasPractical) {
            $errors[] = "At least one component must be enabled for subject ID $subjectId";
            continue;
        }

        // Check if pattern already exists
        $checkQuery = "SELECT id FROM subject_exam_pattern WHERE subject_id = ?";
        $checkStmt = $conn->prepare($checkQuery);
        $checkStmt->bind_param("i", $subjectId);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result();

        if ($checkResult->num_rows > 0) {
            // Update existing pattern
            $updateQuery = "UPDATE subject_exam_pattern SET 
                           has_cq = ?, has_mcq = ?, has_practical = ?,
                           cq_marks = ?, mcq_marks = ?, practical_marks = ?, total_marks = ?,
                           updated_at = NOW()
                           WHERE subject_id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bind_param("iiiddddi", $hasCq, $hasMcq, $hasPractical, 
                                   $cqMarks, $mcqMarks, $practicalMarks, $totalMarks, $subjectId);
            
            if ($updateStmt->execute()) {
                $savedCount++;
            } else {
                $errors[] = "Failed to update pattern for subject ID $subjectId: " . $updateStmt->error;
            }
        } else {
            // Insert new pattern
            $insertQuery = "INSERT INTO subject_exam_pattern 
                           (subject_id, has_cq, has_mcq, has_practical, 
                            cq_marks, mcq_marks, practical_marks, total_marks, 
                            status, created_at, updated_at) 
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())";
            $insertStmt = $conn->prepare($insertQuery);
            $insertStmt->bind_param("iiidddds", $subjectId, $hasCq, $hasMcq, $hasPractical,
                                   $cqMarks, $mcqMarks, $practicalMarks, $totalMarks);
            
            if ($insertStmt->execute()) {
                $savedCount++;
            } else {
                $errors[] = "Failed to insert pattern for subject ID $subjectId: " . $insertStmt->error;
            }
        }
    }

    if (!empty($errors)) {
        $conn->rollback();
        echo json_encode([
            'success' => false, 
            'message' => 'Validation errors occurred',
            'errors' => $errors,
            'saved_count' => 0
        ]);
        exit();
    }

    // Commit transaction
    $conn->commit();

    echo json_encode([
        'success' => true,
        'message' => "Successfully saved $savedCount marks patterns",
        'saved_count' => $savedCount
    ]);

} catch (Exception $e) {
    $conn->rollback();
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
