/* Modern Dashboard Layout CSS */
@import url('https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap');

:root {
    --primary-color: #4361ee;
    --primary-light: #4895ef;
    --primary-dark: #3f37c9;
    --secondary-color: #4cc9f0;
    --accent-color: #f72585;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --dark-color: #212529;
    --light-color: #f8f9fa;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 12px 28px rgba(0, 0, 0, 0.15);
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
    --border-radius-sm: 0.25rem;
    --border-radius: 0.5rem;
    --border-radius-lg: 1rem;
    --border-radius-xl: 1.5rem;
    --font-family: 'Hind Siliguri', sans-serif;
}

/* Base Styles */
body, html {
    font-family: var(--font-family);
    background-color: var(--gray-100);
    color: var(--gray-800);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Dashboard Layout */
.dashboard-container {
    display: flex;
    min-height: 100vh;
    position: relative;
}

/* Header */
.dashboard-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    display: flex;
    align-items: center;
    padding: 0 1.5rem;
    z-index: 1000;
    box-shadow: var(--shadow-md);
}

.header-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-weight: 700;
    font-size: 1.5rem;
}

.header-logo img {
    height: 40px;
    width: auto;
}

.header-title {
    flex-grow: 1;
    text-align: center;
    font-weight: 600;
    font-size: 1.25rem;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Sidebar */
.dashboard-sidebar {
    width: 280px;
    background: white;
    height: 100vh;
    position: fixed;
    top: 70px;
    left: 0;
    overflow-y: auto;
    z-index: 900;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    padding-bottom: 70px;
}

.sidebar-nav {
    padding: 1.5rem 1rem;
}

.nav-section {
    margin-bottom: 1.5rem;
}

.nav-section-title {
    font-size: 0.8rem;
    text-transform: uppercase;
    color: var(--gray-600);
    font-weight: 600;
    padding: 0.5rem 1rem;
    letter-spacing: 0.05em;
}

.nav-items {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
    font-weight: 500;
}

.nav-link:hover {
    background-color: var(--gray-100);
    color: var(--primary-color);
}

.nav-link.active {
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
    font-weight: 600;
}

.nav-link i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
    width: 24px;
    text-align: center;
}

/* Main Content */
.dashboard-content {
    flex-grow: 1;
    margin-left: 280px;
    margin-top: 70px;
    padding: 2rem;
    transition: var(--transition-normal);
}

/* Cards */
.dashboard-card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    overflow: hidden;
    height: 100%;
}

.dashboard-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-5px);
}

.card-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    background-color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-weight: 600;
    margin: 0;
    color: var(--gray-800);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-title i {
    color: var(--primary-color);
}

.card-body {
    padding: 1.5rem;
}

/* Responsive */
@media (max-width: 992px) {
    .dashboard-sidebar {
        width: 70px;
        transform: translateX(0);
    }
    
    .dashboard-sidebar:hover {
        width: 280px;
    }
    
    .dashboard-content {
        margin-left: 70px;
    }
    
    .nav-link span {
        display: none;
    }
    
    .dashboard-sidebar:hover .nav-link span {
        display: inline;
    }
}

@media (max-width: 768px) {
    .dashboard-sidebar {
        transform: translateX(-100%);
        width: 280px;
    }
    
    .dashboard-content {
        margin-left: 0;
    }
    
    .nav-link span {
        display: inline;
    }
    
    .sidebar-open .dashboard-sidebar {
        transform: translateX(0);
    }
    
    .sidebar-toggle {
        display: block;
    }
}

/* Utilities */
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-light { background-color: var(--light-color) !important; }
.bg-dark { background-color: var(--dark-color) !important; }

.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }

.rounded { border-radius: var(--border-radius) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-circle { border-radius: 50% !important; }

.transition { transition: var(--transition-normal) !important; }
