<?php
// Database Connection
require_once 'includes/dbh.inc.php';

// Check admin user
$sql = "SELECT * FROM users WHERE username='admin' AND user_type='admin'";
$result = $conn->query($sql);

echo "<h1>Admin User Check</h1>";

if ($result->num_rows > 0) {
    $admin = $result->fetch_assoc();
    echo "<p style='color:green;'>Admin user exists:</p>";
    echo "<ul>";
    echo "<li>ID: " . $admin['id'] . "</li>";
    echo "<li>Username: " . $admin['username'] . "</li>";
    echo "<li>User Type: " . $admin['user_type'] . "</li>";
    echo "</ul>";
    
    // Check if password is valid hash
    $validHash = password_get_info($admin['password'])['algo'] !== 0;
    if (!$validHash) {
        echo "<p style='color:red;'>WARNING: Password hash appears to be invalid!</p>";
    }
} else {
    echo "<p style='color:red;'>No admin user found in the database!</p>";
    
    // Look for any admin type users
    $sql = "SELECT * FROM users WHERE user_type='admin'";
    $result = $conn->query($sql);
    
    if ($result->num_rows > 0) {
        echo "<p>Found other admin users:</p>";
        echo "<ul>";
        while ($row = $result->fetch_assoc()) {
            echo "<li>Username: " . $row['username'] . "</li>";
        }
        echo "</ul>";
    }
}

echo "<p><a href='reset_admin.php'>Reset Admin Password</a> | <a href='index.php'>Go to Login</a></p>";
?> 