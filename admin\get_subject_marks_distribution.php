<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

require_once '../includes/dbh.inc.php';

// Set header to return JSON
header('Content-Type: application/json');

// Check if subject_id is provided
if (!isset($_GET['subject_id']) || empty($_GET['subject_id'])) {
    echo json_encode(['success' => false, 'message' => 'Subject ID is required']);
    exit();
}

$subject_id = intval($_GET['subject_id']);

// Get subject marks distribution
$query = "SELECT smd.*, s.subject_name, s.subject_code
          FROM subject_marks_distribution smd
          JOIN subjects s ON smd.subject_id = s.id
          WHERE smd.subject_id = ? AND smd.is_active = 1";

$stmt = $conn->prepare($query);
$stmt->bind_param("i", $subject_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $distribution = $result->fetch_assoc();
    echo json_encode([
        'success' => true,
        'distribution' => $distribution
    ]);
} else {
    // If no distribution found, get subject info and return default values
    $subjectQuery = "SELECT id, subject_name, subject_code FROM subjects WHERE id = ?";
    $stmt = $conn->prepare($subjectQuery);
    $stmt->bind_param("i", $subject_id);
    $stmt->execute();
    $subjectResult = $stmt->get_result();

    if ($subjectResult->num_rows > 0) {
        $subject = $subjectResult->fetch_assoc();
        echo json_encode([
            'success' => true,
            'distribution' => [
                'subject_id' => $subject['id'],
                'subject_name' => $subject['subject_name'],
                'subject_code' => $subject['subject_code'],
                'cq_marks' => 70,
                'mcq_marks' => 30,
                'practical_marks' => 0,
                'total_marks' => 100,
                'min_cq_pass_marks' => 23.10,
                'min_mcq_pass_marks' => 9.90,
                'min_practical_pass_marks' => 0,
                'is_active' => 1
            ]
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Subject not found']);
    }
}
