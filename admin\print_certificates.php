<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Initialize variables
$students = [];
$certificateSettings = [];

// Check if student_ids parameter is provided
if (!isset($_GET['student_ids']) || empty($_GET['student_ids'])) {
    header("Location: advanced_certificate.php");
    exit();
}

// Get student IDs from URL
$studentIds = explode(',', $_GET['student_ids']);

// Validate student IDs
if (empty($studentIds)) {
    header("Location: advanced_certificate.php");
    exit();
}

// Get student data
$placeholders = str_repeat('?,', count($studentIds) - 1) . '?';
$types = str_repeat('i', count($studentIds));

$query = "SELECT s.*, d.department_name, c.class_name, ss.session_name,
          CONCAT(s.first_name, ' ', s.last_name) AS full_name,
          s.father_name, s.mother_name
          FROM students s
          LEFT JOIN departments d ON s.department_id = d.id
          LEFT JOIN classes c ON s.class_id = c.id
          LEFT JOIN sessions ss ON s.session_id = ss.id
          WHERE s.id IN ($placeholders)
          ORDER BY s.first_name, s.last_name";

$stmt = $conn->prepare($query);
$stmt->bind_param($types, ...$studentIds);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $students[] = $row;
    }
} else {
    header("Location: advanced_certificate.php");
    exit();
}

// Check if certificate_settings table exists
$tableCheck = $conn->query("SHOW TABLES LIKE 'certificate_settings'");
if ($tableCheck->num_rows == 0) {
    // Create certificate_settings table if it doesn't exist
    $createTableSQL = "CREATE TABLE certificate_settings (
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        header_text VARCHAR(255) DEFAULT 'Certificate of Achievement',
        footer_text VARCHAR(255) DEFAULT 'This certificate is awarded for outstanding performance',
        signature_text VARCHAR(255) DEFAULT 'Principal',
        logo_path VARCHAR(255) DEFAULT NULL,
        watermark_path VARCHAR(255) DEFAULT NULL,
        border_color VARCHAR(20) DEFAULT '#000000',
        header_color VARCHAR(20) DEFAULT '#000000',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $conn->query($createTableSQL);

    // Insert default settings
    $insertSQL = "INSERT INTO certificate_settings (id, header_text, footer_text, signature_text, border_color, header_color)
                 VALUES (1, 'Certificate of Achievement', 'This certificate is awarded for outstanding performance', 'Principal', '#000000', '#000000')";
    $conn->query($insertSQL);
}

// Get certificate settings
$settingsQuery = $conn->query("SELECT * FROM certificate_settings WHERE id = 1");
if ($settingsQuery && $settingsQuery->num_rows > 0) {
    $certificateSettings = $settingsQuery->fetch_assoc();
} else {
    // Insert default settings if no settings found
    $insertSQL = "INSERT INTO certificate_settings (id, header_text, footer_text, signature_text, border_color, header_color)
                 VALUES (1, 'Certificate of Achievement', 'This certificate is awarded for outstanding performance', 'Principal', '#000000', '#000000')";
    $conn->query($insertSQL);
    $settingsQuery = $conn->query("SELECT * FROM certificate_settings WHERE id = 1");
    $certificateSettings = $settingsQuery->fetch_assoc();
}

// Format date in Bengali
function formatDateBengali($date) {
    $englishMonths = array('January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December');
    $bengaliMonths = array('জানুয়ারি', 'ফেব্রুয়ারি', 'মার্চ', 'এপ্রিল', 'মে', 'জুন', 'জুলাই', 'আগস্ট', 'সেপ্টেম্বর', 'অক্টোবর', 'নভেম্বর', 'ডিসেম্বর');

    $englishDays = array('0', '1', '2', '3', '4', '5', '6', '7', '8', '9');
    $bengaliDays = array('০', '১', '২', '৩', '৪', '৫', '৬', '৭', '৮', '৯');

    $formattedDate = date('d F Y', strtotime($date));
    $formattedDate = str_replace($englishMonths, $bengaliMonths, $formattedDate);
    $formattedDate = str_replace($englishDays, $bengaliDays, $formattedDate);

    return $formattedDate;
}



// Current date in Bengali
$currentDate = formatDateBengali(date('Y-m-d'));
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>সার্টিফিকেট প্রিন্ট - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    

    <!-- Hind Siliguri Font -->
    
    <style>
        @page {
            size: A4 landscape;
            margin: 0;
        }

        body, html, h1, h2, h3, h4, h5, h6, p, span, div {
            font-family: 'Hind Siliguri', sans-serif;
        }
        @media print {
            body {
                margin: 0;
                padding: 0;
                background: white;
                font-family: 'Hind Siliguri', sans-serif;
            }
            .no-print {
                display: none !important;
            }
            .certificate-container {
                page-break-after: always;
                margin: 0;
                padding: 0;
                border: none !important;
                box-shadow: none !important;
                width: 100%;
                height: 100%;
            }
            .certificate {
                border: 5px solid <?php echo $certificateSettings['border_color']; ?> !important;
                margin: 0 !important;
                padding: 30px !important;
                width: 100% !important;
                height: 100% !important;
                box-sizing: border-box !important;
            }
        }

        body {
            background-color: #f5f5f5;
            font-family: 'Hind Siliguri', sans-serif;
        }
        .certificate-container {
            margin: 20px auto;
            max-width: 1000px;
            background: white;
            padding: 0;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            aspect-ratio: 1.414 / 1; /* A4 landscape ratio */
        }
        .certificate {
            border: 5px solid <?php echo $certificateSettings['border_color']; ?>;
            padding: 40px;
            position: relative;
            background-color: white;
            margin: 0 auto;
            width: 100%;
            height: 100%;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        .certificate-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }
        .certificate-title-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 15px 0;
        }
        .certificate-title {
            color: <?php echo $certificateSettings['header_color']; ?>;
            font-size: 32px;
            font-weight: bold;
            margin: 0 0 5px 0;
            text-transform: uppercase;
            letter-spacing: 2px;
            position: relative;
            display: inline-block;
        }
        .certificate-subtitle {
            color: <?php echo $certificateSettings['header_color']; ?>;
            font-size: 24px;
            font-weight: bold;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            display: inline-block;
        }
        .certificate-title-container:after {
            content: '';
            position: absolute;
            width: 60%;
            height: 2px;
            background-color: <?php echo $certificateSettings['header_color']; ?>;
            bottom: -8px;
            left: 20%;
        }
        .certificate-body {
            flex-grow: 1;
            position: relative;
            z-index: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 20px 60px;
            text-align: justify;
        }
        .certificate-intro {
            font-size: 18px;
            margin-bottom: 20px;
            font-style: italic;
            text-align: center;
        }
        .student-name {
            font-size: 36px;
            font-weight: bold;
            margin: 15px 0;
            color: #333;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.1);
            text-align: center;
        }
        .certificate-content {
            margin: 20px 0;
            font-size: 18px;
            line-height: 1.8;
            text-align: justify;
        }
        .student-info {
            font-weight: bold;
        }
        .certificate-text {
            font-size: 18px;
            margin: 30px 0;
            line-height: 1.8;
            text-align: justify;
            padding: 0 20px;
        }
        .certificate-footer {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #f0f0f0;
        }
        .certificate-date {
            text-align: left;
            font-style: italic;
            flex: 1;
        }
        .certificate-seal {
            text-align: center;
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .signature-area {
            text-align: right;
            flex: 1;
        }
        .signature-line {
            width: 200px;
            border-top: 1px solid #000;
            margin: 10px 0 5px auto;
        }
        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            opacity: 0.07;
            z-index: 0;
            max-width: 70%;
            max-height: 70%;
        }
        .logo {
            max-height: 80px;
            margin-bottom: 10px;
        }
        .print-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        .certificate-seal {
            width: 120px;
            height: 120px;
            background-repeat: no-repeat;
            opacity: <?php echo $certificateSettings['seal_opacity'] ?? '0.7'; ?>;
            z-index: 10;
            margin-top: -60px;
            margin-bottom: -20px;
        }


    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <!-- Print Controls -->
    <div class="print-controls no-print">
        <button class="btn btn-primary" onclick="window.print()">
            <i class="fas fa-print me-2"></i>সার্টিফিকেট প্রিন্ট করুন
        </button>
        <a href="advanced_certificate.php" class="btn btn-secondary ms-2">
            <i class="fas fa-arrow-left me-2"></i>ফিরে যান
        </a>
    </div>

    <!-- Certificates -->
    <?php foreach ($students as $student): ?>
        <div class="certificate-container">
            <div class="certificate">
                <?php if (!empty($certificateSettings['watermark_path'])): ?>
                    <img src="<?php echo htmlspecialchars($certificateSettings['watermark_path']); ?>" alt="Watermark" class="watermark">
                <?php endif; ?>

                <div class="certificate-header">
                    <?php if (!empty($certificateSettings['logo_path'])): ?>
                        <img src="<?php echo htmlspecialchars($certificateSettings['logo_path']); ?>" alt="Logo" class="logo">
                    <?php endif; ?>
                    <div class="certificate-title-container">
                        <h1 class="certificate-title"><?php
                            $header_text = htmlspecialchars($certificateSettings['header_text']);
                            // Split the header text if it contains a comma or other separator
                            if (strpos($header_text, ',') !== false) {
                                $parts = explode(',', $header_text, 2);
                                echo trim($parts[0]);
                            } else {
                                // Try to split approximately in half for display
                                $words = explode(' ', $header_text);
                                $half = ceil(count($words) / 2);
                                $first_line = implode(' ', array_slice($words, 0, $half));
                                echo $first_line;
                            }
                        ?></h1>
                        <h2 class="certificate-subtitle"><?php
                            if (strpos($header_text, ',') !== false) {
                                $parts = explode(',', $header_text, 2);
                                echo trim($parts[1]);
                            } else {
                                // Second half of words
                                $words = explode(' ', $header_text);
                                $half = ceil(count($words) / 2);
                                $second_line = implode(' ', array_slice($words, $half));
                                echo $second_line;
                            }
                        ?></h2>
                    </div>
                </div>

                <div class="certificate-body">
                    <p class="certificate-intro">এই মর্মে প্রত্যয়ন করা যাচ্ছে যে,</p>
                    <h2 class="student-name"><?php echo htmlspecialchars($student['full_name']); ?></h2>

                    <div class="certificate-content">
                        <?php
                        // Build student information paragraph
                        $studentInfo = '';

                        // Add father's name if available
                        if (!empty($student['father_name'])) {
                            $studentInfo .= "পিতা " . htmlspecialchars($student['father_name']) . " ";
                        }

                        // Add mother's name if available
                        if (!empty($student['mother_name'])) {
                            $studentInfo .= (!empty($studentInfo) ? "ও " : "") . "মাতা " . htmlspecialchars($student['mother_name']) . " ";
                        }

                        // Add "এর সন্তান" if either parent is mentioned
                        if (!empty($studentInfo)) {
                            $studentInfo .= "এর সন্তান, ";
                        }

                        // Add student ID
                        $studentInfo .= "শিক্ষার্থী আইডি <span class='student-info'>" . htmlspecialchars($student['student_id']) . "</span>, ";

                        // Add roll number
                        $studentInfo .= "রোল নম্বর <span class='student-info'>" . htmlspecialchars($student['roll_number']) . "</span>, ";

                        // Add class
                        if (!empty($student['class_name'])) {
                            $studentInfo .= "<span class='student-info'>" . htmlspecialchars($student['class_name']) . "</span> শ্রেণীর ";
                        }

                        // Add department
                        if (!empty($student['department_name'])) {
                            $studentInfo .= "<span class='student-info'>" . htmlspecialchars($student['department_name']) . "</span> বিভাগের ";
                        }

                        // Add session
                        if (!empty($student['session_name'])) {
                            $studentInfo .= "<span class='student-info'>" . htmlspecialchars($student['session_name']) . "</span> সেশনের ";
                        }

                        $studentInfo .= "একজন নিয়মিত শিক্ষার্থী। ";

                        echo $studentInfo;
                        ?>

                        <?php echo htmlspecialchars($certificateSettings['footer_text']); ?>
                    </div>

                </div>

                <div class="certificate-footer">
                    <div class="certificate-date">
                        <p>তারিখ: <?php echo $currentDate; ?></p>
                    </div>

                    <div class="certificate-seal">
                        <?php
                        // Create the seal
                        $sealSvg = '<svg xmlns="http://www.w3.org/2000/svg" width="120" height="120" viewBox="0 0 120 120">';

                        // Add circles
                        $sealSvg .= '<defs><path id="circle-text-path-' . $student['id'] . '" d="M 60,60 m -40,0 a 40,40 0 1,1 80,0 a 40,40 0 1,1 -80,0" /></defs>';
                        $sealSvg .= '<circle cx="60" cy="60" r="55" fill="none" stroke="' . ($certificateSettings['seal_color'] ?? $certificateSettings['border_color']) . '" stroke-width="2" stroke-dasharray="5,5" />';
                        $sealSvg .= '<circle cx="60" cy="60" r="50" fill="none" stroke="' . ($certificateSettings['seal_color'] ?? $certificateSettings['border_color']) . '" stroke-width="1" />';
                        $sealSvg .= '<circle cx="60" cy="60" r="40" fill="none" stroke="' . ($certificateSettings['seal_color'] ?? $certificateSettings['border_color']) . '" stroke-width="0.5" />';

                        // Add institution name in circle
                        $sealSvg .= '<text><textPath href="#circle-text-path-' . $student['id'] . '" startOffset="50%" text-anchor="middle" fill="' . ($certificateSettings['seal_color'] ?? $certificateSettings['border_color']) . '" font-size="8" font-family="Arial">' . htmlspecialchars($certificateSettings['institution_name'] ?? 'College Management System') . '</textPath></text>';

                        // Add other text
                        $sealSvg .= '<text x="60" y="50" text-anchor="middle" fill="' . ($certificateSettings['seal_color'] ?? $certificateSettings['border_color']) . '" font-size="7" font-family="Arial">' . htmlspecialchars($certificateSettings['institution_address'] ?? 'Address, City, Country') . '</text>';
                        $sealSvg .= '<text x="60" y="62" text-anchor="middle" fill="' . ($certificateSettings['seal_color'] ?? $certificateSettings['border_color']) . '" font-size="10" font-weight="bold" font-family="Arial">OFFICIAL SEAL</text>';
                        $sealSvg .= '<text x="60" y="75" text-anchor="middle" fill="' . ($certificateSettings['seal_color'] ?? $certificateSettings['border_color']) . '" font-size="7" font-family="Arial">Established: ' . htmlspecialchars($certificateSettings['established_year'] ?? '2000') . '</text>';
                        $sealSvg .= '<text x="60" y="85" text-anchor="middle" fill="' . ($certificateSettings['seal_color'] ?? $certificateSettings['border_color']) . '" font-size="7" font-family="Arial">' . date("Y") . '</text>';

                        $sealSvg .= '</svg>';

                        echo $sealSvg;
                        ?>
                    </div>

                    <div class="signature-area">
                        <div class="signature-line"></div>
                        <p><?php echo htmlspecialchars($certificateSettings['signature_text']); ?></p>
                    </div>
                </div>


            </div>
        </div>
    <?php endforeach; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Auto print when page loads
        window.onload = function() {
            // Delay printing to ensure everything is loaded
            setTimeout(function() {
                window.print();
            }, 1000);
        };
    </script>
</body>
</html>
