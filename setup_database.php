<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Database Setup</h1>";

// Connection parameters
$servername = "127.0.0.1";
$username = "root";
$password = "";
$dbname = "zfaw";

// Step 1: Connect to MySQL server (without database)
echo "<h2>Step 1: Connecting to MySQL Server</h2>";
try {
    $conn = new mysqli($servername, $username, $password);
    if ($conn->connect_error) {
        die("<p style='color:red'>Connection to MySQL server failed: " . $conn->connect_error . "</p>");
    }
    echo "<p style='color:green'>Connection to MySQL server successful!</p>";
} catch (Exception $e) {
    die("<p style='color:red'>Exception: " . $e->getMessage() . "</p>");
}

// Step 2: Create database if it doesn't exist
echo "<h2>Step 2: Creating Database</h2>";
$sql = "CREATE DATABASE IF NOT EXISTS $dbname";
if ($conn->query($sql) === TRUE) {
    echo "<p style='color:green'>Database '$dbname' created successfully or already exists!</p>";
} else {
    die("<p style='color:red'>Error creating database: " . $conn->error . "</p>");
}

// Step 3: Select the database
echo "<h2>Step 3: Selecting Database</h2>";
$conn->select_db($dbname);
echo "<p style='color:green'>Database '$dbname' selected!</p>";

// Step 4: Create tables if they don't exist
echo "<h2>Step 4: Creating Tables</h2>";

// Users table
$usersTableQuery = "CREATE TABLE IF NOT EXISTS users (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    user_type ENUM('admin', 'teacher', 'student', 'staff') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($usersTableQuery)) {
    echo "<p style='color:green'>users table created or already exists!</p>";
} else {
    echo "<p style='color:red'>Error creating users table: " . $conn->error . "</p>";
}

// Departments table
$departmentsTableQuery = "CREATE TABLE IF NOT EXISTS departments (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    department_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($departmentsTableQuery)) {
    echo "<p style='color:green'>departments table created or already exists!</p>";
} else {
    echo "<p style='color:red'>Error creating departments table: " . $conn->error . "</p>";
}

// Students table
$studentsTableQuery = "CREATE TABLE IF NOT EXISTS students (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id VARCHAR(20) NOT NULL UNIQUE,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) NULL,
    phone VARCHAR(20) NULL,
    gender ENUM('male', 'female', 'other') NOT NULL,
    dob DATE NULL,
    address TEXT NULL,
    department_id INT(11) NULL,
    user_id INT(11) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($studentsTableQuery)) {
    echo "<p style='color:green'>students table created or already exists!</p>";
} else {
    echo "<p style='color:red'>Error creating students table: " . $conn->error . "</p>";
}

// Step 5: Insert admin user if not exists
echo "<h2>Step 5: Creating Admin User</h2>";
$checkAdminQuery = "SELECT * FROM users WHERE username = 'admin' AND user_type = 'admin'";
$result = $conn->query($checkAdminQuery);

if ($result->num_rows == 0) {
    // Admin doesn't exist, create one
    // Password is 'admin123' hashed with bcrypt
    $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $insertAdminQuery = "INSERT INTO users (username, password, user_type) 
                         VALUES ('admin', '$hashedPassword', 'admin')";
    
    if ($conn->query($insertAdminQuery) === TRUE) {
        echo "<p style='color:green'>Admin user created successfully!</p>";
        echo "<p>Username: admin</p>";
        echo "<p>Password: admin123</p>";
    } else {
        echo "<p style='color:red'>Error creating admin user: " . $conn->error . "</p>";
    }
} else {
    echo "<p style='color:green'>Admin user already exists!</p>";
}

// Close connection
$conn->close();

echo "<h2>Setup Complete!</h2>";
echo "<p>The database and essential tables have been set up.</p>";
echo "<p><a href='test_db_connection.php'>Test Database Connection</a></p>";
echo "<p><a href='index.php'>Go to Homepage</a></p>";
?>
