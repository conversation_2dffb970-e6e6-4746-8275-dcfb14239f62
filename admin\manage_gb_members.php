<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../login.php");
    exit();
}

// Include database connection
require_once '../includes/dbh.inc.php';

// Set current page for sidebar highlighting
$currentPage = 'manage_gb_members.php';

// Check if governing_board_members table exists, if not create it
$tableCheckQuery = "SHOW TABLES LIKE 'governing_board_members'";
$tableExists = $conn->query($tableCheckQuery)->num_rows > 0;

if (!$tableExists) {
    $createTableQuery = "CREATE TABLE governing_board_members (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        position VARCHAR(100) NOT NULL,
        address VARCHAR(255),
        phone VARCHAR(20),
        email VARCHAR(100),
        photo VARCHAR(255),
        bio TEXT,
        display_order INT(11) DEFAULT 0,
        is_active TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    if ($conn->query($createTableQuery)) {
        $success_message = "পরিচালনা বোর্ডের সদস্য টেবিল সফলভাবে তৈরি করা হয়েছে।";
    } else {
        $error_message = "টেবিল তৈরি করতে সমস্যা হয়েছে: " . $conn->error;
    }
}

// Process form submission for adding/editing members
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        // Add new member
        if ($_POST['action'] === 'add') {
            $name = $_POST['name'];
            $position = $_POST['position'];
            $address = $_POST['address'];
            $phone = $_POST['phone'];
            $email = $_POST['email'];
            $bio = $_POST['bio'];
            $display_order = $_POST['display_order'];
            $is_active = isset($_POST['is_active']) ? 1 : 0;

            // Handle photo upload
            $photo = '';
            if (isset($_FILES['photo']) && $_FILES['photo']['error'] === 0) {
                $upload_dir = '../uploads/gb_members/';

                // Create directory if it doesn't exist
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }

                $file_name = time() . '_' . basename($_FILES['photo']['name']);
                $target_file = $upload_dir . $file_name;

                // Check if image file is a actual image
                $check = getimagesize($_FILES['photo']['tmp_name']);
                if ($check !== false) {
                    // Upload file
                    if (move_uploaded_file($_FILES['photo']['tmp_name'], $target_file)) {
                        $photo = 'uploads/gb_members/' . $file_name;
                    }
                }
            }

            // Insert into database
            $sql = "INSERT INTO governing_board_members (name, position, address, phone, email, photo, bio, display_order, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("sssssssii", $name, $position, $address, $phone, $email, $photo, $bio, $display_order, $is_active);

            if ($stmt->execute()) {
                $success_message = "সদস্য সফলভাবে যোগ করা হয়েছে।";
            } else {
                $error_message = "সদস্য যোগ করতে সমস্যা হয়েছে: " . $stmt->error;
            }

            $stmt->close();
        }

        // Edit existing member
        elseif ($_POST['action'] === 'edit' && isset($_POST['id'])) {
            $id = $_POST['id'];
            $name = $_POST['name'];
            $position = $_POST['position'];
            $address = $_POST['address'];
            $phone = $_POST['phone'];
            $email = $_POST['email'];
            $bio = $_POST['bio'];
            $display_order = $_POST['display_order'];
            $is_active = isset($_POST['is_active']) ? 1 : 0;

            // Get current photo
            $sql = "SELECT photo FROM governing_board_members WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $id);
            $stmt->execute();
            $result = $stmt->get_result();
            $row = $result->fetch_assoc();
            $current_photo = $row['photo'];
            $stmt->close();

            // Handle photo upload
            $photo = $current_photo;
            if (isset($_FILES['photo']) && $_FILES['photo']['error'] === 0) {
                $upload_dir = '../uploads/gb_members/';

                // Create directory if it doesn't exist
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }

                $file_name = time() . '_' . basename($_FILES['photo']['name']);
                $target_file = $upload_dir . $file_name;

                // Check if image file is a actual image
                $check = getimagesize($_FILES['photo']['tmp_name']);
                if ($check !== false) {
                    // Upload file
                    if (move_uploaded_file($_FILES['photo']['tmp_name'], $target_file)) {
                        $photo = 'uploads/gb_members/' . $file_name;

                        // Delete old photo if exists
                        if (!empty($current_photo) && file_exists('../' . $current_photo)) {
                            unlink('../' . $current_photo);
                        }
                    }
                }
            }

            // Update database
            $sql = "UPDATE governing_board_members
                    SET name = ?, position = ?, address = ?, phone = ?, email = ?, photo = ?, bio = ?, display_order = ?, is_active = ?
                    WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("sssssssiis", $name, $position, $address, $phone, $email, $photo, $bio, $display_order, $is_active, $id);

            if ($stmt->execute()) {
                $success_message = "সদস্য সফলভাবে আপডেট করা হয়েছে।";
            } else {
                $error_message = "সদস্য আপডেট করতে সমস্যা হয়েছে: " . $stmt->error;
            }

            $stmt->close();
        }

        // Delete member
        elseif ($_POST['action'] === 'delete' && isset($_POST['id'])) {
            $id = $_POST['id'];

            // Get photo path before deleting
            $sql = "SELECT photo FROM governing_board_members WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $id);
            $stmt->execute();
            $result = $stmt->get_result();
            $row = $result->fetch_assoc();
            $photo = $row['photo'];
            $stmt->close();

            // Delete from database
            $sql = "DELETE FROM governing_board_members WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $id);

            if ($stmt->execute()) {
                // Delete photo file if exists
                if (!empty($photo) && file_exists('../' . $photo)) {
                    unlink('../' . $photo);
                }

                $success_message = "সদস্য সফলভাবে মুছে ফেলা হয়েছে।";
            } else {
                $error_message = "সদস্য মুছতে সমস্যা হয়েছে: " . $stmt->error;
            }

            $stmt->close();
        }
    }
}

// Get member data for editing
$edit_id = isset($_GET['edit']) ? $_GET['edit'] : null;
$edit_data = null;

if ($edit_id && $tableExists) {
    $sql = "SELECT * FROM governing_board_members WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $edit_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $edit_data = $result->fetch_assoc();
    }

    $stmt->close();
}

// Get all members for display
$result = null;
if ($tableExists) {
    $sql = "SELECT * FROM governing_board_members ORDER BY display_order ASC, name ASC";
    $result = $conn->query($sql);
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>পরিচালনা বোর্ডের সদস্য ব্যবস্থাপনা</title>

    <!-- Bootstrap CSS -->


    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/admin.css">
    <link rel="stylesheet" href="../css/admin-sidebar.css">

    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
        }

        .member-photo {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 50%;
        }

        .form-container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
        }

        .section-title {
            border-left: 4px solid #00a65a;
            padding-left: 10px;
            margin-bottom: 20px;
            color: #00a65a;
        }

        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        .table th {
            background-color: #00a65a;
            color: white;
        }

        /* Sidebar Styles */
        .sidebar {
            background-color: #343a40;
            color: #fff;
            overflow-y: auto;
            height: 100vh;
        }

        .sidebar h3 {
            color: #ffffff;
            padding: 10px 15px;
            margin-bottom: 20px;
            font-size: 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.75);
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover {
            color: #ffffff;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar .nav-link.active {
            color: #ffffff;
            background-color: #007bff;
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Include sidebar -->
            <?php include '../includes/admin_sidebar.php'; ?>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">পরিচালনা বোর্ডের সদস্য ব্যবস্থাপনা</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="../gb_members.php" target="_blank" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-eye"></i> পাবলিক পেজ দেখুন
                        </a>
                    </div>
                </div>

                <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <!-- Add/Edit Form -->
                <div class="form-container">
                    <h3 class="section-title"><?php echo $edit_data ? 'সদস্য আপডেট করুন' : 'নতুন সদস্য যোগ করুন'; ?></h3>

                    <form action="" method="post" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="<?php echo $edit_data ? 'edit' : 'add'; ?>">
                        <?php if ($edit_data): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_data['id']; ?>">
                        <?php endif; ?>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">নাম <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" value="<?php echo $edit_data ? htmlspecialchars($edit_data['name']) : ''; ?>" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="position" class="form-label">পদবী <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="position" name="position" value="<?php echo $edit_data ? htmlspecialchars($edit_data['position']) : ''; ?>" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="address" class="form-label">ঠিকানা</label>
                                <input type="text" class="form-control" id="address" name="address" value="<?php echo $edit_data ? htmlspecialchars($edit_data['address']) : ''; ?>">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">ফোন নম্বর</label>
                                <input type="text" class="form-control" id="phone" name="phone" value="<?php echo $edit_data ? htmlspecialchars($edit_data['phone']) : ''; ?>">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">ইমেইল</label>
                                <input type="email" class="form-control" id="email" name="email" value="<?php echo $edit_data ? htmlspecialchars($edit_data['email']) : ''; ?>">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="display_order" class="form-label">প্রদর্শন ক্রম</label>
                                <input type="number" class="form-control" id="display_order" name="display_order" value="<?php echo $edit_data ? htmlspecialchars($edit_data['display_order']) : '0'; ?>">
                                <small class="text-muted">ছোট সংখ্যা আগে প্রদর্শিত হবে</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="photo" class="form-label">ছবি <?php echo $edit_data ? '(পরিবর্তন করতে চাইলে)' : ''; ?></label>
                                <input type="file" class="form-control" id="photo" name="photo" accept="image/*">
                                <?php if ($edit_data && !empty($edit_data['photo'])): ?>
                                <div class="mt-2">
                                    <img src="../<?php echo htmlspecialchars($edit_data['photo']); ?>" alt="Current Photo" class="member-photo">
                                    <small class="ms-2">বর্তমান ছবি</small>
                                </div>
                                <?php endif; ?>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" <?php echo (!$edit_data || $edit_data['is_active']) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_active">
                                        সক্রিয় (পাবলিক পেজে প্রদর্শিত হবে)
                                    </label>
                                </div>
                            </div>

                            <div class="col-12 mb-3">
                                <label for="bio" class="form-label">জীবনী</label>
                                <textarea class="form-control" id="bio" name="bio" rows="3"><?php echo $edit_data ? htmlspecialchars($edit_data['bio']) : ''; ?></textarea>
                            </div>

                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas <?php echo $edit_data ? 'fa-save' : 'fa-plus-circle'; ?> me-1"></i>
                                    <?php echo $edit_data ? 'আপডেট করুন' : 'যোগ করুন'; ?>
                                </button>

                                <?php if ($edit_data): ?>
                                <a href="manage_gb_members.php" class="btn btn-secondary ms-2">
                                    <i class="fas fa-times me-1"></i> বাতিল করুন
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Members List -->
                <h3 class="section-title">সদস্যদের তালিকা</h3>

                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ছবি</th>
                                <th>নাম</th>
                                <th>পদবী</th>
                                <th>যোগাযোগ</th>
                                <th>প্রদর্শন ক্রম</th>
                                <th>স্ট্যাটাস</th>
                                <th>অ্যাকশন</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            if ($result && $result->num_rows > 0) {
                                while ($row = $result->fetch_assoc()) {
                                    ?>
                                    <tr>
                                        <td>
                                            <img src="../<?php echo !empty($row['photo']) ? $row['photo'] : 'img/default-user.png'; ?>"
                                                 alt="<?php echo htmlspecialchars($row['name']); ?>"
                                                 class="member-photo"
                                                 onerror="this.src='../img/default-user.png'">
                                        </td>
                                        <td><?php echo htmlspecialchars($row['name']); ?></td>
                                        <td><?php echo htmlspecialchars($row['position']); ?></td>
                                        <td>
                                            <?php if (!empty($row['phone'])): ?>
                                            <div><i class="fas fa-phone me-1"></i> <?php echo htmlspecialchars($row['phone']); ?></div>
                                            <?php endif; ?>

                                            <?php if (!empty($row['email'])): ?>
                                            <div><i class="fas fa-envelope me-1"></i> <?php echo htmlspecialchars($row['email']); ?></div>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo $row['display_order']; ?></td>
                                        <td>
                                            <span class="badge <?php echo $row['is_active'] ? 'bg-success' : 'bg-secondary'; ?>">
                                                <?php echo $row['is_active'] ? 'সক্রিয়' : 'নিষ্ক্রিয়'; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <a href="manage_gb_members.php?edit=<?php echo $row['id']; ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>

                                            <button type="button" class="btn btn-sm btn-danger"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#deleteModal<?php echo $row['id']; ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>

                                            <!-- Delete Confirmation Modal -->
                                            <div class="modal fade" id="deleteModal<?php echo $row['id']; ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?php echo $row['id']; ?>" aria-hidden="true">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="deleteModalLabel<?php echo $row['id']; ?>">নিশ্চিতকরণ</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            আপনি কি নিশ্চিত যে আপনি <strong><?php echo htmlspecialchars($row['name']); ?></strong> কে মুছে ফেলতে চান?
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল করুন</button>
                                                            <form action="" method="post">
                                                                <input type="hidden" name="action" value="delete">
                                                                <input type="hidden" name="id" value="<?php echo $row['id']; ?>">
                                                                <button type="submit" class="btn btn-danger">মুছে ফেলুন</button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php
                                }
                            } else {
                                ?>
                                <tr>
                                    <td colspan="7" class="text-center">কোন সদস্য পাওয়া যায়নি</td>
                                </tr>
                                <?php
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
