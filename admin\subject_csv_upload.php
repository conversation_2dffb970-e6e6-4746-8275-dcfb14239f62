<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$errorMessage = '';
$successMessage = '';

// Handle CSV template download for student subject selection
if (isset($_GET['download_template'])) {
    // Get filter parameters
    $session_id = isset($_GET['session_id']) ? intval($_GET['session_id']) : 0;
    $class_id = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;
    $department_id = isset($_GET['department_id']) ? intval($_GET['department_id']) : 0;

    $filename = 'student_subject_selection_template_' . date('Y-m-d') . '.csv';

    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');

    $output = fopen('php://output', 'w');

    // Add UTF-8 BOM for proper Excel support
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

    // Headers for student subject selection
    $headers = ['student_id', 'student_name', 'roll_number', 'session', 'class', 'department', 'subject1_id', 'subject2_id', 'subject3_id', 'subject4_id', 'subject5_id', 'subject6_id', 'subject7_id', 'subject8_id'];

    // Write headers
    fputcsv($output, $headers);

    // Build query based on filters
    $where_conditions = [];
    $params = [];
    $param_types = '';

    if ($session_id > 0) {
        $where_conditions[] = "s.session_id = ?";
        $params[] = $session_id;
        $param_types .= 'i';
    }

    if ($class_id > 0) {
        $where_conditions[] = "s.class_id = ?";
        $params[] = $class_id;
        $param_types .= 'i';
    }

    if ($department_id > 0) {
        $where_conditions[] = "s.department_id = ?";
        $params[] = $department_id;
        $param_types .= 'i';
    }

    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

    // Get students data
    $students_query = "SELECT s.student_id,
                              CONCAT(s.first_name, ' ', s.last_name) as student_name,
                              s.roll_number,
                              sess.session_name,
                              c.class_name,
                              d.department_name
                       FROM students s
                       LEFT JOIN sessions sess ON s.session_id = sess.id
                       LEFT JOIN classes c ON s.class_id = c.id
                       LEFT JOIN departments d ON s.department_id = d.id
                       $where_clause
                       ORDER BY s.student_id
                       LIMIT 100";

    if (!empty($params)) {
        $stmt = $conn->prepare($students_query);
        $stmt->bind_param($param_types, ...$params);
        $stmt->execute();
        $students_result = $stmt->get_result();
    } else {
        $students_result = $conn->query($students_query);
    }

    if ($students_result && $students_result->num_rows > 0) {
        // Write student data with empty subject columns
        while ($row = $students_result->fetch_assoc()) {
            $csv_row = [
                $row['student_id'],
                $row['student_name'],
                $row['roll_number'] ?? '',
                $row['session_name'] ?? '',
                $row['class_name'] ?? '',
                $row['department_name'] ?? '',
                '', // subject1_id - empty for user to fill
                '', // subject2_id - empty for user to fill
                '', // subject3_id - empty for user to fill
                '', // subject4_id - empty for user to fill
                '', // subject5_id - empty for user to fill
                '', // subject6_id - empty for user to fill
                '', // subject7_id - empty for user to fill
                ''  // subject8_id - empty for user to fill
            ];
            fputcsv($output, $csv_row);
        }
    } else {
        // No students found - add sample row
        $sample_row = [
            'STD-001', 'নমুনা শিক্ষার্থী', '001', '2024', 'একাদশ', 'বিজ্ঞান', '', '', '', '', '', '', '', ''
        ];
        fputcsv($output, $sample_row);
    }

    fclose($output);
    exit();
}

// Handle CSV upload processing for student subject selection
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_file'])) {
    if ($_FILES['csv_file']['error'] === UPLOAD_ERR_OK) {
        $file_extension = strtolower(pathinfo($_FILES['csv_file']['name'], PATHINFO_EXTENSION));

        if ($file_extension !== 'csv') {
            $errorMessage = 'অবৈধ ফাইল টাইপ। শুধুমাত্র CSV ফাইল অনুমোদিত।';
        } else {
            $csv_file = $_FILES['csv_file']['tmp_name'];

            // Read file content and handle UTF-8 BOM
            $content = file_get_contents($csv_file);
            if (substr($content, 0, 3) === "\xEF\xBB\xBF") {
                $content = substr($content, 3);
            }

            // Create temporary file with cleaned content
            $temp_file = tempnam(sys_get_temp_dir(), 'csv_upload');
            file_put_contents($temp_file, $content);

            $handle = fopen($temp_file, 'r');

            if ($handle !== FALSE) {
                $headers = fgetcsv($handle); // Skip headers

                $success_count = 0;
                $error_count = 0;
                $errors = [];

                $conn->begin_transaction();

                try {
                    $row_number = 1;

                    while (($data = fgetcsv($handle)) !== FALSE) {
                        $row_number++;

                        // Skip empty rows
                        if (empty(array_filter($data))) {
                            continue;
                        }

                        $student_id = !empty(trim($data[0])) ? trim($data[0]) : '';

                        if (empty($student_id)) {
                            $error_count++;
                            $errors[] = "সারি $row_number: শিক্ষার্থী ID অনুপস্থিত";
                            continue;
                        }

                        // Check if student exists
                        $student_check = $conn->prepare("SELECT id FROM students WHERE student_id = ?");
                        $student_check->bind_param("s", $student_id);
                        $student_check->execute();
                        $student_result = $student_check->get_result();

                        if ($student_result->num_rows === 0) {
                            $error_count++;
                            $errors[] = "সারি $row_number: শিক্ষার্থী '$student_id' পাওয়া যায়নি";
                            continue;
                        }

                        $student_data = $student_result->fetch_assoc();
                        $internal_student_id = $student_data['id'];

                        // Extract subject IDs from columns 6-13 (subject1_id to subject8_id)
                        $subject_ids = [];
                        for ($i = 6; $i < count($data) && $i < 14; $i++) {
                            if (!empty(trim($data[$i])) && is_numeric(trim($data[$i]))) {
                                $subject_ids[] = intval($data[$i]);
                            }
                        }

                        if (empty($subject_ids)) {
                            $error_count++;
                            $errors[] = "সারি $row_number: কোন বিষয় ID পাওয়া যায়নি";
                            continue;
                        }

                        // Validate all subjects exist
                        $invalid_subjects = [];
                        foreach ($subject_ids as $subject_id) {
                            $subject_check = $conn->prepare("SELECT id FROM subjects WHERE id = ? AND is_active = 1");
                            $subject_check->bind_param("i", $subject_id);
                            $subject_check->execute();
                            if ($subject_check->get_result()->num_rows === 0) {
                                $invalid_subjects[] = $subject_id;
                            }
                        }

                        if (!empty($invalid_subjects)) {
                            $error_count++;
                            $errors[] = "সারি $row_number: অবৈধ বিষয় ID: " . implode(', ', $invalid_subjects);
                            continue;
                        }

                        // Delete existing subjects for this student
                        $delete_existing = $conn->prepare("DELETE FROM student_subjects WHERE student_id = ?");
                        $delete_existing->bind_param("i", $internal_student_id);
                        $delete_existing->execute();

                        // Get student's department for proper category detection
                        $student_dept_query = "SELECT department_id FROM students WHERE id = ?";
                        $student_dept_stmt = $conn->prepare($student_dept_query);
                        $student_dept_stmt->bind_param("i", $internal_student_id);
                        $student_dept_stmt->execute();
                        $student_dept_result = $student_dept_stmt->get_result();
                        $student_department_id = null;

                        if ($student_dept_result->num_rows > 0) {
                            $student_dept_data = $student_dept_result->fetch_assoc();
                            $student_department_id = $student_dept_data['department_id'];
                        }

                        // Insert new subjects with proper category detection
                        $row_success = 0;
                        foreach ($subject_ids as $subject_id) {
                            $category = 'optional'; // default

                            // First, try to get category from department_subject_types table
                            if ($student_department_id) {
                                $dept_category_query = "SELECT subject_type FROM department_subject_types WHERE department_id = ? AND subject_id = ?";
                                $dept_category_stmt = $conn->prepare($dept_category_query);
                                $dept_category_stmt->bind_param("ii", $student_department_id, $subject_id);
                                $dept_category_stmt->execute();
                                $dept_category_result = $dept_category_stmt->get_result();

                                if ($dept_category_result->num_rows > 0) {
                                    $dept_category_data = $dept_category_result->fetch_assoc();
                                    $category = $dept_category_data['subject_type'];
                                }
                            }

                            // If not found in department_subject_types, fall back to subjects table
                            if ($category === 'optional') {
                                $subject_category_query = "SELECT category FROM subjects WHERE id = ?";
                                $subject_category_stmt = $conn->prepare($subject_category_query);
                                $subject_category_stmt->bind_param("i", $subject_id);
                                $subject_category_stmt->execute();
                                $subject_category_result = $subject_category_stmt->get_result();

                                if ($subject_category_result->num_rows > 0) {
                                    $subject_category_data = $subject_category_result->fetch_assoc();
                                    $category = $subject_category_data['category'] ?? 'optional';
                                }
                            }

                            $insert_query = "INSERT INTO student_subjects (student_id, subject_id, category) VALUES (?, ?, ?)";
                            $stmt = $conn->prepare($insert_query);
                            $stmt->bind_param("iis", $internal_student_id, $subject_id, $category);

                            if ($stmt->execute()) {
                                $row_success++;
                            }
                        }

                        if ($row_success > 0) {
                            $success_count += $row_success;
                        }
                    }

                    $conn->commit();

                    if ($success_count > 0) {
                        $successMessage = "$success_count টি বিষয় নির্বাচন সফলভাবে আপডেট করা হয়েছে।";
                    }

                    if ($error_count > 0) {
                        $errorMessage = "$error_count টি ত্রুটি হয়েছে। " . implode('<br>', array_slice($errors, 0, 10));
                        if (count($errors) > 10) {
                            $errorMessage .= "<br>... এবং আরও " . (count($errors) - 10) . " টি ত্রুটি";
                        }
                    }

                } catch (Exception $e) {
                    $conn->rollback();
                    $errorMessage = 'CSV আপলোড করতে সমস্যা হয়েছে: ' . $e->getMessage();
                }

                fclose($handle);
                unlink($temp_file);
            } else {
                $errorMessage = 'CSV ফাইল পড়তে সমস্যা হয়েছে।';
            }
        }
    } else {
        $errorMessage = 'ফাইল আপলোড করতে সমস্যা হয়েছে।';
    }
}

// Get available sessions, classes, departments for filters
$sessions_query = "SELECT id, session_name FROM sessions ORDER BY session_name DESC";
$sessions_result = $conn->query($sessions_query);

$classes_query = "SELECT id, class_name FROM classes ORDER BY class_name";
$classes_result = $conn->query($classes_query);

$departments_query = "SELECT id, department_name FROM departments ORDER BY department_name";
$departments_result = $conn->query($departments_query);

// Get available subjects for reference
$subjects_query = "SELECT id, subject_name, subject_code FROM subjects WHERE is_active = 1 ORDER BY subject_name";
$subjects_result = $conn->query($subjects_query);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>শিক্ষার্থী বিষয় নির্বাচন CSV আপলোড - কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/hind-siliguri.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include('includes/sidebar.php'); ?>

            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">📚 শিক্ষার্থী বিষয় নির্বাচন CSV আপলোড</h1>
                    <div>
                        <a href="student_subject_selection.php" class="btn btn-outline-primary">
                            <i class="fas fa-list me-1"></i> বিষয় নির্বাচন তালিকা
                        </a>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>নির্দেশনা:</strong> শিক্ষার্থীদের বিষয় নির্বাচনের জন্য CSV ফাইল আপলোড করুন।
                    <br><small><strong>ফরম্যাট:</strong> student_id, student_name, roll_number, session, class, department, subject1_id, subject2_id, ...</small>
                    <br><small><strong>বিষয় ID:</strong> শুধুমাত্র বিষয়ের ID নম্বর দিন (যেমন: 1, 2, 3)</small>
                    <br><small><strong>নোট:</strong> টেমপ্লেট ডাউনলোড করলে শিক্ষার্থীদের তথ্য ও ফাঁকা বিষয় কলাম পাবেন</small>
                </div>
                
                <!-- Messages -->
                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>ত্রুটি!</strong> <?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>সফল!</strong> <?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Template Download Filters -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-filter me-2"></i>টেমপ্লেট ডাউনলোড (ফিল্টার)</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" id="filterForm">
                            <input type="hidden" name="download_template" value="1">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="session_id" class="form-label">সেশন</label>
                                        <select class="form-select" id="session_id" name="session_id">
                                            <option value="">সব সেশন</option>
                                            <?php if ($sessions_result && $sessions_result->num_rows > 0): ?>
                                                <?php while ($session = $sessions_result->fetch_assoc()): ?>
                                                    <option value="<?php echo $session['id']; ?>"><?php echo htmlspecialchars($session['session_name']); ?></option>
                                                <?php endwhile; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="class_id" class="form-label">ক্লাস</label>
                                        <select class="form-select" id="class_id" name="class_id">
                                            <option value="">সব ক্লাস</option>
                                            <?php if ($classes_result && $classes_result->num_rows > 0): ?>
                                                <?php while ($class = $classes_result->fetch_assoc()): ?>
                                                    <option value="<?php echo $class['id']; ?>"><?php echo htmlspecialchars($class['class_name']); ?></option>
                                                <?php endwhile; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="department_id" class="form-label">বিভাগ</label>
                                        <select class="form-select" id="department_id" name="department_id">
                                            <option value="">সব বিভাগ</option>
                                            <?php if ($departments_result && $departments_result->num_rows > 0): ?>
                                                <?php while ($department = $departments_result->fetch_assoc()): ?>
                                                    <option value="<?php echo $department['id']; ?>"><?php echo htmlspecialchars($department['department_name']); ?></option>
                                                <?php endwhile; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-download me-2"></i>টেমপ্লেট ডাউনলোড
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Upload Form -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-upload me-2"></i>CSV ফাইল আপলোড</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="csv_file" class="form-label">CSV ফাইল নির্বাচন করুন</label>
                                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                                        <div class="form-text">ফরম্যাট: student_id, student_name, roll_number, session, class, department, subject1_id, subject2_id, ...</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-upload me-2"></i>আপলোড করুন
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Subject Reference -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6><i class="fas fa-book me-2"></i>বিষয় তালিকা (রেফারেন্স)</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive" style="max-height: 400px;">
                            <table class="table table-sm table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>বিষয় ID</th>
                                        <th>বিষয়ের নাম</th>
                                        <th>বিষয় কোড</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($subjects_result && $subjects_result->num_rows > 0): ?>
                                        <?php while ($subject = $subjects_result->fetch_assoc()): ?>
                                        <tr>
                                            <td><strong><?php echo $subject['id']; ?></strong></td>
                                            <td><?php echo htmlspecialchars($subject['subject_name']); ?></td>
                                            <td><?php echo htmlspecialchars($subject['subject_code']); ?></td>
                                        </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="3" class="text-center text-muted">কোন বিষয় পাওয়া যায়নি</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
