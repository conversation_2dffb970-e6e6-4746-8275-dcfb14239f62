<?php
session_start();

// Modified session check - allows access without redirecting
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    $isAdmin = false;
} else {
    $isAdmin = true;
}

// Set current page for sidebar active menu
$currentPage = 'notices.php';

require_once "../includes/dbh.inc.php";

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: notices.php");
    exit();
}

$noticeId = intval($_GET['id']);

// Get notice details
$sql = "SELECT * FROM notices WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $noticeId);
$stmt->execute();
$result = $stmt->get_result();

// Check if notice exists
if ($result->num_rows === 0) {
    header("Location: notices.php");
    exit();
}

$notice = $result->fetch_assoc();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>নোটিশ দেখুন - <?php echo htmlspecialchars($notice["title"]); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/hind-siliguri.css">
    <style>
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .notice-content {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-top: 15px;
            white-space: pre-line;
        }
        .attachment-preview {
            max-width: 100%;
            max-height: 500px;
            margin-top: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .pdf-preview {
            display: inline-block;
            padding: 10px 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #ddd;
            margin-top: 15px;
        }
        .pdf-container {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            margin: 15px 0;
            border-radius: 5px;
        }
        .notice-meta {
            color: #6c757d;
            font-size: 0.9rem;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            background: linear-gradient(to right, #1a2980, #26d0ce);
            color: white;
        }

        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: 1rem;
            overflow-x: hidden;
            overflow-y: auto;
        }

        .sidebar .nav-link {
            font-weight: 500;
            color: rgba(255, 255, 255, 0.75);
            padding: 0.5rem 1rem;
            margin: 0.2rem 0;
            border-radius: 0.25rem;
        }

        .sidebar .nav-link:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar .nav-link.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.2);
        }

        .sidebar .nav-link i {
            margin-right: 0.5rem;
        }

        .sidebar-heading {
            font-size: 0.75rem;
            text-transform: uppercase;
            color: rgba(255, 255, 255, 0.5);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">নোটিশ দেখুন</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="notices.php" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left me-1"></i> সকল নোটিশে ফিরে যান
                        </a>
                        <a href="notices.php" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-plus me-1"></i> নতুন নোটিশ যোগ করুন
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><?php echo htmlspecialchars($notice["title"]); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="notice-meta mb-3">
                            <div><i class="fas fa-calendar-alt me-2"></i> <strong>তারিখ:</strong> <?php echo $notice["date"]; ?></div>
                            <?php if (isset($notice["added_by"])): ?>
                            <div><i class="fas fa-user me-2"></i> <strong>যোগ করেছেন:</strong> <?php echo $notice["added_by"] ?? 'অজানা'; ?></div>
                            <?php endif; ?>
                            <?php if (isset($notice["created_at"])): ?>
                            <div><i class="fas fa-clock me-2"></i> <strong>যোগ করার সময়:</strong> <?php echo $notice["created_at"]; ?></div>
                            <?php endif; ?>
                        </div>

                        <h6 class="mt-4 mb-2">বিবরণ:</h6>
                        <div class="notice-content">
                            <?php echo nl2br(htmlspecialchars($notice["content"])); ?>
                        </div>

                        <?php if (!empty($notice["attachment_path"])): ?>
                            <h6 class="mt-4 mb-2">সংযুক্ত ফাইল:</h6>
                            <?php
                            $ext = strtolower(pathinfo($notice["attachment_path"], PATHINFO_EXTENSION));
                            if (in_array($ext, ['jpg', 'jpeg', 'png', 'gif'])):
                            ?>
                                <img src="../<?php echo $notice["attachment_path"]; ?>" class="attachment-preview" alt="Attached image">
                            <?php elseif ($ext == 'pdf'): ?>
                                <div class="pdf-preview mb-2">
                                    <i class="fas fa-file-pdf fa-2x text-danger"></i>
                                    <span class="ms-2">PDF ফাইল</span>
                                </div>

                                <!-- Inline PDF Viewer -->
                                <div class="pdf-container">
                                    <object data="../<?php echo $notice["attachment_path"]; ?>" type="application/pdf" width="100%" height="100%">
                                        <p>আপনার ব্রাউজারে PDF দেখা যাচ্ছে না।
                                        <a href="../<?php echo $notice["attachment_path"]; ?>" target="_blank">PDF ডাউনলোড করুন</a>.</p>
                                    </object>
                                </div>

                                <div class="mt-2 d-flex gap-2">
                                    <a href="../<?php echo $notice["attachment_path"]; ?>" class="btn btn-outline-primary" target="_blank">
                                        <i class="fas fa-external-link-alt me-1"></i> নতুন ট্যাবে খুলুন
                                    </a>
                                    <a href="../<?php echo $notice["attachment_path"]; ?>" class="btn btn-outline-success" download>
                                        <i class="fas fa-download me-1"></i> ডাউনলোড করুন
                                    </a>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>

                        <?php if ($isAdmin): ?>
                        <div class="mt-4 d-flex gap-2">
                            <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#editModal">
                                <i class="fas fa-edit me-1"></i> সম্পাদনা করুন
                            </button>
                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                <i class="fas fa-trash me-1"></i> মুছুন
                            </button>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php if ($isAdmin): ?>
    <!-- Edit Modal -->
    <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalLabel">নোটিশ সম্পাদনা করুন</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form method="post" action="notices.php" enctype="multipart/form-data">
                        <input type="hidden" name="notice_id" value="<?php echo $notice["id"]; ?>">
                        <div class="mb-3">
                            <label for="edit_title" class="form-label">শিরোনাম</label>
                            <input type="text" class="form-control" id="edit_title" name="edit_title" value="<?php echo htmlspecialchars($notice["title"]); ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_content" class="form-label">বিবরণ</label>
                            <textarea class="form-control" id="edit_content" name="edit_content" rows="6" required><?php echo htmlspecialchars($notice["content"]); ?></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="edit_date" class="form-label">তারিখ</label>
                            <input type="date" class="form-control" id="edit_date" name="edit_date" value="<?php echo $notice["date"]; ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="edit_attachment" class="form-label">PDF বা ছবি পরিবর্তন করুন (ঐচ্ছিক)</label>
                            <input type="file" class="form-control" id="edit_attachment" name="edit_attachment">
                            <div class="form-text">অনুমোদিত ফাইল: PDF, JPG, PNG, GIF (সর্বোচ্চ 5MB)</div>

                            <?php if (!empty($notice["attachment_path"])): ?>
                                <div class="mt-2">
                                    <span class="badge bg-info">বর্তমান ফাইল:</span>
                                    <a href="../<?php echo $notice["attachment_path"]; ?>" target="_blank">
                                        <?php
                                        $filename = basename($notice["attachment_path"]);
                                        echo $filename;
                                        ?>
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>

                        <button type="submit" name="update_notice" class="btn btn-primary">আপডেট করুন</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">নোটিশ মুছুন</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>আপনি কি নিশ্চিত যে আপনি এই নোটিশটি মুছতে চান?</p>
                    <p><strong>শিরোনাম:</strong> <?php echo htmlspecialchars($notice["title"]); ?></p>
                    <?php if (!empty($notice["attachment_path"])): ?>
                        <p class="text-danger"><strong>সতর্কতা:</strong> এর সাথে সংযুক্ত ফাইলও মুছে যাবে।</p>
                    <?php endif; ?>
                </div>
                <div class="modal-footer">
                    <form method="post" action="notices.php">
                        <input type="hidden" name="delete_id" value="<?php echo $notice["id"]; ?>">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল করুন</button>
                        <button type="submit" name="delete_notice" class="btn btn-danger">মুছুন</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
