<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Include heading functions if not already included
if (!function_exists('has_institution_logo')) {
    require_once '../includes/heading_functions.php';
}

// Get parameters from URL
$examId = isset($_GET['exam_id']) ? intval($_GET['exam_id']) : 0;
$classId = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;
$departmentId = isset($_GET['department_id']) ? intval($_GET['department_id']) : 0;
$sessionId = isset($_GET['session_id']) ? intval($_GET['session_id']) : 0;
$subjectId = isset($_GET['subject_id']) ? intval($_GET['subject_id']) : 0;
$searchTerm = isset($_GET['search']) ? trim($_GET['search']) : '';
$sortBy = isset($_GET['sort_by']) ? $_GET['sort_by'] : 'roll_number';
$perPage = isset($_GET['per_page']) ? $_GET['per_page'] : '25';
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;

// Check if institution logo exists
$hasLogo = false;
$logoUrl = '';
$logoPath = '../uploads/signatures/institution_logo.png';
if (file_exists($logoPath)) {
    $hasLogo = true;
    $logoUrl = $logoPath . '?v=' . time(); // Add timestamp to prevent caching
}

// If no logo exists, create a default logo path
$defaultLogoPath = '../assets/images/default_logo.png';
if (!$hasLogo && !file_exists($defaultLogoPath)) {
    // Create directory if it doesn't exist
    if (!file_exists('../assets/images')) {
        mkdir('../assets/images', 0777, true);
    }

    // Use a placeholder image
    $defaultLogoUrl = 'https://via.placeholder.com/80x80?text=ZFAW';
    file_put_contents($defaultLogoPath, file_get_contents($defaultLogoUrl));
}

// Initialize variables
$selectedExam = null;
$students = null;
$selectedSubject = null;
$marksDistribution = null;
$success_message = '';
$error_message = '';

// Get all classes, departments, and sessions for dropdowns
$classes = $conn->query("SELECT * FROM classes ORDER BY class_name");
$departments = $conn->query("SELECT * FROM departments ORDER BY department_name");
$sessions = $conn->query("SELECT * FROM sessions ORDER BY session_name DESC");

// Check if the results table has all the necessary columns
$columnsToCheck = [
    'subject_id' => "ALTER TABLE results ADD COLUMN subject_id INT(11) AFTER student_id",
    'cq_marks' => "ALTER TABLE results ADD COLUMN cq_marks DECIMAL(10,2) DEFAULT 0 AFTER grade",
    'mcq_marks' => "ALTER TABLE results ADD COLUMN mcq_marks DECIMAL(10,2) DEFAULT 0 AFTER cq_marks",
    'practical_marks' => "ALTER TABLE results ADD COLUMN practical_marks DECIMAL(10,2) DEFAULT 0 AFTER mcq_marks",
    'remarks' => "ALTER TABLE results ADD COLUMN remarks TEXT AFTER grade"
];

foreach ($columnsToCheck as $column => $alterQuery) {
    $checkColumnQuery = "SHOW COLUMNS FROM results LIKE '$column'";
    $checkColumnResult = $conn->query($checkColumnQuery);

    // If the column doesn't exist, add it
    if ($checkColumnResult->num_rows == 0) {
        $conn->query($alterQuery);
    }
}

// Process form submission for saving marks
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_marks'])) {
    $examId = intval($_POST['exam_id']);
    $subjectId = intval($_POST['subject_id']);
    $marks = $_POST['marks'] ?? [];
    $remarks = $_POST['remarks'] ?? [];
    $date = date('Y-m-d');
    $createdBy = $_SESSION['userId'];

    // Get subject marks distribution
    $distributionQuery = "SELECT * FROM subject_marks_distribution WHERE subject_id = ?";
    $stmt = $conn->prepare($distributionQuery);
    $stmt->bind_param("i", $subjectId);
    $stmt->execute();
    $distribution = $stmt->get_result()->fetch_assoc();

    $totalMarks = $distribution ? $distribution['total_marks'] : 100;

    // Begin transaction
    $conn->begin_transaction();

    try {
        // First, get a list of students who have this subject
        $studentsWithSubjectQuery = "SELECT student_id FROM student_subjects WHERE subject_id = ?";
        $stmt = $conn->prepare($studentsWithSubjectQuery);
        $stmt->bind_param("i", $subjectId);
        $stmt->execute();
        $result = $stmt->get_result();

        $studentsWithSubject = [];
        while ($row = $result->fetch_assoc()) {
            $studentsWithSubject[] = $row['student_id'];
        }

        foreach ($marks as $studentId => $marksData) {
            // Skip students who don't have this subject
            if (!in_array($studentId, $studentsWithSubject)) {
                continue;
            }

            // Get the subject exam pattern
            $patternQuery = "SELECT * FROM subject_exam_pattern WHERE subject_id = ? AND is_active = 1";
            $stmt = $conn->prepare($patternQuery);
            $stmt->bind_param("i", $subjectId);
            $stmt->execute();
            $pattern = $stmt->get_result()->fetch_assoc();

            // If no pattern found, use default values from marks distribution
            $maxCQMarks = $pattern ? $pattern['cq_marks'] : ($marksDistribution['cq_marks'] ?? 70);
            $maxMCQMarks = $pattern ? $pattern['mcq_marks'] : ($marksDistribution['mcq_marks'] ?? 30);
            $maxPracticalMarks = $pattern ? $pattern['practical_marks'] : ($marksDistribution['practical_marks'] ?? 0);

            // Get and validate component marks
            $cqMarks = isset($marksData['cq']) ? floatval($marksData['cq']) : 0;
            $mcqMarks = isset($marksData['mcq']) ? floatval($marksData['mcq']) : 0;
            $practicalMarks = isset($marksData['practical']) ? floatval($marksData['practical']) : 0;

            // Ensure marks don't exceed maximum allowed
            $cqMarks = min($cqMarks, $maxCQMarks);
            $mcqMarks = min($mcqMarks, $maxMCQMarks);
            $practicalMarks = min($practicalMarks, $maxPracticalMarks);

            $marksObtained = $cqMarks + $mcqMarks + $practicalMarks;
            $studentRemarks = $remarks[$studentId] ?? '';

            // Check if student has passed based on minimum passing marks
            $passStatus = checkPassStatus($cqMarks, $mcqMarks, $practicalMarks, $totalMarks, $subjectId);

            // Calculate grade based on marks percentage and pass status
            $percentage = ($marksObtained / $totalMarks) * 100;
            $grade = calculateGrade($percentage, $passStatus['is_passed']);

            // Check if result already exists
            $hasSubjectIdColumn = $conn->query("SHOW COLUMNS FROM results LIKE 'subject_id'")->num_rows > 0;

            if ($hasSubjectIdColumn) {
                $checkQuery = "SELECT id FROM results WHERE exam_id = ? AND student_id = ? AND subject_id = ?";
                $stmt = $conn->prepare($checkQuery);
                $stmt->bind_param("iii", $examId, $studentId, $subjectId);
            } else {
                $checkQuery = "SELECT id FROM results WHERE exam_id = ? AND student_id = ?";
                $stmt = $conn->prepare($checkQuery);
                $stmt->bind_param("ii", $examId, $studentId);
            }

            $stmt->execute();
            $existingResult = $stmt->get_result();

            if ($existingResult->num_rows > 0) {
                // Update existing result
                $resultId = $existingResult->fetch_assoc()['id'];

                // Check which columns exist
                $hasRemarksColumn = $conn->query("SHOW COLUMNS FROM results LIKE 'remarks'")->num_rows > 0;
                $hasComponentColumns = $conn->query("SHOW COLUMNS FROM results LIKE 'cq_marks'")->num_rows > 0;
                $hasSubjectIdColumn = $conn->query("SHOW COLUMNS FROM results LIKE 'subject_id'")->num_rows > 0;

                // Build the update query dynamically
                $updateParts = [
                    "marks_obtained = ?",
                    "total_marks = ?",
                    "grade = ?"
                ];

                $types = "dds";
                $values = [$marksObtained, $totalMarks, $grade];

                if ($hasRemarksColumn) {
                    $updateParts[] = "remarks = ?";
                    $types .= "s";
                    $values[] = $studentRemarks;
                }

                if ($hasComponentColumns) {
                    $updateParts[] = "cq_marks = ?";
                    $updateParts[] = "mcq_marks = ?";
                    $updateParts[] = "practical_marks = ?";
                    $types .= "ddd";
                    $values = array_merge($values, [$cqMarks, $mcqMarks, $practicalMarks]);
                }

                if ($hasSubjectIdColumn) {
                    $updateParts[] = "subject_id = ?";
                    $types .= "i";
                    $values[] = $subjectId;
                }

                $updateQuery = "UPDATE results SET " . implode(", ", $updateParts) . " WHERE id = ?";
                $types .= "i";
                $values[] = $resultId;

                $stmt = $conn->prepare($updateQuery);

                // Create a reference array for bind_param
                $bindParams = [$types];
                foreach ($values as $key => $value) {
                    $bindParams[] = &$values[$key];
                }

                // Call bind_param with the reference array
                call_user_func_array([$stmt, 'bind_param'], $bindParams);
                $stmt->execute();
            } else {
                // Insert new result
                $hasSubjectIdColumn = $conn->query("SHOW COLUMNS FROM results LIKE 'subject_id'")->num_rows > 0;
                $hasRemarksColumn = $conn->query("SHOW COLUMNS FROM results LIKE 'remarks'")->num_rows > 0;
                $hasComponentColumns = $conn->query("SHOW COLUMNS FROM results LIKE 'cq_marks'")->num_rows > 0;

                // Build the insert query dynamically based on available columns
                $columns = ['exam_id', 'student_id'];
                $placeholders = ['?', '?'];
                $types = 'ii';
                $values = [$examId, $studentId];

                if ($hasSubjectIdColumn) {
                    $columns[] = 'subject_id';
                    $placeholders[] = '?';
                    $types .= 'i';
                    $values[] = $subjectId;
                }

                // Always include marks_obtained, total_marks, and grade
                $columns = array_merge($columns, ['marks_obtained', 'total_marks', 'grade']);
                $placeholders = array_merge($placeholders, ['?', '?', '?']);
                $types .= 'dds';
                $values = array_merge($values, [$marksObtained, $totalMarks, $grade]);

                if ($hasRemarksColumn) {
                    $columns[] = 'remarks';
                    $placeholders[] = '?';
                    $types .= 's';
                    $values[] = $studentRemarks;
                }

                // Always include date and created_by
                $columns = array_merge($columns, ['date', 'created_by']);
                $placeholders = array_merge($placeholders, ['?', '?']);
                $types .= 'si';
                $values = array_merge($values, [$date, $createdBy]);

                if ($hasComponentColumns) {
                    $columns = array_merge($columns, ['cq_marks', 'mcq_marks', 'practical_marks']);
                    $placeholders = array_merge($placeholders, ['?', '?', '?']);
                    $types .= 'ddd';
                    $values = array_merge($values, [$cqMarks, $mcqMarks, $practicalMarks]);
                }

                $insertQuery = "INSERT INTO results (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $placeholders) . ")";
                $stmt = $conn->prepare($insertQuery);

                // Create a reference array for bind_param
                $bindParams = [$types];
                foreach ($values as $key => $value) {
                    $bindParams[] = &$values[$key];
                }

                // Call bind_param with the reference array
                call_user_func_array([$stmt, 'bind_param'], $bindParams);
                $stmt->execute();
            }
        }

        // Commit transaction
        $conn->commit();
        $success_message = "নম্বর সফলভাবে সংরক্ষণ করা হয়েছে।";
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        $error_message = "নম্বর সংরক্ষণ করতে সমস্যা হয়েছে: " . $e->getMessage();
    }
}

// Get exam details if exam is selected
if ($examId > 0) {
    $examQuery = "SELECT e.*, c.class_name, d.department_name, s.session_name
                 FROM exams e
                 LEFT JOIN classes c ON e.class_id = c.id
                 LEFT JOIN departments d ON e.department_id = d.id
                 LEFT JOIN sessions s ON e.session_id = s.id
                 WHERE e.id = ?";
    $stmt = $conn->prepare($examQuery);
    $stmt->bind_param("i", $examId);
    $stmt->execute();
    $selectedExam = $stmt->get_result()->fetch_assoc();

    // Get subjects for this exam
    if ($selectedExam) {
        $subjectsQuery = "SELECT s.*, esr.id as relation_id
                         FROM subjects s
                         JOIN exam_subject_relations esr ON s.id = esr.subject_id
                         WHERE esr.exam_id = ?
                         ORDER BY s.subject_name";
        $stmt = $conn->prepare($subjectsQuery);
        $stmt->bind_param("i", $examId);
        $stmt->execute();
        $subjects = $stmt->get_result();

        // Get selected subject details
        if ($subjectId > 0) {
            $subjectQuery = "SELECT * FROM subjects WHERE id = ?";
            $stmt = $conn->prepare($subjectQuery);
            $stmt->bind_param("i", $subjectId);
            $stmt->execute();
            $selectedSubject = $stmt->get_result()->fetch_assoc();

            // Get marks distribution for this subject
            if ($selectedSubject) {
                // First check if there's a pattern in subject_exam_pattern
                $patternQuery = "SELECT * FROM subject_exam_pattern WHERE subject_id = ? AND is_active = 1";
                $stmt = $conn->prepare($patternQuery);
                $stmt->bind_param("i", $subjectId);
                $stmt->execute();
                $pattern = $stmt->get_result()->fetch_assoc();

                if ($pattern) {
                    // Use pattern from subject_exam_pattern
                    $marksDistribution = [
                        'cq_marks' => $pattern['has_cq'] ? $pattern['cq_marks'] : 0,
                        'mcq_marks' => $pattern['has_mcq'] ? $pattern['mcq_marks'] : 0,
                        'practical_marks' => $pattern['has_practical'] ? $pattern['practical_marks'] : 0,
                        'total_marks' => $pattern['total_marks']
                    ];
                } else {
                    // Fallback to subject_marks_distribution
                    $distributionQuery = "SELECT * FROM subject_marks_distribution WHERE subject_id = ?";
                    $stmt = $conn->prepare($distributionQuery);
                    $stmt->bind_param("i", $subjectId);
                    $stmt->execute();
                    $marksDistribution = $stmt->get_result()->fetch_assoc();

                    // If no distribution found, use default values
                    if (!$marksDistribution) {
                        $marksDistribution = [
                            'cq_marks' => 70,
                            'mcq_marks' => 30,
                            'practical_marks' => 0,
                            'total_marks' => 100
                        ];
                    }
                }

                // Check which columns exist in the results table
                $columnsToSelect = "r.marks_obtained, r.grade";

                // Check for remarks column
                $checkRemarksColumn = $conn->query("SHOW COLUMNS FROM results LIKE 'remarks'");
                if ($checkRemarksColumn->num_rows > 0) {
                    $columnsToSelect .= ", r.remarks";
                }

                // Check for component-wise columns
                $checkCQColumn = $conn->query("SHOW COLUMNS FROM results LIKE 'cq_marks'");
                if ($checkCQColumn->num_rows > 0) {
                    $columnsToSelect .= ", r.cq_marks, r.mcq_marks, r.practical_marks";
                }

                // Check if subject_id column exists in results table
                $hasSubjectIdColumn = $conn->query("SHOW COLUMNS FROM results LIKE 'subject_id'")->num_rows > 0;

                // Get students for this exam and subject
                $studentsQuery = "SELECT s.*, c.class_name, d.department_name, ss.session_name,
                                 $columnsToSelect,
                                 1 as has_subject
                                 FROM students s
                                 LEFT JOIN classes c ON s.class_id = c.id
                                 LEFT JOIN departments d ON s.department_id = d.id
                                 LEFT JOIN sessions ss ON s.session_id = ss.id
                                 INNER JOIN student_subjects ss_rel ON s.id = ss_rel.student_id AND ss_rel.subject_id = ?";

                // Join results table based on whether subject_id column exists
                if ($hasSubjectIdColumn) {
                    $studentsQuery .= " LEFT JOIN results r ON s.id = r.student_id AND r.exam_id = ? AND r.subject_id = ?";
                } else {
                    $studentsQuery .= " LEFT JOIN results r ON s.id = r.student_id AND r.exam_id = ?";
                }

                $studentsQuery .= " WHERE 1=1";

                // Add filters based on exam and user selection
                if (!empty($selectedExam['class_id']) && $classId == 0) {
                    $studentsQuery .= " AND s.class_id = " . intval($selectedExam['class_id']);
                } elseif ($classId > 0) {
                    $studentsQuery .= " AND s.class_id = " . $classId;
                }

                if (!empty($selectedExam['department_id']) && $departmentId == 0) {
                    $studentsQuery .= " AND s.department_id = " . intval($selectedExam['department_id']);
                } elseif ($departmentId > 0) {
                    $studentsQuery .= " AND s.department_id = " . $departmentId;
                }

                if (!empty($selectedExam['session_id']) && $sessionId == 0) {
                    $studentsQuery .= " AND s.session_id = " . intval($selectedExam['session_id']);
                } elseif ($sessionId > 0) {
                    $studentsQuery .= " AND s.session_id = " . $sessionId;
                }

                // Add search term filter
                if (!empty($searchTerm)) {
                    $studentsQuery .= " AND (s.first_name LIKE '%" . $conn->real_escape_string($searchTerm) . "%'
                                       OR s.last_name LIKE '%" . $conn->real_escape_string($searchTerm) . "%'
                                       OR s.roll_number LIKE '%" . $conn->real_escape_string($searchTerm) . "%'
                                       OR s.student_id LIKE '%" . $conn->real_escape_string($searchTerm) . "%')";
                }

                // Add sorting
                switch ($sortBy) {
                    case 'name':
                        $studentsQuery .= " ORDER BY s.first_name, s.last_name";
                        break;
                    case 'department':
                        $studentsQuery .= " ORDER BY d.department_name, s.roll_number";
                        break;
                    case 'roll_number':
                    default:
                        $studentsQuery .= " ORDER BY s.roll_number, s.first_name, s.last_name";
                        break;
                }

                // First, get total count for pagination
                $countQuery = $studentsQuery;

                // Prepare and execute count query
                $countStmt = $conn->prepare($countQuery);

                // Bind parameters based on whether subject_id column exists
                if ($hasSubjectIdColumn) {
                    $countStmt->bind_param("iii", $subjectId, $examId, $subjectId);
                } else {
                    $countStmt->bind_param("ii", $subjectId, $examId);
                }

                $countStmt->execute();
                $countResult = $countStmt->get_result();
                $totalStudents = $countResult->num_rows;

                // Calculate pagination
                $totalPages = 1;
                $offset = 0;

                if ($perPage != 'all') {
                    $perPageInt = intval($perPage);
                    $totalPages = max(1, ceil($totalStudents / $perPageInt)); // Ensure at least 1 page

                    // Ensure page is within valid range
                    if ($page < 1) $page = 1;
                    if ($page > $totalPages) $page = $totalPages;

                    // Calculate offset (ensure it's not negative)
                    $offset = max(0, ($page - 1) * $perPageInt);

                    // Add LIMIT clause to query
                    $studentsQuery .= " LIMIT $offset, $perPageInt";
                }

                // Prepare and execute main query with pagination
                $stmt = $conn->prepare($studentsQuery);

                // Bind parameters based on whether subject_id column exists
                if ($hasSubjectIdColumn) {
                    $stmt->bind_param("iii", $subjectId, $examId, $subjectId);
                } else {
                    $stmt->bind_param("ii", $subjectId, $examId);
                }

                $stmt->execute();
                $students = $stmt->get_result();
            }
        }
    }
}

// Get all exams for the dropdown
$examsQuery = "SELECT e.*, c.class_name, d.department_name, s.session_name
               FROM exams e
               LEFT JOIN classes c ON e.class_id = c.id
               LEFT JOIN departments d ON e.department_id = d.id
               LEFT JOIN sessions s ON e.session_id = s.id
               ORDER BY e.exam_date DESC, e.exam_name";
$exams = $conn->query($examsQuery);

// Function to check if a student has passed based on minimum passing marks
function checkPassStatus($cqMarks, $mcqMarks, $practicalMarks, $totalMarks, $subjectId) {
    global $conn;

    // Get minimum passing marks for this subject
    $minPassQuery = "SELECT * FROM subject_minimum_pass WHERE subject_id = ? AND is_active = 1";
    $stmt = $conn->prepare($minPassQuery);
    $stmt->bind_param("i", $subjectId);
    $stmt->execute();
    $minPass = $stmt->get_result()->fetch_assoc();

    // Default minimum passing marks if not set
    $cqMinMarks = $minPass ? $minPass['cq_min_marks'] : 0;
    $mcqMinMarks = $minPass ? $minPass['mcq_min_marks'] : 0;
    $practicalMinMarks = $minPass ? $minPass['practical_min_marks'] : 0;
    $totalMinPercentage = $minPass ? $minPass['total_min_marks'] : 33;

    // Calculate total percentage
    $totalPercentage = ($totalMarks > 0) ? (($cqMarks + $mcqMarks + $practicalMarks) / $totalMarks) * 100 : 0;

    // Check if each component passes
    $cqPassed = ($cqMinMarks <= 0) || ($cqMarks >= $cqMinMarks);
    $mcqPassed = ($mcqMinMarks <= 0) || ($mcqMarks >= $mcqMinMarks);
    $practicalPassed = ($practicalMinMarks <= 0) || ($practicalMarks >= $practicalMinMarks);

    // Check if total percentage passes
    $totalPassed = ($totalPercentage >= $totalMinPercentage);

    // Student passes only if all components and total percentage pass
    $isPassed = $cqPassed && $mcqPassed && $practicalPassed && $totalPassed;

    return [
        'is_passed' => $isPassed,
        'cq_passed' => $cqPassed,
        'mcq_passed' => $mcqPassed,
        'practical_passed' => $practicalPassed,
        'total_passed' => $totalPassed
    ];
}

// Function to calculate grade based on percentage and pass status
function calculateGrade($percentage, $isPassed = true) {
    // If student failed, return F regardless of percentage
    if (!$isPassed) {
        return 'F';
    }

    if ($percentage >= 80) return 'A+';
    if ($percentage >= 70) return 'A';
    if ($percentage >= 60) return 'A-';
    if ($percentage >= 50) return 'B';
    if ($percentage >= 40) return 'C';
    if ($percentage >= 33) return 'D';
    return 'F';
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পরীক্ষার নম্বর এন্ট্রি</title>
    <?php include 'includes/global-head.php'; ?>
    <style>
        .marks-input {
            width: 70px;
            text-align: center;
        }
        .marks-table th, .marks-table td {
            vertical-align: middle;
        }
        .student-row:nth-child(even) {
            background-color: rgba(0,0,0,0.02);
        }
        .marks-container {
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        .mark-input-group {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .inactive-student {
            background-color: #f8f9fa;
            opacity: 0.8;
        }
        .inactive-student td {
            color: #6c757d;
        }
        .marks-input.invalid {
            border-color: #dc3545;
            background-color: rgba(220, 53, 69, 0.1);
        }
        .marks-error-tooltip {
            position: absolute;
            background-color: #dc3545;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            display: none;
            width: max-content;
            max-width: 200px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .marks-error-tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #dc3545 transparent transparent transparent;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-edit me-2 text-primary"></i> পরীক্ষার নম্বর এন্ট্রি
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="exam_dashboard.php" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left me-1"></i> পরীক্ষা ড্যাশবোর্ড
                        </a>
                    </div>
                </div>

                <!-- Exam Navigation Buttons -->
                <?php include 'exam_buttons.php'; ?>

                <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-1"></i> <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-1"></i> <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <!-- Search and Filter Form -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-search me-2"></i> পরীক্ষা এবং বিষয় নির্বাচন করুন
                        </h5>
                    </div>
                    <div class="card-body">
                        <form action="" method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="exam_id" class="form-label">পরীক্ষা*</label>
                                <select class="form-select" id="exam_id" name="exam_id" required>
                                    <option value="">পরীক্ষা নির্বাচন করুন</option>
                                    <?php if ($exams && $exams->num_rows > 0): ?>
                                        <?php while ($exam = $exams->fetch_assoc()): ?>
                                            <option value="<?php echo $exam['id']; ?>" <?php echo ($examId == $exam['id']) ? 'selected' : ''; ?>>
                                                <?php
                                                    echo htmlspecialchars($exam['exam_name']);
                                                    if (!empty($exam['exam_type'])) {
                                                        echo ' (' . htmlspecialchars($exam['exam_type']) . ')';
                                                    }
                                                    if (!empty($exam['class_name'])) {
                                                        echo ' - ' . htmlspecialchars($exam['class_name']);
                                                    }
                                                    if (!empty($exam['session_name'])) {
                                                        echo ' - ' . htmlspecialchars($exam['session_name']);
                                                    }
                                                ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>

                            <?php if ($examId > 0 && isset($subjects) && $subjects->num_rows > 0): ?>
                            <div class="col-md-4">
                                <label for="subject_id" class="form-label">বিষয়*</label>
                                <select class="form-select" id="subject_id" name="subject_id" required>
                                    <option value="">বিষয় নির্বাচন করুন</option>
                                    <?php while ($subject = $subjects->fetch_assoc()): ?>
                                        <option value="<?php echo $subject['id']; ?>" <?php echo ($subjectId == $subject['id']) ? 'selected' : ''; ?>>
                                            <?php
                                                echo htmlspecialchars($subject['subject_name']);
                                                if (!empty($subject['subject_code'])) {
                                                    echo ' (' . htmlspecialchars($subject['subject_code']) . ')';
                                                }
                                            ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            <?php endif; ?>

                            <div class="col-md-3">
                                <label for="search" class="form-label">শিক্ষার্থী খুঁজুন</label>
                                <input type="text" class="form-control" id="search" name="search" placeholder="নাম, রোল বা আইডি দিয়ে খুঁজুন" value="<?php echo htmlspecialchars($searchTerm); ?>">
                            </div>

                            <div class="col-md-3">
                                <label for="per_page" class="form-label">প্রতি পেজে শিক্ষার্থী সংখ্যা</label>
                                <select class="form-select" id="per_page" name="per_page">
                                    <option value="10" <?php echo (isset($_GET['per_page']) && $_GET['per_page'] == '10') ? 'selected' : ''; ?>>১০ জন</option>
                                    <option value="25" <?php echo (isset($_GET['per_page']) && $_GET['per_page'] == '25') ? 'selected' : ''; ?>>২৫ জন</option>
                                    <option value="50" <?php echo (isset($_GET['per_page']) && $_GET['per_page'] == '50') ? 'selected' : ''; ?>>৫০ জন</option>
                                    <option value="100" <?php echo (isset($_GET['per_page']) && $_GET['per_page'] == '100') ? 'selected' : ''; ?>>১০০ জন</option>
                                    <option value="all" <?php echo (isset($_GET['per_page']) && $_GET['per_page'] == 'all') ? 'selected' : ''; ?>>সকল</option>
                                </select>
                            </div>

                            <?php if ($examId > 0 && $subjectId > 0): ?>
                            <div class="col-md-4">
                                <label for="class_id" class="form-label">শ্রেণী</label>
                                <select class="form-select" id="class_id" name="class_id">
                                    <option value="0">সকল শ্রেণী</option>
                                    <?php if ($classes && $classes->num_rows > 0): ?>
                                        <?php $classes->data_seek(0); ?>
                                        <?php while ($class = $classes->fetch_assoc()): ?>
                                            <option value="<?php echo $class['id']; ?>" <?php echo ($classId == $class['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($class['class_name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>

                            <div class="col-md-4">
                                <label for="department_id" class="form-label">বিভাগ</label>
                                <select class="form-select" id="department_id" name="department_id">
                                    <option value="0">সকল বিভাগ</option>
                                    <?php if ($departments && $departments->num_rows > 0): ?>
                                        <?php $departments->data_seek(0); ?>
                                        <?php while ($department = $departments->fetch_assoc()): ?>
                                            <option value="<?php echo $department['id']; ?>" <?php echo ($departmentId == $department['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($department['department_name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>

                            <div class="col-md-4">
                                <label for="session_id" class="form-label">সেশন</label>
                                <select class="form-select" id="session_id" name="session_id">
                                    <option value="0">সকল সেশন</option>
                                    <?php if ($sessions && $sessions->num_rows > 0): ?>
                                        <?php $sessions->data_seek(0); ?>
                                        <?php while ($session = $sessions->fetch_assoc()): ?>
                                            <option value="<?php echo $session['id']; ?>" <?php echo ($sessionId == $session['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($session['session_name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                            <?php endif; ?>

                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i> খুঁজুন
                                </button>
                                <?php if ($examId > 0 && $subjectId > 0): ?>
                                <a href="marks_entry.php" class="btn btn-outline-secondary ms-2">
                                    <i class="fas fa-redo me-1"></i> রিসেট
                                </a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Marks Entry Form -->
                <?php if ($examId > 0 && $subjectId > 0 && $selectedSubject && $students && $students->num_rows > 0): ?>
                <div class="card mb-4">
                    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-edit me-2"></i> নম্বর এন্ট্রি - <?php echo htmlspecialchars($selectedSubject['subject_name']); ?>
                            <?php if (!empty($selectedSubject['subject_code'])): ?>
                                (<?php echo htmlspecialchars($selectedSubject['subject_code']); ?>)
                            <?php endif; ?>
                        </h5>
                        <div>
                            <span class="badge bg-light text-dark">
                                মোট নম্বর: <?php echo $marksDistribution['total_marks']; ?>
                            </span>
                        </div>
                    </div>
                    <div class="card-body">
                        <form action="" method="POST" id="marksForm">
                            <input type="hidden" name="exam_id" value="<?php echo $examId; ?>">
                            <input type="hidden" name="subject_id" value="<?php echo $subjectId; ?>">

                            <div class="table-responsive">
                                <table class="table table-bordered table-hover marks-table">
                                    <thead class="table-light">
                                        <tr>
                                            <th style="width: 60px;">ক্রম</th>
                                            <th style="width: 100px;">রোল</th>
                                            <th>শিক্ষার্থীর নাম</th>
                                            <th style="width: 250px;">
                                                নম্বর বিভাজন
                                                <div class="small text-muted mt-1">
                                                    <?php
                                                    $components = [];
                                                    if (isset($marksDistribution['cq_marks']) && $marksDistribution['cq_marks'] > 0) {
                                                        $components[] = "CQ (" . $marksDistribution['cq_marks'] . ")";
                                                    }
                                                    if (isset($marksDistribution['mcq_marks']) && $marksDistribution['mcq_marks'] > 0) {
                                                        $components[] = "MCQ (" . $marksDistribution['mcq_marks'] . ")";
                                                    }
                                                    if (isset($marksDistribution['practical_marks']) && $marksDistribution['practical_marks'] > 0) {
                                                        $components[] = "Practical (" . $marksDistribution['practical_marks'] . ")";
                                                    }
                                                    echo implode(' | ', $components);
                                                    ?>
                                                </div>
                                            </th>
                                            <th style="width: 100px;">মোট</th>
                                            <th style="width: 100px;">গ্রেড</th>
                                            <th>মন্তব্য</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $counter = 1;
                                        $hasComponentColumns = $conn->query("SHOW COLUMNS FROM results LIKE 'cq_marks'")->num_rows > 0;
                                        $hasRemarksColumn = $conn->query("SHOW COLUMNS FROM results LIKE 'remarks'")->num_rows > 0;

                                        while ($student = $students->fetch_assoc()):
                                            $hasCQ = $marksDistribution['cq_marks'] > 0;
                                            $hasMCQ = $marksDistribution['mcq_marks'] > 0;
                                            $hasPractical = $marksDistribution['practical_marks'] > 0;

                                            // Initialize component marks
                                            $cqMarks = 0;
                                            $mcqMarks = 0;
                                            $practicalMarks = 0;

                                            // If component columns exist in the database, use their values
                                            if ($hasComponentColumns) {
                                                $cqMarks = $student['cq_marks'] ?? 0;
                                                $mcqMarks = $student['mcq_marks'] ?? 0;
                                                $practicalMarks = $student['practical_marks'] ?? 0;
                                            } else if (isset($student['marks_obtained'])) {
                                                // If no component columns but total marks exist, distribute proportionally
                                                $totalObtained = $student['marks_obtained'] ?? 0;
                                                $totalPossible = $marksDistribution['total_marks'];

                                                if ($totalPossible > 0 && $totalObtained > 0) {
                                                    $ratio = $totalObtained / $totalPossible;

                                                    if ($hasCQ) {
                                                        $cqMarks = round($marksDistribution['cq_marks'] * $ratio, 2);
                                                    }

                                                    if ($hasMCQ) {
                                                        $mcqMarks = round($marksDistribution['mcq_marks'] * $ratio, 2);
                                                    }

                                                    if ($hasPractical) {
                                                        $practicalMarks = round($marksDistribution['practical_marks'] * $ratio, 2);
                                                    }
                                                }
                                            }

                                            $totalMarks = $student['marks_obtained'] ?? 0;
                                            $grade = $student['grade'] ?? '';
                                            $remarks = $hasRemarksColumn ? ($student['remarks'] ?? '') : '';
                                            $hasSubject = $student['has_subject'] == 1;
                                        ?>
                                        <tr class="student-row <?php echo !$hasSubject ? 'inactive-student' : ''; ?>">
                                            <td class="text-center"><?php echo $counter++; ?></td>
                                            <td>
                                                <?php echo htmlspecialchars($student['roll_number']); ?>
                                                <div class="small text-muted"><?php echo htmlspecialchars($student['student_id']); ?></div>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>
                                                <div class="small text-muted">
                                                    <?php
                                                        $studentInfo = [];
                                                        if (!empty($student['class_name'])) $studentInfo[] = $student['class_name'];
                                                        if (!empty($student['department_name'])) $studentInfo[] = $student['department_name'];
                                                        if (!empty($student['session_name'])) $studentInfo[] = $student['session_name'];
                                                        echo implode(' | ', $studentInfo);
                                                    ?>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if (!$hasSubject): ?>
                                                <div class="alert alert-warning py-1 px-2 mb-0 text-center">
                                                    <small><i class="fas fa-exclamation-triangle me-1"></i> এই শিক্ষার্থীর এই বিষয় নেই</small>
                                                </div>
                                                <?php else: ?>
                                                <div class="marks-container">
                                                    <?php if ($hasCQ): ?>
                                                    <div class="mark-input-group">
                                                        <input type="number" class="form-control form-control-sm marks-input cq-marks"
                                                               id="cq_<?php echo $student['id']; ?>"
                                                               name="marks[<?php echo $student['id']; ?>][cq]"
                                                               value="<?php echo $cqMarks; ?>"
                                                               min="0" max="<?php echo $marksDistribution['cq_marks']; ?>"
                                                               step="0.01"
                                                               data-student-id="<?php echo $student['id']; ?>"
                                                               oninput="this.value = this.value > <?php echo $marksDistribution['cq_marks']; ?> ? <?php echo $marksDistribution['cq_marks']; ?> : this.value"
                                                               placeholder="CQ">
                                                    </div>
                                                    <?php endif; ?>

                                                    <?php if ($hasMCQ): ?>
                                                    <div class="mark-input-group">
                                                        <input type="number" class="form-control form-control-sm marks-input mcq-marks"
                                                               id="mcq_<?php echo $student['id']; ?>"
                                                               name="marks[<?php echo $student['id']; ?>][mcq]"
                                                               value="<?php echo $mcqMarks; ?>"
                                                               min="0" max="<?php echo $marksDistribution['mcq_marks']; ?>"
                                                               step="0.01"
                                                               data-student-id="<?php echo $student['id']; ?>"
                                                               oninput="this.value = this.value > <?php echo $marksDistribution['mcq_marks']; ?> ? <?php echo $marksDistribution['mcq_marks']; ?> : this.value"
                                                               placeholder="MCQ">
                                                    </div>
                                                    <?php endif; ?>

                                                    <?php if ($hasPractical): ?>
                                                    <div class="mark-input-group">
                                                        <input type="number" class="form-control form-control-sm marks-input practical-marks"
                                                               id="practical_<?php echo $student['id']; ?>"
                                                               name="marks[<?php echo $student['id']; ?>][practical]"
                                                               value="<?php echo $practicalMarks; ?>"
                                                               min="0" max="<?php echo $marksDistribution['practical_marks']; ?>"
                                                               step="0.01"
                                                               data-student-id="<?php echo $student['id']; ?>"
                                                               oninput="this.value = this.value > <?php echo $marksDistribution['practical_marks']; ?> ? <?php echo $marksDistribution['practical_marks']; ?> : this.value"
                                                               placeholder="Practical">
                                                    </div>
                                                    <?php endif; ?>
                                                </div>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-center">
                                                <?php if ($hasSubject): ?>
                                                <span class="total-marks badge bg-primary" id="total_<?php echo $student['id']; ?>"><?php echo $totalMarks; ?></span>
                                                <input type="hidden" id="total_input_<?php echo $student['id']; ?>" value="<?php echo $totalMarks; ?>">
                                                <?php else: ?>
                                                <span class="badge bg-secondary">N/A</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-center">
                                                <?php if ($hasSubject): ?>
                                                <span class="grade-display badge bg-success" id="grade_<?php echo $student['id']; ?>"><?php echo $grade; ?></span>
                                                <?php else: ?>
                                                <span class="badge bg-secondary">N/A</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <input type="text" class="form-control form-control-sm"
                                                       name="remarks[<?php echo $student['id']; ?>]"
                                                       value="<?php echo htmlspecialchars($remarks); ?>"
                                                       placeholder="মন্তব্য (ঐচ্ছিক)"
                                                       <?php echo !$hasSubject ? 'disabled' : ''; ?>>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>

                            <div class="d-flex justify-content-end mt-3">
                                <button type="submit" name="save_marks" class="btn btn-success">
                                    <i class="fas fa-save me-1"></i> নম্বর সংরক্ষণ করুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <?php elseif ($examId > 0 && $subjectId > 0 && $selectedSubject && (!$students || $students->num_rows == 0)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> নির্বাচিত পরীক্ষা এবং বিষয়ের জন্য কোন শিক্ষার্থী পাওয়া যায়নি।
                </div>
                <?php elseif ($examId > 0 && (!$subjectId || $subjectId == 0)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> অনুগ্রহ করে একটি বিষয় নির্বাচন করুন।
                </div>
                <?php elseif (!$examId || $examId == 0): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> অনুগ্রহ করে একটি পরীক্ষা নির্বাচন করুন।
                </div>
                <?php endif; ?>

                <?php if ($examId > 0 && $subjectId > 0 && $selectedSubject && $students && $students->num_rows > 0 && $perPage != 'all' && $totalPages > 1): ?>
                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3 mb-4">
                    <div class="text-muted">
                        মোট <?php echo $totalStudents; ?> জন শিক্ষার্থী |
                        <?php echo $offset + 1; ?> - <?php echo min($offset + intval($perPage), $totalStudents); ?> দেখাচ্ছে
                    </div>
                    <nav aria-label="Page navigation">
                        <ul class="pagination mb-0">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php
                                    $params = $_GET;
                                    $params['page'] = 1;
                                    echo http_build_query($params);
                                ?>" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?<?php
                                    $params = $_GET;
                                    $params['page'] = $page - 1;
                                    echo http_build_query($params);
                                ?>" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            <?php endif; ?>

                            <?php
                            // Calculate range of pages to show
                            $startPage = max(1, $page - 2);
                            $endPage = min($totalPages, $page + 2);

                            // Always show at least 5 pages if available
                            if ($endPage - $startPage + 1 < 5) {
                                if ($startPage == 1) {
                                    $endPage = min($totalPages, $startPage + 4);
                                } elseif ($endPage == $totalPages) {
                                    $startPage = max(1, $endPage - 4);
                                }
                            }

                            // Show pages
                            for ($i = $startPage; $i <= $endPage; $i++):
                            ?>
                            <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                <a class="page-link" href="?<?php
                                    $params = $_GET;
                                    $params['page'] = $i;
                                    echo http_build_query($params);
                                ?>"><?php echo $i; ?></a>
                            </li>
                            <?php endfor; ?>

                            <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php
                                    $params = $_GET;
                                    $params['page'] = $page + 1;
                                    echo http_build_query($params);
                                ?>" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?<?php
                                    $params = $_GET;
                                    $params['page'] = $totalPages;
                                    echo http_build_query($params);
                                ?>" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get the marks distribution from PHP
            const totalMarks = <?php echo isset($marksDistribution['total_marks']) ? $marksDistribution['total_marks'] : 100; ?>;
            const cqMaxMarks = <?php echo isset($marksDistribution['cq_marks']) ? $marksDistribution['cq_marks'] : 70; ?>;
            const mcqMaxMarks = <?php echo isset($marksDistribution['mcq_marks']) ? $marksDistribution['mcq_marks'] : 30; ?>;
            const practicalMaxMarks = <?php echo isset($marksDistribution['practical_marks']) ? $marksDistribution['practical_marks'] : 0; ?>;

            // Get minimum passing marks for this subject
            <?php
            // Get minimum passing marks for this subject
            $minPassQuery = "SELECT * FROM subject_minimum_pass WHERE subject_id = ? AND is_active = 1";
            $stmt = $conn->prepare($minPassQuery);
            $stmt->bind_param("i", $subjectId);
            $stmt->execute();
            $minPass = $stmt->get_result()->fetch_assoc();

            // Default minimum passing marks if not set
            $cqMinMarks = $minPass ? $minPass['cq_min_marks'] : 0;
            $mcqMinMarks = $minPass ? $minPass['mcq_min_marks'] : 0;
            $practicalMinMarks = $minPass ? $minPass['practical_min_marks'] : 0;
            $totalMinPercentage = $minPass ? $minPass['total_min_marks'] : 33;
            ?>

            const cqMinMarks = <?php echo $cqMinMarks; ?>;
            const mcqMinMarks = <?php echo $mcqMinMarks; ?>;
            const practicalMinMarks = <?php echo $practicalMinMarks; ?>;
            const totalMinPercentage = <?php echo $totalMinPercentage; ?>;

            // Calculate total marks and grade when marks inputs change
            const marksInputs = document.querySelectorAll('.marks-input');

            // Create tooltip element
            const tooltip = document.createElement('div');
            tooltip.className = 'marks-error-tooltip';
            document.body.appendChild(tooltip);

            marksInputs.forEach(input => {
                // Add input event listener
                input.addEventListener('input', function() {
                    const studentId = this.dataset.studentId;
                    validateAndLimitMarksInput(this);
                    calculateTotalAndGrade(studentId);
                });

                // Add focus event listener
                input.addEventListener('focus', function() {
                    validateAndLimitMarksInput(this);
                });

                // Add blur event listener
                input.addEventListener('blur', function() {
                    validateAndLimitMarksInput(this, true);
                });
            });

            // Validate marks input against maximum allowed and limit the input value
            function validateAndLimitMarksInput(input, isBlur = false) {
                // Remove any existing tooltip
                hideTooltip();
                input.classList.remove('invalid');

                // Get the input value
                let value = parseFloat(input.value || 0);

                // Determine the maximum allowed value based on input type
                let maxAllowed = 0;
                let componentName = '';

                if (input.id.startsWith('cq_')) {
                    maxAllowed = cqMaxMarks;
                    componentName = 'সিকিউ';
                } else if (input.id.startsWith('mcq_')) {
                    maxAllowed = mcqMaxMarks;
                    componentName = 'এমসিকিউ';
                } else if (input.id.startsWith('practical_')) {
                    maxAllowed = practicalMaxMarks;
                    componentName = 'ব্যবহারিক';
                }

                // Check if the value exceeds the maximum allowed
                if (value > maxAllowed) {
                    // Show tooltip
                    const rect = input.getBoundingClientRect();
                    tooltip.style.left = rect.left + (rect.width / 2) - 100 + 'px';
                    tooltip.style.top = rect.top - 40 + 'px';
                    tooltip.textContent = `সর্বোচ্চ ${maxAllowed} নম্বর দেওয়া যাবে ${componentName} এর জন্য`;
                    tooltip.style.display = 'block';

                    // Limit the input value to the maximum allowed
                    input.value = maxAllowed;

                    // Add invalid class for visual feedback
                    input.classList.add('invalid');

                    // Hide tooltip after 3 seconds
                    setTimeout(hideTooltip, 3000);
                }
            }

            // Hide tooltip
            function hideTooltip() {
                tooltip.style.display = 'none';
            }

            // Add click event listener to document to hide tooltip
            document.addEventListener('click', function(event) {
                if (!event.target.classList.contains('marks-input')) {
                    hideTooltip();
                }
            });

            function calculateTotalAndGrade(studentId) {
                let total = 0;

                // Calculate CQ marks
                const cqInput = document.getElementById('cq_' + studentId);
                if (cqInput) {
                    let cqValue = parseFloat(cqInput.value || 0);
                    // Ensure CQ marks don't exceed maximum
                    if (cqValue > cqMaxMarks) {
                        cqValue = cqMaxMarks;
                        cqInput.value = cqMaxMarks; // Update the input field
                    }
                    total += cqValue;
                }

                // Calculate MCQ marks
                const mcqInput = document.getElementById('mcq_' + studentId);
                if (mcqInput) {
                    let mcqValue = parseFloat(mcqInput.value || 0);
                    // Ensure MCQ marks don't exceed maximum
                    if (mcqValue > mcqMaxMarks) {
                        mcqValue = mcqMaxMarks;
                        mcqInput.value = mcqMaxMarks; // Update the input field
                    }
                    total += mcqValue;
                }

                // Calculate Practical marks
                const practicalInput = document.getElementById('practical_' + studentId);
                if (practicalInput) {
                    let practicalValue = parseFloat(practicalInput.value || 0);
                    // Ensure Practical marks don't exceed maximum
                    if (practicalValue > practicalMaxMarks) {
                        practicalValue = practicalMaxMarks;
                        practicalInput.value = practicalMaxMarks; // Update the input field
                    }
                    total += practicalValue;
                }

                // Update total display
                const totalDisplay = document.getElementById('total_' + studentId);
                if (totalDisplay) {
                    totalDisplay.textContent = total.toFixed(2);
                }

                // Check if student has passed based on minimum passing marks
                const cqValue = parseFloat(cqInput ? cqInput.value || 0 : 0);
                const mcqValue = parseFloat(mcqInput ? mcqInput.value || 0 : 0);
                const practicalValue = parseFloat(practicalInput ? practicalInput.value || 0 : 0);

                // Check if each component passes
                const cqPassed = (cqMinMarks <= 0) || (cqValue >= cqMinMarks);
                const mcqPassed = (mcqMinMarks <= 0) || (mcqValue >= mcqMinMarks);
                const practicalPassed = (practicalMinMarks <= 0) || (practicalValue >= practicalMinMarks);

                // Calculate total percentage
                const percentage = (total / totalMarks) * 100;

                // Check if total percentage passes
                const totalPassed = (percentage >= totalMinPercentage);

                // Student passes only if all components and total percentage pass
                const isPassed = cqPassed && mcqPassed && practicalPassed && totalPassed;

                // Calculate grade based on percentage and pass status
                let grade = '';

                if (!isPassed) {
                    grade = 'F';
                } else if (percentage >= 80) {
                    grade = 'A+';
                } else if (percentage >= 70) {
                    grade = 'A';
                } else if (percentage >= 60) {
                    grade = 'A-';
                } else if (percentage >= 50) {
                    grade = 'B';
                } else if (percentage >= 40) {
                    grade = 'C';
                } else if (percentage >= 33) {
                    grade = 'D';
                } else {
                    grade = 'F';
                }

                // Update grade display
                const gradeDisplay = document.getElementById('grade_' + studentId);
                if (gradeDisplay) {
                    gradeDisplay.textContent = grade;

                    // Update grade badge color
                    gradeDisplay.className = 'grade-display badge';
                    if (grade === 'A+' || grade === 'A') {
                        gradeDisplay.classList.add('bg-success');
                    } else if (grade === 'A-' || grade === 'B') {
                        gradeDisplay.classList.add('bg-info');
                    } else if (grade === 'C') {
                        gradeDisplay.classList.add('bg-primary');
                    } else if (grade === 'D') {
                        gradeDisplay.classList.add('bg-warning');
                    } else {
                        gradeDisplay.classList.add('bg-danger');
                    }
                }
            }

            // Initialize all totals and grades
            marksInputs.forEach(input => {
                const studentId = input.dataset.studentId;
                calculateTotalAndGrade(studentId);
            });

            // Add form submission validation
            const marksForm = document.getElementById('marksForm');
            if (marksForm) {
                marksForm.addEventListener('submit', function(event) {
                    // Check for any invalid inputs
                    const invalidInputs = document.querySelectorAll('.marks-input.invalid');
                    if (invalidInputs.length > 0) {
                        event.preventDefault();

                        // Scroll to the first invalid input
                        invalidInputs[0].scrollIntoView({ behavior: 'smooth', block: 'center' });

                        // Show alert
                        alert('দয়া করে সমস্ত ত্রুটি সংশোধন করুন। কিছু নম্বর সর্বোচ্চ অনুমোদিত সীমার বেশি।');

                        // Focus on the first invalid input
                        setTimeout(() => {
                            invalidInputs[0].focus();
                        }, 500);
                    }
                });
            }
        });
    </script>
</body>
</html>