<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if student_shortcodes table exists
$check_shortcodes_table = "SHOW TABLES LIKE 'student_shortcodes'";
$shortcodes_table_exists = $conn->query($check_shortcodes_table)->num_rows > 0;

if (!$shortcodes_table_exists) {
    echo "সর্টকোড টেবিল পাওয়া যায়নি।";
    exit;
}

// Check students table structure
$check_students_columns = "SHOW COLUMNS FROM students";
$students_columns_result = $conn->query($check_students_columns);
$has_first_name = false;
$has_last_name = false;
$has_student_id = false;
$has_class_id = false;
$has_phone = false;

if ($students_columns_result) {
    while ($column = $students_columns_result->fetch_assoc()) {
        if ($column['Field'] == 'first_name') $has_first_name = true;
        if ($column['Field'] == 'last_name') $has_last_name = true;
        if ($column['Field'] == 'student_id') $has_student_id = true;
        if ($column['Field'] == 'class_id') $has_class_id = true;
        if ($column['Field'] == 'phone') $has_phone = true;
    }
}

// Build select fields based on available columns
$select_fields = "sc.shortcode";
if ($has_first_name) $select_fields .= ", s.first_name";
if ($has_last_name) $select_fields .= ", s.last_name";
if ($has_student_id) $select_fields .= ", s.student_id";
if ($has_phone) $select_fields .= ", s.phone";

// Check if classes table exists
$check_classes_table = "SHOW TABLES LIKE 'classes'";
$classes_table_exists = $conn->query($check_classes_table)->num_rows > 0;

$join_clause = "";
if ($classes_table_exists && $has_class_id) {
    $join_clause = "LEFT JOIN classes c ON s.class_id = c.id";
    $select_fields .= ", c.class_name";
}

// Get shortcodes data
$shortcodes_query = "SELECT $select_fields
                    FROM student_shortcodes sc
                    JOIN students s ON sc.student_id = s.id
                    $join_clause
                    ORDER BY sc.shortcode";
$shortcodes_result = $conn->query($shortcodes_query);

// Prepare data
$students = [];
if ($shortcodes_result && $shortcodes_result->num_rows > 0) {
    while ($row = $shortcodes_result->fetch_assoc()) {
        $name = '';
        if (isset($row['first_name'])) {
            $name .= $row['first_name'];
        }
        if (isset($row['last_name'])) {
            $name .= ' ' . $row['last_name'];
        }

        $phone = isset($row['phone']) ? $row['phone'] : '';
        $roll = isset($row['student_id']) ? $row['student_id'] : 'N/A';
        $class = isset($row['class_name']) ? $row['class_name'] : 'N/A';

        $whatsapp_link = '';
        if (!empty($phone)) {
            $whatsapp_link = 'https://wa.me/' . (substr($phone, 0, 1) === '0' ? '88' . $phone : $phone) . '?text=শিক্ষার্থীর সর্টকোড: ' . $row['shortcode'];
        } else {
            $whatsapp_link = 'https://wa.me/?text=শিক্ষার্থীর সর্টকোড: ' . $row['shortcode'];
        }

        $students[] = [
            'shortcode' => $row['shortcode'],
            'name' => $name,
            'roll' => $roll,
            'class' => $class,
            'phone' => $phone,
            'whatsapp_link' => $whatsapp_link
        ];
    }
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>শিক্ষার্থী সর্টকোড QR কোড - ZFAW</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <style>
        body {
            font-family: 'SolaimanLipi', Arial, sans-serif;
            background-color: #f8f9fa;
        }
        .qr-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
            padding: 20px;
        }
        .qr-card {
            width: 300px;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            text-align: center;
        }
        .qr-code {
            margin: 10px auto;
            width: 200px;
            height: 200px;
        }
        .shortcode {
            font-size: 24px;
            font-weight: bold;
            color: #0d6efd;
            margin: 10px 0;
        }
        .student-info {
            margin-bottom: 10px;
        }
        .print-header {
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
        }
        .print-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        @media print {
            .print-btn {
                display: none;
            }
            .qr-card {
                break-inside: avoid;
                page-break-inside: avoid;
                box-shadow: none;
            }
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <button class="btn btn-primary print-btn" onclick="window.print()">
            <i class="fas fa-print"></i> প্রিন্ট করুন
        </button>

        <div class="print-header">
            <h1>শিক্ষার্থী সর্টকোড QR কোড</h1>
            <p>QR কোড স্ক্যান করে WhatsApp-এ সর্টকোড পাঠান</p>
        </div>

        <div class="qr-container">
            <?php foreach ($students as $student): ?>
                <div class="qr-card">
                    <div class="student-info">
                        <h5><?php echo !empty($student['name']) ? $student['name'] : 'শিক্ষার্থী'; ?></h5>
                        <p>রোল: <?php echo $student['roll']; ?> | শ্রেণী: <?php echo $student['class']; ?></p>
                    </div>

                    <div class="shortcode">
                        <?php echo $student['shortcode']; ?>
                    </div>

                    <div class="qr-code">
                        <img src="https://chart.googleapis.com/chart?cht=qr&chs=200x200&chl=<?php echo urlencode($student['whatsapp_link']); ?>"
                             alt="QR Code" class="img-fluid">
                    </div>

                    <div class="mt-2">
                        <small>QR কোড স্ক্যান করুন</small>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</body>
</html>
