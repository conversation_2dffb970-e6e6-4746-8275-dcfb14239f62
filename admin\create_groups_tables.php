<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$successMessages = [];
$errorMessages = [];

// Create groups table if it doesn't exist
$groupsTableQuery = "CREATE TABLE IF NOT EXISTS groups (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    group_name VARCHAR(255) NOT NULL,
    group_code VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($groupsTableQuery)) {
    $successMessages[] = "গ্রুপ টেবিল সফলভাবে তৈরি করা হয়েছে।";
} else {
    $errorMessages[] = "গ্রুপ টেবিল তৈরি করতে সমস্যা হয়েছে: " . $conn->error;
}

// Create subject_groups table if it doesn't exist
$subjectGroupsTableQuery = "CREATE TABLE IF NOT EXISTS subject_groups (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    subject_id INT(11) NOT NULL,
    group_id INT(11) NOT NULL,
    subject_type ENUM('required', 'optional', 'fourth') NOT NULL DEFAULT 'optional',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (group_id) REFERENCES groups(id) ON DELETE CASCADE
)";

if ($conn->query($subjectGroupsTableQuery)) {
    $successMessages[] = "বিষয়-গ্রুপ টেবিল সফলভাবে তৈরি করা হয়েছে।";
} else {
    $errorMessages[] = "বিষয়-গ্রুপ টেবিল তৈরি করতে সমস্যা হয়েছে: " . $conn->error;
}

// Check if groups table has any data
$checkGroupsQuery = "SELECT COUNT(*) as count FROM groups";
$result = $conn->query($checkGroupsQuery);
$groupCount = 0;

if ($result) {
    $groupCount = $result->fetch_assoc()['count'];
}

// Add sample groups if the table is empty
if ($groupCount == 0) {
    // Sample groups
    $sampleGroups = [
        ['বিজ্ঞান', 'SCI', 'বিজ্ঞান বিভাগের জন্য'],
        ['মানবিক', 'HUM', 'মানবিক বিভাগের জন্য'],
        ['ব্যবসায় শিক্ষা', 'COM', 'ব্যবসায় শিক্ষা বিভাগের জন্য']
    ];

    $insertGroupQuery = "INSERT INTO groups (group_name, group_code, description, is_active) VALUES (?, ?, ?, 1)";
    $stmt = $conn->prepare($insertGroupQuery);

    foreach ($sampleGroups as $group) {
        $stmt->bind_param("sss", $group[0], $group[1], $group[2]);
        if ($stmt->execute()) {
            $successMessages[] = "নমুনা গ্রুপ '{$group[0]}' সফলভাবে যোগ করা হয়েছে।";
        } else {
            $errorMessages[] = "নমুনা গ্রুপ '{$group[0]}' যোগ করতে সমস্যা হয়েছে: " . $stmt->error;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>গ্রুপ টেবিল সেটআপ - কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="groups.php">
                            <i class="fas fa-layer-group me-2"></i> গ্রুপ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sessions.php">
                            <i class="fas fa-calendar-alt me-2"></i> সেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>গ্রুপ টেবিল সেটআপ</h2>
                        <p class="text-muted">গ্রুপ সংক্রান্ত টেবিল তৈরি এবং সেটআপ করুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="groups.php" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>গ্রুপ পৃষ্ঠায় ফিরে যান
                        </a>
                    </div>
                </div>

                <?php if (!empty($successMessages)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <h5 class="alert-heading"><i class="fas fa-check-circle me-2"></i>সফল!</h5>
                        <ul class="mb-0">
                            <?php foreach ($successMessages as $message): ?>
                                <li><?php echo $message; ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($errorMessages)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <h5 class="alert-heading"><i class="fas fa-exclamation-circle me-2"></i>ত্রুটি!</h5>
                        <ul class="mb-0">
                            <?php foreach ($errorMessages as $message): ?>
                                <li><?php echo $message; ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Setup Status -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">টেবিল সেটআপ স্ট্যাটাস</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>টেবিল</th>
                                        <th>অবস্থা</th>
                                        <th>বিবরণ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $tables = [
                                        'groups' => 'গ্রুপ টেবিল',
                                        'subject_groups' => 'বিষয়-গ্রুপ টেবিল'
                                    ];

                                    foreach ($tables as $table => $tableName):
                                        $tableExists = $conn->query("SHOW TABLES LIKE '$table'")->num_rows > 0;

                                        if ($tableExists) {
                                            $countQuery = "SELECT COUNT(*) as count FROM $table";
                                            $countResult = $conn->query($countQuery);
                                            $count = $countResult->fetch_assoc()['count'];
                                        } else {
                                            $count = 0;
                                        }
                                    ?>
                                        <tr>
                                            <td><?php echo $tableName; ?></td>
                                            <td>
                                                <?php if ($tableExists): ?>
                                                    <span class="badge bg-success">তৈরি করা হয়েছে</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">তৈরি করা হয়নি</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($tableExists): ?>
                                                    <span class="text-success">
                                                        <i class="fas fa-check-circle me-1"></i>
                                                        টেবিল সফলভাবে তৈরি করা হয়েছে। <?php echo $count; ?> টি রেকর্ড আছে।
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-danger">
                                                        <i class="fas fa-times-circle me-1"></i>
                                                        টেবিল তৈরি করা হয়নি। পুনরায় চেষ্টা করুন।
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer">
                        <form method="POST" action="create_groups_tables.php">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-sync-alt me-2"></i>টেবিল পুনরায় তৈরি করুন
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Next Steps -->
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">পরবর্তী পদক্ষেপ</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-layer-group fa-3x text-primary mb-3"></i>
                                        <h5>গ্রুপ ব্যবস্থাপনা</h5>
                                        <p>গ্রুপ যোগ করুন, সম্পাদনা করুন এবং ব্যবস্থাপনা করুন</p>
                                        <a href="groups.php" class="btn btn-outline-primary">গ্রুপ পৃষ্ঠায় যান</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-link fa-3x text-success mb-3"></i>
                                        <h5>বিষয়-গ্রুপ সংযোগ</h5>
                                        <p>বিষয়গুলিকে গ্রুপের সাথে সংযুক্ত করুন</p>
                                        <a href="subject_groups.php" class="btn btn-outline-success">সংযোগ পৃষ্ঠায় যান</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-book-open fa-3x text-info mb-3"></i>
                                        <h5>বিষয় ব্যবস্থাপনা</h5>
                                        <p>বিষয় যোগ করুন, সম্পাদনা করুন এবং ব্যবস্থাপনা করুন</p>
                                        <a href="subjects.php" class="btn btn-outline-info">বিষয় পৃষ্ঠায় যান</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
