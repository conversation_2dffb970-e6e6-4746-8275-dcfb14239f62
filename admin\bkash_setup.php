<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';
require_once '../includes/functions.php';

// Initialize variables
$success_message = $error_message = '';
$bkash_app_key = $bkash_app_secret = $bkash_username = $bkash_password = '';
$bkash_sandbox = true;
$bkash_success_url = 'http://localhost/zfaw/admin/bkash_success.php';
$bkash_failed_url = 'http://localhost/zfaw/admin/bkash_failed.php';

// Get current configuration
$config_file = '../includes/bkash_config.php';
if (file_exists($config_file)) {
    include_once $config_file;
    
    // Extract current values from constants
    $bkash_app_key = defined('BKASH_APP_KEY') ? BKASH_APP_KEY : '';
    $bkash_app_secret = defined('BKASH_APP_SECRET') ? BKASH_APP_SECRET : '';
    $bkash_username = defined('BKASH_USERNAME') ? BKASH_USERNAME : '';
    $bkash_password = defined('BKASH_PASSWORD') ? BKASH_PASSWORD : '';
    $bkash_sandbox = defined('BKASH_SANDBOX') ? BKASH_SANDBOX : true;
    $bkash_success_url = defined('BKASH_SUCCESS_URL') ? BKASH_SUCCESS_URL : 'http://localhost/zfaw/admin/bkash_success.php';
    $bkash_failed_url = defined('BKASH_FAILED_URL') ? BKASH_FAILED_URL : 'http://localhost/zfaw/admin/bkash_failed.php';
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_bkash_config'])) {
    // Get form data
    $bkash_app_key = trim($_POST['bkash_app_key']);
    $bkash_app_secret = trim($_POST['bkash_app_secret']);
    $bkash_username = trim($_POST['bkash_username']);
    $bkash_password = trim($_POST['bkash_password']);
    $bkash_sandbox = isset($_POST['bkash_sandbox']) ? true : false;
    $bkash_success_url = trim($_POST['bkash_success_url']);
    $bkash_failed_url = trim($_POST['bkash_failed_url']);
    
    // Validate required fields
    if (empty($bkash_app_key) || empty($bkash_app_secret) || empty($bkash_username) || empty($bkash_password)) {
        $error_message = "সব প্রয়োজনীয় ফিল্ড পূরণ করুন।";
    } else {
        // Create configuration content
        $config_content = <<<EOT
<?php
/**
 * bKash Payment Gateway Configuration
 * 
 * This file contains the configuration settings for the bKash payment gateway integration.
 * You need to obtain these credentials from bKash when you register as a merchant.
 */

// bKash API Configuration
define('BKASH_SANDBOX', {$bkash_sandbox} ? true : false); // Set to false for production
define('BKASH_VERSION', 'v1.2.0-beta');
define('BKASH_APP_KEY', '{$bkash_app_key}'); // Replace with your actual app key
define('BKASH_APP_SECRET', '{$bkash_app_secret}'); // Replace with your actual app secret
define('BKASH_USERNAME', '{$bkash_username}'); // Replace with your actual username
define('BKASH_PASSWORD', '{$bkash_password}'); // Replace with your actual password

// bKash Script URLs
define('BKASH_SCRIPT_URL_SANDBOX', 'https://scripts.sandbox.bka.sh/versions/1.2.0-beta/checkout/bKash-checkout-sandbox.js');
define('BKASH_SCRIPT_URL_PRODUCTION', 'https://scripts.pay.bka.sh/versions/1.2.0-beta/checkout/bKash-checkout.js');

// bKash API Endpoints
define('BKASH_GRANT_TOKEN_URL', 'https://checkout.sandbox.bka.sh/v1.2.0-beta/checkout/token/grant');
define('BKASH_CREATE_PAYMENT_URL', 'https://checkout.sandbox.bka.sh/v1.2.0-beta/checkout/payment/create');
define('BKASH_EXECUTE_PAYMENT_URL', 'https://checkout.sandbox.bka.sh/v1.2.0-beta/checkout/payment/execute');
define('BKASH_QUERY_PAYMENT_URL', 'https://checkout.sandbox.bka.sh/v1.2.0-beta/checkout/payment/query');

// Callback URLs
define('BKASH_SUCCESS_URL', '{$bkash_success_url}');
define('BKASH_FAILED_URL', '{$bkash_failed_url}');

/**
 * Function to get the appropriate bKash script URL based on environment
 */
function getBkashScriptUrl() {
    return BKASH_SANDBOX ? BKASH_SCRIPT_URL_SANDBOX : BKASH_SCRIPT_URL_PRODUCTION;
}

/**
 * Function to get bKash API headers with authorization token
 */
function getBkashHeaders(\$token = null) {
    \$headers = [
        'Content-Type: application/json',
        'Accept: application/json',
        'username: ' . BKASH_USERNAME,
        'password: ' . BKASH_PASSWORD
    ];
    
    if (\$token) {
        \$headers[] = 'authorization: ' . \$token;
        \$headers[] = 'x-app-key: ' . BKASH_APP_KEY;
    }
    
    return \$headers;
}
EOT;

        // Save configuration to file
        if (file_put_contents($config_file, $config_content)) {
            $success_message = "bKash কনফিগারেশন সফলভাবে সংরক্ষণ করা হয়েছে।";
        } else {
            $error_message = "কনফিগারেশন সংরক্ষণ করতে সমস্যা হয়েছে। ফাইল রাইট পারমিশন চেক করুন।";
        }
    }
}

// Set current page for sidebar highlighting
$currentPage = 'bkash_setup.php';
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>bKash সেটআপ - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    
    <style>
        .config-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .form-label {
            font-weight: 500;
        }
        .required-field::after {
            content: "*";
            color: red;
            margin-left: 4px;
        }
        .api-key-field {
            font-family: monospace;
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>bKash পেমেন্ট গেটওয়ে সেটআপ</h2>
                        <p class="text-muted">bKash পেমেন্ট গেটওয়ে ইন্টিগ্রেশনের জন্য API কনফিগারেশন সেটিংস</p>
                    </div>
                    <div class="col-auto">
                        <a href="bkash_dashboard.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>bKash ড্যাশবোর্ডে ফিরে যান
                        </a>
                    </div>
                </div>
                
                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <div class="card config-card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cog me-2"></i>bKash API কনফিগারেশন
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="">
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="bkash_app_key" class="form-label required-field">App Key</label>
                                        <input type="text" class="form-control api-key-field" id="bkash_app_key" name="bkash_app_key" value="<?php echo htmlspecialchars($bkash_app_key); ?>" required>
                                        <div class="form-text">bKash মার্চেন্ট অ্যাকাউন্ট থেকে প্রাপ্ত App Key</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="bkash_app_secret" class="form-label required-field">App Secret</label>
                                        <input type="text" class="form-control api-key-field" id="bkash_app_secret" name="bkash_app_secret" value="<?php echo htmlspecialchars($bkash_app_secret); ?>" required>
                                        <div class="form-text">bKash মার্চেন্ট অ্যাকাউন্ট থেকে প্রাপ্ত App Secret</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="bkash_username" class="form-label required-field">Username</label>
                                        <input type="text" class="form-control" id="bkash_username" name="bkash_username" value="<?php echo htmlspecialchars($bkash_username); ?>" required>
                                        <div class="form-text">bKash মার্চেন্ট অ্যাকাউন্ট থেকে প্রাপ্ত Username</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="bkash_password" class="form-label required-field">Password</label>
                                        <input type="password" class="form-control" id="bkash_password" name="bkash_password" value="<?php echo htmlspecialchars($bkash_password); ?>" required>
                                        <div class="form-text">bKash মার্চেন্ট অ্যাকাউন্ট থেকে প্রাপ্ত Password</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="bkash_success_url" class="form-label">Success URL</label>
                                        <input type="text" class="form-control" id="bkash_success_url" name="bkash_success_url" value="<?php echo htmlspecialchars($bkash_success_url); ?>">
                                        <div class="form-text">পেমেন্ট সফল হলে রিডাইরেক্ট করার URL</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="bkash_failed_url" class="form-label">Failed URL</label>
                                        <input type="text" class="form-control" id="bkash_failed_url" name="bkash_failed_url" value="<?php echo htmlspecialchars($bkash_failed_url); ?>">
                                        <div class="form-text">পেমেন্ট ব্যর্থ হলে রিডাইরেক্ট করার URL</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="bkash_sandbox" name="bkash_sandbox" <?php echo $bkash_sandbox ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="bkash_sandbox">স্যান্ডবক্স মোড (টেস্টিং)</label>
                                </div>
                                <div class="form-text">টেস্টিং এর জন্য স্যান্ডবক্স মোড চালু রাখুন। প্রোডাকশনে যাওয়ার সময় এটি বন্ধ করুন।</div>
                            </div>
                            
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>নোট:</strong> bKash পেমেন্ট গেটওয়ে ব্যবহার করতে আপনাকে অবশ্যই bKash মার্চেন্ট অ্যাকাউন্ট খুলতে হবে এবং তাদের কাছ থেকে API ক্রেডেনশিয়ালস সংগ্রহ করতে হবে।
                            </div>
                            
                            <div class="text-end">
                                <button type="submit" name="save_bkash_config" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>কনফিগারেশন সংরক্ষণ করুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="card config-card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-question-circle me-2"></i>bKash ইন্টিগ্রেশন সম্পর্কে সাহায্য
                        </h5>
                    </div>
                    <div class="card-body">
                        <h6>bKash পেমেন্ট গেটওয়ে ইন্টিগ্রেশন করার জন্য নিম্নলিখিত পদক্ষেপগুলি অনুসরণ করুন:</h6>
                        <ol>
                            <li>bKash মার্চেন্ট অ্যাকাউন্ট খুলুন</li>
                            <li>bKash ডেভেলপার পোর্টালে রেজিস্টার করুন</li>
                            <li>API ক্রেডেনশিয়ালস সংগ্রহ করুন</li>
                            <li>উপরের ফর্মে API ক্রেডেনশিয়ালস যোগ করুন</li>
                            <li>টেস্টিং এর জন্য স্যান্ডবক্স মোড ব্যবহার করুন</li>
                            <li>সবকিছু ঠিক থাকলে প্রোডাকশন মোডে স্যুইচ করুন</li>
                        </ol>
                        
                        <div class="mt-3">
                            <a href="https://developer.bka.sh/docs/checkout-overview" target="_blank" class="btn btn-outline-info">
                                <i class="fas fa-external-link-alt me-2"></i>bKash ডেভেলপার ডকুমেন্টেশন
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle password visibility
        document.addEventListener('DOMContentLoaded', function() {
            const passwordField = document.getElementById('bkash_password');
            const togglePassword = document.createElement('button');
            togglePassword.type = 'button';
            togglePassword.className = 'btn btn-outline-secondary position-absolute end-0 top-0 h-100';
            togglePassword.innerHTML = '<i class="fas fa-eye"></i>';
            togglePassword.style.borderTopLeftRadius = '0';
            togglePassword.style.borderBottomLeftRadius = '0';
            
            passwordField.parentElement.style.position = 'relative';
            passwordField.parentElement.appendChild(togglePassword);
            
            togglePassword.addEventListener('click', function() {
                const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordField.setAttribute('type', type);
                this.innerHTML = type === 'password' ? '<i class="fas fa-eye"></i>' : '<i class="fas fa-eye-slash"></i>';
            });
        });
    </script>
</body>
</html>
