<?php
// Include database connection
require_once "includes/dbh.inc.php";

// Get latest notice
$notice_text = "বর্তমানে কোন নোটিশ নেই।";
try {
    // Check if notices table exists
    $latest_notice_query = "SHOW TABLES LIKE 'notices'";
    $latest_notice_result = $conn->query($latest_notice_query);

    if ($latest_notice_result && $latest_notice_result->num_rows > 0) {
        // Get latest notice
        $sql = "SELECT * FROM notices ORDER BY date DESC LIMIT 1";
        $result = $conn->query($sql);

        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $notice_text = htmlspecialchars($row['title']) . ' - ' . htmlspecialchars(substr($row['content'], 0, 150)) .
                 (strlen($row['content']) > 150 ? '...' : '');
        }
    }
} catch (Exception $e) {
    $notice_text = "নোটিশ লোড করতে সমস্যা হচ্ছে। পরে আবার চেষ্টা করুন।";
    error_log('Notice Error: ' . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>মাঝখানে স্ক্রোলিং নোটিশ</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
        }

        .notice-container {
            width: 100%;
            height: 100%;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-top: 1px solid #dee2e6;
            border-bottom: 1px solid #dee2e6;
            padding-left: 120px; /* আপডেট খবর লেবেলের জন্য স্পেস */
        }

        .notice-content {
            white-space: nowrap;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            animation: scrollText 30s linear infinite;
            font-family: Arial, sans-serif;
            font-size: 16px;
            font-weight: 600;
            color: #333;
            padding: 0;
            line-height: 1.5;
        }

        @keyframes scrollText {
            0% { transform: translate(100%, -50%); }
            100% { transform: translate(-100%, -50%); }
        }

        /* Make icons more visible */
        .notice-content i {
            color: #007bff;
            margin-right: 5px;
        }

        /* Make strong text more visible */
        .notice-content strong {
            color: #212529;
            font-weight: 700;
        }

        /* আপডেট খবর লেবেল স্টাইল */
        .update-label {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            background-color: #007bff;
            color: white;
            padding: 0 15px;
            font-weight: bold;
            font-size: 16px;
            z-index: 10;
            border-right: 3px solid #0056b3;
            box-shadow: 2px 0 5px rgba(0,0,0,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100px;
            text-align: center;
        }

        /* লেবেলে আইকন যুক্ত করা */
        .update-label:before {
            content: '\f0a1'; /* মেগাফোন আইকন */
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            margin-right: 8px;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="notice-container">
        <!-- আপডেট খবর লেবেল -->
        <div class="update-label">আপডেট<br>খবর</div>

        <div class="notice-content" id="scrolling-text">
            <i class="fas fa-bullhorn"></i> <strong>সর্বশেষ নোটিশ:</strong>
            <?php echo $notice_text; ?>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <i class="fas fa-calendar-alt"></i> <strong>আজকের তারিখ:</strong> <?php echo date('d/m/Y'); ?>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <i class="fas fa-graduation-cap"></i> <strong>ভর্তি চলছে:</strong> নতুন শিক্ষার্থীদের জন্য ভর্তি চলছে। আজই যোগাযোগ করুন।
        </div>
    </div>

    <script>
        // Force animation to start properly
        document.addEventListener('DOMContentLoaded', function() {
            var scrollingText = document.getElementById('scrolling-text');
            if (scrollingText) {
                // Force redraw
                scrollingText.style.display = 'none';
                setTimeout(function() {
                    scrollingText.style.display = 'block';
                }, 10);

                // Restart animation
                scrollingText.style.animationName = 'none';
                setTimeout(function() {
                    scrollingText.style.animationName = 'scrollText';
                }, 50);
            }
        });
    </script>
</body>
</html>
