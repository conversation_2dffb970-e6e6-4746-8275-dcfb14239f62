<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Include database connection
require_once '../includes/dbh.inc.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Get student ID from URL
$studentId = isset($_GET['id']) ? $_GET['id'] : '';

if (empty($studentId)) {
    echo "Student ID is required";
    exit;
}

// Get student data
$studentQuery = "SELECT s.*, d.department_name
                 FROM students s
                 LEFT JOIN departments d ON s.department_id = d.id
                 WHERE s.student_id = ?";
$stmt = $conn->prepare($studentQuery);
$stmt->bind_param("s", $studentId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo "Student not found";
    exit;
}

$student = $result->fetch_assoc();
$studentDbId = $student['id'];
$studentName = $student['first_name'] . ' ' . $student['last_name'];
$departmentName = $student['department_name'];

// Get current session
$currentSessionQuery = "SELECT id, session_name FROM sessions ORDER BY id DESC LIMIT 1";
$currentSessionResult = $conn->query($currentSessionQuery);

if (!$currentSessionResult || $currentSessionResult->num_rows === 0) {
    echo "No active session found";
    exit;
}

$currentSession = $currentSessionResult->fetch_assoc();

// Get subjects for the student's department/group
$departmentId = $student['department_id'];
$groupId = null;

// Find the corresponding group ID for the student's department
if ($departmentName) {
    $groupQuery = "SELECT id FROM groups WHERE group_name = ?";
    $stmt = $conn->prepare($groupQuery);
    $stmt->bind_param("s", $departmentName);
    $stmt->execute();
    $groupResult = $stmt->get_result();
    
    if ($groupResult->num_rows > 0) {
        $groupId = $groupResult->fetch_assoc()['id'];
    }
}

// If no group found, use department ID as fallback
if (!$groupId) {
    $groupId = $departmentId;
}

// Get required subjects
$requiredSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code
                         FROM subjects s
                         JOIN subject_groups sg ON s.id = sg.subject_id
                         WHERE sg.group_id = ? AND s.is_active = 1
                         AND sg.subject_type = 'required' AND sg.is_applicable = 1
                         GROUP BY s.id
                         ORDER BY s.subject_name";
$stmt = $conn->prepare($requiredSubjectsQuery);
$stmt->bind_param("i", $groupId);
$stmt->execute();
$requiredSubjects = $stmt->get_result();

// Get optional subjects
$optionalSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code
                         FROM subjects s
                         JOIN subject_groups sg ON s.id = sg.subject_id
                         WHERE sg.group_id = ? AND s.is_active = 1
                         AND sg.subject_type = 'optional' AND sg.is_applicable = 1
                         GROUP BY s.id
                         ORDER BY s.subject_name";
$stmt = $conn->prepare($optionalSubjectsQuery);
$stmt->bind_param("i", $groupId);
$stmt->execute();
$optionalSubjects = $stmt->get_result();

// Get fourth subjects
$fourthSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code
                       FROM subjects s
                       JOIN subject_groups sg ON s.id = sg.subject_id
                       WHERE sg.group_id = ? AND s.is_active = 1
                       AND sg.subject_type = 'fourth' AND sg.is_applicable = 1
                       GROUP BY s.id
                       ORDER BY s.subject_name";
$stmt = $conn->prepare($fourthSubjectsQuery);
$stmt->bind_param("i", $groupId);
$stmt->execute();
$fourthSubjects = $stmt->get_result();

// Create student_subjects table if it doesn't exist
$createTableQuery = "CREATE TABLE IF NOT EXISTS student_subjects (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    subject_id INT(11) NOT NULL,
    category VARCHAR(20) NOT NULL DEFAULT 'optional',
    session_id INT(11) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if (!$conn->query($createTableQuery)) {
    echo "Error creating table: " . $conn->error;
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get selected subjects
    $selectedSubjects = isset($_POST['subjects']) ? $_POST['subjects'] : [];
    
    if (empty($selectedSubjects)) {
        $error = "Please select at least one subject";
    } else {
        // Delete existing selections
        $deleteQuery = "DELETE FROM student_subjects WHERE student_id = ?";
        $stmt = $conn->prepare($deleteQuery);
        $stmt->bind_param("i", $studentDbId);
        
        if (!$stmt->execute()) {
            $error = "Error deleting existing selections: " . $stmt->error;
        } else {
            // Insert new selections
            $insertQuery = "INSERT INTO student_subjects (student_id, subject_id, category, session_id) VALUES (?, ?, ?, ?)";
            $stmt = $conn->prepare($insertQuery);
            
            if (!$stmt) {
                $error = "Error preparing insert statement: " . $conn->error;
            } else {
                $success = true;
                
                foreach ($selectedSubjects as $subject) {
                    list($subjectId, $category) = explode('|', $subject);
                    
                    $stmt->bind_param("iisi", $studentDbId, $subjectId, $category, $currentSession['id']);
                    
                    if (!$stmt->execute()) {
                        $error = "Error inserting subject: " . $stmt->error;
                        $success = false;
                        break;
                    }
                }
                
                if ($success) {
                    $message = "Subjects saved successfully!";
                }
            }
        }
    }
}

// Get currently selected subjects
$selectedSubjectsQuery = "SELECT subject_id, category FROM student_subjects WHERE student_id = ?";
$stmt = $conn->prepare($selectedSubjectsQuery);
$stmt->bind_param("i", $studentDbId);
$stmt->execute();
$selectedSubjectsResult = $stmt->get_result();

$selectedSubjectIds = [];
while ($row = $selectedSubjectsResult->fetch_assoc()) {
    $selectedSubjectIds[$row['subject_id']] = $row['category'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual Subject Selection</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .subject-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
        }
        .subject-card.selected {
            background-color: #e8f5e9;
            border-color: #4caf50;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1>Manual Subject Selection</h1>
        
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Student Information</h5>
            </div>
            <div class="card-body">
                <p><strong>Student ID:</strong> <?php echo $studentId; ?></p>
                <p><strong>Name:</strong> <?php echo $studentName; ?></p>
                <p><strong>Department:</strong> <?php echo $departmentName; ?></p>
            </div>
        </div>
        
        <?php if (isset($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if (isset($message)): ?>
            <div class="alert alert-success"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="row">
                <div class="col-md-4">
                    <div class="card mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">Required Subjects</h5>
                        </div>
                        <div class="card-body">
                            <?php if ($requiredSubjects && $requiredSubjects->num_rows > 0): ?>
                                <?php while ($subject = $requiredSubjects->fetch_assoc()): ?>
                                    <div class="subject-card <?php echo isset($selectedSubjectIds[$subject['id']]) && $selectedSubjectIds[$subject['id']] === 'required' ? 'selected' : ''; ?>">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="subjects[]" value="<?php echo $subject['id']; ?>|required" id="req<?php echo $subject['id']; ?>" <?php echo isset($selectedSubjectIds[$subject['id']]) && $selectedSubjectIds[$subject['id']] === 'required' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="req<?php echo $subject['id']; ?>">
                                                <?php echo $subject['subject_name']; ?> (<?php echo $subject['subject_code']; ?>)
                                            </label>
                                        </div>
                                    </div>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <p>No required subjects found.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">Optional Subjects</h5>
                        </div>
                        <div class="card-body">
                            <?php if ($optionalSubjects && $optionalSubjects->num_rows > 0): ?>
                                <?php while ($subject = $optionalSubjects->fetch_assoc()): ?>
                                    <div class="subject-card <?php echo isset($selectedSubjectIds[$subject['id']]) && $selectedSubjectIds[$subject['id']] === 'optional' ? 'selected' : ''; ?>">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="subjects[]" value="<?php echo $subject['id']; ?>|optional" id="opt<?php echo $subject['id']; ?>" <?php echo isset($selectedSubjectIds[$subject['id']]) && $selectedSubjectIds[$subject['id']] === 'optional' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="opt<?php echo $subject['id']; ?>">
                                                <?php echo $subject['subject_name']; ?> (<?php echo $subject['subject_code']; ?>)
                                            </label>
                                        </div>
                                    </div>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <p>No optional subjects found.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">Fourth Subjects</h5>
                        </div>
                        <div class="card-body">
                            <?php if ($fourthSubjects && $fourthSubjects->num_rows > 0): ?>
                                <?php while ($subject = $fourthSubjects->fetch_assoc()): ?>
                                    <div class="subject-card <?php echo isset($selectedSubjectIds[$subject['id']]) && $selectedSubjectIds[$subject['id']] === 'fourth' ? 'selected' : ''; ?>">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="subjects[]" value="<?php echo $subject['id']; ?>|fourth" id="fourth<?php echo $subject['id']; ?>" <?php echo isset($selectedSubjectIds[$subject['id']]) && $selectedSubjectIds[$subject['id']] === 'fourth' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="fourth<?php echo $subject['id']; ?>">
                                                <?php echo $subject['subject_name']; ?> (<?php echo $subject['subject_code']; ?>)
                                            </label>
                                        </div>
                                    </div>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <p>No fourth subjects found.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end mb-4">
                <button type="submit" class="btn btn-primary">Save Subject Selection</button>
                <a href="student_subject_selection.php?id=<?php echo $studentId; ?>" class="btn btn-secondary">Back to Regular Page</a>
            </div>
        </form>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle selected class on subject cards
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(function(checkbox) {
                checkbox.addEventListener('change', function() {
                    const card = this.closest('.subject-card');
                    if (this.checked) {
                        card.classList.add('selected');
                    } else {
                        card.classList.remove('selected');
                    }
                });
            });
        });
    </script>
</body>
</html>
