<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get parameters
$class_id = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;
$month = isset($_GET['month']) ? $_GET['month'] : date('Y-m');
$subject_id = isset($_GET['subject_id']) ? intval($_GET['subject_id']) : 0;

// Get classes for dropdown
$classesQuery = "SELECT id, class_name FROM classes ORDER BY class_name";
$classesResult = $conn->query($classesQuery);
$classes = [];
if ($classesResult && $classesResult->num_rows > 0) {
    while ($row = $classesResult->fetch_assoc()) {
        $classes[$row['id']] = $row['class_name'];
    }
}

// Get subjects for dropdown
$subjectsQuery = "SELECT id, subject_name FROM subjects ORDER BY subject_name";
$subjectsResult = $conn->query($subjectsQuery);
$subjects = [];
if ($subjectsResult && $subjectsResult->num_rows > 0) {
    while ($row = $subjectsResult->fetch_assoc()) {
        $subjects[$row['id']] = $row['subject_name'];
    }
}

// Get attendance data if class is selected
$attendance_data = [];
$students = [];
$dates = [];

if ($class_id > 0) {
    // Get students for this class
    $studentsQuery = "SELECT id, student_id as student_code, first_name, last_name, roll_number 
                     FROM students WHERE class_id = ? ORDER BY roll_number, first_name";
    $stmt = $conn->prepare($studentsQuery);
    $stmt->bind_param("i", $class_id);
    $stmt->execute();
    $studentsResult = $stmt->get_result();
    
    while ($row = $studentsResult->fetch_assoc()) {
        $students[$row['id']] = $row;
    }
    
    // Get attendance records for the month
    $start_date = $month . '-01';
    $end_date = date('Y-m-t', strtotime($start_date));
    
    if ($subject_id > 0) {
        $attendanceQuery = "SELECT student_id, date, status 
                           FROM attendance 
                           WHERE class_id = ? AND subject_id = ? AND date BETWEEN ? AND ?
                           ORDER BY date, student_id";
        $stmt = $conn->prepare($attendanceQuery);
        $stmt->bind_param("iiss", $class_id, $subject_id, $start_date, $end_date);
    } else {
        $attendanceQuery = "SELECT student_id, date, status 
                           FROM attendance 
                           WHERE class_id = ? AND date BETWEEN ? AND ?
                           ORDER BY date, student_id";
        $stmt = $conn->prepare($attendanceQuery);
        $stmt->bind_param("iss", $class_id, $start_date, $end_date);
    }
    
    $stmt->execute();
    $attendanceResult = $stmt->get_result();
    
    while ($row = $attendanceResult->fetch_assoc()) {
        $attendance_data[$row['student_id']][$row['date']] = $row['status'];
        if (!in_array($row['date'], $dates)) {
            $dates[] = $row['date'];
        }
    }
    
    sort($dates);
}

// Calculate statistics
$stats = [];
if (!empty($students) && !empty($dates)) {
    foreach ($students as $student_id => $student) {
        $present = 0;
        $absent = 0;
        $late = 0;
        $excused = 0;
        $total_days = count($dates);
        
        foreach ($dates as $date) {
            $status = isset($attendance_data[$student_id][$date]) ? $attendance_data[$student_id][$date] : 'absent';
            switch ($status) {
                case 'present':
                    $present++;
                    break;
                case 'absent':
                    $absent++;
                    break;
                case 'late':
                    $late++;
                    break;
                case 'excused':
                    $excused++;
                    break;
            }
        }
        
        $attendance_percentage = $total_days > 0 ? round(($present + $late) / $total_days * 100, 2) : 0;
        
        $stats[$student_id] = [
            'present' => $present,
            'absent' => $absent,
            'late' => $late,
            'excused' => $excused,
            'total_days' => $total_days,
            'percentage' => $attendance_percentage
        ];
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>মাসিক উপস্থিতি রিপোর্ট - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .status-present { background-color: #d4edda; color: #155724; }
        .status-absent { background-color: #f8d7da; color: #721c24; }
        .status-late { background-color: #fff3cd; color: #856404; }
        .status-excused { background-color: #d1ecf1; color: #0c5460; }
        .attendance-cell {
            text-align: center;
            font-weight: bold;
            font-size: 12px;
            padding: 2px !important;
        }
        .date-header {
            writing-mode: vertical-rl;
            text-orientation: mixed;
            font-size: 10px;
            padding: 5px 2px !important;
        }
        .stats-good { color: #28a745; }
        .stats-warning { color: #ffc107; }
        .stats-danger { color: #dc3545; }
        
        @media print {
            .no-print { display: none !important; }
            .table { font-size: 10px; }
            .attendance-cell { font-size: 8px; }
            .date-header { font-size: 8px; }
        }
    </style>
    <link rel="stylesheet" href="css/hind-siliguri.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include('includes/sidebar.php'); ?>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">মাসিক উপস্থিতি রিপোর্ট</h1>
                    <div class="no-print">
                        <button onclick="window.print()" class="btn btn-primary">
                            <i class="fas fa-print me-1"></i> প্রিন্ট করুন
                        </button>
                    </div>
                </div>
                
                <!-- Filter Form -->
                <div class="card mb-4 no-print">
                    <div class="card-header">
                        <h5><i class="fas fa-filter me-2"></i>রিপোর্ট ফিল্টার</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="" class="row g-3">
                            <div class="col-md-4">
                                <label for="class_id" class="form-label">ক্লাস</label>
                                <select class="form-select" id="class_id" name="class_id" required>
                                    <option value="">ক্লাস নির্বাচন করুন</option>
                                    <?php foreach ($classes as $id => $name): ?>
                                    <option value="<?php echo $id; ?>" <?php echo ($class_id == $id) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($name); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="subject_id" class="form-label">বিষয় (ঐচ্ছিক)</label>
                                <select class="form-select" id="subject_id" name="subject_id">
                                    <option value="0">সকল বিষয়</option>
                                    <?php foreach ($subjects as $id => $name): ?>
                                    <option value="<?php echo $id; ?>" <?php echo ($subject_id == $id) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($name); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="month" class="form-label">মাস</label>
                                <input type="month" class="form-control" id="month" name="month" value="<?php echo $month; ?>" required>
                            </div>
                            
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i> রিপোর্ট দেখুন
                                </button>
                                <a href="attendance.php" class="btn btn-secondary ms-2">
                                    <i class="fas fa-arrow-left me-1"></i> ফিরে যান
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <?php if ($class_id > 0 && !empty($students)): ?>
                <!-- Report Header -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4>রিপোর্ট বিবরণ</h4>
                                <p><strong>ক্লাস:</strong> <?php echo htmlspecialchars($classes[$class_id]); ?></p>
                                <?php if ($subject_id > 0): ?>
                                <p><strong>বিষয়:</strong> <?php echo htmlspecialchars($subjects[$subject_id]); ?></p>
                                <?php endif; ?>
                                <p><strong>মাস:</strong> <?php echo date('F Y', strtotime($month . '-01')); ?></p>
                                <p><strong>মোট শিক্ষার্থী:</strong> <?php echo count($students); ?></p>
                                <p><strong>মোট দিন:</strong> <?php echo count($dates); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Statistics Summary -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-pie me-2"></i>পরিসংখ্যান সারাংশ</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>রোল</th>
                                        <th>নাম</th>
                                        <th>উপস্থিত</th>
                                        <th>অনুপস্থিত</th>
                                        <th>দেরি</th>
                                        <th>ছুটি</th>
                                        <th>মোট দিন</th>
                                        <th>উপস্থিতির হার</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($students as $student_id => $student): 
                                        $stat = $stats[$student_id];
                                        $percentage_class = '';
                                        if ($stat['percentage'] >= 80) $percentage_class = 'stats-good';
                                        elseif ($stat['percentage'] >= 60) $percentage_class = 'stats-warning';
                                        else $percentage_class = 'stats-danger';
                                    ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($student['roll_number'] ?? $student['student_code']); ?></td>
                                        <td><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></td>
                                        <td class="text-center"><?php echo $stat['present']; ?></td>
                                        <td class="text-center"><?php echo $stat['absent']; ?></td>
                                        <td class="text-center"><?php echo $stat['late']; ?></td>
                                        <td class="text-center"><?php echo $stat['excused']; ?></td>
                                        <td class="text-center"><?php echo $stat['total_days']; ?></td>
                                        <td class="text-center <?php echo $percentage_class; ?>">
                                            <strong><?php echo $stat['percentage']; ?>%</strong>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <?php if (!empty($dates)): ?>
                <!-- Daily Attendance Grid -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-calendar-alt me-2"></i>দৈনিক উপস্থিতি গ্রিড</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-sm">
                                <thead class="table-light">
                                    <tr>
                                        <th rowspan="2">রোল</th>
                                        <th rowspan="2">নাম</th>
                                        <?php foreach ($dates as $date): ?>
                                        <th class="date-header"><?php echo date('d', strtotime($date)); ?></th>
                                        <?php endforeach; ?>
                                    </tr>
                                    <tr>
                                        <?php foreach ($dates as $date): ?>
                                        <th class="date-header"><?php echo date('D', strtotime($date)); ?></th>
                                        <?php endforeach; ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($students as $student_id => $student): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($student['roll_number'] ?? $student['student_code']); ?></td>
                                        <td><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></td>
                                        <?php foreach ($dates as $date): 
                                            $status = isset($attendance_data[$student_id][$date]) ? $attendance_data[$student_id][$date] : 'absent';
                                            $status_symbol = '';
                                            $status_class = '';
                                            switch ($status) {
                                                case 'present':
                                                    $status_symbol = 'P';
                                                    $status_class = 'status-present';
                                                    break;
                                                case 'absent':
                                                    $status_symbol = 'A';
                                                    $status_class = 'status-absent';
                                                    break;
                                                case 'late':
                                                    $status_symbol = 'L';
                                                    $status_class = 'status-late';
                                                    break;
                                                case 'excused':
                                                    $status_symbol = 'E';
                                                    $status_class = 'status-excused';
                                                    break;
                                            }
                                        ?>
                                        <td class="attendance-cell <?php echo $status_class; ?>"><?php echo $status_symbol; ?></td>
                                        <?php endforeach; ?>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <strong>চিহ্ন:</strong> 
                                P = উপস্থিত, A = অনুপস্থিত, L = দেরি, E = ছুটি
                            </small>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php elseif ($class_id > 0): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> নির্বাচিত ক্লাসে কোন শিক্ষার্থী পাওয়া যায়নি।
                </div>
                <?php else: ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i> রিপোর্ট দেখতে একটি ক্লাস নির্বাচন করুন।
                </div>
                <?php endif; ?>
                
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
