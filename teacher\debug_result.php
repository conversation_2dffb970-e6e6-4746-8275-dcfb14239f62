<?php
session_start();

// Check if user is logged in and is a teacher
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'teacher') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if result ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo "No result ID provided";
    exit();
}

$resultId = intval($_GET['id']);
echo "<h2>Debugging Result ID: $resultId</h2>";

// Get result information
try {
    // First, check if the result exists
    $checkQuery = "SELECT * FROM results WHERE id = ?";
    $stmt = $conn->prepare($checkQuery);
    $stmt->bind_param("i", $resultId);
    $stmt->execute();
    $checkResult = $stmt->get_result();
    
    if ($checkResult->num_rows === 0) {
        echo "<p>Result not found in database</p>";
        exit();
    }
    
    $resultData = $checkResult->fetch_assoc();
    echo "<h3>Basic Result Data:</h3>";
    echo "<pre>";
    print_r($resultData);
    echo "</pre>";
    
    // Check if exams table exists
    $checkExamsTableQuery = "SHOW TABLES LIKE 'exams'";
    $examsTableResult = $conn->query($checkExamsTableQuery);
    if ($examsTableResult->num_rows === 0) {
        echo "<p>Exams table does not exist</p>";
    } else {
        // Check if the exam exists
        $examId = $resultData['exam_id'];
        $examQuery = "SELECT * FROM exams WHERE id = ?";
        $stmt = $conn->prepare($examQuery);
        $stmt->bind_param("i", $examId);
        $stmt->execute();
        $examResult = $stmt->get_result();
        
        if ($examResult->num_rows === 0) {
            echo "<p>Exam with ID $examId not found</p>";
        } else {
            $examData = $examResult->fetch_assoc();
            echo "<h3>Exam Data:</h3>";
            echo "<pre>";
            print_r($examData);
            echo "</pre>";
        }
    }
    
    // Check if students table exists
    $checkStudentsTableQuery = "SHOW TABLES LIKE 'students'";
    $studentsTableResult = $conn->query($checkStudentsTableQuery);
    if ($studentsTableResult->num_rows === 0) {
        echo "<p>Students table does not exist</p>";
    } else {
        // Check if the student exists
        $studentId = $resultData['student_id'];
        $studentQuery = "SELECT * FROM students WHERE id = ?";
        $stmt = $conn->prepare($studentQuery);
        $stmt->bind_param("i", $studentId);
        $stmt->execute();
        $studentResult = $stmt->get_result();
        
        if ($studentResult->num_rows === 0) {
            echo "<p>Student with ID $studentId not found</p>";
        } else {
            $studentData = $studentResult->fetch_assoc();
            echo "<h3>Student Data:</h3>";
            echo "<pre>";
            print_r($studentData);
            echo "</pre>";
        }
    }
    
    // Check if subjects table exists
    $checkSubjectsTableQuery = "SHOW TABLES LIKE 'subjects'";
    $subjectsTableResult = $conn->query($checkSubjectsTableQuery);
    if ($subjectsTableResult->num_rows === 0) {
        echo "<p>Subjects table does not exist</p>";
    } else if (isset($resultData['subject_id']) && !empty($resultData['subject_id'])) {
        // Check if the subject exists
        $subjectId = $resultData['subject_id'];
        $subjectQuery = "SELECT * FROM subjects WHERE id = ?";
        $stmt = $conn->prepare($subjectQuery);
        $stmt->bind_param("i", $subjectId);
        $stmt->execute();
        $subjectResult = $stmt->get_result();
        
        if ($subjectResult->num_rows === 0) {
            echo "<p>Subject with ID $subjectId not found</p>";
        } else {
            $subjectData = $subjectResult->fetch_assoc();
            echo "<h3>Subject Data:</h3>";
            echo "<pre>";
            print_r($subjectData);
            echo "</pre>";
        }
    } else {
        echo "<p>No subject ID in result data</p>";
    }
    
    // Check if classes table exists
    $checkClassesTableQuery = "SHOW TABLES LIKE 'classes'";
    $classesTableResult = $conn->query($checkClassesTableQuery);
    if ($classesTableResult->num_rows === 0) {
        echo "<p>Classes table does not exist</p>";
    } else if (isset($studentData['class_id']) && !empty($studentData['class_id'])) {
        // Check if the class exists
        $classId = $studentData['class_id'];
        $classQuery = "SELECT * FROM classes WHERE id = ?";
        $stmt = $conn->prepare($classQuery);
        $stmt->bind_param("i", $classId);
        $stmt->execute();
        $classResult = $stmt->get_result();
        
        if ($classResult->num_rows === 0) {
            echo "<p>Class with ID $classId not found</p>";
        } else {
            $classData = $classResult->fetch_assoc();
            echo "<h3>Class Data:</h3>";
            echo "<pre>";
            print_r($classData);
            echo "</pre>";
        }
    } else {
        echo "<p>No class ID in student data</p>";
    }
    
    // Now try the full query
    $fullQuery = "SELECT r.*, e.exam_name, e.exam_type, e.exam_date, e.total_marks as exam_total_marks,
                 s.student_id as student_code, s.first_name, s.last_name, s.roll_number,
                 c.class_name, subj.subject_name, subj.subject_code
                FROM results r
                LEFT JOIN exams e ON r.exam_id = e.id
                LEFT JOIN students s ON r.student_id = s.id
                LEFT JOIN classes c ON s.class_id = c.id
                LEFT JOIN subjects subj ON r.subject_id = subj.id
                WHERE r.id = ?";
    $stmt = $conn->prepare($fullQuery);
    $stmt->bind_param("i", $resultId);
    $stmt->execute();
    $fullResult = $stmt->get_result();
    
    if ($fullResult->num_rows === 0) {
        echo "<p>Full query returned no results</p>";
    } else {
        $fullData = $fullResult->fetch_assoc();
        echo "<h3>Full Query Result:</h3>";
        echo "<pre>";
        print_r($fullData);
        echo "</pre>";
    }
    
} catch (Exception $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
