<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h2>PHP Error Checking</h2>";
echo "<p>PHP Version: " . phpversion() . "</p>";

// Try database connection
try {
    require_once '../includes/dbh.inc.php';
    echo "<p>✅ Database connection successful</p>";
    
    // Check if we can query the database
    $testQuery = "SELECT 1";
    $result = $conn->query($testQuery);
    if ($result) {
        echo "<p>✅ Database query successful</p>";
    } else {
        echo "<p>❌ Database query failed: " . $conn->error . "</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Database connection failed: " . $e->getMessage() . "</p>";
}

// Check for session
echo "<h3>Session Information</h3>";
session_start();
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Check for required extensions
echo "<h3>Required PHP Extensions</h3>";
$requiredExtensions = ['mysqli', 'json', 'session', 'mbstring'];
foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p>✅ Extension '$ext' is loaded</p>";
    } else {
        echo "<p>❌ Extension '$ext' is NOT loaded</p>";
    }
}
?>
