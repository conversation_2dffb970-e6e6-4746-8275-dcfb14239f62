<?php
session_start();
require_once '../includes/dbh.inc.php';
require_once '../includes/bkash_config.php';
require_once '../includes/bkash_functions.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Test token if requested
$tokenResponse = null;
$tokenError = null;

if (isset($_POST['test_token'])) {
    try {
        $tokenResponse = bkashGrantToken();
        
        if (!isset($tokenResponse['id_token'])) {
            $tokenError = 'টোকেন জেনারেট করতে সমস্যা হয়েছে: ' . ($tokenResponse['errorMessage'] ?? 'অজানা ত্রুটি');
        }
    } catch (Exception $e) {
        $tokenError = 'টোকেন টেস্ট করতে সমস্যা হয়েছে: ' . $e->getMessage();
    }
}

// Include header
include_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Include sidebar -->
        <?php include_once 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-key me-2"></i> বিকাশ টোকেন টেস্ট</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="bkash_dashboard.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-tachometer-alt me-1"></i> ড্যাশবোর্ড
                        </a>
                        <a href="bkash_test.php" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-cogs me-1"></i> বিকাশ সেটিংস
                        </a>
                    </div>
                </div>
            </div>
            
            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i> <?= $_SESSION['success'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>
            
            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i> <?= $_SESSION['error'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-key me-2"></i> বিকাশ টোকেন টেস্ট</h5>
                        </div>
                        <div class="card-body">
                            <p>বিকাশ API ব্যবহার করতে আপনাকে প্রথমে একটি টোকেন জেনারেট করতে হবে। এই টোকেন ব্যবহার করে আপনি বিকাশ API-এর সাথে যোগাযোগ করতে পারবেন।</p>
                            <p>নিচের বাটনে ক্লিক করে আপনি টোকেন জেনারেট করতে পারেন এবং দেখতে পারেন যে আপনার API ক্রেডেনশিয়ালস সঠিকভাবে কনফিগার করা আছে কিনা।</p>
                            
                            <form action="" method="POST" class="mt-4">
                                <div class="d-grid gap-2">
                                    <button type="submit" name="test_token" class="btn btn-primary">
                                        <i class="fas fa-key me-2"></i> টোকেন টেস্ট করুন
                                    </button>
                                </div>
                            </form>
                            
                            <?php if ($tokenResponse): ?>
                                <?php if (isset($tokenResponse['id_token'])): ?>
                                    <div class="alert alert-success mt-4">
                                        <h6><i class="fas fa-check-circle me-2"></i> টোকেন সফলভাবে জেনারেট করা হয়েছে!</h6>
                                        <p class="mb-0"><strong>টোকেন:</strong> <?= substr($tokenResponse['id_token'], 0, 30) ?>...</p>
                                        <p class="mb-0"><strong>মেয়াদ:</strong> <?= $tokenResponse['expires_in'] ?? 'N/A' ?> সেকেন্ড</p>
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-danger mt-4">
                                        <h6><i class="fas fa-exclamation-triangle me-2"></i> টোকেন জেনারেট করতে সমস্যা হয়েছে!</h6>
                                        <p class="mb-0"><strong>ত্রুটি:</strong> <?= $tokenResponse['errorMessage'] ?? 'অজানা ত্রুটি' ?></p>
                                    </div>
                                <?php endif; ?>
                            <?php elseif ($tokenError): ?>
                                <div class="alert alert-danger mt-4">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i> টোকেন টেস্ট করতে সমস্যা হয়েছে!</h6>
                                    <p class="mb-0"><strong>ত্রুটি:</strong> <?= $tokenError ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-cog me-2"></i> বিকাশ কনফিগারেশন</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-bordered">
                                <tr>
                                    <th width="40%">মোড</th>
                                    <td>
                                        <?php if (BKASH_SANDBOX): ?>
                                            <span class="badge bg-warning">স্যান্ডবক্স (টেস্ট)</span>
                                        <?php else: ?>
                                            <span class="badge bg-success">প্রোডাকশন (লাইভ)</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>API ভার্সন</th>
                                    <td><?= BKASH_VERSION ?></td>
                                </tr>
                                <tr>
                                    <th>APP KEY</th>
                                    <td>
                                        <?php if (BKASH_APP_KEY === 'your_app_key'): ?>
                                            <span class="badge bg-danger">কনফিগার করা হয়নি</span>
                                        <?php else: ?>
                                            <span class="badge bg-success">কনফিগার করা হয়েছে</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>APP SECRET</th>
                                    <td>
                                        <?php if (BKASH_APP_SECRET === 'your_app_secret'): ?>
                                            <span class="badge bg-danger">কনফিগার করা হয়নি</span>
                                        <?php else: ?>
                                            <span class="badge bg-success">কনফিগার করা হয়েছে</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>USERNAME</th>
                                    <td>
                                        <?php if (BKASH_USERNAME === 'your_username'): ?>
                                            <span class="badge bg-danger">কনফিগার করা হয়নি</span>
                                        <?php else: ?>
                                            <span class="badge bg-success">কনফিগার করা হয়েছে</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>PASSWORD</th>
                                    <td>
                                        <?php if (BKASH_PASSWORD === 'your_password'): ?>
                                            <span class="badge bg-danger">কনফিগার করা হয়নি</span>
                                        <?php else: ?>
                                            <span class="badge bg-success">কনফিগার করা হয়েছে</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                            
                            <div class="mt-3">
                                <a href="bkash_test.php" class="btn btn-primary">
                                    <i class="fas fa-cogs me-2"></i> কনফিগারেশন পরিবর্তন করুন
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-12">
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> বিকাশ টোকেন সম্পর্কে তথ্য</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-question-circle me-2"></i> বিকাশ টোকেন কি?</h6>
                                    <p>বিকাশ টোকেন হল একটি অস্থায়ী অনুমতিপত্র যা বিকাশ API-এর সাথে যোগাযোগ করতে ব্যবহৃত হয়। এটি আপনার API ক্রেডেনশিয়ালস ব্যবহার করে জেনারেট করা হয় এবং একটি নির্দিষ্ট সময়ের জন্য বৈধ থাকে।</p>
                                    <p>টোকেন ব্যবহার করে আপনি নিম্নলিখিত কাজগুলি করতে পারেন:</p>
                                    <ul>
                                        <li>পেমেন্ট তৈরি করা</li>
                                        <li>পেমেন্ট এক্সিকিউট করা</li>
                                        <li>পেমেন্ট স্ট্যাটাস চেক করা</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i> সমস্যা সমাধান</h6>
                                    <p>যদি টোকেন জেনারেট করতে সমস্যা হয়, তাহলে নিম্নলিখিত বিষয়গুলি চেক করুন:</p>
                                    <ol>
                                        <li>আপনার API ক্রেডেনশিয়ালস সঠিক কিনা</li>
                                        <li>আপনার ইন্টারনেট সংযোগ আছে কিনা</li>
                                        <li>বিকাশ সার্ভার অনলাইন আছে কিনা</li>
                                        <li>আপনার PHP cURL এক্সটেনশন সক্রিয় আছে কিনা</li>
                                    </ol>
                                    <p>যদি সমস্যা অব্যাহত থাকে, তাহলে বিকাশ সাপোর্ট টিমের সাথে যোগাযোগ করুন।</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<?php
// Include footer
include_once 'includes/footer.php';
?>
