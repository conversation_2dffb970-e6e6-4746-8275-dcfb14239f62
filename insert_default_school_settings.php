<?php
require_once 'includes/dbh.inc.php';

// Check if there's already a record in the school_settings table
$checkQuery = "SELECT COUNT(*) as count FROM school_settings";
$result = $conn->query($checkQuery);
$row = $result->fetch_assoc();

if ($row['count'] == 0) {
    // No records exist, insert default values
    $insertQuery = "INSERT INTO school_settings (school_name, school_address, school_phone, school_email) 
                   VALUES ('Default School Name', 'Default School Address', '************', '<EMAIL>')";
    
    if ($conn->query($insertQuery)) {
        echo "Default school settings have been added successfully.";
    } else {
        echo "Error adding default school settings: " . $conn->error;
    }
} else {
    echo "School settings already exist. No default values were added.";
}
?> 