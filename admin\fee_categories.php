<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Create fee_categories table if it doesn't exist
$createTableQuery = "CREATE TABLE IF NOT EXISTS fee_categories (
    id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";
$conn->query($createTableQuery);

// Check if default category exists
$checkDefaultQuery = "SELECT id FROM fee_categories WHERE id = 1";
$defaultResult = $conn->query($checkDefaultQuery);

if ($defaultResult->num_rows === 0) {
    // Insert default category
    $insertDefaultQuery = "INSERT INTO fee_categories (id, name, description) VALUES (1, 'Default', 'Default fee category')";
    $conn->query($insertDefaultQuery);
}

// Process category addition
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_category'])) {
    $name = $_POST['name'] ?? '';
    $description = $_POST['description'] ?? '';
    
    if (!empty($name)) {
        // Check if category already exists
        $checkQuery = "SELECT id FROM fee_categories WHERE name = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param('s', $name);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $_SESSION['error'] = 'এই ফি ক্যাটাগরি ইতিমধ্যে বিদ্যমান!';
        } else {
            // Create new category
            $insertQuery = "INSERT INTO fee_categories (name, description) VALUES (?, ?)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param('ss', $name, $description);
            
            if ($stmt->execute()) {
                $_SESSION['success'] = 'ফি ক্যাটাগরি সফলভাবে যোগ করা হয়েছে!';
            } else {
                $_SESSION['error'] = 'ফি ক্যাটাগরি যোগ করতে সমস্যা: ' . $conn->error;
            }
        }
    } else {
        $_SESSION['error'] = 'ফি ক্যাটাগরির নাম প্রদান করুন!';
    }
}

// Process category update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_category'])) {
    $id = $_POST['id'] ?? 0;
    $name = $_POST['name'] ?? '';
    $description = $_POST['description'] ?? '';
    
    if ($id > 0 && !empty($name)) {
        // Check if category name already exists for other categories
        $checkQuery = "SELECT id FROM fee_categories WHERE name = ? AND id != ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param('si', $name, $id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $_SESSION['error'] = 'এই ফি ক্যাটাগরি নাম ইতিমধ্যে অন্য একটি ক্যাটাগরিতে ব্যবহৃত হয়েছে!';
        } else {
            // Update category
            $updateQuery = "UPDATE fee_categories SET name = ?, description = ? WHERE id = ?";
            $stmt = $conn->prepare($updateQuery);
            $stmt->bind_param('ssi', $name, $description, $id);
            
            if ($stmt->execute()) {
                $_SESSION['success'] = 'ফি ক্যাটাগরি সফলভাবে আপডেট করা হয়েছে!';
            } else {
                $_SESSION['error'] = 'ফি ক্যাটাগরি আপডেট করতে সমস্যা: ' . $conn->error;
            }
        }
    } else {
        $_SESSION['error'] = 'অবৈধ ফি ক্যাটাগরি আইডি বা নাম!';
    }
}

// Process category deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_category'])) {
    $id = $_POST['id'] ?? 0;
    
    if ($id > 0 && $id != 1) { // Prevent deletion of default category
        // Check if category is used in fees
        $checkQuery = "SELECT COUNT(*) as count FROM fees WHERE category_id = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param('i', $id);
        $stmt->execute();
        $result = $stmt->get_result();
        $count = $result->fetch_assoc()['count'];
        
        if ($count > 0) {
            $_SESSION['error'] = 'এই ফি ক্যাটাগরি ইতিমধ্যে ব্যবহৃত হচ্ছে! আগে সম্পর্কিত ফি রেকর্ড আপডেট করুন।';
        } else {
            // Delete category
            $deleteQuery = "DELETE FROM fee_categories WHERE id = ?";
            $stmt = $conn->prepare($deleteQuery);
            $stmt->bind_param('i', $id);
            
            if ($stmt->execute()) {
                $_SESSION['success'] = 'ফি ক্যাটাগরি সফলভাবে মুছে ফেলা হয়েছে!';
            } else {
                $_SESSION['error'] = 'ফি ক্যাটাগরি মুছতে সমস্যা: ' . $conn->error;
            }
        }
    } else if ($id == 1) {
        $_SESSION['error'] = 'ডিফল্ট ক্যাটাগরি মুছা যাবে না!';
    } else {
        $_SESSION['error'] = 'অবৈধ ফি ক্যাটাগরি আইডি!';
    }
}

// Get all categories
$categoriesQuery = "SELECT * FROM fee_categories ORDER BY id";
$categoriesResult = $conn->query($categoriesQuery);

// Include header
include_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Include sidebar -->
        <?php include_once 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-folder me-2"></i> ফি ক্যাটাগরি ম্যানেজমেন্ট</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="fee_management.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i> ফি ম্যানেজমেন্ট
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                            <i class="fas fa-plus-circle me-1"></i> নতুন ক্যাটাগরি
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Display success/error messages -->
            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $_SESSION['success'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>
            
            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $_SESSION['error'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>
            
            <!-- Categories Table -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i> ফি ক্যাটাগরি তালিকা</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>আইডি</th>
                                    <th>নাম</th>
                                    <th>বিবরণ</th>
                                    <th>তৈরির তারিখ</th>
                                    <th>পদক্ষেপ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($categoriesResult && $categoriesResult->num_rows > 0): ?>
                                    <?php while ($category = $categoriesResult->fetch_assoc()): ?>
                                        <tr>
                                            <td><?= $category['id'] ?></td>
                                            <td><?= htmlspecialchars($category['name']) ?></td>
                                            <td><?= htmlspecialchars($category['description'] ?: '-') ?></td>
                                            <td><?= date('d/m/Y', strtotime($category['created_at'])) ?></td>
                                            <td>
                                                <?php if ($category['id'] != 1): // Prevent editing default category ?>
                                                    <button type="button" class="btn btn-sm btn-primary edit-category-btn" 
                                                            data-id="<?= $category['id'] ?>" 
                                                            data-name="<?= htmlspecialchars($category['name']) ?>" 
                                                            data-description="<?= htmlspecialchars($category['description']) ?>"
                                                            data-bs-toggle="modal" data-bs-target="#editCategoryModal">
                                                        <i class="fas fa-edit"></i> এডিট
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-danger delete-category-btn" 
                                                            data-id="<?= $category['id'] ?>" 
                                                            data-name="<?= htmlspecialchars($category['name']) ?>"
                                                            data-bs-toggle="modal" data-bs-target="#deleteCategoryModal">
                                                        <i class="fas fa-trash"></i> মুছুন
                                                    </button>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">ডিফল্ট ক্যাটাগরি</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="5" class="text-center">কোন ফি ক্যাটাগরি পাওয়া যায়নি</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addCategoryModalLabel"><i class="fas fa-plus-circle me-2"></i> নতুন ফি ক্যাটাগরি যোগ করুন</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">নাম</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">বিবরণ</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="submit" name="add_category" class="btn btn-primary">যোগ করুন</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Category Modal -->
<div class="modal fade" id="editCategoryModal" tabindex="-1" aria-labelledby="editCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editCategoryModalLabel"><i class="fas fa-edit me-2"></i> ফি ক্যাটাগরি এডিট করুন</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <input type="hidden" id="edit_id" name="id">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">নাম</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">বিবরণ</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="submit" name="update_category" class="btn btn-primary">আপডেট করুন</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Category Modal -->
<div class="modal fade" id="deleteCategoryModal" tabindex="-1" aria-labelledby="deleteCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteCategoryModalLabel"><i class="fas fa-trash me-2"></i> ফি ক্যাটাগরি মুছুন</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <input type="hidden" id="delete_id" name="id">
                    <p>আপনি কি নিশ্চিত যে আপনি এই ফি ক্যাটাগরি মুছতে চান?</p>
                    <p><strong>ক্যাটাগরি:</strong> <span id="delete_name"></span></p>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i> সতর্কতা: এই ক্যাটাগরি মুছে ফেললে এটি আর পুনরুদ্ধার করা যাবে না।
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="submit" name="delete_category" class="btn btn-danger">মুছুন</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
        
        // Handle edit button click
        const editButtons = document.querySelectorAll('.edit-category-btn');
        editButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');
                const description = this.getAttribute('data-description');
                
                document.getElementById('edit_id').value = id;
                document.getElementById('edit_name').value = name;
                document.getElementById('edit_description').value = description;
            });
        });
        
        // Handle delete button click
        const deleteButtons = document.querySelectorAll('.delete-category-btn');
        deleteButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');
                
                document.getElementById('delete_id').value = id;
                document.getElementById('delete_name').textContent = name;
            });
        });
    });
</script>

<?php
// Include footer
include_once 'includes/footer.php';
?>
