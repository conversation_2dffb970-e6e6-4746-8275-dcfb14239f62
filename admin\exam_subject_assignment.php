<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Create exam_subjects table if it doesn't exist
$tableCheck = $conn->query("SHOW TABLES LIKE 'exam_subjects'");
if ($tableCheck->num_rows == 0) {
    $createTableQuery = "CREATE TABLE IF NOT EXISTS exam_subjects (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        exam_id INT(11) NOT NULL,
        subject_id INT(11) NOT NULL,
        total_marks INT(11) NOT NULL DEFAULT 100,
        passing_marks INT(11) DEFAULT 33,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY (exam_id, subject_id),
        FOREI<PERSON>N KEY (exam_id) REFERENCES exams(id) ON DELETE CASCADE,
        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
    )";
    $conn->query($createTableQuery);
}

// Create exam_type_subjects table if it doesn't exist
$tableCheck = $conn->query("SHOW TABLES LIKE 'exam_type_subjects'");
if ($tableCheck->num_rows == 0) {
    $createTableQuery = "CREATE TABLE IF NOT EXISTS exam_type_subjects (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        exam_type_id INT(11) NOT NULL,
        subject_id INT(11) NOT NULL,
        class_id INT(11) NULL,
        department_id INT(11) NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY (exam_type_id, subject_id, class_id, department_id),
        FOREIGN KEY (exam_type_id) REFERENCES exam_types(id) ON DELETE CASCADE,
        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
        FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL,
        FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
    )";
    $conn->query($createTableQuery);
}

// Get all exam types
$examTypesQuery = "SELECT * FROM exam_types WHERE is_active = 1 ORDER BY type_name";
$examTypes = $conn->query($examTypesQuery);

// Get all subjects
$subjectsQuery = "SELECT * FROM subjects WHERE is_active = 1 ORDER BY subject_name";
$subjects = $conn->query($subjectsQuery);

// Get all classes
$classesQuery = "SELECT * FROM classes ORDER BY class_name";
$classes = $conn->query($classesQuery);

// Get all departments
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Handle form submission
$successMessage = '';
$errorMessage = '';

// Check for success message in URL
if (isset($_GET['success'])) {
    $successMessage = urldecode($_GET['success']);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['assign_subjects']) || isset($_POST['assign_all_subjects'])) {
        $examTypeId = intval($_POST['exam_type_id']);
        $classId = !empty($_POST['class_id']) ? intval($_POST['class_id']) : null;
        $departmentId = !empty($_POST['department_id']) ? intval($_POST['department_id']) : null;
        $subjectIds = isset($_POST['subject_ids']) ? $_POST['subject_ids'] : [];
        $assignAllSubjects = isset($_POST['assign_all_subjects']);

        if (empty($examTypeId) || (empty($subjectIds) && !$assignAllSubjects)) {
            $errorMessage = "পরীক্ষার ধরন এবং অন্তত একটি বিষয় নির্বাচন করুন!";
        } else {
            // Begin transaction
            $conn->begin_transaction();

            try {
                // First, delete existing assignments for this exam type, class, and department
                $deleteQuery = "DELETE FROM exam_type_subjects
                               WHERE exam_type_id = ?
                               AND (class_id = ? OR class_id IS NULL)
                               AND (department_id = ? OR department_id IS NULL)";
                $stmt = $conn->prepare($deleteQuery);
                $stmt->bind_param("iii", $examTypeId, $classId, $departmentId);
                $stmt->execute();

                // Insert new assignments
                $insertQuery = "INSERT INTO exam_type_subjects
                               (exam_type_id, subject_id, class_id, department_id)
                               VALUES (?, ?, ?, ?)";
                $stmt = $conn->prepare($insertQuery);

                // If assign all subjects is selected, get all subject IDs
                if ($assignAllSubjects) {
                    $allSubjectsQuery = "SELECT id FROM subjects WHERE is_active = 1";
                    $allSubjectsResult = $conn->query($allSubjectsQuery);
                    $subjectIds = [];

                    while ($subject = $allSubjectsResult->fetch_assoc()) {
                        $subjectIds[] = $subject['id'];
                    }
                }

                foreach ($subjectIds as $subjectId) {
                    $stmt->bind_param("iiii", $examTypeId, $subjectId, $classId, $departmentId);
                    $stmt->execute();
                }

                // Commit transaction
                $conn->commit();

                if ($assignAllSubjects) {
                    $successMessage = "সকল বিষয়সমূহ সফলভাবে পরীক্ষার ধরনে যুক্ত করা হয়েছে!";
                } else {
                    $successMessage = "নির্বাচিত বিষয়সমূহ সফলভাবে পরীক্ষার ধরনে যুক্ত করা হয়েছে!";
                }

                // Redirect to the same page to refresh the assignments list
                $redirectUrl = "exam_subject_assignment.php?exam_type_id=" . $examTypeId;
                if ($classId) {
                    $redirectUrl .= "&class_id=" . $classId;
                }
                if ($departmentId) {
                    $redirectUrl .= "&department_id=" . $departmentId;
                }
                $redirectUrl .= "&success=" . urlencode($successMessage);

                header("Location: " . $redirectUrl);
                exit();
            } catch (Exception $e) {
                // Rollback transaction on error
                $conn->rollback();
                $errorMessage = "বিষয় যুক্ত করতে সমস্যা হয়েছে: " . $e->getMessage();
            }
        }
    }

    // Handle reset all subjects
    if (isset($_POST['reset_all_subjects'])) {
        $examTypeId = intval($_POST['exam_type_id']);
        $classId = !empty($_POST['class_id']) ? intval($_POST['class_id']) : null;
        $departmentId = !empty($_POST['department_id']) ? intval($_POST['department_id']) : null;

        if (empty($examTypeId)) {
            $errorMessage = "পরীক্ষার ধরন নির্বাচন করুন!";
        } else {
            try {
                // Delete all assignments for this exam type, class, and department
                $deleteQuery = "DELETE FROM exam_type_subjects
                               WHERE exam_type_id = ?
                               AND (class_id = ? OR class_id IS NULL)
                               AND (department_id = ? OR department_id IS NULL)";
                $stmt = $conn->prepare($deleteQuery);
                $stmt->bind_param("iii", $examTypeId, $classId, $departmentId);
                $stmt->execute();

                $successMessage = "সকল বিষয় রিসেট করা হয়েছে!";

                // Redirect to the same page to refresh the assignments list
                $redirectUrl = "exam_subject_assignment.php?exam_type_id=" . $examTypeId;
                if ($classId) {
                    $redirectUrl .= "&class_id=" . $classId;
                }
                if ($departmentId) {
                    $redirectUrl .= "&department_id=" . $departmentId;
                }
                $redirectUrl .= "&success=" . urlencode($successMessage);

                header("Location: " . $redirectUrl);
                exit();
            } catch (Exception $e) {
                $errorMessage = "বিষয় রিসেট করতে সমস্যা হয়েছে: " . $e->getMessage();
            }
        }
    }
}

// Get existing assignments if exam type is selected
$assignments = [];
if (isset($_GET['exam_type_id'])) {
    $examTypeId = intval($_GET['exam_type_id']);
    $classId = isset($_GET['class_id']) ? intval($_GET['class_id']) : null;
    $departmentId = isset($_GET['department_id']) ? intval($_GET['department_id']) : null;

    $assignmentsQuery = "SELECT ets.*, s.subject_name, s.subject_code
                        FROM exam_type_subjects ets
                        JOIN subjects s ON ets.subject_id = s.id
                        WHERE ets.exam_type_id = ?";

    $params = [$examTypeId];
    $types = "i";

    if ($classId) {
        $assignmentsQuery .= " AND (ets.class_id = ? OR ets.class_id IS NULL)";
        $params[] = $classId;
        $types .= "i";
    }

    if ($departmentId) {
        $assignmentsQuery .= " AND (ets.department_id = ? OR ets.department_id IS NULL)";
        $params[] = $departmentId;
        $types .= "i";
    }

    $assignmentsQuery .= " ORDER BY s.subject_name";

    $stmt = $conn->prepare($assignmentsQuery);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $assignmentsResult = $stmt->get_result();

    while ($row = $assignmentsResult->fetch_assoc()) {
        $assignments[] = $row;
    }
}

// Get exam type name if selected
$selectedExamTypeName = '';
if (isset($_GET['exam_type_id'])) {
    $examTypeId = intval($_GET['exam_type_id']);
    $examTypeQuery = "SELECT type_name FROM exam_types WHERE id = ?";
    $stmt = $conn->prepare($examTypeQuery);
    $stmt->bind_param("i", $examTypeId);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($row = $result->fetch_assoc()) {
        $selectedExamTypeName = $row['type_name'];
    }
}

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>পরীক্ষার ধরন অনুযায়ী বিষয় নির্ধারণ - স্কুল ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css">
</head>
<body>
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10 main-content">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">পরীক্ষার ধরন অনুযায়ী বিষয় নির্ধারণ</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="exam_dashboard.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> পরীক্ষা ড্যাশবোর্ড
                        </a>
                    </div>
                </div>
            </div>

            <?php if (!empty($successMessage)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $successMessage; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (!empty($errorMessage)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $errorMessage; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">পরীক্ষার ধরন অনুযায়ী বিষয় নির্ধারণ</h5>
                        </div>
                        <div class="card-body">
                            <form method="GET" action="" id="filterForm">
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="exam_type_id" class="form-label">পরীক্ষার ধরন*</label>
                                        <select class="form-select" id="exam_type_id" name="exam_type_id" required onchange="document.getElementById('filterForm').submit();">
                                            <option value="">পরীক্ষার ধরন নির্বাচন করুন</option>
                                            <?php if ($examTypes && $examTypes->num_rows > 0): ?>
                                                <?php while ($type = $examTypes->fetch_assoc()): ?>
                                                    <option value="<?php echo $type['id']; ?>" <?php echo (isset($_GET['exam_type_id']) && $_GET['exam_type_id'] == $type['id']) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($type['type_name']); ?>
                                                    </option>
                                                <?php endwhile; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="class_id" class="form-label">শ্রেণী</label>
                                        <select class="form-select" id="class_id" name="class_id" onchange="document.getElementById('filterForm').submit();">
                                            <option value="">সকল শ্রেণী</option>
                                            <?php if ($classes && $classes->num_rows > 0): ?>
                                                <?php while ($class = $classes->fetch_assoc()): ?>
                                                    <option value="<?php echo $class['id']; ?>" <?php echo (isset($_GET['class_id']) && $_GET['class_id'] == $class['id']) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($class['class_name']); ?>
                                                    </option>
                                                <?php endwhile; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="department_id" class="form-label">বিভাগ</label>
                                        <select class="form-select" id="department_id" name="department_id" onchange="document.getElementById('filterForm').submit();">
                                            <option value="">সকল বিভাগ</option>
                                            <?php if ($departments && $departments->num_rows > 0): ?>
                                                <?php while ($dept = $departments->fetch_assoc()): ?>
                                                    <option value="<?php echo $dept['id']; ?>" <?php echo (isset($_GET['department_id']) && $_GET['department_id'] == $dept['id']) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($dept['department_name']); ?>
                                                    </option>
                                                <?php endwhile; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                </div>
                            </form>

                            <?php if (isset($_GET['exam_type_id'])): ?>
                                <form method="POST" action="">
                                    <input type="hidden" name="exam_type_id" value="<?php echo intval($_GET['exam_type_id']); ?>">
                                    <input type="hidden" name="class_id" value="<?php echo isset($_GET['class_id']) ? intval($_GET['class_id']) : ''; ?>">
                                    <input type="hidden" name="department_id" value="<?php echo isset($_GET['department_id']) ? intval($_GET['department_id']) : ''; ?>">

                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <label class="form-label mb-0">বিষয় নির্বাচন করুন*</label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="select_all_subjects" onchange="toggleAllSubjects(this)">
                                                <label class="form-check-label" for="select_all_subjects">
                                                    সকল বিষয় নির্বাচন করুন
                                                </label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <?php
                                            if ($subjects && $subjects->num_rows > 0):
                                                $subjects->data_seek(0); // Reset pointer
                                                $counter = 0;
                                                while ($subject = $subjects->fetch_assoc()):
                                                    $isChecked = false;
                                                    foreach ($assignments as $assignment) {
                                                        if ($assignment['subject_id'] == $subject['id']) {
                                                            $isChecked = true;
                                                            break;
                                                        }
                                                    }
                                            ?>
                                                <div class="col-md-4 mb-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="subject_ids[]" value="<?php echo $subject['id']; ?>" id="subject_<?php echo $subject['id']; ?>" <?php echo $isChecked ? 'checked' : ''; ?>>
                                                        <label class="form-check-label" for="subject_<?php echo $subject['id']; ?>">
                                                            <?php echo htmlspecialchars($subject['subject_name']); ?>
                                                            <?php if (!empty($subject['subject_code'])): ?>
                                                                <small class="text-muted">(<?php echo htmlspecialchars($subject['subject_code']); ?>)</small>
                                                            <?php endif; ?>
                                                        </label>
                                                    </div>
                                                </div>
                                            <?php
                                                $counter++;
                                                endwhile;
                                            else:
                                            ?>
                                                <div class="col-12">
                                                    <div class="alert alert-warning">
                                                        কোন বিষয় পাওয়া যায়নি। আগে বিষয় যোগ করুন।
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="mt-3 d-flex flex-wrap gap-2">
                                        <button type="submit" name="assign_subjects" class="btn btn-success">
                                            <i class="fas fa-save me-2"></i>নির্বাচিত বিষয় নির্ধারণ করুন
                                        </button>
                                        <button type="submit" name="assign_all_subjects" class="btn btn-primary">
                                            <i class="fas fa-check-double me-2"></i>সকল বিষয় নির্ধারণ করুন
                                        </button>
                                        <button type="submit" name="reset_all_subjects" class="btn btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি সকল বিষয় রিসেট করতে চান?')">
                                            <i class="fas fa-trash me-2"></i>সকল বিষয় রিসেট করুন
                                        </button>
                                    </div>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <?php if (!empty($assignments)): ?>
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <?php echo htmlspecialchars($selectedExamTypeName); ?> - নির্ধারিত বিষয়সমূহ
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>বিষয়</th>
                                            <th>কোড</th>
                                            <th>শ্রেণী</th>
                                            <th>বিভাগ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($assignments as $index => $assignment): ?>
                                            <tr>
                                                <td><?php echo $index + 1; ?></td>
                                                <td><?php echo htmlspecialchars($assignment['subject_name']); ?></td>
                                                <td><?php echo htmlspecialchars($assignment['subject_code']); ?></td>
                                                <td>
                                                    <?php
                                                    if ($assignment['class_id']) {
                                                        $classQuery = "SELECT class_name FROM classes WHERE id = ?";
                                                        $stmt = $conn->prepare($classQuery);
                                                        $stmt->bind_param("i", $assignment['class_id']);
                                                        $stmt->execute();
                                                        $result = $stmt->get_result();
                                                        if ($row = $result->fetch_assoc()) {
                                                            echo htmlspecialchars($row['class_name']);
                                                        } else {
                                                            echo 'N/A';
                                                        }
                                                    } else {
                                                        echo 'সকল শ্রেণী';
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    if ($assignment['department_id']) {
                                                        $deptQuery = "SELECT department_name FROM departments WHERE id = ?";
                                                        $stmt = $conn->prepare($deptQuery);
                                                        $stmt->bind_param("i", $assignment['department_id']);
                                                        $stmt->execute();
                                                        $result = $stmt->get_result();
                                                        if ($row = $result->fetch_assoc()) {
                                                            echo htmlspecialchars($row['department_name']);
                                                        } else {
                                                            echo 'N/A';
                                                        }
                                                    } else {
                                                        echo 'সকল বিভাগ';
                                                    }
                                                    ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleAllSubjects(checkbox) {
            const isChecked = checkbox.checked;
            const subjectCheckboxes = document.querySelectorAll('input[name="subject_ids[]"]');

            subjectCheckboxes.forEach(function(subjectCheckbox) {
                subjectCheckbox.checked = isChecked;
            });
        }

        // Check if all subjects are selected and update the "select all" checkbox accordingly
        document.addEventListener('DOMContentLoaded', function() {
            const subjectCheckboxes = document.querySelectorAll('input[name="subject_ids[]"]');
            const selectAllCheckbox = document.getElementById('select_all_subjects');

            if (subjectCheckboxes.length > 0 && selectAllCheckbox) {
                // Add event listeners to all subject checkboxes
                subjectCheckboxes.forEach(function(checkbox) {
                    checkbox.addEventListener('change', function() {
                        const allChecked = Array.from(subjectCheckboxes).every(function(cb) {
                            return cb.checked;
                        });

                        selectAllCheckbox.checked = allChecked;
                    });
                });

                // Check initial state
                const allChecked = Array.from(subjectCheckboxes).every(function(cb) {
                    return cb.checked;
                });

                selectAllCheckbox.checked = allChecked;
            }
        });
    </script>
</body>
</html>
