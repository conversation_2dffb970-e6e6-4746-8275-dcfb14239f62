/* Sidebar Fix CSS */

/* Reset some bootstrap styles */
body {
    padding: 0;
    margin: 0;
    overflow-x: hidden;
}

/* Sidebar Styles */
.sidebar {
    background-color: #2c3e50;
    color: white;
    height: 100vh;
    position: fixed;
    overflow-y: auto;
    padding-top: 20px;
    padding-bottom: 60px;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
    transition: all 0.3s ease;
    z-index: 100;
    width: 16.66%; /* Match col-md-2 width */
    left: 0;
    top: 0;
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 10px 20px;
    margin-bottom: 5px;
    border-radius: 5px;
    transition: all 0.2s ease;
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    background-color: #3498db;
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Main Content styles */
.main-content {
    margin-left: 16.66%; /* col-md-2 width */
    padding-left: 25px;
    padding-right: 25px;
    padding-top: 20px;
    padding-bottom: 20px;
    transition: all 0.3s ease;
    width: 83.33%; /* Match col-md-10 width */
}

/* Media queries for responsiveness */
@media (max-width: 991.98px) {
    .sidebar {
        width: 25%; /* Match col-md-3 width */
    }

    .main-content {
        margin-left: 25%;
        width: 75%;
    }
}

@media (max-width: 767.98px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }

    .main-content {
        margin-left: 0;
        width: 100%;
    }
}
