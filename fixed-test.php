<?php
// Complete response fix for spinning tab icon
header('Content-Type: text/html; charset=UTF-8');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');

// Calculate content length
ob_start();
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <title>নিশাত এডুকেশন সেন্টার - Fixed</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 50px;
            background: #f0f8ff;
            color: #333;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .status {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🏫 নিশাত এডুকেশন সেন্টার</h1>
    
    <div class="success">
        ✅ <strong>Fixed Version</strong> - Tab loading icon should stop now!
    </div>
    
    <div class="status">
        📊 <strong>Status:</strong> Page loaded completely
    </div>
    
    <div class="status">
        🕒 <strong>Load Time:</strong> <?php echo date('Y-m-d H:i:s'); ?>
    </div>
    
    <div class="status">
        🌐 <strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?>
    </div>
    
    <h2>🧪 Test Results:</h2>
    <ul>
        <li>✅ PHP processing complete</li>
        <li>✅ Headers sent properly</li>
        <li>✅ Content-length calculated</li>
        <li>✅ Connection will close properly</li>
    </ul>
    
    <h2>🎯 Instructions:</h2>
    <p>If the tab loading icon stops spinning on this page, then the fix works!</p>
    
    <script>
        // Simple script to confirm JavaScript execution
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded successfully - no infinite loops');
            document.title = 'নিশাত এডুকেশন সেন্টার - Fixed ✅';
        });
    </script>
</body>
</html>
<?php
$content = ob_get_contents();
$content_length = strlen($content);
ob_end_clean();

// Send proper headers
header('Content-Length: ' . $content_length);
header('Connection: close');

// Output content
echo $content;

// Ensure complete response
if (function_exists('fastcgi_finish_request')) {
    fastcgi_finish_request();
} else {
    // Force connection close
    if (ob_get_level()) {
        ob_end_flush();
    }
    flush();
}

// Explicitly exit to ensure no additional output
exit();
?>
