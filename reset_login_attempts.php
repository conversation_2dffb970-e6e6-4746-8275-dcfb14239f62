<?php
session_start();

// Clear login attempts tracking
if (isset($_SESSION['login_attempts'])) {
    unset($_SESSION['login_attempts']);
    echo "<div style='background-color: #d1e7dd; color: #0f5132; padding: 15px; border-radius: 5px; margin: 20px;'>
            <h3>লগইন অ্যাটেম্পট রিসেট করা হয়েছে</h3>
            <p>আপনি এখন আবার লগইন করার চেষ্টা করতে পারেন।</p>
            <a href='index.php#login-section' style='display: inline-block; background-color: #0f5132; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-top: 10px;'>লগইন পেজে ফিরে যান</a>
         </div>";
} else {
    echo "<div style='background-color: #cff4fc; color: #055160; padding: 15px; border-radius: 5px; margin: 20px;'>
            <h3>কোন লগইন অ্যাটেম্পট পাওয়া যায়নি</h3>
            <p>আপনার সেশনে কোন লগইন অ্যাটেম্পট ট্র্যাকিং নেই।</p>
            <a href='index.php#login-section' style='display: inline-block; background-color: #055160; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-top: 10px;'>লগইন পেজে ফিরে যান</a>
         </div>";
}
?> 