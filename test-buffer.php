<?php
// Test page to check if buffering issue is fixed
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Pragma: no-cache');
header('Expires: 0');

// Disable output buffering
if (ob_get_level()) {
    ob_end_clean();
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>নিশাত এডুকেশন সেন্টার - টেস্ট</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f8f9fa;
            padding: 50px 0;
        }
        
        .test-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            padding: 40px;
            text-align: center;
        }
        
        .status-good {
            color: #28a745;
            font-size: 1.2rem;
            font-weight: bold;
        }
        
        .status-bad {
            color: #dc3545;
            font-size: 1.2rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="test-container">
                    <h1 class="mb-4">বাফার টেস্ট পেজ</h1>
                    
                    <div class="alert alert-info">
                        <h4>টাইটেল বার চেক করুন:</h4>
                        <p>যদি টাইটেল বারে "নিশাত এডুকেশন সেন্টার - টেস্ট" দেখায় এবং কোনো বাফার/লোডিং দেখা না যায়, তাহলে সমস্যা সমাধান হয়েছে।</p>
                    </div>
                    
                    <div id="status" class="mt-4">
                        <div class="status-good">✅ পেজ সফলভাবে লোড হয়েছে</div>
                        <div class="mt-2">
                            <small>বর্তমান সময়: <?php echo date('d/m/Y H:i:s'); ?></small>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <a href="index.php" class="btn btn-primary">মূল পেজে ফিরে যান</a>
                        <button onclick="location.reload()" class="btn btn-secondary">পেজ রিফ্রেশ করুন</button>
                    </div>
                    
                    <div class="mt-4">
                        <h5>ডিবাগ তথ্য:</h5>
                        <div class="text-start">
                            <small>
                                <strong>User Agent:</strong> <span id="userAgent"></span><br>
                                <strong>Page Load Time:</strong> <span id="loadTime"></span><br>
                                <strong>Active Intervals:</strong> <span id="activeIntervals">0</span><br>
                                <strong>Active Timeouts:</strong> <span id="activeTimeouts">0</span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Simple test script without intervals
        document.addEventListener('DOMContentLoaded', function() {
            // Set debug info
            document.getElementById('userAgent').textContent = navigator.userAgent;
            document.getElementById('loadTime').textContent = new Date().toLocaleString('bn-BD');
            
            // Check for active intervals (if debug script is loaded)
            if (window.debugBufferFix) {
                document.getElementById('activeIntervals').textContent = window.debugBufferFix.getActiveIntervals().length;
                document.getElementById('activeTimeouts').textContent = window.debugBufferFix.getActiveTimeouts().length;
            }
            
            // Ensure title is correct
            document.title = 'নিশাত এডুকেশন সেন্টার - টেস্ট';
            
            console.log('Test page loaded successfully');
        });
    </script>
</body>
</html>
<?php
// Flush output
if (function_exists('fastcgi_finish_request')) {
    fastcgi_finish_request();
} else {
    flush();
}
?>
