<?php
// Quick test to stop tab loading icon
header('Content-Type: text/html; charset=UTF-8');
header('Connection: close');

ob_start();
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <title>নিশাত এডুকেশন সেন্টার</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 50px;
            background: #e8f5e8;
            color: #333;
            text-align: center;
        }
        .success-box {
            background: #d4edda;
            border: 2px solid #28a745;
            padding: 30px;
            border-radius: 15px;
            margin: 20px auto;
            max-width: 600px;
        }
        .status {
            background: #fff3cd;
            border: 1px solid #ffc107;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .icon {
            font-size: 48px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="success-box">
        <div class="icon">🏫</div>
        <h1>নিশাত এডুকেশন সেন্টার</h1>
        <h2>চুয়াডাঙ্গা, বাংলাদেশ</h2>
        
        <div class="status">
            ✅ <strong>Tab Loading Icon Fix Applied!</strong>
        </div>
        
        <div class="status">
            📊 Status: Page loaded completely<br>
            🕒 Time: <?php echo date('Y-m-d H:i:s'); ?><br>
            🌐 Server: <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'XAMPP'; ?>
        </div>
        
        <div class="status">
            🎯 <strong>Check the browser tab - loading icon should stop spinning!</strong>
        </div>
        
        <p><a href="index.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Go to Main Page</a></p>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Quick test loaded - tab icon should stop');
        });
    </script>
</body>
</html>
<?php
$content = ob_get_contents();
ob_end_clean();

header('Content-Length: ' . strlen($content));
echo $content;

if (function_exists('fastcgi_finish_request')) {
    fastcgi_finish_request();
}
exit();
?>
