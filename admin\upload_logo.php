<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

$success_message = '';
$error_message = '';

// Handle logo upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['logo'])) {
    $targetDir = "../uploads/";
    $targetFile = $targetDir . "institution_logo.png";
    
    // Create directory if it doesn't exist
    if (!file_exists($targetDir)) {
        mkdir($targetDir, 0777, true);
    }
    
    // Check if image file is a actual image
    $check = getimagesize($_FILES["logo"]["tmp_name"]);
    if ($check !== false) {
        // Check file size (max 2MB)
        if ($_FILES["logo"]["size"] <= 2000000) {
            // Allow only certain file formats
            $imageFileType = strtolower(pathinfo($_FILES["logo"]["name"], PATHINFO_EXTENSION));
            if ($imageFileType == "jpg" || $imageFileType == "png" || $imageFileType == "jpeg" || $imageFileType == "gif") {
                // Upload the file
                if (move_uploaded_file($_FILES["logo"]["tmp_name"], $targetFile)) {
                    $success_message = "প্রতিষ্ঠানের লোগো সফলভাবে আপলোড করা হয়েছে।";
                } else {
                    $error_message = "ফাইল আপলোড করতে সমস্যা হয়েছে।";
                }
            } else {
                $error_message = "শুধুমাত্র JPG, JPEG, PNG এবং GIF ফাইল আপলোড করা যাবে।";
            }
        } else {
            $error_message = "ফাইলের সাইজ 2MB এর বেশি হতে পারবে না।";
        }
    } else {
        $error_message = "আপলোড করা ফাইলটি একটি ছবি নয়।";
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>প্রতিষ্ঠানের লোগো আপলোড - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <?php include 'includes/global-head.php'; ?>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-upload me-2 text-primary"></i> প্রতিষ্ঠানের লোগো আপলোড
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="create_default_images.php" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left me-1"></i> ফিরে যান
                        </a>
                        <a href="dashboard.php" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-tachometer-alt me-1"></i> ড্যাশবোর্ড
                        </a>
                    </div>
                </div>

                <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-1"></i> <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-1"></i> <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-upload me-2"></i> লোগো আপলোড করুন
                                </h5>
                            </div>
                            <div class="card-body">
                                <form action="" method="POST" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="logo" class="form-label">প্রতিষ্ঠানের লোগো নির্বাচন করুন</label>
                                        <input type="file" class="form-control" id="logo" name="logo" required>
                                        <div class="form-text">সর্বোচ্চ ফাইল সাইজ: 2MB। অনুমোদিত ফরম্যাট: JPG, JPEG, PNG, GIF</div>
                                    </div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-upload me-1"></i> আপলোড করুন
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-info-circle me-2"></i> বর্তমান লোগো
                                </h5>
                            </div>
                            <div class="card-body text-center">
                                <?php
                                $logoPath = '../uploads/institution_logo.png';
                                $defaultLogoPath = '../assets/images/default_logo.png';
                                
                                if (file_exists($logoPath)) {
                                    echo '<img src="' . $logoPath . '?v=' . time() . '" alt="Institution Logo" class="img-fluid mb-3" style="max-width: 200px;">';
                                    echo '<p>বর্তমান প্রতিষ্ঠানের লোগো</p>';
                                } else if (file_exists($defaultLogoPath)) {
                                    echo '<img src="' . $defaultLogoPath . '" alt="Default Logo" class="img-fluid mb-3" style="max-width: 200px;">';
                                    echo '<p>ডিফল্ট লোগো (কোন লোগো আপলোড করা হয়নি)</p>';
                                } else {
                                    echo '<div class="alert alert-warning">কোন লোগো পাওয়া যায়নি। অনুগ্রহ করে একটি লোগো আপলোড করুন।</div>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-lightbulb me-2"></i> টিপস
                        </h5>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li>সর্বোত্তম ফলাফলের জন্য, একটি স্কোয়ার (বর্গাকার) লোগো ব্যবহার করুন।</li>
                            <li>লোগোর রেজোলিউশন কমপক্ষে 200x200 পিক্সেল হওয়া উচিত।</li>
                            <li>স্বচ্ছ ব্যাকগ্রাউন্ড (transparent background) সহ PNG ফরম্যাট সর্বোত্তম।</li>
                            <li>লোগো আপলোড করার পর, মার্কশীটে এটি স্বয়ংক্রিয়ভাবে দেখানো হবে।</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
