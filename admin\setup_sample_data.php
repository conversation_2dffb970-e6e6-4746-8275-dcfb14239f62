<?php
session_start();
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

echo "<h2>নমুনা ডেটা সেটআপ</h2>";

try {
    // Step 1: Ensure all required tables exist
    echo "<h3>ধাপ ১: টেবিল চেক ও তৈরি</h3>";
    
    // Create classes table if not exists
    $createClassesTable = "CREATE TABLE IF NOT EXISTS classes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        class_name VARCHAR(100) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $conn->query($createClassesTable);
    echo "<p>✅ classes টেবিল প্রস্তুত</p>";
    
    // Create students table if not exists
    $createStudentsTable = "CREATE TABLE IF NOT EXISTS students (
        id INT AUTO_INCREMENT PRIMARY KEY,
        first_name VARCHAR(50) NOT NULL,
        last_name <PERSON><PERSON>HA<PERSON>(50) NOT NULL,
        roll_no VARCHAR(20),
        student_id VARCHAR(20),
        class_id INT,
        email VARCHAR(100),
        phone VARCHAR(20),
        gender ENUM('male', 'female', 'other'),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (class_id) REFERENCES classes(id)
    )";
    $conn->query($createStudentsTable);
    echo "<p>✅ students টেবিল প্রস্তুত</p>";
    
    // Create fees table if not exists
    $createFeesTable = "CREATE TABLE IF NOT EXISTS fees (
        id INT AUTO_INCREMENT PRIMARY KEY,
        student_id INT NOT NULL,
        fee_type VARCHAR(100) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        paid DECIMAL(10,2) DEFAULT 0,
        due_date DATE,
        payment_status ENUM('paid', 'partial', 'due') DEFAULT 'due',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE
    )";
    $conn->query($createFeesTable);
    echo "<p>✅ fees টেবিল প্রস্তুত</p>";
    
    // Create fee_payments table if not exists
    $createPaymentsTable = "CREATE TABLE IF NOT EXISTS fee_payments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        fee_id INT NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        payment_date DATE NOT NULL,
        payment_method VARCHAR(50) NOT NULL,
        receipt_no VARCHAR(50) DEFAULT NULL,
        notes TEXT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (fee_id) REFERENCES fees(id) ON DELETE CASCADE
    )";
    $conn->query($createPaymentsTable);
    echo "<p>✅ fee_payments টেবিল প্রস্তুত</p>";
    
    // Step 2: Insert sample data
    echo "<h3>ধাপ ২: নমুনা ডেটা যোগ</h3>";
    
    // Insert sample classes
    $classCheck = $conn->query("SELECT COUNT(*) as count FROM classes")->fetch_assoc()['count'];
    if ($classCheck == 0) {
        $classes = [
            ['একাদশ শ্রেণী', 'একাদশ শ্রেণীর শিক্ষার্থীরা'],
            ['দ্বাদশ শ্রেণী', 'দ্বাদশ শ্রেণীর শিক্ষার্থীরা'],
            ['নবম শ্রেণী', 'নবম শ্রেণীর শিক্ষার্থীরা'],
            ['দশম শ্রেণী', 'দশম শ্রেণীর শিক্ষার্থীরা']
        ];
        
        foreach ($classes as $class) {
            $stmt = $conn->prepare("INSERT INTO classes (class_name, description) VALUES (?, ?)");
            $stmt->bind_param("ss", $class[0], $class[1]);
            $stmt->execute();
        }
        echo "<p>✅ নমুনা ক্লাস যোগ করা হয়েছে</p>";
    }
    
    // Insert sample students
    $studentCheck = $conn->query("SELECT COUNT(*) as count FROM students")->fetch_assoc()['count'];
    if ($studentCheck < 5) {
        $students = [
            ['মোহাম্মদ', 'রহিম উদ্দিন', '2024001', 'S2024001', 1, '<EMAIL>', '01712345678', 'male'],
            ['ফাতেমা', 'খাতুন', '2024002', 'S2024002', 1, '<EMAIL>', '01712345679', 'female'],
            ['আব্দুল', 'করিম', '2024003', 'S2024003', 2, '<EMAIL>', '01712345680', 'male'],
            ['রাহেলা', 'বেগম', '2024004', 'S2024004', 2, '<EMAIL>', '01712345681', 'female'],
            ['মোস্তাফিজুর', 'রহমান', '2024005', 'S2024005', 3, '<EMAIL>', '01712345682', 'male']
        ];
        
        foreach ($students as $student) {
            $stmt = $conn->prepare("INSERT INTO students (first_name, last_name, roll_no, student_id, class_id, email, phone, gender) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->bind_param("ssssssss", $student[0], $student[1], $student[2], $student[3], $student[4], $student[5], $student[6], $student[7]);
            $stmt->execute();
        }
        echo "<p>✅ নমুনা শিক্ষার্থী যোগ করা হয়েছে</p>";
    }
    
    // Insert sample fees
    $feeCheck = $conn->query("SELECT COUNT(*) as count FROM fees")->fetch_assoc()['count'];
    if ($feeCheck < 5) {
        $students = $conn->query("SELECT id FROM students LIMIT 5")->fetch_all(MYSQLI_ASSOC);
        $feeTypes = ['মাসিক বেতন', 'পরীক্ষার ফি', 'ভর্তি ফি', 'লাইব্রেরি ফি', 'খেলাধুলা ফি'];
        $amounts = [1500, 500, 2000, 300, 200];
        
        foreach ($students as $index => $student) {
            $stmt = $conn->prepare("INSERT INTO fees (student_id, fee_type, amount, due_date, payment_status) VALUES (?, ?, ?, ?, 'due')");
            $dueDate = date('Y-m-d', strtotime('+30 days'));
            $stmt->bind_param("isds", $student['id'], $feeTypes[$index], $amounts[$index], $dueDate);
            $stmt->execute();
        }
        echo "<p>✅ নমুনা ফি যোগ করা হয়েছে</p>";
    }
    
    // Step 3: Create sample payments
    echo "<h3>ধাপ ৩: নমুনা পেমেন্ট তৈরি</h3>";
    
    $paymentCheck = $conn->query("SELECT COUNT(*) as count FROM fee_payments")->fetch_assoc()['count'];
    if ($paymentCheck < 3) {
        $fees = $conn->query("SELECT id, amount FROM fees LIMIT 3")->fetch_all(MYSQLI_ASSOC);
        $paymentMethods = ['cash', 'bank', 'mobile_banking'];
        
        foreach ($fees as $index => $fee) {
            $receiptNo = 'RCP-' . date('Ymd') . '-' . str_pad($index + 1, 4, '0', STR_PAD_LEFT);
            $stmt = $conn->prepare("INSERT INTO fee_payments (fee_id, receipt_no, amount, payment_date, payment_method, notes) VALUES (?, ?, ?, ?, ?, ?)");
            $paymentDate = date('Y-m-d', strtotime('-' . ($index + 1) . ' days'));
            $notes = 'নমুনা পেমেন্ট - টেস্ট ডেটা';
            $stmt->bind_param("isdsss", $fee['id'], $receiptNo, $fee['amount'], $paymentDate, $paymentMethods[$index], $notes);
            $stmt->execute();
            
            // Update fee status
            $updateFee = $conn->prepare("UPDATE fees SET paid = ?, payment_status = 'paid' WHERE id = ?");
            $updateFee->bind_param("di", $fee['amount'], $fee['id']);
            $updateFee->execute();
            
            echo "<p>✅ পেমেন্ট তৈরি: $receiptNo</p>";
        }
    }
    
    // Step 4: Show results
    echo "<h3>ধাপ ৪: সেটআপ সম্পন্ন</h3>";
    
    $stats = [
        'classes' => $conn->query("SELECT COUNT(*) as count FROM classes")->fetch_assoc()['count'],
        'students' => $conn->query("SELECT COUNT(*) as count FROM students")->fetch_assoc()['count'],
        'fees' => $conn->query("SELECT COUNT(*) as count FROM fees")->fetch_assoc()['count'],
        'payments' => $conn->query("SELECT COUNT(*) as count FROM fee_payments")->fetch_assoc()['count']
    ];
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>ডেটাবেস স্ট্যাটাস:</h4>";
    echo "<ul>";
    echo "<li>ক্লাস: {$stats['classes']} টি</li>";
    echo "<li>শিক্ষার্থী: {$stats['students']} জন</li>";
    echo "<li>ফি: {$stats['fees']} টি</li>";
    echo "<li>পেমেন্ট: {$stats['payments']} টি</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='payment_receipts.php' class='btn btn-primary' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 5px;'>পেমেন্ট রিসিপ্ট দেখুন</a>";
    echo "<a href='fee_management.php' class='btn btn-secondary' style='background: #6c757d; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 5px;'>ফি ম্যানেজমেন্ট</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: 'Hind Siliguri', Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}
h2, h3 {
    color: #333;
}
p {
    margin: 10px 0;
}
.btn {
    display: inline-block;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 5px;
    margin: 5px;
}
</style>
