<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'includes/dbh.inc.php';

echo "<h1>Creating Certificates Table - Direct Method</h1>";
echo "<pre>";

// Ensure connection is active
$conn = ensure_connection();

if ($conn) {
    echo "Database connection successful.\n";
    
    // Create the certificates table directly with SQL
    $sql = "CREATE TABLE IF NOT EXISTS `certificates` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `student_id` int(11) NOT NULL,
        `title` varchar(255) NOT NULL,
        `description` text NOT NULL,
        `certificate_date` date NOT NULL,
        `issued_by` varchar(255) NOT NULL,
        `certificate_type` varchar(50) NOT NULL,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY <PERSON>EY (`id`),
        KEY `student_id` (`student_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    try {
        $result = $conn->query($sql);
        
        if ($result) {
            echo "Certificates table created successfully!\n";
        } else {
            echo "Error creating table: " . $conn->error . "\n";
        }
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "\n";
    }
    
    // Try to add the foreign key constraint separately
    // This is done separately in case the students table doesn't exist or has a different structure
    try {
        $fk_sql = "ALTER TABLE `certificates` 
                   ADD CONSTRAINT `certificates_ibfk_1` 
                   FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) 
                   ON DELETE CASCADE";
        
        $result = $conn->query($fk_sql);
        
        if ($result) {
            echo "Foreign key constraint added successfully!\n";
        } else {
            echo "Warning: Could not add foreign key constraint: " . $conn->error . "\n";
            echo "This is not critical - the table will still work without the constraint.\n";
        }
    } catch (Exception $e) {
        echo "Warning: " . $e->getMessage() . "\n";
        echo "This is not critical - the table will still work without the constraint.\n";
    }
    
    // Verify the table exists
    try {
        $result = $conn->query("SHOW TABLES LIKE 'certificates'");
        
        if ($result && $result->num_rows > 0) {
            echo "Verification: The 'certificates' table exists in the database.\n";
        } else {
            echo "Verification failed: The 'certificates' table does not appear to exist.\n";
        }
    } catch (Exception $e) {
        echo "Verification error: " . $e->getMessage() . "\n";
    }
    
    // Show the table structure
    try {
        $result = $conn->query("DESCRIBE certificates");
        
        if ($result) {
            echo "\nTable structure for 'certificates':\n";
            echo "------------------------------------\n";
            echo "Field | Type | Null | Key | Default | Extra\n";
            echo "------------------------------------\n";
            
            while ($row = $result->fetch_assoc()) {
                echo "{$row['Field']} | {$row['Type']} | {$row['Null']} | {$row['Key']} | {$row['Default']} | {$row['Extra']}\n";
            }
            
            $result->free();
        } else {
            echo "Error describing table: " . $conn->error . "\n";
        }
    } catch (Exception $e) {
        echo "Error describing table: " . $e->getMessage() . "\n";
    }
    
    // Check database name to ensure we're using the correct database
    try {
        $result = $conn->query("SELECT DATABASE()");
        if ($result) {
            $row = $result->fetch_row();
            echo "\nCurrent database: " . $row[0] . "\n";
            if ($row[0] != 'college_management') {
                echo "WARNING: Current database is not 'college_management'!\n";
            }
            $result->free();
        }
    } catch (Exception $e) {
        echo "Error checking database name: " . $e->getMessage() . "\n";
    }
    
    $conn->close();
    echo "Database connection closed.\n";
} else {
    echo "Error: Could not establish database connection.\n";
}

echo "</pre>";
echo "<p>Process completed. <a href='admin/certificates.php'>Go to Certificates Page</a></p>";
?>
