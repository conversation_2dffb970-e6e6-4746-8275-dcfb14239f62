<?php
// Include database connection
require_once "includes/dbh.inc.php";

// Define subject categories
$requiredSubjects = [
    'বাংলা', 'ইংরেজি', 'গণিত', 'বিজ্ঞান', 'ইসলাম শিক্ষা', 'হিন্দু ধর্ম', 'বৌদ্ধ ধর্ম', 'খ্রিষ্টান ধর্ম'
];

$optionalSubjects = [
    'ইতিহাস', 'ভূগোল', 'অর্থনীতি', 'রসায়ন', 'সমাজবিজ্ঞান', 'ধর্ম', 'বাংলাদেশ পরিচয়', 'তথ্য ও যোগাযোগ প্রযুক্তি'
];

$fourthSubjects = [
    'পদার্থবিজ্ঞান', 'জীববিজ্ঞান', 'কম্পিউটার', 'উচ্চতর গণিত', 'কৃষি শিক্ষা', 'গার্হস্থ্য বিজ্ঞান', 'চারু ও কারুকলা'
];

// Start transaction
$conn->begin_transaction();

try {
    // 1. Update subjects table
    $updateSubjectQuery = "UPDATE subjects SET category = ? WHERE subject_name = ?";
    $stmt = $conn->prepare($updateSubjectQuery);
    
    // Update required subjects
    foreach ($requiredSubjects as $subject) {
        $category = 'required';
        $stmt->bind_param("ss", $category, $subject);
        $stmt->execute();
    }
    
    // Update optional subjects
    foreach ($optionalSubjects as $subject) {
        $category = 'optional';
        $stmt->bind_param("ss", $category, $subject);
        $stmt->execute();
    }
    
    // Update fourth subjects
    foreach ($fourthSubjects as $subject) {
        $category = 'fourth';
        $stmt->bind_param("ss", $category, $subject);
        $stmt->execute();
    }
    
    // 2. Update department_subject_types table
    // First, get all subjects with their categories
    $subjectsQuery = "SELECT id, subject_name, category FROM subjects";
    $subjects = $conn->query($subjectsQuery);
    
    if ($subjects && $subjects->num_rows > 0) {
        $updateTypeQuery = "UPDATE department_subject_types SET subject_type = ? WHERE subject_id = ?";
        $stmt = $conn->prepare($updateTypeQuery);
        
        while ($subject = $subjects->fetch_assoc()) {
            $subjectId = $subject['id'];
            $category = $subject['category'];
            
            $stmt->bind_param("si", $category, $subjectId);
            $stmt->execute();
        }
    }
    
    // Commit transaction
    $conn->commit();
    
    echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
    echo "<h3>সফলভাবে আপডেট করা হয়েছে!</h3>";
    echo "<p>সকল বিষয়ের ক্যাটাগরি সঠিকভাবে আপডেট করা হয়েছে।</p>";
    echo "<ul>";
    echo "<li><strong>আবশ্যিক বিষয়:</strong> " . implode(", ", $requiredSubjects) . "</li>";
    echo "<li><strong>ঐচ্ছিক বিষয়:</strong> " . implode(", ", $optionalSubjects) . "</li>";
    echo "<li><strong>৪র্থ বিষয়:</strong> " . implode(", ", $fourthSubjects) . "</li>";
    echo "</ul>";
    echo "<p>এখন আপনি <a href='admin/student_subject_selection.php'>শিক্ষার্থী বিষয় নির্বাচন</a> পেজে যেতে পারেন।</p>";
    echo "</div>";
    
} catch (Exception $e) {
    // Rollback on error
    $conn->rollback();
    
    echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
    echo "<h3>ত্রুটি!</h3>";
    echo "<p>বিষয় ক্যাটাগরি আপডেট করতে ব্যর্থ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

// Close connection
$conn->close();
?>
