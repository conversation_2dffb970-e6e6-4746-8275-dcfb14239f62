// Subjects Page JavaScript Functions

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all functionality
    initializeActionButtons();
    initializeSearchAndFilter();
    initializeTooltips();
    initializeModals();
});

// Action Button Functionality
function initializeActionButtons() {
    // Delete confirmation
    const deleteButtons = document.querySelectorAll('a[href*="delete="]');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const subjectName = this.closest('tr').querySelector('td:nth-child(2)').textContent.trim();
            
            if (confirm(`আপনি কি নিশ্চিত যে "${subjectName}" বিষয়টি মুছে ফেলতে চান?\n\nএই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।`)) {
                window.location.href = this.href;
            }
        });
    });

    // Toggle status confirmation
    const toggleButtons = document.querySelectorAll('a[href*="toggle="]');
    toggleButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const subjectName = this.closest('tr').querySelector('td:nth-child(2)').textContent.trim();
            const isActive = this.classList.contains('btn-warning'); // Currently active, will be deactivated
            const action = isActive ? 'নিষ্ক্রিয় করতে' : 'সক্রিয় করতে';
            
            if (confirm(`আপনি কি নিশ্চিত যে "${subjectName}" বিষয়টি ${action} চান?`)) {
                window.location.href = this.href;
            }
        });
    });

    // Edit button - no confirmation needed, just navigate
    const editButtons = document.querySelectorAll('a[href*="edit_subject.php"]');
    editButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Add loading state
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            this.classList.add('disabled');
        });
    });

    // Assignment button - no confirmation needed, just navigate
    const assignButtons = document.querySelectorAll('a[href*="subject_assignment.php"]');
    assignButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Add loading state
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            this.classList.add('disabled');
        });
    });
}

// Search and Filter Functionality
function initializeSearchAndFilter() {
    const searchInput = document.getElementById('searchSubject');
    const statusFilter = document.getElementById('statusFilter');
    const categoryFilter = document.getElementById('categoryFilter');
    
    if (searchInput) {
        searchInput.addEventListener('input', filterSubjects);
    }
    
    if (statusFilter) {
        statusFilter.addEventListener('change', filterSubjects);
    }
    
    if (categoryFilter) {
        categoryFilter.addEventListener('change', filterSubjects);
    }
}

function filterSubjects() {
    const searchTerm = document.getElementById('searchSubject')?.value.toLowerCase() || '';
    const statusFilter = document.getElementById('statusFilter')?.value || '';
    const categoryFilter = document.getElementById('categoryFilter')?.value || '';
    
    const tableRows = document.querySelectorAll('#subjectsTable tbody tr');
    let visibleCount = 0;
    
    tableRows.forEach(row => {
        const subjectCode = row.querySelector('td:nth-child(1)')?.textContent.toLowerCase() || '';
        const subjectName = row.querySelector('td:nth-child(2)')?.textContent.toLowerCase() || '';
        const categoryBadge = row.querySelector('td:nth-child(3) .badge')?.textContent || '';
        const statusBadge = row.querySelector('td:nth-child(4) .badge')?.textContent || '';
        
        // Check search term
        const matchesSearch = searchTerm === '' || 
                            subjectCode.includes(searchTerm) || 
                            subjectName.includes(searchTerm);
        
        // Check status filter
        const matchesStatus = statusFilter === '' || 
                            (statusFilter === 'active' && statusBadge.includes('সক্রিয়')) ||
                            (statusFilter === 'inactive' && statusBadge.includes('নিষ্ক্রিয়'));
        
        // Check category filter
        const matchesCategory = categoryFilter === '' ||
                              (categoryFilter === 'required' && categoryBadge.includes('আবশ্যিক')) ||
                              (categoryFilter === 'optional' && categoryBadge.includes('ঐচ্ছিক')) ||
                              (categoryFilter === 'fourth' && categoryBadge.includes('চতুর্থ'));
        
        if (matchesSearch && matchesStatus && matchesCategory) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });
    
    // Update results count
    updateResultsCount(visibleCount);
}

function updateResultsCount(count) {
    const resultsElement = document.getElementById('resultsCount');
    if (resultsElement) {
        resultsElement.textContent = `${count} টি বিষয় পাওয়া গেছে`;
    }
}

// Tooltip Initialization
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"], [title]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Modal Functionality
function initializeModals() {
    // Add Subject Modal
    const addSubjectModal = document.getElementById('addSubjectModal');
    if (addSubjectModal) {
        addSubjectModal.addEventListener('shown.bs.modal', function () {
            document.getElementById('subject_name').focus();
        });
        
        addSubjectModal.addEventListener('hidden.bs.modal', function () {
            // Reset form
            const form = this.querySelector('form');
            if (form) {
                form.reset();
                // Clear any error states
                form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
                form.querySelectorAll('.invalid-feedback').forEach(el => el.remove());
            }
        });
    }
    
    // Form validation
    const addSubjectForm = document.querySelector('#addSubjectModal form');
    if (addSubjectForm) {
        addSubjectForm.addEventListener('submit', function(e) {
            if (!validateSubjectForm(this)) {
                e.preventDefault();
            }
        });
    }
}

// Form Validation
function validateSubjectForm(form) {
    let isValid = true;
    
    // Clear previous validation
    form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
    form.querySelectorAll('.invalid-feedback').forEach(el => el.remove());
    
    // Validate subject name
    const subjectName = form.querySelector('#subject_name');
    if (!subjectName.value.trim()) {
        showFieldError(subjectName, 'বিষয়ের নাম অবশ্যই পূরণ করতে হবে');
        isValid = false;
    }
    
    // Validate subject code
    const subjectCode = form.querySelector('#subject_code');
    if (!subjectCode.value.trim()) {
        showFieldError(subjectCode, 'বিষয় কোড অবশ্যই পূরণ করতে হবে');
        isValid = false;
    } else if (subjectCode.value.length < 2) {
        showFieldError(subjectCode, 'বিষয় কোড কমপক্ষে ২ অক্ষরের হতে হবে');
        isValid = false;
    }
    
    return isValid;
}

function showFieldError(field, message) {
    field.classList.add('is-invalid');
    const feedback = document.createElement('div');
    feedback.className = 'invalid-feedback';
    feedback.textContent = message;
    field.parentNode.appendChild(feedback);
}

// Utility Functions
function showAlert(message, type = 'success') {
    const alertContainer = document.querySelector('.alert-container') || document.querySelector('main');
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.insertBefore(alert, alertContainer.firstChild);
    
    // Auto dismiss after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}

// Export functions for global use
window.subjectsJS = {
    filterSubjects,
    showAlert,
    validateSubjectForm
};
