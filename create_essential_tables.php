<?php
// Database connection
require_once 'includes/dbh.inc.php';

echo "<h2>Creating Essential Tables for College Management System</h2>";

// First, check if the database exists
echo "<p>Checking database connection...</p>";
if ($conn->connect_error) {
    die("<p>Connection failed: " . $conn->connect_error . "</p>");
} else {
    echo "<p>Database connection successful!</p>";
}

// Create users table first
$usersTableQuery = "CREATE TABLE IF NOT EXISTS users (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    user_type ENUM('admin', 'teacher', 'student', 'staff') NOT NULL,
    email VARCHAR(100) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active'
)";

if ($conn->query($usersTableQuery)) {
    echo "<p>Users table created successfully!</p>";

    // Check if admin user exists
    $checkAdmin = $conn->query("SELECT * FROM users WHERE username='admin' AND user_type='admin'");

    if ($checkAdmin && $checkAdmin->num_rows == 0) {
        // Create default admin user
        $adminPassword = password_hash("admin123", PASSWORD_DEFAULT);
        $insertAdmin = $conn->prepare("INSERT INTO users (username, password, user_type, email) VALUES (?, ?, 'admin', '<EMAIL>')");
        $insertAdmin->bind_param("ss", $adminUsername, $adminPassword);

        $adminUsername = "admin";

        if ($insertAdmin->execute()) {
            echo "<p>Default admin user created successfully!</p>";
            echo "<p>Username: admin<br>Password: admin123</p>";
        } else {
            echo "<p>Error creating default admin user: " . $insertAdmin->error . "</p>";
        }

        $insertAdmin->close();
    } else {
        echo "<p>Admin user already exists.</p>";
    }
} else {
    echo "<p>Error creating users table: " . $conn->error . "</p>";
}

// Create departments table
$departmentsTableQuery = "CREATE TABLE IF NOT EXISTS departments (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    department_name VARCHAR(100) NOT NULL,
    description TEXT NULL,
    hod_id INT(11) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($departmentsTableQuery)) {
    echo "<p>Departments table created successfully!</p>";

    // Check if department_code column exists
    $checkColumn = $conn->query("SHOW COLUMNS FROM departments LIKE 'department_code'");
    if ($checkColumn->num_rows == 0) {
        // Add department_code column if it doesn't exist
        $alterTable = $conn->query("ALTER TABLE departments ADD COLUMN department_code VARCHAR(20) NOT NULL UNIQUE");
        if ($alterTable) {
            echo "<p>Added department_code column to departments table.</p>";
        } else {
            echo "<p>Error adding department_code column: " . $conn->error . "</p>";
        }
    }

    // Insert a default department
    $checkDepartment = $conn->query("SELECT * FROM departments LIMIT 1");
    if ($checkDepartment && $checkDepartment->num_rows == 0) {
        $insertDept = $conn->query("INSERT INTO departments (department_name, department_code, description)
                                  VALUES ('General', 'GEN-001', 'Default department')");
        if ($insertDept) {
            echo "<p>Default department created successfully!</p>";
        } else {
            echo "<p>Error creating default department: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Department already exists.</p>";
    }
} else {
    echo "<p>Error creating departments table: " . $conn->error . "</p>";
}

// Create teachers table
$teachersTableQuery = "CREATE TABLE IF NOT EXISTS teachers (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    teacher_id VARCHAR(20) NOT NULL UNIQUE,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) NULL,
    phone VARCHAR(20) NULL,
    gender ENUM('male', 'female', 'other') NOT NULL,
    dob DATE NULL,
    address TEXT NULL,
    city VARCHAR(50) NULL,
    state VARCHAR(50) NULL,
    postal_code VARCHAR(20) NULL,
    country VARCHAR(50) NULL DEFAULT 'Bangladesh',
    joining_date DATE NULL,
    profile_photo VARCHAR(255) NULL,
    department_id INT(11) NULL,
    user_id INT(11) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($teachersTableQuery)) {
    echo "<p>Teachers table created successfully!</p>";

    // Insert a sample teacher
    $checkTeacher = $conn->query("SELECT * FROM teachers LIMIT 1");
    if ($checkTeacher && $checkTeacher->num_rows == 0) {
        $deptId = 1; // Default department ID
        $insertTeacher = $conn->query("INSERT INTO teachers (teacher_id, first_name, last_name, email, phone, gender, joining_date, department_id)
                                     VALUES ('TCH-001', 'Rahim', 'Ahmed', '<EMAIL>', '01712345678', 'male', CURDATE(), $deptId)");
        if ($insertTeacher) {
            echo "<p>Sample teacher created successfully!</p>";
        } else {
            echo "<p>Error creating sample teacher: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Teacher already exists.</p>";
    }
} else {
    echo "<p>Error creating teachers table: " . $conn->error . "</p>";
}

// Create students table
$studentsTableQuery = "CREATE TABLE IF NOT EXISTS students (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id VARCHAR(20) NOT NULL UNIQUE,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) NULL,
    phone VARCHAR(20) NULL,
    gender ENUM('male', 'female', 'other') NOT NULL,
    dob DATE NULL,
    address TEXT NULL,
    city VARCHAR(50) NULL,
    state VARCHAR(50) NULL,
    postal_code VARCHAR(20) NULL,
    country VARCHAR(50) NULL DEFAULT 'Bangladesh',
    admission_date DATE NULL,
    profile_photo VARCHAR(255) NULL,
    department_id INT(11) NULL,
    class_id INT(11) NULL,
    batch VARCHAR(20) NULL,
    roll_no VARCHAR(20) NULL,
    guardian_name VARCHAR(100) NULL,
    guardian_phone VARCHAR(20) NULL,
    guardian_relation VARCHAR(50) NULL,
    user_id INT(11) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($studentsTableQuery)) {
    echo "<p>Students table created successfully!</p>";

    // Check if gender column exists
    $checkGender = $conn->query("SHOW COLUMNS FROM students LIKE 'gender'");
    if ($checkGender && $checkGender->num_rows == 0) {
        // Gender column doesn't exist, add it
        $alterTable = $conn->query("ALTER TABLE students ADD COLUMN gender ENUM('male', 'female', 'other') NOT NULL AFTER phone");
        if ($alterTable) {
            echo "<p>Added 'gender' column to students table.</p>";
        } else {
            echo "<p>Error adding 'gender' column: " . $conn->error . "</p>";
        }
    }

    // Insert a sample student
    $checkStudent = $conn->query("SELECT * FROM students LIMIT 1");
    if ($checkStudent && $checkStudent->num_rows == 0) {
        $deptId = 1; // Default department ID
        $insertStudent = $conn->query("INSERT INTO students (student_id, first_name, last_name, email, phone, gender, admission_date, department_id, batch, roll_no)
                                     VALUES ('STD-001', 'Karim', 'Uddin', '<EMAIL>', '01812345678', 'male', CURDATE(), $deptId, '2023', '101')");
        if ($insertStudent) {
            echo "<p>Sample student created successfully!</p>";
        } else {
            echo "<p>Error creating sample student: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Student already exists.</p>";
    }
} else {
    echo "<p>Error creating students table: " . $conn->error . "</p>";
}

// Create staff table
$staffTableQuery = "CREATE TABLE IF NOT EXISTS staff (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    staff_id VARCHAR(20) NOT NULL UNIQUE,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) NULL,
    phone VARCHAR(20) NULL,
    gender ENUM('male', 'female', 'other') NOT NULL,
    dob DATE NULL,
    address TEXT NULL,
    city VARCHAR(50) NULL,
    state VARCHAR(50) NULL,
    postal_code VARCHAR(20) NULL,
    country VARCHAR(50) NULL DEFAULT 'Bangladesh',
    joining_date DATE NULL,
    profile_photo VARCHAR(255) NULL,
    department_id INT(11) NULL,
    designation VARCHAR(100) NULL,
    user_id INT(11) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($staffTableQuery)) {
    echo "<p>Staff table created successfully!</p>";

    // Insert a sample staff
    $checkStaff = $conn->query("SELECT * FROM staff LIMIT 1");
    if ($checkStaff && $checkStaff->num_rows == 0) {
        $deptId = 1; // Default department ID
        $insertStaff = $conn->query("INSERT INTO staff (staff_id, first_name, last_name, email, phone, gender, joining_date, department_id, designation)
                                   VALUES ('STF-001', 'Jamal', 'Hossain', '<EMAIL>', '01912345678', 'male', CURDATE(), $deptId, 'Office Assistant')");
        if ($insertStaff) {
            echo "<p>Sample staff created successfully!</p>";
        } else {
            echo "<p>Error creating sample staff: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Staff already exists.</p>";
    }
} else {
    echo "<p>Error creating staff table: " . $conn->error . "</p>";
}

// Create classes table
$classesTableQuery = "CREATE TABLE IF NOT EXISTS classes (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    class_name VARCHAR(100) NOT NULL,
    class_code VARCHAR(20) NOT NULL UNIQUE,
    department_id INT(11) NULL,
    class_teacher_id INT(11) NULL,
    description TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($classesTableQuery)) {
    echo "<p>Classes table created successfully!</p>";

    // Insert a sample class
    $checkClass = $conn->query("SELECT * FROM classes LIMIT 1");
    if ($checkClass && $checkClass->num_rows == 0) {
        $deptId = 1; // Default department ID
        $teacherId = 1; // First teacher ID
        $insertClass = $conn->query("INSERT INTO classes (class_name, class_code, department_id, class_teacher_id, description)
                                   VALUES ('Class XI', 'XI-SCI', $deptId, $teacherId, 'Science Group - Class XI')");
        if ($insertClass) {
            echo "<p>Sample class created successfully!</p>";
        } else {
            echo "<p>Error creating sample class: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Class already exists.</p>";
    }
} else {
    echo "<p>Error creating classes table: " . $conn->error . "</p>";
}

// Create exams table
$examsTableQuery = "CREATE TABLE IF NOT EXISTS exams (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    exam_name VARCHAR(100) NOT NULL,
    exam_code VARCHAR(20) NOT NULL UNIQUE,
    exam_date DATE NOT NULL,
    start_time TIME NULL,
    end_time TIME NULL,
    total_marks INT(11) NOT NULL DEFAULT 100,
    passing_marks INT(11) NOT NULL DEFAULT 33,
    class_id INT(11) NULL,
    subject_id INT(11) NULL,
    description TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($examsTableQuery)) {
    echo "<p>Exams table created successfully!</p>";
} else {
    echo "<p>Error creating exams table: " . $conn->error . "</p>";
}

echo "<p><strong>All essential tables have been created successfully!</strong></p>";
echo "<p>Note: Foreign key constraints have been omitted for simplicity. The tables will still work for basic functionality.</p>";
echo "<p><a href='index.php'>Return to homepage</a></p>";
echo "<p><a href='admin/dashboard.php'>Go to Admin Dashboard</a></p>";

$conn->close();
?>





