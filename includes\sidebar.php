<?php
// Get the current page filename
$currentPage = basename($_SERVER['PHP_SELF']);
?>

<div class="col-md-3 col-lg-2 sidebar">
    <div class="text-center mb-4">
        <h3>মেনু</h3>
    </div>
    <ul class="nav flex-column">
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage == 'index.php') ? 'active' : ''; ?>" href="index.php">
                <i class="fas fa-home me-2"></i> হোম
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage == 'students.php') ? 'active' : ''; ?>" href="students.php">
                <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী অনুসন্ধান
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage == 'subjects.php') ? 'active' : ''; ?>" href="subjects.php">
                <i class="fas fa-book me-2"></i> বিষয়সমূহ
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage == 'teachers.php') ? 'active' : ''; ?>" href="teachers.php">
                <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষকবৃন্দ
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage == 'notices.php') ? 'active' : ''; ?>" href="notices.php">
                <i class="fas fa-bullhorn me-2"></i> নোটিশ
            </a>
        </li>
        <?php if (isset($_SESSION['userId'])): ?>
            <?php if ($_SESSION['userType'] == 'student'): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo ($currentPage == 'student/dashboard.php') ? 'active' : ''; ?>" href="student/dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo ($currentPage == 'student/subject_selection.php') ? 'active' : ''; ?>" href="student/subject_selection.php">
                        <i class="fas fa-tasks me-2"></i> বিষয় নির্বাচন
                    </a>
                </li>

            <?php elseif ($_SESSION['userType'] == 'admin'): ?>
                <li class="nav-item">
                    <a class="nav-link" href="admin/dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i> অ্যাডমিন প্যানেল
                    </a>
                </li>
            <?php endif; ?>
            <li class="nav-item">
                <a class="nav-link" href="includes/logout.inc.php">
                    <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                </a>
            </li>
        <?php else: ?>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage == 'login.php') ? 'active' : ''; ?>" href="login.php">
                    <i class="fas fa-sign-in-alt me-2"></i> লগইন
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage == 'register.php') ? 'active' : ''; ?>" href="register.php">
                    <i class="fas fa-user-plus me-2"></i> রেজিস্টার
                </a>
            </li>
        <?php endif; ?>
    </ul>
</div>
