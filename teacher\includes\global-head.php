<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="../css/hind-siliguri.css">

<style>
    body {
        font-family: 'Hind Siliguri', sans-serif;
        background-color: #f8f9fa;
    }
    
    .sidebar {
        background-color: #343a40;
        color: #fff;
        min-height: 100vh;
        padding-top: 20px;
    }
    
    .sidebar h3 {
        color: #ffffff;
        padding: 10px 15px;
        margin-bottom: 20px;
        font-size: 1.5rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .sidebar .nav-link {
        color: rgba(255, 255, 255, 0.75) !important;
        padding: 10px 15px;
        transition: all 0.3s ease;
    }
    
    .sidebar .nav-link:hover {
        color: #ffffff !important;
        background-color: rgba(255, 255, 255, 0.1);
    }
    
    .sidebar .nav-link.active {
        color: #ffffff !important;
        background-color: #007bff !important;
    }
    
    .main-content {
        padding: 20px;
    }
    
    .card {
        border-radius: 10px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
        margin-bottom: 20px;
    }
    
    .card-header {
        border-radius: 10px 10px 0 0;
        padding: 15px 20px;
    }
    
    .profile-header {
        border-radius: 10px;
        overflow: hidden;
    }
    
    .profile-img {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        margin-top: 20px;
        object-fit: cover;
        border: 4px solid #fff;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
    
    .profile-info {
        padding: 20px;
    }
    
    .table th {
        background-color: #f8f9fa;
    }
    
    @media (max-width: 767.98px) {
        .sidebar {
            min-height: auto;
        }
        
        .profile-img {
            width: 80px;
            height: 80px;
            margin: 10px auto;
        }
    }
</style>
