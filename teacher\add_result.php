<?php
session_start();

// Check if user is logged in and is a teacher
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'teacher') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get teacher information with department name
$userId = $_SESSION['userId'];
$username = $_SESSION['username'] ?? '';

// First try to get teacher by user_id
$sql = "SELECT t.*, d.department_name
        FROM teachers t
        LEFT JOIN departments d ON t.department_id = d.id
        WHERE t.user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();

// If no results, try to get by username
if ($result->num_rows === 0 && !empty($username)) {
    $sql = "SELECT t.*, d.department_name
            FROM teachers t
            LEFT JOIN departments d ON t.department_id = d.id
            WHERE t.username = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
}

$teacher = $result->fetch_assoc();

// If still no teacher found, redirect to login
if (!$teacher) {
    header("Location: ../index.php");
    exit();
}

// Create results table if it doesn't exist
$resultsTableQuery = "CREATE TABLE IF NOT EXISTS results (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    exam_id INT(11) NOT NULL,
    student_id INT(11) NOT NULL,
    subject_id INT(11),
    marks_obtained FLOAT NOT NULL,
    total_marks FLOAT NOT NULL,
    grade VARCHAR(10),
    remarks TEXT,
    date DATE NOT NULL,
    created_by INT(11),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";
$conn->query($resultsTableQuery);

// Check if subject_id column exists in results table
$checkSubjectIdColumnQuery = "SHOW COLUMNS FROM results LIKE 'subject_id'";
$subjectIdColumnResult = $conn->query($checkSubjectIdColumnQuery);
if ($subjectIdColumnResult->num_rows == 0) {
    // Add subject_id column if it doesn't exist
    $addSubjectIdColumnQuery = "ALTER TABLE results ADD COLUMN subject_id INT(11) NULL";
    $conn->query($addSubjectIdColumnQuery);
}

// Get exams for dropdown
try {
    // Check if subjects table exists
    $checkSubjectsTableQuery = "SHOW TABLES LIKE 'subjects'";
    $subjectsTableResult = $conn->query($checkSubjectsTableQuery);
    $subjectsTableExists = $subjectsTableResult->num_rows > 0;

    // Check if classes table exists
    $checkClassesTableQuery = "SHOW TABLES LIKE 'classes'";
    $classesTableResult = $conn->query($checkClassesTableQuery);
    $classesTableExists = $classesTableResult->num_rows > 0;

    // Build query based on existing tables
    $examsQuery = "SELECT e.id, e.exam_name, e.exam_type, e.exam_date, e.total_marks";

    if ($subjectsTableExists) {
        $examsQuery .= ", s.subject_name, s.subject_code";
    }

    if ($classesTableExists) {
        $examsQuery .= ", c.class_name";
    }

    $examsQuery .= " FROM exams e";

    if ($subjectsTableExists) {
        $examsQuery .= " LEFT JOIN subjects s ON e.subject_id = s.id";
    }

    if ($classesTableExists) {
        $examsQuery .= " LEFT JOIN classes c ON e.class_id = c.id";
    }

    // Filter by teacher's department
    if (!empty($teacher['department_id'])) {
        $examsQuery .= " WHERE e.department_id = ? OR e.created_by = ?";
        $stmt = $conn->prepare($examsQuery);
        $stmt->bind_param("ii", $teacher['department_id'], $teacher['id']);
    } else {
        $examsQuery .= " WHERE e.created_by = ?";
        $stmt = $conn->prepare($examsQuery);
        $stmt->bind_param("i", $teacher['id']);
    }

    $examsQuery .= " ORDER BY e.exam_date DESC";
    $stmt->execute();
    $exams = $stmt->get_result();
} catch (Exception $e) {
    $error_msg = "পরীক্ষার তথ্য লোড করতে সমস্যা হয়েছে: " . $e->getMessage();
    $exams = null;
}

// Get students for dropdown
try {
    // Check if students table exists
    $checkStudentsTableQuery = "SHOW TABLES LIKE 'students'";
    $studentsTableResult = $conn->query($checkStudentsTableQuery);

    if ($studentsTableResult->num_rows > 0) {
        // If students table exists, get students for this teacher's department
        $studentsQuery = "SELECT s.id, s.student_id, s.first_name, s.last_name, s.roll_number, s.class_id, c.class_name
                         FROM students s
                         LEFT JOIN classes c ON s.class_id = c.id
                         WHERE 1=1";

        $params = [];
        $types = "";

        // If teacher has department_id, filter by department
        if (!empty($teacher['department_id'])) {
            $studentsQuery .= " AND s.department_id = ?";
            $params[] = $teacher['department_id'];
            $types .= "i";
        }

        $studentsQuery .= " ORDER BY s.roll_number, s.first_name, s.last_name";

        $stmt = $conn->prepare($studentsQuery);

        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }

        $stmt->execute();
        $students = $stmt->get_result();
    } else {
        $students = null;
    }
} catch (Exception $e) {
    $error_msg = "শিক্ষার্থীদের তথ্য লোড করতে সমস্যা হয়েছে: " . $e->getMessage();
    $students = null;
}

// Handle result submission
$success_msg = '';
$error_msg = '';

if (isset($_POST['submit_result'])) {
    $examId = intval($_POST['exam_id']);
    $studentId = intval($_POST['student_id']);
    $marksObtained = floatval($_POST['marks_obtained']);
    $remarks = $conn->real_escape_string($_POST['remarks'] ?? '');
    $date = date('Y-m-d');
    $createdBy = $teacher['id'];

    // Get exam details to get total marks and subject_id
    $examQuery = "SELECT * FROM exams WHERE id = ?";
    $stmt = $conn->prepare($examQuery);
    $stmt->bind_param("i", $examId);
    $stmt->execute();
    $examResult = $stmt->get_result();

    if ($examResult->num_rows === 0) {
        $error_msg = "নির্বাচিত পরীক্ষা খুঁজে পাওয়া যায়নি";
    } else {
        $exam = $examResult->fetch_assoc();
        $totalMarks = $exam['total_marks'];
        $subjectId = $exam['subject_id'] ?? null;

        // Check if result already exists for this student and exam
        $checkQuery = "SELECT id FROM results WHERE exam_id = ? AND student_id = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param("ii", $examId, $studentId);
        $stmt->execute();
        $checkResult = $stmt->get_result();

        if ($checkResult->num_rows > 0) {
            $resultRow = $checkResult->fetch_assoc();
            $resultId = $resultRow['id'];

            // Update existing result
            $updateQuery = "UPDATE results SET marks_obtained = ?, remarks = ? WHERE id = ?";
            $stmt = $conn->prepare($updateQuery);
            $stmt->bind_param("dsi", $marksObtained, $remarks, $resultId);

            if ($stmt->execute()) {
                $success_msg = "ফলাফল সফলভাবে আপডেট করা হয়েছে";
            } else {
                $error_msg = "ফলাফল আপডেট করতে সমস্যা হয়েছে: " . $conn->error;
            }
        } else {
            // Calculate grade based on marks percentage
            $percentage = ($marksObtained / $totalMarks) * 100;
            $grade = '';

            if ($percentage >= 80) {
                $grade = 'A+';
            } elseif ($percentage >= 70) {
                $grade = 'A';
            } elseif ($percentage >= 60) {
                $grade = 'A-';
            } elseif ($percentage >= 50) {
                $grade = 'B';
            } elseif ($percentage >= 40) {
                $grade = 'C';
            } elseif ($percentage >= 33) {
                $grade = 'D';
            } else {
                $grade = 'F';
            }

            // Insert new result
            $insertQuery = "INSERT INTO results (exam_id, student_id, subject_id, marks_obtained, total_marks, grade, remarks, date, created_by)
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param("iiiddsssi", $examId, $studentId, $subjectId, $marksObtained, $totalMarks, $grade, $remarks, $date, $createdBy);

            if ($stmt->execute()) {
                $success_msg = "ফলাফল সফলভাবে যোগ করা হয়েছে";
            } else {
                $error_msg = "ফলাফল যোগ করতে সমস্যা হয়েছে: " . $conn->error;
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include __DIR__ . '/includes/global-head.php'; ?>
    <title>Add Single Result - Teacher Panel</title>

    <style>
        body, html, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, select, textarea, label, li, th, td {
            font-family: 'Hind Siliguri', sans-serif;
        }

        .form-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            padding: 30px;
            margin-bottom: 30px;
        }

        .form-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        .form-section h4 {
            margin-bottom: 20px;
            color: #333;
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>Teacher Panel</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user me-2"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> Students
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> Courses
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> Exams
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> Results
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">
                            <i class="fas fa-calendar-check me-2"></i> Attendance
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2>একক ফলাফল যোগ করুন</h2>
                            <div>
                                <a href="exams.php" class="btn btn-secondary me-2">
                                    <i class="fas fa-arrow-left me-2"></i> পরীক্ষা তালিকায় ফিরে যান
                                </a>
                                <a href="results.php" class="btn btn-info">
                                    <i class="fas fa-list me-2"></i> সকল ফলাফল দেখুন
                                </a>
                            </div>
                        </div>

                        <?php if (!empty($success_msg)): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <?php echo $success_msg; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($error_msg)): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <?php echo $error_msg; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>

                        <div class="card form-card">
                            <form action="" method="post" id="resultForm">
                                <div class="form-section">
                                    <h4>পরীক্ষা এবং শিক্ষার্থী নির্বাচন করুন</h4>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="exam_id" class="form-label">পরীক্ষা*</label>
                                            <select class="form-select" id="exam_id" name="exam_id" required>
                                                <option value="">পরীক্ষা নির্বাচন করুন</option>
                                                <?php if ($exams && $exams->num_rows > 0): ?>
                                                    <?php while ($exam = $exams->fetch_assoc()): ?>
                                                        <option value="<?php echo $exam['id']; ?>" data-total-marks="<?php echo $exam['total_marks']; ?>">
                                                            <?php
                                                                $examInfo = $exam['exam_name'];
                                                                if (isset($exam['subject_name'])) {
                                                                    $examInfo .= ' - ' . $exam['subject_name'];
                                                                }
                                                                if (isset($exam['class_name'])) {
                                                                    $examInfo .= ' (' . $exam['class_name'] . ')';
                                                                }
                                                                $examInfo .= ' - ' . date('d/m/Y', strtotime($exam['exam_date']));
                                                                echo htmlspecialchars($examInfo);
                                                            ?>
                                                        </option>
                                                    <?php endwhile; ?>
                                                <?php endif; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="student_id" class="form-label">শিক্ষার্থী*</label>
                                            <select class="form-select" id="student_id" name="student_id" required>
                                                <option value="">শিক্ষার্থী নির্বাচন করুন</option>
                                                <?php if ($students && $students->num_rows > 0): ?>
                                                    <?php while ($student = $students->fetch_assoc()): ?>
                                                        <option value="<?php echo $student['id']; ?>" data-class="<?php echo $student['class_id']; ?>">
                                                            <?php
                                                                $studentInfo = $student['first_name'] . ' ' . $student['last_name'];
                                                                if (!empty($student['roll_number'])) {
                                                                    $studentInfo .= ' (Roll: ' . $student['roll_number'] . ')';
                                                                }
                                                                if (!empty($student['student_id'])) {
                                                                    $studentInfo .= ' - ID: ' . $student['student_id'];
                                                                }
                                                                if (!empty($student['class_name'])) {
                                                                    $studentInfo .= ' - ' . $student['class_name'];
                                                                }
                                                                echo htmlspecialchars($studentInfo);
                                                            ?>
                                                        </option>
                                                    <?php endwhile; ?>
                                                <?php endif; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-section">
                                    <h4>ফলাফল বিবরণ</h4>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="marks_obtained" class="form-label">প্রাপ্ত নম্বর*</label>
                                            <input type="number" class="form-control" id="marks_obtained" name="marks_obtained" min="0" step="0.01" required>
                                            <div class="form-text" id="total-marks-info"></div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="remarks" class="form-label">মন্তব্য</label>
                                            <textarea class="form-control" id="remarks" name="remarks" rows="3" placeholder="শিক্ষার্থীর ফলাফল সম্পর্কে মন্তব্য (ঐচ্ছিক)"></textarea>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <a href="results.php" class="btn btn-secondary">বাতিল করুন</a>
                                    <button type="submit" name="submit_result" class="btn btn-primary">ফলাফল সংরক্ষণ করুন</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const examSelect = document.getElementById('exam_id');
            const studentSelect = document.getElementById('student_id');
            const marksInput = document.getElementById('marks_obtained');
            const totalMarksInfo = document.getElementById('total-marks-info');
            const form = document.getElementById('resultForm');

            // Update total marks info when exam is selected
            examSelect.addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                if (selectedOption.value) {
                    const totalMarks = selectedOption.getAttribute('data-total-marks');
                    marksInput.max = totalMarks;
                    totalMarksInfo.textContent = `মোট নম্বর: ${totalMarks}`;
                } else {
                    totalMarksInfo.textContent = '';
                }
            });

            // Filter students based on selected exam's class
            examSelect.addEventListener('change', function() {
                // This would require additional AJAX to get exam details
                // For now, we'll just show all students
            });

            // Validate form on submit
            form.addEventListener('submit', function(e) {
                const selectedExam = examSelect.value;
                const selectedStudent = studentSelect.value;
                const marksValue = parseFloat(marksInput.value);
                const maxMarks = parseFloat(marksInput.max);

                if (!selectedExam) {
                    e.preventDefault();
                    alert('অনুগ্রহ করে একটি পরীক্ষা নির্বাচন করুন');
                    return;
                }

                if (!selectedStudent) {
                    e.preventDefault();
                    alert('অনুগ্রহ করে একজন শিক্ষার্থী নির্বাচন করুন');
                    return;
                }

                if (isNaN(marksValue) || marksValue < 0 || (maxMarks && marksValue > maxMarks)) {
                    e.preventDefault();
                    alert(`অনুগ্রহ করে সঠিক নম্বর দিন। নম্বর 0 থেকে ${maxMarks} এর মধ্যে হতে হবে।`);
                    return;
                }
            });
        });
    </script>
</body>
</html>
