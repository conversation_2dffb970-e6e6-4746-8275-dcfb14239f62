<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$successMessages = [];
$errorMessages = [];

// Get department ID from URL
$departmentId = $_GET['department_id'] ?? 0;

if (empty($departmentId) || !is_numeric($departmentId)) {
    header("Location: departments.php");
    exit();
}

// Get department details
$departmentQuery = "SELECT * FROM departments WHERE id = ?";
$stmt = $conn->prepare($departmentQuery);
$stmt->bind_param("i", $departmentId);
$stmt->execute();
$department = $stmt->get_result()->fetch_assoc();

if (!$department) {
    header("Location: departments.php");
    exit();
}

// Check if subject_departments table exists
$checkSubjectDeptTable = $conn->query("SHOW TABLES LIKE 'subject_departments'");
$subjectDeptTableExists = $checkSubjectDeptTable->num_rows > 0;

// Create subject_departments table if it doesn't exist
if (!$subjectDeptTableExists) {
    $createTableQuery = "CREATE TABLE IF NOT EXISTS subject_departments (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        subject_id INT(11) NOT NULL,
        department_id INT(11) NOT NULL,
        UNIQUE KEY (subject_id, department_id),
        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
        FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE
    )";
    
    if ($conn->query($createTableQuery)) {
        $successMessages[] = "বিষয়-বিভাগ ম্যাপিং টেবিল সফলভাবে তৈরি করা হয়েছে।";
        $subjectDeptTableExists = true;
    } else {
        $errorMessages[] = "টেবিল তৈরি করতে সমস্যা হয়েছে: " . $conn->error;
    }
}

// Get all subjects that are not already assigned to this department
$subjectsQuery = "SELECT s.* FROM subjects s 
                 WHERE s.is_active = 1 
                 AND s.id NOT IN (
                     SELECT sd.subject_id FROM subject_departments sd 
                     WHERE sd.department_id = ?
                 )
                 ORDER BY s.subject_name";
$stmt = $conn->prepare($subjectsQuery);
$stmt->bind_param("i", $departmentId);
$stmt->execute();
$availableSubjects = $stmt->get_result();

// Get subjects already assigned to this department
$assignedSubjectsQuery = "SELECT s.* FROM subjects s 
                         JOIN subject_departments sd ON s.id = sd.subject_id
                         WHERE sd.department_id = ? AND s.is_active = 1
                         ORDER BY s.subject_name";
$stmt = $conn->prepare($assignedSubjectsQuery);
$stmt->bind_param("i", $departmentId);
$stmt->execute();
$assignedSubjects = $stmt->get_result();

// Handle form submission
if (isset($_POST['add_subjects'])) {
    $selectedSubjects = $_POST['subject_ids'] ?? [];
    
    if (empty($selectedSubjects)) {
        $errorMessages[] = "দয়া করে কমপক্ষে একটি বিষয় নির্বাচন করুন।";
    } else {
        // Begin transaction
        $conn->begin_transaction();
        
        try {
            // Insert new entries
            $insertQuery = "INSERT IGNORE INTO subject_departments (department_id, subject_id) VALUES (?, ?)";
            $stmt = $conn->prepare($insertQuery);
            
            $successCount = 0;
            foreach ($selectedSubjects as $subjectId) {
                $stmt->bind_param("ii", $departmentId, $subjectId);
                if ($stmt->execute()) {
                    $successCount++;
                }
            }
            
            $conn->commit();
            
            if ($successCount > 0) {
                $successMessages[] = "$successCount টি বিষয় সফলভাবে বিভাগে যোগ করা হয়েছে।";
                // Redirect to refresh the page and show updated lists
                header("Location: add_subject_to_department.php?department_id=$departmentId&success=1");
                exit();
            } else {
                $errorMessages[] = "কোন নতুন বিষয় যোগ করা হয়নি।";
            }
        } catch (Exception $e) {
            $conn->rollback();
            $errorMessages[] = "বিষয় যোগ করতে সমস্যা হয়েছে: " . $e->getMessage();
        }
    }
}

// Handle remove subject
if (isset($_GET['remove_subject']) && is_numeric($_GET['remove_subject'])) {
    $subjectId = $_GET['remove_subject'];
    
    $deleteQuery = "DELETE FROM subject_departments WHERE department_id = ? AND subject_id = ?";
    $stmt = $conn->prepare($deleteQuery);
    $stmt->bind_param("ii", $departmentId, $subjectId);
    
    if ($stmt->execute() && $stmt->affected_rows > 0) {
        $successMessages[] = "বিষয় সফলভাবে বিভাগ থেকে সরানো হয়েছে।";
        // Redirect to refresh the page
        header("Location: add_subject_to_department.php?department_id=$departmentId&success=1");
        exit();
    } else {
        $errorMessages[] = "বিষয় সরাতে সমস্যা হয়েছে।";
    }
}

// Check for success parameter in URL
if (isset($_GET['success']) && $_GET['success'] == 1) {
    $successMessages[] = "অপারেশন সফলভাবে সম্পন্ন হয়েছে।";
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>বিভাগে বিষয় যোগ করুন - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .subject-card {
            transition: all 0.3s ease;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 15px;
        }
        .subject-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .subject-card.selected {
            border-color: #28a745;
            background-color: rgba(40, 167, 69, 0.1);
        }
        .subject-list {
            max-height: 600px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="departments.php">
                            <i class="fas fa-building me-2"></i> বিভাগ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subject_categories.php">
                            <i class="fas fa-tags me-2"></i> বিষয় ক্যাটাগরি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sessions.php">
                            <i class="fas fa-calendar-alt me-2"></i> সেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>বিভাগে বিষয় যোগ করুন</h2>
                        <p class="text-muted"><?php echo htmlspecialchars($department['department_name']); ?> বিভাগে বিষয় যোগ করুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="create_department_subject_types.php?department_id=<?php echo $departmentId; ?>" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>বিষয়ের ধরন পৃষ্ঠায় ফিরে যান
                        </a>
                    </div>
                </div>

                <?php if (!empty($successMessages)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <h5 class="alert-heading"><i class="fas fa-check-circle me-2"></i>সফল!</h5>
                        <ul class="mb-0">
                            <?php foreach ($successMessages as $message): ?>
                                <li><?php echo $message; ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($errorMessages)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <h5 class="alert-heading"><i class="fas fa-exclamation-circle me-2"></i>ত্রুটি!</h5>
                        <ul class="mb-0">
                            <?php foreach ($errorMessages as $message): ?>
                                <li><?php echo $message; ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- Available Subjects -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0"><i class="fas fa-list me-2"></i>উপলব্ধ বিষয়সমূহ</h5>
                            </div>
                            <div class="card-body">
                                <?php if ($availableSubjects && $availableSubjects->num_rows > 0): ?>
                                    <form method="POST" action="add_subject_to_department.php?department_id=<?php echo $departmentId; ?>">
                                        <div class="mb-3">
                                            <div class="input-group">
                                                <input type="text" id="subjectSearch" class="form-control" placeholder="বিষয় খুঁজুন...">
                                                <button type="button" class="btn btn-outline-secondary" onclick="clearSearch()">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <div class="subject-list">
                                            <?php while ($subject = $availableSubjects->fetch_assoc()): ?>
                                                <div class="card subject-card">
                                                    <div class="card-body">
                                                        <div class="form-check">
                                                            <input class="form-check-input subject-checkbox" type="checkbox" name="subject_ids[]" value="<?php echo $subject['id']; ?>" id="subject<?php echo $subject['id']; ?>">
                                                            <label class="form-check-label" for="subject<?php echo $subject['id']; ?>">
                                                                <h5 class="card-title"><?php echo htmlspecialchars($subject['subject_name']); ?></h5>
                                                                <p class="card-text text-muted"><?php echo htmlspecialchars($subject['subject_code']); ?></p>
                                                                <span class="badge bg-<?php echo ($subject['category'] == 'required') ? 'primary' : (($subject['category'] == 'optional') ? 'success' : 'warning'); ?>">
                                                                    <?php echo ($subject['category'] == 'required') ? 'আবশ্যিক' : (($subject['category'] == 'optional') ? 'ঐচ্ছিক' : '৪র্থ'); ?>
                                                                </span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endwhile; ?>
                                        </div>
                                        
                                        <div class="mt-3">
                                            <button type="submit" name="add_subjects" class="btn btn-success">
                                                <i class="fas fa-plus-circle me-2"></i>নির্বাচিত বিষয় যোগ করুন
                                            </button>
                                            <button type="button" class="btn btn-outline-primary ms-2" id="selectAllBtn">
                                                <i class="fas fa-check-square me-2"></i>সব নির্বাচন করুন
                                            </button>
                                        </div>
                                    </form>
                                <?php else: ?>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>কোন উপলব্ধ বিষয় নেই। সব বিষয় ইতিমধ্যে এই বিভাগে যোগ করা হয়েছে।
                                    </div>
                                    <a href="add_subject.php" class="btn btn-primary">
                                        <i class="fas fa-plus-circle me-2"></i>নতুন বিষয় যোগ করুন
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Assigned Subjects -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0"><i class="fas fa-check-circle me-2"></i>বিভাগে যোগকৃত বিষয়সমূহ</h5>
                            </div>
                            <div class="card-body">
                                <?php if ($assignedSubjects && $assignedSubjects->num_rows > 0): ?>
                                    <div class="mb-3">
                                        <div class="input-group">
                                            <input type="text" id="assignedSearch" class="form-control" placeholder="যোগকৃত বিষয় খুঁজুন...">
                                            <button type="button" class="btn btn-outline-secondary" onclick="clearAssignedSearch()">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div class="subject-list">
                                        <?php while ($subject = $assignedSubjects->fetch_assoc()): ?>
                                            <div class="card subject-card assigned-subject">
                                                <div class="card-body">
                                                    <div class="d-flex justify-content-between align-items-start">
                                                        <div>
                                                            <h5 class="card-title"><?php echo htmlspecialchars($subject['subject_name']); ?></h5>
                                                            <p class="card-text text-muted"><?php echo htmlspecialchars($subject['subject_code']); ?></p>
                                                            <span class="badge bg-<?php echo ($subject['category'] == 'required') ? 'primary' : (($subject['category'] == 'optional') ? 'success' : 'warning'); ?>">
                                                                <?php echo ($subject['category'] == 'required') ? 'আবশ্যিক' : (($subject['category'] == 'optional') ? 'ঐচ্ছিক' : '৪র্থ'); ?>
                                                            </span>
                                                        </div>
                                                        <a href="add_subject_to_department.php?department_id=<?php echo $departmentId; ?>&remove_subject=<?php echo $subject['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই বিষয়টি সরাতে চান?');">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endwhile; ?>
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>এই বিভাগে কোন বিষয় যোগ করা হয়নি।
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Next Steps -->
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0"><i class="fas fa-arrow-right me-2"></i>পরবর্তী ধাপ</h5>
                            </div>
                            <div class="card-body">
                                <p>বিভাগে বিষয় যোগ করার পর, আপনি প্রতিটি বিষয়ের ধরন (আবশ্যিক/ঐচ্ছিক/৪র্থ) নির্ধারণ করতে পারেন।</p>
                                <a href="create_department_subject_types.php?department_id=<?php echo $departmentId; ?>" class="btn btn-primary">
                                    <i class="fas fa-cog me-2"></i>বিষয়ের ধরন নির্ধারণ করুন
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Subject search functionality
            const subjectSearch = document.getElementById('subjectSearch');
            if (subjectSearch) {
                subjectSearch.addEventListener('keyup', function() {
                    const searchTerm = this.value.toLowerCase();
                    const subjectCards = document.querySelectorAll('.subject-card:not(.assigned-subject)');
                    
                    subjectCards.forEach(card => {
                        const subjectName = card.querySelector('.card-title').textContent.toLowerCase();
                        const subjectCode = card.querySelector('.card-text').textContent.toLowerCase();
                        
                        if (subjectName.includes(searchTerm) || subjectCode.includes(searchTerm)) {
                            card.style.display = '';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            }
            
            // Assigned subject search
            const assignedSearch = document.getElementById('assignedSearch');
            if (assignedSearch) {
                assignedSearch.addEventListener('keyup', function() {
                    const searchTerm = this.value.toLowerCase();
                    const subjectCards = document.querySelectorAll('.assigned-subject');
                    
                    subjectCards.forEach(card => {
                        const subjectName = card.querySelector('.card-title').textContent.toLowerCase();
                        const subjectCode = card.querySelector('.card-text').textContent.toLowerCase();
                        
                        if (subjectName.includes(searchTerm) || subjectCode.includes(searchTerm)) {
                            card.style.display = '';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            }
            
            // Select all button
            const selectAllBtn = document.getElementById('selectAllBtn');
            if (selectAllBtn) {
                selectAllBtn.addEventListener('click', function() {
                    const checkboxes = document.querySelectorAll('.subject-checkbox');
                    const allChecked = Array.from(checkboxes).every(cb => cb.checked);
                    
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = !allChecked;
                        updateCardStyle(checkbox);
                    });
                    
                    this.innerHTML = allChecked ? 
                        '<i class="fas fa-check-square me-2"></i>সব নির্বাচন করুন' : 
                        '<i class="fas fa-square me-2"></i>সব অনির্বাচন করুন';
                });
            }
            
            // Update card style when checkbox is clicked
            const subjectCheckboxes = document.querySelectorAll('.subject-checkbox');
            subjectCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateCardStyle(this);
                });
                
                // Initialize card styles
                updateCardStyle(checkbox);
            });
            
            function updateCardStyle(checkbox) {
                const card = checkbox.closest('.subject-card');
                if (checkbox.checked) {
                    card.classList.add('selected');
                } else {
                    card.classList.remove('selected');
                }
            }
        });
        
        function clearSearch() {
            const searchInput = document.getElementById('subjectSearch');
            searchInput.value = '';
            searchInput.dispatchEvent(new Event('keyup'));
        }
        
        function clearAssignedSearch() {
            const searchInput = document.getElementById('assignedSearch');
            searchInput.value = '';
            searchInput.dispatchEvent(new Event('keyup'));
        }
    </script>
</body>
</html>
