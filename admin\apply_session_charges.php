<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Process session charge application
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['apply_charges'])) {
    $sessionId = $_POST['session_id'] ?? 0;
    $classId = $_POST['class_id'] ?? 0;
    $departmentId = $_POST['department_id'] ?? 0;
    $studentIds = $_POST['student_ids'] ?? [];
    $chargeIds = $_POST['charge_ids'] ?? [];
    $dueDate = $_POST['due_date'] ?? date('Y-m-d');

    if ($sessionId > 0 && !empty($chargeIds) && (!empty($studentIds) || $classId > 0 || $departmentId > 0)) {
        // Start transaction
        $conn->begin_transaction();

        try {
            $successCount = 0;
            $existingCount = 0;
            $errorCount = 0;

            // Get students based on filters
            $studentsQuery = "SELECT id FROM students WHERE 1=1";
            $studentParams = [];
            $studentParamTypes = "";

            if (!empty($studentIds)) {
                $placeholders = str_repeat('?,', count($studentIds) - 1) . '?';
                $studentsQuery .= " AND id IN ($placeholders)";
                foreach ($studentIds as $id) {
                    $studentParams[] = $id;
                    $studentParamTypes .= "i";
                }
            } else {
                if ($sessionId > 0) {
                    $studentsQuery .= " AND session_id = ?";
                    $studentParams[] = $sessionId;
                    $studentParamTypes .= "i";
                }

                if ($classId > 0) {
                    $studentsQuery .= " AND class_id = ?";
                    $studentParams[] = $classId;
                    $studentParamTypes .= "i";
                }

                if ($departmentId > 0) {
                    $studentsQuery .= " AND department_id = ?";
                    $studentParams[] = $departmentId;
                    $studentParamTypes .= "i";
                }
            }

            // Prepare and execute the query to get students
            $stmt = $conn->prepare($studentsQuery);
            if (!empty($studentParams)) {
                $stmt->bind_param($studentParamTypes, ...$studentParams);
            }
            $stmt->execute();
            $studentsResult = $stmt->get_result();

            if ($studentsResult->num_rows === 0) {
                throw new Exception("কোন শিক্ষার্থী পাওয়া যায়নি!");
            }

            // Get all students
            $allStudentIds = [];
            while ($student = $studentsResult->fetch_assoc()) {
                $allStudentIds[] = $student['id'];
            }

            // Get session charges
            $chargesQuery = "SELECT sc.*, ft.name as fee_type_name, sc.department_id
                            FROM session_charges sc
                            JOIN fee_types ft ON sc.fee_type_id = ft.id
                            WHERE sc.id IN (" . implode(',', array_map('intval', $chargeIds)) . ")
                            AND sc.is_active = 1";
            $chargesResult = $conn->query($chargesQuery);

            if ($chargesResult->num_rows === 0) {
                throw new Exception("কোন সক্রিয় সেশন চার্জ পাওয়া যায়নি!");
            }

            // Prepare statements
            $checkQuery = "SELECT id FROM fees WHERE student_id = ? AND fee_type = ? AND due_date = ?";
            $checkStmt = $conn->prepare($checkQuery);

            $insertQuery = "INSERT INTO fees (student_id, fee_type, amount, paid, due_date, payment_status, category_id, session_id)
                           VALUES (?, ?, ?, 0, ?, 'due', 1, ?)";
            $insertStmt = $conn->prepare($insertQuery);

            // Get student department information
            $studentDeptQuery = "SELECT id, department_id FROM students WHERE id IN (" . implode(',', array_map('intval', $allStudentIds)) . ")";
            $studentDeptResult = $conn->query($studentDeptQuery);
            $studentDepartments = [];

            if ($studentDeptResult && $studentDeptResult->num_rows > 0) {
                while ($studentDept = $studentDeptResult->fetch_assoc()) {
                    $studentDepartments[$studentDept['id']] = $studentDept['department_id'];
                }
            }

            // Process each student and charge
            foreach ($allStudentIds as $studentId) {
                $chargesResult->data_seek(0);
                while ($charge = $chargesResult->fetch_assoc()) {
                    // Skip if charge is department-specific and doesn't match student's department
                    if (!empty($charge['department_id']) &&
                        isset($studentDepartments[$studentId]) &&
                        $charge['department_id'] != $studentDepartments[$studentId]) {
                        continue;
                    }

                    // Check if fee already exists
                    $checkStmt->bind_param('iss', $studentId, $charge['fee_type_name'], $dueDate);
                    $checkStmt->execute();
                    $result = $checkStmt->get_result();

                    if ($result->num_rows > 0) {
                        $existingCount++;
                    } else {
                        // Create new fee
                        $insertStmt->bind_param('isdsi', $studentId, $charge['fee_type_name'], $charge['amount'], $dueDate, $sessionId);

                        if ($insertStmt->execute()) {
                            $successCount++;
                        } else {
                            $errorCount++;
                            error_log("Error adding fee: " . $insertStmt->error);
                        }
                    }
                }
            }

            // Commit transaction
            $conn->commit();

            // Set appropriate message
            if ($successCount > 0) {
                $_SESSION['success'] = $successCount . ' টি ফি সফলভাবে যোগ করা হয়েছে!';

                if ($existingCount > 0) {
                    $_SESSION['warning'] = $existingCount . ' টি ফি ইতিমধ্যে বিদ্যমান ছিল।';
                }

                if ($errorCount > 0) {
                    $_SESSION['error'] = $errorCount . ' টি ফি যোগ করতে সমস্যা হয়েছে।';
                }
            } else if ($existingCount > 0 && $errorCount == 0) {
                $_SESSION['warning'] = 'সকল ফি ইতিমধ্যে বিদ্যমান!';
            } else {
                $_SESSION['error'] = 'ফি যোগ করতে সমস্যা হয়েছে।';
            }

            // Redirect back to session charges page
            header("Location: session_charges.php");
            exit();
        } catch (Exception $e) {
            // Roll back transaction on error
            $conn->rollback();
            $_SESSION['error'] = 'ফি যোগ করতে সমস্যা: ' . $e->getMessage();
            header("Location: session_charges.php");
            exit();
        }
    } else {
        $_SESSION['error'] = 'সমস্ত প্রয়োজনীয় ফিল্ড পূরণ করুন!';
        header("Location: session_charges.php");
        exit();
    }
}

// Get all sessions
$sessionsQuery = "SELECT id, session_name FROM sessions ORDER BY session_name DESC";
$sessions = $conn->query($sessionsQuery);

// Get all classes
$classesQuery = "SELECT id, class_name FROM classes ORDER BY class_name";
$classes = $conn->query($classesQuery);

// Get all departments
$departmentsQuery = "SELECT id, department_name FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Get all students
$studentsQuery = "SELECT s.id, s.first_name, s.last_name, s.student_id, s.class_id, s.session_id, s.department_id,
                 c.class_name, ss.session_name, d.department_name
                 FROM students s
                 LEFT JOIN classes c ON s.class_id = c.id
                 LEFT JOIN sessions ss ON s.session_id = ss.id
                 LEFT JOIN departments d ON s.department_id = d.id
                 ORDER BY ss.session_name DESC, c.class_name, d.department_name, s.first_name, s.last_name";
$students = $conn->query($studentsQuery);

// Debug query
// echo "<!-- Student Query: " . $studentsQuery . " -->";

// Get all active session charges
$chargesQuery = "SELECT sc.*, s.session_name, d.department_name, ft.name as fee_type_name
                FROM session_charges sc
                JOIN sessions s ON sc.session_id = s.id
                LEFT JOIN departments d ON sc.department_id = d.id
                JOIN fee_types ft ON sc.fee_type_id = ft.id
                WHERE sc.is_active = 1
                ORDER BY s.session_name DESC, d.department_name, ft.name";
$charges = $conn->query($chargesQuery);

// Include header
include_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Include sidebar -->
        <?php include_once 'includes/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-money-bill-wave me-2"></i> সেশন চার্জ প্রয়োগ করুন</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="session_charges.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i> সেশন চার্জ ম্যানেজমেন্ট
                        </a>
                    </div>
                </div>
            </div>

            <!-- Display Messages -->
            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $_SESSION['success'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['warning'])): ?>
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <?= $_SESSION['warning'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['warning']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $_SESSION['error'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>



            <!-- Apply Session Charges Form -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-users me-2"></i> শিক্ষার্থীদের সেশন চার্জ প্রয়োগ করুন</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="" id="applyChargesForm">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="session_id" class="form-label">সেশন</label>
                                <select class="form-select" id="session_id" name="session_id" required>
                                    <option value="">সেশন নির্বাচন করুন</option>
                                    <?php if ($sessions && $sessions->num_rows > 0): ?>
                                        <?php while ($session = $sessions->fetch_assoc()): ?>
                                            <option value="<?= $session['id'] ?>"><?= $session['session_name'] ?></option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="class_id" class="form-label">শ্রেণী (ঐচ্ছিক)</label>
                                <select class="form-select" id="class_id" name="class_id">
                                    <option value="">সকল শ্রেণী</option>
                                    <?php if ($classes && $classes->num_rows > 0): ?>
                                        <?php while ($class = $classes->fetch_assoc()): ?>
                                            <option value="<?= $class['id'] ?>"><?= $class['class_name'] ?></option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="department_id" class="form-label">বিভাগ (ঐচ্ছিক)</label>
                                <select class="form-select" id="department_id" name="department_id">
                                    <option value="">সকল বিভাগ</option>
                                    <?php if ($departments && $departments->num_rows > 0): ?>
                                        <?php while ($department = $departments->fetch_assoc()): ?>
                                            <option value="<?= $department['id'] ?>"><?= $department['department_name'] ?></option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="student_ids" class="form-label">শিক্ষার্থী (ঐচ্ছিক)</label>
                                <select class="form-select" id="student_ids" name="student_ids[]" multiple>
                                    <?php if ($students && $students->num_rows > 0): ?>
                                        <?php while ($student = $students->fetch_assoc()): ?>
                                            <?php
                                                // Debug student data
                                                // echo "<!-- Student: " . print_r($student, true) . " -->";
                                            ?>
                                            <option value="<?= $student['id'] ?>"
                                                data-session-id="<?= $student['session_id'] ?? '' ?>"
                                                data-class-id="<?= $student['class_id'] ?? '' ?>"
                                                data-department-id="<?= $student['department_id'] ?? '' ?>">
                                                <?= $student['first_name'] . ' ' . $student['last_name'] ?>
                                                (<?= $student['student_id'] ?>) -
                                                <?= $student['class_name'] ?? 'N/A' ?>,
                                                <?= $student['session_name'] ?? 'N/A' ?>
                                                <?= $student['department_name'] ? ', ' . $student['department_name'] : '' ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                                <div class="form-text">
                                    <p>সেশন, শ্রেণী, বিভাগ নির্বাচন করলে শিক্ষার্থীদের তালিকা সেই অনুযায়ী ফিল্টার হবে।</p>
                                    <p>শিক্ষার্থী নির্বাচন না করলে, সেশন/শ্রেণী/বিভাগ অনুযায়ী সকল শিক্ষার্থীদের চার্জ প্রয়োগ করা হবে।</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="charge_ids" class="form-label">সেশন চার্জ</label>
                                <select class="form-select" id="charge_ids" name="charge_ids[]" multiple required>
                                    <?php if ($charges && $charges->num_rows > 0): ?>
                                        <?php while ($charge = $charges->fetch_assoc()): ?>
                                            <option value="<?= $charge['id'] ?>"
                                                data-session-id="<?= $charge['session_id'] ?>"
                                                data-department-id="<?= $charge['department_id'] ?>">
                                                <?= $charge['session_name'] ?> -
                                                <?= $charge['department_name'] ? $charge['department_name'] . ' - ' : '' ?>
                                                <?= $charge['fee_type_name'] ?>
                                                (৳ <?= number_format($charge['amount'], 2) ?>)
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="due_date" class="form-label">বকেয়া তারিখ</label>
                                <input type="date" class="form-control" id="due_date" name="due_date" value="<?= date('Y-m-d') ?>" required>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" name="apply_charges" class="btn btn-primary">
                                <i class="fas fa-check-circle me-1"></i> চার্জ প্রয়োগ করুন
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize select2 for multiple selects
        if (typeof $.fn.select2 !== 'undefined') {
            $('#student_ids').select2({
                placeholder: 'শিক্ষার্থী নির্বাচন করুন',
                allowClear: true
            });

            $('#charge_ids').select2({
                placeholder: 'সেশন চার্জ নির্বাচন করুন',
                allowClear: false
            });
        }

        // Add debug button
        $('<button type="button" class="btn btn-sm btn-info mb-3">Debug Data Attributes</button>')
            .insertAfter('#student_ids')
            .on('click', function(e) {
                e.preventDefault();
                console.log('Debugging student data attributes:');
                $('#student_ids option').each(function(index) {
                    if (index < 10) { // Show first 10 for brevity
                        console.log($(this).text(), {
                            'session_id': $(this).attr('data-session-id'),
                            'class_id': $(this).attr('data-class-id'),
                            'department_id': $(this).attr('data-department-id')
                        });
                    }
                });
            });

        // Filter students based on session, class, and department
        $('#session_id, #class_id, #department_id').on('change', function() {
            const sessionId = $('#session_id').val();
            const classId = $('#class_id').val();
            const departmentId = $('#department_id').val();

            console.log('Filter changed:', {
                'session_id': sessionId,
                'class_id': classId,
                'department_id': departmentId
            });

            // Reset student selection
            $('#student_ids').val(null).trigger('change');

            // Filter students
            filterStudentList(sessionId, classId, departmentId);

            // If session or department changed, also filter session charges
            if ($(this).attr('id') === 'session_id' || $(this).attr('id') === 'department_id') {
                filterSessionCharges(sessionId);
            }
        });

        // Function to filter student list
        function filterStudentList(sessionId, classId, departmentId) {
            console.log('Filtering students with:', {
                sessionId: sessionId,
                classId: classId,
                departmentId: departmentId
            });

            // Get the select element
            const studentSelect = $('#student_ids');

            // First, show all options
            studentSelect.find('option').each(function() {
                $(this).show().prop('disabled', false);
            });

            // Apply filters if any are selected
            if (sessionId || classId || departmentId) {
                studentSelect.find('option').each(function() {
                    const option = $(this);
                    const optionSessionId = option.attr('data-session-id');
                    const optionClassId = option.attr('data-class-id');
                    const optionDepartmentId = option.attr('data-department-id');

                    // For debugging
                    if (option.index() < 3) {
                        console.log('Option:', option.text(), {
                            optionSessionId,
                            optionClassId,
                            optionDepartmentId,
                            sessionMatch: !sessionId || optionSessionId == sessionId,
                            classMatch: !classId || optionClassId == classId,
                            deptMatch: !departmentId || optionDepartmentId == departmentId
                        });
                    }

                    // Check if this option matches all selected filters
                    const matchesSession = !sessionId || optionSessionId == sessionId;
                    const matchesClass = !classId || optionClassId == classId;
                    const matchesDepartment = !departmentId || optionDepartmentId == departmentId;

                    // Hide if it doesn't match all filters
                    if (!matchesSession || !matchesClass || !matchesDepartment) {
                        option.hide().prop('disabled', true);
                    }
                });
            }

            // Refresh select2
            if (typeof $.fn.select2 !== 'undefined') {
                studentSelect.select2('destroy').select2({
                    placeholder: 'শিক্ষার্থী নির্বাচন করুন',
                    allowClear: true
                });
            }
        }

        // Function to filter session charges
        function filterSessionCharges(sessionId) {
            const departmentId = $('#department_id').val();

            // Reset charge selection
            $('#charge_ids').val(null).trigger('change');

            // Get all charge options
            const chargeOptions = $('#charge_ids option');

            // Show all options first
            chargeOptions.each(function() {
                $(this).show().prop('disabled', false);
            });

            // Apply filters if selected
            if (sessionId || departmentId) {
                chargeOptions.each(function() {
                    const option = $(this);
                    const optionSessionId = option.attr('data-session-id');
                    const optionDepartmentId = option.attr('data-department-id');

                    // Check if this option matches all selected filters
                    const matchesSession = !sessionId || optionSessionId == sessionId;
                    const matchesDepartment = !departmentId ||
                                             optionDepartmentId == departmentId ||
                                             optionDepartmentId === 'null' ||
                                             optionDepartmentId === null ||
                                             optionDepartmentId === '';

                    // Hide if it doesn't match all filters
                    if (!matchesSession || !matchesDepartment) {
                        option.hide().prop('disabled', true);
                    }
                });
            }

            // Refresh select2
            if (typeof $.fn.select2 !== 'undefined') {
                $('#charge_ids').select2('destroy').select2({
                    placeholder: 'সেশন চার্জ নির্বাচন করুন',
                    allowClear: false
                });
            }
        }
    });
</script>

<?php
// Include footer
include_once 'includes/footer.php';
?>
