# ZFAW স্কুল ম্যানেজমেন্ট সিস্টেমকে মোবাইল ফ্রেন্ডলি করার জন্য সাজেশন

## ১. Bootstrap রেসপন্সিভ ক্লাস আপডেট করুন

আপনার `admin/manage_exams.php` এবং অন্যান্য ফাইলগুলোতে নিম্নলিখিত পরিবর্তন করুন:

- ছোট স্ক্রিনে সাইডবার হাইড করুন:
```php
<div class="container-fluid">
    <div class="row">
        <div class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse" id="sidebarMenu">
            <?php include('includes/sidebar.php'); ?>
        </div>
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
```

- টপ নেভিগেশন বারে সাইডবার টগল বাটন যোগ করুন:
```php
<header class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
    <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="#">ZFAW</a>
    <button class="navbar-toggler d-md-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false">
        <span class="navbar-toggler-icon"></span>
    </button>
    ...
</header>
```

## ২. টেবিল এবং ফর্ম আপডেট করুন

- রেসপন্সিভ টেবিল করুন:
```html
<div class="table-responsive">
    <table class="table">
        ...
    </table>
</div>
```

- ফর্ম লেভেল এবং ইনপুট ফিল্ড কলাম সাইজ অ্যাডজাস্ট করুন:
```html
<div class="row mb-3">
    <label for="example" class="col-sm-4 col-form-label">লেবেল:</label>
    <div class="col-sm-8">
        <input type="text" class="form-control" id="example">
    </div>
</div>
```

## ৩. সেরা টাচ মোবাইল অভিজ্ঞতার জন্য CSS আপডেট

CSS এ যোগ করুন:
```css
/* ছোট ডিভাইসের জন্য টাচ ফ্রেন্ডলি ইলিমেন্ট */
@media (max-width: 768px) {
    /* সব এক্শন বাটন বড় করুন */
    .btn {
        padding: 0.6rem 1rem;
        margin-bottom: 0.5rem;
    }
    
    /* টেবিল রো পাডিং বাড়ান */
    .table td, .table th {
        padding: 0.75rem;
    }
    
    /* সার্চ ইনপুট বড় করুন */
    .form-control {
        height: calc(1.5em + 1rem + 2px);
        padding: 0.5rem 0.75rem;
    }
    
    /* টপ নেভিগেশন ব্যবহারযোগ্য করুন */
    .navbar-brand {
        font-size: 1.2rem;
    }
}
```

## ৪. পেজ স্পিড অপ্টিমাইজেশন

- ছবি সাইজ কমান (compress করুন)
- এক্সটারনাল CSS/JS ফাইল মিনিফাই করুন
- ফন্ট অপ্টিমাইজ করুন, শুধু প্রয়োজনীয় ফন্ট ওয়েট/স্টাইল লোড করুন
- CSS এ defer/async ব্যবহার করুন জাভাস্ক্রিপ্ট ফাইলের জন্য

## ৫. আরও মোবাইল-ফ্রেন্ডলি ফিচার যোগ করুন

- ফন্ট সাইজ বাড়ান (minimum 16px)
- ইনপুট ফিল্ড এবং বাটনগুলো বড় করুন 
- পুল-টু-রিফ্রেশ ব্যবহার করুন (`pull-to-refresh.js` লাইব্রেরি)
- মোবাইল-স্পেসিফিক জেসচার সাপোর্ট যোগ করুন
- ডার্ক মোড সাপোর্ট যোগ করুন (prefers-color-scheme মিডিয়া কোয়েরি ব্যবহার করে) 