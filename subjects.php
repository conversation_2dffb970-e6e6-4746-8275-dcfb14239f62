<?php
require_once 'includes/dbh.inc.php';

// Get all subjects if exists
$subjectsQuery = "SHOW TABLES LIKE 'subjects'";
$tableExists = $conn->query($subjectsQuery);

$subjects = null;
if ($tableExists && $tableExists->num_rows > 0) {
    $subjects = $conn->query("SELECT * FROM subjects ORDER BY subject_name");
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>বিষয়সমূহ - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    
    
    

    <style>
        body, html, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, select, textarea, label, li {
            font-family: 'Noto Sans Bengali', 'Hind Siliguri', 'Baloo Da 2', sans-serif;
        }

        h1, h2, h3 {
            font-family: 'Baloo Da 2', 'Hind Siliguri', sans-serif;
            font-weight: 600;
        }

        .display-4 {
            font-weight: 700;
            color: #006A4E;
        }

        .lead {
            font-family: 'Noto Sans Bengali', sans-serif;
            font-weight: 400;
        }

        .subject-card {
            transition: all 0.3s;
            height: 100%;
        }
        .subject-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .subject-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
        }

        .card-title {
            font-family: 'Baloo Da 2', sans-serif;
            font-weight: 600;
        }

        .btn {
            font-family: 'Hind Siliguri', sans-serif;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-university me-2"></i>কলেজ ম্যানেজমেন্ট সিস্টেম
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home me-1"></i> হোম
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="subjects.php">
                            <i class="fas fa-book me-1"></i> বিষয়সমূহ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">
                            <i class="fas fa-info-circle me-1"></i> আমাদের সম্পর্কে
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">
                            <i class="fas fa-envelope me-1"></i> যোগাযোগ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-outline-light ms-2" href="index.php#login-section">
                            <i class="fas fa-sign-in-alt me-1"></i> লগইন
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Scrolling Notice Section (iframe solution with vertical alignment fix) -->
    <div style="width: 100%; height: 85px; position: relative; margin-bottom: 15px; display: flex; align-items: center; justify-content: center; overflow: hidden;">
        <iframe src="centered_notice.php" style="width: 100%; height: 85px; border: none; overflow: hidden; position: absolute; top: 0; left: 0;"></iframe>
    </div>

    <!-- Original notice section (hidden) -->
    <div class="scrolling-notice-container" style="display: none;">
                    <i class="fas fa-bullhorn me-2"></i> <strong>সর্বশেষ নোটিশ:</strong>
                    <?php
                    // Safe notice display with error handling
                    try {
                        // Check if connection is still alive
                        $is_connected = false;
                        try {
                            $is_connected = $conn && $conn->query("SELECT 1");
                        } catch (Exception $e) {
                            $is_connected = false;
                        }

                        if (!$is_connected) {
                            // Reconnect to database
                            require_once 'includes/dbh.inc.php';
                        }

                        // Check if notices table exists
                        $latest_notice_query = "SHOW TABLES LIKE 'notices'";
                        $latest_notice_result = $conn->query($latest_notice_query);

                        if ($latest_notice_result && $latest_notice_result->num_rows > 0) {
                            // Get latest notice with a timeout limit
                            $sql = "SELECT * FROM notices ORDER BY date DESC LIMIT 1";
                            $result = $conn->query($sql);

                            if ($result && $result->num_rows > 0) {
                                $row = $result->fetch_assoc();
                                echo htmlspecialchars($row['title']) . ' - ' . htmlspecialchars(substr($row['content'], 0, 150)) .
                                     (strlen($row['content']) > 150 ? '...' : '');
                            } else {
                                echo "বর্তমানে কোন নোটিশ নেই।";
                            }
                        } else {
                            echo "বর্তমানে কোন নোটিশ নেই।";
                        }
                    } catch (Exception $e) {
                        // Silently handle the error and show a generic message
                        echo "নোটিশ লোড করতে সমস্যা হচ্ছে। পরে আবার চেষ্টা করুন।";
                        // Optionally log the error
                        error_log('Notice Error: ' . $e->getMessage());
                    }
                    ?>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <i class="fas fa-calendar-alt me-2"></i> <strong>আজকের তারিখ:</strong> <?php echo date('d/m/Y'); ?>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <i class="fas fa-graduation-cap me-2"></i> <strong>ভর্তি চলছে:</strong> নতুন শিক্ষার্থীদের জন্য ভর্তি চলছে। আজই যোগাযোগ করুন।
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Scrolling notice animation */
        @keyframes scrollText {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
        }
        @-webkit-keyframes scrollText {
            0% { -webkit-transform: translateX(100%); }
            100% { -webkit-transform: translateX(-100%); }
        }
        @-moz-keyframes scrollText {
            0% { -moz-transform: translateX(100%); }
            100% { -moz-transform: translateX(-100%); }
        }
        @-o-keyframes scrollText {
            0% { -o-transform: translateX(100%); }
            100% { -o-transform: translateX(-100%); }
        }

        /* Force visibility */
        .scrolling-notice-container {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            z-index: 1000 !important;
        }

        .scrolling-notice {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        #scrolling-text {
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
            animation-duration: 30s !important;
            animation-timing-function: linear !important;
            animation-iteration-count: infinite !important;
            animation-name: scrollText !important;
            -webkit-animation-name: scrollText !important;
            -moz-animation-name: scrollText !important;
            -o-animation-name: scrollText !important;
        }
    </style>

    <script>
        // Improved scrolling notice animation
        document.addEventListener('DOMContentLoaded', function() {
            // Force redraw of the scrolling text element
            var scrollingText = document.getElementById('scrolling-text');
            if (scrollingText) {
                // Temporarily hide and show to force redraw
                scrollingText.style.display = 'none';
                setTimeout(function() {
                    scrollingText.style.display = 'inline-block';
                }, 10);

                // Restart animation
                scrollingText.style.animationName = 'none';
                scrollingText.style.webkitAnimationName = 'none';
                scrollingText.style.mozAnimationName = 'none';
                scrollingText.style.oAnimationName = 'none';

                setTimeout(function() {
                    scrollingText.style.animationName = 'scrollText';
                    scrollingText.style.webkitAnimationName = 'scrollText';
                    scrollingText.style.mozAnimationName = 'scrollText';
                    scrollingText.style.oAnimationName = 'scrollText';
                }, 50);

                // Ensure animation is visible
                scrollingText.style.visibility = 'visible';
                scrollingText.style.opacity = '1';
            }
        });
    </script>

    <!-- Header -->
    <div class="bg-light py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-8 mx-auto text-center">
                    <h1 class="display-4">বিভাগ অনুযায়ী বিষয়</h1>
                    <p class="lead">বিভাগ নির্বাচন করে সংশ্লিষ্ট বিষয়সমূহ দেখুন</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Section -->
    <div class="bg-light py-5">
        <div class="container">
            <div class="row">

                <!-- বিভাগসমূহ -->
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card text-center h-100">
                            <div class="card-body">
                                <i class="fas fa-flask fa-3x text-primary mb-3"></i>
                                <h5>বিজ্ঞান বিভাগ</h5>
                                <a href="departments/science.php" class="btn btn-outline-primary mt-2">দেখুন</a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-3">
                        <div class="card text-center h-100">
                            <div class="card-body">
                                <i class="fas fa-book fa-3x text-success mb-3"></i>
                                <h5>কলা বিভাগ</h5>
                                <a href="departments/arts.php" class="btn btn-outline-success mt-2">দেখুন</a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-3">
                        <div class="card text-center h-100">
                            <div class="card-body">
                                <i class="fas fa-chart-pie fa-3x text-danger mb-3"></i>
                                <h5>বাণিজ্য বিভাগ</h5>
                                <a href="departments/commerce.php" class="btn btn-outline-danger mt-2">দেখুন</a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-3">
                        <div class="card text-center h-100">
                            <div class="card-body">
                                <i class="fas fa-users fa-3x text-info mb-3"></i>
                                <h5>সাধারণ বিভাগ</h5>
                                <a href="departments/general.php" class="btn btn-outline-info mt-2">দেখুন</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>কলেজ ম্যানেজমেন্ট সিস্টেম</h5>
                    <p>একটি সম্পূর্ণ কলেজ ব্যবস্থাপনা পদ্ধতি, যা সকল শিক্ষার্থী, শিক্ষক ও কর্মীদের জন্য বিকশিত করা হয়েছে।</p>
                </div>
                <div class="col-md-4">
                    <h5>দ্রুত লিংক</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-white">হোম</a></li>
                        <li><a href="subjects.php" class="text-white">বিষয়সমূহ</a></li>
                        <li><a href="about.php" class="text-white">আমাদের সম্পর্কে</a></li>
                        <li><a href="contact.php" class="text-white">যোগাযোগ</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>যোগাযোগ করুন</h5>
                    <address>
                        <i class="fas fa-map-marker-alt me-2"></i> ঠিকানা, শহর, দেশ<br>
                        <i class="fas fa-phone me-2"></i> +৮৮০১৭XXXXXXXX<br>
                        <i class="fas fa-envelope me-2"></i> <EMAIL>
                    </address>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12 text-center">
                    <p class="mb-0">কপিরাইট &copy; ২০২৩ কলেজ ম্যানেজমেন্ট সিস্টেম। সর্বস্বত্ব সংরক্ষিত।</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>