<!DOCTYPE html>
<html>
<head>
    <title>System Debug Test</title>
</head>
<body>
    <h1>System Debug Test</h1>
    <p>This is a completely minimal test page.</p>
    <p>Current time: <span id="time"></span></p>
    
    <div id="debug-info">
        <h3>Debug Information:</h3>
        <p>Page loaded at: <span id="load-time"></span></p>
        <p>User Agent: <span id="user-agent"></span></p>
        <p>URL: <span id="current-url"></span></p>
    </div>
    
    <script>
        // Set time immediately
        document.getElementById('time').textContent = new Date().toLocaleString();
        document.getElementById('load-time').textContent = new Date().toLocaleString();
        document.getElementById('user-agent').textContent = navigator.userAgent;
        document.getElementById('current-url').textContent = window.location.href;
        
        // Force title
        document.title = 'System Debug Test';
        
        // Log to console
        console.log('Page loaded successfully');
        console.log('Title set to:', document.title);
    </script>
</body>
</html>
