<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Function to check and fix payment tables
function checkAndFixPaymentTables($conn) {
    $results = [];
    
    try {
        // Check if both tables exist
        $paymentsExists = $conn->query("SHOW TABLES LIKE 'payments'")->num_rows > 0;
        $feePaymentsExists = $conn->query("SHOW TABLES LIKE 'fee_payments'")->num_rows > 0;
        
        $results['tables_status'] = [
            'payments' => $paymentsExists,
            'fee_payments' => $feePaymentsExists
        ];
        
        // Get data from both tables if they exist
        $paymentsData = [];
        $feePaymentsData = [];
        
        if ($paymentsExists) {
            $paymentsQuery = "SELECT * FROM payments ORDER BY created_at DESC";
            $paymentsResult = $conn->query($paymentsQuery);
            while ($row = $paymentsResult->fetch_assoc()) {
                $paymentsData[] = $row;
            }
        }
        
        if ($feePaymentsExists) {
            $feePaymentsQuery = "SELECT * FROM fee_payments ORDER BY created_at DESC";
            $feePaymentsResult = $conn->query($feePaymentsQuery);
            while ($row = $feePaymentsResult->fetch_assoc()) {
                $feePaymentsData[] = $row;
            }
        }
        
        $results['payments_data'] = $paymentsData;
        $results['fee_payments_data'] = $feePaymentsData;
        $results['payments_count'] = count($paymentsData);
        $results['fee_payments_count'] = count($feePaymentsData);
        
        return $results;
        
    } catch (Exception $e) {
        return [
            'error' => $e->getMessage()
        ];
    }
}

// Function to migrate payments to fee_payments table
function migratePaymentsToFeePayments($conn) {
    try {
        $conn->begin_transaction();
        
        // Ensure fee_payments table exists with proper structure
        $createFeePaymentsTable = "CREATE TABLE IF NOT EXISTS fee_payments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            fee_id INT NOT NULL,
            student_id INT,
            amount DECIMAL(10,2) NOT NULL,
            payment_date DATE NOT NULL,
            payment_method VARCHAR(50) NOT NULL DEFAULT 'cash',
            receipt_no VARCHAR(50) DEFAULT NULL,
            transaction_id VARCHAR(100) DEFAULT NULL,
            notes TEXT DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (fee_id) REFERENCES fees(id) ON DELETE CASCADE
        )";
        
        if (!$conn->query($createFeePaymentsTable)) {
            throw new Exception('Fee payments table তৈরি করতে সমস্যা: ' . $conn->error);
        }
        
        // Check if payments table exists and has data
        $paymentsExists = $conn->query("SHOW TABLES LIKE 'payments'")->num_rows > 0;
        
        if ($paymentsExists) {
            // Get all payments from payments table
            $paymentsQuery = "SELECT * FROM payments";
            $paymentsResult = $conn->query($paymentsQuery);
            
            $migratedCount = 0;
            
            while ($payment = $paymentsResult->fetch_assoc()) {
                // Check if this payment already exists in fee_payments
                $checkExisting = "SELECT id FROM fee_payments WHERE fee_id = ? AND amount = ? AND payment_date = ? AND receipt_no = ?";
                $checkStmt = $conn->prepare($checkExisting);
                $receiptNo = $payment['receipt_number'] ?? null;
                $checkStmt->bind_param('idss', $payment['fee_id'], $payment['amount'], $payment['payment_date'], $receiptNo);
                $checkStmt->execute();
                
                if ($checkStmt->get_result()->num_rows == 0) {
                    // Insert into fee_payments table
                    $insertQuery = "INSERT INTO fee_payments (fee_id, student_id, amount, payment_date, payment_method, receipt_no, transaction_id, notes, created_at) 
                                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    $insertStmt = $conn->prepare($insertQuery);
                    $insertStmt->bind_param('iidsssss',
                        $payment['fee_id'],
                        $payment['student_id'],
                        $payment['amount'],
                        $payment['payment_date'],
                        $payment['payment_method'],
                        $receiptNo,
                        $payment['transaction_id'] ?? null,
                        $payment['notes'] ?? null,
                        $payment['created_at']
                    );
                    
                    if ($insertStmt->execute()) {
                        $migratedCount++;
                    }
                }
            }
            
            $conn->commit();
            return [
                'success' => true,
                'migrated_count' => $migratedCount,
                'message' => "$migratedCount টি payment record migrate করা হয়েছে"
            ];
        } else {
            return [
                'success' => true,
                'migrated_count' => 0,
                'message' => 'Payments table খুঁজে পাওয়া যায়নি'
            ];
        }
        
    } catch (Exception $e) {
        $conn->rollback();
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// Function to fix student ID validation issues
function fixStudentIdValidation($conn) {
    try {
        // Check for invalid student IDs in fee_payments
        $invalidQuery = "SELECT fp.*, s.student_id as student_roll 
                        FROM fee_payments fp 
                        LEFT JOIN fees f ON fp.fee_id = f.id 
                        LEFT JOIN students s ON f.student_id = s.id 
                        WHERE s.id IS NULL";
        
        $invalidResult = $conn->query($invalidQuery);
        $invalidRecords = [];
        
        while ($row = $invalidResult->fetch_assoc()) {
            $invalidRecords[] = $row;
        }
        
        return [
            'invalid_count' => count($invalidRecords),
            'invalid_records' => $invalidRecords
        ];
        
    } catch (Exception $e) {
        return [
            'error' => $e->getMessage()
        ];
    }
}

// Handle form submissions
$checkResults = null;
$migrateResults = null;
$validationResults = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['check_tables'])) {
        $checkResults = checkAndFixPaymentTables($conn);
    } elseif (isset($_POST['migrate_payments'])) {
        $migrateResults = migratePaymentsToFeePayments($conn);
    } elseif (isset($_POST['check_validation'])) {
        $validationResults = fixStudentIdValidation($conn);
    }
}

// Get current status
$currentStatus = checkAndFixPaymentTables($conn);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Tables সমস্যা সমাধান</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: "Hind Siliguri", sans-serif;
        }
        
        .status-card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .status-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .table-exists {
            background-color: #d1e7dd;
            border-left: 4px solid #198754;
        }
        
        .table-missing {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        
        .data-row {
            background-color: #f8f9fa;
            border-left: 3px solid #0d6efd;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4 bg-white p-3 rounded shadow-sm">
                    <div>
                        <h2 class="mb-0">
                            <i class="fas fa-tools text-warning me-2"></i>
                            Payment Tables সমস্যা সমাধান
                        </h2>
                        <small class="text-muted">Payment tables এর মধ্যে data inconsistency ঠিক করুন</small>
                    </div>
                    <div>
                        <a href="fee_management.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i> ফি ম্যানেজমেন্ট
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Status -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card status-card <?php echo $currentStatus['tables_status']['payments'] ? 'table-exists' : 'table-missing'; ?>">
                    <div class="card-body text-center">
                        <i class="fas fa-database fa-2x mb-2"></i>
                        <h5>Payments Table</h5>
                        <p class="mb-1">
                            <?php if ($currentStatus['tables_status']['payments']): ?>
                                <span class="badge bg-success">বিদ্যমান</span>
                            <?php else: ?>
                                <span class="badge bg-danger">অনুপস্থিত</span>
                            <?php endif; ?>
                        </p>
                        <small><?php echo $currentStatus['payments_count']; ?> টি রেকর্ড</small>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card status-card <?php echo $currentStatus['tables_status']['fee_payments'] ? 'table-exists' : 'table-missing'; ?>">
                    <div class="card-body text-center">
                        <i class="fas fa-receipt fa-2x mb-2"></i>
                        <h5>Fee Payments Table</h5>
                        <p class="mb-1">
                            <?php if ($currentStatus['tables_status']['fee_payments']): ?>
                                <span class="badge bg-success">বিদ্যমান</span>
                            <?php else: ?>
                                <span class="badge bg-danger">অনুপস্থিত</span>
                            <?php endif; ?>
                        </p>
                        <small><?php echo $currentStatus['fee_payments_count']; ?> টি রেকর্ড</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">সমাধানের পদক্ষেপ</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <form method="POST" style="display: inline;">
                                    <button type="submit" name="check_tables" class="btn btn-info w-100 mb-2">
                                        <i class="fas fa-search me-1"></i>
                                        Tables চেক করুন
                                    </button>
                                </form>
                            </div>
                            <div class="col-md-4">
                                <form method="POST" style="display: inline;">
                                    <button type="submit" name="migrate_payments" class="btn btn-warning w-100 mb-2"
                                            onclick="return confirm('আপনি কি নিশ্চিত যে payments table থেকে fee_payments table এ data migrate করতে চান?')">
                                        <i class="fas fa-exchange-alt me-1"></i>
                                        Data Migrate করুন
                                    </button>
                                </form>
                            </div>
                            <div class="col-md-4">
                                <form method="POST" style="display: inline;">
                                    <button type="submit" name="check_validation" class="btn btn-danger w-100 mb-2">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        Validation চেক করুন
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Display -->
        <?php if ($checkResults): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Tables চেক করার ফলাফল</h5>
                        </div>
                        <div class="card-body">
                            <?php if (isset($checkResults['error'])): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    <?php echo htmlspecialchars($checkResults['error']); ?>
                                </div>
                            <?php else: ?>
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Payments Table Data:</h6>
                                        <?php if (!empty($checkResults['payments_data'])): ?>
                                            <div class="table-responsive">
                                                <table class="table table-sm">
                                                    <thead>
                                                        <tr>
                                                            <th>ID</th>
                                                            <th>Fee ID</th>
                                                            <th>Amount</th>
                                                            <th>Date</th>
                                                            <th>Method</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach (array_slice($checkResults['payments_data'], 0, 5) as $payment): ?>
                                                            <tr>
                                                                <td><?php echo $payment['id']; ?></td>
                                                                <td><?php echo $payment['fee_id']; ?></td>
                                                                <td>৳<?php echo number_format($payment['amount'], 2); ?></td>
                                                                <td><?php echo $payment['payment_date']; ?></td>
                                                                <td><?php echo $payment['payment_method']; ?></td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        <?php else: ?>
                                            <p class="text-muted">কোন data নেই</p>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Fee Payments Table Data:</h6>
                                        <?php if (!empty($checkResults['fee_payments_data'])): ?>
                                            <div class="table-responsive">
                                                <table class="table table-sm">
                                                    <thead>
                                                        <tr>
                                                            <th>ID</th>
                                                            <th>Fee ID</th>
                                                            <th>Amount</th>
                                                            <th>Date</th>
                                                            <th>Method</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach (array_slice($checkResults['fee_payments_data'], 0, 5) as $payment): ?>
                                                            <tr>
                                                                <td><?php echo $payment['id']; ?></td>
                                                                <td><?php echo $payment['fee_id']; ?></td>
                                                                <td>৳<?php echo number_format($payment['amount'], 2); ?></td>
                                                                <td><?php echo $payment['payment_date']; ?></td>
                                                                <td><?php echo $payment['payment_method']; ?></td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        <?php else: ?>
                                            <p class="text-muted">কোন data নেই</p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($migrateResults): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <?php if ($migrateResults['success']): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>সফল!</strong> <?php echo htmlspecialchars($migrateResults['message']); ?>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong>ত্রুটি!</strong> <?php echo htmlspecialchars($migrateResults['error']); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($validationResults): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Validation চেক করার ফলাফল</h5>
                        </div>
                        <div class="card-body">
                            <?php if (isset($validationResults['error'])): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    <?php echo htmlspecialchars($validationResults['error']); ?>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Invalid Records:</strong> <?php echo $validationResults['invalid_count']; ?> টি
                                </div>

                                <?php if (!empty($validationResults['invalid_records'])): ?>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Payment ID</th>
                                                    <th>Fee ID</th>
                                                    <th>Amount</th>
                                                    <th>Date</th>
                                                    <th>সমস্যা</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($validationResults['invalid_records'] as $record): ?>
                                                    <tr class="table-warning">
                                                        <td><?php echo $record['id']; ?></td>
                                                        <td><?php echo $record['fee_id']; ?></td>
                                                        <td>৳<?php echo number_format($record['amount'], 2); ?></td>
                                                        <td><?php echo $record['payment_date']; ?></td>
                                                        <td>Invalid student reference</td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Recommendations -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">সুপারিশ</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb me-2"></i>সমস্যা সমাধানের জন্য:</h6>
                            <ol>
                                <li><strong>Tables চেক করুন</strong> - বর্তমান অবস্থা দেখার জন্য</li>
                                <li><strong>Data Migrate করুন</strong> - payments table থেকে fee_payments table এ data স্থানান্তর করুন</li>
                                <li><strong>Validation চেক করুন</strong> - invalid student references খুঁজে বের করুন</li>
                                <li><strong>Fee Management</strong> এ গিয়ে payment records verify করুন</li>
                            </ol>
                        </div>

                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>সতর্কতা:</h6>
                            <ul class="mb-0">
                                <li>Data migrate করার আগে database backup নিন</li>
                                <li>Migration process একবারই চালান</li>
                                <li>Invalid records manually fix করতে হতে পারে</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
