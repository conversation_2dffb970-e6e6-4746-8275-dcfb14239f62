<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$errorMessage = '';
$successMessage = '';

// Handle CSV upload test
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_upload'])) {
    echo "<h3>ডিবাগ তথ্য:</h3>";
    echo "<pre>";
    echo "POST ডেটা:\n";
    print_r($_POST);
    echo "\nFILES ডেটা:\n";
    print_r($_FILES);
    echo "</pre>";
    
    if (isset($_FILES['csv_file'])) {
        echo "<h4>ফাইল তথ্য:</h4>";
        echo "<ul>";
        echo "<li>নাম: " . $_FILES['csv_file']['name'] . "</li>";
        echo "<li>সাইজ: " . $_FILES['csv_file']['size'] . " bytes</li>";
        echo "<li>টাইপ: " . $_FILES['csv_file']['type'] . "</li>";
        echo "<li>ত্রুটি কোড: " . $_FILES['csv_file']['error'] . "</li>";
        echo "<li>টেম্প ফাইল: " . $_FILES['csv_file']['tmp_name'] . "</li>";
        echo "</ul>";
        
        if ($_FILES['csv_file']['error'] === UPLOAD_ERR_OK) {
            $content = file_get_contents($_FILES['csv_file']['tmp_name']);
            echo "<h4>ফাইল কন্টেন্ট (প্রথম ৫০০ ক্যারেক্টার):</h4>";
            echo "<pre>" . htmlspecialchars(substr($content, 0, 500)) . "</pre>";
            
            // Try to parse CSV
            $handle = fopen($_FILES['csv_file']['tmp_name'], 'r');
            if ($handle) {
                echo "<h4>CSV পার্সিং:</h4>";
                $row_count = 0;
                while (($data = fgetcsv($handle)) !== FALSE && $row_count < 5) {
                    $row_count++;
                    echo "সারি $row_count: ";
                    print_r($data);
                    echo "<br>";
                }
                fclose($handle);
            }
        }
    }
    exit();
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>CSV আপলোড টেস্ট - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/hind-siliguri.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include('includes/sidebar.php'); ?>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">CSV আপলোড টেস্ট</h1>
                    <div>
                        <a href="add_student.php" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-1"></i> ফিরে যান
                        </a>
                    </div>
                </div>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    এই পেজটি CSV আপলোড সমস্যা ডিবাগ করার জন্য। ফাইল আপলোড করলে বিস্তারিত তথ্য দেখাবে।
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5>CSV ফাইল টেস্ট আপলোড</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="csv_file" class="form-label">CSV ফাইল নির্বাচন করুন</label>
                                <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                            </div>
                            <button type="submit" name="test_upload" class="btn btn-primary">
                                <i class="fas fa-upload me-2"></i>টেস্ট আপলোড
                            </button>
                        </form>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>নমুনা CSV তৈরি করুন</h5>
                    </div>
                    <div class="card-body">
                        <p>টেস্টের জন্য একটি সহজ CSV ফাইল তৈরি করুন:</p>
                        <textarea class="form-control" rows="10" readonly>student_id,roll_number,first_name,last_name,email,phone,address,dob,gender,batch,admission_date,department_id,class_id,session_id,username,password
STD-001,001,রহিম,আহমেদ,<EMAIL>,01712345678,ঢাকা,2000-01-15,Male,2024,2024-01-01,1,1,1,rahim001,password123
STD-002,002,করিম,হাসান,<EMAIL>,01812345678,চট্টগ্রাম,2001-02-20,Male,2024,2024-01-01,1,1,1,karim002,password123</textarea>
                        <p class="mt-2 text-muted">উপরের টেক্সট কপি করে একটি .csv ফাইলে সেভ করুন এবং আপলোড করে টেস্ট করুন।</p>
                    </div>
                </div>
                
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
