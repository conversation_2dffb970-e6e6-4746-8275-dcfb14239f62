<?php
// Include database connection
require_once "includes/dbh.inc.php";

// Get all departments
$departmentsQuery = "SELECT id, department_name FROM departments";
$departments = $conn->query($departmentsQuery);

if (!$departments || $departments->num_rows == 0) {
    echo "<p style='color:red'>কোন বিভাগ পাওয়া যায়নি। অনুগ্রহ করে প্রথমে বিভাগ যোগ করুন।</p>";
    exit;
}

// Get all subjects
$subjectsQuery = "SELECT id, subject_name, category FROM subjects WHERE is_active = 1";
$subjects = $conn->query($subjectsQuery);

if (!$subjects || $subjects->num_rows == 0) {
    echo "<p style='color:red'>কোন বিষয় পাওয়া যায়নি। অনুগ্রহ করে প্রথমে বিষয় যোগ করুন।</p>";
    exit;
}

// Store subjects by category
$requiredSubjects = [];
$optionalSubjects = [];
$fourthSubjects = [];

while ($subject = $subjects->fetch_assoc()) {
    if ($subject['category'] == 'required') {
        $requiredSubjects[] = $subject;
    } elseif ($subject['category'] == 'optional') {
        $optionalSubjects[] = $subject;
    } elseif ($subject['category'] == 'fourth') {
        $fourthSubjects[] = $subject;
    }
}

// Begin transaction
$conn->begin_transaction();

try {
    // For each department, assign subject types
    while ($department = $departments->fetch_assoc()) {
        $departmentId = $department['id'];
        $departmentName = $department['department_name'];
        
        echo "<h3>বিভাগ: {$departmentName}</h3>";
        
        // Clear existing subject type mappings for this department
        $clearQuery = "DELETE FROM department_subject_types WHERE department_id = ?";
        $stmt = $conn->prepare($clearQuery);
        $stmt->bind_param("i", $departmentId);
        $stmt->execute();
        
        // Insert required subjects
        $insertQuery = "INSERT INTO department_subject_types (department_id, subject_id, subject_type) VALUES (?, ?, ?)";
        $stmt = $conn->prepare($insertQuery);
        
        echo "<p>আবশ্যিক বিষয়সমূহ:</p>";
        echo "<ul>";
        foreach ($requiredSubjects as $subject) {
            $subjectId = $subject['id'];
            $subjectType = 'required';
            $stmt->bind_param("iis", $departmentId, $subjectId, $subjectType);
            $stmt->execute();
            echo "<li>{$subject['subject_name']}</li>";
        }
        echo "</ul>";
        
        // Insert optional subjects
        echo "<p>ঐচ্ছিক বিষয়সমূহ:</p>";
        echo "<ul>";
        foreach ($optionalSubjects as $subject) {
            $subjectId = $subject['id'];
            $subjectType = 'optional';
            $stmt->bind_param("iis", $departmentId, $subjectId, $subjectType);
            $stmt->execute();
            echo "<li>{$subject['subject_name']}</li>";
        }
        echo "</ul>";
        
        // Insert fourth subjects
        echo "<p>৪র্থ বিষয়সমূহ:</p>";
        echo "<ul>";
        foreach ($fourthSubjects as $subject) {
            $subjectId = $subject['id'];
            $subjectType = 'fourth';
            $stmt->bind_param("iis", $departmentId, $subjectId, $subjectType);
            $stmt->execute();
            echo "<li>{$subject['subject_name']}</li>";
        }
        echo "</ul>";
        
        // Also ensure all subjects are assigned to this department in subject_departments table
        $allSubjects = array_merge($requiredSubjects, $optionalSubjects, $fourthSubjects);
        
        foreach ($allSubjects as $subject) {
            $subjectId = $subject['id'];
            
            // Check if mapping exists
            $checkQuery = "SELECT COUNT(*) as count FROM subject_departments WHERE department_id = ? AND subject_id = ?";
            $stmt = $conn->prepare($checkQuery);
            $stmt->bind_param("ii", $departmentId, $subjectId);
            $stmt->execute();
            $result = $stmt->get_result();
            $count = $result->fetch_assoc()['count'];
            
            if ($count == 0) {
                // Insert mapping
                $insertMappingQuery = "INSERT INTO subject_departments (department_id, subject_id) VALUES (?, ?)";
                $stmt = $conn->prepare($insertMappingQuery);
                $stmt->bind_param("ii", $departmentId, $subjectId);
                $stmt->execute();
            }
        }
    }
    
    // Commit transaction
    $conn->commit();
    
    echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>সফলভাবে আপডেট করা হয়েছে!</h3>";
    echo "<p>সকল বিভাগের জন্য বিষয় ক্যাটাগরি সঠিকভাবে সেট করা হয়েছে।</p>";
    echo "<p>এখন আপনি <a href='admin/student_subject_selection.php'>শিক্ষার্থী বিষয় নির্বাচন</a> পেজে যেতে পারেন।</p>";
    echo "</div>";
    
} catch (Exception $e) {
    // Rollback on error
    $conn->rollback();
    
    echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>ত্রুটি!</h3>";
    echo "<p>বিষয় ক্যাটাগরি সেট করতে ব্যর্থ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

// Close connection
$conn->close();
?>
