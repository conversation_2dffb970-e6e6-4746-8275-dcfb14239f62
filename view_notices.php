<?php
require_once 'includes/dbh.inc.php';

echo "<h1>সকল নোটিশ</h1>";

// Check if notices table exists
$sql = "SHOW TABLES LIKE 'notices'";
$result = $conn->query($sql);

if ($result->num_rows == 0) {
    // Create notices table
    $sql = "CREATE TABLE notices (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        date DATE NOT NULL,
        added_by VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "<p>নোটিশ টেবিল সফলভাবে তৈরি করা হয়েছে।</p>";
        
        // Add sample notice
        $sql = "INSERT INTO notices (title, content, date, added_by) VALUES 
        ('কলেজ ম্যানেজমেন্ট সিস্টেমে স্বাগতম', 'এটি একটি নমুনা নোটিশ। অ্যাডমিন প্যানেল থেকে আরও নোটিশ যোগ করা যাবে।', CURDATE(), 'admin')";
        
        $conn->query($sql);
    }
}

// Get all notices
$sql = "SELECT * FROM notices ORDER BY date DESC";
$result = $conn->query($sql);

if ($result && $result->num_rows > 0) {
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px;'>";
    
    while($row = $result->fetch_assoc()) {
        echo "<div style='border: 1px solid #ddd; border-radius: 8px; padding: 15px; background-color: #f9f9f9;'>";
        echo "<h3 style='color: #007bff; margin-top: 0;'>" . htmlspecialchars($row["title"]) . "</h3>";
        echo "<p style='color: #6c757d; margin-bottom: 10px; font-size: 0.9em;'>তারিখ: " . $row["date"] . "</p>";
        echo "<div style='margin-top: 10px;'>" . nl2br(htmlspecialchars($row["content"])) . "</div>";
        echo "</div>";
    }
    
    echo "</div>";
} else {
    echo "<p>কোন নোটিশ পাওয়া যায়নি</p>";
}

echo "<p><a href='index.php'>হোম পেজে ফিরে যান</a></p>";
?>