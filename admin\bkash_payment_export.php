<?php
session_start();
require_once '../includes/dbh.inc.php';
require_once '../includes/bkash_config.php';
require_once '../includes/bkash_functions.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Get search and filter parameters
$searchTerm = isset($_GET['search']) ? $_GET['search'] : '';
$statusFilter = isset($_GET['status']) ? $_GET['status'] : '';
$dateFrom = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$dateTo = isset($_GET['date_to']) ? $_GET['date_to'] : '';

// Build query conditions
$whereClause = "";
$params = [];
$types = "";

if (!empty($searchTerm)) {
    $whereClause .= " WHERE (s.student_id LIKE ? OR s.first_name LIKE ? OR s.last_name LIKE ? OR bp.trx_id LIKE ?)";
    $searchParam = "%$searchTerm%";
    $params = [$searchParam, $searchParam, $searchParam, $searchParam];
    $types = "ssss";
}

if (!empty($statusFilter)) {
    $whereClause = empty($whereClause) ? " WHERE bp.status = ?" : $whereClause . " AND bp.status = ?";
    $params[] = $statusFilter;
    $types .= "s";
}

if (!empty($dateFrom)) {
    $whereClause = empty($whereClause) ? " WHERE DATE(bp.payment_date) >= ?" : $whereClause . " AND DATE(bp.payment_date) >= ?";
    $params[] = $dateFrom;
    $types .= "s";
}

if (!empty($dateTo)) {
    $whereClause = empty($whereClause) ? " WHERE DATE(bp.payment_date) <= ?" : $whereClause . " AND DATE(bp.payment_date) <= ?";
    $params[] = $dateTo;
    $types .= "s";
}

// Get payments
$paymentsQuery = "SELECT bp.*, f.fee_type, s.first_name, s.last_name, s.student_id as roll, c.class_name
                 FROM bkash_payments bp
                 LEFT JOIN fees f ON bp.fee_id = f.id
                 LEFT JOIN students s ON f.student_id = s.id
                 LEFT JOIN classes c ON s.class_id = c.id" . 
                 $whereClause . 
                 " ORDER BY bp.payment_date DESC";

$stmt = $conn->prepare($paymentsQuery);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$payments = $stmt->get_result();

// Set headers for CSV download
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename=bkash_payments_' . date('Y-m-d') . '.csv');

// Create a file pointer connected to the output stream
$output = fopen('php://output', 'w');

// Add BOM to fix UTF-8 in Excel
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Set column headers
fputcsv($output, [
    'আইডি',
    'শিক্ষার্থীর নাম',
    'রোল',
    'শ্রেণী',
    'ফি টাইপ',
    'পরিমাণ',
    'ট্রানজেকশন আইডি',
    'পেমেন্ট আইডি',
    'পেয়ার রেফারেন্স',
    'তারিখ',
    'স্ট্যাটাস'
]);

// Output each row of the data
while ($row = $payments->fetch_assoc()) {
    $status = '';
    if ($row['status'] === 'Completed') {
        $status = 'সফল';
    } elseif ($row['status'] === 'Initiated') {
        $status = 'প্রক্রিয়াধীন';
    } else {
        $status = 'ব্যর্থ';
    }
    
    fputcsv($output, [
        $row['id'],
        $row['first_name'] . ' ' . $row['last_name'],
        $row['roll'],
        $row['class_name'],
        $row['fee_type'],
        $row['amount'],
        $row['trx_id'],
        $row['payment_id'],
        $row['payer_reference'],
        date('d/m/Y H:i', strtotime($row['payment_date'])),
        $status
    ]);
}

fclose($output);
exit;
?>
