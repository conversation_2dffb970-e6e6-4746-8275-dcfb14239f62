<?php
session_start();

// Check if user is logged in and is a teacher
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'teacher') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get teacher information with department name
$userId = $_SESSION['userId'];
$username = $_SESSION['username'] ?? '';

// First try to get teacher by user_id
$sql = "SELECT t.*, d.department_name 
        FROM teachers t
        LEFT JOIN departments d ON t.department_id = d.id
        WHERE t.user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();

// If no results, try to get by username
if ($result->num_rows === 0 && !empty($username)) {
    $sql = "SELECT t.*, d.department_name 
            FROM teachers t
            LEFT JOIN departments d ON t.department_id = d.id
            WHERE t.username = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
}

$teacher = $result->fetch_assoc();

// If still no teacher found, redirect to login
if (!$teacher) {
    header("Location: ../index.php");
    exit();
}

// Create attendance table if it doesn't exist
$attendanceTableQuery = "CREATE TABLE IF NOT EXISTS attendance (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    subject_id INT(11),
    class_id INT(11),
    date DATE NOT NULL,
    status ENUM('present', 'absent', 'late', 'excused') NOT NULL DEFAULT 'present',
    remarks TEXT,
    created_by INT(11),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
$conn->query($attendanceTableQuery);

// Handle attendance submission
$success_msg = '';
$error_msg = '';

if (isset($_POST['submit_attendance'])) {
    $date = $_POST['attendance_date'];
    $subjectId = $_POST['subject_id'] ? intval($_POST['subject_id']) : null;
    $classId = $_POST['class_id'] ? intval($_POST['class_id']) : null;
    $createdBy = $teacher['id'];
    
    if (empty($date) || empty($classId)) {
        $error_msg = "তারিখ এবং শ্রেণী আবশ্যক";
    } else {
        // Begin transaction
        $conn->begin_transaction();
        
        try {
            // Check if attendance already exists for this date, class and subject
            $checkQuery = "SELECT id FROM attendance WHERE date = ? AND class_id = ?";
            $params = [$date, $classId];
            $types = "si";
            
            if ($subjectId) {
                $checkQuery .= " AND subject_id = ?";
                $params[] = $subjectId;
                $types .= "i";
            }
            
            $stmt = $conn->prepare($checkQuery);
            $stmt->bind_param($types, ...$params);
            $stmt->execute();
            $checkResult = $stmt->get_result();
            
            if ($checkResult->num_rows > 0) {
                // Delete existing attendance records
                $deleteQuery = "DELETE FROM attendance WHERE date = ? AND class_id = ?";
                $deleteParams = [$date, $classId];
                $deleteTypes = "si";
                
                if ($subjectId) {
                    $deleteQuery .= " AND subject_id = ?";
                    $deleteParams[] = $subjectId;
                    $deleteTypes .= "i";
                }
                
                $deleteStmt = $conn->prepare($deleteQuery);
                $deleteStmt->bind_param($deleteTypes, ...$deleteParams);
                $deleteStmt->execute();
            }
            
            // Insert new attendance records
            $insertQuery = "INSERT INTO attendance (student_id, subject_id, class_id, date, status, remarks, created_by)
                           VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($insertQuery);
            
            $success = true;
            $insertCount = 0;
            
            foreach ($_POST['attendance'] as $studentId => $status) {
                $remarks = $_POST['remarks'][$studentId] ?? '';
                
                $stmt->bind_param("iiisssi", $studentId, $subjectId, $classId, $date, $status, $remarks, $createdBy);
                
                if ($stmt->execute()) {
                    $insertCount++;
                } else {
                    $success = false;
                    break;
                }
            }
            
            if ($success) {
                $conn->commit();
                $success_msg = "$insertCount জন শিক্ষার্থীর উপস্থিতি সফলভাবে যোগ করা হয়েছে";
            } else {
                $conn->rollback();
                $error_msg = "উপস্থিতি যোগ করতে সমস্যা হয়েছে: " . $conn->error;
            }
        } catch (Exception $e) {
            $conn->rollback();
            $error_msg = "উপস্থিতি যোগ করতে সমস্যা হয়েছে: " . $e->getMessage();
        }
    }
}

// Get filter values
$dateFilter = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');
$classFilter = isset($_GET['class']) ? intval($_GET['class']) : 0;
$subjectFilter = isset($_GET['subject']) ? intval($_GET['subject']) : 0;

// Get subjects for dropdown
try {
    // Check if subjects table exists
    $checkSubjectsTableQuery = "SHOW TABLES LIKE 'subjects'";
    $subjectsTableResult = $conn->query($checkSubjectsTableQuery);
    
    if ($subjectsTableResult->num_rows > 0) {
        // If subjects table exists, get subjects for this teacher's department
        $subjectsQuery = "SELECT id, subject_name, subject_code
                         FROM subjects
                         WHERE department_id = ?
                         ORDER BY subject_name";
        $stmt = $conn->prepare($subjectsQuery);
        $stmt->bind_param("i", $teacher['department_id']);
        $stmt->execute();
        $subjects = $stmt->get_result();
    } else {
        $subjects = null;
    }
} catch (Exception $e) {
    $subjects = null;
}

// Get classes for dropdown
try {
    // Check if classes table exists
    $checkClassesTableQuery = "SHOW TABLES LIKE 'classes'";
    $classesTableResult = $conn->query($checkClassesTableQuery);
    
    if ($classesTableResult->num_rows > 0) {
        // If classes table exists, get all classes
        $classesQuery = "SELECT id, class_name
                        FROM classes
                        ORDER BY class_name";
        $stmt = $conn->prepare($classesQuery);
        $stmt->execute();
        $classes = $stmt->get_result();
    } else {
        $classes = null;
    }
} catch (Exception $e) {
    $classes = null;
}

// Get students for selected class
$students = null;
if ($classFilter > 0) {
    try {
        $studentsQuery = "SELECT s.id, s.student_id, s.first_name, s.last_name, s.roll_number
                         FROM students s
                         WHERE s.class_id = ?
                         ORDER BY s.roll_number, s.first_name, s.last_name";
        $stmt = $conn->prepare($studentsQuery);
        $stmt->bind_param("i", $classFilter);
        $stmt->execute();
        $students = $stmt->get_result();
    } catch (Exception $e) {
        $error_msg = "শিক্ষার্থীদের তথ্য লোড করতে সমস্যা হয়েছে: " . $e->getMessage();
    }
}

// Get existing attendance for the selected date, class and subject
$existingAttendance = [];
if ($classFilter > 0 && !empty($dateFilter)) {
    try {
        $attendanceQuery = "SELECT a.* 
                           FROM attendance a
                           WHERE a.date = ? AND a.class_id = ?";
        $params = [$dateFilter, $classFilter];
        $types = "si";
        
        if ($subjectFilter > 0) {
            $attendanceQuery .= " AND a.subject_id = ?";
            $params[] = $subjectFilter;
            $types .= "i";
        }
        
        $stmt = $conn->prepare($attendanceQuery);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $attendanceResult = $stmt->get_result();
        
        while ($attendance = $attendanceResult->fetch_assoc()) {
            $existingAttendance[$attendance['student_id']] = [
                'status' => $attendance['status'],
                'remarks' => $attendance['remarks']
            ];
        }
    } catch (Exception $e) {
        $error_msg = "উপস্থিতি তথ্য লোড করতে সমস্যা হয়েছে: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include __DIR__ . '/includes/global-head.php'; ?>
    <title>Attendance - Teacher Panel</title>
    
    <style>
        body, html, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, select, textarea, label, li, th, td {
            font-family: 'Hind Siliguri', sans-serif;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        .filter-section {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .attendance-present {
            background-color: #d4edda;
        }
        
        .attendance-absent {
            background-color: #f8d7da;
        }
        
        .attendance-late {
            background-color: #fff3cd;
        }
        
        .attendance-excused {
            background-color: #d1ecf1;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>Teacher Panel</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user me-2"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> Students
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> Courses
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> Exams
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> Results
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="attendance.php">
                            <i class="fas fa-calendar-check me-2"></i> Attendance
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h2 class="mb-4">উপস্থিতি ব্যবস্থাপনা</h2>
                        
                        <?php if (!empty($success_msg)): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <?php echo $success_msg; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($error_msg)): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <?php echo $error_msg; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Filter Section -->
                        <div class="filter-section">
                            <form action="" method="get" class="row g-3">
                                <div class="col-md-3">
                                    <label for="date" class="form-label">তারিখ</label>
                                    <input type="date" class="form-control" id="date" name="date" value="<?php echo $dateFilter; ?>" required>
                                </div>
                                <div class="col-md-3">
                                    <label for="class" class="form-label">শ্রেণী</label>
                                    <select class="form-control" id="class" name="class" required>
                                        <option value="">শ্রেণী নির্বাচন করুন</option>
                                        <?php if ($classes && $classes->num_rows > 0): ?>
                                            <?php 
                                            // Reset pointer to beginning
                                            $classes->data_seek(0);
                                            while ($class = $classes->fetch_assoc()): 
                                            ?>
                                                <option value="<?php echo $class['id']; ?>" <?php echo ($classFilter == $class['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($class['class_name']); ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="subject" class="form-label">বিষয় (ঐচ্ছিক)</label>
                                    <select class="form-control" id="subject" name="subject">
                                        <option value="0">সকল বিষয়</option>
                                        <?php if ($subjects && $subjects->num_rows > 0): ?>
                                            <?php 
                                            // Reset pointer to beginning
                                            $subjects->data_seek(0);
                                            while ($subject = $subjects->fetch_assoc()): 
                                            ?>
                                                <option value="<?php echo $subject['id']; ?>" <?php echo ($subjectFilter == $subject['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($subject['subject_name'] . ' (' . $subject['subject_code'] . ')'); ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="col-md-2 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-search me-2"></i> খুঁজুন
                                    </button>
                                </div>
                            </form>
                        </div>
                        
                        <?php if ($students && $students->num_rows > 0): ?>
                            <!-- Attendance Form -->
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="card-title mb-0">
                                        উপস্থিতি যোগ করুন - 
                                        <?php 
                                            echo date('d F, Y', strtotime($dateFilter));
                                            
                                            if ($classFilter > 0 && $classes) {
                                                $classes->data_seek(0);
                                                while ($class = $classes->fetch_assoc()) {
                                                    if ($class['id'] == $classFilter) {
                                                        echo ' | ' . $class['class_name'];
                                                        break;
                                                    }
                                                }
                                            }
                                            
                                            if ($subjectFilter > 0 && $subjects) {
                                                $subjects->data_seek(0);
                                                while ($subject = $subjects->fetch_assoc()) {
                                                    if ($subject['id'] == $subjectFilter) {
                                                        echo ' | ' . $subject['subject_name'] . ' (' . $subject['subject_code'] . ')';
                                                        break;
                                                    }
                                                }
                                            }
                                        ?>
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <form action="" method="post">
                                        <input type="hidden" name="attendance_date" value="<?php echo $dateFilter; ?>">
                                        <input type="hidden" name="class_id" value="<?php echo $classFilter; ?>">
                                        <input type="hidden" name="subject_id" value="<?php echo $subjectFilter; ?>">
                                        
                                        <div class="mb-3">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <button type="button" class="btn btn-success btn-sm me-2" id="markAllPresent">
                                                        <i class="fas fa-check me-1"></i> সবাইকে উপস্থিত হিসেবে চিহ্নিত করুন
                                                    </button>
                                                    <button type="button" class="btn btn-danger btn-sm" id="markAllAbsent">
                                                        <i class="fas fa-times me-1"></i> সবাইকে অনুপস্থিত হিসেবে চিহ্নিত করুন
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>রোল</th>
                                                        <th>আইডি</th>
                                                        <th>নাম</th>
                                                        <th>স্ট্যাটাস</th>
                                                        <th>মন্তব্য</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php while ($student = $students->fetch_assoc()): 
                                                        $status = isset($existingAttendance[$student['id']]) ? $existingAttendance[$student['id']]['status'] : 'present';
                                                        $remarks = isset($existingAttendance[$student['id']]) ? $existingAttendance[$student['id']]['remarks'] : '';
                                                        
                                                        $rowClass = '';
                                                        switch ($status) {
                                                            case 'present':
                                                                $rowClass = 'attendance-present';
                                                                break;
                                                            case 'absent':
                                                                $rowClass = 'attendance-absent';
                                                                break;
                                                            case 'late':
                                                                $rowClass = 'attendance-late';
                                                                break;
                                                            case 'excused':
                                                                $rowClass = 'attendance-excused';
                                                                break;
                                                        }
                                                    ?>
                                                        <tr class="<?php echo $rowClass; ?>">
                                                            <td><?php echo htmlspecialchars($student['roll_number'] ?? 'N/A'); ?></td>
                                                            <td><?php echo htmlspecialchars($student['student_id']); ?></td>
                                                            <td><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></td>
                                                            <td>
                                                                <select class="form-control attendance-select" name="attendance[<?php echo $student['id']; ?>]" data-student-id="<?php echo $student['id']; ?>">
                                                                    <option value="present" <?php echo ($status == 'present') ? 'selected' : ''; ?>>উপস্থিত</option>
                                                                    <option value="absent" <?php echo ($status == 'absent') ? 'selected' : ''; ?>>অনুপস্থিত</option>
                                                                    <option value="late" <?php echo ($status == 'late') ? 'selected' : ''; ?>>বিলম্বে</option>
                                                                    <option value="excused" <?php echo ($status == 'excused') ? 'selected' : ''; ?>>ছুটি</option>
                                                                </select>
                                                            </td>
                                                            <td>
                                                                <input type="text" class="form-control" name="remarks[<?php echo $student['id']; ?>]" placeholder="মন্তব্য (ঐচ্ছিক)" value="<?php echo htmlspecialchars($remarks); ?>">
                                                            </td>
                                                        </tr>
                                                    <?php endwhile; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                        
                                        <button type="submit" name="submit_attendance" class="btn btn-primary mt-3">
                                            <i class="fas fa-save me-2"></i> উপস্থিতি সংরক্ষণ করুন
                                        </button>
                                    </form>
                                </div>
                            </div>
                        <?php elseif ($classFilter > 0): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> নির্বাচিত শ্রেণীতে কোন শিক্ষার্থী খুঁজে পাওয়া যায়নি।
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> উপস্থিতি যোগ করতে শ্রেণী নির্বাচন করুন।
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-submit form when dropdown changes
            document.getElementById('class').addEventListener('change', function() {
                document.querySelector('form[method="get"]').submit();
            });
            
            document.getElementById('subject').addEventListener('change', function() {
                document.querySelector('form[method="get"]').submit();
            });
            
            // Mark all present button
            document.getElementById('markAllPresent').addEventListener('click', function() {
                const selects = document.querySelectorAll('.attendance-select');
                selects.forEach(function(select) {
                    select.value = 'present';
                    updateRowClass(select);
                });
            });
            
            // Mark all absent button
            document.getElementById('markAllAbsent').addEventListener('click', function() {
                const selects = document.querySelectorAll('.attendance-select');
                selects.forEach(function(select) {
                    select.value = 'absent';
                    updateRowClass(select);
                });
            });
            
            // Update row class when attendance status changes
            const attendanceSelects = document.querySelectorAll('.attendance-select');
            attendanceSelects.forEach(function(select) {
                select.addEventListener('change', function() {
                    updateRowClass(this);
                });
            });
            
            function updateRowClass(select) {
                const row = select.closest('tr');
                row.classList.remove('attendance-present', 'attendance-absent', 'attendance-late', 'attendance-excused');
                
                switch (select.value) {
                    case 'present':
                        row.classList.add('attendance-present');
                        break;
                    case 'absent':
                        row.classList.add('attendance-absent');
                        break;
                    case 'late':
                        row.classList.add('attendance-late');
                        break;
                    case 'excused':
                        row.classList.add('attendance-excused');
                        break;
                }
            }
        });
    </script>
</body>
</html>
