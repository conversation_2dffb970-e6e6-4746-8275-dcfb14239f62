<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['userId'])) {
    header("Location: ../login.php"); 
    exit();
}

require_once '../includes/dbh.inc.php';

// Get student ID from session (assuming 'userId' is the user ID from the 'users' table)
$user_id = $_SESSION['userId']; 
$student_name = $_SESSION['name'] ?? 'Student Name'; // Get student name from session, fallback

// Fetch the student's internal ID from the 'students' table based on the 'user_id'
$student_id_for_certs = null;
$studentInfoStmt = $conn->prepare("SELECT id FROM students WHERE user_id = ?"); 
if ($studentInfoStmt) {
    $studentInfoStmt->bind_param("i", $user_id); 
    $studentInfoStmt->execute();
    $studentInfoResult = $studentInfoStmt->get_result();
    if ($studentInfoRow = $studentInfoResult->fetch_assoc()) {
        $student_id_for_certs = $studentInfoRow['id']; 
    } else {
        // Fallback or error handling if student record not found
        // For now, we might allow viewing the page but show no certificates
        // Or redirect with an error message
    }
    $studentInfoStmt->close();
} else {
    echo "Error preparing student info query: " . $conn->error;
    // Consider more robust error handling
}

// Get certificate types for display
$certificateTypes = ["appreciation" => "প্রশংসা পত্র", "achievement" => "কৃতিত্ব সনদ", "participation" => "অংশগ্রহন সনদ", "completion" => "সমাপ্তি সনদ"];

// Get all certificates for the student using the correct student ID
$certificates = null; // Initialize certificates result
if ($student_id_for_certs !== null) {
    $certificatesQuery = "SELECT * FROM certificates WHERE student_id = ?";
    $stmt = $conn->prepare($certificatesQuery);
    if ($stmt) {
        $stmt->bind_param("i", $student_id_for_certs); 
        $stmt->execute();
        $certificates = $stmt->get_result();
        $stmt->close();
    } else {
        echo "Error preparing certificates query: " . $conn->error;
    }
}

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>আমার সার্টিফিকেট - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="css/student.css"> 
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .certificate-preview {
            border: 2px solid #ddd;
            padding: 20px;
            margin-bottom: 20px;
            position: relative;
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .certificate-preview h3 {
            color: #007bff;
            text-align: center;
            margin-bottom: 15px;
            font-family: 'Arial', sans-serif;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .certificate-content {
            text-align: center;
            margin-bottom: 20px;
            min-height: 50px; /* Ensure space for content */
        }
        .certificate-footer {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            border-top: 1px solid #eee;
            padding-top: 15px;
            font-size: 0.9em;
            color: #555;
        }
        .certificate-watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-30deg);
            opacity: 0.05; /* Lighter watermark */
            font-size: 6rem; /* Adjusted size */
            z-index: 0;
            color: #000;
            white-space: nowrap;
            pointer-events: none; /* Prevent watermark from interfering with clicks */
        }
        .certificate-body {
            position: relative;
            z-index: 1;
        }
        .certificate-actions {
            text-align: center;
            margin-top: 15px;
        }
        /* Basic student sidebar styling */
        .sidebar {
            background-color: #f8f9fa;
            padding: 15px;
            height: 100vh;
            position: sticky;
            top: 0;
        }
        .main-content {
            padding: 20px;
        }
        .sidebar .nav-link {
            color: #333;
        }
        .sidebar .nav-link.active {
            background-color: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>ছাত্র প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php"> 
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php"> 
                            <i class="fas fa-book me-2"></i> কোর্স
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="certificates.php">
                            <i class="fas fa-certificate me-2"></i> সার্টিফিকেট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../logout.php"> 
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>আমার সার্টিফিকেট</h2>
                        <p class="text-muted">এখানে আপনার অর্জিত সকল সার্টিফিকেট দেখতে পারবেন</p>
                    </div>
                </div>

                <!-- Certificate List -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">সার্টিফিকেটসমূহ</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($certificates && $certificates->num_rows > 0): ?>
                            <?php while ($certificate = $certificates->fetch_assoc()): ?>
                                <div class="certificate-preview" 
                                     data-title="<?php echo htmlspecialchars($certificate['title']); ?>"
                                     data-description="<?php echo htmlspecialchars($certificate['description']); ?>"
                                     data-date="<?php echo date('d/m/Y', strtotime($certificate['certificate_date'])); ?>"
                                     data-issued-by="<?php echo htmlspecialchars($certificate['issued_by']); ?>">
                                    <div class="certificate-watermark">ZFAW</div>
                                    <div class="certificate-body">
                                        <h3><?php echo htmlspecialchars($certificate['title']); ?></h3>
                                        <div class="certificate-content">
                                            <p><?php echo htmlspecialchars($certificate['description']); ?></p>
                                        </div>
                                        <div class="certificate-footer">
                                            <div class="certificate-date">
                                                <?php echo date('d/m/Y', strtotime($certificate['certificate_date'])); ?>
                                            </div>
                                            <div class="certificate-signature">
                                                ইস্যু করেছেন: <?php echo htmlspecialchars($certificate['issued_by']); ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="certificate-actions">
                                        <button class="btn btn-success print-certificate"
                                                data-id="<?php echo $certificate['id']; ?>">
                                            <i class="fas fa-print me-2"></i>প্রিন্ট করুন
                                        </button>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        <?php elseif ($student_id_for_certs === null): ?>
                             <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>আপনার ছাত্র রেকর্ড খুঁজে পাওয়া যায়নি। অনুগ্রহ করে এডমিনের সাথে যোগাযোগ করুন।
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>কোনো সার্টিফিকেট পাওয়া যায়নি।
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Print Certificate Iframe (hidden) -->
    <iframe id="printFrame" style="display:none;"></iframe>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        // Pass PHP session name to JavaScript safely
        const studentNameForPrint = <?php echo json_encode($student_name); ?>;

        $(document).ready(function() {
            // Handle print certificate button
            $('.print-certificate').click(function() {
                const certificateElement = $(this).closest('.certificate-preview');
                const title = certificateElement.data('title');
                const description = certificateElement.data('description');
                const date = certificateElement.data('date');
                const issuedBy = certificateElement.data('issued-by');

                // Prepare print content using string concatenation
                // Ensure quotes within the HTML string are properly handled
                let printContent = '<!DOCTYPE html><html><head>
    <?php include 'includes/global-head.php'; ?>
    <title>Print Certificate</title><style>' +
                    'body {font-family: Arial, sans-serif;margin: 0;padding: 0;}' +
                    '.certificate-container {width: 800px;margin: 20px auto;padding: 40px;position: relative;border: 5px solid #007bff; min-height: 500px; display: flex; flex-direction: column; justify-content: space-between;}' +
                    '.certificate-watermark {position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%) rotate(-30deg);opacity: 0.05;font-size: 8rem;z-index: 0;color: #000;white-space: nowrap; pointer-events: none;}' +
                    '.certificate-header {text-align: center;margin-bottom: 20px;border-bottom: 2px solid #007bff;padding-bottom: 10px;}' +
                    '.certificate-title {color: #007bff;font-size: 36px;margin-bottom: 10px;}' +
                    '.certificate-student {font-size: 24px;margin: 40px 0;text-align: center;}' + 
                    '.certificate-content {text-align: center;font-size: 18px;line-height: 1.6;margin: 40px 0; flex-grow: 1;}' + 
                    '.certificate-footer {display: flex;justify-content: space-between;margin-top: 40px;border-top: 1px solid #eee;padding-top: 20px; font-size: 16px;}' + 
                    '.certificate-logo {text-align: center;margin-bottom: 20px;font-size: 24px;font-weight: bold;}' +
                    '@media print {' +
                    '  @page { size: A4 landscape; margin: 1cm; }' + 
                    '  body { margin: 0; }' + 
                    '  .certificate-container { margin: 0; border: 5px solid #007bff; box-shadow: none; }' + 
                    '}' +
                    '</style></head><body>' +
                    '<div class="certificate-container">' +
                    '<div class="certificate-watermark">ZFAW</div>' +
                    '<div class="certificate-logo">ZFAW College Management System</div>' +
                    '<div class="certificate-header"><h1 class="certificate-title">' + title + '</h1></div>' +
                    // Use the JavaScript variable for student name
                    '<div class="certificate-student"><h2>' + studentNameForPrint + '</h2></div>' + 
                    '<div class="certificate-content"><p>' + description + '</p></div>' +
                    '<div class="certificate-footer">' +
                    '<div class="certificate-date">' + date + '</div>' +
                    '<div class="certificate-signature">Issued by: ' + issuedBy + '</div>' +
                    '</div>' +
                    '</div>' +
                    '<script>window.onload = function() { setTimeout(function() { window.print(); }, 500); };</script>' +
                    '</body></html>';
                
                const frame = document.getElementById('printFrame');
                if (frame && frame.contentDocument) {
                    frame.contentDocument.open();
                    frame.contentDocument.write(printContent);
                    frame.contentDocument.close();
                } else {
                    console.error("Could not find print frame or access its contentDocument.");
                }
            });
        });
    </script>
</body>
</html>
