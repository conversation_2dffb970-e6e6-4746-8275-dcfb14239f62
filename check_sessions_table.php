<?php
require_once 'includes/dbh.inc.php';

// Check if sessions table exists
$result = $conn->query("SHOW TABLES LIKE 'sessions'");
if ($result->num_rows > 0) {
    echo "sessions table exists<br>";
    
    // Get table structure
    $structure = $conn->query("DESCRIBE sessions");
    echo "<h3>Table Structure:</h3>";
    echo "<pre>";
    while ($row = $structure->fetch_assoc()) {
        print_r($row);
    }
    echo "</pre>";
    
    // Get sessions data
    $data = $conn->query("SELECT * FROM sessions ORDER BY id");
    echo "<h3>Sessions Data:</h3>";
    echo "<pre>";
    while ($row = $data->fetch_assoc()) {
        print_r($row);
    }
    echo "</pre>";
} else {
    echo "sessions table does not exist<br>";
}
?>
