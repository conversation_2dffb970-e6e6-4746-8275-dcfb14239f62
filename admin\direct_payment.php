<?php
// Disable caching
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");

session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Process payment for a single fee
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_payment'])) {
    $feeId = $_POST['fee_id'] ?? 0;
    $paymentAmount = $_POST['payment_amount'] ?? 0;
    $paymentDate = $_POST['payment_date'] ?? date('Y-m-d');
    $paymentMethod = $_POST['payment_method'] ?? 'cash';
    $receiptNo = $_POST['receipt_no'] ?? '';
    $notes = $_POST['notes'] ?? '';

    if ($feeId > 0 && $paymentAmount > 0) {
        // Get current fee details
        $feeQuery = "SELECT * FROM fees WHERE id = ?";
        $stmt = $conn->prepare($feeQuery);
        $stmt->bind_param('i', $feeId);
        $stmt->execute();
        $feeResult = $stmt->get_result();

        if ($feeResult->num_rows > 0) {
            $fee = $feeResult->fetch_assoc();
            $newPaidAmount = $fee['paid'] + $paymentAmount;

            // Determine payment status
            $paymentStatus = 'due';
            if ($newPaidAmount >= $fee['amount']) {
                $paymentStatus = 'paid';
            } else if ($newPaidAmount > 0) {
                $paymentStatus = 'partial';
            }

            // Start transaction
            $conn->begin_transaction();

            try {
                // Update fee record
                $updateFeeQuery = "UPDATE fees SET paid = ?, payment_status = ?, payment_date = ? WHERE id = ?";
                $stmt = $conn->prepare($updateFeeQuery);
                $stmt->bind_param('dssi', $newPaidAmount, $paymentStatus, $paymentDate, $feeId);
                $stmt->execute();

                // Check if fee_payments table exists
                $checkTableQuery = "SHOW TABLES LIKE 'fee_payments'";
                $tableResult = $conn->query($checkTableQuery);

                if ($tableResult->num_rows === 0) {
                    // Create fee_payments table
                    $createTableQuery = "CREATE TABLE IF NOT EXISTS fee_payments (
                        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
                        fee_id INT(11) NOT NULL,
                        receipt_no VARCHAR(50),
                        amount DECIMAL(10,2) NOT NULL,
                        payment_date DATE NOT NULL,
                        payment_method VARCHAR(50) NOT NULL DEFAULT 'cash',
                        notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (fee_id) REFERENCES fees(id) ON DELETE CASCADE
                    )";
                    $conn->query($createTableQuery);
                }

                // Generate a unique receipt number if not provided
                if (empty($receiptNo)) {
                    $receiptNo = 'RCPT-' . date('Ymd') . '-' . rand(1000, 9999);
                }

                // Add payment record
                $paymentQuery = "INSERT INTO fee_payments (fee_id, receipt_no, amount, payment_date, payment_method, notes)
                               VALUES (?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($paymentQuery);
                $stmt->bind_param('isdsss', $feeId, $receiptNo, $paymentAmount, $paymentDate, $paymentMethod, $notes);
                $stmt->execute();

                // Commit transaction
                $conn->commit();

                $_SESSION['success'] = 'পেমেন্ট সফলভাবে যোগ করা হয়েছে!';
            } catch (Exception $e) {
                // Roll back transaction on error
                $conn->rollback();
                $_SESSION['error'] = 'পেমেন্ট যোগ করতে সমস্যা: ' . $e->getMessage();
            }
        } else {
            $_SESSION['error'] = 'ফি রেকর্ড খুঁজে পাওয়া যায়নি!';
        }
    } else {
        $_SESSION['error'] = 'সমস্ত প্রয়োজনীয় ফিল্ড পূরণ করুন!';
    }

    // Redirect back to fee management page
    header("Location: fee_management.php");
    exit();
}

// If no valid action is provided, redirect to fee management page
header("Location: fee_management.php");
exit();
?>
