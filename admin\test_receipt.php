<?php
session_start();
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Create a test receipt with sample data
$receiptNo = 'TEST-' . date('Ymd') . '-' . rand(1000, 9999);
$schoolName = "জাফর আহমদ ফতেহ আলী ওয়াকফ কলেজ";
$studentName = "মোহাম্মদ রহিম উদ্দিন";
$feeType = "মাসিক বেতন";
$amount = 1500.00;
$paymentDate = date('Y-m-d');
$studentId = "S123456";
$rollNo = "2024001";
$className = "একাদশ শ্রেণী";
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>টেস্ট রিসিপ্ট - <?= htmlspecialchars($receiptNo) ?></title>
    <style>
        body { 
            font-family: "Hind Siliguri", <PERSON><PERSON>, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background-color: #f5f5f5; 
        }
        .print-container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .receipt-copy { 
            border: 2px dashed #333; 
            margin-bottom: 30px; 
            padding: 20px; 
            position: relative; 
        }
        .copy-watermark { 
            position: absolute; 
            top: 10px; 
            right: 15px; 
            background: #007bff; 
            color: white; 
            padding: 5px 10px; 
            border-radius: 15px; 
            font-size: 12px; 
            font-weight: bold; 
        }
        .header { 
            text-align: center; 
            margin-bottom: 20px; 
            border-bottom: 2px solid #333; 
            padding-bottom: 15px; 
        }
        .header h3 { 
            margin: 0; 
            font-size: 18px; 
            color: #333; 
        }
        .row { 
            display: flex; 
            justify-content: space-between; 
            margin-bottom: 8px; 
            padding: 5px 0; 
        }
        .row span:first-child { 
            font-weight: bold; 
            color: #555; 
        }
        .amount { 
            text-align: center; 
            font-size: 16px; 
            font-weight: bold; 
            background: #f8f9fa; 
            padding: 15px; 
            margin: 20px 0; 
            border: 2px solid #007bff; 
            border-radius: 5px; 
        }
        .footer { 
            text-align: center; 
            margin-top: 20px; 
            padding-top: 15px; 
            border-top: 1px solid #ddd; 
            font-size: 12px; 
            color: #666; 
        }
        @media print {
            body { background: white; margin: 0; padding: 0; }
            .print-container { box-shadow: none; margin: 0; padding: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="no-print" style="text-align: center; margin-bottom: 20px;">
        <h2>টেস্ট রিসিপ্ট - নমুনা ডেটা</h2>
        <button onclick="window.print()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">প্রিন্ট করুন</button>
        <a href="fee_management.php" style="margin-left: 10px; background: #6c757d; color: white; text-decoration: none; padding: 10px 20px; border-radius: 5px;">ফিরে যান</a>
    </div>

    <div class="print-container">
        <!-- Office Copy -->
        <div class="receipt-copy">
            <div class="copy-watermark">অফিস কপি</div>
            <div class="receipt-content">
                <div class="header">
                    <h3><?= htmlspecialchars($schoolName) ?></h3>
                    <p style="font-weight: bold; margin: 5px 0;">ফি পেমেন্ট রিসিপ্ট - অফিস কপি</p>
                </div>
                
                <div class="row">
                    <span>রিসিপ্ট নং:</span>
                    <span><?= htmlspecialchars($receiptNo) ?></span>
                </div>
                <div class="row">
                    <span>তারিখ:</span>
                    <span><?= date('d/m/Y', strtotime($paymentDate)) ?></span>
                </div>
                <div class="row">
                    <span>শিক্ষার্থী:</span>
                    <span><?= htmlspecialchars($studentName) ?></span>
                </div>
                <div class="row">
                    <span>রোল নং:</span>
                    <span><?= htmlspecialchars($rollNo) ?></span>
                </div>
                <div class="row">
                    <span>শ্রেণী:</span>
                    <span><?= htmlspecialchars($className) ?></span>
                </div>
                <div class="row">
                    <span>ফি ধরন:</span>
                    <span><?= htmlspecialchars($feeType) ?></span>
                </div>
                
                <div class="amount">পরিশোধিত পরিমাণ: ৳<?= number_format($amount, 2) ?></div>
                
                <div class="footer">
                    <p>ধন্যবাদ!</p>
                    <p>অফিস রেকর্ডের জন্য</p>
                </div>
            </div>
        </div>

        <!-- Student Copy -->
        <div class="receipt-copy">
            <div class="copy-watermark">গ্রাহক কপি</div>
            <div class="receipt-content">
                <div class="header">
                    <h3><?= htmlspecialchars($schoolName) ?></h3>
                    <p style="font-weight: bold; margin: 5px 0;">ফি পেমেন্ট রিসিপ্ট - গ্রাহক কপি</p>
                </div>
                
                <div class="row">
                    <span>রিসিপ্ট নং:</span>
                    <span><?= htmlspecialchars($receiptNo) ?></span>
                </div>
                <div class="row">
                    <span>তারিখ:</span>
                    <span><?= date('d/m/Y', strtotime($paymentDate)) ?></span>
                </div>
                <div class="row">
                    <span>শিক্ষার্থী:</span>
                    <span><?= htmlspecialchars($studentName) ?></span>
                </div>
                <div class="row">
                    <span>রোল নং:</span>
                    <span><?= htmlspecialchars($rollNo) ?></span>
                </div>
                <div class="row">
                    <span>শ্রেণী:</span>
                    <span><?= htmlspecialchars($className) ?></span>
                </div>
                <div class="row">
                    <span>ফি ধরন:</span>
                    <span><?= htmlspecialchars($feeType) ?></span>
                </div>
                
                <div class="amount">পরিশোধিত পরিমাণ: ৳<?= number_format($amount, 2) ?></div>
                
                <div class="footer">
                    <p>ধন্যবাদ!</p>
                    <p>আপনার কপি সংরক্ষণ করুন</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
