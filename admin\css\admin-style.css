/* Admin Panel Global Styles */

/* Font Import */
@import url('https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap');

/* Root Variables */
:root {
    --primary-color: #4361ee;
    --primary-light: #4895ef;
    --primary-dark: #3f37c9;
    --secondary-color: #4cc9f0;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #0ea5e9;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --border-radius: 0.5rem;
    --border-radius-md: 0.75rem;
    --border-radius-lg: 1rem;
    --transition: 0.3s ease;
    --transition-fast: 0.15s ease;
}

/* Global Typography */
body, html, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, select, textarea, label, li, table, th, td {
    font-family: 'Hind Siliguri', 'Noto Sans Bengali', sans-serif !important;
    letter-spacing: -0.01em;
}

/* Body */
body {
    background-color: var(--gray-100);
    color: var(--gray-800);
    line-height: 1.6;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: var(--gray-800);
    line-height: 1.3;
}

.h2 {
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
}

p {
    color: var(--gray-600);
    line-height: 1.6;
}

.text-muted {
    color: var(--gray-500) !important;
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow);
    transition: var(--transition);
    overflow: hidden;
    background: white;
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid var(--gray-200);
    padding: 1rem 1.25rem;
    font-weight: 600;
}

.card-header.bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%) !important;
    color: white;
}

.card-header.bg-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #34d399 100%) !important;
    color: white;
}

.card-header.bg-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #fbbf24 100%) !important;
    color: white;
}

.card-header.bg-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #38bdf8 100%) !important;
    color: white;
}

.card-body {
    padding: 1.5rem;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: var(--transition);
    border: none;
    box-shadow: var(--shadow-sm);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
    text-decoration: none;
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #34d399 100%);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #f87171 100%);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #fbbf24 100%);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #38bdf8 100%);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, var(--gray-500) 0%, var(--gray-400) 100%);
    color: white;
}

.btn-outline-primary {
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-outline-success {
    border: 1px solid var(--success-color);
    color: var(--success-color);
    background: transparent;
}

.btn-outline-success:hover {
    background-color: var(--success-color);
    color: white;
}

.btn-outline-warning {
    border: 1px solid var(--warning-color);
    color: var(--warning-color);
    background: transparent;
}

.btn-outline-warning:hover {
    background-color: var(--warning-color);
    color: white;
}

/* Small Buttons */
.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: var(--border-radius);
}

/* Action Buttons */
.action-btn {
    width: 36px;
    height: 36px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0 3px;
    padding: 0;
    transition: var(--transition);
    text-decoration: none;
}

.action-btn:hover {
    transform: translateY(-3px) scale(1.1);
    text-decoration: none;
}

/* Tables */
.table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    background: white;
}

.table th {
    background-color: var(--gray-100);
    color: var(--gray-700);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    padding: 1rem;
    border-top: none;
    border-bottom: 2px solid var(--gray-200);
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--gray-200);
    color: var(--gray-700);
}

.table tbody tr {
    transition: var(--transition-fast);
}

.table tbody tr:hover {
    background-color: var(--gray-100);
}

.table-responsive {
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow);
    overflow: hidden;
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    padding: 1rem 1.25rem;
}

.alert-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: #065f46;
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: #991b1b;
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: #92400e;
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background-color: rgba(14, 165, 233, 0.1);
    color: #0c4a6e;
    border-left: 4px solid var(--info-color);
}

/* Badges */
.badge {
    padding: 0.35em 0.65em;
    font-weight: 500;
    border-radius: var(--border-radius);
    font-size: 0.75rem;
}

.badge.bg-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #34d399 100%) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #f87171 100%) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #fbbf24 100%) !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #38bdf8 100%) !important;
}

.badge.bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%) !important;
}

/* Form Controls */
.form-control, .form-select {
    border-radius: var(--border-radius);
    padding: 0.65rem 1rem;
    border: 1px solid var(--gray-300);
    transition: var(--transition-fast);
    background: white;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
    outline: none;
}

.form-label {
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Utilities */
.border-bottom {
    border-bottom: 1px solid var(--gray-200) !important;
}

.text-center {
    text-align: center !important;
}

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mb-5 { margin-bottom: 3rem !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.mt-5 { margin-top: 3rem !important; }

.pt-3 { padding-top: 1rem !important; }
.pb-2 { padding-bottom: 0.5rem !important; }
.px-md-4 { padding-left: 1.5rem !important; padding-right: 1.5rem !important; }

/* Responsive */
@media (max-width: 768px) {
    .action-btn {
        width: 32px;
        height: 32px;
        margin: 0 2px;
    }
    
    .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table th,
    .table td {
        padding: 0.75rem 0.5rem;
        font-size: 0.875rem;
    }
}
