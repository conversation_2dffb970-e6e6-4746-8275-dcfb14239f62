<?php
session_start();

// Create or update the database connection
require_once "includes/dbh.inc.php";

// Default admin credentials
$adminUsername = "admin";
$userType = "admin";

// Check if admin user exists
$sql = "SELECT * FROM users WHERE username=? AND user_type=?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("ss", $adminUsername, $userType);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    echo "<h3>অ্যাডমিন ইউজার তৈরি করা হচ্ছে...</h3>";
    
    // Admin doesn't exist, create one
    $adminPassword = "admin123";
    $passwordHash = password_hash($adminPassword, PASSWORD_DEFAULT);
    
    $sql = "INSERT INTO users (username, password, user_type) VALUES (?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sss", $adminUsername, $passwordHash, $userType);
    
    if ($stmt->execute()) {
        echo "<p style='color:green;'>অ্যাডমিন ইউজার সফলভাবে তৈরি করা হয়েছে!</p>";
        echo "<p>অ্যাডমিন পাসওয়ার্ড: admin123</p>";
    } else {
        echo "<p style='color:red;'>অ্যাডমিন ইউজার তৈরি করা যায়নি: " . $stmt->error . "</p>";
    }
}

// Set admin session directly
$_SESSION["userId"] = 1; // Assuming admin ID is 1
$_SESSION["username"] = "admin";
$_SESSION["userType"] = "admin";
$_SESSION["lastActivity"] = time();

// Check if redirect destination is set
$redirectTo = isset($_SESSION["redirect_after_login"]) ? $_SESSION["redirect_after_login"] : "admin/dashboard.php";

// Clear the redirect from session
unset($_SESSION["redirect_after_login"]);

echo "<h3>সেশন সফলভাবে সেট করা হয়েছে!</h3>";
echo "<p>আপনি এখন <strong>অ্যাডমিন</strong> হিসেবে লগইন আছেন।</p>";
echo "<p>পরবর্তী পদক্ষেপ:</p>";
echo "<ul>";
echo "<li><a href='" . $redirectTo . "'>নোটিশে যান</a></li>";
echo "<li><a href='admin/dashboard.php'>ড্যাশবোর্ডে যান</a></li>";
echo "<li><a href='clear_session.php'>লগআউট করুন</a></li>";
echo "</ul>";
?>