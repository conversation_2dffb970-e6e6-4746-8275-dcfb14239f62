<?php
session_start();

// Check if user is logged in and is a teacher
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'teacher') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get teacher information with department name
$userId = $_SESSION['userId'];
$username = $_SESSION['username'] ?? '';

// First try to get teacher by user_id
$sql = "SELECT t.*, d.department_name 
        FROM teachers t
        LEFT JOIN departments d ON t.department_id = d.id
        WHERE t.user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();

// If no results, try to get by username
if ($result->num_rows === 0 && !empty($username)) {
    $sql = "SELECT t.*, d.department_name 
            FROM teachers t
            LEFT JOIN departments d ON t.department_id = d.id
            WHERE t.username = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
}

$teacher = $result->fetch_assoc();

// If still no teacher found, redirect to login
if (!$teacher) {
    header("Location: ../index.php");
    exit();
}

// Handle profile update
$success_msg = '';
$error_msg = '';

if (isset($_POST['update_profile'])) {
    $firstName = $conn->real_escape_string($_POST['first_name']);
    $lastName = $conn->real_escape_string($_POST['last_name']);
    $email = $conn->real_escape_string($_POST['email']);
    $phone = $conn->real_escape_string($_POST['phone']);
    
    // Update teacher information
    $updateQuery = "UPDATE teachers SET 
                    first_name = ?, 
                    last_name = ?, 
                    email = ?, 
                    phone = ? 
                    WHERE id = ?";
    $updateStmt = $conn->prepare($updateQuery);
    $updateStmt->bind_param("ssssi", $firstName, $lastName, $email, $phone, $teacher['id']);
    
    if ($updateStmt->execute()) {
        $success_msg = "প্রোফাইল সফলভাবে আপডেট করা হয়েছে";
        
        // Refresh teacher data
        $stmt->execute();
        $result = $stmt->get_result();
        $teacher = $result->fetch_assoc();
    } else {
        $error_msg = "প্রোফাইল আপডেট করতে সমস্যা হয়েছে: " . $conn->error;
    }
}

// Handle password change
if (isset($_POST['change_password'])) {
    $currentPassword = $_POST['current_password'];
    $newPassword = $_POST['new_password'];
    $confirmPassword = $_POST['confirm_password'];
    
    // Check if password contains non-ASCII characters (like Bangla)
    if (preg_match('/[^\x20-\x7E]/', $newPassword)) {
        $error_msg = "পাসওয়ার্ড অবশ্যই ইংরেজি অক্ষর, সংখ্যা এবং বিশেষ চিহ্ন দিয়ে লিখতে হবে। বাংলায় লেখা যাবে না।";
    } 
    // Check if new password and confirm password match
    else if ($newPassword !== $confirmPassword) {
        $error_msg = "নতুন পাসওয়ার্ড এবং নিশ্চিতকরণ পাসওয়ার্ড মিলছে না";
    } else {
        // Get current password from database
        $passwordQuery = "SELECT password FROM users WHERE username = ?";
        $passwordStmt = $conn->prepare($passwordQuery);
        $passwordStmt->bind_param("s", $teacher['username']);
        $passwordStmt->execute();
        $passwordResult = $passwordStmt->get_result();
        
        if ($passwordResult->num_rows > 0) {
            $userData = $passwordResult->fetch_assoc();
            
            // Verify current password
            if (password_verify($currentPassword, $userData['password'])) {
                // Hash new password
                $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                
                // Update password
                $updatePasswordQuery = "UPDATE users SET password = ? WHERE username = ?";
                $updatePasswordStmt = $conn->prepare($updatePasswordQuery);
                $updatePasswordStmt->bind_param("ss", $hashedPassword, $teacher['username']);
                
                if ($updatePasswordStmt->execute()) {
                    $success_msg = "পাসওয়ার্ড সফলভাবে পরিবর্তন করা হয়েছে";
                } else {
                    $error_msg = "পাসওয়ার্ড পরিবর্তন করতে সমস্যা হয়েছে: " . $conn->error;
                }
            } else {
                $error_msg = "বর্তমান পাসওয়ার্ড সঠিক নয়";
            }
        } else {
            $error_msg = "ইউজার খুঁজে পাওয়া যায়নি";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include __DIR__ . '/includes/global-head.php'; ?>
    <title>Teacher Profile - College Management System</title>
    
    <style>
        body, html, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, select, textarea, label, li, th, td {
            font-family: 'Hind Siliguri', sans-serif;
        }
        
        .nav-tabs .nav-link {
            color: #495057;
        }
        
        .nav-tabs .nav-link.active {
            color: #007bff;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>Teacher Panel</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="profile.php">
                            <i class="fas fa-user me-2"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> Students
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> Courses
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> Exams
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> Results
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">
                            <i class="fas fa-calendar-check me-2"></i> Attendance
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h2 class="mb-4">প্রোফাইল ম্যানেজমেন্ট</h2>
                        
                        <?php if (!empty($success_msg)): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <?php echo $success_msg; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($error_msg)): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <?php echo $error_msg; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>
                        
                        <div class="card">
                            <div class="card-body">
                                <ul class="nav nav-tabs" id="profileTabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" type="button" role="tab" aria-controls="profile" aria-selected="true">প্রোফাইল তথ্য</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="edit-tab" data-bs-toggle="tab" data-bs-target="#edit" type="button" role="tab" aria-controls="edit" aria-selected="false">প্রোফাইল আপডেট</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="password-tab" data-bs-toggle="tab" data-bs-target="#password" type="button" role="tab" aria-controls="password" aria-selected="false">পাসওয়ার্ড পরিবর্তন</button>
                                    </li>
                                </ul>
                                
                                <div class="tab-content p-3" id="profileTabsContent">
                                    <!-- Profile Information Tab -->
                                    <div class="tab-pane fade show active" id="profile" role="tabpanel" aria-labelledby="profile-tab">
                                        <div class="row">
                                            <div class="col-md-3 text-center mb-4">
                                                <?php if (!empty($teacher['profile_photo'])): ?>
                                                    <img src="../uploads/teacher_photos/<?php echo $teacher['profile_photo']; ?>" alt="Profile Photo" class="img-fluid rounded-circle profile-img">
                                                <?php else: ?>
                                                    <img src="https://via.placeholder.com/150" alt="Profile Photo" class="img-fluid rounded-circle profile-img">
                                                <?php endif; ?>
                                            </div>
                                            <div class="col-md-9">
                                                <h3><?php echo $teacher['first_name'] . ' ' . $teacher['last_name']; ?></h3>
                                                <hr>
                                                <div class="row mb-2">
                                                    <div class="col-md-4 fw-bold">শিক্ষক আইডি:</div>
                                                    <div class="col-md-8"><?php echo $teacher['teacher_id']; ?></div>
                                                </div>
                                                <div class="row mb-2">
                                                    <div class="col-md-4 fw-bold">ইউজারনেম:</div>
                                                    <div class="col-md-8"><?php echo $teacher['username'] ?? 'N/A'; ?></div>
                                                </div>
                                                <div class="row mb-2">
                                                    <div class="col-md-4 fw-bold">বিভাগ:</div>
                                                    <div class="col-md-8"><?php echo $teacher['department_name'] ?? 'N/A'; ?></div>
                                                </div>
                                                <div class="row mb-2">
                                                    <div class="col-md-4 fw-bold">পদবি:</div>
                                                    <div class="col-md-8"><?php echo $teacher['designation'] ?? 'N/A'; ?></div>
                                                </div>
                                                <div class="row mb-2">
                                                    <div class="col-md-4 fw-bold">ইমেইল:</div>
                                                    <div class="col-md-8"><?php echo $teacher['email'] ?? 'N/A'; ?></div>
                                                </div>
                                                <div class="row mb-2">
                                                    <div class="col-md-4 fw-bold">ফোন:</div>
                                                    <div class="col-md-8"><?php echo $teacher['phone'] ?? 'N/A'; ?></div>
                                                </div>
                                                <div class="row mb-2">
                                                    <div class="col-md-4 fw-bold">যোগদানের তারিখ:</div>
                                                    <div class="col-md-8">
                                                        <?php 
                                                        echo !empty($teacher['joining_date']) 
                                                            ? date('d F, Y', strtotime($teacher['joining_date'])) 
                                                            : 'N/A'; 
                                                        ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Edit Profile Tab -->
                                    <div class="tab-pane fade" id="edit" role="tabpanel" aria-labelledby="edit-tab">
                                        <form action="" method="post">
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label for="first_name" class="form-label">নামের প্রথম অংশ</label>
                                                    <input type="text" class="form-control" id="first_name" name="first_name" value="<?php echo $teacher['first_name']; ?>" required>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="last_name" class="form-label">নামের শেষ অংশ</label>
                                                    <input type="text" class="form-control" id="last_name" name="last_name" value="<?php echo $teacher['last_name']; ?>">
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="email" class="form-label">ইমেইল</label>
                                                <input type="email" class="form-control" id="email" name="email" value="<?php echo $teacher['email']; ?>">
                                            </div>
                                            <div class="mb-3">
                                                <label for="phone" class="form-label">ফোন নম্বর</label>
                                                <input type="text" class="form-control" id="phone" name="phone" value="<?php echo $teacher['phone']; ?>">
                                            </div>
                                            <button type="submit" name="update_profile" class="btn btn-primary">আপডেট করুন</button>
                                        </form>
                                    </div>
                                    
                                    <!-- Change Password Tab -->
                                    <div class="tab-pane fade" id="password" role="tabpanel" aria-labelledby="password-tab">
                                        <form action="" method="post" id="passwordForm">
                                            <div class="mb-3">
                                                <label for="current_password" class="form-label">বর্তমান পাসওয়ার্ড</label>
                                                <input type="password" class="form-control" id="current_password" name="current_password" required>
                                            </div>
                                            <div class="mb-3">
                                                <label for="new_password" class="form-label">নতুন পাসওয়ার্ড</label>
                                                <input type="password" class="form-control" id="new_password" name="new_password" required>
                                                <div class="form-text">পাসওয়ার্ড অবশ্যই ইংরেজি অক্ষর, সংখ্যা এবং বিশেষ চিহ্ন দিয়ে লিখতে হবে</div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="confirm_password" class="form-label">নতুন পাসওয়ার্ড নিশ্চিত করুন</label>
                                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                            </div>
                                            <button type="submit" name="change_password" class="btn btn-primary">পাসওয়ার্ড পরিবর্তন করুন</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Password validation
        document.getElementById('new_password').addEventListener('input', function(e) {
            const password = e.target.value;
            const hasBangla = /[^\x00-\x7F]/.test(password); // Check for non-ASCII characters
            
            if (hasBangla) {
                e.target.setCustomValidity('পাসওয়ার্ড অবশ্যই ইংরেজি অক্ষর, সংখ্যা এবং বিশেষ চিহ্ন দিয়ে লিখতে হবে');
                e.target.classList.add('is-invalid');
            } else {
                e.target.setCustomValidity('');
                e.target.classList.remove('is-invalid');
            }
        });
        
        // Form validation
        document.getElementById('passwordForm').addEventListener('submit', function(e) {
            const password = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const hasBangla = /[^\x00-\x7F]/.test(password);
            
            if (hasBangla) {
                e.preventDefault();
                alert('পাসওয়ার্ড অবশ্যই ইংরেজি অক্ষর, সংখ্যা এবং বিশেষ চিহ্ন দিয়ে লিখতে হবে');
            } else if (password !== confirmPassword) {
                e.preventDefault();
                alert('নতুন পাসওয়ার্ড এবং নিশ্চিতকরণ পাসওয়ার্ড মিলছে না');
            }
        });
    </script>
</body>
</html>
