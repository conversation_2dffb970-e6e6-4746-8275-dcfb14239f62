<?php
session_start();

// Check if user is logged in and is a student
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'student') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get student information
$userId = $_SESSION['userId'];
$sql = "SELECT s.*, d.department_name
        FROM students s
        LEFT JOIN departments d ON s.department_id = d.id
        WHERE s.user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();
$student = $result->fetch_assoc();

// Create student_subjects table if it doesn't exist
$checkTableQuery = "SHOW TABLES LIKE 'student_subjects'";
$tableExists = $conn->query($checkTableQuery)->num_rows > 0;

if (!$tableExists) {
    // Redirect to setup page if table doesn't exist
    $setupMessage = "বিষয় নির্বাচন টেবিল সেটআপ করা হয়নি। প্রশাসক সাহায্য নিন।";
} else {
    $studentSubjectsTableQuery = "CREATE TABLE IF NOT EXISTS student_subjects (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        student_id INT(11) NOT NULL,
        subject_id INT(11) NOT NULL,
        category VARCHAR(50) NOT NULL,
        selection_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        session_id INT(11) NULL,
        UNIQUE KEY(student_id, subject_id),
        FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
        FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE
    )";
    $conn->query($studentSubjectsTableQuery);
}

// Check if student has already selected subjects
$checkSelectionQuery = "SELECT COUNT(*) as count FROM student_subjects WHERE student_id = ?";
$stmt = $conn->prepare($checkSelectionQuery);
$stmt->bind_param("i", $student['id']);
$stmt->execute();
$checkResult = $stmt->get_result();
$selectionExists = $checkResult->fetch_assoc()['count'] > 0;

// Get required subjects for student's department
$requiredSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code, s.category
                         FROM subjects s
                         JOIN subject_departments sd ON s.id = sd.subject_id
                         WHERE sd.department_id = ? AND s.is_active = 1
                         AND (s.category = 'required' OR s.category LIKE '%required%')
                         ORDER BY s.subject_name";
$stmt = $conn->prepare($requiredSubjectsQuery);
$stmt->bind_param("i", $student['department_id']);
$stmt->execute();
$requiredSubjects = $stmt->get_result();

// Get optional subjects for student's department
$optionalSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code, s.category
                         FROM subjects s
                         JOIN subject_departments sd ON s.id = sd.subject_id
                         WHERE sd.department_id = ? AND s.is_active = 1
                         AND (s.category = 'optional' OR s.category LIKE '%optional%')
                         ORDER BY s.subject_name";
$stmt = $conn->prepare($optionalSubjectsQuery);
$stmt->bind_param("i", $student['department_id']);
$stmt->execute();
$optionalSubjects = $stmt->get_result();

// Get fourth subjects for student's department
$fourthSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code, s.category
                       FROM subjects s
                       JOIN subject_departments sd ON s.id = sd.subject_id
                       WHERE sd.department_id = ? AND s.is_active = 1
                       AND (s.category = 'fourth' OR s.category LIKE '%fourth%')
                       ORDER BY s.subject_name";
$stmt = $conn->prepare($fourthSubjectsQuery);
$stmt->bind_param("i", $student['department_id']);
$stmt->execute();
$fourthSubjects = $stmt->get_result();

// Get current session
$currentSessionQuery = "SELECT id, session_name FROM sessions ORDER BY id DESC LIMIT 1";
$currentSession = $conn->query($currentSessionQuery)->fetch_assoc();

// Handle subject selection submission
if (isset($_POST['submit_selection'])) {
    // Validate selection - total of 7 subjects
    $requiredSubjectIds = isset($_POST['required_subjects']) ? $_POST['required_subjects'] : [];
    $optionalSubjectIds = isset($_POST['optional_subjects']) ? $_POST['optional_subjects'] : [];
    $fourthSubjectIds = isset($_POST['fourth_subjects']) ? $_POST['fourth_subjects'] : [];

    $totalSelected = count($requiredSubjectIds) + count($optionalSubjectIds) + count($fourthSubjectIds);

    if ($totalSelected != 7) {
        $errorMessage = "আপনাকে অবশ্যই ঠিক ৭টি বিষয় নির্বাচন করতে হবে। আপনি {$totalSelected}টি নির্বাচন করেছেন।";
    } elseif (count($fourthSubjectIds) > 1) {
        $errorMessage = "আপনি একটির বেশি ৪র্থ বিষয় নির্বাচন করতে পারবেন না।";
    } else {
        // Begin transaction
        $conn->begin_transaction();

        try {
            // Delete existing selections
            $deleteQuery = "DELETE FROM student_subjects WHERE student_id = ?";
            $stmt = $conn->prepare($deleteQuery);
            $stmt->bind_param("i", $student['id']);
            $stmt->execute();

            // Insert required subjects
            $insertQuery = "INSERT INTO student_subjects (student_id, subject_id, category, session_id) VALUES (?, ?, ?, ?)";
            $stmt = $conn->prepare($insertQuery);

            foreach ($requiredSubjectIds as $subjectId) {
                $category = 'required';
                $stmt->bind_param("iisi", $student['id'], $subjectId, $category, $currentSession['id']);
                $stmt->execute();
            }

            // Insert optional subjects
            foreach ($optionalSubjectIds as $subjectId) {
                $category = 'optional';
                $stmt->bind_param("iisi", $student['id'], $subjectId, $category, $currentSession['id']);
                $stmt->execute();
            }

            // Insert fourth subjects
            foreach ($fourthSubjectIds as $subjectId) {
                $category = 'fourth';
                $stmt->bind_param("iisi", $student['id'], $subjectId, $category, $currentSession['id']);
                $stmt->execute();
            }

            // Commit transaction
            $conn->commit();
            $successMessage = "বিষয় নির্বাচন সফলভাবে সম্পন্ন হয়েছে!";
            $selectionExists = true;

        } catch (Exception $e) {
            // Rollback on error
            $conn->rollback();
            $errorMessage = "বিষয় নির্বাচন করতে সমস্যা হয়েছে: " . $e->getMessage();
        }
    }
}

// Get student's selected subjects
$selectedSubjectsQuery = "SELECT ss.id, ss.category, s.subject_name, s.subject_code
                         FROM student_subjects ss
                         JOIN subjects s ON ss.subject_id = s.id
                         WHERE ss.student_id = ?
                         ORDER BY FIELD(ss.category, 'required', 'optional', 'fourth'), s.subject_name";
$stmt = $conn->prepare($selectedSubjectsQuery);
$stmt->bind_param("i", $student['id']);
$stmt->execute();
$selectedSubjects = $stmt->get_result();

// Create arrays to store selected subject IDs by category
$selectedSubjectIds = [];
$selectedRequiredIds = [];
$selectedOptionalIds = [];
$selectedFourthIds = [];

if ($selectedSubjects && $selectedSubjects->num_rows > 0) {
    while ($subject = $selectedSubjects->fetch_assoc()) {
        $selectedSubjectIds[] = $subject['id'];

        if ($subject['category'] == 'required') {
            $selectedRequiredIds[] = $subject['id'];
        } elseif ($subject['category'] == 'optional') {
            $selectedOptionalIds[] = $subject['id'];
        } elseif ($subject['category'] == 'fourth') {
            $selectedFourthIds[] = $subject['id'];
        }
    }
    // Reset result pointer
    $selectedSubjects->data_seek(0);
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>বিষয় নির্বাচন - কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .subject-selection-container {
            margin-bottom: 30px;
        }
        .subject-card {
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        .subject-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .subject-card.selected {
            border-color: #28a745;
            background-color: rgba(40, 167, 69, 0.1);
        }
        .selection-summary {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .counter-badge {
            font-size: 16px;
            padding: 5px 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>শিক্ষার্থী প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user me-2"></i> প্রোফাইল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="subject_selection.php">
                            <i class="fas fa-book-open me-2"></i> বিষয় নির্বাচন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> কোর্স
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">
                            <i class="fas fa-calendar-check me-2"></i> উপস্থিতি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>বিষয় নির্বাচন</h2>
                        <p class="text-muted">আপনার পছন্দের বিষয়গুলি নির্বাচন করুন</p>
                    </div>
                </div>

                <?php if (isset($setupMessage)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $setupMessage; ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($errorMessage)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $errorMessage; ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($successMessage)): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo $successMessage; ?>
                    </div>
                <?php endif; ?>

                <!-- Selection Instructions -->
                <?php if (!isset($setupMessage)): ?>
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">নির্দেশাবলী</h5>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li>আপনাকে অবশ্যই মোট <strong>৭টি</strong> বিষয় নির্বাচন করতে হবে।</li>
                            <li>আপনার বিভাগের জন্য <strong>আবশ্যিক বিষয়গুলি</strong> অবশ্যই নির্বাচন করতে হবে।</li>
                            <li>ঐচ্ছিক বিষয়গুলি থেকে আপনি বাকি বিষয়গুলি পূরণ করতে পারেন।</li>
                            <li>আপনি একটির বেশি <strong>৪র্থ বিষয়</strong> নির্বাচন করতে পারবেন না।</li>
                            <li>বর্তমান শিক্ষাবর্ষ: <strong><?php echo $currentSession['session_name']; ?></strong></li>
                        </ul>
                    </div>
                </div>

                <?php if ($selectionExists): ?>
                    <!-- Selected Subjects Summary -->
                    <div class="card mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">আপনার নির্বাচিত বিষয়সমূহ</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>বিষয় কোড</th>
                                            <th>বিষয়ের নাম</th>
                                            <th>ক্যাটাগরি</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        // Create an array to store subjects by category
                                        $subjectsByCategory = [
                                            'required' => [],
                                            'optional' => [],
                                            'fourth' => []
                                        ];

                                        // Group subjects by category
                                        if ($selectedSubjects && $selectedSubjects->num_rows > 0) {
                                            $selectedSubjects->data_seek(0);
                                            while ($subject = $selectedSubjects->fetch_assoc()) {
                                                if (isset($subjectsByCategory[$subject['category']])) {
                                                    $subjectsByCategory[$subject['category']][] = $subject;
                                                }
                                            }
                                        }

                                        // Display subjects in order: required, optional, fourth
                                        foreach ($subjectsByCategory as $category => $subjects) {
                                            foreach ($subjects as $subject) {
                                        ?>
                                                <tr>
                                                    <td><?php echo $subject['subject_code']; ?></td>
                                                    <td><?php echo $subject['subject_name']; ?></td>
                                                    <td>
                                                        <?php if ($subject['category'] == 'required'): ?>
                                                            <span class="badge bg-primary">আবশ্যিক</span>
                                                        <?php elseif ($subject['category'] == 'optional'): ?>
                                                            <span class="badge bg-info">ঐচ্ছিক</span>
                                                        <?php elseif ($subject['category'] == 'fourth'): ?>
                                                            <span class="badge bg-warning">৪র্থ বিষয়</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                        <?php
                                            }
                                        }

                                        if ($selectedSubjects && $selectedSubjects->num_rows == 0): ?>
                                            <tr>
                                                <td colspan="3" class="text-center">কোন বিষয় নির্বাচন করা হয়নি</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                            <a href="#selectionForm" class="btn btn-primary mt-3" data-bs-toggle="collapse">বিষয় পুনরায় নির্বাচন করুন</a>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Subject Selection Form -->
                <div id="selectionForm" class="<?php echo $selectionExists ? 'collapse' : ''; ?>">
                    <form method="POST" action="subject_selection.php">
                        <!-- Selection Counter -->
                        <div class="selection-summary mb-4">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h4 class="mb-0">নির্বাচিত বিষয়সমূহ: <span id="selectedCount">0</span>/7</h4>
                                </div>
                                <div class="col-auto">
                                    <div class="d-flex gap-2">
                                        <span class="badge bg-primary counter-badge">আবশ্যিক: <span id="requiredCount">0</span></span>
                                        <span class="badge bg-info counter-badge">ঐচ্ছিক: <span id="optionalCount">0</span></span>
                                        <span class="badge bg-warning counter-badge">৪র্থ বিষয়: <span id="fourthCount">0</span>/1</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Required Subjects -->
                        <div class="subject-selection-container">
                            <h4 class="mb-3">আবশ্যিক বিষয়সমূহ</h4>
                            <div class="row">
                                <?php if ($requiredSubjects && $requiredSubjects->num_rows > 0): ?>
                                    <?php while ($subject = $requiredSubjects->fetch_assoc()): ?>
                                        <div class="col-md-4">
                                            <div class="card subject-card">
                                                <div class="card-body">
                                                    <div class="form-check">
                                                        <input class="form-check-input subject-checkbox required-subject"
                                                               type="checkbox"
                                                               name="required_subjects[]"
                                                               value="<?php echo $subject['id']; ?>"
                                                               id="subject<?php echo $subject['id']; ?>"
                                                               data-category="required"
                                                               <?php echo in_array($subject['id'], $selectedRequiredIds) ? 'checked' : ''; ?>>
                                                        <label class="form-check-label" for="subject<?php echo $subject['id']; ?>">
                                                            <strong><?php echo $subject['subject_name']; ?></strong>
                                                            <br>
                                                            <small class="text-muted"><?php echo $subject['subject_code']; ?></small>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <div class="col-12">
                                        <div class="alert alert-info">কোন আবশ্যিক বিষয় পাওয়া যায়নি</div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Optional Subjects -->
                        <div class="subject-selection-container">
                            <h4 class="mb-3">ঐচ্ছিক বিষয়সমূহ</h4>
                            <div class="row">
                                <?php if ($optionalSubjects && $optionalSubjects->num_rows > 0): ?>
                                    <?php while ($subject = $optionalSubjects->fetch_assoc()): ?>
                                        <div class="col-md-4">
                                            <div class="card subject-card">
                                                <div class="card-body">
                                                    <div class="form-check">
                                                        <input class="form-check-input subject-checkbox optional-subject"
                                                               type="checkbox"
                                                               name="optional_subjects[]"
                                                               value="<?php echo $subject['id']; ?>"
                                                               id="subject<?php echo $subject['id']; ?>"
                                                               data-category="optional"
                                                               <?php echo in_array($subject['id'], $selectedOptionalIds) ? 'checked' : ''; ?>>
                                                        <label class="form-check-label" for="subject<?php echo $subject['id']; ?>">
                                                            <strong><?php echo $subject['subject_name']; ?></strong>
                                                            <br>
                                                            <small class="text-muted"><?php echo $subject['subject_code']; ?></small>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <div class="col-12">
                                        <div class="alert alert-info">কোন ঐচ্ছিক বিষয় পাওয়া যায়নি</div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Fourth Subjects -->
                        <div class="subject-selection-container">
                            <h4 class="mb-3">৪র্থ বিষয় (সর্বোচ্চ ১টি)</h4>
                            <div class="row">
                                <?php if ($fourthSubjects && $fourthSubjects->num_rows > 0): ?>
                                    <?php while ($subject = $fourthSubjects->fetch_assoc()): ?>
                                        <div class="col-md-4">
                                            <div class="card subject-card">
                                                <div class="card-body">
                                                    <div class="form-check">
                                                        <input class="form-check-input subject-checkbox fourth-subject"
                                                               type="checkbox"
                                                               name="fourth_subjects[]"
                                                               value="<?php echo $subject['id']; ?>"
                                                               id="subject<?php echo $subject['id']; ?>"
                                                               data-category="fourth"
                                                               <?php echo in_array($subject['id'], $selectedFourthIds) ? 'checked' : ''; ?>>
                                                        <label class="form-check-label" for="subject<?php echo $subject['id']; ?>">
                                                            <strong><?php echo $subject['subject_name']; ?></strong>
                                                            <br>
                                                            <small class="text-muted"><?php echo $subject['subject_code']; ?></small>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <div class="col-12">
                                        <div class="alert alert-info">কোন ৪র্থ বিষয় পাওয়া যায়নি</div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="d-grid gap-2 col-md-6 mx-auto mt-4 mb-5">
                            <button type="submit" name="submit_selection" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i>বিষয় নির্বাচন সংরক্ষণ করুন
                            </button>
                        </div>
                    </form>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const subjectCheckboxes = document.querySelectorAll('.subject-checkbox');
            const requiredCountElement = document.getElementById('requiredCount');
            const optionalCountElement = document.getElementById('optionalCount');
            const fourthCountElement = document.getElementById('fourthCount');
            const selectedCountElement = document.getElementById('selectedCount');

            // Initial count update
            updateCounts();

            // Update card styles for selected subjects
            subjectCheckboxes.forEach(checkbox => {
                updateCardStyle(checkbox);

                checkbox.addEventListener('change', function() {
                    updateCardStyle(this);
                    updateCounts();

                    // Fourth subject validation - allow only one
                    if (this.dataset.category === 'fourth' && this.checked) {
                        const fourthCheckboxes = document.querySelectorAll('.fourth-subject:checked');
                        if (fourthCheckboxes.length > 1) {
                            fourthCheckboxes.forEach(cb => {
                                if (cb !== this) {
                                    cb.checked = false;
                                    updateCardStyle(cb);
                                }
                            });
                        }
                    }
                });
            });

            function updateCardStyle(checkbox) {
                const card = checkbox.closest('.subject-card');
                if (checkbox.checked) {
                    card.classList.add('selected');
                } else {
                    card.classList.remove('selected');
                }
            }

            function updateCounts() {
                const requiredSelected = document.querySelectorAll('.required-subject:checked').length;
                const optionalSelected = document.querySelectorAll('.optional-subject:checked').length;
                const fourthSelected = document.querySelectorAll('.fourth-subject:checked').length;
                const totalSelected = requiredSelected + optionalSelected + fourthSelected;

                requiredCountElement.textContent = requiredSelected;
                optionalCountElement.textContent = optionalSelected;
                fourthCountElement.textContent = fourthSelected;
                selectedCountElement.textContent = totalSelected;
            }
        });
    </script>
</body>
</html>