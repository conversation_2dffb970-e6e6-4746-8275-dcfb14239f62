<?php
require_once '../includes/dbh.inc.php';

// Check if updated_at column exists
$result = $conn->query("SHOW COLUMNS FROM exams LIKE 'updated_at'");
$columnExists = $result->num_rows > 0;

if (!$columnExists) {
    // Add updated_at column
    $alterQuery = "ALTER TABLE exams ADD COLUMN updated_at TIMESTAMP NULL DEFAULT NULL AFTER created_at";
    
    if ($conn->query($alterQuery)) {
        echo "updated_at column added successfully to exams table!";
    } else {
        echo "Error adding updated_at column: " . $conn->error;
    }
} else {
    echo "updated_at column already exists in exams table.";
}
?>
