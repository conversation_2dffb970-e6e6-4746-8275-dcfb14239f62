<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if staff table exists
$tableCheck = $conn->query("SHOW TABLES LIKE 'staff'");
if ($tableCheck->num_rows == 0) {
    header("Location: ../create_staff_table.php");
    exit();
}

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: staff.php");
    exit();
}

$staffId = $_GET['id'];

// Get staff details
$staffQuery = "SELECT s.*, d.department_name 
               FROM staff s 
               LEFT JOIN departments d ON s.department_id = d.id
               WHERE s.id = '$staffId'";
$staffResult = $conn->query($staffQuery);

if (!$staffResult || $staffResult->num_rows == 0) {
    header("Location: staff.php");
    exit();
}

$staff = $staffResult->fetch_assoc();

// Check if staff has a user account
$hasAccount = false;
$accountDetails = null;

if (!empty($staff['user_id'])) {
    $userQuery = "SELECT * FROM users WHERE id = '{$staff['user_id']}'";
    $userResult = $conn->query($userQuery);
    
    if ($userResult && $userResult->num_rows > 0) {
        $hasAccount = true;
        $accountDetails = $userResult->fetch_assoc();
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>স্টাফ বিবরণ - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css">
    
    <style>
        /* Font Settings */
        body, html, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, select, textarea, label, li, table, th, td {
            font-family: 'Hind Siliguri', sans-serif !important;
        }
        
        /* Sidebar Styles */
        .sidebar {
            background-color: #2c3e50;
            color: white;
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            width: 16.66%;
            overflow-y: auto;
            padding-top: 20px;
            padding-bottom: 60px;
            z-index: 100;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 10px 20px;
            margin-bottom: 5px;
            border-radius: 5px;
            transition: all 0.2s ease;
        }
        
        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .sidebar .nav-link.active {
            background-color: #3498db;
            color: white;
        }
        
        .main-content {
            margin-left: 16.66%;
            padding: 20px;
        }
        
        @media (max-width: 991.98px) {
            .sidebar {
                width: 25%;
            }
            
            .main-content {
                margin-left: 25%;
            }
        }
        
        @media (max-width: 767.98px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            
            .main-content {
                margin-left: 0;
            }
        }
        
        /* Profile Styles */
        .profile-header {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .profile-img {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            border: 5px solid #fff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .info-card {
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.05);
            margin-bottom: 20px;
        }
        
        .info-card .card-header {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        
        .info-item {
            padding: 10px 0;
            border-bottom: 1px solid #f1f1f1;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> স্টাফ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-school me-2"></i> শ্রেণী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">
                            <i class="fas fa-calendar-check me-2"></i> উপস্থিতি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="notices.php">
                            <i class="fas fa-bullhorn me-2"></i> নোটিশ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">
                            <i class="fas fa-cog me-2"></i> সেটিংস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2><i class="fas fa-user-tie me-2"></i> স্টাফ বিবরণ</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">ড্যাশবোর্ড</a></li>
                                <li class="breadcrumb-item"><a href="staff.php">স্টাফ</a></li>
                                <li class="breadcrumb-item active" aria-current="page">স্টাফ বিবরণ</li>
                            </ol>
                        </nav>
                    </div>
                </div>
                
                <!-- Profile Header -->
                <div class="profile-header d-flex flex-column flex-md-row align-items-center">
                    <div class="text-center me-md-4 mb-3 mb-md-0">
                        <?php if (!empty($staff['profile_photo'])): ?>
                            <img src="../uploads/staff/<?php echo $staff['profile_photo']; ?>" alt="Profile" class="profile-img">
                        <?php else: ?>
                            <div class="profile-img d-flex align-items-center justify-content-center bg-secondary text-white">
                                <i class="fas fa-user-tie fa-4x"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div>
                        <h3 class="mb-1"><?php echo htmlspecialchars($staff['first_name'] . ' ' . $staff['last_name']); ?></h3>
                        <p class="text-muted mb-2"><?php echo htmlspecialchars($staff['staff_id']); ?></p>
                        <p class="mb-2">
                            <span class="badge bg-primary"><?php echo htmlspecialchars($staff['designation'] ?? 'স্টাফ'); ?></span>
                            <?php if ($hasAccount): ?>
                                <span class="badge bg-success">অ্যাকাউন্ট আছে</span>
                            <?php endif; ?>
                        </p>
                        <div class="mt-3">
                            <a href="edit_staff.php?id=<?php echo $staff['id']; ?>" class="btn btn-warning btn-sm">
                                <i class="fas fa-edit me-1"></i> সম্পাদনা করুন
                            </a>
                            <a href="staff.php?delete=<?php echo $staff['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই স্টাফকে মুছতে চান?');">
                                <i class="fas fa-trash me-1"></i> মুছে ফেলুন
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Personal Information -->
                    <div class="col-md-6 mb-4">
                        <div class="card info-card">
                            <div class="card-header">
                                <i class="fas fa-user me-2"></i> ব্যক্তিগত তথ্য
                            </div>
                            <div class="card-body">
                                <div class="info-item">
                                    <div class="info-label">পূর্ণ নাম</div>
                                    <div><?php echo htmlspecialchars($staff['first_name'] . ' ' . $staff['last_name']); ?></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">লিঙ্গ</div>
                                    <div>
                                        <?php 
                                        if ($staff['gender'] == 'male') {
                                            echo 'পুরুষ';
                                        } elseif ($staff['gender'] == 'female') {
                                            echo 'মহিলা';
                                        } else {
                                            echo 'অন্যান্য';
                                        }
                                        ?>
                                    </div>
                                </div>
                                <?php if (!empty($staff['dob'])): ?>
                                <div class="info-item">
                                    <div class="info-label">জন্ম তারিখ</div>
                                    <div><?php echo date('d/m/Y', strtotime($staff['dob'])); ?></div>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($staff['joining_date'])): ?>
                                <div class="info-item">
                                    <div class="info-label">যোগদানের তারিখ</div>
                                    <div><?php echo date('d/m/Y', strtotime($staff['joining_date'])); ?></div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Contact Information -->
                    <div class="col-md-6 mb-4">
                        <div class="card info-card">
                            <div class="card-header">
                                <i class="fas fa-address-book me-2"></i> যোগাযোগের তথ্য
                            </div>
                            <div class="card-body">
                                <?php if (!empty($staff['email'])): ?>
                                <div class="info-item">
                                    <div class="info-label">ইমেইল</div>
                                    <div><?php echo htmlspecialchars($staff['email']); ?></div>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($staff['phone'])): ?>
                                <div class="info-item">
                                    <div class="info-label">ফোন</div>
                                    <div><?php echo htmlspecialchars($staff['phone']); ?></div>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($staff['address'])): ?>
                                <div class="info-item">
                                    <div class="info-label">ঠিকানা</div>
                                    <div><?php echo htmlspecialchars($staff['address']); ?></div>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($staff['city']) || !empty($staff['state']) || !empty($staff['postal_code'])): ?>
                                <div class="info-item">
                                    <div class="info-label">শহর/জেলা/পোস্টাল কোড</div>
                                    <div>
                                        <?php 
                                        $location = [];
                                        if (!empty($staff['city'])) $location[] = htmlspecialchars($staff['city']);
                                        if (!empty($staff['state'])) $location[] = htmlspecialchars($staff['state']);
                                        if (!empty($staff['postal_code'])) $location[] = htmlspecialchars($staff['postal_code']);
                                        echo implode(', ', $location);
                                        ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($staff['country'])): ?>
                                <div class="info-item">
                                    <div class="info-label">দেশ</div>
                                    <div><?php echo htmlspecialchars($staff['country']); ?></div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Professional Information -->
                    <div class="col-md-6 mb-4">
                        <div class="card info-card">
                            <div class="card-header">
                                <i class="fas fa-briefcase me-2"></i> পেশাগত তথ্য
                            </div>
                            <div class="card-body">
                                <div class="info-item">
                                    <div class="info-label">স্টাফ আইডি</div>
                                    <div><?php echo htmlspecialchars($staff['staff_id']); ?></div>
                                </div>
                                <?php if (!empty($staff['designation'])): ?>
                                <div class="info-item">
                                    <div class="info-label">পদবি</div>
                                    <div><?php echo htmlspecialchars($staff['designation']); ?></div>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($staff['department_name'])): ?>
                                <div class="info-item">
                                    <div class="info-label">বিভাগ</div>
                                    <div><?php echo htmlspecialchars($staff['department_name']); ?></div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Account Information -->
                    <?php if ($hasAccount): ?>
                    <div class="col-md-6 mb-4">
                        <div class="card info-card">
                            <div class="card-header">
                                <i class="fas fa-user-lock me-2"></i> অ্যাকাউন্ট তথ্য
                            </div>
                            <div class="card-body">
                                <div class="info-item">
                                    <div class="info-label">ইউজারনেম</div>
                                    <div><?php echo htmlspecialchars($accountDetails['username']); ?></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">ইউজার টাইপ</div>
                                    <div><?php echo htmlspecialchars($accountDetails['user_type']); ?></div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">অ্যাকাউন্ট তৈরির তারিখ</div>
                                    <div><?php echo date('d/m/Y', strtotime($accountDetails['created_at'])); ?></div>
                                </div>
                                <div class="mt-3">
                                    <a href="reset_password.php?user_id=<?php echo $accountDetails['id']; ?>" class="btn btn-warning btn-sm">
                                        <i class="fas fa-key me-1"></i> পাসওয়ার্ড রিসেট করুন
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="mt-3">
                    <a href="staff.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> স্টাফ তালিকায় ফিরে যান
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
