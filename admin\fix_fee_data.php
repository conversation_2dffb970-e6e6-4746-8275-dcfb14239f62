<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Function to fix fee data inconsistencies
function fixFeeDataInconsistencies($conn) {
    $results = [];
    
    try {
        $conn->begin_transaction();
        
        // Get all students with their fees and payments
        $query = "SELECT 
                    s.id as student_id,
                    s.first_name,
                    s.last_name,
                    s.student_id as roll,
                    f.id as fee_id,
                    f.fee_type,
                    f.amount as fee_amount,
                    f.paid as fee_paid,
                    f.payment_status,
                    COALESCE(SUM(fp.amount), 0) as actual_payments
                  FROM students s
                  LEFT JOIN fees f ON s.id = f.student_id
                  LEFT JOIN fee_payments fp ON f.id = fp.fee_id
                  WHERE f.id IS NOT NULL
                  GROUP BY f.id
                  ORDER BY s.id, f.id";
        
        $result = $conn->query($query);
        $fixedCount = 0;
        
        while ($row = $result->fetch_assoc()) {
            $feeId = $row['fee_id'];
            $feeAmount = floatval($row['fee_amount']);
            $currentPaid = floatval($row['fee_paid']);
            $actualPayments = floatval($row['actual_payments']);
            
            // Check if there's a discrepancy
            if (abs($currentPaid - $actualPayments) > 0.01) {
                // Fix the paid amount
                $newPaid = min($actualPayments, $feeAmount); // Can't pay more than fee amount
                
                // Determine correct status
                $newStatus = 'due';
                if ($newPaid >= $feeAmount) {
                    $newStatus = 'paid';
                } elseif ($newPaid > 0) {
                    $newStatus = 'partial';
                }
                
                // Update the fee record
                $updateQuery = "UPDATE fees SET paid = ?, payment_status = ? WHERE id = ?";
                $updateStmt = $conn->prepare($updateQuery);
                $updateStmt->bind_param("dsi", $newPaid, $newStatus, $feeId);
                $updateStmt->execute();
                
                $results[] = [
                    'student' => $row['first_name'] . ' ' . $row['last_name'] . ' (' . $row['roll'] . ')',
                    'fee_type' => $row['fee_type'],
                    'old_paid' => $currentPaid,
                    'new_paid' => $newPaid,
                    'actual_payments' => $actualPayments,
                    'old_status' => $row['payment_status'],
                    'new_status' => $newStatus
                ];
                
                $fixedCount++;
            }
        }
        
        $conn->commit();
        
        return [
            'success' => true,
            'fixed_count' => $fixedCount,
            'details' => $results
        ];
        
    } catch (Exception $e) {
        $conn->rollback();
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// Handle form submission
$fixResults = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_data'])) {
    $fixResults = fixFeeDataInconsistencies($conn);
}

// Get current data inconsistencies for preview
$inconsistenciesQuery = "SELECT 
                        s.id as student_id,
                        s.first_name,
                        s.last_name,
                        s.student_id as roll,
                        f.id as fee_id,
                        f.fee_type,
                        f.amount as fee_amount,
                        f.paid as fee_paid,
                        f.payment_status,
                        COALESCE(SUM(fp.amount), 0) as actual_payments
                      FROM students s
                      LEFT JOIN fees f ON s.id = f.student_id
                      LEFT JOIN fee_payments fp ON f.id = fp.fee_id
                      WHERE f.id IS NOT NULL
                      GROUP BY f.id
                      HAVING ABS(f.paid - COALESCE(SUM(fp.amount), 0)) > 0.01
                      ORDER BY s.id, f.id";

$inconsistencies = $conn->query($inconsistenciesQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ফি ডেটা সংশোধন</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: "Hind Siliguri", sans-serif;
        }
        
        .inconsistency-row {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        
        .fixed-row {
            background-color: #d1e7dd;
            border-left: 4px solid #198754;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4 bg-white p-3 rounded shadow-sm">
                    <div>
                        <h2 class="mb-0">
                            <i class="fas fa-tools text-warning me-2"></i>
                            ফি ডেটা সংশোধন
                        </h2>
                        <small class="text-muted">ফি এবং পেমেন্ট ডেটার মধ্যে অসামঞ্জস্য ঠিক করুন</small>
                    </div>
                    <div>
                        <a href="fee_management.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i> ফি ম্যানেজমেন্ট
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <?php if ($fixResults): ?>
            <!-- Fix Results -->
            <div class="row mb-4">
                <div class="col-12">
                    <?php if ($fixResults['success']): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>সফল!</strong> <?php echo $fixResults['fixed_count']; ?>টি ফি রেকর্ড সংশোধন করা হয়েছে।
                        </div>
                        
                        <?php if (!empty($fixResults['details'])): ?>
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">সংশোধিত রেকর্ডসমূহ</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>ছাত্র</th>
                                                    <th>ফি টাইপ</th>
                                                    <th>পুরাতন পেমেন্ট</th>
                                                    <th>নতুন পেমেন্ট</th>
                                                    <th>প্রকৃত পেমেন্ট</th>
                                                    <th>পুরাতন স্ট্যাটাস</th>
                                                    <th>নতুন স্ট্যাটাস</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($fixResults['details'] as $detail): ?>
                                                    <tr class="fixed-row">
                                                        <td><?php echo htmlspecialchars($detail['student']); ?></td>
                                                        <td><?php echo htmlspecialchars($detail['fee_type']); ?></td>
                                                        <td>৳ <?php echo number_format($detail['old_paid'], 2); ?></td>
                                                        <td>৳ <?php echo number_format($detail['new_paid'], 2); ?></td>
                                                        <td>৳ <?php echo number_format($detail['actual_payments'], 2); ?></td>
                                                        <td><?php echo htmlspecialchars($detail['old_status']); ?></td>
                                                        <td><?php echo htmlspecialchars($detail['new_status']); ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong>ত্রুটি!</strong> <?php echo htmlspecialchars($fixResults['error']); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Current Inconsistencies -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                বর্তমান অসামঞ্জস্যসমূহ
                            </h5>
                            <?php if ($inconsistencies->num_rows > 0): ?>
                                <form method="POST" style="display: inline;">
                                    <button type="submit" name="fix_data" class="btn btn-warning" 
                                            onclick="return confirm('আপনি কি নিশ্চিত যে সব অসামঞ্জস্য ঠিক করতে চান?')">
                                        <i class="fas fa-wrench me-1"></i>
                                        সব ঠিক করুন (<?php echo $inconsistencies->num_rows; ?>টি)
                                    </button>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if ($inconsistencies->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>ছাত্র</th>
                                            <th>ফি টাইপ</th>
                                            <th>ফি পরিমাণ</th>
                                            <th>রেকর্ডে পেমেন্ট</th>
                                            <th>প্রকৃত পেমেন্ট</th>
                                            <th>পার্থক্য</th>
                                            <th>বর্তমান স্ট্যাটাস</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($row = $inconsistencies->fetch_assoc()): ?>
                                            <tr class="inconsistency-row">
                                                <td><?php echo htmlspecialchars($row['first_name'] . ' ' . $row['last_name'] . ' (' . $row['roll'] . ')'); ?></td>
                                                <td><?php echo htmlspecialchars($row['fee_type']); ?></td>
                                                <td>৳ <?php echo number_format($row['fee_amount'], 2); ?></td>
                                                <td>৳ <?php echo number_format($row['fee_paid'], 2); ?></td>
                                                <td>৳ <?php echo number_format($row['actual_payments'], 2); ?></td>
                                                <td>৳ <?php echo number_format(abs($row['fee_paid'] - $row['actual_payments']), 2); ?></td>
                                                <td>
                                                    <span class="badge bg-warning"><?php echo htmlspecialchars($row['payment_status']); ?></span>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                <h4 class="text-success">কোন অসামঞ্জস্য নেই!</h4>
                                <p class="text-muted">সব ফি ডেটা সঠিক আছে।</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
