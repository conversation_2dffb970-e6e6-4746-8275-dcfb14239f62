# ক্লাস অনুযায়ী বিষয় নির্বাচন সিস্টেম

## বিবরণ
এই সিস্টেম প্রতিটি ক্লাসের জন্য নির্দিষ্ট বিষয় নির্ধারণ করার সুবিধা প্রদান করে, যা পরীক্ষার ফলাফল তৈরির ভিত্তি হিসেবে কাজ করে।

## মূল বৈশিষ্ট্য

### 1. ক্লাস অনুযায়ী বিষয় কনফিগারেশন
- প্রতিটি ক্লাসের জন্য আলাদা বিষয় নির্ধারণ
- বিভাগ অনুযায়ী বিষয় নির্ধারণ (ঐচ্ছিক)
- বিষয়ের ধরন নির্ধারণ: আবশ্যিক, ঐচ্ছিক, চতুর্থ বিষয়

### 2. বিষয়ের ধরন
- **আবশ্যিক (Required)**: সকল শিক্ষার্থীর জন্য বাধ্যতামূলক
- **ঐচ্ছিক (Optional)**: শিক্ষার্থী নির্বাচন করতে পারে
- **চতুর্থ বিষয় (Fourth)**: অতিরিক্ত বিষয়, আলাদা গণনা

### 3. পরীক্ষার ফলাফলে প্রভাব
- শুধুমাত্র নির্ধারিত বিষয়ে নম্বর দেওয়া যাবে
- গ্রেড ও পজিশন এই বিষয়ের ভিত্তিতে নির্ধারিত হবে
- চতুর্থ বিষয়ের আলাদা গণনা

## ব্যবহারের নির্দেশনা

### ধাপ ১: বিষয় কনফিগারেশন
1. `admin/subjects.php` পেজে যান
2. "ক্লাস অনুযায়ী বিষয় নির্বাচন" কার্ডে "বিষয় কনফিগার করুন" বাটনে ক্লিক করুন
3. ক্লাস নির্বাচন করুন
4. প্রয়োজনে বিভাগ নির্বাচন করুন
5. বিষয়গুলি নির্বাচন করুন এবং ধরন নির্ধারণ করুন
6. "সংরক্ষণ করুন" বাটনে ক্লিক করুন

### ধাপ ২: কনফিগারেশন যাচাই
1. "বর্তমান কনফিগারেশন দেখুন" বাটনে ক্লিক করুন
2. অথবা "টেস্ট করুন" বাটনে ক্লিক করে বিস্তারিত টেস্ট করুন

### ধাপ ৩: পরীক্ষার ফলাফল তৈরি
- পরীক্ষার ফলাফল তৈরির সময় সিস্টেম স্বয়ংক্রিয়ভাবে এই কনফিগারেশন ব্যবহার করবে

## ডেটাবেস স্ট্রাকচার

### class_subjects টেবিল
```sql
CREATE TABLE class_subjects (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    class_id INT(11) NOT NULL,
    department_id INT(11) DEFAULT NULL,
    subject_id INT(11) NOT NULL,
    subject_type ENUM('required', 'optional', 'fourth') DEFAULT 'optional',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_class_subject (class_id, department_id, subject_id)
);
```

## ফাইল তালিকা

### মূল ফাইলসমূহ
- `subjects.php` - মূল বিষয় ব্যবস্থাপনা পেজ (আপডেটেড)
- `get_class_subjects.php` - ক্লাসের বিষয় লোড করার AJAX হ্যান্ডলার
- `save_class_subjects.php` - বিষয় কনফিগারেশন সংরক্ষণের হ্যান্ডলার
- `view_class_subjects.php` - কনফিগারেশন দেখার পেজ

### সহায়ক ফাইলসমূহ
- `class_subjects_helper.php` - সহায়ক ফাংশনসমূহ
- `test_class_subjects.php` - টেস্ট পেজ
- `test_class_subjects_ajax.php` - টেস্ট AJAX হ্যান্ডলার

## API ফাংশনসমূহ

### getClassSubjects($classId, $departmentId, $subjectType)
নির্দিষ্ট ক্লাসের বিষয় তালিকা পেতে

### getRequiredSubjects($classId, $departmentId)
আবশ্যিক বিষয়ের তালিকা পেতে

### getOptionalSubjects($classId, $departmentId)
ঐচ্ছিক বিষয়ের তালিকা পেতে

### getFourthSubjects($classId, $departmentId)
চতুর্থ বিষয়ের তালিকা পেতে

### validateClassSubjectConfig($classId, $departmentId)
কনফিগারেশন যাচাই করতে

## নিরাপত্তা বৈশিষ্ট্য
- সেশন ভিত্তিক অ্যাক্সেস কন্ট্রোল
- SQL ইনজেকশন প্রতিরোধ
- ডেটা ভ্যালিডেশন
- ট্রানজেকশন ব্যবহার

## ভবিষ্যত উন্নতি
- বাল্ক ইমপোর্ট/এক্সপোর্ট
- টেমপ্লেট সিস্টেম
- অটো-অ্যাসাইনমেন্ট রুলস
- রিপোর্ট জেনারেশন

## সমস্যা সমাধান

### সাধারণ সমস্যা
1. **টেবিল তৈরি না হওয়া**: ডেটাবেস পারমিশন চেক করুন
2. **AJAX কাজ না করা**: JavaScript কনসোল চেক করুন
3. **ডেটা সেভ না হওয়া**: ডেটাবেস কানেকশন চেক করুন

### ডিবাগিং
- `test_class_subjects.php` পেজ ব্যবহার করুন
- ব্রাউজার কনসোল চেক করুন
- ডেটাবেস লগ দেখুন

## যোগাযোগ
কোন সমস্যা বা প্রশ্ন থাকলে সিস্টেম অ্যাডমিনিস্ট্রেটরের সাথে যোগাযোগ করুন।
