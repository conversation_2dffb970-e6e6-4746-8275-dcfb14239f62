<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if teachers table exists
$tableCheck = $conn->query("SHOW TABLES LIKE 'teachers'");
if ($tableCheck->num_rows == 0) {
    // Redirect to a page that creates the teachers table
    header("Location: ../create_essential_tables.php");
    exit();
}

// Get departments for dropdown
$departments = [];
$departmentsQuery = "SHOW TABLES LIKE 'departments'";
$departmentsTableExists = $conn->query($departmentsQuery)->num_rows > 0;

if ($departmentsTableExists) {
    $departmentsResult = $conn->query("SELECT * FROM departments ORDER BY department_name");
    if ($departmentsResult && $departmentsResult->num_rows > 0) {
        while ($row = $departmentsResult->fetch_assoc()) {
            $departments[] = $row;
        }
    }
}

// Process form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $teacher_id = $_POST['teacher_id'];
    $first_name = $_POST['first_name'];
    $last_name = $_POST['last_name'];
    $email = $_POST['email'] ?? '';
    $phone = $_POST['phone'] ?? '';
    $gender = $_POST['gender'];
    $dob = $_POST['dob'] ?? null;
    $address = $_POST['address'] ?? '';
    $qualification = $_POST['qualification'] ?? '';
    $joining_date = $_POST['joining_date'] ?? null;
    $department_id = $_POST['department_id'] ?? null;

    // Handle profile photo upload
    $profile_photo = '';
    if (isset($_FILES['profile_photo']) && $_FILES['profile_photo']['error'] == 0) {
        $allowed = ['jpg', 'jpeg', 'png', 'gif'];
        $filename = $_FILES['profile_photo']['name'];
        $fileExt = pathinfo($filename, PATHINFO_EXTENSION);

        // Check if the file extension is allowed
        if (in_array(strtolower($fileExt), $allowed)) {
            // Create upload directory if it doesn't exist
            $uploadDir = '../uploads/teachers/';
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            // Generate a unique filename
            $newFilename = 'teacher_' . $teacher_id . '_' . time() . '.' . $fileExt;
            $destination = $uploadDir . $newFilename;

            // Move the uploaded file
            if (move_uploaded_file($_FILES['profile_photo']['tmp_name'], $destination)) {
                $profile_photo = $newFilename;
            } else {
                $error_message = "ছবি আপলোড করতে সমস্যা হয়েছে।";
            }
        } else {
            $error_message = "অনুমোদিত ফাইল টাইপ: jpg, jpeg, png, gif";
        }
    }

    // Validate required fields
    if (empty($teacher_id) || empty($first_name) || empty($last_name) || empty($gender)) {
        $error_message = "সকল প্রয়োজনীয় ফিল্ড পূরণ করুন।";
    } else {
        // Check if teacher_id already exists
        $checkTeacherId = $conn->query("SELECT * FROM teachers WHERE teacher_id = '$teacher_id'");
        if ($checkTeacherId->num_rows > 0) {
            $error_message = "এই শিক্ষক আইডি ইতিমধ্যে ব্যবহৃত হয়েছে। অন্য একটি আইডি ব্যবহার করুন।";
        } else {
            // Prepare SQL query based on available columns
            $columns = [];
            $values = [];

            // Check each field and add to columns/values if it exists
            $columnsResult = $conn->query("SHOW COLUMNS FROM teachers");
            $availableColumns = [];
            while ($column = $columnsResult->fetch_assoc()) {
                $availableColumns[] = $column['Field'];
            }

            // Add required fields
            $columns[] = 'teacher_id';
            $values[] = "'$teacher_id'";

            $columns[] = 'first_name';
            $values[] = "'$first_name'";

            $columns[] = 'last_name';
            $values[] = "'$last_name'";

            // Add optional fields if they exist in the table
            if (in_array('email', $availableColumns) && !empty($email)) {
                $columns[] = 'email';
                $values[] = "'$email'";
            }

            if (in_array('phone', $availableColumns) && !empty($phone)) {
                $columns[] = 'phone';
                $values[] = "'$phone'";
            }

            if (in_array('gender', $availableColumns)) {
                $columns[] = 'gender';
                $values[] = "'$gender'";
            }

            if (in_array('dob', $availableColumns) && !empty($dob)) {
                $columns[] = 'dob';
                $values[] = "'$dob'";
            }

            if (in_array('address', $availableColumns) && !empty($address)) {
                $columns[] = 'address';
                $values[] = "'$address'";
            }

            if (in_array('qualification', $availableColumns) && !empty($qualification)) {
                $columns[] = 'qualification';
                $values[] = "'$qualification'";
            }

            if (in_array('joining_date', $availableColumns) && !empty($joining_date)) {
                $columns[] = 'joining_date';
                $values[] = "'$joining_date'";
            }

            if (in_array('department_id', $availableColumns) && !empty($department_id)) {
                $columns[] = 'department_id';
                $values[] = "'$department_id'";
            }

            // Add profile photo if uploaded
            if (in_array('profile_photo', $availableColumns) && !empty($profile_photo)) {
                $columns[] = 'profile_photo';
                $values[] = "'$profile_photo'";
            }

            // Build and execute the query
            $columnsStr = implode(', ', $columns);
            $valuesStr = implode(', ', $values);

            $insertQuery = "INSERT INTO teachers ($columnsStr) VALUES ($valuesStr)";

            if ($conn->query($insertQuery)) {
                $success_message = "শিক্ষক সফলভাবে যোগ করা হয়েছে!";

                // Create user account if requested
                if (isset($_POST['create_account']) && $_POST['create_account'] == 1) {
                    $username = $teacher_id;
                    $password = password_hash($teacher_id, PASSWORD_DEFAULT); // Default password is teacher_id

                    $userInsertQuery = "INSERT INTO users (username, password, user_type) VALUES ('$username', '$password', 'teacher')";

                    if ($conn->query($userInsertQuery)) {
                        $userId = $conn->insert_id;

                        // Update teacher record with user_id
                        $conn->query("UPDATE teachers SET user_id = $userId WHERE teacher_id = '$teacher_id'");

                        $success_message .= " ইউজার অ্যাকাউন্টও তৈরি করা হয়েছে। ইউজারনেম: $username";
                    } else {
                        $error_message = "শিক্ষক যোগ করা হয়েছে কিন্তু ইউজার অ্যাকাউন্ট তৈরি করতে সমস্যা হয়েছে: " . $conn->error;
                    }
                }
            } else {
                $error_message = "শিক্ষক যোগ করতে সমস্যা হয়েছে: " . $conn->error;
            }
        }
    }
}

// Generate random 6-digit teacher ID
$nextTeacherId = "TCH-" . mt_rand(100000, 999999);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>নতুন শিক্ষক যোগ করুন - কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css">

    <style>
        /* Font Settings */
        body, html, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, select, textarea, label, li, table, th, td {
            font-family: 'Hind Siliguri', sans-serif !important;
        }

        /* Sidebar Styles */
        .sidebar {
            background-color: #2c3e50;
            color: white;
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            width: 16.66%;
            overflow-y: auto;
            padding-top: 20px;
            padding-bottom: 60px;
            z-index: 100;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 10px 20px;
            margin-bottom: 5px;
            border-radius: 5px;
            transition: all 0.2s ease;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .sidebar .nav-link.active {
            background-color: #3498db;
            color: white;
        }

        .main-content {
            margin-left: 16.66%;
            padding: 20px;
        }

        @media (max-width: 991.98px) {
            .sidebar {
                width: 25%;
            }

            .main-content {
                margin-left: 25%;
            }
        }

        @media (max-width: 767.98px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }

            .main-content {
                margin-left: 0;
            }
        }

        /* Form Styles */
        .form-label {
            font-weight: 500;
        }

        .required-field::after {
            content: " *";
            color: red;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> স্টাফ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-school me-2"></i> শ্রেণী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">
                            <i class="fas fa-calendar-check me-2"></i> উপস্থিতি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="notices.php">
                            <i class="fas fa-bullhorn me-2"></i> নোটিশ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">
                            <i class="fas fa-cog me-2"></i> সেটিংস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2><i class="fas fa-user-plus me-2"></i> নতুন শিক্ষক যোগ করুন</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">ড্যাশবোর্ড</a></li>
                                <li class="breadcrumb-item"><a href="teachers.php">শিক্ষক</a></li>
                                <li class="breadcrumb-item active" aria-current="page">নতুন শিক্ষক যোগ করুন</li>
                            </ol>
                        </nav>
                    </div>
                </div>

                <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-body">
                        <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" enctype="multipart/form-data">
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <h4 class="card-title mb-3">প্রাথমিক তথ্য</h4>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="teacher_id" class="form-label required-field">শিক্ষক আইডি</label>
                                    <input type="text" class="form-control" id="teacher_id" name="teacher_id" value="<?php echo $nextTeacherId; ?>" required>
                                    <small class="text-muted">সিস্টেম স্বয়ংক্রিয়ভাবে একটি ৬ ডিজিটের রেন্ডম আইডি তৈরি করেছে। প্রয়োজনে পরিবর্তন করুন।</small>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="first_name" class="form-label required-field">নামের প্রথম অংশ</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" required>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="last_name" class="form-label required-field">নামের শেষ অংশ</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" required>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="gender" class="form-label required-field">লিঙ্গ</label>
                                    <select class="form-select" id="gender" name="gender" required>
                                        <option value="">লিঙ্গ নির্বাচন করুন</option>
                                        <option value="male">পুরুষ</option>
                                        <option value="female">মহিলা</option>
                                        <option value="other">অন্যান্য</option>
                                    </select>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="dob" class="form-label">জন্ম তারিখ</label>
                                    <input type="date" class="form-control" id="dob" name="dob">
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="joining_date" class="form-label">যোগদানের তারিখ</label>
                                    <input type="date" class="form-control" id="joining_date" name="joining_date" value="<?php echo date('Y-m-d'); ?>">
                                </div>

                                <div class="col-md-12 mb-3">
                                    <label for="profile_photo" class="form-label">প্রোফাইল ছবি</label>
                                    <input type="file" class="form-control" id="profile_photo" name="profile_photo" accept="image/*">
                                    <small class="text-muted">অনুমোদিত ফাইল টাইপ: jpg, jpeg, png, gif</small>
                                    <div id="image-preview" class="mt-2 d-none">
                                        <img src="" alt="Image Preview" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <h4 class="card-title mb-3">যোগাযোগের তথ্য</h4>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">ইমেইল</label>
                                    <input type="email" class="form-control" id="email" name="email">
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">ফোন নম্বর</label>
                                    <input type="text" class="form-control" id="phone" name="phone">
                                </div>

                                <div class="col-md-12 mb-3">
                                    <label for="address" class="form-label">ঠিকানা</label>
                                    <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <h4 class="card-title mb-3">পেশাগত তথ্য</h4>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="department_id" class="form-label">বিভাগ</label>
                                    <select class="form-select" id="department_id" name="department_id">
                                        <option value="">বিভাগ নির্বাচন করুন</option>
                                        <?php foreach ($departments as $department): ?>
                                        <option value="<?php echo $department['id']; ?>"><?php echo $department['department_name']; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="qualification" class="form-label">শিক্ষাগত যোগ্যতা</label>
                                    <input type="text" class="form-control" id="qualification" name="qualification">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <h4 class="card-title mb-3">অ্যাকাউন্ট তথ্য</h4>
                                </div>

                                <div class="col-md-12 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="1" id="create_account" name="create_account">
                                        <label class="form-check-label" for="create_account">
                                            শিক্ষকের জন্য লগইন অ্যাকাউন্ট তৈরি করুন
                                        </label>
                                        <div class="form-text">
                                            অ্যাকাউন্ট তৈরি করলে, ইউজারনেম হবে শিক্ষক আইডি এবং পাসওয়ার্ড হবে শিক্ষক আইডি।
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i> শিক্ষক যোগ করুন
                                    </button>
                                    <a href="teachers.php" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i> বাতিল করুন
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Image preview script
        document.getElementById('profile_photo').addEventListener('change', function(e) {
            const preview = document.getElementById('image-preview');
            const previewImg = preview.querySelector('img');
            const file = e.target.files[0];

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    preview.classList.remove('d-none');
                }
                reader.readAsDataURL(file);
            } else {
                preview.classList.add('d-none');
            }
        });
    </script>
</body>
</html>
