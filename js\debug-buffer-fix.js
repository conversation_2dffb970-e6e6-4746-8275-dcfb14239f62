/**
 * Debug Buffer Fix - Ultimate solution for title bar buffering
 * This script will completely stop all intervals and timers that cause buffering
 */

(function() {
    'use strict';
    
    console.log('🔧 Debug Buffer Fix: Starting...');
    
    // Store original functions
    const originalSetInterval = window.setInterval;
    const originalSetTimeout = window.setTimeout;
    const originalClearInterval = window.clearInterval;
    const originalClearTimeout = window.clearTimeout;
    
    // Track all active intervals and timeouts
    const activeIntervals = new Set();
    const activeTimeouts = new Set();
    
    // Override setInterval to track and prevent problematic intervals
    window.setInterval = function(callback, delay, ...args) {
        // Block intervals that might cause buffering
        if (delay <= 1000) {
            console.log('🚫 Blocked setInterval with delay:', delay);
            return -1; // Return invalid ID
        }
        
        const id = originalSetInterval.call(this, callback, delay, ...args);
        activeIntervals.add(id);
        console.log('✅ Allowed setInterval with ID:', id, 'delay:', delay);
        return id;
    };
    
    // Override setTimeout to track
    window.setTimeout = function(callback, delay, ...args) {
        const id = originalSetTimeout.call(this, callback, delay, ...args);
        activeTimeouts.add(id);
        return id;
    };
    
    // Override clearInterval to track
    window.clearInterval = function(id) {
        activeIntervals.delete(id);
        return originalClearInterval.call(this, id);
    };
    
    // Override clearTimeout to track
    window.clearTimeout = function(id) {
        activeTimeouts.delete(id);
        return originalClearTimeout.call(this, id);
    };
    
    // Function to clear ALL intervals and timeouts
    function clearAllTimers() {
        console.log('🧹 Clearing all timers...');
        
        // Clear tracked intervals
        activeIntervals.forEach(id => {
            originalClearInterval(id);
            console.log('Cleared interval:', id);
        });
        activeIntervals.clear();
        
        // Clear tracked timeouts
        activeTimeouts.forEach(id => {
            originalClearTimeout(id);
            console.log('Cleared timeout:', id);
        });
        activeTimeouts.clear();
        
        // Brute force clear (for intervals created before our override)
        for (let i = 1; i < 10000; i++) {
            try {
                originalClearInterval(i);
                originalClearTimeout(i);
            } catch (e) {
                // Ignore errors
            }
        }
        
        console.log('✅ All timers cleared');
    }
    
    // Function to fix title permanently
    function fixTitle() {
        const correctTitle = 'নিশাত এডুকেশন সেন্টার';
        if (document.title !== correctTitle) {
            document.title = correctTitle;
            console.log('📝 Title fixed to:', correctTitle);
        }
    }
    
    // Function to stop all animations and loading elements
    function stopAllAnimations() {
        // Stop marquee elements
        const marquees = document.querySelectorAll('marquee');
        marquees.forEach(marquee => {
            if (marquee.stop) {
                marquee.stop();
            }
            marquee.style.animation = 'none';
            marquee.style.animationPlayState = 'paused';
        });
        
        // Stop CSS animations that might cause buffering
        const style = document.createElement('style');
        style.textContent = `
            * {
                animation-duration: 0s !important;
                animation-delay: 0s !important;
                transition-duration: 0s !important;
                transition-delay: 0s !important;
            }
            
            .scroll-content {
                animation: none !important;
            }
            
            @keyframes scroll-left {
                0% { transform: translateX(0); }
                100% { transform: translateX(0); }
            }
        `;
        document.head.appendChild(style);
        
        console.log('🎭 All animations stopped');
    }
    
    // Function to prevent AJAX requests that might cause buffering
    function preventProblematicAjax() {
        // Override XMLHttpRequest to prevent automatic requests
        const originalXHR = window.XMLHttpRequest;
        window.XMLHttpRequest = function() {
            const xhr = new originalXHR();
            const originalSend = xhr.send;
            
            xhr.send = function(data) {
                // Only allow user-initiated requests
                if (document.hasFocus()) {
                    return originalSend.call(this, data);
                } else {
                    console.log('🚫 Blocked automatic AJAX request');
                    return;
                }
            };
            
            return xhr;
        };
        
        console.log('🌐 AJAX protection enabled');
    }
    
    // Main initialization
    function init() {
        console.log('🚀 Initializing debug buffer fix...');
        
        // Clear all existing timers
        clearAllTimers();
        
        // Fix title
        fixTitle();
        
        // Stop animations
        stopAllAnimations();
        
        // Prevent problematic AJAX
        preventProblematicAjax();
        
        // Set title once more after a delay
        originalSetTimeout(fixTitle, 100);
        
        console.log('✅ Debug buffer fix initialized successfully');
    }
    
    // Initialize immediately
    init();
    
    // Initialize on DOM ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    }
    
    // Initialize on window load
    window.addEventListener('load', init);
    
    // Expose debug functions globally
    window.debugBufferFix = {
        clearAllTimers: clearAllTimers,
        fixTitle: fixTitle,
        stopAllAnimations: stopAllAnimations,
        getActiveIntervals: () => Array.from(activeIntervals),
        getActiveTimeouts: () => Array.from(activeTimeouts)
    };
    
    console.log('🔧 Debug Buffer Fix: Ready! Use window.debugBufferFix for manual control');
    
})();
