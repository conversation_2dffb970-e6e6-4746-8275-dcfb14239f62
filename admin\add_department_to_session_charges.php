<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Check if department_id column exists in session_charges table
$checkColumnQuery = "SHOW COLUMNS FROM session_charges LIKE 'department_id'";
$columnExists = $conn->query($checkColumnQuery)->num_rows > 0;

if (!$columnExists) {
    // Add department_id column to session_charges table
    $alterQuery = "ALTER TABLE session_charges 
                  ADD COLUMN department_id INT(11) NULL AFTER session_id,
                  ADD FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL";
    
    if ($conn->query($alterQuery)) {
        echo "department_id column added to session_charges table successfully!<br>";
        
        // Update the unique key constraint
        $dropKeyQuery = "ALTER TABLE session_charges DROP INDEX unique_session_fee";
        if ($conn->query($dropKeyQuery)) {
            echo "Dropped old unique key constraint successfully!<br>";
            
            $addKeyQuery = "ALTER TABLE session_charges 
                           ADD CONSTRAINT unique_session_fee UNIQUE KEY (session_id, department_id, fee_type_id)";
            if ($conn->query($addKeyQuery)) {
                echo "Added new unique key constraint successfully!<br>";
                echo "<p>Table structure updated successfully. <a href='session_charges.php'>Go back to Session Charges</a></p>";
            } else {
                echo "Error adding new unique key constraint: " . $conn->error . "<br>";
            }
        } else {
            echo "Error dropping old unique key constraint: " . $conn->error . "<br>";
        }
    } else {
        echo "Error adding department_id column: " . $conn->error . "<br>";
    }
} else {
    echo "department_id column already exists in session_charges table.<br>";
    echo "<p><a href='session_charges.php'>Go back to Session Charges</a></p>";
}
?>
