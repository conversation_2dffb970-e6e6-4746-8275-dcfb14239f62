<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';
require_once 'class_subjects_helper.php';

// Get all classes for testing
$classesQuery = "SELECT * FROM classes ORDER BY class_name";
$classesResult = $conn->query($classesQuery);

// Get all departments for testing
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departmentsResult = $conn->query($departmentsQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>ক্লাস অনুযায়ী বিষয় টেস্ট - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-test-tube me-2"></i>ক্লাস অনুযায়ী বিষয় কনফিগারেশন টেস্ট
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="test_class" class="form-label">ক্লাস নির্বাচন করুন:</label>
                                <select class="form-select" id="test_class" onchange="testClassSubjects()">
                                    <option value="">-- ক্লাস নির্বাচন করুন --</option>
                                    <?php
                                    if ($classesResult && $classesResult->num_rows > 0) {
                                        while ($class = $classesResult->fetch_assoc()) {
                                            echo "<option value='" . $class['id'] . "'>" . htmlspecialchars($class['class_name']) . "</option>";
                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="test_department" class="form-label">বিভাগ নির্বাচন করুন (ঐচ্ছিক):</label>
                                <select class="form-select" id="test_department" onchange="testClassSubjects()">
                                    <option value="">-- সকল বিভাগ --</option>
                                    <?php
                                    if ($departmentsResult && $departmentsResult->num_rows > 0) {
                                        while ($department = $departmentsResult->fetch_assoc()) {
                                            echo "<option value='" . $department['id'] . "'>" . htmlspecialchars($department['department_name']) . "</option>";
                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>

                        <div id="testResults" style="display: none;">
                            <!-- Test results will be displayed here -->
                        </div>

                        <div class="mt-4">
                            <h5>সকল ক্লাসের কনফিগারেশন সারসংক্ষেপ:</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ক্লাস</th>
                                            <th>মোট বিষয়</th>
                                            <th>আবশ্যিক</th>
                                            <th>ঐচ্ছিক</th>
                                            <th>চতুর্থ বিষয়</th>
                                            <th>স্ট্যাটাস</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $allClasses = getClassesWithSubjectConfig();
                                        foreach ($allClasses as $class) {
                                            $validation = validateClassSubjectConfig($class['id']);
                                            $statusClass = $validation['valid'] ? 'success' : 'danger';
                                            $statusIcon = $validation['valid'] ? 'check-circle' : 'exclamation-triangle';
                                            
                                            echo "<tr>";
                                            echo "<td>" . htmlspecialchars($class['class_name']) . "</td>";
                                            echo "<td><span class='badge bg-primary'>" . $class['total_subjects'] . "</span></td>";
                                            echo "<td><span class='badge bg-success'>" . $class['required_count'] . "</span></td>";
                                            echo "<td><span class='badge bg-info'>" . $class['optional_count'] . "</span></td>";
                                            echo "<td><span class='badge bg-warning'>" . $class['fourth_count'] . "</span></td>";
                                            echo "<td><span class='badge bg-$statusClass'><i class='fas fa-$statusIcon me-1'></i>" . 
                                                 ($validation['valid'] ? 'প্রস্তুত' : 'অসম্পূর্ণ') . "</span></td>";
                                            echo "</tr>";
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="mt-4">
                            <a href="subjects.php" class="btn btn-primary">
                                <i class="fas fa-arrow-left me-2"></i>বিষয় ব্যবস্থাপনায় ফিরে যান
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testClassSubjects() {
            const classId = document.getElementById('test_class').value;
            const departmentId = document.getElementById('test_department').value;
            
            if (!classId) {
                document.getElementById('testResults').style.display = 'none';
                return;
            }

            // Show loading
            document.getElementById('testResults').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> লোড হচ্ছে...</div>';
            document.getElementById('testResults').style.display = 'block';

            // Fetch test results via AJAX
            fetch('test_class_subjects_ajax.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `class_id=${classId}&department_id=${departmentId}`
            })
            .then(response => response.text())
            .then(html => {
                document.getElementById('testResults').innerHTML = html;
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('testResults').innerHTML = '<div class="alert alert-danger">টেস্ট ডেটা লোড করতে সমস্যা হয়েছে।</div>';
            });
        }
    </script>
</body>
</html>
