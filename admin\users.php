<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../login.php");
    exit();
}

// Include database connection
require_once '../includes/dbh.inc.php';

// Set current page for sidebar highlighting
$currentPage = 'users.php';

// Process form submission for adding/editing users
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        // Add new user
        if ($_POST['action'] === 'add') {
            $username = $_POST['username'];
            $email = $_POST['email'];
            $password = $_POST['password'];
            $userType = $_POST['userType'];
            $status = isset($_POST['is_active']) ? 'active' : 'inactive';

            // Hash the password
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

            // Check if username already exists
            $checkUsername = $conn->prepare("SELECT id FROM users WHERE username = ?");
            $checkUsername->bind_param("s", $username);
            $checkUsername->execute();
            $checkResult = $checkUsername->get_result();

            if ($checkResult->num_rows > 0) {
                $error_message = "ব্যবহারকারী নাম '$username' ইতিমধ্যে ব্যবহৃত হয়েছে। অনুগ্রহ করে অন্য একটি ব্যবহারকারী নাম ব্যবহার করুন।";
            } else {
                // Insert into database
                $sql = "INSERT INTO users (username, email, password, user_type, status)
                        VALUES (?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("sssss", $username, $email, $hashedPassword, $userType, $status);

                if ($stmt->execute()) {
                    $success_message = "ব্যবহারকারী সফলভাবে যোগ করা হয়েছে।";
                } else {
                    $error_message = "ব্যবহারকারী যোগ করতে সমস্যা হয়েছে: " . $stmt->error;
                }
            }
            $checkUsername->close();

            $stmt->close();
        }

        // Edit existing user
        elseif ($_POST['action'] === 'edit' && isset($_POST['id'])) {
            $id = $_POST['id'];
            $username = $_POST['username'];
            $email = $_POST['email'];
            $userType = $_POST['userType'];
            $status = isset($_POST['is_active']) ? 'active' : 'inactive';

            // Check if username already exists for other users
            $checkUsername = $conn->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
            $checkUsername->bind_param("si", $username, $id);
            $checkUsername->execute();
            $checkResult = $checkUsername->get_result();

            if ($checkResult->num_rows > 0) {
                $error_message = "ব্যবহারকারী নাম '$username' ইতিমধ্যে অন্য একটি অ্যাকাউন্টে ব্যবহৃত হয়েছে। অনুগ্রহ করে অন্য একটি ব্যবহারকারী নাম ব্যবহার করুন।";
            } else {
                // Check if password is being updated
                if (!empty($_POST['password'])) {
                    $password = $_POST['password'];
                    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

                    $sql = "UPDATE users SET username = ?, email = ?, password = ?, user_type = ?, status = ? WHERE id = ?";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("sssssi", $username, $email, $hashedPassword, $userType, $status, $id);
                } else {
                    $sql = "UPDATE users SET username = ?, email = ?, user_type = ?, status = ? WHERE id = ?";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("ssssi", $username, $email, $userType, $status, $id);
                }

                if ($stmt->execute()) {
                    $success_message = "ব্যবহারকারী সফলভাবে আপডেট করা হয়েছে।";
                } else {
                    $error_message = "ব্যবহারকারী আপডেট করতে সমস্যা হয়েছে: " . $stmt->error;
                }
            }
            $checkUsername->close();

            $stmt->close();
        }

        // Delete user
        elseif ($_POST['action'] === 'delete' && isset($_POST['id'])) {
            $id = $_POST['id'];

            // Delete from database
            $sql = "DELETE FROM users WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $id);

            if ($stmt->execute()) {
                $success_message = "ব্যবহারকারী সফলভাবে মুছে ফেলা হয়েছে।";
            } else {
                $error_message = "ব্যবহারকারী মুছতে সমস্যা হয়েছে: " . $stmt->error;
            }

            $stmt->close();
        }
    }
}

// Get user data for editing
$edit_id = isset($_GET['edit']) ? $_GET['edit'] : null;
$edit_data = null;

if ($edit_id) {
    $sql = "SELECT * FROM users WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $edit_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $edit_data = $result->fetch_assoc();
    }

    $stmt->close();
}

// Get all users for display
$sql = "SELECT * FROM users ORDER BY username";
$result = $conn->query($sql);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>ব্যবহারকারী ব্যবস্থাপনা</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css">

    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: #f8f9fa;
        }

        .main-content {
            min-height: 100vh;
            background-color: #fff;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            margin-left: 16.666667%;
            padding: 20px;
        }

        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            background-color: #343a40;
            overflow-y: auto;
        }

        .sidebar h3 {
            color: #ffffff;
            padding: 10px 15px;
            margin-bottom: 20px;
            font-size: 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.75) !important;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover {
            color: #ffffff !important;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar .nav-link.active {
            color: #ffffff !important;
            background-color: #007bff !important;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .card-header {
            border-radius: 10px 10px 0 0;
            padding: 15px 20px;
        }

        .table th {
            background-color: #f8f9fa;
        }

        .btn-sm {
            border-radius: 4px;
        }

        .form-control, .form-select {
            border-radius: 5px;
        }

        @media (max-width: 767.98px) {
            .sidebar {
                position: static;
                height: auto;
                padding-top: 0;
            }

            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <div class="row g-0">
            <!-- Sidebar -->
            <?php include '../includes/admin_sidebar.php'; ?>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>ব্যবহারকারী ব্যবস্থাপনা</h2>
                        <p class="text-muted">ব্যবহারকারী যোগ করুন, সম্পাদনা করুন এবং পরিচালনা করুন</p>
                    </div>
                </div>

                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $error_message; ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo $success_message; ?>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- Add/Edit User Form -->
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">
                                    <?php echo isset($edit_data) ? 'ব্যবহারকারী সম্পাদনা করুন' : 'নতুন ব্যবহারকারী যোগ করুন'; ?>
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="users.php<?php echo isset($edit_data) ? '?edit=' . $edit_data['id'] : ''; ?>">
                                    <?php if (isset($edit_data)): ?>
                                        <input type="hidden" name="id" value="<?php echo $edit_data['id']; ?>">
                                    <?php endif; ?>
                                    <input type="hidden" name="action" value="<?php echo isset($edit_data) ? 'edit' : 'add'; ?>">

                                    <div class="mb-3">
                                        <label for="username" class="form-label">ইউজারনেম*</label>
                                        <input type="text" class="form-control" id="username" name="username"
                                               value="<?php echo isset($edit_data) ? $edit_data['username'] : ''; ?>" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="email" class="form-label">ইমেইল*</label>
                                        <input type="email" class="form-control" id="email" name="email"
                                               value="<?php echo isset($edit_data) ? $edit_data['email'] : ''; ?>" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="password" class="form-label"><?php echo isset($edit_data) ? 'পাসওয়ার্ড (পরিবর্তন করতে চাইলে)' : 'পাসওয়ার্ড*'; ?></label>
                                        <input type="password" class="form-control" id="password" name="password"
                                               <?php echo isset($edit_data) ? '' : 'required'; ?>>
                                        <?php if (isset($edit_data)): ?>
                                            <small class="form-text text-muted">খালি রাখলে পাসওয়ার্ড পরিবর্তন হবে না</small>
                                        <?php endif; ?>
                                    </div>

                                    <div class="mb-3">
                                        <label for="userType" class="form-label">ব্যবহারকারীর ধরন*</label>
                                        <select class="form-select" id="userType" name="userType" required>
                                            <?php
                                            $currentUserType = isset($edit_data) && isset($edit_data['user_type']) ? $edit_data['user_type'] : '';
                                            ?>
                                            <option value="admin" <?php echo ($currentUserType == 'admin') ? 'selected' : ''; ?>>অ্যাডমিন</option>
                                            <option value="teacher" <?php echo ($currentUserType == 'teacher') ? 'selected' : ''; ?>>শিক্ষক</option>
                                            <option value="student" <?php echo ($currentUserType == 'student') ? 'selected' : ''; ?>>শিক্ষার্থী</option>
                                            <option value="staff" <?php echo ($currentUserType == 'staff') ? 'selected' : ''; ?>>কর্মচারী</option>
                                        </select>
                                    </div>

                                    <div class="mb-3 form-check">
                                        <?php
                                        $isActive = false;
                                        if (!isset($edit_data)) {
                                            $isActive = true; // Default for new users
                                        } elseif (isset($edit_data['status'])) {
                                            $isActive = ($edit_data['status'] == 'active');
                                        }
                                        ?>
                                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active"
                                               <?php echo $isActive ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="is_active">সক্রিয়</label>
                                    </div>

                                    <?php if (isset($edit_data)): ?>
                                        <button type="submit" class="btn btn-primary">আপডেট করুন</button>
                                        <a href="users.php" class="btn btn-secondary">বাতিল করুন</a>
                                    <?php else: ?>
                                        <button type="submit" class="btn btn-primary">যোগ করুন</button>
                                    <?php endif; ?>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Users List -->
                    <div class="col-md-8 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">ব্যবহারকারী তালিকা</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>ইউজারনেম</th>
                                                <th>ইমেইল</th>
                                                <th>ব্যবহারকারীর ধরন</th>
                                                <th>স্ট্যাটাস</th>
                                                <th>অ্যাকশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($result && $result->num_rows > 0): ?>
                                                <?php while ($user = $result->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($user['username']); ?></td>
                                                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                                                        <td>
                                                            <?php
                                                            $userType = isset($user['user_type']) ? $user['user_type'] : '';
                                                            switch($userType) {
                                                                case 'admin':
                                                                    echo 'অ্যাডমিন';
                                                                    break;
                                                                case 'teacher':
                                                                    echo 'শিক্ষক';
                                                                    break;
                                                                case 'student':
                                                                    echo 'শিক্ষার্থী';
                                                                    break;
                                                                case 'staff':
                                                                    echo 'কর্মচারী';
                                                                    break;
                                                                default:
                                                                    echo 'অনির্দিষ্ট';
                                                            }
                                                            ?>
                                                        </td>
                                                        <td>
                                                            <?php
                                                            $status = isset($user['status']) ? $user['status'] : 'inactive';
                                                            if ($status == 'active'):
                                                            ?>
                                                                <span class="badge bg-success">সক্রিয়</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-danger">নিষ্ক্রিয়</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <a href="users.php?edit=<?php echo $user['id']; ?>" class="btn btn-sm btn-warning">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <button type="button" class="btn btn-sm btn-danger"
                                                                    data-bs-toggle="modal"
                                                                    data-bs-target="#deleteModal<?php echo $user['id']; ?>">
                                                                <i class="fas fa-trash"></i>
                                                            </button>

                                                            <!-- Delete Confirmation Modal -->
                                                            <div class="modal fade" id="deleteModal<?php echo $user['id']; ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?php echo $user['id']; ?>" aria-hidden="true">
                                                                <div class="modal-dialog">
                                                                    <div class="modal-content">
                                                                        <div class="modal-header">
                                                                            <h5 class="modal-title" id="deleteModalLabel<?php echo $user['id']; ?>">নিশ্চিতকরণ</h5>
                                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                        </div>
                                                                        <div class="modal-body">
                                                                            আপনি কি নিশ্চিত যে আপনি <strong><?php echo htmlspecialchars($user['username']); ?></strong> কে মুছে ফেলতে চান?
                                                                        </div>
                                                                        <div class="modal-footer">
                                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল করুন</button>
                                                                            <form action="" method="post">
                                                                                <input type="hidden" name="action" value="delete">
                                                                                <input type="hidden" name="id" value="<?php echo $user['id']; ?>">
                                                                                <button type="submit" class="btn btn-danger">মুছে ফেলুন</button>
                                                                            </form>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="5" class="text-center">কোন ব্যবহারকারী পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
