# Node.js ও Cordo<PERSON> ইনস্টল করার বিস্তারিত নির্দেশাবলী

## ১. Node.js ইনস্টল করা

### Windows এ Node.js ইনস্টল করুন:

1. **Node.js ডাউনলোড করুন**:
   - https://nodejs.org/en/download/ ওয়েবসাইটে যান
   - Windows Installer (.msi) 64-bit বা 32-bit (আপনার কম্পিউটার অনুসারে) ডাউনলোড করুন
   - LTS (Long Term Support) ভার্সন বেছে নিন সর্বাধিক স্থিরতার জন্য

2. **Node.js ইনস্টল প্রসেস**:
   - ডাউনলোড করা .msi ফাইলে ডাবল-ক্লিক করুন
   - ইনস্টলার উইজার্ড খুলবে, সেখানে "Next" বাটনে ক্লিক করুন
   - লাইসেন্স এগ্রিমেন্ট এক্সেপ্ট করুন
   - ইনস্টলেশন লোকেশন নিশ্চিত করুন (ডিফল্ট রাখা ভালো)
   - ইনস্টল করতে "Next" এ ক্লিক করুন
   - ইনস্টলেশন সম্পন্ন হলে "Finish" এ ক্লিক করুন

3. **ভেরিফাই করুন Node.js সঠিকভাবে ইনস্টল হয়েছে কিনা**:
   - Windows PowerShell বা Command Prompt ওপেন করুন
   - নিচের কমান্ড রান করুন:
     ```
     node --version
     ```
   - আপনার ইনস্টল করা Node.js ভার্সন দেখাবে (যেমন: v18.17.1)
   - npm ভার্সন চেক করুন:
     ```
     npm --version
     ```
   - npm ভার্সন দেখাবে (যেমন: 9.6.7)

## ২. Cordova ইনস্টল করা

### প্রথমে Node.js ইনস্টল করার পর:

1. **Cordova গ্লোবালি ইনস্টল করুন**:
   - Windows PowerShell বা Command Prompt এডমিনিস্ট্রেটর হিসেবে ওপেন করুন 
   - নিচের কমান্ড রান করুন:
     ```
     npm install -g cordova
     ```
   - এটি Cordova গ্লোবালি আপনার সিস্টেমে ইনস্টল করবে

2. **ভেরিফাই করুন Cordova সঠিকভাবে ইনস্টল হয়েছে কিনা**:
   - নিচের কমান্ড রান করুন:
     ```
     cordova --version
     ```
   - সাম্প্রতিক Cordova ভার্সন দেখাবে (যেমন: 12.0.0)

## ৩. অ্যান্ড্রয়েড প্ল্যাটফর্মের জন্য প্রস্তুতি

### Cordova অ্যান্ড্রয়েড অ্যাপ ডেভেলপমেন্টের জন্য প্রয়োজনীয় টুল:

1. **Java Development Kit (JDK) ইনস্টল করুন**:
   - https://adoptium.net/ থেকে OpenJDK ডাউনলোড করুন 
   - সর্বশেষ LTS ভার্সন ইনস্টল করুন (JDK 17 বা 11)
   - ইনস্টলেশন প্রসেস সম্পন্ন করুন
   - JAVA_HOME এনভায়রনমেন্ট ভেরিয়েবল সেট করা আছে কিনা চেক করুন

2. **Android Studio ইনস্টল করুন**:
   - https://developer.android.com/studio থেকে Android Studio ডাউনলোড করুন
   - ইনস্টলার চালিয়ে Android Studio সেটআপ করুন
   - "Custom" ইনস্টলেশন বেছে নিন এবং নিশ্চিত করুন:
     - Android SDK
     - Android SDK Platform
     - Android Virtual Device (emulator)
     - Performance (Intel HAXM)
   - ইনস্টলেশন সম্পন্ন করুন

3. **Android SDK এনভায়রনমেন্ট ভেরিয়েবল সেট করুন**:
   - Windows সার্চে "environment variables" টাইপ করুন
   - "Edit the system environment variables" সিলেক্ট করুন
   - "Environment Variables" বাটনে ক্লিক করুন
   - "System variables" সেকশনে "New" বাটনে ক্লিক করুন
   - যোগ করুন:
     - Variable name: ANDROID_HOME
     - Variable value: [Android SDK লোকেশন, সাধারণত C:\Users\<USER>\AppData\Local\Android\Sdk]
   - "OK" ক্লিক করুন
   - "Path" সিলেক্ট করুন এবং "Edit" ক্লিক করুন
   - "New" ক্লিক করে আরো দুটি পাথ যোগ করুন:
     - %ANDROID_HOME%\platform-tools
     - %ANDROID_HOME%\cmdline-tools\latest\bin
   - "OK" ক্লিক করে সব বন্ধ করুন

## ৪. প্রথম Cordova প্রজেক্ট তৈরি করুন

1. **Cordova প্রজেক্ট তৈরি করুন**:
   - PowerShell/CMD ওপেন করুন
   - আপনার পছন্দের ডিরেক্টরিতে যান (যেমন: `cd D:\xampp\htdocs\`)
   - নিচের কমান্ড রান করুন:
     ```
     cordova create zfaw-mobile com.yourname.zfaw "ZFAW School Management"
     ```

2. **প্রজেক্ট ফোল্ডারে যান**:
   ```
   cd zfaw-mobile
   ```

3. **অ্যান্ড্রয়েড প্ল্যাটফর্ম যোগ করুন**:
   ```
   cordova platform add android
   ```

4. **সিস্টেম চেক করুন**:
   ```
   cordova requirements
   ```
   এটি আপনার সিস্টেমে সমস্ত প্রয়োজনীয় উপাদান ইনস্টল করা আছে কিনা যাচাই করবে।

## ৫. ট্রাবলশুটিং

### সাধারণ সমস্যা ও সমাধান:

1. **npm সমস্যা**:
   - নতুন কমান্ড প্রম্পট/পাওয়ারশেল ওপেন করুন
   - `npm cache clean --force` রান করুন
   - আবার চেষ্টা করুন

2. **অ্যান্ড্রয়েড SDK সমস্যা**:
   - Android Studio ওপেন করুন
   - "SDK Manager" সিলেক্ট করুন
   - প্রয়োজনীয় API লেভেল ইনস্টল করুন (API 30/31/32 রেকমেন্ডেড)
   - "SDK Tools" ট্যাবে যান এবং নিশ্চিত করুন নিচের আইটেমগুলি ইনস্টল করা আছে:
     - Android SDK Build-Tools
     - Android SDK Command-line Tools
     - Android Emulator
     - Android SDK Platform-Tools

3. **গ্রেডল সমস্যা**:
   - গ্রেডল ম্যানুয়ালি ডাউনলোড এবং ইনস্টল করুন
   - `GRADLE_HOME` এনভায়রনমেন্ট ভেরিয়েবল সেট করুন
   - পাথ ভেরিয়েবলে `%GRADLE_HOME%\bin` যোগ করুন 