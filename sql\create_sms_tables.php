<?php
// Include database connection
require_once "../includes/dbh.inc.php";

// Check if students table exists
$check_students_sql = "SHOW TABLES LIKE 'students'";
$students_result = $conn->query($check_students_sql);
$students_table_exists = $students_result->num_rows > 0;

// Create sms_logs table without foreign key constraint
$sql = "CREATE TABLE IF NOT EXISTS sms_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    phone VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('attendance', 'result', 'general') NOT NULL DEFAULT 'general',
    status ENUM('sent', 'failed') NOT NULL DEFAULT 'sent',
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if (!$students_table_exists) {
    echo "<div style='color: orange;'>Warning: students table not found. Foreign key constraint will not be added.</div><br>";
}

if ($conn->query($sql) === TRUE) {
    echo "SMS logs table created successfully<br>";
} else {
    echo "Error creating SMS logs table: " . $conn->error . "<br>";
}

// Create sms_templates table if not exists
$sql = "CREATE TABLE IF NOT EXISTS sms_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    template TEXT NOT NULL,
    type ENUM('attendance', 'result', 'general') NOT NULL DEFAULT 'general',
    is_default TINYINT(1) NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($sql) === TRUE) {
    echo "SMS templates table created successfully<br>";
} else {
    echo "Error creating SMS templates table: " . $conn->error . "<br>";
}

// Insert default templates if not exists
$check_sql = "SELECT COUNT(*) as count FROM sms_templates";
$result = $conn->query($check_sql);
$row = $result->fetch_assoc();

if ($row['count'] == 0) {
    // Insert default attendance template
    $attendance_template = "প্রিয় অভিভাবক, আপনার সন্তান {student_name} (রোল: {roll}, শ্রেণী: {class}) আজ ({date}) ক্লাসে {attendance_status} ছিল। ধন্যবাদ।";
    $sql = "INSERT INTO sms_templates (name, template, type, is_default) VALUES ('ডিফল্ট উপস্থিতি টেমপ্লেট', ?, 'attendance', 1)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $attendance_template);
    $stmt->execute();

    // Insert default result template
    $result_template = "প্রিয় অভিভাবক, আপনার সন্তান {student_name} (রোল: {roll}, শ্রেণী: {class}) এর {exam_name} পরীক্ষার ফলাফল: {marks}/{total_marks}, গ্রেড: {grade}। ধন্যবাদ।";
    $sql = "INSERT INTO sms_templates (name, template, type, is_default) VALUES ('ডিফল্ট ফলাফল টেমপ্লেট', ?, 'result', 1)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $result_template);
    $stmt->execute();

    echo "Default SMS templates inserted successfully<br>";
}

// Create sms_settings table if not exists
$sql = "CREATE TABLE IF NOT EXISTS sms_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    api_name VARCHAR(100) NOT NULL,
    api_key VARCHAR(255) NOT NULL,
    api_url VARCHAR(255) NOT NULL,
    sender_id VARCHAR(50) DEFAULT NULL,
    is_active TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($sql) === TRUE) {
    echo "SMS settings table created successfully<br>";
} else {
    echo "Error creating SMS settings table: " . $conn->error . "<br>";
}

// Create student_groups table if not exists
$sql = "CREATE TABLE IF NOT EXISTS student_groups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    group_name VARCHAR(100) NOT NULL,
    student_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($sql) === TRUE) {
    echo "Student groups table created successfully<br>";
} else {
    echo "Error creating student groups table: " . $conn->error . "<br>";
}

// Insert sample student groups if not exists and if students table exists
if ($students_table_exists) {
    $check_groups_sql = "SELECT COUNT(*) as count FROM student_groups";
    $result = $conn->query($check_groups_sql);

    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();

        if ($row['count'] == 0) {
            // Get some student IDs
            $students_sql = "SELECT id FROM students LIMIT 10";
            $students_result = $conn->query($students_sql);

            if ($students_result && $students_result->num_rows > 0) {
                // Create sample groups
                $groups = ['মেধাবী শিক্ষার্থী', 'ক্রীড়া দল', 'সাংস্কৃতিক দল'];

                foreach ($groups as $group_name) {
                    $students_result->data_seek(0);
                    while ($student = $students_result->fetch_assoc()) {
                        // Randomly assign students to groups
                        if (rand(0, 2) > 0) {
                            $insert_sql = "INSERT INTO student_groups (group_name, student_id) VALUES (?, ?)";
                            $stmt = $conn->prepare($insert_sql);
                            $stmt->bind_param("si", $group_name, $student['id']);
                            $stmt->execute();
                        }
                    }
                }

                echo "Sample student groups created successfully<br>";
            }
        }
    }
} else {
    // If students table doesn't exist, create sample groups with dummy data
    $check_groups_sql = "SELECT COUNT(*) as count FROM student_groups";
    $result = $conn->query($check_groups_sql);

    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();

        if ($row['count'] == 0) {
            // Create sample groups with dummy student IDs
            $groups = ['মেধাবী শিক্ষার্থী', 'ক্রীড়া দল', 'সাংস্কৃতিক দল'];

            foreach ($groups as $group_name) {
                for ($i = 1; $i <= 5; $i++) {
                    $dummy_student_id = $i;
                    $insert_sql = "INSERT INTO student_groups (group_name, student_id) VALUES (?, ?)";
                    $stmt = $conn->prepare($insert_sql);
                    $stmt->bind_param("si", $group_name, $dummy_student_id);
                    $stmt->execute();
                }
            }

            echo "Sample student groups created with dummy data<br>";
        }
    }
}

// Close connection
$conn->close();

echo "<br>All SMS tables created successfully. <a href='../admin/sms_notifications.php'>Go to SMS Notifications</a>";
?>
