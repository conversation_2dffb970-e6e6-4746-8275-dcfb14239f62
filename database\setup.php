<?php
require_once '../includes/dbh.inc.php';

echo "<h1>Running Database Setup</h1>";

// Create users table if it doesn't exist
$usersTableQuery = "CREATE TABLE IF NOT EXISTS users (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    user_type ENUM('admin', 'teacher', 'student', 'staff') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($usersTableQuery)) {
    echo "users table created or already exists!<br>";
} else {
    echo "Error creating users table: " . $conn->error . "<br>";
}

// Create departments table if it doesn't exist
$departmentsTableQuery = "CREATE TABLE IF NOT EXISTS departments (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    department_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($departmentsTableQuery)) {
    echo "departments table created or already exists!<br>";
} else {
    echo "Error creating departments table: " . $conn->error . "<br>";
}

// Create sessions table if it doesn't exist
$sessionsTableQuery = "CREATE TABLE IF NOT EXISTS sessions (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    session_name VARCHAR(50) NOT NULL UNIQUE,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($sessionsTableQuery)) {
    echo "sessions table created or already exists!<br>";
} else {
    echo "Error creating sessions table: " . $conn->error . "<br>";
}

// Create classes table if it doesn't exist
$classesTableQuery = "CREATE TABLE IF NOT EXISTS classes (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    class_name VARCHAR(50) NOT NULL,
    department_id INT(11) NULL,
    section VARCHAR(20) DEFAULT 'A',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
)";

if ($conn->query($classesTableQuery)) {
    echo "classes table created or already exists!<br>";
} else {
    echo "Error creating classes table: " . $conn->error . "<br>";
}

// Create students table if it doesn't exist
$studentsTableQuery = "CREATE TABLE IF NOT EXISTS students (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id VARCHAR(20) NOT NULL UNIQUE,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) NULL,
    phone VARCHAR(20) NULL,
    gender ENUM('male', 'female', 'other') NOT NULL,
    dob DATE NULL,
    address TEXT NULL,
    city VARCHAR(50) NULL,
    state VARCHAR(50) NULL,
    postal_code VARCHAR(20) NULL,
    country VARCHAR(50) NULL DEFAULT 'Bangladesh',
    admission_date DATE NULL,
    profile_photo VARCHAR(255) NULL,
    department_id INT(11) NULL,
    class_id INT(11) NULL,
    session_id INT(11) NULL,
    user_id INT(11) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
)";

if ($conn->query($studentsTableQuery)) {
    echo "students table created or already exists!<br>";
} else {
    echo "Error creating students table: " . $conn->error . "<br>";
}

// Create subjects table if it doesn't exist
$subjectsTableQuery = "CREATE TABLE IF NOT EXISTS subjects (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    subject_name VARCHAR(100) NOT NULL,
    subject_code VARCHAR(20) NOT NULL,
    department_id INT(11) NULL,
    category VARCHAR(255) DEFAULT 'required',
    description TEXT,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE
)";

if ($conn->query($subjectsTableQuery)) {
    echo "subjects table created or already exists!<br>";
} else {
    echo "Error creating subjects table: " . $conn->error . "<br>";
}

// Create a mapping table for subjects to departments (many-to-many)
$subjectDeptTableQuery = "CREATE TABLE IF NOT EXISTS subject_departments (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    subject_id INT(11) NOT NULL,
    department_id INT(11) NOT NULL,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
    UNIQUE KEY (subject_id, department_id)
)";

if ($conn->query($subjectDeptTableQuery)) {
    echo "subject_departments table created or already exists!<br>";
} else {
    echo "Error creating subject_departments table: " . $conn->error . "<br>";
}

// Create student_subjects table if it doesn't exist
$studentSubjectsTableQuery = "CREATE TABLE IF NOT EXISTS student_subjects (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    subject_id INT(11) NOT NULL,
    category VARCHAR(50) NOT NULL,
    selection_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    session_id INT(11) NULL,
    UNIQUE KEY(student_id, subject_id),
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE
)";

if ($conn->query($studentSubjectsTableQuery)) {
    echo "student_subjects table created or already exists!<br>";
} else {
    echo "Error creating student_subjects table: " . $conn->error . "<br>";
}

// Create notices table
$sql = "CREATE TABLE IF NOT EXISTS notices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    target_audience ENUM('all', 'students', 'teachers', 'staff') NOT NULL DEFAULT 'all',
    expiry_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

if ($conn->query($sql) === TRUE) {
    echo "Notices table created successfully<br>";
} else {
    echo "Error creating notices table: " . $conn->error . "<br>";
}

echo "<p>Database setup complete.</p>";
echo "<a href='../admin/dashboard.php'>Go to Admin Dashboard</a>";

$conn->query("SELECT * FROM users");

// Insert a new user
$insertUserQuery = "INSERT INTO users (username, password, user_type) 
VALUES ('admin', '$2y$10$6ELITi8bFAiO5n6O3ypV7ejZ9.87E49FY3y..K9xS0cLYhX0TH3bK', 'admin')";

if ($conn->multi_query($insertUserQuery) === TRUE) {
    echo "<br>New user inserted successfully";
} else {
    echo "<br>Error inserting new user: " . $conn->error;
}
?> 