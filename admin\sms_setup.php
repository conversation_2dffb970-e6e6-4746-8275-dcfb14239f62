<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';
require_once '../includes/functions.php';
require_once '../includes/sms_config.php';

// Initialize variables
$success_message = $error_message = '';

// Get current SMS configuration
$sms_config = get_sms_config();
$api_key = $sms_config['api_key'] ?? '';
$sender_id = $sms_config['sender_id'] ?? '';
$provider = $sms_config['provider'] ?? 'bulksmsbd';
$is_enabled = $sms_config['is_enabled'] ?? false;
$test_mode = $sms_config['test_mode'] ?? true;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_sms_config'])) {
    // Get form data
    $api_key = trim($_POST['api_key']);
    $sender_id = trim($_POST['sender_id']);
    $provider = $_POST['provider'];
    $test_mode = isset($_POST['test_mode']) ? true : false;

    // Validate required fields
    if (empty($api_key) || empty($sender_id)) {
        $error_message = "সব প্রয়োজনীয় ফিল্ড পূরণ করুন।";
    } else {
        // Update configuration
        $new_config = [
            'api_key' => $api_key,
            'sender_id' => $sender_id,
            'provider' => $provider,
            'test_mode' => $test_mode
        ];

        if (update_sms_config($new_config)) {
            $success_message = "SMS কনফিগারেশন সফলভাবে সংরক্ষণ করা হয়েছে।";

            // Refresh configuration
            $sms_config = get_sms_config();
            $is_enabled = $sms_config['is_enabled'];
        } else {
            $error_message = "কনফিগারেশন সংরক্ষণ করতে সমস্যা হয়েছে। ফাইল রাইট পারমিশন চেক করুন।";
        }
    }
}

// Handle test SMS
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_test_sms'])) {
    $test_phone = trim($_POST['test_phone']);
    $test_message = trim($_POST['test_message']);

    if (empty($test_phone) || empty($test_message)) {
        $error_message = "টেস্ট SMS পাঠাতে ফোন নম্বর এবং মেসেজ উভয়ই প্রয়োজন।";
    } else {
        // Force test mode to true for test SMS
        $original_test_mode = $sms_config['test_mode'];
        $sms_config['test_mode'] = false;

        // Send test SMS
        $result = send_sms($test_phone, $test_message);

        // Restore original test mode
        $sms_config['test_mode'] = $original_test_mode;
        update_sms_config(['test_mode' => $original_test_mode]);

        if ($result['status']) {
            $success_message = "টেস্ট SMS সফলভাবে পাঠানো হয়েছে। " . $result['message'];
        } else {
            $error_message = "টেস্ট SMS পাঠাতে সমস্যা: " . $result['message'];
        }
    }
}

// Set current page for sidebar highlighting
$currentPage = 'sms_setup.php';
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>SMS সেটআপ - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    
    <style>
        .config-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .form-label {
            font-weight: 500;
        }
        .required-field::after {
            content: "*";
            color: red;
            margin-left: 4px;
        }
        .api-key-field {
            font-family: monospace;
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>SMS সেটআপ</h2>
                        <p class="text-muted">SMS গেটওয়ে ইন্টিগ্রেশনের জন্য API কনফিগারেশন সেটিংস</p>
                    </div>
                    <div class="col-auto">
                        <a href="sms_ai_tools.php" class="btn btn-primary">
                            <i class="fas fa-robot me-2"></i>SMS AI টুলস
                        </a>
                    </div>
                </div>

                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="card config-card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cog me-2"></i>SMS API কনফিগারেশন
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="">
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="provider" class="form-label required-field">SMS প্রোভাইডার</label>
                                        <select class="form-select" id="provider" name="provider" required>
                                            <option value="bulksmsbd" <?php echo ($provider == 'bulksmsbd') ? 'selected' : ''; ?>>BulkSMSBD</option>
                                            <option value="greenweb" <?php echo ($provider == 'greenweb') ? 'selected' : ''; ?>>GreenWeb</option>
                                            <option value="onnorokom" <?php echo ($provider == 'onnorokom') ? 'selected' : ''; ?>>Onnorokom SMS</option>
                                        </select>
                                        <div class="form-text">আপনার SMS সার্ভিস প্রোভাইডার নির্বাচন করুন</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="api_key" class="form-label required-field">API Key / Username</label>
                                        <input type="text" class="form-control api-key-field" id="api_key" name="api_key" value="<?php echo htmlspecialchars($api_key); ?>" required>
                                        <div class="form-text">SMS প্রোভাইডার থেকে প্রাপ্ত API Key বা Username</div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="sender_id" class="form-label required-field">Sender ID / Password</label>
                                        <input type="text" class="form-control" id="sender_id" name="sender_id" value="<?php echo htmlspecialchars($sender_id); ?>" required>
                                        <div class="form-text">SMS প্রোভাইডার থেকে প্রাপ্ত Sender ID বা Password</div>
                                        <div class="alert alert-warning mt-2 mb-0">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            <strong>সেন্ডার আইডি সম্পর্কে গুরুত্বপূর্ণ তথ্য:</strong> BulkSMSBD-এর ক্ষেত্রে, আপনাকে অবশ্যই তাদের ড্যাশবোর্ড থেকে অনুমোদিত সেন্ডার আইডি ব্যবহার করতে হবে। র‍্যান্ডম সেন্ডার আইডি কাজ করবে না। আপনার অ্যাকাউন্টে অনুমোদিত সেন্ডার আইডি পেতে BulkSMSBD-এর ড্যাশবোর্ডে যান।
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3 mt-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="test_mode" name="test_mode" <?php echo $test_mode ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="test_mode">টেস্ট মোড (SMS আসলে পাঠানো হবে না)</label>
                                        </div>
                                        <div class="form-text">টেস্টিং এর জন্য টেস্ট মোড চালু রাখুন। প্রোডাকশনে যাওয়ার সময় এটি বন্ধ করুন।</div>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>নোট:</strong> SMS গেটওয়ে ব্যবহার করতে আপনাকে অবশ্যই একটি SMS সার্ভিস প্রোভাইডারের সাথে রেজিস্টার করতে হবে এবং তাদের কাছ থেকে API ক্রেডেনশিয়ালস সংগ্রহ করতে হবে।
                            </div>

                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>IP হোয়াইটলিস্টিং:</strong> বেশিরভাগ SMS প্রোভাইডার নিরাপত্তার কারণে IP হোয়াইটলিস্টিং প্রয়োজন করে। আপনার বর্তমান IP অ্যাড্রেস: <strong><?php echo $_SERVER['REMOTE_ADDR']; ?></strong>
                                <br>
                                <small>আপনার SMS প্রোভাইডারের ড্যাশবোর্ডে লগইন করে এই IP অ্যাড্রেসটি হোয়াইটলিস্ট করুন। যতক্ষণ না আপনি IP হোয়াইটলিস্টিং সম্পন্ন করছেন, ততক্ষণ টেস্ট মোড ব্যবহার করুন।</small>
                            </div>

                            <div class="text-end">
                                <button type="submit" name="save_sms_config" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>কনফিগারেশন সংরক্ষণ করুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card config-card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-paper-plane me-2"></i>টেস্ট SMS পাঠান
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (!$is_enabled): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                টেস্ট SMS পাঠাতে আগে SMS কনফিগারেশন সম্পন্ন করুন।
                            </div>
                        <?php else: ?>
                            <form method="post" action="">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="test_phone" class="form-label required-field">ফোন নম্বর</label>
                                            <input type="text" class="form-control" id="test_phone" name="test_phone" placeholder="01XXXXXXXXX" required>
                                            <div class="form-text">যে নম্বরে টেস্ট SMS পাঠাতে চান</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="test_message" class="form-label required-field">মেসেজ</label>
                                            <textarea class="form-control" id="test_message" name="test_message" rows="3" required>এটি একটি টেস্ট মেসেজ। ZFAW সিস্টেম থেকে পাঠানো হয়েছে।</textarea>
                                            <div class="form-text">পাঠানোর জন্য টেস্ট মেসেজ</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-end">
                                    <button type="submit" name="send_test_sms" class="btn btn-success">
                                        <i class="fas fa-paper-plane me-2"></i>টেস্ট SMS পাঠান
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="card config-card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-question-circle me-2"></i>SMS ইন্টিগ্রেশন সম্পর্কে সাহায্য
                        </h5>
                    </div>
                    <div class="card-body">
                        <h6>SMS গেটওয়ে ইন্টিগ্রেশন করার জন্য নিম্নলিখিত পদক্ষেপগুলি অনুসরণ করুন:</h6>
                        <ol>
                            <li>একটি SMS সার্ভিস প্রোভাইডারের সাথে রেজিস্টার করুন (BulkSMSBD, GreenWeb, Onnorokom SMS ইত্যাদি)</li>
                            <li>API ক্রেডেনশিয়ালস সংগ্রহ করুন</li>
                            <li>উপরের ফর্মে API ক্রেডেনশিয়ালস যোগ করুন</li>
                            <li>টেস্টিং এর জন্য টেস্ট মোড ব্যবহার করুন</li>
                            <li>সবকিছু ঠিক থাকলে টেস্ট SMS পাঠিয়ে দেখুন</li>
                            <li>সব ঠিক থাকলে টেস্ট মোড বন্ধ করুন</li>
                        </ol>

                        <div class="mt-3">
                            <h6>জনপ্রিয় SMS সার্ভিস প্রোভাইডার:</h6>
                            <div class="row">
                                <div class="col-md-4 mb-2">
                                    <a href="https://bulksmsbd.com" target="_blank" class="btn btn-outline-info w-100">
                                        <i class="fas fa-external-link-alt me-2"></i>BulkSMSBD
                                    </a>
                                </div>
                                <div class="col-md-4 mb-2">
                                    <a href="https://greenweb.com.bd" target="_blank" class="btn btn-outline-info w-100">
                                        <i class="fas fa-external-link-alt me-2"></i>GreenWeb
                                    </a>
                                </div>
                                <div class="col-md-4 mb-2">
                                    <a href="https://onnorokomsms.com" target="_blank" class="btn btn-outline-info w-100">
                                        <i class="fas fa-external-link-alt me-2"></i>Onnorokom SMS
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
