<?php
// Include database connection
require_once 'includes/dbh.inc.php';

// Get the Humanities group ID
$groupQuery = "SELECT id FROM groups WHERE group_name = 'মানবিক'";
$groupResult = $conn->query($groupQuery);

if ($groupResult && $groupResult->num_rows > 0) {
    $group = $groupResult->fetch_assoc();
    $groupId = $group['id'];
    
    echo "<h2>Humanities Group ID: $groupId</h2>";
    
    // Check subjects assigned to this group
    $subjectsQuery = "SELECT sg.*, s.subject_name, s.subject_code 
                     FROM subject_groups sg 
                     JOIN subjects s ON sg.subject_id = s.id 
                     WHERE sg.group_id = $groupId";
    $subjectsResult = $conn->query($subjectsQuery);
    
    if ($subjectsResult && $subjectsResult->num_rows > 0) {
        echo "<h3>Subjects assigned to Humanities group:</h3>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Subject ID</th><th>Subject Name</th><th>Subject Code</th><th>Type</th><th>Applicable</th></tr>";
        
        while ($subject = $subjectsResult->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$subject['subject_id']}</td>";
            echo "<td>{$subject['subject_name']}</td>";
            echo "<td>{$subject['subject_code']}</td>";
            echo "<td>{$subject['subject_type']}</td>";
            echo "<td>" . ($subject['is_applicable'] ? 'Yes' : 'No') . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>No subjects found for Humanities group.</p>";
    }
    
    // Check if the student's department matches the group
    $studentId = 'STD-601523';
    $studentQuery = "SELECT s.*, d.department_name, d.id as dept_id 
                    FROM students s 
                    LEFT JOIN departments d ON s.department_id = d.id 
                    WHERE s.student_id = '$studentId'";
    $studentResult = $conn->query($studentQuery);
    
    if ($studentResult && $studentResult->num_rows > 0) {
        $student = $studentResult->fetch_assoc();
        echo "<h3>Student Information:</h3>";
        echo "<p>Student ID: {$student['student_id']}</p>";
        echo "<p>Name: {$student['first_name']} {$student['last_name']}</p>";
        echo "<p>Department ID: {$student['department_id']}</p>";
        echo "<p>Department Name: {$student['department_name']}</p>";
        
        // Check if department ID matches group ID
        if ($student['department_id'] == $groupId) {
            echo "<p style='color:green;'>Student's department ID matches the Humanities group ID.</p>";
        } else {
            echo "<p style='color:red;'>Student's department ID does not match the Humanities group ID.</p>";
            
            // Check if there's a mapping between departments and groups
            echo "<h3>Checking department-group mapping:</h3>";
            $mappingQuery = "SELECT * FROM departments WHERE id = {$student['department_id']}";
            $mappingResult = $conn->query($mappingQuery);
            
            if ($mappingResult && $mappingResult->num_rows > 0) {
                $department = $mappingResult->fetch_assoc();
                echo "<p>Department details: ";
                print_r($department);
                echo "</p>";
            }
        }
    }
} else {
    echo "<p>Humanities group not found.</p>";
}

// List all groups and departments for comparison
echo "<h2>All Groups:</h2>";
$allGroupsQuery = "SELECT * FROM groups";
$allGroupsResult = $conn->query($allGroupsQuery);

if ($allGroupsResult && $allGroupsResult->num_rows > 0) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Group Name</th></tr>";
    
    while ($group = $allGroupsResult->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$group['id']}</td>";
        echo "<td>{$group['group_name']}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
}

echo "<h2>All Departments:</h2>";
$allDeptsQuery = "SELECT * FROM departments";
$allDeptsResult = $conn->query($allDeptsQuery);

if ($allDeptsResult && $allDeptsResult->num_rows > 0) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Department Name</th></tr>";
    
    while ($dept = $allDeptsResult->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$dept['id']}</td>";
        echo "<td>{$dept['department_name']}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
}
?>
