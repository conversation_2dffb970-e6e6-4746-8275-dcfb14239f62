<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$messages = [];
$errors = [];

// Function to execute SQL and track results
function executeSQL($conn, $sql, $description) {
    global $messages, $errors;
    
    try {
        if ($conn->query($sql) === TRUE) {
            $messages[] = "✅ $description - সফল";
            return true;
        } else {
            $errors[] = "❌ $description - ব্যর্থ: " . $conn->error;
            return false;
        }
    } catch (Exception $e) {
        $errors[] = "❌ $description - Exception: " . $e->getMessage();
        return false;
    }
}

// 1. Create exams_primary_lower table
$examTableSQL = "CREATE TABLE IF NOT EXISTS exams_primary_lower (
    id INT AUTO_INCREMENT PRIMARY KEY,
    exam_name VARCHAR(255) NOT NULL,
    exam_type VARCHAR(100) NOT NULL,
    class_id INT,
    subject_id INT,
    exam_date DATE NOT NULL,
    start_time TIME,
    end_time TIME,
    total_marks INT NOT NULL DEFAULT 100,
    passing_marks INT NOT NULL DEFAULT 33,
    instructions TEXT,
    status ENUM('upcoming', 'ongoing', 'completed', 'cancelled') DEFAULT 'upcoming',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

executeSQL($conn, $examTableSQL, "ক্লাস ১-২ পরীক্ষা টেবিল তৈরি");

// 2. Create results_primary_lower table
$resultsTableSQL = "CREATE TABLE IF NOT EXISTS results_primary_lower (
    id INT AUTO_INCREMENT PRIMARY KEY,
    exam_id INT NOT NULL,
    student_id INT NOT NULL,
    marks_obtained DECIMAL(5,2),
    grade VARCHAR(10),
    remarks TEXT,
    is_absent BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (exam_id) REFERENCES exams_primary_lower(id) ON DELETE CASCADE
)";

executeSQL($conn, $resultsTableSQL, "ক্লাস ১-২ ফলাফল টেবিল তৈরি");

// 3. First check and add section column to classes table if not exists
$checkSectionColumn = "SHOW COLUMNS FROM classes LIKE 'section'";
$sectionColumnResult = $conn->query($checkSectionColumn);
if ($sectionColumnResult->num_rows == 0) {
    $addSectionSQL = "ALTER TABLE classes ADD COLUMN section VARCHAR(20) DEFAULT 'A'";
    executeSQL($conn, $addSectionSQL, "classes টেবিলে section কলাম যোগ");
}

// 4. Create classes for 1-2 if not exist
$class1SQL = "INSERT IGNORE INTO classes (class_name, department_id, created_at)
              VALUES ('১', 1, NOW())";
executeSQL($conn, $class1SQL, "ক্লাস ১ তৈরি");

$class2SQL = "INSERT IGNORE INTO classes (class_name, department_id, created_at)
              VALUES ('২', 1, NOW())";
executeSQL($conn, $class2SQL, "ক্লাস ২ তৈরি");

// 5. Check subjects table structure and add class_id if needed
$checkClassIdColumn = "SHOW COLUMNS FROM subjects LIKE 'class_id'";
$classIdColumnResult = $conn->query($checkClassIdColumn);
if ($classIdColumnResult->num_rows == 0) {
    $addClassIdSQL = "ALTER TABLE subjects ADD COLUMN class_id INT(11) NULL";
    executeSQL($conn, $addClassIdSQL, "subjects টেবিলে class_id কলাম যোগ");
}

// 6. Create basic subjects for classes 1-2
$subjects = [
    ['name' => 'বাংলা', 'code' => 'BAN'],
    ['name' => 'ইংরেজি', 'code' => 'ENG'],
    ['name' => 'গণিত', 'code' => 'MATH'],
    ['name' => 'পরিবেশ পরিচিতি', 'code' => 'ENV'],
    ['name' => 'ধর্ম ও নৈতিক শিক্ষা', 'code' => 'REL']
];

// Get class IDs for subject creation
$class1Result = $conn->query("SELECT id FROM classes WHERE class_name = '১' LIMIT 1");
$class2Result = $conn->query("SELECT id FROM classes WHERE class_name = '২' LIMIT 1");

$class1Id = $class1Result ? $class1Result->fetch_assoc()['id'] : null;
$class2Id = $class2Result ? $class2Result->fetch_assoc()['id'] : null;

foreach ($subjects as $subject) {
    if ($class1Id) {
        $subjectSQL = "INSERT IGNORE INTO subjects (subject_name, subject_code, class_id, created_at)
                       VALUES ('{$subject['name']}', '{$subject['code']}_1', $class1Id, NOW())";
        executeSQL($conn, $subjectSQL, "ক্লাস ১ - {$subject['name']} বিষয় তৈরি");
    }

    if ($class2Id) {
        $subjectSQL = "INSERT IGNORE INTO subjects (subject_name, subject_code, class_id, created_at)
                       VALUES ('{$subject['name']}', '{$subject['code']}_2', $class2Id, NOW())";
        executeSQL($conn, $subjectSQL, "ক্লাস ২ - {$subject['name']} বিষয় তৈরি");
    }
}

// 6. Get class IDs after creation
$class1Result = $conn->query("SELECT id FROM classes WHERE class_name = '১' LIMIT 1");
$class2Result = $conn->query("SELECT id FROM classes WHERE class_name = '২' LIMIT 1");

$class1Id = $class1Result ? $class1Result->fetch_assoc()['id'] : null;
$class2Id = $class2Result ? $class2Result->fetch_assoc()['id'] : null;

// 7. Check students table structure and create sample students
$checkStudentsTable = "SHOW TABLES LIKE 'students'";
$studentsTableResult = $conn->query($checkStudentsTable);

if ($studentsTableResult && $studentsTableResult->num_rows > 0) {
    // Check what columns exist in students table
    $studentsColumns = $conn->query("SHOW COLUMNS FROM students");
    $columnNames = [];
    while ($col = $studentsColumns->fetch_assoc()) {
        $columnNames[] = $col['Field'];
    }

    // Create sample students based on available columns
    $sampleStudents = [
        ['name' => 'রহিম আহমেদ', 'roll' => '001', 'class' => $class1Id],
        ['name' => 'করিম হাসান', 'roll' => '002', 'class' => $class1Id],
        ['name' => 'ফাতেমা খাতুন', 'roll' => '003', 'class' => $class1Id],
        ['name' => 'আয়েশা বেগম', 'roll' => '001', 'class' => $class2Id],
        ['name' => 'মোহাম্মদ আলী', 'roll' => '002', 'class' => $class2Id],
        ['name' => 'সালমা আক্তার', 'roll' => '003', 'class' => $class2Id]
    ];

    foreach ($sampleStudents as $student) {
        if ($student['class']) {
            // Use appropriate column names based on what exists
            if (in_array('student_name', $columnNames)) {
                $nameColumn = 'student_name';
            } elseif (in_array('first_name', $columnNames)) {
                $nameColumn = 'first_name';
            } else {
                continue; // Skip if no name column found
            }

            if (in_array('roll_number', $columnNames)) {
                $rollColumn = 'roll_number';
            } elseif (in_array('student_id', $columnNames)) {
                $rollColumn = 'student_id';
            } else {
                continue; // Skip if no roll column found
            }

            $studentSQL = "INSERT IGNORE INTO students ($nameColumn, $rollColumn, class_id, created_at)
                           VALUES ('{$student['name']}', '{$student['roll']}', {$student['class']}, NOW())";
            executeSQL($conn, $studentSQL, "নমুনা ছাত্র/ছাত্রী তৈরি - {$student['name']}");
        }
    }
}

// 7. Create a sample exam for demonstration
$sampleExamSQL = "INSERT IGNORE INTO exams_primary_lower 
                  (exam_name, exam_type, exam_date, total_marks, passing_marks, instructions, created_by) 
                  VALUES 
                  ('প্রথম সাপ্তাহিক পরীক্ষা', 'সাপ্তাহিক পরীক্ষা', DATE_ADD(CURDATE(), INTERVAL 7 DAY), 100, 33, 'নমুনা পরীক্ষা - পরীক্ষার হলে নিরবতা বজায় রাখুন', 1)";

executeSQL($conn, $sampleExamSQL, "নমুনা পরীক্ষা তৈরি");

// 8. Create class_groups table first if not exists
$classGroupsTableSQL = "CREATE TABLE IF NOT EXISTS class_groups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    group_name VARCHAR(255) NOT NULL,
    group_code VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    min_class INT NOT NULL,
    max_class INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

executeSQL($conn, $classGroupsTableSQL, "ক্লাস গ্রুপ টেবিল তৈরি");

// 9. Insert PRIMARY_LOWER group
$updateGroupSQL = "INSERT INTO class_groups (group_name, group_code, description, min_class, max_class, is_active)
                   VALUES ('প্রাথমিক নিম্ন (ক্লাস ১-২)', 'PRIMARY_LOWER', 'ক্লাস ১ ও ২ এর জন্য পরীক্ষা ব্যবস্থাপনা', 1, 2, 1)
                   ON DUPLICATE KEY UPDATE is_active = 1";

executeSQL($conn, $updateGroupSQL, "ক্লাস গ্রুপ ডেটা যোগ");

$conn->close();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ক্লাস ১-২ ডেটাবেস সেটআপ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: #f8f9fa;
        }
        .setup-container {
            max-width: 800px;
            margin: 50px auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .success-item {
            color: #28a745;
            margin-bottom: 10px;
        }
        .error-item {
            color: #dc3545;
            margin-bottom: 10px;
        }
        .header-section {
            background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="header-section">
            <h1><i class="fas fa-database me-2"></i>ক্লাস ১-২ ডেটাবেস সেটআপ</h1>
            <p class="mb-0">পূর্ণাঙ্গ কার্যকারিতার জন্য প্রয়োজনীয় টেবিল ও ডেটা তৈরি</p>
        </div>

        <?php if (!empty($messages)): ?>
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>সফল অপারেশনসমূহ:</h5>
                <?php foreach ($messages as $message): ?>
                    <div class="success-item"><?php echo $message; ?></div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle me-2"></i>সমস্যাসমূহ:</h5>
                <?php foreach ($errors as $error): ?>
                    <div class="error-item"><?php echo $error; ?></div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle me-2"></i>যা তৈরি করা হয়েছে:</h5>
            <ul class="mb-0">
                <li><strong>exams_primary_lower</strong> - ক্লাস ১-২ এর পরীক্ষার টেবিল</li>
                <li><strong>results_primary_lower</strong> - ক্লাস ১-২ এর ফলাফলের টেবিল</li>
                <li><strong>Classes</strong> - ক্লাস ১ ও ২</li>
                <li><strong>Subjects</strong> - বাংলা, ইংরেজি, গণিত, পরিবেশ পরিচিতি, ধর্ম ও নৈতিক শিক্ষা</li>
                <li><strong>Sample Students</strong> - পরীক্ষার জন্য নমুনা ছাত্র/ছাত্রী</li>
                <li><strong>Sample Exam</strong> - একটি নমুনা পরীক্ষা</li>
            </ul>
        </div>

        <div class="text-center">
            <a href="class_exam_primary_lower_1_2.php" class="btn btn-primary btn-lg me-3">
                <i class="fas fa-arrow-right me-2"></i>ক্লাস ১-২ পরীক্ষা পেজে যান
            </a>
            <a href="class_based_exam_dashboard.php" class="btn btn-secondary btn-lg">
                <i class="fas fa-home me-2"></i>ড্যাশবোর্ডে ফিরে যান
            </a>
        </div>

        <div class="mt-4 text-center">
            <small class="text-muted">
                <i class="fas fa-clock me-1"></i>সেটআপ সম্পন্ন: <?php echo date('d/m/Y H:i:s'); ?>
            </small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
