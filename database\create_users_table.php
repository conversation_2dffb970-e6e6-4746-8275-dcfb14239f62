<?php
require_once '../includes/dbh.inc.php';

// Create users table if not exists
$sql = "CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    user_type ENUM('admin', 'teacher', 'student', 'staff') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($sql) === TRUE) {
    echo "Users table created successfully\n";
    
    // Check if admin user exists
    $checkAdmin = "SELECT id FROM users WHERE username = 'admin'";
    $result = $conn->query($checkAdmin);
    
    if ($result->num_rows == 0) {
        // Create admin user
        $adminUser = 'admin';
        $adminPass = password_hash('admin123', PASSWORD_DEFAULT);
        $userType = 'admin';
        
        $sql = "INSERT INTO users (username, password, user_type) VALUES (?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sss", $adminUser, $adminPass, $userType);
        
        if ($stmt->execute()) {
            echo "Admin user created successfully\n";
        } else {
            echo "Error creating admin user: " . $stmt->error . "\n";
        }
        $stmt->close();
    } else {
        echo "Admin user already exists\n";
    }
} else {
    echo "Error creating table: " . $conn->error . "\n";
}

$conn->close();
?>