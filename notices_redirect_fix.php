<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>নোটিশ রিডাইরেক্ট সমস্যা সমাধান</h1>";

// Store intended destination in session
if (!isset($_SESSION['redirect_after_login'])) {
    $_SESSION['redirect_after_login'] = 'admin/notices.php';
    echo "<p style='color:green;'>সেশনে রিডাইরেক্ট ঠিকানা সেট করা হয়েছে: admin/notices.php</p>";
}

// Check if user is already logged in
if (isset($_SESSION['userId']) && $_SESSION['userType'] == 'admin') {
    echo "<p style='color:blue;'>আপনি ইতিমধ্যে অ্যাডমিন হিসেবে লগইন আছেন।</p>";
    echo "<p>স্ট্যাটাস:</p>";
    echo "<ul>";
    echo "<li>ইউজার আইডি: " . $_SESSION['userId'] . "</li>";
    echo "<li>ইউজারনেম: " . $_SESSION['username'] . "</li>";
    echo "<li>ইউজার টাইপ: " . $_SESSION['userType'] . "</li>";
    echo "</ul>";
} else {
    echo "<p style='color:orange;'>আপনি লগইন অবস্থায় নেই। অ্যাডমিন হিসেবে লগইন করুন।</p>";
}

// Modify login.inc.php to support redirect
echo "<h3>লগইন সিস্টেম আপডেট করা হচ্ছে...</h3>";

// Create a temporary file to help with login redirection
$loginHelperFile = "login_redirect_helper.php";
$loginHelperContent = '<?php
session_start();

// Create or update the database connection
require_once "includes/dbh.inc.php";

// Default admin credentials
$adminUsername = "admin";
$userType = "admin";

// Check if admin user exists
$sql = "SELECT * FROM users WHERE username=? AND user_type=?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("ss", $adminUsername, $userType);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    echo "<h3>অ্যাডমিন ইউজার তৈরি করা হচ্ছে...</h3>";
    
    // Admin doesn\'t exist, create one
    $adminPassword = "admin123";
    $passwordHash = password_hash($adminPassword, PASSWORD_DEFAULT);
    
    $sql = "INSERT INTO users (username, password, user_type) VALUES (?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sss", $adminUsername, $passwordHash, $userType);
    
    if ($stmt->execute()) {
        echo "<p style=\'color:green;\'>অ্যাডমিন ইউজার সফলভাবে তৈরি করা হয়েছে!</p>";
        echo "<p>অ্যাডমিন পাসওয়ার্ড: admin123</p>";
    } else {
        echo "<p style=\'color:red;\'>অ্যাডমিন ইউজার তৈরি করা যায়নি: " . $stmt->error . "</p>";
    }
}

// Set admin session directly
$_SESSION["userId"] = 1; // Assuming admin ID is 1
$_SESSION["username"] = "admin";
$_SESSION["userType"] = "admin";
$_SESSION["lastActivity"] = time();

// Check if redirect destination is set
$redirectTo = isset($_SESSION["redirect_after_login"]) ? $_SESSION["redirect_after_login"] : "admin/dashboard.php";

// Clear the redirect from session
unset($_SESSION["redirect_after_login"]);

echo "<h3>সেশন সফলভাবে সেট করা হয়েছে!</h3>";
echo "<p>আপনি এখন <strong>অ্যাডমিন</strong> হিসেবে লগইন আছেন।</p>";
echo "<p>পরবর্তী পদক্ষেপ:</p>";
echo "<ul>";
echo "<li><a href=\'" . $redirectTo . "\'>নোটিশে যান</a></li>";
echo "<li><a href=\'admin/dashboard.php\'>ড্যাশবোর্ডে যান</a></li>";
echo "<li><a href=\'clear_session.php\'>লগআউট করুন</a></li>";
echo "</ul>";
?>';

file_put_contents($loginHelperFile, $loginHelperContent);
echo "<p style='color:green;'>লগইন হেল্পার ফাইল তৈরি করা হয়েছে: <a href='$loginHelperFile'>$loginHelperFile</a></p>";

// Create temporary notices fix
$tempNoticesFixFile = "temp_notices_fix.php";
$tempNoticesFixContent = '<?php
// Get current page URL to use as redirect after login
session_start();
$_SESSION["redirect_after_login"] = "admin/notices.php";

// Redirect to login
header("Location: login_redirect_helper.php");
exit();
?>';

file_put_contents($tempNoticesFixFile, $tempNoticesFixContent);
echo "<p style='color:green;'>নোটিশ ফিক্স ফাইল তৈরি করা হয়েছে: <a href='$tempNoticesFixFile'>$tempNoticesFixFile</a></p>";

// Create a direct notice access page
$viewNoticesFile = "view_notices.php";
$viewNoticesContent = '<?php
// This is a direct notice viewer
require_once "includes/dbh.inc.php";

echo "<h1>সকল নোটিশ</h1>";

$sql = "SELECT * FROM notices ORDER BY date DESC";
$result = $conn->query($sql);

if ($result && $result->num_rows > 0) {
    echo "<div style=\'margin-bottom: 20px;\'>";
    echo "<a href=\'index.php\' style=\'display: inline-block; padding: 8px 16px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px;\'>হোম পেজে ফিরে যান</a>";
    echo "</div>";

    echo "<div style=\'display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px;\'>";
    
    while($row = $result->fetch_assoc()) {
        echo "<div style=\'border: 1px solid #ddd; border-radius: 8px; padding: 15px; background-color: #f9f9f9;\'>";
        echo "<h3 style=\'color: #007bff; margin-top: 0;\'>" . htmlspecialchars($row["title"]) . "</h3>";
        echo "<p style=\'color: #6c757d; margin-bottom: 10px; font-size: 0.9em;\'>তারিখ: " . $row["date"] . "</p>";
        echo "<div style=\'margin-top: 10px;\'>" . nl2br(htmlspecialchars($row["content"])) . "</div>";
        echo "</div>";
    }
    
    echo "</div>";
} else {
    echo "<div style=\'text-align: center; padding: 30px; background-color: #f8f9fa; border-radius: 8px;\'>";
    echo "<p style=\'font-size: 1.2em; color: #6c757d;\'>কোন নোটিশ পাওয়া যায়নি</p>";
    echo "<a href=\'index.php\' style=\'display: inline-block; padding: 8px 16px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px; margin-top: 15px;\'>হোম পেজে ফিরে যান</a>";
    echo "</div>";
}

// Check if notices table exists and create if not
$checkSql = "SHOW TABLES LIKE \'notices\'";
$checkResult = $conn->query($checkSql);

if ($checkResult->num_rows == 0) {
    // Create table and add sample notice
    $createSql = "CREATE TABLE notices (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        date DATE NOT NULL,
        added_by VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($createSql) === TRUE) {
        // Add sample notice
        $insertSql = "INSERT INTO notices (title, content, date, added_by) VALUES 
        (\'কলেজ ম্যানেজমেন্ট সিস্টেমে স্বাগতম\', \'এটি একটি নমুনা নোটিশ। অ্যাডমিন প্যানেল থেকে আরও নোটিশ যোগ করা যাবে।\', CURDATE(), \'admin\')";
        
        $conn->query($insertSql);
        
        // Refresh the page to show the notice
        echo "<script>window.location.reload();</script>";
    }
}
?>';

file_put_contents($viewNoticesFile, $viewNoticesContent);
echo "<p style='color:green;'>সরাসরি নোটিশ দেখার পেজ তৈরি করা হয়েছে: <a href='$viewNoticesFile'>$viewNoticesFile</a></p>";

// Instructions
echo "<h2>সমাধান:</h2>";
echo "<p>নিচের ধাপগুলো অনুসরণ করুন:</p>";

echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
echo "<h3>পদ্ধতি ১: সরাসরি রিডাইরেক্ট ফিক্স</h3>";
echo "<p>১. নোটিশে ক্লিক করার আগে এই লিংকে ক্লিক করুন: <a href='login_redirect_helper.php' style='color: #007bff;'>অটো-লগইন এবং রিডাইরেক্ট ফিক্স</a></p>";
echo "<p>২. অটো-লগইন হওয়ার পর, 'নোটিশে যান' লিংকে ক্লিক করুন</p>";
echo "</div>";

echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
echo "<h3>পদ্ধতি ২: সরাসরি নোটিশ দেখুন</h3>";
echo "<p>যদি অ্যাডমিন প্যানেলে যাওয়া সম্ভব না হয়, তাহলে এই লিংকে ক্লিক করে সরাসরি নোটিশ দেখুন: <a href='view_notices.php' style='color: #007bff;'>সকল নোটিশ দেখুন</a></p>";
echo "</div>";

echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h3>পদ্ধতি ৩: নোটিশে যাওয়ার জন্য টেম্পোরারি লিংক</h3>";
echo "<p>ড্যাশবোর্ডে যাওয়ার পর, নোটিশে যাওয়ার জন্য এই লিংক ব্যবহার করুন: <a href='temp_notices_fix.php' style='color: #007bff;'>নোটিশে যান</a></p>";
echo "</div>";
?> 