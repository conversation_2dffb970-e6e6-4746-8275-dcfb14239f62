<?php
require_once '../includes/dbh.inc.php';

// Check if updated_by column exists
$result = $conn->query("SHOW COLUMNS FROM exams LIKE 'updated_by'");
$columnExists = $result->num_rows > 0;

if (!$columnExists) {
    // Add updated_by column
    $alterQuery = "ALTER TABLE exams ADD COLUMN updated_by VARCHAR(255) NULL DEFAULT NULL AFTER updated_at";
    
    if ($conn->query($alterQuery)) {
        echo "updated_by column added successfully to exams table!";
    } else {
        echo "Error adding updated_by column: " . $conn->error;
    }
} else {
    echo "updated_by column already exists in exams table.";
}
?>
