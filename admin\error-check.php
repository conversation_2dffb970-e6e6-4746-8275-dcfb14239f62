<?php
// Simple error check page
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Error Check Page</h1>";
echo "<p>PHP Version: " . PHP_VERSION . "</p>";
echo "<p>Current Time: " . date('Y-m-d H:i:s') . "</p>";

// Check if fee_management.php has syntax errors
echo "<h2>Checking fee_management.php for syntax errors...</h2>";

$file = 'fee_management.php';
$output = [];
$return_var = 0;

// Use php -l to check syntax
exec("php -l $file 2>&1", $output, $return_var);

if ($return_var === 0) {
    echo "<p style='color: green;'>✅ No syntax errors found in fee_management.php</p>";
} else {
    echo "<p style='color: red;'>❌ Syntax errors found:</p>";
    echo "<pre style='background: #f8f8f8; padding: 10px; border: 1px solid #ddd;'>";
    foreach ($output as $line) {
        echo htmlspecialchars($line) . "\n";
    }
    echo "</pre>";
}

// Check if we can include the file
echo "<h2>Testing file inclusion...</h2>";

try {
    // Capture any output
    ob_start();
    
    // Try to include just the PHP part without executing
    $content = file_get_contents('fee_management.php');
    
    if ($content === false) {
        throw new Exception("Could not read fee_management.php");
    }
    
    echo "<p style='color: green;'>✅ File can be read successfully</p>";
    echo "<p>File size: " . strlen($content) . " bytes</p>";
    
    // Check for common issues
    $issues = [];
    
    if (strpos($content, '<?php') === false) {
        $issues[] = "No PHP opening tag found";
    }
    
    if (substr_count($content, '<?php') > 1) {
        $issues[] = "Multiple PHP opening tags found";
    }
    
    if (strpos($content, 'session_start()') === false) {
        $issues[] = "No session_start() found";
    }
    
    // Check for unmatched brackets
    $openBraces = substr_count($content, '{');
    $closeBraces = substr_count($content, '}');
    if ($openBraces !== $closeBraces) {
        $issues[] = "Unmatched braces: $openBraces open, $closeBraces close";
    }
    
    $openParens = substr_count($content, '(');
    $closeParens = substr_count($content, ')');
    if ($openParens !== $closeParens) {
        $issues[] = "Unmatched parentheses: $openParens open, $closeParens close";
    }
    
    if (empty($issues)) {
        echo "<p style='color: green;'>✅ No obvious structural issues found</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Potential issues found:</p>";
        echo "<ul>";
        foreach ($issues as $issue) {
            echo "<li>$issue</li>";
        }
        echo "</ul>";
    }
    
    ob_end_clean();
    
} catch (Exception $e) {
    ob_end_clean();
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

// Check database connection
echo "<h2>Testing database connection...</h2>";

try {
    require_once '../includes/dbh.inc.php';
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    
    // Test a simple query
    $result = $conn->query("SELECT 1 as test");
    if ($result) {
        echo "<p style='color: green;'>✅ Database query successful</p>";
    } else {
        echo "<p style='color: red;'>❌ Database query failed: " . $conn->error . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
}

// Check session
echo "<h2>Testing session...</h2>";

session_start();
echo "<p>Session ID: " . session_id() . "</p>";

if (isset($_SESSION['userId'])) {
    echo "<p style='color: green;'>✅ User logged in: ID " . $_SESSION['userId'] . ", Type: " . $_SESSION['userType'] . "</p>";
} else {
    echo "<p style='color: orange;'>⚠️ User not logged in</p>";
    echo "<p><a href='../login.php'>Login here</a></p>";
}

// Show recent error log
echo "<h2>Recent PHP Error Log</h2>";

$errorLog = ini_get('error_log');
if ($errorLog && file_exists($errorLog)) {
    $errors = file($errorLog);
    $recentErrors = array_slice($errors, -10); // Last 10 errors
    
    if (!empty($recentErrors)) {
        echo "<pre style='background: #f8f8f8; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow-y: auto;'>";
        foreach ($recentErrors as $error) {
            echo htmlspecialchars($error);
        }
        echo "</pre>";
    } else {
        echo "<p>No recent errors found</p>";
    }
} else {
    echo "<p>Error log not found or not accessible</p>";
}

echo "<h2>Quick Actions</h2>";
echo "<p>";
echo "<a href='fee_management.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Try Fee Management Again</a>";
echo "<a href='super-simple-test.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Super Simple Test</a>";
echo "<a href='../login.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Login</a>";
echo "</p>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    line-height: 1.6;
}
h1, h2 {
    color: #333;
}
pre {
    white-space: pre-wrap;
    word-wrap: break-word;
}
</style>
