<?php
// Database Connection
require_once 'includes/dbh.inc.php';

echo "<h1>Creating Notices Table</h1>";

// Create notices table
$noticesTableQuery = "CREATE TABLE IF NOT EXISTS notices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    date DATE NOT NULL,
    target_audience ENUM('all', 'students', 'teachers', 'staff') NOT NULL DEFAULT 'all',
    expiry_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

if ($conn->query($noticesTableQuery)) {
    echo "<p style='color:green'>Notices table created successfully!</p>";
    
    // Insert a sample notice
    $checkNotices = $conn->query("SELECT * FROM notices LIMIT 1");
    if ($checkNotices && $checkNotices->num_rows == 0) {
        $currentDate = date('Y-m-d');
        $expiryDate = date('Y-m-d', strtotime('+30 days'));
        
        $insertNotice = $conn->query("INSERT INTO notices (title, content, date, target_audience, expiry_date) 
                                    VALUES ('স্বাগতম আমাদের কলেজ ম্যানেজমেন্ট সিস্টেমে', 'এই সিস্টেমটি ব্যবহার করে আপনি সহজেই আপনার কলেজের সকল কার্যক্রম পরিচালনা করতে পারবেন।', 
                                    '$currentDate', 'all', '$expiryDate')");
        
        if ($insertNotice) {
            echo "<p style='color:green'>Sample notice created successfully!</p>";
        } else {
            echo "<p style='color:red'>Error creating sample notice: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Notices already exist in the table.</p>";
    }
} else {
    echo "<p style='color:red'>Error creating notices table: " . $conn->error . "</p>";
}

echo "<p><a href='admin/dashboard.php' style='background-color: #4CAF50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>Go to Admin Dashboard</a></p>";

// Close connection
$conn->close();
?>
