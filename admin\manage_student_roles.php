<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if student_roles table exists, if not create it
$tableCheck = $conn->query("SHOW TABLES LIKE 'student_roles'");
if ($tableCheck->num_rows == 0) {
    $createTable = "CREATE TABLE student_roles (
        id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
        role_name VARCHAR(100) NOT NULL,
        role_name_bn VARCHAR(100) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $conn->query($createTable);
    
    // Add default roles
    $defaultRoles = [
        ['Class Captain', 'ক্লাস ক্যাপ্টেন', 'Class captain role'],
        ['Class Representative', 'ক্লাস প্রতিনিধি', 'Class representative role'],
        ['Class Monitor', 'ক্লাস মনিটর', 'Class monitor role'],
        ['Student Council Member', 'ছাত্র সংসদ সদস্য', 'Student council member role'],
        ['Regular Student', 'সাধারণ শিক্ষার্থী', 'Regular student role']
    ];
    
    $insertStmt = $conn->prepare("INSERT INTO student_roles (role_name, role_name_bn, description) VALUES (?, ?, ?)");
    foreach ($defaultRoles as $role) {
        $insertStmt->bind_param("sss", $role[0], $role[1], $role[2]);
        $insertStmt->execute();
    }
}

// Handle form submissions
$successMessage = '';
$errorMessage = '';

// Add new role
if (isset($_POST['add_role'])) {
    $roleName = $_POST['role_name'];
    $roleNameBn = $_POST['role_name_bn'];
    $description = $_POST['description'];
    
    // Check if role already exists
    $checkStmt = $conn->prepare("SELECT id FROM student_roles WHERE role_name = ? OR role_name_bn = ?");
    $checkStmt->bind_param("ss", $roleName, $roleNameBn);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();
    
    if ($checkResult->num_rows > 0) {
        $errorMessage = "এই রোল ইতিমধ্যে বিদ্যমান আছে।";
    } else {
        $insertStmt = $conn->prepare("INSERT INTO student_roles (role_name, role_name_bn, description) VALUES (?, ?, ?)");
        $insertStmt->bind_param("sss", $roleName, $roleNameBn, $description);
        
        if ($insertStmt->execute()) {
            $successMessage = "নতুন রোল সফলভাবে যোগ করা হয়েছে।";
        } else {
            $errorMessage = "রোল যোগ করতে সমস্যা হয়েছে: " . $conn->error;
        }
    }
}

// Edit role
if (isset($_POST['edit_role'])) {
    $roleId = $_POST['role_id'];
    $roleName = $_POST['role_name'];
    $roleNameBn = $_POST['role_name_bn'];
    $description = $_POST['description'];
    
    // Check if role already exists (excluding the current role)
    $checkStmt = $conn->prepare("SELECT id FROM student_roles WHERE (role_name = ? OR role_name_bn = ?) AND id != ?");
    $checkStmt->bind_param("ssi", $roleName, $roleNameBn, $roleId);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();
    
    if ($checkResult->num_rows > 0) {
        $errorMessage = "এই রোল ইতিমধ্যে বিদ্যমান আছে।";
    } else {
        $updateStmt = $conn->prepare("UPDATE student_roles SET role_name = ?, role_name_bn = ?, description = ? WHERE id = ?");
        $updateStmt->bind_param("sssi", $roleName, $roleNameBn, $description, $roleId);
        
        if ($updateStmt->execute()) {
            $successMessage = "রোল সফলভাবে আপডেট করা হয়েছে।";
        } else {
            $errorMessage = "রোল আপডেট করতে সমস্যা হয়েছে: " . $conn->error;
        }
    }
}

// Delete role
if (isset($_POST['delete_role'])) {
    $roleId = $_POST['role_id'];
    
    // Check if role is being used by any student
    $checkStmt = $conn->prepare("SELECT id FROM students WHERE role = (SELECT role_name FROM student_roles WHERE id = ?)");
    $checkStmt->bind_param("i", $roleId);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();
    
    if ($checkResult->num_rows > 0) {
        $errorMessage = "এই রোল " . $checkResult->num_rows . "টি শিক্ষার্থীর সাথে সংযুক্ত আছে। আগে শিক্ষার্থীদের রোল পরিবর্তন করুন।";
    } else {
        $deleteStmt = $conn->prepare("DELETE FROM student_roles WHERE id = ?");
        $deleteStmt->bind_param("i", $roleId);
        
        if ($deleteStmt->execute()) {
            $successMessage = "রোল সফলভাবে মুছে ফেলা হয়েছে।";
        } else {
            $errorMessage = "রোল মুছতে সমস্যা হয়েছে: " . $conn->error;
        }
    }
}

// Get all roles
$roles = $conn->query("SELECT * FROM student_roles ORDER BY role_name");
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>শিক্ষার্থী রোল ব্যবস্থাপনা - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .role-card {
            transition: all 0.3s ease;
        }
        .role-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="manage_student_roles.php">
                            <i class="fas fa-user-tag me-2"></i> শিক্ষার্থী রোল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>শিক্ষার্থী রোল ব্যবস্থাপনা</h2>
                        <p class="text-muted">শিক্ষার্থীদের জন্য বিভিন্ন রোল যোগ, সম্পাদনা এবং মুছুন</p>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRoleModal">
                            <i class="fas fa-plus-circle me-2"></i>নতুন রোল যোগ করুন
                        </button>
                    </div>
                </div>

                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Roles List -->
                <div class="row">
                    <?php if ($roles && $roles->num_rows > 0): ?>
                        <?php while ($role = $roles->fetch_assoc()): ?>
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card role-card h-100">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0"><?php echo htmlspecialchars($role['role_name_bn']); ?></h5>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#editRoleModal" 
                                                       data-id="<?php echo $role['id']; ?>"
                                                       data-name="<?php echo htmlspecialchars($role['role_name']); ?>"
                                                       data-name-bn="<?php echo htmlspecialchars($role['role_name_bn']); ?>"
                                                       data-description="<?php echo htmlspecialchars($role['description']); ?>">
                                                        <i class="fas fa-edit me-2"></i>সম্পাদনা করুন
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item text-danger" href="#" data-bs-toggle="modal" data-bs-target="#deleteRoleModal" 
                                                       data-id="<?php echo $role['id']; ?>"
                                                       data-name="<?php echo htmlspecialchars($role['role_name_bn']); ?>">
                                                        <i class="fas fa-trash-alt me-2"></i>মুছুন
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <p class="card-text text-muted small mb-2">ইংরেজি নাম: <?php echo htmlspecialchars($role['role_name']); ?></p>
                                        <p class="card-text"><?php echo htmlspecialchars($role['description'] ?: 'কোন বিবরণ নেই'); ?></p>
                                    </div>
                                    <div class="card-footer text-muted small">
                                        <i class="fas fa-clock me-1"></i> যোগ করা হয়েছে: <?php echo date('d/m/Y', strtotime($role['created_at'])); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>কোন রোল পাওয়া যায়নি। নতুন রোল যোগ করতে উপরের বাটনে ক্লিক করুন।
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Role Modal -->
    <div class="modal fade" id="addRoleModal" tabindex="-1" aria-labelledby="addRoleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addRoleModalLabel">নতুন রোল যোগ করুন</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="role_name" class="form-label">রোলের নাম (ইংরেজি)*</label>
                            <input type="text" class="form-control" id="role_name" name="role_name" required>
                            <small class="form-text text-muted">ইংরেজিতে রোলের নাম লিখুন (যেমন: Class Captain)</small>
                        </div>
                        <div class="mb-3">
                            <label for="role_name_bn" class="form-label">রোলের নাম (বাংলা)*</label>
                            <input type="text" class="form-control" id="role_name_bn" name="role_name_bn" required>
                            <small class="form-text text-muted">বাংলায় রোলের নাম লিখুন (যেমন: ক্লাস ক্যাপ্টেন)</small>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">বিবরণ</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                            <small class="form-text text-muted">রোল সম্পর্কে একটি সংক্ষিপ্ত বিবরণ লিখুন (ঐচ্ছিক)</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল করুন</button>
                        <button type="submit" name="add_role" class="btn btn-primary">যোগ করুন</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Role Modal -->
    <div class="modal fade" id="editRoleModal" tabindex="-1" aria-labelledby="editRoleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editRoleModalLabel">রোল সম্পাদনা করুন</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" id="edit_role_id" name="role_id">
                        <div class="mb-3">
                            <label for="edit_role_name" class="form-label">রোলের নাম (ইংরেজি)*</label>
                            <input type="text" class="form-control" id="edit_role_name" name="role_name" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_role_name_bn" class="form-label">রোলের নাম (বাংলা)*</label>
                            <input type="text" class="form-control" id="edit_role_name_bn" name="role_name_bn" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_description" class="form-label">বিবরণ</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল করুন</button>
                        <button type="submit" name="edit_role" class="btn btn-primary">আপডেট করুন</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Role Modal -->
    <div class="modal fade" id="deleteRoleModal" tabindex="-1" aria-labelledby="deleteRoleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteRoleModalLabel">রোল মুছুন</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>আপনি কি নিশ্চিত যে আপনি "<span id="delete_role_name"></span>" রোল মুছতে চান?</p>
                    <p class="text-danger">সতর্কতা: এই রোল মুছে ফেলা হলে, এটি ব্যবহারকারী শিক্ষার্থীদের রোল পরিবর্তন করতে হবে।</p>
                </div>
                <form method="POST" action="">
                    <input type="hidden" id="delete_role_id" name="role_id">
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল করুন</button>
                        <button type="submit" name="delete_role" class="btn btn-danger">মুছুন</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // Edit role modal
        $('#editRoleModal').on('show.bs.modal', function (event) {
            var button = $(event.relatedTarget);
            var id = button.data('id');
            var name = button.data('name');
            var nameBn = button.data('name-bn');
            var description = button.data('description');
            
            var modal = $(this);
            modal.find('#edit_role_id').val(id);
            modal.find('#edit_role_name').val(name);
            modal.find('#edit_role_name_bn').val(nameBn);
            modal.find('#edit_description').val(description);
        });
        
        // Delete role modal
        $('#deleteRoleModal').on('show.bs.modal', function (event) {
            var button = $(event.relatedTarget);
            var id = button.data('id');
            var name = button.data('name');
            
            var modal = $(this);
            modal.find('#delete_role_id').val(id);
            modal.find('#delete_role_name').text(name);
        });
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
</body>
</html>
