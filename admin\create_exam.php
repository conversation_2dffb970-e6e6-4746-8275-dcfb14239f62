<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if necessary tables exist
$tables = ['classes', 'subjects', 'exam_types', 'exams'];
$missingTables = [];

foreach ($tables as $table) {
    $tableCheck = $conn->query("SHOW TABLES LIKE '$table'");
    if ($tableCheck->num_rows == 0) {
        $missingTables[] = $table;
    }
}

if (!empty($missingTables)) {
    header("Location: result_management.php");
    exit();
}

// Handle form submissions
$successMessage = '';
$errorMessage = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add new exam
    if (isset($_POST['add_exam'])) {
        $examName = trim($_POST['exam_name']);
        $examType = trim($_POST['exam_type']);
        $sessionId = !empty($_POST['session_id']) ? intval($_POST['session_id']) : null;
        $classId = !empty($_POST['class_id']) ? intval($_POST['class_id']) : null;

        // Handle subject IDs
        $subjectIds = isset($_POST['subject_ids']) ? $_POST['subject_ids'] : [];
        $subjectId = null;
        $allSubjects = false;

        // Check if "all" is selected for subjects
        if (in_array('all', $subjectIds)) {
            $allSubjects = true;
            $subjectId = null; // Set to null to indicate all subjects
        }
        // If only one subject is selected, use that ID
        elseif (count($subjectIds) == 1) {
            $subjectId = intval($subjectIds[0]);
        }
        // If multiple subjects are selected, we'll handle them differently
        // For now, we'll store the first one in subjectId and handle the rest later
        elseif (count($subjectIds) > 1) {
            $subjectId = intval($subjectIds[0]);
        }

        // Handle department IDs
        $departmentIds = isset($_POST['department_ids']) ? $_POST['department_ids'] : [];
        $departmentId = null;
        $allDepartments = false;

        // Check if "all" is selected for departments
        if (in_array('all', $departmentIds)) {
            $allDepartments = true;
            $departmentId = null; // Set to null to indicate all departments
        }
        // If only one department is selected, use that ID
        elseif (count($departmentIds) == 1) {
            $departmentId = intval($departmentIds[0]);
        }
        // If multiple departments are selected, we'll handle them differently
        // For now, we'll store the first one in departmentId and handle the rest later
        elseif (count($departmentIds) > 1) {
            $departmentId = intval($departmentIds[0]);
        }
        $examDate = $_POST['exam_date'];
        $startTime = !empty($_POST['start_time']) ? $_POST['start_time'] : null;
        $endTime = !empty($_POST['end_time']) ? $_POST['end_time'] : null;
        $totalMarks = intval($_POST['total_marks']);
        $passingMarks = intval($_POST['passing_marks']);
        $createdBy = $_SESSION['userId'];

        if (empty($examName) || empty($examDate) || empty($totalMarks) || empty($sessionId)) {
            $errorMessage = "পরীক্ষার নাম, সেশন, তারিখ এবং মোট নম্বর আবশ্যক!";
        } else {
            // Insert new exam
            $insertQuery = "INSERT INTO exams (exam_name, exam_type, session_id, subject_id, class_id, department_id, exam_date, start_time, end_time, total_marks, passing_marks, created_by)
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param("ssiiiisssiis", $examName, $examType, $sessionId, $subjectId, $classId, $departmentId, $examDate, $startTime, $endTime, $totalMarks, $passingMarks, $createdBy);

            if ($stmt->execute()) {
                $examId = $conn->insert_id;

                // Handle multiple departments if needed
                if (count($departmentIds) > 1 && !$allDepartments) {
                    // Skip the first department as it's already saved in the exams table
                    for ($i = 1; $i < count($departmentIds); $i++) {
                        $deptId = intval($departmentIds[$i]);

                        // Insert into exam_departments table
                        $insertDeptQuery = "INSERT INTO exam_departments (exam_id, department_id) VALUES (?, ?)";
                        $deptStmt = $conn->prepare($insertDeptQuery);
                        $deptStmt->bind_param("ii", $examId, $deptId);
                        $deptStmt->execute();
                    }
                }

                // Handle multiple subjects if needed
                if (count($subjectIds) > 1 && !$allSubjects) {
                    // Skip the first subject as it's already saved in the exams table
                    for ($i = 1; $i < count($subjectIds); $i++) {
                        $subjId = intval($subjectIds[$i]);

                        // Insert into exam_subject_relations table
                        $insertSubjQuery = "INSERT INTO exam_subject_relations (exam_id, subject_id, total_marks, passing_marks) VALUES (?, ?, ?, ?)";
                        $subjStmt = $conn->prepare($insertSubjQuery);
                        $subjStmt->bind_param("iiii", $examId, $subjId, $totalMarks, $passingMarks);
                        $subjStmt->execute();
                    }
                }

                // If "All Subjects" is selected and we have a list of all subjects
                if ($allSubjects) {
                    // Get all active subjects
                    $allSubjectsQuery = "SELECT id FROM subjects WHERE is_active = 1";
                    $allSubjectsResult = $conn->query($allSubjectsQuery);

                    if ($allSubjectsResult && $allSubjectsResult->num_rows > 0) {
                        $insertSubjQuery = "INSERT INTO exam_subject_relations (exam_id, subject_id, total_marks, passing_marks) VALUES (?, ?, ?, ?)";
                        $subjStmt = $conn->prepare($insertSubjQuery);

                        while ($subj = $allSubjectsResult->fetch_assoc()) {
                            $subjId = $subj['id'];
                            $subjStmt->bind_param("iiii", $examId, $subjId, $totalMarks, $passingMarks);
                            $subjStmt->execute();
                        }
                    }
                }

                $successMessage = "পরীক্ষা সফলভাবে যোগ করা হয়েছে!";
            } else {
                $errorMessage = "পরীক্ষা যোগ করতে সমস্যা হয়েছে: " . $conn->error;
            }
        }
    }
}

// Get all classes
$classesQuery = "SELECT id, class_name FROM classes ORDER BY class_name";
$classes = $conn->query($classesQuery);

// Get all subjects
$subjectsQuery = "SELECT id, subject_name FROM subjects ORDER BY subject_name";
$subjects = $conn->query($subjectsQuery);

// Check if exam_type_subjects table exists
$tableCheck = $conn->query("SHOW TABLES LIKE 'exam_type_subjects'");
$examTypeSubjectsExists = $tableCheck->num_rows > 0;

// Get all departments
$departmentsQuery = "SELECT id, department_name FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Get all sessions
$sessionsQuery = "SELECT id, session_name FROM sessions ORDER BY start_date DESC";
$sessions = $conn->query($sessionsQuery);

// Get all exam types
$examTypesQuery = "SELECT id, type_name FROM exam_types WHERE is_active = 1 ORDER BY type_name";
$examTypes = $conn->query($examTypesQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>নতুন পরীক্ষা যোগ করুন - স্কুল ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css">

    <style>
        :root {
            --primary-color: #4361ee;
            --primary-hover: #3a56d4;
            --secondary-color: #6c757d;
            --success-color: #2ecc71;
            --success-hover: #27ae60;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --info-color: #3498db;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --border-radius: 10px;
            --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        body {
            background-color: #f8f9fa;
            font-family: 'Hind Siliguri', sans-serif;
            color: #333;
        }

        .main-content {
            padding: 25px;
        }

        /* Card Styles */
        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 30px;
            transition: var(--transition);
            overflow: hidden;
        }

        .card:hover {
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            transform: translateY(-5px);
        }

        .card-header {
            border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
            padding: 18px 25px;
            border-bottom: none;
        }

        .card-header.bg-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover)) !important;
        }

        .card-body {
            padding: 30px;
        }

        /* Form Controls */
        .form-control, .form-select {
            border-radius: 8px;
            padding: 12px 15px;
            border: 1px solid #e0e0e0;
            transition: var(--transition);
            font-size: 1rem;
            height: auto;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
        }

        /* Buttons */
        .btn {
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 500;
            transition: var(--transition);
            border: none;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .btn-success {
            background-color: var(--success-color);
        }

        .btn-success:hover {
            background-color: var(--success-hover);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-outline-secondary {
            color: var(--secondary-color);
            border: 1px solid var(--secondary-color);
        }

        .btn-outline-secondary:hover {
            background-color: var(--secondary-color);
            color: white;
        }

        /* Form Elements */
        .form-label {
            font-weight: 600;
            margin-bottom: 10px;
            color: #444;
            font-size: 0.95rem;
        }

        .form-text {
            color: #6c757d;
            font-size: 0.85rem;
            margin-top: 6px;
        }

        /* Select2 Customization */
        .select2-container {
            width: 100% !important;
        }

        .select2-container .select2-selection--multiple {
            min-height: 50px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            padding: 5px;
        }

        .select2-container--bootstrap-5 .select2-selection {
            padding: 8px 12px;
        }

        .select2-selection__choice {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover)) !important;
            color: white !important;
            border: none !important;
            padding: 5px 10px !important;
            border-radius: 5px !important;
            margin: 3px !important;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .select2-selection__choice__remove {
            color: white !important;
            margin-right: 5px !important;
            font-weight: bold;
        }

        .select2-dropdown {
            border-radius: 8px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            overflow: hidden;
        }

        .select2-results__option {
            padding: 10px 15px;
            transition: var(--transition);
        }

        .select2-results__option--highlighted {
            background-color: var(--primary-color) !important;
        }

        /* Alerts */
        .alert {
            border-radius: 10px;
            padding: 18px 25px;
            margin-bottom: 25px;
            border: none;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .alert-success {
            background-color: rgba(46, 204, 113, 0.15);
            color: #27ae60;
        }

        .alert-danger {
            background-color: rgba(231, 76, 60, 0.15);
            color: #c0392b;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .main-content {
                padding: 15px;
            }

            .card-body {
                padding: 20px;
            }

            .form-control, .form-select, .btn {
                padding: 10px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <div class="d-flex align-items-center">
                            <div>
                                <h2 class="mb-1">নতুন পরীক্ষা যোগ করুন</h2>
                                <p class="text-muted mb-0">পরীক্ষার বিবরণ দিয়ে নতুন পরীক্ষা তৈরি করুন</p>
                            </div>
                            <div class="ms-auto">
                                <a href="manage_exams.php" class="btn btn-outline-primary">
                                    <i class="fas fa-list me-2"></i>পরীক্ষা তালিকা দেখুন
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if ($successMessage): ?>
                    <div class="alert alert-success d-flex align-items-center" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <div>
                            <?php echo $successMessage; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($errorMessage): ?>
                    <div class="alert alert-danger d-flex align-items-center" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <div>
                            <?php echo $errorMessage; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-edit me-2"></i>
                            <h5 class="mb-0">পরীক্ষার বিবরণ</h5>
                        </div>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="exam_name" class="form-label">পরীক্ষার নাম*</label>
                                    <input type="text" class="form-control" id="exam_name" name="exam_name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="exam_type" class="form-label">পরীক্ষার ধরন</label>
                                    <select class="form-select" id="exam_type" name="exam_type">
                                        <option value="">পরীক্ষার ধরন নির্বাচন করুন</option>
                                        <?php if ($examTypes && $examTypes->num_rows > 0): ?>
                                            <?php while ($type = $examTypes->fetch_assoc()): ?>
                                                <option value="<?php echo htmlspecialchars($type['type_name']); ?>">
                                                    <?php echo htmlspecialchars($type['type_name']); ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="session_id" class="form-label">সেশন*</label>
                                    <select class="form-select" id="session_id" name="session_id" required>
                                        <option value="">সেশন নির্বাচন করুন</option>
                                        <?php if ($sessions && $sessions->num_rows > 0): ?>
                                            <?php while ($session = $sessions->fetch_assoc()): ?>
                                                <option value="<?php echo $session['id']; ?>">
                                                    <?php echo htmlspecialchars($session['session_name']); ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="class_id" class="form-label">শ্রেণী</label>
                                    <select class="form-select" id="class_id" name="class_id">
                                        <option value="">শ্রেণী নির্বাচন করুন</option>
                                        <?php if ($classes && $classes->num_rows > 0): ?>
                                            <?php while ($class = $classes->fetch_assoc()): ?>
                                                <option value="<?php echo $class['id']; ?>">
                                                    <?php echo htmlspecialchars($class['class_name']); ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="department_ids" class="form-label">বিভাগ</label>
                                    <select class="form-select" id="department_ids" name="department_ids[]" multiple>
                                        <option value="all">সকল বিভাগ</option>
                                        <?php if ($departments && $departments->num_rows > 0): ?>
                                            <?php while ($department = $departments->fetch_assoc()): ?>
                                                <option value="<?php echo $department['id']; ?>">
                                                    <?php echo htmlspecialchars($department['department_name']); ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                    <div class="form-text">একাধিক বিভাগ নির্বাচন করতে Ctrl/Cmd কী চেপে ধরুন</div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="subject_ids" class="form-label">বিষয়</label>
                                    <select class="form-select" id="subject_ids" name="subject_ids[]" multiple>
                                        <option value="all">সকল বিষয়</option>
                                        <?php if ($subjects && $subjects->num_rows > 0): ?>
                                            <?php while ($subject = $subjects->fetch_assoc()): ?>
                                                <option value="<?php echo $subject['id']; ?>">
                                                    <?php echo htmlspecialchars($subject['subject_name']); ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                    <div class="form-text">একাধিক বিষয় নির্বাচন করতে Ctrl/Cmd কী চেপে ধরুন</div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="exam_date" class="form-label">পরীক্ষার তারিখ*</label>
                                    <input type="date" class="form-control" id="exam_date" name="exam_date" required>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="start_time" class="form-label">শুরুর সময়</label>
                                    <input type="time" class="form-control" id="start_time" name="start_time">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="end_time" class="form-label">শেষের সময়</label>
                                    <input type="time" class="form-control" id="end_time" name="end_time">
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="total_marks" class="form-label">মোট নম্বর*</label>
                                    <input type="number" class="form-control" id="total_marks" name="total_marks" min="1" value="100" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="passing_marks" class="form-label">পাস নম্বর</label>
                                    <input type="number" class="form-control" id="passing_marks" name="passing_marks" min="0" value="33">
                                </div>
                            </div>

                            <div class="mt-4 d-flex justify-content-end">
                                <button type="reset" class="btn btn-outline-secondary me-2">
                                    <i class="fas fa-undo me-2"></i>রিসেট করুন
                                </button>
                                <button type="submit" name="add_exam" class="btn btn-success">
                                    <i class="fas fa-plus-circle me-2"></i>পরীক্ষা যোগ করুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Select2 for department and subject selection
            $(document).ready(function() {
                // Initialize department select
                $('#department_ids').select2({
                    theme: 'bootstrap-5',
                    placeholder: 'বিভাগ নির্বাচন করুন',
                    allowClear: true
                });

                // Initialize subject select
                $('#subject_ids').select2({
                    theme: 'bootstrap-5',
                    placeholder: 'বিষয় নির্বাচন করুন',
                    allowClear: true
                });

                // Handle "All Departments" option
                $('#department_ids').on('select2:select', function(e) {
                    if (e.params.data.id === 'all') {
                        // If "All Departments" is selected, deselect all other options
                        $('#department_ids').val(['all']).trigger('change');
                    } else {
                        // If any other option is selected, deselect "All Departments"
                        var values = $('#department_ids').val();
                        if (values && values.includes('all')) {
                            values = values.filter(value => value !== 'all');
                            $('#department_ids').val(values).trigger('change');
                        }
                    }
                });

                // Handle "All Subjects" option
                $('#subject_ids').on('select2:select', function(e) {
                    if (e.params.data.id === 'all') {
                        // If "All Subjects" is selected, deselect all other options
                        $('#subject_ids').val(['all']).trigger('change');
                    } else {
                        // If any other option is selected, deselect "All Subjects"
                        var values = $('#subject_ids').val();
                        if (values && values.includes('all')) {
                            values = values.filter(value => value !== 'all');
                            $('#subject_ids').val(values).trigger('change');
                        }
                    }
                });
            });

            // Auto-calculate passing marks as 33% of total marks
            const totalMarksInput = document.getElementById('total_marks');
            const passingMarksInput = document.getElementById('passing_marks');

            totalMarksInput.addEventListener('input', function() {
                const totalMarks = parseInt(this.value) || 0;
                passingMarksInput.value = Math.ceil(totalMarks * 0.33);
            });

            <?php if ($examTypeSubjectsExists): ?>
            // Load subjects based on exam type
            const examTypeSelect = document.getElementById('exam_type');
            const classSelect = document.getElementById('class_id');
            const departmentSelect = $('#department_ids');
            const subjectSelect = $('#subject_ids');

            const sessionSelect = document.getElementById('session_id');

            function loadSubjects() {
                const examType = examTypeSelect.value;
                const classId = classSelect.value;
                const departmentIds = departmentSelect.val() || [];
                const sessionId = sessionSelect.value;

                // Check if "All Departments" is selected
                const allDepartmentsSelected = departmentIds.includes('all');

                // Create department_ids parameter
                let departmentParam = '';
                if (allDepartmentsSelected) {
                    departmentParam = 'all';
                } else if (departmentIds.length > 0) {
                    departmentParam = departmentIds.join(',');
                }

                if (!examType) {
                    return;
                }

                // Create AJAX request
                const xhr = new XMLHttpRequest();
                xhr.open('GET', `get_exam_subjects.php?exam_type=${encodeURIComponent(examType)}&class_id=${classId}&department_ids=${departmentParam}&session_id=${sessionId}`, true);

                xhr.onload = function() {
                    if (this.status === 200) {
                        try {
                            const subjects = JSON.parse(this.responseText);

                            // Clear current options and add the "All Subjects" option
                            subjectSelect.empty().append('<option value="all">সকল বিষয়</option>');

                            // Add new options
                            subjects.forEach(subject => {
                                subjectSelect.append(new Option(subject.subject_name, subject.id));
                            });

                            // Trigger change to refresh Select2
                            subjectSelect.trigger('change');
                        } catch (e) {
                            console.error('Error parsing JSON:', e);
                        }
                    }
                };

                xhr.send();
            }

            examTypeSelect.addEventListener('change', loadSubjects);
            classSelect.addEventListener('change', loadSubjects);
            departmentSelect.on('change', loadSubjects);
            sessionSelect.addEventListener('change', loadSubjects);
            <?php endif; ?>
        });
    </script>
</body>
</html>
