/**
 * Fallback solution for scrolling notice
 * This script creates a scrolling notice if the original one is not visible
 */
document.addEventListener('DOMContentLoaded', function() {
    // Check if the original scrolling notice is visible
    setTimeout(function() {
        var originalNotice = document.getElementById('scrolling-text');
        var noticeContainer = document.querySelector('.scrolling-notice-container');
        
        // Check if notice is visible and has proper height
        var isNoticeVisible = originalNotice && 
                             (originalNotice.offsetHeight > 10) && 
                             (window.getComputedStyle(originalNotice).visibility !== 'hidden') &&
                             (window.getComputedStyle(originalNotice).display !== 'none');
        
        // If notice is not visible, create a fallback
        if (!isNoticeVisible) {
            console.log('Creating fallback notice');
            
            // Create fallback container
            var fallbackContainer = document.createElement('div');
            fallbackContainer.className = 'fallback-notice-container';
            fallbackContainer.style.cssText = 'background-color: #f8f9fa; padding: 15px 0; border-bottom: 2px solid #dee2e6; position: relative; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); width: 100%; z-index: 1001; min-height: 50px; margin-bottom: 15px;';
            
            // Create inner container
            var innerContainer = document.createElement('div');
            innerContainer.className = 'container';
            fallbackContainer.appendChild(innerContainer);
            
            // Create notice wrapper
            var noticeWrapper = document.createElement('div');
            noticeWrapper.className = 'fallback-notice';
            noticeWrapper.style.cssText = 'width: 100%; overflow: hidden; position: relative; min-height: 30px;';
            innerContainer.appendChild(noticeWrapper);
            
            // Create scrolling content
            var noticeContent = document.createElement('div');
            noticeContent.className = 'fallback-notice-content';
            noticeContent.id = 'fallback-scrolling-text';
            noticeContent.style.cssText = 'white-space: nowrap; display: inline-block; animation: scrollText 30s linear infinite; padding: 5px 0; color: #333; font-weight: 600; font-size: 16px; line-height: 1.5;';
            noticeWrapper.appendChild(noticeContent);
            
            // Add notice content
            noticeContent.innerHTML = '<i class="fas fa-bullhorn" style="color: #007bff; margin-right: 5px;"></i> <strong style="color: #212529; font-weight: 700;">সর্বশেষ নোটিশ:</strong> বর্তমানে কোন নোটিশ নেই। &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <i class="fas fa-calendar-alt" style="color: #007bff; margin-right: 5px;"></i> <strong style="color: #212529; font-weight: 700;">আজকের তারিখ:</strong> ' + new Date().toLocaleDateString() + ' &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <i class="fas fa-graduation-cap" style="color: #007bff; margin-right: 5px;"></i> <strong style="color: #212529; font-weight: 700;">ভর্তি চলছে:</strong> নতুন শিক্ষার্থীদের জন্য ভর্তি চলছে। আজই যোগাযোগ করুন।';
            
            // Add animation style
            var animationStyle = document.createElement('style');
            animationStyle.textContent = `
                @keyframes scrollText {
                    0% { transform: translateX(100%); }
                    100% { transform: translateX(-100%); }
                }
                @-webkit-keyframes scrollText {
                    0% { -webkit-transform: translateX(100%); }
                    100% { -webkit-transform: translateX(-100%); }
                }
                @-moz-keyframes scrollText {
                    0% { -moz-transform: translateX(100%); }
                    100% { -moz-transform: translateX(-100%); }
                }
                @-o-keyframes scrollText {
                    0% { -o-transform: translateX(100%); }
                    100% { -o-transform: translateX(-100%); }
                }
            `;
            document.head.appendChild(animationStyle);
            
            // Insert fallback notice after navigation or at the beginning of main content
            var navigation = document.querySelector('nav');
            if (navigation && navigation.nextElementSibling) {
                navigation.parentNode.insertBefore(fallbackContainer, navigation.nextElementSibling);
            } else {
                var mainContent = document.querySelector('.container');
                if (mainContent) {
                    mainContent.parentNode.insertBefore(fallbackContainer, mainContent);
                } else {
                    document.body.insertBefore(fallbackContainer, document.body.firstChild);
                }
            }
            
            // Hide original notice if it exists but is not visible
            if (noticeContainer) {
                noticeContainer.style.display = 'none';
            }
        }
    }, 1000); // Check after 1 second to allow original notice to load
});
