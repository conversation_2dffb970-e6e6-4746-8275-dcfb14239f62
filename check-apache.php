<?php
// Check Apache configuration for buffering issues
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>";
echo "<html><head><title>Apache Configuration Check</title></head><body>";
echo "<h1>🔍 Apache Configuration Check</h1>";

echo "<h2>📋 Apache Modules:</h2>";
echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
echo "<tr><th>Module</th><th>Status</th><th>Impact on Buffering</th></tr>";

$modules_to_check = [
    'mod_deflate' => 'Can cause compression buffering',
    'mod_gzip' => 'Can cause compression buffering', 
    'mod_headers' => 'Needed for cache control',
    'mod_rewrite' => 'URL rewriting',
    'mod_php' => 'PHP processing',
    'mod_ssl' => 'HTTPS support'
];

if (function_exists('apache_get_modules')) {
    $loaded_modules = apache_get_modules();
    
    foreach ($modules_to_check as $module => $description) {
        $is_loaded = in_array($module, $loaded_modules);
        $status_color = ($module === 'mod_deflate' || $module === 'mod_gzip') ? 
                       ($is_loaded ? 'red' : 'green') : 
                       ($is_loaded ? 'green' : 'orange');
        
        echo "<tr>";
        echo "<td><strong>$module</strong></td>";
        echo "<td style='color: $status_color;'>" . ($is_loaded ? '✅ Loaded' : '❌ Not Loaded') . "</td>";
        echo "<td>$description</td>";
        echo "</tr>";
    }
} else {
    echo "<tr><td colspan='3'>❌ apache_get_modules() not available</td></tr>";
}

echo "</table>";

echo "<h2>🌐 Server Environment:</h2>";
echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
echo "<tr><th>Variable</th><th>Value</th></tr>";

$server_vars = [
    'SERVER_SOFTWARE',
    'SERVER_NAME', 
    'SERVER_PORT',
    'DOCUMENT_ROOT',
    'HTTP_HOST',
    'REQUEST_METHOD',
    'HTTP_USER_AGENT'
];

foreach ($server_vars as $var) {
    $value = $_SERVER[$var] ?? 'Not Set';
    echo "<tr><td><strong>$var</strong></td><td>" . htmlspecialchars($value) . "</td></tr>";
}

echo "</table>";

echo "<h2>🔧 Response Headers Test:</h2>";
echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 5px;'>";
echo "<p>Testing if custom headers are working...</p>";

// Set test headers
header('X-Buffer-Test: Working');
header('X-Timestamp: ' . time());

echo "<script>";
echo "fetch(window.location.href)";
echo ".then(response => {";
echo "    console.log('Response headers:', response.headers);";
echo "    const bufferTest = response.headers.get('X-Buffer-Test');";
echo "    const timestamp = response.headers.get('X-Timestamp');";
echo "    ";
echo "    const resultDiv = document.createElement('div');";
echo "    resultDiv.innerHTML = `";
echo "        <strong>Header Test Results:</strong><br>";
echo "        X-Buffer-Test: \${bufferTest || 'Not found'}<br>";
echo "        X-Timestamp: \${timestamp || 'Not found'}";
echo "    `;";
echo "    resultDiv.style.background = bufferTest ? '#ccffcc' : '#ffcccc';";
echo "    resultDiv.style.padding = '10px';";
echo "    resultDiv.style.margin = '10px 0';";
echo "    document.body.appendChild(resultDiv);";
echo "})";
echo ".catch(error => {";
echo "    console.error('Header test failed:', error);";
echo "});";
echo "</script>";

echo "</div>";

echo "<h2>💡 Recommendations:</h2>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; border-left: 4px solid #2196f3;'>";

if (function_exists('apache_get_modules')) {
    $loaded_modules = apache_get_modules();
    
    if (in_array('mod_deflate', $loaded_modules)) {
        echo "<p style='color: red;'>⚠️ <strong>mod_deflate is loaded</strong> - This is likely causing the buffering!</p>";
        echo "<p><strong>Solution:</strong> Disable mod_deflate in Apache configuration or add specific rules to exclude your site.</p>";
    }
    
    if (in_array('mod_gzip', $loaded_modules)) {
        echo "<p style='color: red;'>⚠️ <strong>mod_gzip is loaded</strong> - This can also cause buffering!</p>";
        echo "<p><strong>Solution:</strong> Disable mod_gzip or configure it to exclude your site.</p>";
    }
    
    if (!in_array('mod_deflate', $loaded_modules) && !in_array('mod_gzip', $loaded_modules)) {
        echo "<p style='color: green;'>✅ No compression modules detected - buffering issue is elsewhere.</p>";
    }
}

echo "<h3>🔧 How to Fix Apache Compression:</h3>";
echo "<ol>";
echo "<li><strong>Open XAMPP Control Panel</strong></li>";
echo "<li><strong>Click 'Config' next to Apache</strong></li>";
echo "<li><strong>Select 'httpd.conf'</strong></li>";
echo "<li><strong>Find and comment out these lines (add # at the beginning):</strong>";
echo "<pre style='background: #000; color: #00ff00; padding: 10px;'>";
echo "#LoadModule deflate_module modules/mod_deflate.so\n";
echo "#LoadModule gzip_module modules/mod_gzip.so";
echo "</pre>";
echo "</li>";
echo "<li><strong>Save the file and restart Apache</strong></li>";
echo "</ol>";

echo "</div>";

echo "<h2>🧪 Alternative Test:</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<p>Try accessing your site through different methods:</p>";
echo "<ul>";
echo "<li><a href='http://127.0.0.1/zfaw/ultra-minimal.html' target='_blank'>127.0.0.1 (IP address)</a></li>";
echo "<li><a href='http://localhost:80/zfaw/ultra-minimal.html' target='_blank'>localhost:80 (explicit port)</a></li>";
echo "<li><a href='file:///" . str_replace('\\', '/', __DIR__) . "/ultra-minimal.html' target='_blank'>Direct file access</a></li>";
echo "</ul>";
echo "</div>";

echo "</body></html>";
?>
