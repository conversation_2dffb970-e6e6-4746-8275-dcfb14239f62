<?php
session_start();
require_once 'includes/dbh.inc.php';

// No login required to view student details

// Get student ID from URL
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: students.php");
    exit();
}

$studentId = $_GET['id'];

// Get student data with user information and department name
$studentQuery = "SELECT s.*, u.username, u.created_at as account_created,
                d.department_name as department
                FROM students s
                LEFT JOIN users u ON s.user_id = u.id
                LEFT JOIN departments d ON s.department_id = d.id
                WHERE s.student_id = ?";
$stmt = $conn->prepare($studentQuery);
$stmt->bind_param("s", $studentId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header("Location: students.php");
    exit();
}

$student = $result->fetch_assoc();

// Get courses enrolled by student (if they exist in the database)
$coursesQuery = "SELECT s.subject_name, s.subject_code, ss.category
                FROM subjects s
                JOIN student_subjects ss ON s.id = ss.subject_id
                WHERE ss.student_id = ?
                ORDER BY s.subject_name";
$stmt = $conn->prepare($coursesQuery);
$stmt->bind_param("i", $student['id']);
$stmt->execute();
$courses = $stmt->get_result();

// Get fees information (if they exist in the database)
$feesQuery = "SELECT fee_type, amount, due_date, payment_date, payment_status
              FROM fees
              WHERE student_id = ?
              ORDER BY due_date DESC";
$stmt = $conn->prepare($feesQuery);
$stmt->bind_param("i", $student['id']);
$stmt->execute();
$fees = $stmt->get_result();

// Courses table has been removed, so set these to null
$attendance = null;
$results = null;
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>শিক্ষার্থী প্রোফাইল - কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .student-img {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 50%;
            border: 4px solid #f8f9fa;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .profile-header {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .student-details {
            margin-top: 20px;
        }
        .detail-item {
            margin-bottom: 10px;
        }
        .tab-pane {
            padding: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Include sidebar navigation -->
            <?php include 'includes/sidebar.php'; ?>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>শিক্ষার্থী প্রোফাইল</h2>
                        <p class="text-muted">শিক্ষার্থীর বিস্তারিত তথ্য দেখুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="students.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>শিক্ষার্থী তালিকায় ফিরে যান
                        </a>
                    </div>
                </div>

                <!-- Profile Header -->
                <div class="profile-header">
                    <div class="row align-items-center">
                        <div class="col-md-2 text-center mb-3 mb-md-0">
                            <?php if (!empty($student['profile_photo'])): ?>
                                <img src="<?php echo $student['profile_photo']; ?>" alt="শিক্ষার্থীর ছবি" class="student-img">
                            <?php else: ?>
                                <div class="student-img d-flex align-items-center justify-content-center bg-secondary text-white">
                                    <i class="fas fa-user fa-4x"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-10">
                            <h3><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h3>
                            <p class="text-muted mb-2">শিক্ষার্থী আইডি: <?php echo htmlspecialchars($student['student_id']); ?></p>
                            <p class="mb-2"><i class="fas fa-graduation-cap me-2"></i> <?php echo htmlspecialchars($student['department'] ?? 'N/A'); ?> - ব্যাচ <?php echo htmlspecialchars($student['batch'] ?? 'N/A'); ?></p>
                            <p class="mb-2"><i class="fas fa-envelope me-2"></i> <?php echo htmlspecialchars($student['email']); ?></p>
                            <p class="mb-0"><i class="fas fa-phone me-2"></i> <?php echo htmlspecialchars($student['phone']); ?></p>
                        </div>
                    </div>
                </div>

                <!-- Student Information Tabs -->
                <div class="card">
                    <div class="card-body">
                        <ul class="nav nav-tabs" id="studentTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="personal-tab" data-bs-toggle="tab" data-bs-target="#personal" type="button" role="tab" aria-controls="personal" aria-selected="true">ব্যক্তিগত তথ্য</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="academic-tab" data-bs-toggle="tab" data-bs-target="#academic" type="button" role="tab" aria-controls="academic" aria-selected="false">একাডেমিক তথ্য</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="courses-tab" data-bs-toggle="tab" data-bs-target="#courses" type="button" role="tab" aria-controls="courses" aria-selected="false">বিষয়সমূহ</button>
                            </li>
                        </ul>

                        <div class="tab-content" id="studentTabsContent">
                            <!-- Personal Information Tab -->
                            <div class="tab-pane fade show active" id="personal" role="tabpanel" aria-labelledby="personal-tab">
                                <div class="row student-details">
                                    <div class="col-md-6">
                                        <h5 class="mb-3">ব্যক্তিগত তথ্য</h5>
                                        <div class="detail-item">
                                            <strong>শিক্ষার্থী আইডি:</strong> <?php echo htmlspecialchars($student['student_id']); ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>পূর্ণ নাম:</strong> <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>ইমেইল:</strong> <?php echo htmlspecialchars($student['email']); ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>ফোন:</strong> <?php echo htmlspecialchars($student['phone']); ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>জন্ম তারিখ:</strong> <?php echo !empty($student['dob']) ? date('d M Y', strtotime($student['dob'])) : 'N/A'; ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>লিঙ্গ:</strong> <?php echo htmlspecialchars($student['gender']); ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>ঠিকানা:</strong> <?php echo htmlspecialchars($student['address']); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Academic Information Tab -->
                            <div class="tab-pane fade" id="academic" role="tabpanel" aria-labelledby="academic-tab">
                                <div class="row student-details">
                                    <div class="col-md-6">
                                        <h5 class="mb-3">একাডেমিক তথ্য</h5>
                                        <div class="detail-item">
                                            <strong>বিভাগ:</strong> <?php echo htmlspecialchars($student['department'] ?? 'N/A'); ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>ব্যাচ:</strong> <?php echo htmlspecialchars($student['batch'] ?? 'N/A'); ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>ভর্তির তারিখ:</strong> <?php echo !empty($student['admission_date']) ? date('d M Y', strtotime($student['admission_date'])) : 'N/A'; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Subjects Tab -->
                            <div class="tab-pane fade" id="courses" role="tabpanel" aria-labelledby="courses-tab">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>বিষয় কোড</th>
                                                <th>বিষয়ের নাম</th>
                                                <th>ক্যাটাগরি</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($courses && $courses->num_rows > 0): ?>
                                                <?php while ($course = $courses->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($course['subject_code']); ?></td>
                                                        <td><?php echo htmlspecialchars($course['subject_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($course['category']); ?></td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="3" class="text-center">কোন বিষয় পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
