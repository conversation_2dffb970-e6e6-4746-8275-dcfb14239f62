<?php
// Database Connection
require_once 'includes/dbh.inc.php';

echo "<h1>Database Tables Fix Utility</h1>";

// Function to check if a table exists
function tableExists($conn, $tableName) {
    $result = $conn->query("SHOW TABLES LIKE '$tableName'");
    return $result && $result->num_rows > 0;
}

// Function to check if a column exists in a table
function columnExists($conn, $tableName, $columnName) {
    $result = $conn->query("SHOW COLUMNS FROM `$tableName` LIKE '$columnName'");
    return $result && $result->num_rows > 0;
}

// Fix students table
echo "<h2>Checking Students Table</h2>";
if (tableExists($conn, 'students')) {
    echo "<p>Students table exists.</p>";

    // Check for gender column
    if (!columnExists($conn, 'students', 'gender')) {
        $alterTable = $conn->query("ALTER TABLE students ADD COLUMN gender ENUM('male', 'female', 'other') NOT NULL AFTER phone");
        if ($alterTable) {
            echo "<p style='color:green'>Added 'gender' column to students table.</p>";
        } else {
            echo "<p style='color:red'>Error adding 'gender' column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Gender column already exists.</p>";
    }

    // Check for admission_date column
    if (!columnExists($conn, 'students', 'admission_date')) {
        $alterTable = $conn->query("ALTER TABLE students ADD COLUMN admission_date DATE NULL");
        if ($alterTable) {
            echo "<p style='color:green'>Added 'admission_date' column to students table.</p>";
        } else {
            echo "<p style='color:red'>Error adding 'admission_date' column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Admission_date column already exists.</p>";
    }

    // Check for profile_photo column
    if (!columnExists($conn, 'students', 'profile_photo')) {
        $alterTable = $conn->query("ALTER TABLE students ADD COLUMN profile_photo VARCHAR(255) NULL");
        if ($alterTable) {
            echo "<p style='color:green'>Added 'profile_photo' column to students table.</p>";
        } else {
            echo "<p style='color:red'>Error adding 'profile_photo' column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Profile_photo column already exists.</p>";
    }

    // Check for department_id column
    if (!columnExists($conn, 'students', 'department_id')) {
        // Add department_id at the end of the table to avoid dependency on other columns
        $alterTable = $conn->query("ALTER TABLE students ADD COLUMN department_id INT(11) NULL");
        if ($alterTable) {
            echo "<p style='color:green'>Added 'department_id' column to students table.</p>";
        } else {
            echo "<p style='color:red'>Error adding 'department_id' column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Department_id column already exists.</p>";
    }

    // Check for class_id column
    if (!columnExists($conn, 'students', 'class_id')) {
        $alterTable = $conn->query("ALTER TABLE students ADD COLUMN class_id INT(11) NULL");
        if ($alterTable) {
            echo "<p style='color:green'>Added 'class_id' column to students table.</p>";
        } else {
            echo "<p style='color:red'>Error adding 'class_id' column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Class_id column already exists.</p>";
    }

    // Check for batch column
    if (!columnExists($conn, 'students', 'batch')) {
        $alterTable = $conn->query("ALTER TABLE students ADD COLUMN batch VARCHAR(20) NULL");
        if ($alterTable) {
            echo "<p style='color:green'>Added 'batch' column to students table.</p>";
        } else {
            echo "<p style='color:red'>Error adding 'batch' column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Batch column already exists.</p>";
    }

    // Check for group_name column
    if (!columnExists($conn, 'students', 'group_name')) {
        $alterTable = $conn->query("ALTER TABLE students ADD COLUMN group_name VARCHAR(50) NULL");
        if ($alterTable) {
            echo "<p style='color:green'>Added 'group_name' column to students table.</p>";
        } else {
            echo "<p style='color:red'>Error adding 'group_name' column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Group_name column already exists.</p>";
    }
} else {
    echo "<p style='color:red'>Students table does not exist. Please run the database setup script first.</p>";
}

// Fix teachers table
echo "<h2>Checking Teachers Table</h2>";
if (tableExists($conn, 'teachers')) {
    echo "<p>Teachers table exists.</p>";

    // Check for gender column
    if (!columnExists($conn, 'teachers', 'gender')) {
        $alterTable = $conn->query("ALTER TABLE teachers ADD COLUMN gender ENUM('male', 'female', 'other') NOT NULL AFTER phone");
        if ($alterTable) {
            echo "<p style='color:green'>Added 'gender' column to teachers table.</p>";
        } else {
            echo "<p style='color:red'>Error adding 'gender' column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Gender column already exists in teachers table.</p>";
    }

    // Check for joining_date column
    if (!columnExists($conn, 'teachers', 'joining_date')) {
        $alterTable = $conn->query("ALTER TABLE teachers ADD COLUMN joining_date DATE NULL");
        if ($alterTable) {
            echo "<p style='color:green'>Added 'joining_date' column to teachers table.</p>";
        } else {
            echo "<p style='color:red'>Error adding 'joining_date' column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Joining_date column already exists in teachers table.</p>";
    }

    // Check for profile_photo column
    if (!columnExists($conn, 'teachers', 'profile_photo')) {
        $alterTable = $conn->query("ALTER TABLE teachers ADD COLUMN profile_photo VARCHAR(255) NULL");
        if ($alterTable) {
            echo "<p style='color:green'>Added 'profile_photo' column to teachers table.</p>";
        } else {
            echo "<p style='color:red'>Error adding 'profile_photo' column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Profile_photo column already exists in teachers table.</p>";
    }

    // Check for department_id column
    if (!columnExists($conn, 'teachers', 'department_id')) {
        // Add department_id at the end of the table to avoid dependency on other columns
        $alterTable = $conn->query("ALTER TABLE teachers ADD COLUMN department_id INT(11) NULL");
        if ($alterTable) {
            echo "<p style='color:green'>Added 'department_id' column to teachers table.</p>";
        } else {
            echo "<p style='color:red'>Error adding 'department_id' column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Department_id column already exists in teachers table.</p>";
    }
} else {
    echo "<p style='color:red'>Teachers table does not exist. Please run the database setup script first.</p>";
}

// Fix staff table
echo "<h2>Checking Staff Table</h2>";
if (tableExists($conn, 'staff')) {
    echo "<p>Staff table exists.</p>";

    // Check for gender column
    if (!columnExists($conn, 'staff', 'gender')) {
        $alterTable = $conn->query("ALTER TABLE staff ADD COLUMN gender ENUM('male', 'female', 'other') NOT NULL AFTER phone");
        if ($alterTable) {
            echo "<p style='color:green'>Added 'gender' column to staff table.</p>";
        } else {
            echo "<p style='color:red'>Error adding 'gender' column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Gender column already exists in staff table.</p>";
    }

    // Check for joining_date column
    if (!columnExists($conn, 'staff', 'joining_date')) {
        $alterTable = $conn->query("ALTER TABLE staff ADD COLUMN joining_date DATE NULL");
        if ($alterTable) {
            echo "<p style='color:green'>Added 'joining_date' column to staff table.</p>";
        } else {
            echo "<p style='color:red'>Error adding 'joining_date' column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Joining_date column already exists in staff table.</p>";
    }

    // Check for profile_photo column
    if (!columnExists($conn, 'staff', 'profile_photo')) {
        $alterTable = $conn->query("ALTER TABLE staff ADD COLUMN profile_photo VARCHAR(255) NULL");
        if ($alterTable) {
            echo "<p style='color:green'>Added 'profile_photo' column to staff table.</p>";
        } else {
            echo "<p style='color:red'>Error adding 'profile_photo' column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Profile_photo column already exists in staff table.</p>";
    }

    // Check for department_id column
    if (!columnExists($conn, 'staff', 'department_id')) {
        // Add department_id at the end of the table to avoid dependency on other columns
        $alterTable = $conn->query("ALTER TABLE staff ADD COLUMN department_id INT(11) NULL");
        if ($alterTable) {
            echo "<p style='color:green'>Added 'department_id' column to staff table.</p>";
        } else {
            echo "<p style='color:red'>Error adding 'department_id' column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Department_id column already exists in staff table.</p>";
    }
} else {
    echo "<p style='color:red'>Staff table does not exist. <a href='../create_staff_table.php' style='color:blue;'>Click here to create the staff table</a>.</p>";
}

// Check for department_code column in departments table
echo "<h2>Checking Departments Table</h2>";
if (tableExists($conn, 'departments')) {
    echo "<p>Departments table exists.</p>";

    if (!columnExists($conn, 'departments', 'department_code')) {
        $alterTable = $conn->query("ALTER TABLE departments ADD COLUMN department_code VARCHAR(20) NOT NULL UNIQUE AFTER department_name");
        if ($alterTable) {
            echo "<p style='color:green'>Added 'department_code' column to departments table.</p>";

            // Update existing departments with a code
            $updateDepts = $conn->query("UPDATE departments SET department_code = CONCAT('DEPT-', id) WHERE department_code IS NULL OR department_code = ''");
            if ($updateDepts) {
                echo "<p style='color:green'>Updated department codes for existing departments.</p>";
            } else {
                echo "<p style='color:red'>Error updating department codes: " . $conn->error . "</p>";
            }
        } else {
            echo "<p style='color:red'>Error adding 'department_code' column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Department_code column already exists.</p>";
    }
} else {
    echo "<p style='color:red'>Departments table does not exist. Please run the database setup script first.</p>";
}

// Check for notices table
echo "<h2>Checking Notices Table</h2>";
if (tableExists($conn, 'notices')) {
    echo "<p>Notices table exists.</p>";

    // Check for date column
    if (!columnExists($conn, 'notices', 'date')) {
        $alterTable = $conn->query("ALTER TABLE notices ADD COLUMN date DATE NOT NULL AFTER content");
        if ($alterTable) {
            echo "<p style='color:green'>Added 'date' column to notices table.</p>";
        } else {
            echo "<p style='color:red'>Error adding 'date' column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Date column already exists in notices table.</p>";
    }

    // Check for expiry_date column
    if (!columnExists($conn, 'notices', 'expiry_date')) {
        $alterTable = $conn->query("ALTER TABLE notices ADD COLUMN expiry_date DATE NOT NULL");
        if ($alterTable) {
            echo "<p style='color:green'>Added 'expiry_date' column to notices table.</p>";
        } else {
            echo "<p style='color:red'>Error adding 'expiry_date' column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Expiry_date column already exists in notices table.</p>";
    }
} else {
    echo "<p style='color:red'>Notices table does not exist. <a href='create_notices_table.php' style='color:blue;'>Click here to create the notices table</a>.</p>";
}

echo "<h2>Database Fix Complete</h2>";
echo "<p>All necessary fixes have been applied to the database tables.</p>";
echo "<div style='margin-top: 20px;'>";
echo "<a href='create_essential_tables.php' style='background-color: #4CAF50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>Run Create Essential Tables Script</a>";
echo "<a href='create_staff_table.php' style='background-color: #2196F3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>Create Staff Table</a>";
echo "<a href='create_notices_table.php' style='background-color: #f44336; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>Create Notices Table</a>";
echo "<a href='index.php' style='background-color: #555555; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>Return to Homepage</a>";
echo "</div>";

// Close connection
$conn->close();
?>
