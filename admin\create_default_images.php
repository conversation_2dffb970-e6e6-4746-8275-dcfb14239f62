<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Create directories if they don't exist
$directories = [
    '../assets/images',
    '../uploads',
    '../uploads/students'
];

foreach ($directories as $dir) {
    if (!file_exists($dir)) {
        mkdir($dir, 0777, true);
    }
}

// Check if GD library is available
$gdAvailable = function_exists('imagecreatetruecolor');

// Create default logo
$defaultLogoPath = '../assets/images/default_logo.png';
$defaultLogoSvgPath = '../assets/images/default_logo.svg';

if (!file_exists($defaultLogoPath) && !file_exists($defaultLogoSvgPath)) {
    if ($gdAvailable) {
        // Create a blank image using GD
        $logo = imagecreatetruecolor(100, 100);

        // Colors
        $white = imagecolorallocate($logo, 255, 255, 255);
        $blue = imagecolorallocate($logo, 0, 114, 187);
        $darkBlue = imagecolorallocate($logo, 0, 51, 102);

        // Fill background
        imagefill($logo, 0, 0, $white);

        // Draw a circle
        imagefilledellipse($logo, 50, 50, 80, 80, $blue);

        // Draw text
        imagestring($logo, 5, 35, 40, 'SMS', $white);

        // Save the image
        imagepng($logo, $defaultLogoPath);
        imagedestroy($logo);
    } else {
        // If GD is not available, create a simple SVG logo
        $logoHtml = '<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100">
            <circle cx="50" cy="50" r="40" fill="#0072BB" />
            <text x="50" y="55" font-family="Arial" font-size="20" fill="white" text-anchor="middle">SMS</text>
        </svg>';

        // Save the SVG to a file
        file_put_contents($defaultLogoSvgPath, $logoHtml);
    }
}

// Create default student photo
$defaultStudentPath = '../assets/images/default_student.png';
$defaultStudentSvgPath = '../assets/images/default_student.svg';

if (!file_exists($defaultStudentPath) && !file_exists($defaultStudentSvgPath)) {
    if ($gdAvailable) {
        // Create a blank image using GD
        $student = imagecreatetruecolor(100, 120);

        // Colors
        $white = imagecolorallocate($student, 255, 255, 255);
        $gray = imagecolorallocate($student, 200, 200, 200);
        $darkGray = imagecolorallocate($student, 100, 100, 100);

        // Fill background
        imagefill($student, 0, 0, $white);

        // Draw a silhouette
        imagefilledellipse($student, 50, 40, 60, 60, $gray);
        imagefilledrectangle($student, 20, 70, 80, 120, $gray);

        // Draw face details
        imagefilledellipse($student, 50, 40, 30, 30, $darkGray);

        // Save the image
        imagepng($student, $defaultStudentPath);
        imagedestroy($student);
    } else {
        // If GD is not available, create a simple SVG student silhouette
        $studentHtml = '<svg xmlns="http://www.w3.org/2000/svg" width="100" height="120" viewBox="0 0 100 120">
            <rect width="100" height="120" fill="white" />
            <circle cx="50" cy="40" r="30" fill="#C8C8C8" />
            <rect x="20" y="70" width="60" height="50" fill="#C8C8C8" />
        </svg>';

        // Save the SVG to a file
        file_put_contents($defaultStudentSvgPath, $studentHtml);
    }
}

$success = true;
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ডিফল্ট ছবি তৈরি - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <?php include 'includes/global-head.php'; ?>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-images me-2 text-primary"></i> ডিফল্ট ছবি তৈরি
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="dashboard.php" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="fas fa-tachometer-alt me-1"></i> ড্যাশবোর্ড
                        </a>
                    </div>
                </div>

                <?php if ($success): ?>
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i> ডিফল্ট ছবিগুলি সফলভাবে তৈরি করা হয়েছে।
                </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-university me-2"></i> প্রতিষ্ঠানের লোগো
                                </h5>
                            </div>
                            <div class="card-body text-center">
                                <?php if (file_exists($defaultLogoPath)): ?>
                                <img src="<?php echo $defaultLogoPath; ?>" alt="Default Logo" class="img-fluid mb-3" style="max-width: 150px;">
                                <?php elseif (file_exists($defaultLogoSvgPath)): ?>
                                <img src="<?php echo $defaultLogoSvgPath; ?>" alt="Default Logo" class="img-fluid mb-3" style="max-width: 150px;">
                                <?php else: ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i> ডিফল্ট লোগো তৈরি করতে সমস্যা হয়েছে।
                                </div>
                                <?php endif; ?>
                                <p>এটি ডিফল্ট প্রতিষ্ঠানের লোগো। আপনি এটি পরিবর্তন করতে পারেন।</p>
                                <a href="upload_logo.php" class="btn btn-primary">
                                    <i class="fas fa-upload me-1"></i> নতুন লোগো আপলোড করুন
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-user me-2"></i> ডিফল্ট শিক্ষার্থী ছবি
                                </h5>
                            </div>
                            <div class="card-body text-center">
                                <?php if (file_exists($defaultStudentPath)): ?>
                                <img src="<?php echo $defaultStudentPath; ?>" alt="Default Student" class="img-fluid mb-3" style="max-width: 150px;">
                                <?php elseif (file_exists($defaultStudentSvgPath)): ?>
                                <img src="<?php echo $defaultStudentSvgPath; ?>" alt="Default Student" class="img-fluid mb-3" style="max-width: 150px;">
                                <?php else: ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i> ডিফল্ট শিক্ষার্থী ছবি তৈরি করতে সমস্যা হয়েছে।
                                </div>
                                <?php endif; ?>
                                <p>এটি ডিফল্ট শিক্ষার্থী ছবি। শিক্ষার্থীর নিজস্ব ছবি না থাকলে এটি ব্যবহার করা হবে।</p>
                                <p class="small text-muted">শিক্ষার্থীর ছবি আপলোড করতে, শিক্ষার্থী প্রোফাইল পেজে যান।</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i> ব্যবহার নির্দেশিকা
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>মার্কশীটে প্রতিষ্ঠানের লোগো এবং শিক্ষার্থীর ছবি দেখানোর জন্য:</p>
                        <ol>
                            <li>প্রতিষ্ঠানের লোগো আপলোড করতে, "নতুন লোগো আপলোড করুন" বাটনে ক্লিক করুন।</li>
                            <li>শিক্ষার্থীর ছবি আপলোড করতে, শিক্ষার্থী প্রোফাইল পেজে যান।</li>
                            <li>যদি কোন ছবি না থাকে, তাহলে ডিফল্ট ছবি ব্যবহার করা হবে।</li>
                        </ol>
                        <p>ফাইল নামকরণ নিয়ম:</p>
                        <ul>
                            <li>প্রতিষ্ঠানের লোগো: <code>uploads/institution_logo.png</code></li>
                            <li>শিক্ষার্থীর ছবি: <code>uploads/students/[student_id].jpg</code></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
