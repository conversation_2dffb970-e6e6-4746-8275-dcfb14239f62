<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get filter parameters
$classId = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;
$departmentId = isset($_GET['department_id']) ? intval($_GET['department_id']) : 0;
$batchId = isset($_GET['batch']) ? $_GET['batch'] : '';
$studentIds = isset($_GET['student_ids']) ? explode(',', $_GET['student_ids']) : [];
$expiryDate = isset($_GET['expiry_date']) ? $_GET['expiry_date'] : date('Y-m-d', strtotime('+1 year'));

// Get classes for filter dropdown
$classesQuery = "SELECT DISTINCT id, class_name FROM classes ORDER BY class_name";
$classesResult = $conn->query($classesQuery);

// Get departments for filter dropdown
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departmentsResult = $conn->query($departmentsQuery);

// Get batches for filter dropdown
$batchesQuery = "SELECT DISTINCT batch FROM students WHERE batch IS NOT NULL AND batch != '' ORDER BY batch";
$batchesResult = $conn->query($batchesQuery);

// Get students based on filters
$studentsQuery = "SELECT s.*, c.class_name, d.department_name
                 FROM students s
                 LEFT JOIN classes c ON s.class_id = c.id
                 LEFT JOIN departments d ON s.department_id = d.id
                 WHERE 1=1";

$params = [];
$types = "";

if ($classId > 0) {
    $studentsQuery .= " AND s.class_id = ?";
    $params[] = $classId;
    $types .= "i";
}

if ($departmentId > 0) {
    $studentsQuery .= " AND s.department_id = ?";
    $params[] = $departmentId;
    $types .= "i";
}

if (!empty($batchId)) {
    $studentsQuery .= " AND s.batch = ?";
    $params[] = $batchId;
    $types .= "s";
}

if (!empty($studentIds)) {
    $placeholders = str_repeat('?,', count($studentIds) - 1) . '?';
    $studentsQuery .= " AND s.id IN ($placeholders)";
    foreach ($studentIds as $id) {
        $params[] = $id;
        $types .= "i";
    }
}

$studentsQuery .= " ORDER BY s.first_name, s.last_name";

$stmt = $conn->prepare($studentsQuery);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$studentsResult = $stmt->get_result();

// Get school settings
$schoolSettings = [];
$schoolQuery = "SELECT * FROM school_settings LIMIT 1";
$schoolResult = $conn->query($schoolQuery);
if ($schoolResult && $schoolResult->num_rows > 0) {
    $schoolSettings = $schoolResult->fetch_assoc();
}

// School name and address from settings or fallback to defaults
$schoolName = isset($schoolSettings['school_name']) ? $schoolSettings['school_name'] : 'জেড এফ এ ডব্লিউ কলেজ';
$schoolAddress = isset($schoolSettings['school_address']) ? $schoolSettings['school_address'] : 'বাংলাদেশ';
$schoolPhone = isset($schoolSettings['school_phone']) ? $schoolSettings['school_phone'] : '';
$schoolEmail = isset($schoolSettings['school_email']) ? $schoolSettings['school_email'] : '';
// Get school logo directly from database
$schoolLogo = '';

// Try to get school logo from settings
$logoQuery = "SELECT logo_path FROM school_settings LIMIT 1";
$logoResult = $conn->query($logoQuery);

if ($logoResult && $logoResult->num_rows > 0) {
    $logoData = $logoResult->fetch_assoc();
    if (!empty($logoData['logo_path'])) {
        $logoPath = '../' . $logoData['logo_path'];
        if (file_exists($logoPath)) {
            $schoolLogo = $logoPath;
        }
    }
}

// If no logo found, use default
if (empty($schoolLogo)) {
    $defaultLogo = '../assets/images/school-logo.png';
    if (file_exists($defaultLogo)) {
        $schoolLogo = $defaultLogo;
    } else {
        // Use data URI as last resort
        $schoolLogo = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAACXBIWXMAAAsTAAALEwEAmpwYAAAEFUlEQVR4nO2ZW4hVVRjHf3PmzHhpJu2iYjZFZEQPvVRERBBREUQvRQ9BvUWUERFBQpDQQz1E9BBEL5FQBBFET0EQQRQxRBBDYEQQXchLOpmXGWfmdPDfsBj2OWevvfY+Z+bEDz6Ys9f3rf9/r8v6vvUdqKmmmmrqf6YJwCJgJXAA6AcGgT+BH4FNwCPAjJrNMEJTgGeAQ0AhpRwFPgQWA2NrMeGRwFrgWIZJJMsB4Clg9EhMfCrwdYGJJMsXwNThXMBs4PcKTCJZfgNmDcci5gC7KziJZNkJnF3NRcwDDlVhEslyELiuGou4Bjhe5YnEchyYX8lFLADODNMkYjkNLCx3EcuAoRpMIlmGgNvLWcTDNZxELI8UO/mxwKc1nkRSPgHGFTP5V+owiaS8UMzkX6/TBJL5yYysCbxbx0nE8g4wKm3yrw3DJJLyatrkNw7TJGL5GBiTnPwPwziBWL4Dxicn/9swTyKWPcCk5OR/r8MEYvkLmBpPfn8dJxHLAeC8ePJ/13ECsRwCLogn31/HCcRyBLgwnvyJOk4ilpPAJfHkT9VxErGcBi6LJ3+6jpOI5QxweTz5gTpOIpZB4Ip48oN1nEQsQ8BV8eQH6jiBWIaAq+PJH6/jJGI5AVwTT/5oHScRy1HgunjyB+s4iVgOADfEk99Xx0nEshe4MZ78rjpOIpadwE3x5LfXcRKxbANujif/dR0nEcuXwC3x5D+r4yRi+Ri4NZ78hjpOIpZ3gNviyb9Ux0nE8jxwezz5Z+s4iVhWA3fEk19Zx0nEsgK4M578sjpOIpYlwF3x5BfWcRKx3APcHU9+Th0nEcudwL3x5GfWcRKxzAMWxZOfWsdJxDINeDCe/IQ6TiKW8cDD8eRbajSBVmAy0AHcBDwOrAE+APqAf/TdPuBT4FVgGXCj+jSrz1jgkXjyY6o4+Wn6xV8CdgP/ZvTt0xh9wGZgNXC9+o7TuI/Gk2+uwOTHAHcDrwM/A/8UOeGssldtXwNWAXOBsRqnJZ58U4mTnww8DbQDp0qccFbpV9stwDPAFPUxRuM2x5MvZvLjgQeBN4BfKzDhYmSv+r4OPKBcqEXjj4snP5DJ36Ik6nSNJpxV9msMY5hJGq85nnzI5JcCO4Z5wlllh8ZcGk++IZN/EngX+KfGEy1G/tXYT4VMvlGJ0BvAkTpPtBg5ojEbQybfADwG7K7T5MqR3RrbhEw+9Mq+Ddhdp8mVI7s1tgmZfOiVfRvQXafJlSPdGtuETD70yr4N6KrT5MqRLo1tQiYfemXfBnTWaXLlSKfGNiGTD72ybwM66jS5cqRDY5uQyYde2bcB7XWaXDnSrrFNyORDr+zbgLY6Ta4cadPYJmTyoVf2bUBrnSZXjrRqbBMy+dAr+zagpU6TK0daNLYJmXzolX1TTTXVVFMj6j+SaG0mXMH2bAAAAABJRU5ErkJggg==';
    }
}

// Function to convert English numbers to Bengali
function convertToBengaliNumber($number) {
    $bengaliDigits = ['০', '১', '২', '৩', '৪', '৫', '৬', '৭', '৮', '৯'];
    $englishDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    return str_replace($englishDigits, $bengaliDigits, $number);
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>শিক্ষার্থী আইডি কার্ড - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* General Styles */
        body {
            font-family: 'SolaimanLipi', Arial, sans-serif;
            background-color: #f8f9fa;
        }

        /* ID Card Styles */
        .id-card-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            gap: 5mm;
            padding: 10mm;
        }

        .id-card {
            width: 2.5in;
            height: 3.5in;
            border: none;
            border-radius: 12px;
            overflow: visible;
            background-color: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            margin-bottom: 5mm;
            page-break-inside: avoid;
            position: relative;
        }

        .id-card-header {
            background: linear-gradient(135deg, #0d6efd, #0a4bb8);
            color: white;
            padding: 12px 10px;
            text-align: center;
            border-bottom: none;
            border-radius: 12px 12px 0 0;
            position: relative;
            overflow: hidden;
        }

        .id-card-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: rgba(255, 255, 255, 0.3);
        }

        .id-card-logo {
            width: 50px;
            height: 50px;
            margin: 0 auto 5px;
            display: block;
            object-fit: contain;
            border-radius: 50%;
            padding: 2px;
            background-color: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .id-card-school-name {
            font-size: 14px;
            font-weight: bold;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .id-card-school-address {
            font-size: 10px;
            margin: 3px 0 0;
            color: rgba(255, 255, 255, 0.9);
            background-color: rgba(0, 0, 0, 0.15);
            display: inline-block;
            padding: 2px 10px;
            border-radius: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .id-card-body {
            padding: 10px;
            text-align: center;
        }

        .id-card-content {
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            justify-content: space-between;
            width: 100%;
            margin-bottom: 10px;
            padding: 5px;
        }

        .id-card-photo-container {
            width: 35%;
            padding-right: 10px;
        }

        .id-card-details-container {
            width: 65%;
            text-align: left;
            padding-left: 12px;
            border-left: 1px solid #eee;
            position: relative;
        }

        .id-card-details-container::before {
            content: '';
            position: absolute;
            left: -1px;
            top: 0;
            bottom: 0;
            width: 1px;
            background: linear-gradient(to bottom, transparent, #ddd, transparent);
        }

        .id-card-photo {
            width: 75px;
            height: 95px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 0 auto;
        }

        .id-card-photo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }

        .id-card-name {
            font-size: 13px;
            font-weight: bold;
            margin-bottom: 8px;
            text-align: left;
            border-bottom: 1px dashed #ccc;
            padding-bottom: 4px;
            color: #0a4bb8;
            position: relative;
        }

        .id-card-name::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #0d6efd;
        }

        .id-card-details {
            font-size: 10px;
            margin-bottom: 5px;
            text-align: left;
            padding-left: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.4;
            color: #333;
        }

        .id-card-details strong {
            color: #0d6efd;
            font-weight: 600;
            margin-right: 3px;
        }

        .id-card-body {
            padding: 12px 10px;
            height: calc(100% - 80px - 30px); /* Subtract header and footer height */
            display: flex;
            flex-direction: column;
            position: relative;
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
            overflow: visible;
        }

        .id-card-info {
            flex-grow: 1;
            margin-bottom: 30px; /* Make space for signature and footer */
            max-width: 100%;
            overflow: hidden;
        }

        .id-card-footer {
            position: absolute;
            bottom: 3px;
            left: 10px;
            text-align: left;
            padding: 2px;
            font-size: 5px;
            line-height: 1;
            width: 60%;
            color: #555;
        }

        .id-card-signature {
            margin-top: 10px;
            border-top: 1px solid #000;
            padding-top: 3px;
            font-size: 7px;
            width: 40%;
            position: absolute;
            bottom: 15px;
            right: 10px;
            color: #000;
            text-align: center;
            display: block !important;
            z-index: 10;
            background-color: white;
        }

        .id-card-signature span {
            display: inline-block;
            width: 100%;
            padding-top: 2px;
            background-color: white;
        }

        /* Print Styles */
        @page {
            size: A4;
            margin: 0;
        }

        @media print {
            body {
                background: white;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .no-print {
                display: none !important;
            }

            .id-card-container {
                padding: 10mm;
            }

            .id-card {
                break-inside: avoid;
                page-break-inside: avoid;
                box-shadow: none;
            }

            .id-card-header {
                background-color: #0d6efd !important;
                color: white !important;
            }
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar no-print">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i>শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i>শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="generate_id_cards.php">
                            <i class="fas fa-id-card me-2"></i>আইডি কার্ড
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 ms-auto main-content">
                <div class="container mt-4 no-print">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-id-card me-2"></i>শিক্ষার্থী আইডি কার্ড</h2>
                        <button class="btn btn-success" onclick="window.print()">
                            <i class="fas fa-print me-2"></i>প্রিন্ট করুন
                        </button>
                    </div>

                    <!-- Filter Form -->
                    <div class="card mb-4 no-print">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-filter me-2"></i>ফিল্টার অপশন</h5>
                        </div>
                        <div class="card-body">
                            <form action="" method="GET" class="row g-3">
                                <div class="col-md-4">
                                    <label for="class_id" class="form-label">শ্রেণী</label>
                                    <select class="form-select" id="class_id" name="class_id">
                                        <option value="0">সকল শ্রেণী</option>
                                        <?php
                                        $seenClasses = [];
                                        $classesResult->data_seek(0); // Reset pointer
                                        while ($class = $classesResult->fetch_assoc()):
                                            // Prevent duplicate class names
                                            if (!in_array($class['class_name'], $seenClasses)):
                                                $seenClasses[] = $class['class_name'];
                                        ?>
                                            <option value="<?= $class['id'] ?>" <?= ($classId == $class['id']) ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($class['class_name']) ?>
                                            </option>
                                        <?php
                                            endif;
                                        endwhile;
                                        ?>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="department_id" class="form-label">বিভাগ</label>
                                    <select class="form-select" id="department_id" name="department_id">
                                        <option value="0">সকল বিভাগ</option>
                                        <?php while ($department = $departmentsResult->fetch_assoc()): ?>
                                            <option value="<?= $department['id'] ?>" <?= ($departmentId == $department['id']) ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($department['department_name']) ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="batch" class="form-label">ব্যাচ</label>
                                    <select class="form-select" id="batch" name="batch">
                                        <option value="">সকল ব্যাচ</option>
                                        <?php while ($batch = $batchesResult->fetch_assoc()): ?>
                                            <option value="<?= $batch['batch'] ?>" <?= ($batchId == $batch['batch']) ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($batch['batch']) ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="expiry_date" class="form-label">আইডি কার্ডের মেয়াদ</label>
                                    <input type="date" class="form-control" id="expiry_date" name="expiry_date" value="<?= $expiryDate ?>">
                                </div>
                                <div class="col-12 text-center mt-3">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>খুঁজুন
                                    </button>
                                    <a href="generate_id_cards.php" class="btn btn-secondary">
                                        <i class="fas fa-redo me-2"></i>রিসেট
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Student Selection -->
                    <?php if ($studentsResult->num_rows > 0): ?>
                        <div class="card mb-4 no-print">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0"><i class="fas fa-users me-2"></i>শিক্ষার্থী নির্বাচন করুন</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="select-all-students">
                                        <label class="form-check-label" for="select-all-students">
                                            সকল শিক্ষার্থী নির্বাচন করুন
                                        </label>
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>নির্বাচন</th>
                                                <th>আইডি</th>
                                                <th>নাম</th>
                                                <th>শ্রেণী</th>
                                                <th>বিভাগ</th>
                                                <th>ব্যাচ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php while ($student = $studentsResult->fetch_assoc()): ?>
                                                <tr>
                                                    <td>
                                                        <div class="form-check">
                                                            <input class="form-check-input student-checkbox" type="checkbox"
                                                                value="<?= $student['id'] ?>"
                                                                id="student-<?= $student['id'] ?>"
                                                                <?= in_array($student['id'], $studentIds) ? 'checked' : '' ?>>
                                                        </div>
                                                    </td>
                                                    <td><?= htmlspecialchars($student['student_id']) ?></td>
                                                    <td><?= htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) ?></td>
                                                    <td><?= htmlspecialchars($student['class_name'] ?? 'N/A') ?></td>
                                                    <td><?= htmlspecialchars($student['department_name'] ?? 'N/A') ?></td>
                                                    <td><?= htmlspecialchars($student['batch'] ?? 'N/A') ?></td>
                                                </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-center mt-3">
                                    <button id="generate-selected" class="btn btn-primary">
                                        <i class="fas fa-id-card me-2"></i>নির্বাচিত শিক্ষার্থীদের আইডি কার্ড তৈরি করুন
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- ID Cards Container -->
                <div class="id-card-container">
                    <?php
                    // Reset the result pointer
                    if ($studentsResult->num_rows > 0) {
                        $studentsResult->data_seek(0);

                        while ($student = $studentsResult->fetch_assoc()):
                            // Skip if not in selected student IDs when filtering is applied
                            if (!empty($studentIds) && !in_array($student['id'], $studentIds)) {
                                continue;
                            }

                                                        // Get student photo
                            $photoFile = '';

                            // Check if profile_photo exists in database
                            if (!empty($student['profile_photo'])) {
                                // If profile_photo contains full path
                                if (strpos($student['profile_photo'], 'uploads/') !== false) {
                                    $photoFile = '../' . $student['profile_photo'];
                                } else {
                                    // If profile_photo contains only filename
                                    $photoFile = '../uploads/profile_photos/' . $student['profile_photo'];
                                }

                                // Verify file exists
                                if (!file_exists($photoFile)) {
                                    // Try to get photo from database again
                                    $photoQuery = "SELECT profile_photo FROM students WHERE id = ?";
                                    $photoStmt = $conn->prepare($photoQuery);
                                    $photoStmt->bind_param("i", $student['id']);
                                    $photoStmt->execute();
                                    $photoResult = $photoStmt->get_result();

                                    if ($photoResult && $photoResult->num_rows > 0) {
                                        $photoData = $photoResult->fetch_assoc();
                                        if (!empty($photoData['profile_photo'])) {
                                            // Check if it contains full path
                                            if (strpos($photoData['profile_photo'], 'uploads/') !== false) {
                                                $photoFile = '../' . $photoData['profile_photo'];
                                            } else {
                                                $photoFile = '../uploads/profile_photos/' . $photoData['profile_photo'];
                                            }
                                        }
                                    }
                                }
                            }

                            // If still no photo, use a default image
                            if (empty($photoFile) || !file_exists($photoFile)) {
                                $photoFile = '../assets/images/student-placeholder.png';

                                // If default image doesn't exist, use data URI
                                if (!file_exists($photoFile)) {
                                    $photoFile = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAABQCAYAAACOEfKtAAAACXBIWXMAAAsTAAALEwEAmpwYAAAGlklEQVR4nO2ca2xURRTHB0WJiIAKVt4Pob5QH6Cg4gM/GBM1MUYwxkSjidGo8YMaE2PiB+MrJj4SNTHGGDWiH4xGCRolKFWrEAVFKwQQEBCQh1Cgyv/EnJjd7t29d+7M3N3d/pPJpnTvnDn/PXPmzJkzZwoFh8PhcDgcDofD4XA4HA6Hw+FwOBwOh8PhcDgcjmwxHJgBLAU2AzuAvcAB4G9gHfAZcC8wKkM9GoALgOeAL4FdQBPwJ/A98DYwG6jPUJdIOQZ4BNgM9Pn8NgFvAWdlpNdU4FVgbwCd9gCvAFMy0isUdcBDwP4QHdwHfAJcbFm3BuBOYGcEnfYBjwL1lvWLhKuBbRE7uR94GRhnSbdJwGKgO6ZOW4BrLOkXmqnAjwY72gPMB0ZY0G8e0GlQp++AUy3oGIhRwBsGO9kLPAEMtaDjcOBJoNuwTm8CIy3oWZXpwC8GO7gbuMWCfgDXAX9Z0GkVcIYFPctyObDHYOd2AtMM6zcG+Myibj3AlYZ1LcudQJfBjm0DTjKs3/nAHxZ16gLuMqxrCWOBTwx3qgU40bCOTwFHLOu0AjjeoK79uNNwZ/qAH4DjDOo3Dvguhk5HgG+BhcBNwCxgMnAiMBE4HbgUuB94HlgdQ6cfgfEGde3nIcOdaQMmGNRvJrA9hC5HgaXAPcCZIeYfDcA5wHzguxB6tQOXGNS3wHDgfcOdaTWY+owCFoXQYxtwP3BSzLlwPPAAsCOEfh8Aw0zpXGe4E0eB6w3pdwGwJ0CHXuBVYJJBHSYDrwWcI3uBCw3pXJhnsAPNwAkG9LsF6AjQ4QvgNAv6TAe+DNCjA7jZgN6FWwx1YLXhxOJeoC1Ah9XAGIt6jQVWBujTBtxmQPfCNYY6sNjQvDcS+DhAh5+AYy3rNg74OUCnJcDImLoX5hjqwEsG5r4XA3TYAJyQkH4nAr8G6PZCzDYKMw114KmYc9/xwA8+7fcCFyWo30XAPh/9fgLGxWi/MMtQBx6JOfctCJj7bkpYv5t95sJeYH6M9guzDXXg/hht1wEf+bS9Ajg2Yf2OA1b6tP9hjFR4YY6hDsyL0fYcn3Z7gGkp6DfNZ9mzB7g6YtuF6w114JYYbS/yaXdRCvpd7NPuwohtF64z1IFrYrS91KfdG1PQ702fdpdFbLsw01AHZsRo+zufdt9OQb/vfNqdHrHtwiRDHZgYo+0Wn3Z/SUG/Vp92J0Zsu2B6+TEuYbfRbRHbLgwz1IFhMdru9Gm3KwX9On3aHRGx7ULBUAfiEPXDJE2fD5OCoQ4MidF2vU+79SnoN8Gn3aiHRQXTHYhKnU+7dSno5/chFPWDqJDVHBiVOp92f0tBv7997h0Vse1CVnNgVIb6tLs7Bf12+bQ7OmLbhYLpDkQl6vYtKf38tm9Rt6+FrObAqAzxabcnBf16fNoNvH0rR1ZzYFT8Pky6U9Cv26fdqB9EhYLpDkTF7zB9fwr6+R2mRz1ML2Q1B0Zlo0+7Z6eg31k+7W6I2HYhqzkwKst82r0iBf2u9Gl3acS2C1nNgVFZ7NPuTSnoN9un3UUR2y5kNQdGZZFPu/eloN+DPu0ujNh2Ias5MCqP+7T7Ygr6veTT7mMR2y5kNQdG5Vafdr8Hjk9Yv/HABp92b4nYdiGrOTAqp/jkwb3AzIT1u8ln+9YFnBqx7UJWc2Ac/E6U1wInJaTbycA6H93+iHeinNUcGJe7ffTrA94GGizrNgJ4J0C3u2K0X8hqDozLOGC9jw69wEsWz4lHAq8H6LUOGBuj/UJWc6AJJgObAnTZCNxoQZ+bgE0BOm0GTjOgeyGrOdAUC3zyYeX3GXCuId3OAz4P0KMDuN2Q7oWs5kCTzPHJh5XfIeBJYHQMvUYDTwGHfXTqAWYb1LuQ1RxomjrgVZ+5UPltBu4jXCHbBOB+YEtA+6uAOsN6F7KaA23QAHwaoEu5Xzvwuc9vGXCKJZ0LWc2BtpgA/BxCn3K/VuANYC4wDRgLDAHGAGcDdwCLQ+bPNcBEi/oWspoDbXI28FsEnZL4/QZMtaxrIas50DaXAVsj6GT7txW41IKehazmoCSYDXTG0Mf0rxOYZUm/QlZzUJJMBZbH0Mn0bxlwuiXdClnNQUkzElgQQy+Tv4XASEt6FbKag9JiOrAyhm4mfiuBaRZ1KWQ1B6XJMGAe0BZDN1O/NuBeC9u1srqS0hyUNqOAB4HWGDrG/bUCDwCjLOtQyGoOygZ1wJXAEqAphp5Rfk3AYuAKS3NeOVzJcTgcDofD4XA4HFnxP7nGi9Pj9ff4AAAAAElFTkSuQmCC';
                                }
                            }
                    ?>
                        <div class="id-card">
                            <div class="id-card-header">
                                <img src="<?= $schoolLogo ?>" alt="School Logo" class="id-card-logo">
                                <h3 class="id-card-school-name"><?= htmlspecialchars($schoolName) ?></h3>
                                <p class="id-card-school-address"><?= htmlspecialchars($schoolAddress) ?></p>
                            </div>
                            <div class="id-card-body">
                                <div class="id-card-info">
                                    <div class="id-card-content">
                                        <div class="id-card-photo-container">
                                            <div class="id-card-photo">
                                                <img src="<?= $photoFile ?>" alt="Student Photo">
                                            </div>
                                        </div>
                                        <div class="id-card-details-container">
                                            <h4 class="id-card-name"><?= htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) ?></h4>
                                            <p class="id-card-details"><strong>আইডি:</strong> <?= htmlspecialchars($student['student_id']) ?></p>
                                            <p class="id-card-details"><strong>শ্রেণী:</strong> <?= htmlspecialchars($student['class_name'] ?? 'N/A') ?></p>
                                            <p class="id-card-details"><strong>বিভাগ:</strong> <?= htmlspecialchars($student['department_name'] ?? 'N/A') ?></p>
                                            <?php if (!empty($student['batch'])): ?>
                                                <p class="id-card-details"><strong>ব্যাচ:</strong> <?= htmlspecialchars($student['batch']) ?></p>
                                            <?php endif; ?>
                                            <?php if (!empty($student['phone'])): ?>
                                                <p class="id-card-details"><strong>ফোন:</strong> <?= htmlspecialchars($student['phone']) ?></p>
                                            <?php endif; ?>
                                            <p class="id-card-details"><strong>মেয়াদ:</strong> <?= date('d/m/Y', strtotime($expiryDate)) ?></p>
                                        </div>
                                    </div>
                                </div>

                                <div class="id-card-signature">
                                    <span style="display: block; padding-top: 2px; background-color: white;">প্রতিষ্ঠান প্রধানের স্বাক্ষর</span>
                                </div>
                            </div>
                            <div class="id-card-footer">
                                এই আইডি কার্ডটি প্রতিষ্ঠানের সম্পত্তি
                            </div>
                        </div>
                    <?php
                        endwhile;
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Select/Deselect all students
            $('#select-all-students').change(function() {
                $('.student-checkbox').prop('checked', $(this).prop('checked'));
            });

            // Generate ID cards for selected students
            $('#generate-selected').click(function() {
                const selectedStudents = [];
                $('.student-checkbox:checked').each(function() {
                    selectedStudents.push($(this).val());
                });

                if (selectedStudents.length === 0) {
                    alert('দয়া করে কমপক্ষে একজন শিক্ষার্থী নির্বাচন করুন।');
                    return;
                }

                // Build URL with current filters and selected students
                let url = 'generate_id_cards.php?';
                const classId = $('#class_id').val();
                const departmentId = $('#department_id').val();
                const batch = $('#batch').val();
                const expiryDate = $('#expiry_date').val();

                if (classId > 0) {
                    url += 'class_id=' + classId + '&';
                }

                if (departmentId > 0) {
                    url += 'department_id=' + departmentId + '&';
                }

                if (batch) {
                    url += 'batch=' + batch + '&';
                }

                if (expiryDate) {
                    url += 'expiry_date=' + expiryDate + '&';
                }

                url += 'student_ids=' + selectedStudents.join(',');

                // Navigate to the URL
                window.location.href = url;
            });
        });
    </script>
</body>
</html>
