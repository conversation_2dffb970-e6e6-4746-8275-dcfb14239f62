<?php
require_once 'includes/dbh.inc.php';

// Check if exam_subjects table exists
$result = $conn->query("SHOW TABLES LIKE 'exam_subjects'");
if ($result->num_rows > 0) {
    echo "exam_subjects table exists<br>";
    
    // Get table structure
    $structure = $conn->query("DESCRIBE exam_subjects");
    echo "<h3>Table Structure:</h3>";
    echo "<pre>";
    while ($row = $structure->fetch_assoc()) {
        print_r($row);
    }
    echo "</pre>";
} else {
    echo "exam_subjects table does not exist<br>";
}

// Check if exams table exists
$result = $conn->query("SHOW TABLES LIKE 'exams'");
if ($result->num_rows > 0) {
    echo "exams table exists<br>";
    
    // Get table structure
    $structure = $conn->query("DESCRIBE exams");
    echo "<h3>Exams Table Structure:</h3>";
    echo "<pre>";
    while ($row = $structure->fetch_assoc()) {
        print_r($row);
    }
    echo "</pre>";
} else {
    echo "exams table does not exist<br>";
}

// Check if exam_types table exists
$result = $conn->query("SHOW TABLES LIKE 'exam_types'");
if ($result->num_rows > 0) {
    echo "exam_types table exists<br>";
    
    // Get table structure
    $structure = $conn->query("DESCRIBE exam_types");
    echo "<h3>Exam Types Table Structure:</h3>";
    echo "<pre>";
    while ($row = $structure->fetch_assoc()) {
        print_r($row);
    }
    echo "</pre>";
} else {
    echo "exam_types table does not exist<br>";
}
?>
