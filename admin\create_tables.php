<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$messages = [];

// Create classes table if it doesn't exist
$classesTableQuery = "CREATE TABLE IF NOT EXISTS classes (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    class_name VARCHAR(100) NOT NULL,
    department_id INT(11) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($classesTableQuery)) {
    $messages[] = "Classes table created or already exists.";
} else {
    $messages[] = "Error creating classes table: " . $conn->error;
}

// Create sessions table if it doesn't exist
$sessionsTableQuery = "CREATE TABLE IF NOT EXISTS sessions (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    session_name VARCHAR(100) NOT NULL,
    start_date DATE DEFAULT NULL,
    end_date DATE DEFAULT NULL,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($sessionsTableQuery)) {
    $messages[] = "Sessions table created or already exists.";
} else {
    $messages[] = "Error creating sessions table: " . $conn->error;
}

// Check if classes table is empty
$checkClassesQuery = "SELECT COUNT(*) as count FROM classes";
$classesResult = $conn->query($checkClassesQuery);
$classesCount = 0;
if ($classesResult) {
    $classesCount = $classesResult->fetch_assoc()['count'];
}

// Insert sample classes if table is empty
if ($classesCount == 0) {
    $sampleClasses = [
        "ষষ্ঠ শ্রেণী",
        "সপ্তম শ্রেণী",
        "অষ্টম শ্রেণী",
        "নবম শ্রেণী",
        "দশম শ্রেণী",
        "একাদশ শ্রেণী",
        "দ্বাদশ শ্রেণী"
    ];
    
    $insertClassQuery = "INSERT INTO classes (class_name) VALUES (?)";
    $stmt = $conn->prepare($insertClassQuery);
    
    foreach ($sampleClasses as $className) {
        $stmt->bind_param("s", $className);
        $stmt->execute();
    }
    
    $messages[] = "Sample classes added.";
}

// Check if sessions table is empty
$checkSessionsQuery = "SELECT COUNT(*) as count FROM sessions";
$sessionsResult = $conn->query($checkSessionsQuery);
$sessionsCount = 0;
if ($sessionsResult) {
    $sessionsCount = $sessionsResult->fetch_assoc()['count'];
}

// Insert sample sessions if table is empty
if ($sessionsCount == 0) {
    $currentYear = date('Y');
    $sampleSessions = [
        "$currentYear-" . ($currentYear + 1),
        ($currentYear - 1) . "-$currentYear",
        ($currentYear + 1) . "-" . ($currentYear + 2),
        ($currentYear - 2) . "-" . ($currentYear - 1)
    ];
    
    $insertSessionQuery = "INSERT INTO sessions (session_name) VALUES (?)";
    $stmt = $conn->prepare($insertSessionQuery);
    
    foreach ($sampleSessions as $sessionName) {
        $stmt->bind_param("s", $sessionName);
        $stmt->execute();
    }
    
    $messages[] = "Sample sessions added.";
}

// Count classes and sessions
$classesQuery = "SELECT COUNT(*) as count FROM classes";
$classesResult = $conn->query($classesQuery);
$classesCount = $classesResult->fetch_assoc()['count'];

$sessionsQuery = "SELECT COUNT(*) as count FROM sessions";
$sessionsResult = $conn->query($sessionsQuery);
$sessionsCount = $sessionsResult->fetch_assoc()['count'];

$messages[] = "Total classes: $classesCount";
$messages[] = "Total sessions: $sessionsCount";
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>টেবিল তৈরি করুন</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Hind Siliguri -->
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">টেবিল তৈরি করুন</h1>
        
        <div class="alert alert-info">
            <?php foreach ($messages as $message): ?>
                <p><?= $message ?></p>
            <?php endforeach; ?>
        </div>
        
        <div class="mt-4">
            <a href="test_ajax.php" class="btn btn-primary">AJAX টেস্ট পেজে যান</a>
            <a href="fee_assign.php" class="btn btn-success">ফি এসাইন পেজে যান</a>
        </div>
    </div>
</body>
</html>
