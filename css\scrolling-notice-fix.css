/* Additional fixes for scrolling notice */

/* Force visibility */
.scrolling-notice-container {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.scrolling-notice {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    min-height: 30px !important;
}

.notice-content {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: absolute !important;
    left: 0 !important;
}

/* Ensure animation works */
@keyframes scrollText {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

/* Ensure text is visible */
.notice-content {
    color: #006A4E !important;
    text-shadow: 0 0 1px rgba(0,0,0,0.1);
}

/* Fix for some browsers */
.scrolling-notice-container:after {
    content: "";
    display: table;
    clear: both;
}
