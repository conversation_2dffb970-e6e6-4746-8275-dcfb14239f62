<?php
// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Get current page name
$current_page = basename($_SERVER['PHP_SELF']);
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title : 'অ্যাডমিন প্যানেল'; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Google Fonts - Hind Siliguri -->
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- Modern Styles CSS -->
    <link rel="stylesheet" href="css/modern-styles.css">

    <style>
        :root {
            --primary-color: #4361ee;
            --primary-light: #4895ef;
            --primary-dark: #3f37c9;
            --secondary-color: #4cc9f0;
            --accent-color: #f72585;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --danger-color: #f44336;
            --dark-color: #212529;
            --light-color: #f8f9fa;
        }

        body, html {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: #f5f7fa;
        }

        /* Modern Layout Styles */
        .admin-container {
            display: flex;
            min-height: 100vh;
        }

        /* Header */
        .admin-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            z-index: 1030;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            padding: 0 20px;
        }

        .header-brand {
            display: flex;
            align-items: center;
            gap: 15px;
            width: 280px;
        }

        .header-brand img {
            height: 40px;
        }

        .header-title {
            flex-grow: 1;
            text-align: center;
            font-weight: 600;
            font-size: 1.25rem;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        /* Sidebar */
        .admin-sidebar {
            width: 280px;
            background: white;
            position: fixed;
            top: 70px;
            left: 0;
            bottom: 0;
            overflow-y: auto;
            z-index: 1020;
            box-shadow: 2px 0 10px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        .sidebar-menu {
            padding: 20px 0;
        }

        .menu-section {
            margin-bottom: 20px;
        }

        .menu-section-title {
            padding: 10px 25px;
            font-size: 0.8rem;
            text-transform: uppercase;
            color: #6c757d;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .menu-items {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .menu-item {
            margin-bottom: 5px;
        }

        .menu-link {
            display: flex;
            align-items: center;
            padding: 12px 25px;
            color: #495057;
            text-decoration: none;
            border-left: 3px solid transparent;
            transition: all 0.2s ease;
        }

        .menu-link:hover {
            background-color: rgba(67, 97, 238, 0.05);
            color: var(--primary-color);
            border-left-color: var(--primary-light);
        }

        .menu-link.active {
            background-color: rgba(67, 97, 238, 0.1);
            color: var(--primary-color);
            border-left-color: var(--primary-color);
            font-weight: 500;
        }

        .menu-link i {
            margin-right: 15px;
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }

        /* Main Content */
        .admin-content {
            flex: 1;
            margin-left: 280px;
            margin-top: 70px;
            padding: 30px;
            transition: all 0.3s ease;
        }

        /* Responsive */
        @media (max-width: 992px) {
            .admin-sidebar {
                width: 70px;
            }

            .admin-sidebar:hover {
                width: 280px;
            }

            .admin-content {
                margin-left: 70px;
            }

            .menu-link span, .menu-section-title {
                display: none;
            }

            .admin-sidebar:hover .menu-link span,
            .admin-sidebar:hover .menu-section-title {
                display: inline;
            }

            .header-brand {
                width: 70px;
            }

            .header-brand span {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .admin-sidebar {
                transform: translateX(-100%);
                width: 280px;
            }

            .admin-content {
                margin-left: 0;
            }

            .menu-link span, .menu-section-title {
                display: inline;
            }

            .sidebar-open .admin-sidebar {
                transform: translateX(0);
            }

            .sidebar-toggle {
                display: block;
            }
        }

        /* Page Loader */
        #page-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease;
        }

        .loader-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>

    <?php if (isset($extra_css)): ?>
        <?php echo $extra_css; ?>
    <?php endif; ?>
</head>
<body>
    <!-- Page Loading Indicator -->
    <div id="page-loader">
        <div class="d-flex flex-column align-items-center">
            <div class="loader-spinner"></div>
            <div class="loader-text mt-3">পৃষ্ঠা লোড হচ্ছে...</div>
        </div>
    </div>

    <div class="admin-container">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-brand">
                <button class="sidebar-toggle btn btn-link text-white d-md-none" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <img src="../assets/images/logo.png" alt="Logo" class="d-none d-md-block">
                <span>শিক্ষা ব্যবস্থাপনা</span>
            </div>

            <div class="header-title">
                <?php echo isset($page_title) ? $page_title : 'অ্যাডমিন প্যানেল'; ?>
            </div>

            <div class="header-actions">
                <div class="dropdown">
                    <button class="btn btn-link text-white dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user-circle me-1"></i>
                        <span class="d-none d-md-inline">অ্যাডমিন</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i> প্রোফাইল</a></li>
                        <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i> সেটিংস</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="../includes/logout.inc.php"><i class="fas fa-sign-out-alt me-2"></i> লগআউট</a></li>
                    </ul>
                </div>
            </div>
        </header>

        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <nav class="sidebar-menu">
                <div class="menu-section">
                    <div class="menu-section-title">মূল মেনু</div>
                    <ul class="menu-items">
                        <li class="menu-item">
                            <a class="menu-link <?php echo ($current_page == 'dashboard.php') ? 'active' : ''; ?>" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>ড্যাশবোর্ড</span>
                            </a>
                        </li>
                        <li class="menu-item">
                            <a class="menu-link <?php echo ($current_page == 'students.php') ? 'active' : ''; ?>" href="students.php">
                                <i class="fas fa-user-graduate"></i>
                                <span>শিক্ষার্থী</span>
                            </a>
                        </li>
                        <li class="menu-item">
                            <a class="menu-link <?php echo ($current_page == 'teachers.php') ? 'active' : ''; ?>" href="teachers.php">
                                <i class="fas fa-chalkboard-teacher"></i>
                                <span>শিক্ষক</span>
                            </a>
                        </li>
                        <li class="menu-item">
                            <a class="menu-link <?php echo ($current_page == 'classes.php') ? 'active' : ''; ?>" href="classes.php">
                                <i class="fas fa-school"></i>
                                <span>শ্রেণী</span>
                            </a>
                        </li>
                        <li class="menu-item">
                            <a class="menu-link <?php echo ($current_page == 'subjects.php') ? 'active' : ''; ?>" href="subjects.php">
                                <i class="fas fa-book"></i>
                                <span>বিষয়</span>
                            </a>
                        </li>
                        <li class="menu-item">
                            <a class="menu-link <?php echo ($current_page == 'attendance.php') ? 'active' : ''; ?>" href="attendance.php">
                                <i class="fas fa-calendar-check"></i>
                                <span>উপস্থিতি</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="menu-section">
                    <div class="menu-section-title">পরীক্ষা ব্যবস্থাপনা</div>
                    <ul class="menu-items">
                        <li class="menu-item">
                            <a class="menu-link <?php echo ($current_page == 'manage_exams.php') ? 'active' : ''; ?>" href="manage_exams.php">
                                <i class="fas fa-file-alt"></i>
                                <span>পরীক্ষা</span>
                            </a>
                        </li>
                        <li class="menu-item">
                            <a class="menu-link <?php echo ($current_page == 'student_exam_attendance.php') ? 'active' : ''; ?>" href="student_exam_attendance.php">
                                <i class="fas fa-clipboard-check"></i>
                                <span>শিক্ষার্থী হাজিরা পত্র</span>
                            </a>
                        </li>
                        <li class="menu-item">
                            <a class="menu-link <?php echo ($current_page == 'subject_exam_pattern.php') ? 'active' : ''; ?>" href="subject_exam_pattern.php">
                                <i class="fas fa-sliders-h"></i>
                                <span>বিষয় পরীক্ষা প্যাটার্ন</span>
                            </a>
                        </li>
                        <li class="menu-item">
                            <a class="menu-link <?php echo ($current_page == 'subject_marks_distribution.php') ? 'active' : ''; ?>" href="subject_marks_distribution.php">
                                <i class="fas fa-chart-pie"></i>
                                <span>বিষয় মার্কস ডিস্ট্রিবিউশন</span>
                            </a>
                        </li>
                        <li class="menu-item">
                            <a class="menu-link <?php echo ($current_page == 'results.php') ? 'active' : ''; ?>" href="results.php">
                                <i class="fas fa-chart-bar"></i>
                                <span>ফলাফল</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-content">
            <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeInUp shadow-sm" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle fa-2x me-3"></i>
                        <div>
                            <strong>সফল!</strong> <?php echo $success_message; ?>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeInUp shadow-sm" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                        <div>
                            <strong>ত্রুটি!</strong> <?php echo $error_message; ?>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <!-- Page content will be inserted here -->
            <?php if (isset($content)): ?>
                <?php echo $content; ?>
            <?php endif; ?>
        </main>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Layout JS -->
    <script>
    // Fix for auto-reload issue
    if (window.history.replaceState) {
        window.history.replaceState(null, null, window.location.href);
    }

    // Page Loading Handler
    window.addEventListener('load', function() {
        // Hide loader and show content
        setTimeout(function() {
            const loader = document.getElementById('page-loader');
            if (loader) {
                loader.style.opacity = '0';
                document.body.classList.add('loaded');

                setTimeout(function() {
                    loader.style.display = 'none';
                }, 500);
            }
        }, 300);
    });

    document.addEventListener('DOMContentLoaded', function() {
        // Sidebar toggle for mobile
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', function() {
                document.querySelector('.admin-container').classList.toggle('sidebar-open');
            });
        }

        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl, {
                animation: true,
                delay: { show: 100, hide: 100 }
            });
        });
    });
    </script>

    <?php if (isset($extra_js)): ?>
        <?php echo $extra_js; ?>
    <?php endif; ?>
</body>
</html>
