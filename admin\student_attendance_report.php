<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get parameters
$student_id = isset($_GET['student_id']) ? intval($_GET['student_id']) : 0;
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01');
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$class_id = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;

// Get classes for dropdown
$classesQuery = "SELECT id, class_name FROM classes ORDER BY class_name";
$classesResult = $conn->query($classesQuery);
$classes = [];
if ($classesResult && $classesResult->num_rows > 0) {
    while ($row = $classesResult->fetch_assoc()) {
        $classes[$row['id']] = $row['class_name'];
    }
}

// Get students for dropdown based on class
$students = [];
if ($class_id > 0) {
    $studentsQuery = "SELECT id, student_id as student_code, first_name, last_name, roll_number 
                     FROM students WHERE class_id = ? ORDER BY roll_number, first_name";
    $stmt = $conn->prepare($studentsQuery);
    $stmt->bind_param("i", $class_id);
    $stmt->execute();
    $studentsResult = $stmt->get_result();
    
    while ($row = $studentsResult->fetch_assoc()) {
        $students[$row['id']] = $row;
    }
}

// Get student details and attendance data
$student_details = null;
$attendance_records = [];
$subjects_attendance = [];
$overall_stats = [];

if ($student_id > 0) {
    // Get student details
    $studentQuery = "SELECT s.*, c.class_name, d.department_name 
                    FROM students s 
                    LEFT JOIN classes c ON s.class_id = c.id 
                    LEFT JOIN departments d ON s.department_id = d.id 
                    WHERE s.id = ?";
    $stmt = $conn->prepare($studentQuery);
    $stmt->bind_param("i", $student_id);
    $stmt->execute();
    $student_details = $stmt->get_result()->fetch_assoc();
    
    if ($student_details) {
        // Get attendance records
        $attendanceQuery = "SELECT a.*, sub.subject_name 
                           FROM attendance a 
                           LEFT JOIN subjects sub ON a.subject_id = sub.id 
                           WHERE a.student_id = ? AND a.date BETWEEN ? AND ? 
                           ORDER BY a.date DESC, sub.subject_name";
        $stmt = $conn->prepare($attendanceQuery);
        $stmt->bind_param("iss", $student_id, $start_date, $end_date);
        $stmt->execute();
        $attendanceResult = $stmt->get_result();
        
        while ($row = $attendanceResult->fetch_assoc()) {
            $attendance_records[] = $row;
            
            // Group by subject for statistics
            $subject_name = $row['subject_name'] ?? 'সাধারণ উপস্থিতি';
            if (!isset($subjects_attendance[$subject_name])) {
                $subjects_attendance[$subject_name] = [
                    'present' => 0,
                    'absent' => 0,
                    'late' => 0,
                    'excused' => 0,
                    'total' => 0
                ];
            }
            
            $subjects_attendance[$subject_name][$row['status']]++;
            $subjects_attendance[$subject_name]['total']++;
        }
        
        // Calculate overall statistics
        $total_present = 0;
        $total_absent = 0;
        $total_late = 0;
        $total_excused = 0;
        $total_days = 0;
        
        foreach ($subjects_attendance as $subject => $stats) {
            $total_present += $stats['present'];
            $total_absent += $stats['absent'];
            $total_late += $stats['late'];
            $total_excused += $stats['excused'];
            $total_days += $stats['total'];
        }
        
        $overall_stats = [
            'present' => $total_present,
            'absent' => $total_absent,
            'late' => $total_late,
            'excused' => $total_excused,
            'total' => $total_days,
            'percentage' => $total_days > 0 ? round(($total_present + $total_late) / $total_days * 100, 2) : 0
        ];
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>শিক্ষার্থী উপস্থিতি রিপোর্ট - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .status-present { background-color: #d4edda; color: #155724; }
        .status-absent { background-color: #f8d7da; color: #721c24; }
        .status-late { background-color: #fff3cd; color: #856404; }
        .status-excused { background-color: #d1ecf1; color: #0c5460; }
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .stats-card {
            border-left: 4px solid #007bff;
        }
        .stats-good { color: #28a745; }
        .stats-warning { color: #ffc107; }
        .stats-danger { color: #dc3545; }
        
        @media print {
            .no-print { display: none !important; }
            .table { font-size: 12px; }
        }
    </style>
    <link rel="stylesheet" href="css/hind-siliguri.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include('includes/sidebar.php'); ?>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">শিক্ষার্থী উপস্থিতি রিপোর্ট</h1>
                    <div class="no-print">
                        <button onclick="window.print()" class="btn btn-primary">
                            <i class="fas fa-print me-1"></i> প্রিন্ট করুন
                        </button>
                    </div>
                </div>
                
                <!-- Filter Form -->
                <div class="card mb-4 no-print">
                    <div class="card-header">
                        <h5><i class="fas fa-filter me-2"></i>রিপোর্ট ফিল্টার</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="" class="row g-3">
                            <div class="col-md-3">
                                <label for="class_id" class="form-label">ক্লাস</label>
                                <select class="form-select" id="class_id" name="class_id" required onchange="loadStudents()">
                                    <option value="">ক্লাস নির্বাচন করুন</option>
                                    <?php foreach ($classes as $id => $name): ?>
                                    <option value="<?php echo $id; ?>" <?php echo ($class_id == $id) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($name); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label for="student_id" class="form-label">শিক্ষার্থী</label>
                                <select class="form-select" id="student_id" name="student_id" required>
                                    <option value="">শিক্ষার্থী নির্বাচন করুন</option>
                                    <?php foreach ($students as $id => $student): ?>
                                    <option value="<?php echo $id; ?>" <?php echo ($student_id == $id) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($student['roll_number'] . ' - ' . $student['first_name'] . ' ' . $student['last_name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label for="start_date" class="form-label">শুরুর তারিখ</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $start_date; ?>" required>
                            </div>
                            
                            <div class="col-md-3">
                                <label for="end_date" class="form-label">শেষ তারিখ</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $end_date; ?>" required>
                            </div>
                            
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i> রিপোর্ট দেখুন
                                </button>
                                <a href="attendance.php" class="btn btn-secondary ms-2">
                                    <i class="fas fa-arrow-left me-1"></i> ফিরে যান
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <?php if ($student_details): ?>
                <!-- Student Details -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-user-graduate me-2"></i>শিক্ষার্থীর বিবরণ</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>নাম:</strong> <?php echo htmlspecialchars($student_details['first_name'] . ' ' . $student_details['last_name']); ?></p>
                                <p><strong>রোল নম্বর:</strong> <?php echo htmlspecialchars($student_details['roll_number']); ?></p>
                                <p><strong>শিক্ষার্থী আইডি:</strong> <?php echo htmlspecialchars($student_details['student_id']); ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>ক্লাস:</strong> <?php echo htmlspecialchars($student_details['class_name']); ?></p>
                                <p><strong>বিভাগ:</strong> <?php echo htmlspecialchars($student_details['department_name'] ?? 'N/A'); ?></p>
                                <p><strong>রিপোর্ট সময়কাল:</strong> <?php echo date('d/m/Y', strtotime($start_date)) . ' - ' . date('d/m/Y', strtotime($end_date)); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Overall Statistics -->
                <div class="card mb-4 stats-card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-pie me-2"></i>সামগ্রিক পরিসংখ্যান</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-2">
                                <div class="card bg-success bg-opacity-10">
                                    <div class="card-body">
                                        <h4 class="text-success"><?php echo $overall_stats['present']; ?></h4>
                                        <p class="mb-0">উপস্থিত</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-danger bg-opacity-10">
                                    <div class="card-body">
                                        <h4 class="text-danger"><?php echo $overall_stats['absent']; ?></h4>
                                        <p class="mb-0">অনুপস্থিত</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-warning bg-opacity-10">
                                    <div class="card-body">
                                        <h4 class="text-warning"><?php echo $overall_stats['late']; ?></h4>
                                        <p class="mb-0">দেরি</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-info bg-opacity-10">
                                    <div class="card-body">
                                        <h4 class="text-info"><?php echo $overall_stats['excused']; ?></h4>
                                        <p class="mb-0">ছুটি</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-secondary bg-opacity-10">
                                    <div class="card-body">
                                        <h4 class="text-secondary"><?php echo $overall_stats['total']; ?></h4>
                                        <p class="mb-0">মোট দিন</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-primary bg-opacity-10">
                                    <div class="card-body">
                                        <?php 
                                        $percentage_class = '';
                                        if ($overall_stats['percentage'] >= 80) $percentage_class = 'stats-good';
                                        elseif ($overall_stats['percentage'] >= 60) $percentage_class = 'stats-warning';
                                        else $percentage_class = 'stats-danger';
                                        ?>
                                        <h4 class="<?php echo $percentage_class; ?>"><?php echo $overall_stats['percentage']; ?>%</h4>
                                        <p class="mb-0">উপস্থিতির হার</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Subject-wise Statistics -->
                <?php if (!empty($subjects_attendance)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-book me-2"></i>বিষয়ভিত্তিক পরিসংখ্যান</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>বিষয়</th>
                                        <th>উপস্থিত</th>
                                        <th>অনুপস্থিত</th>
                                        <th>দেরি</th>
                                        <th>ছুটি</th>
                                        <th>মোট</th>
                                        <th>উপস্থিতির হার</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($subjects_attendance as $subject => $stats): 
                                        $percentage = $stats['total'] > 0 ? round(($stats['present'] + $stats['late']) / $stats['total'] * 100, 2) : 0;
                                        $percentage_class = '';
                                        if ($percentage >= 80) $percentage_class = 'stats-good';
                                        elseif ($percentage >= 60) $percentage_class = 'stats-warning';
                                        else $percentage_class = 'stats-danger';
                                    ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($subject); ?></td>
                                        <td class="text-center"><?php echo $stats['present']; ?></td>
                                        <td class="text-center"><?php echo $stats['absent']; ?></td>
                                        <td class="text-center"><?php echo $stats['late']; ?></td>
                                        <td class="text-center"><?php echo $stats['excused']; ?></td>
                                        <td class="text-center"><?php echo $stats['total']; ?></td>
                                        <td class="text-center <?php echo $percentage_class; ?>">
                                            <strong><?php echo $percentage; ?>%</strong>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Detailed Attendance Records -->
                <?php if (!empty($attendance_records)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-list me-2"></i>বিস্তারিত উপস্থিতি রেকর্ড</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>তারিখ</th>
                                        <th>বিষয়</th>
                                        <th>অবস্থা</th>
                                        <th>মন্তব্য</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($attendance_records as $record): 
                                        $status_text = '';
                                        $status_class = '';
                                        switch ($record['status']) {
                                            case 'present':
                                                $status_text = 'উপস্থিত';
                                                $status_class = 'status-present';
                                                break;
                                            case 'absent':
                                                $status_text = 'অনুপস্থিত';
                                                $status_class = 'status-absent';
                                                break;
                                            case 'late':
                                                $status_text = 'দেরি';
                                                $status_class = 'status-late';
                                                break;
                                            case 'excused':
                                                $status_text = 'ছুটি';
                                                $status_class = 'status-excused';
                                                break;
                                        }
                                    ?>
                                    <tr>
                                        <td><?php echo date('d/m/Y', strtotime($record['date'])); ?></td>
                                        <td><?php echo htmlspecialchars($record['subject_name'] ?? 'সাধারণ উপস্থিতি'); ?></td>
                                        <td>
                                            <span class="status-badge <?php echo $status_class; ?>">
                                                <?php echo $status_text; ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($record['remarks'] ?? ''); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php elseif ($student_id > 0): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i> নির্বাচিত শিক্ষার্থীর কোন তথ্য পাওয়া যায়নি।
                </div>
                <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> রিপোর্ট দেখতে একটি ক্লাস এবং শিক্ষার্থী নির্বাচন করুন।
                </div>
                <?php endif; ?>
                
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function loadStudents() {
            const classId = document.getElementById('class_id').value;
            if (classId) {
                window.location.href = `student_attendance_report.php?class_id=${classId}`;
            }
        }
    </script>
</body>
</html>
