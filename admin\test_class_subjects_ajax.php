<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    echo '<div class="alert alert-danger">অননুমোদিত প্রবেশ!</div>';
    exit();
}

require_once '../includes/dbh.inc.php';
require_once 'class_subjects_helper.php';

$classId = isset($_POST['class_id']) ? intval($_POST['class_id']) : 0;
$departmentId = isset($_POST['department_id']) && !empty($_POST['department_id']) ? intval($_POST['department_id']) : null;

if (!$classId) {
    echo '<div class="alert alert-danger">ক্লাস আইডি প্রয়োজন!</div>';
    exit();
}

try {
    // Get class name
    $classQuery = "SELECT class_name FROM classes WHERE id = ?";
    $stmt = $conn->prepare($classQuery);
    $stmt->bind_param("i", $classId);
    $stmt->execute();
    $classResult = $stmt->get_result();
    $className = $classResult->num_rows > 0 ? $classResult->fetch_assoc()['class_name'] : 'অজানা ক্লাস';

    // Get department name if specified
    $departmentName = 'সকল বিভাগ';
    if ($departmentId) {
        $deptQuery = "SELECT department_name FROM departments WHERE id = ?";
        $stmt = $conn->prepare($deptQuery);
        $stmt->bind_param("i", $departmentId);
        $stmt->execute();
        $deptResult = $stmt->get_result();
        $departmentName = $deptResult->num_rows > 0 ? $deptResult->fetch_assoc()['department_name'] : 'অজানা বিভাগ';
    }

    // Validate configuration
    $validation = validateClassSubjectConfig($classId, $departmentId);

    echo '<div class="card">';
    echo '<div class="card-header bg-info text-white">';
    echo '<h5 class="mb-0">টেস্ট ফলাফল: ' . htmlspecialchars($className) . ' - ' . htmlspecialchars($departmentName) . '</h5>';
    echo '</div>';
    echo '<div class="card-body">';

    // Show validation status
    if ($validation['valid']) {
        echo '<div class="alert alert-success">';
        echo '<i class="fas fa-check-circle me-2"></i>' . $validation['message'];
        echo '<ul class="mb-0 mt-2">';
        echo '<li>মোট বিষয়: ' . $validation['total_subjects'] . '</li>';
        echo '<li>আবশ্যিক বিষয়: ' . $validation['required_subjects'] . '</li>';
        echo '<li>ঐচ্ছিক বিষয়: ' . $validation['optional_subjects'] . '</li>';
        echo '<li>চতুর্থ বিষয়: ' . $validation['fourth_subjects'] . '</li>';
        echo '</ul>';
        echo '</div>';
    } else {
        echo '<div class="alert alert-danger">';
        echo '<i class="fas fa-exclamation-triangle me-2"></i>' . $validation['message'];
        echo '</div>';
    }

    // Get and display subjects by type
    $requiredSubjects = getRequiredSubjects($classId, $departmentId);
    $optionalSubjects = getOptionalSubjects($classId, $departmentId);
    $fourthSubjects = getFourthSubjects($classId, $departmentId);

    echo '<div class="row">';

    // Required subjects
    echo '<div class="col-md-4">';
    echo '<h6 class="text-success"><i class="fas fa-star me-1"></i>আবশ্যিক বিষয় (' . count($requiredSubjects) . ')</h6>';
    if (!empty($requiredSubjects)) {
        echo '<div class="list-group list-group-flush">';
        foreach ($requiredSubjects as $subject) {
            echo '<div class="list-group-item">';
            echo '<strong>' . htmlspecialchars($subject['subject_code']) . '</strong><br>';
            echo '<small>' . htmlspecialchars($subject['subject_name']) . '</small>';
            echo '</div>';
        }
        echo '</div>';
    } else {
        echo '<p class="text-muted">কোন আবশ্যিক বিষয় নেই</p>';
    }
    echo '</div>';

    // Optional subjects
    echo '<div class="col-md-4">';
    echo '<h6 class="text-info"><i class="fas fa-circle me-1"></i>ঐচ্ছিক বিষয় (' . count($optionalSubjects) . ')</h6>';
    if (!empty($optionalSubjects)) {
        echo '<div class="list-group list-group-flush">';
        foreach ($optionalSubjects as $subject) {
            echo '<div class="list-group-item">';
            echo '<strong>' . htmlspecialchars($subject['subject_code']) . '</strong><br>';
            echo '<small>' . htmlspecialchars($subject['subject_name']) . '</small>';
            echo '</div>';
        }
        echo '</div>';
    } else {
        echo '<p class="text-muted">কোন ঐচ্ছিক বিষয় নেই</p>';
    }
    echo '</div>';

    // Fourth subjects
    echo '<div class="col-md-4">';
    echo '<h6 class="text-warning"><i class="fas fa-plus me-1"></i>চতুর্থ বিষয় (' . count($fourthSubjects) . ')</h6>';
    if (!empty($fourthSubjects)) {
        echo '<div class="list-group list-group-flush">';
        foreach ($fourthSubjects as $subject) {
            echo '<div class="list-group-item">';
            echo '<strong>' . htmlspecialchars($subject['subject_code']) . '</strong><br>';
            echo '<small>' . htmlspecialchars($subject['subject_name']) . '</small>';
            echo '</div>';
        }
        echo '</div>';
    } else {
        echo '<p class="text-muted">কোন চতুর্থ বিষয় নেই</p>';
    }
    echo '</div>';

    echo '</div>'; // End row

    // Show how this would be used in exam results
    echo '<hr>';
    echo '<div class="alert alert-info">';
    echo '<h6><i class="fas fa-lightbulb me-2"></i>পরীক্ষার ফলাফলে ব্যবহার:</h6>';
    echo '<p class="mb-2">এই কনফিগারেশন অনুযায়ী পরীক্ষার ফলাফল তৈরি করার সময়:</p>';
    echo '<ul class="mb-0">';
    echo '<li>শুধুমাত্র উপরের বিষয়গুলিতে নম্বর দেওয়া যাবে</li>';
    echo '<li>আবশ্যিক বিষয়গুলি অবশ্যই থাকতে হবে</li>';
    echo '<li>চতুর্থ বিষয়ের নম্বর আলাদাভাবে গণনা করা হবে</li>';
    echo '<li>গ্রেড এবং পজিশন এই বিষয়গুলির ভিত্তিতে নির্ধারিত হবে</li>';
    echo '</ul>';
    echo '</div>';

    echo '</div>'; // End card-body
    echo '</div>'; // End card

} catch (Exception $e) {
    echo '<div class="alert alert-danger">';
    echo '<i class="fas fa-exclamation-circle me-2"></i>ডেটা লোড করতে সমস্যা হয়েছে: ' . htmlspecialchars($e->getMessage());
    echo '</div>';
}
?>
