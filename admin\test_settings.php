<?php
session_start();

// Force admin session for testing
$_SESSION['userId'] = 1;
$_SESSION['userType'] = 'admin';

// Include the database connection
require_once '../includes/dbh.inc.php';

// Test database connection
echo "Database connection test: ";
if ($conn) {
    echo "SUCCESS<br>";
} else {
    echo "FAILED<br>";
    die("Database connection failed");
}

// Check if the school_settings table exists
echo "Checking for school_settings table: ";
$checkTableQuery = "SHOW TABLES LIKE 'school_settings'";
$tableResult = $conn->query($checkTableQuery);

if ($tableResult && $tableResult->num_rows > 0) {
    echo "EXISTS<br>";
    
    // Check if logo_path column exists
    echo "Checking for logo_path column: ";
    $checkLogoColumn = "SHOW COLUMNS FROM school_settings LIKE 'logo_path'";
    $logoColumnResult = $conn->query($checkLogoColumn);
    
    if ($logoColumnResult->num_rows == 0) {
        echo "MISSING - Adding logo_path column...<br>";
        $addColumnQuery = "ALTER TABLE school_settings ADD COLUMN logo_path VARCHAR(255) DEFAULT NULL";
        if ($conn->query($addColumnQuery)) {
            echo "logo_path column added successfully.<br>";
        } else {
            echo "Error adding logo_path column: " . $conn->error . "<br>";
        }
    } else {
        echo "EXISTS<br>";
    }
    
    // Check if there are any records
    $checkRecordsQuery = "SELECT COUNT(*) as count FROM school_settings";
    $recordsResult = $conn->query($checkRecordsQuery);
    $row = $recordsResult->fetch_assoc();
    
    echo "Records in table: " . $row['count'] . "<br>";
    
    if ($row['count'] > 0) {
        echo "First record data:<br>";
        $dataQuery = "SELECT * FROM school_settings LIMIT 1";
        $dataResult = $conn->query($dataQuery);
        $settings = $dataResult->fetch_assoc();
        
        echo "School name: " . $settings['school_name'] . "<br>";
        echo "School address: " . $settings['school_address'] . "<br>";
        echo "School phone: " . $settings['school_phone'] . "<br>";
        echo "School email: " . $settings['school_email'] . "<br>";
        echo "Logo path: " . ($settings['logo_path'] ? $settings['logo_path'] : 'Not set') . "<br>";
        
        if (!empty($settings['logo_path']) && file_exists('../' . $settings['logo_path'])) {
            echo "Logo status: EXISTS<br>";
            echo "<img src='../" . $settings['logo_path'] . "' style='max-width: 200px; max-height: 200px;'><br>";
        } else {
            echo "Logo status: NOT FOUND OR NOT SET<br>";
        }
    } else {
        echo "No records in table. Creating default...<br>";
        $insertQuery = "INSERT INTO school_settings (school_name, school_address, school_phone, school_email) 
                     VALUES ('Default School Name', 'Default School Address', '************', '<EMAIL>')";
        
        if ($conn->query($insertQuery)) {
            echo "Default settings created successfully.<br>";
        } else {
            echo "Error creating default settings: " . $conn->error . "<br>";
        }
    }
} else {
    echo "DOES NOT EXIST<br>";
    echo "Running create_school_settings_table.php...<br>";
    
    // Create the school_settings table
    $query = "CREATE TABLE IF NOT EXISTS school_settings (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        school_name VARCHAR(255) NOT NULL,
        school_address TEXT,
        school_phone VARCHAR(50),
        school_email VARCHAR(100),
        logo_path VARCHAR(255) DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";

    if ($conn->query($query)) {
        echo "school_settings table created successfully.<br>";
        
        // Insert default record
        $insertQuery = "INSERT INTO school_settings (school_name, school_address, school_phone, school_email) 
                     VALUES ('Default School Name', 'Default School Address', '************', '<EMAIL>')";
        
        if ($conn->query($insertQuery)) {
            echo "Default settings created successfully.<br>";
        } else {
            echo "Error creating default settings: " . $conn->error . "<br>";
        }
    } else {
        echo "Error creating school_settings table: " . $conn->error . "<br>";
    }
}

// Create uploads directory if it doesn't exist
$uploadDir = '../uploads/logos/';
if (!file_exists($uploadDir)) {
    echo "Creating logo uploads directory...<br>";
    if (mkdir($uploadDir, 0777, true)) {
        echo "Upload directory created successfully.<br>";
    } else {
        echo "Error creating upload directory.<br>";
    }
} else {
    echo "Upload directory already exists.<br>";
}

echo "<hr>";
echo "You can now try to access the <a href='settings.php'>settings page</a>.";
?> 