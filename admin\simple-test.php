<?php
// Simple test file to check basic functionality
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Simple Test Page</h1>";
echo "<p>PHP is working!</p>";
echo "<p>PHP Version: " . PHP_VERSION . "</p>";
echo "<p>Current Time: " . date('Y-m-d H:i:s') . "</p>";

// Test session
session_start();
echo "<p>Session started successfully</p>";

// Test database connection
try {
    require_once '../includes/dbh.inc.php';
    echo "<p>✅ Database connection successful</p>";
    
    // Test simple query
    $result = $conn->query("SELECT 1 as test");
    if ($result) {
        echo "<p>✅ Database query successful</p>";
    } else {
        echo "<p>❌ Database query failed: " . $conn->error . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Database connection failed: " . $e->getMessage() . "</p>";
}

// Test if user session exists
if (isset($_SESSION['userId'])) {
    echo "<p>✅ User logged in: ID " . $_SESSION['userId'] . ", Type: " . $_SESSION['userType'] . "</p>";
} else {
    echo "<p>⚠️ User not logged in</p>";
}

echo "<h2>🔗 Navigation</h2>";
echo "<a href='../login.php'>Login</a> | ";
echo "<a href='dashboard.php'>Dashboard</a> | ";
echo "<a href='fee_management.php'>Fee Management</a>";

?>

<!DOCTYPE html>
<html>
<head>
    <title>Simple Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        p { margin: 10px 0; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <h2>📊 System Status</h2>
    <ul>
        <li>Server: <?php echo $_SERVER['SERVER_SOFTWARE']; ?></li>
        <li>Document Root: <?php echo $_SERVER['DOCUMENT_ROOT']; ?></li>
        <li>Script Path: <?php echo __FILE__; ?></li>
        <li>Working Directory: <?php echo getcwd(); ?></li>
    </ul>
    
    <h2>🧪 Quick Tests</h2>
    <form method="post" action="">
        <p>
            <input type="text" name="test_input" placeholder="Test input" value="Hello World">
            <button type="submit" name="test_submit">Test Form Submit</button>
        </p>
    </form>
    
    <?php
    if (isset($_POST['test_submit'])) {
        echo "<p class='success'>✅ Form submitted successfully!</p>";
        echo "<p>Input value: " . htmlspecialchars($_POST['test_input']) . "</p>";
    }
    ?>
    
    <h2>🔍 Debug Information</h2>
    <details>
        <summary>Click to show $_SERVER array</summary>
        <pre><?php print_r($_SERVER); ?></pre>
    </details>
    
    <details>
        <summary>Click to show $_SESSION array</summary>
        <pre><?php print_r($_SESSION); ?></pre>
    </details>
</body>
</html>
