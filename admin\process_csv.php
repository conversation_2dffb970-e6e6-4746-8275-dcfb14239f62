<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if we have a temp file from csv_upload.php
if (!isset($_SESSION['csv_temp_file']) || !file_exists($_SESSION['csv_temp_file'])) {
    $_SESSION['csv_error'] = 'CSV ফাইল পাওয়া যায়নি। আবার চেষ্টা করুন।';
    header("Location: csv_upload.php");
    exit();
}

$temp_file = $_SESSION['csv_temp_file'];
$filename = $_SESSION['csv_filename'] ?? 'unknown.csv';

// Helper function to fix date formats
function fixDateFormat($date) {
    if (empty($date)) return $date;
    
    $date = trim($date);
    
    // Handle Excel date formats like "15-Jan-2000", "20-Mar-2001"
    if (preg_match('/^(\d{1,2})-([A-Za-z]{3})-(\d{2,4})$/', $date, $matches)) {
        $day = $matches[1];
        $month = $matches[2];
        $year = $matches[3];
        
        $months = [
            'Jan' => '01', 'Feb' => '02', 'Mar' => '03', 'Apr' => '04',
            'May' => '05', 'Jun' => '06', 'Jul' => '07', 'Aug' => '08',
            'Sep' => '09', 'Oct' => '10', 'Nov' => '11', 'Dec' => '12'
        ];
        
        if (isset($months[$month])) {
            if (strlen($year) == 2) {
                $year = (intval($year) < 50) ? '20' . $year : '19' . $year;
            }
            return sprintf('%04d-%02d-%02d', $year, $months[$month], $day);
        }
    }
    
    $formats = [
        'd-m-y', 'd-m-Y', 'd/m/y', 'd/m/Y',
        'm-d-y', 'm-d-Y', 'm/d/y', 'm/d/Y',
        'Y-m-d', 'Y/m/d'
    ];
    
    foreach ($formats as $format) {
        $dateObj = DateTime::createFromFormat($format, $date);
        if ($dateObj !== false) {
            $year = $dateObj->format('Y');
            if ($year < 1950) {
                $dateObj->modify('+100 years');
            }
            return $dateObj->format('Y-m-d');
        }
    }
    
    return $date;
}

// Process CSV file
$handle = fopen($temp_file, 'r');

if ($handle !== FALSE) {
    $headers = fgetcsv($handle); // Read headers
    $success_count = 0;
    $error_count = 0;
    $duplicate_count = 0;
    $errors = [];
    $duplicates = [];
    
    // Clean BOM from first header if present
    if (!empty($headers[0])) {
        $headers[0] = preg_replace('/^\xEF\xBB\xBF/', '', $headers[0]);
        $headers[0] = trim($headers[0], "\xEF\xBB\xBF\x00..\x20");
    }
    
    $conn->begin_transaction();
    
    try {
        $row_number = 1;
        $total_rows_processed = 0;
        
        while (($data = fgetcsv($handle)) !== FALSE) {
            $row_number++;
            $total_rows_processed++;
            
            // Skip empty rows
            if (empty(array_filter($data))) {
                continue;
            }
            
            // Ensure minimum data length
            if (count($data) < 11) {
                $error_count++;
                $errors[] = "সারি $row_number: অপর্যাপ্ত ডেটা (কমপক্ষে ১১টি কলাম প্রয়োজন, পাওয়া গেছে " . count($data) . "টি)";
                continue;
            }
            
            // Map CSV data to variables
            $student_id = !empty(trim($data[0])) ? trim($data[0]) : '';
            $roll_number = !empty(trim($data[1])) ? trim($data[1]) : null;
            $first_name = !empty(trim($data[2])) ? trim($data[2]) : '';
            $last_name = !empty(trim($data[3])) ? trim($data[3]) : '';
            $email = !empty(trim($data[4])) ? trim($data[4]) : null;
            $phone = !empty(trim($data[5])) ? trim($data[5]) : '';
            $address = !empty(trim($data[6])) ? trim($data[6]) : '';
            $dob = !empty(trim($data[7])) ? trim($data[7]) : '';
            $gender = !empty(trim($data[8])) ? trim($data[8]) : '';
            $batch = !empty(trim($data[9])) ? trim($data[9]) : '';
            $admission_date = !empty(trim($data[10])) ? trim($data[10]) : '';
            $department_id = !empty(trim($data[11])) ? intval($data[11]) : 0;
            $class_id = !empty(trim($data[12])) ? intval($data[12]) : null;
            $session_id = !empty(trim($data[13])) ? intval($data[13]) : null;
            
            // Auto-fix phone number
            if (!empty($phone)) {
                $phone = preg_replace('/[^0-9]/', '', $phone);
                if (!preg_match('/^01/', $phone) && strlen($phone) == 9) {
                    $phone = '01' . $phone;
                }
                if (preg_match('/^1[3-9]\d{8}$/', $phone)) {
                    $phone = '0' . $phone;
                }
            }
            
            // Auto-fix date formats
            if (!empty($dob)) {
                $dob = fixDateFormat($dob);
            }
            if (!empty($admission_date)) {
                $admission_date = fixDateFormat($admission_date);
            }
            
            // Guardian and parent info (optional) - adjusted indices after removing username/password
            $guardian_name = (isset($data[14]) && !empty(trim($data[14]))) ? trim($data[14]) : null;
            $guardian_relation = (isset($data[15]) && !empty(trim($data[15]))) ? trim($data[15]) : null;
            $guardian_phone = (isset($data[16]) && !empty(trim($data[16]))) ? trim($data[16]) : null;
            $guardian_email = (isset($data[17]) && !empty(trim($data[17]))) ? trim($data[17]) : null;
            $guardian_address = (isset($data[18]) && !empty(trim($data[18]))) ? trim($data[18]) : null;
            $guardian_occupation = (isset($data[19]) && !empty(trim($data[19]))) ? trim($data[19]) : null;
            $father_name = (isset($data[20]) && !empty(trim($data[20]))) ? trim($data[20]) : null;
            $father_phone = (isset($data[21]) && !empty(trim($data[21]))) ? trim($data[21]) : null;
            $father_email = (isset($data[22]) && !empty(trim($data[22]))) ? trim($data[22]) : null;
            $father_occupation = (isset($data[23]) && !empty(trim($data[23]))) ? trim($data[23]) : null;
            $father_income = (isset($data[24]) && !empty(trim($data[24]))) ? trim($data[24]) : null;
            $mother_name = (isset($data[25]) && !empty(trim($data[25]))) ? trim($data[25]) : null;
            $mother_phone = (isset($data[26]) && !empty(trim($data[26]))) ? trim($data[26]) : null;
            $mother_email = (isset($data[27]) && !empty(trim($data[27]))) ? trim($data[27]) : null;
            $mother_occupation = (isset($data[28]) && !empty(trim($data[28]))) ? trim($data[28]) : null;
            $mother_income = (isset($data[29]) && !empty(trim($data[29]))) ? trim($data[29]) : null;
            
            // Validate mandatory fields (excluding username/password for now)
            $mandatory_fields = [
                'student_id' => $student_id,
                'first_name' => $first_name,
                'last_name' => $last_name,
                'phone' => $phone,
                'address' => $address,
                'dob' => $dob,
                'gender' => $gender,
                'batch' => $batch,
                'admission_date' => $admission_date,
                'department_id' => $department_id
            ];
            
            $missing_fields = [];
            foreach ($mandatory_fields as $field_name => $field_value) {
                if (empty($field_value)) {
                    $missing_fields[] = $field_name;
                }
            }
            
            if (!empty($missing_fields)) {
                $error_count++;
                $errors[] = "সারি $row_number: প্রয়োজনীয় ফিল্ড অনুপস্থিত (" . implode(', ', $missing_fields) . ") - Student ID: '$student_id'";
                continue;
            }
            
            // Additional validation for department_id
            if ($department_id <= 0) {
                $error_count++;
                $errors[] = "সারি $row_number: বৈধ department_id প্রয়োজন (পাওয়া গেছে: '$department_id')";
                continue;
            }
            
            // Check if student ID already exists (DUPLICATE CHECK)
            $check_id = $conn->prepare("SELECT id FROM students WHERE student_id = ?");
            $check_id->bind_param("s", $student_id);
            $check_id->execute();
            if ($check_id->get_result()->num_rows > 0) {
                $duplicate_count++;
                $duplicates[] = "সারি $row_number: শিক্ষার্থী আইডি '$student_id' ইতিমধ্যে বিদ্যমান (স্কিপ করা হয়েছে)";
                continue;
            }
            
            // Skip username check for now (users table integration later)
            
            // Insert student data (only existing columns)
            $columns = ['student_id', 'roll_number', 'first_name', 'last_name', 'email', 'phone', 'address', 'dob', 'gender', 'batch', 'admission_date', 'department_id', 'class_id', 'session_id', 'guardian_name', 'guardian_relation', 'guardian_phone', 'guardian_email', 'guardian_address', 'guardian_occupation', 'father_name', 'father_phone', 'father_email', 'father_occupation', 'father_income', 'mother_name', 'mother_phone', 'mother_email', 'mother_occupation', 'mother_income'];

            $values = [$student_id, $roll_number, $first_name, $last_name, $email, $phone, $address, $dob, $gender, $batch, $admission_date, $department_id, $class_id, $session_id, $guardian_name, $guardian_relation, $guardian_phone, $guardian_email, $guardian_address, $guardian_occupation, $father_name, $father_phone, $father_email, $father_occupation, $father_income, $mother_name, $mother_phone, $mother_email, $mother_occupation, $mother_income];
            
            $placeholders = str_repeat('?,', count($values) - 1) . '?';
            $insertQuery = "INSERT INTO students (" . implode(',', $columns) . ") VALUES ($placeholders)";
            
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param(str_repeat('s', count($values)), ...$values);
            
            if ($stmt->execute()) {
                $success_count++;
            } else {
                $error_count++;
                $errors[] = "সারি $row_number: ডেটাবেস ত্রুটি - " . $conn->error;
            }
        }
        
        $conn->commit();
        
        // Prepare success message
        $message_parts = [];
        if ($success_count > 0) {
            $message_parts[] = "$success_count জন শিক্ষার্থী সফলভাবে যোগ করা হয়েছে";
        }
        if ($duplicate_count > 0) {
            $message_parts[] = "$duplicate_count টি ডুপ্লিকেট ডেটা স্কিপ করা হয়েছে";
        }
        if ($error_count > 0) {
            $message_parts[] = "$error_count টি ত্রুটি হয়েছে";
        }
        
        if (!empty($message_parts)) {
            $_SESSION['csv_success'] = implode(", ", $message_parts) . "। (মোট $total_rows_processed টি সারি প্রসেস করা হয়েছে)";
        } else {
            if ($total_rows_processed == 0) {
                $_SESSION['csv_error'] = "CSV ফাইলে কোন ডেটা পাওয়া যায়নি।";
            } else {
                $_SESSION['csv_error'] = "CSV ফাইল প্রসেস করা হয়েছে কিন্তু কোন বৈধ ডেটা পাওয়া যায়নি।";
            }
        }
        
        // Prepare error message
        $all_messages = array_merge($duplicates, $errors);
        if (!empty($all_messages)) {
            $error_details = "বিস্তারিত:<br>" . implode("<br>", array_slice($all_messages, 0, 15));
            if (count($all_messages) > 15) {
                $error_details .= "<br>... এবং আরও " . (count($all_messages) - 15) . " টি বার্তা";
            }
            $_SESSION['csv_error'] = ($_SESSION['csv_error'] ?? '') . '<br>' . $error_details;
        }
        
    } catch (Exception $e) {
        $conn->rollback();
        $_SESSION['csv_error'] = 'CSV আপলোড করতে সমস্যা হয়েছে: ' . $e->getMessage();
    }
    
    fclose($handle);
} else {
    $_SESSION['csv_error'] = 'CSV ফাইল পড়তে সমস্যা হয়েছে।';
}

// Clean up
unlink($temp_file);
unset($_SESSION['csv_temp_file']);
unset($_SESSION['csv_filename']);

// Redirect back to csv_upload.php
header("Location: csv_upload.php");
exit();
?>
