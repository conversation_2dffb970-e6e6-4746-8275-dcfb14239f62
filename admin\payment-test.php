<?php
// Payment test page
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Bypass login for testing
if (isset($_GET['bypass']) && $_GET['bypass'] === 'test') {
    $_SESSION['userId'] = 999;
    $_SESSION['userType'] = 'admin';
    $_SESSION['username'] = 'test_admin';
}

require_once '../includes/dbh.inc.php';

// Check if user is logged in
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    echo "<!DOCTYPE html><html><head><title>Login Required</title></head><body>";
    echo "<h1>Login Required</h1>";
    echo "<p><a href='../login.php'>Login Here</a> OR <a href='?bypass=test'>Bypass for Testing</a></p>";
    echo "</body></html>";
    exit();
}

echo "<!DOCTYPE html><html><head><title>Payment Test</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head><body><div class='container mt-4'>";

echo "<h1>🧪 Payment System Test</h1>";

// Check if payments table exists
echo "<h2>📊 Database Check</h2>";
$tablesCheck = ['fees', 'payments', 'students'];
foreach ($tablesCheck as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result && $result->num_rows > 0) {
        echo "<p style='color: green;'>✅ Table '$table' exists</p>";
        
        // Count records
        $countResult = $conn->query("SELECT COUNT(*) as count FROM $table");
        if ($countResult) {
            $count = $countResult->fetch_assoc()['count'];
            echo "<p>📋 Records in '$table': $count</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Table '$table' missing</p>";
        
        // Create payments table if missing
        if ($table === 'payments') {
            echo "<p>🔧 Creating payments table...</p>";
            $createPaymentsTable = "CREATE TABLE IF NOT EXISTS payments (
                id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
                fee_id INT(11) NOT NULL,
                student_id INT(11) NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                payment_date DATE NOT NULL,
                payment_method VARCHAR(50) DEFAULT 'cash',
                receipt_number VARCHAR(100),
                transaction_id VARCHAR(100),
                reference_number VARCHAR(100),
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            
            if ($conn->query($createPaymentsTable)) {
                echo "<p style='color: green;'>✅ Payments table created successfully</p>";
            } else {
                echo "<p style='color: red;'>❌ Error creating payments table: " . $conn->error . "</p>";
            }
        }
    }
}

// Handle payment submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_payment'])) {
    echo "<h2>🔄 Processing Payment...</h2>";
    
    $feeId = intval($_POST['fee_id']);
    $paymentAmount = floatval($_POST['payment_amount']);
    $paymentMethod = $_POST['payment_method'];
    $paymentDate = $_POST['payment_date'];
    $receiptNumber = $_POST['receipt_number'];
    $notes = $_POST['notes'];
    
    echo "<div style='background: #f0f8ff; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3>📊 Payment Data:</h3>";
    echo "<ul>";
    echo "<li><strong>Fee ID:</strong> $feeId</li>";
    echo "<li><strong>Amount:</strong> ৳$paymentAmount</li>";
    echo "<li><strong>Method:</strong> $paymentMethod</li>";
    echo "<li><strong>Date:</strong> $paymentDate</li>";
    echo "<li><strong>Receipt:</strong> $receiptNumber</li>";
    echo "<li><strong>Notes:</strong> $notes</li>";
    echo "</ul>";
    echo "</div>";
    
    if ($feeId > 0 && $paymentAmount > 0) {
        try {
            // Get fee details
            $feeQuery = "SELECT f.*, CONCAT(s.first_name, ' ', s.last_name) as student_name 
                        FROM fees f 
                        JOIN students s ON f.student_id = s.id 
                        WHERE f.id = $feeId";
            echo "<p><strong>Fee Query:</strong> $feeQuery</p>";
            
            $feeResult = $conn->query($feeQuery);
            
            if ($feeResult && $feeResult->num_rows > 0) {
                $fee = $feeResult->fetch_assoc();
                echo "<p style='color: green;'>✅ Fee found: " . $fee['student_name'] . " - " . $fee['fee_type'] . "</p>";
                
                $dueAmount = $fee['amount'] - $fee['paid'];
                echo "<p>💰 Due amount: ৳$dueAmount</p>";
                
                if ($paymentAmount <= $dueAmount) {
                    // Insert payment
                    if (empty($receiptNumber)) {
                        $receiptNumber = 'RCP-' . date('Ymd') . '-' . $feeId . '-' . time();
                    }
                    
                    $paymentQuery = "INSERT INTO payments (fee_id, student_id, amount, payment_date, payment_method, receipt_number, notes) 
                                    VALUES ($feeId, {$fee['student_id']}, $paymentAmount, '$paymentDate', '$paymentMethod', '$receiptNumber', '$notes')";
                    echo "<p><strong>Payment Insert Query:</strong> $paymentQuery</p>";
                    
                    if ($conn->query($paymentQuery)) {
                        $paymentId = $conn->insert_id;
                        echo "<p style='color: green;'>✅ Payment inserted with ID: $paymentId</p>";
                        
                        // Update fee
                        $newPaidAmount = $fee['paid'] + $paymentAmount;
                        $newStatus = ($newPaidAmount >= $fee['amount']) ? 'paid' : 'partial';
                        
                        $updateQuery = "UPDATE fees SET paid = $newPaidAmount, payment_status = '$newStatus' WHERE id = $feeId";
                        echo "<p><strong>Fee Update Query:</strong> $updateQuery</p>";
                        
                        if ($conn->query($updateQuery)) {
                            echo "<p style='color: green;'>✅ Fee updated successfully</p>";
                            echo "<p><strong>New Status:</strong> $newStatus</p>";
                            echo "<p><strong>New Paid Amount:</strong> ৳$newPaidAmount</p>";
                        } else {
                            echo "<p style='color: red;'>❌ Fee update failed: " . $conn->error . "</p>";
                        }
                    } else {
                        echo "<p style='color: red;'>❌ Payment insert failed: " . $conn->error . "</p>";
                    }
                } else {
                    echo "<p style='color: red;'>❌ Payment amount exceeds due amount</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ Fee not found</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Invalid fee ID or payment amount</p>";
    }
}

// Show unpaid fees for testing
echo "<h2>💳 Test Payment</h2>";

// First check all fees
$allFeesQuery = "SELECT f.*, CONCAT(s.first_name, ' ', s.last_name) as student_name, c.class_name
                FROM fees f
                JOIN students s ON f.student_id = s.id
                LEFT JOIN classes c ON s.class_id = c.id
                ORDER BY f.id DESC LIMIT 10";
echo "<h3>🔍 All Fees Debug:</h3>";
echo "<p><strong>Query:</strong> $allFeesQuery</p>";

$allFeesResult = $conn->query($allFeesQuery);
if ($allFeesResult && $allFeesResult->num_rows > 0) {
    echo "<table class='table table-sm'>";
    echo "<tr><th>ID</th><th>Student</th><th>Fee Type</th><th>Amount</th><th>Paid</th><th>Status</th><th>Due</th></tr>";
    while ($fee = $allFeesResult->fetch_assoc()) {
        $dueAmount = $fee['amount'] - $fee['paid'];
        $statusColor = ($fee['payment_status'] === 'paid') ? 'green' : (($fee['payment_status'] === 'partial') ? 'orange' : 'red');
        echo "<tr>";
        echo "<td>{$fee['id']}</td>";
        echo "<td>{$fee['student_name']}</td>";
        echo "<td>{$fee['fee_type']}</td>";
        echo "<td>৳{$fee['amount']}</td>";
        echo "<td>৳{$fee['paid']}</td>";
        echo "<td style='color: $statusColor'>{$fee['payment_status']}</td>";
        echo "<td>৳$dueAmount</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ No fees found at all!</p>";
}

// Now check unpaid fees
$unpaidFeesQuery = "SELECT f.*, CONCAT(s.first_name, ' ', s.last_name) as student_name, c.class_name
                   FROM fees f
                   JOIN students s ON f.student_id = s.id
                   LEFT JOIN classes c ON s.class_id = c.id
                   WHERE f.payment_status != 'paid' OR f.payment_status IS NULL
                   ORDER BY f.id DESC LIMIT 10";
echo "<h3>💰 Unpaid Fees Query:</h3>";
echo "<p><strong>Query:</strong> $unpaidFeesQuery</p>";

$unpaidFeesResult = $conn->query($unpaidFeesQuery);

echo "<p><strong>Unpaid fees found:</strong> " . ($unpaidFeesResult ? $unpaidFeesResult->num_rows : 0) . "</p>";

if ($unpaidFeesResult && $unpaidFeesResult->num_rows > 0) {
    echo "<h3>📝 Payment Form:</h3>";
    echo "<form method='post'>";
    echo "<div class='row mb-3'>";
    echo "<div class='col-md-6'>";
    echo "<label class='form-label'>Select Fee to Pay:</label>";
    echo "<select name='fee_id' class='form-control' required>";
    echo "<option value=''>Choose Fee</option>";

    while ($fee = $unpaidFeesResult->fetch_assoc()) {
        $dueAmount = $fee['amount'] - $fee['paid'];
        echo "<option value='{$fee['id']}' data-due='$dueAmount'>";
        echo "{$fee['student_name']} - {$fee['fee_type']} - Due: ৳$dueAmount";
        echo "</option>";
    }
    echo "</select>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='row mb-3'>";
    echo "<div class='col-md-3'>";
    echo "<label class='form-label'>Payment Amount:</label>";
    echo "<input type='number' name='payment_amount' class='form-control' step='0.01' required>";
    echo "</div>";
    echo "<div class='col-md-3'>";
    echo "<label class='form-label'>Payment Method:</label>";
    echo "<select name='payment_method' class='form-control'>";
    echo "<option value='cash'>Cash</option>";
    echo "<option value='bank'>Bank</option>";
    echo "<option value='mobile_banking'>Mobile Banking</option>";
    echo "</select>";
    echo "</div>";
    echo "<div class='col-md-3'>";
    echo "<label class='form-label'>Payment Date:</label>";
    echo "<input type='date' name='payment_date' class='form-control' value='" . date('Y-m-d') . "' required>";
    echo "</div>";
    echo "<div class='col-md-3'>";
    echo "<label class='form-label'>Receipt Number:</label>";
    echo "<input type='text' name='receipt_number' class='form-control' placeholder='Auto-generated'>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='mb-3'>";
    echo "<label class='form-label'>Notes:</label>";
    echo "<textarea name='notes' class='form-control' rows='2'></textarea>";
    echo "</div>";
    
    echo "<button type='submit' name='test_payment' class='btn btn-success'>Test Payment</button>";
    echo "</form>";
} else {
    echo "<div class='alert alert-warning'>";
    echo "<h4>⚠️ No unpaid fees found for testing</h4>";
    echo "<p>This could mean:</p>";
    echo "<ul>";
    echo "<li>All fees are already paid</li>";
    echo "<li>payment_status column has unexpected values</li>";
    echo "<li>No fees exist in the database</li>";
    echo "</ul>";
    echo "</div>";

    // Show a manual payment form for testing
    echo "<h3>🧪 Manual Payment Test</h3>";
    echo "<p>Enter fee ID manually for testing:</p>";
    echo "<form method='post'>";
    echo "<div class='row mb-3'>";
    echo "<div class='col-md-2'>";
    echo "<label class='form-label'>Fee ID:</label>";
    echo "<input type='number' name='fee_id' class='form-control' min='1' required>";
    echo "</div>";
    echo "<div class='col-md-3'>";
    echo "<label class='form-label'>Payment Amount:</label>";
    echo "<input type='number' name='payment_amount' class='form-control' step='0.01' value='100' required>";
    echo "</div>";
    echo "<div class='col-md-3'>";
    echo "<label class='form-label'>Payment Method:</label>";
    echo "<select name='payment_method' class='form-control'>";
    echo "<option value='cash'>Cash</option>";
    echo "<option value='bank'>Bank</option>";
    echo "</select>";
    echo "</div>";
    echo "<div class='col-md-3'>";
    echo "<label class='form-label'>Payment Date:</label>";
    echo "<input type='date' name='payment_date' class='form-control' value='" . date('Y-m-d') . "' required>";
    echo "</div>";
    echo "</div>";
    echo "<div class='mb-3'>";
    echo "<input type='text' name='receipt_number' class='form-control' placeholder='Receipt Number (optional)'>";
    echo "</div>";
    echo "<div class='mb-3'>";
    echo "<textarea name='notes' class='form-control' rows='2' placeholder='Notes (optional)'></textarea>";
    echo "</div>";
    echo "<button type='submit' name='test_payment' class='btn btn-warning'>Manual Test Payment</button>";
    echo "</form>";
}

// Show recent payments
echo "<h2>📋 Recent Payments</h2>";
$paymentsQuery = "SELECT p.*, CONCAT(s.first_name, ' ', s.last_name) as student_name, f.fee_type 
                 FROM payments p 
                 JOIN students s ON p.student_id = s.id 
                 JOIN fees f ON p.fee_id = f.id 
                 ORDER BY p.id DESC LIMIT 5";
$paymentsResult = $conn->query($paymentsQuery);

if ($paymentsResult && $paymentsResult->num_rows > 0) {
    echo "<table class='table table-striped'>";
    echo "<tr><th>ID</th><th>Student</th><th>Fee Type</th><th>Amount</th><th>Method</th><th>Date</th><th>Receipt</th></tr>";
    while ($payment = $paymentsResult->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$payment['id']}</td>";
        echo "<td>{$payment['student_name']}</td>";
        echo "<td>{$payment['fee_type']}</td>";
        echo "<td>৳{$payment['amount']}</td>";
        echo "<td>{$payment['payment_method']}</td>";
        echo "<td>{$payment['payment_date']}</td>";
        echo "<td>{$payment['receipt_number']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No payments found</p>";
}

echo "<div class='mt-4'>";
echo "<a href='fee_management.php' class='btn btn-primary'>Back to Fee Management</a>";
echo "</div>";

echo "</div></body></html>";
?>
