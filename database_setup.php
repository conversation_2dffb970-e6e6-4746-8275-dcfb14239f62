<?php
// ডাটাবেস কনফিগারেশন
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "zfaw"; // আপনার কনফিগ.পিএইচপি ফাইলে এই নামটি ব্যবহার করা হয়েছে

// কানেকশন তৈরি করুন (ডাটাবেস ছাড়া)
$conn = new mysqli($servername, $username, $password);

// কানেকশন চেক করুন
if ($conn->connect_error) {
    die("কানেকশন ব্যর্থ হয়েছে: " . $conn->connect_error);
}
echo "সার্ভারের সাথে সংযোগ সফল হয়েছে<br>";

// ডাটাবেস তৈরি করুন
$sql = "CREATE DATABASE IF NOT EXISTS $dbname";
if ($conn->query($sql) === TRUE) {
    echo "ডাটাবেস সফলভাবে তৈরি করা হয়েছে<br>";
} else {
    echo "ডাটাবেস তৈরি করতে সমস্যা: " . $conn->error . "<br>";
}

// ডাটাবেস নির্বাচন করুন
$conn->select_db($dbname);

// ইউজার টেবিল তৈরি করুন
$sql = "CREATE TABLE IF NOT EXISTS users (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    user_type ENUM('admin', 'teacher', 'student', 'staff') NOT NULL,
    email VARCHAR(100) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($sql) === TRUE) {
    echo "users টেবিল সফলভাবে তৈরি করা হয়েছে<br>";
} else {
    echo "টেবিল তৈরি করতে সমস্যা: " . $conn->error . "<br>";
}

// ডিপার্টমেন্ট টেবিল তৈরি করুন
$sql = "CREATE TABLE IF NOT EXISTS departments (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    department_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($sql) === TRUE) {
    echo "departments টেবিল সফলভাবে তৈরি করা হয়েছে<br>";
} else {
    echo "টেবিল তৈরি করতে সমস্যা: " . $conn->error . "<br>";
}

// ক্লাস টেবিল তৈরি করুন
$sql = "CREATE TABLE IF NOT EXISTS classes (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    class_name VARCHAR(50) NOT NULL,
    department_id INT(11),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
)";

if ($conn->query($sql) === TRUE) {
    echo "classes টেবিল সফলভাবে তৈরি করা হয়েছে<br>";
} else {
    echo "টেবিল তৈরি করতে সমস্যা: " . $conn->error . "<br>";
}

// সেশন টেবিল তৈরি করুন
$sql = "CREATE TABLE IF NOT EXISTS sessions (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    session_name VARCHAR(50) NOT NULL UNIQUE,
    start_date DATE,
    end_date DATE,
    is_current TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($sql) === TRUE) {
    echo "sessions টেবিল সফলভাবে তৈরি করা হয়েছে<br>";
} else {
    echo "টেবিল তৈরি করতে সমস্যা: " . $conn->error . "<br>";
}

// স্টুডেন্ট টেবিল তৈরি করুন
$sql = "CREATE TABLE IF NOT EXISTS students (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id VARCHAR(20) NOT NULL UNIQUE,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    class_id INT(11),
    session_id INT(11),
    user_id INT(11),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
)";

if ($conn->query($sql) === TRUE) {
    echo "students টেবিল সফলভাবে তৈরি করা হয়েছে<br>";
} else {
    echo "টেবিল তৈরি করতে সমস্যা: " . $conn->error . "<br>";
}

// অ্যাডমিন ইউজার তৈরি করুন
$admin_username = "admin";
$admin_password = password_hash("admin123", PASSWORD_DEFAULT);
$admin_type = "admin";
$admin_email = "<EMAIL>";

// চেক করুন অ্যাডমিন আগে থেকে আছে কিনা
$check = $conn->query("SELECT * FROM users WHERE username='admin'");
if ($check->num_rows == 0) {
    $sql = "INSERT INTO users (username, password, user_type, email) 
            VALUES ('$admin_username', '$admin_password', '$admin_type', '$admin_email')";
    
    if ($conn->query($sql) === TRUE) {
        echo "অ্যাডমিন ইউজার সফলভাবে তৈরি করা হয়েছে<br>";
        echo "ইউজারনেম: admin<br>";
        echo "পাসওয়ার্ড: admin123<br>";
    } else {
        echo "অ্যাডমিন ইউজার তৈরি করতে সমস্যা: " . $conn->error . "<br>";
    }
} else {
    echo "অ্যাডমিন ইউজার আগে থেকেই আছে<br>";
}

$conn->close();
echo "<br><a href='login.php'>লগইন পেজে যান</a>";
?>