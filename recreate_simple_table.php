<?php
// Include database connection
require_once 'includes/dbh.inc.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Recreate Simple Student Subjects Table</h1>";

// Drop the table if it exists
$dropTableQuery = "DROP TABLE IF EXISTS student_subjects";
if ($conn->query($dropTableQuery)) {
    echo "<p>Dropped existing student_subjects table.</p>";
} else {
    echo "<p>Error dropping table: " . $conn->error . "</p>";
}

// Create a simple table without foreign keys
$createTableQuery = "CREATE TABLE student_subjects (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    subject_id INT(11) NOT NULL,
    category VARCHAR(20) NOT NULL DEFAULT 'optional',
    session_id INT(11) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($createTableQuery)) {
    echo "<p>Created simple student_subjects table without foreign keys.</p>";
} else {
    echo "<p>Error creating table: " . $conn->error . "</p>";
    exit;
}

// Test insert
$studentId = 'STD-601523';
$studentQuery = "SELECT id FROM students WHERE student_id = '$studentId'";
$studentResult = $conn->query($studentQuery);

if ($studentResult && $studentResult->num_rows > 0) {
    $studentDbId = $studentResult->fetch_assoc()['id'];
    
    // Get a valid subject ID
    $subjectQuery = "SELECT id FROM subjects LIMIT 1";
    $subjectResult = $conn->query($subjectQuery);
    
    if ($subjectResult && $subjectResult->num_rows > 0) {
        $subjectId = $subjectResult->fetch_assoc()['id'];
        
        // Get current session
        $sessionQuery = "SELECT id FROM sessions ORDER BY id DESC LIMIT 1";
        $sessionResult = $conn->query($sessionQuery);
        
        if ($sessionResult && $sessionResult->num_rows > 0) {
            $sessionId = $sessionResult->fetch_assoc()['id'];
            
            // Test insert
            $insertQuery = "INSERT INTO student_subjects (student_id, subject_id, category, session_id) 
                           VALUES ($studentDbId, $subjectId, 'required', $sessionId)";
            
            if ($conn->query($insertQuery)) {
                echo "<p>Test insert successful!</p>";
                
                // Check the inserted data
                $checkQuery = "SELECT * FROM student_subjects";
                $checkResult = $conn->query($checkQuery);
                
                if ($checkResult && $checkResult->num_rows > 0) {
                    echo "<h2>Inserted Data</h2>";
                    echo "<table border='1' cellpadding='5'>";
                    echo "<tr><th>ID</th><th>Student ID</th><th>Subject ID</th><th>Category</th><th>Session ID</th><th>Created At</th></tr>";
                    
                    while ($row = $checkResult->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>{$row['id']}</td>";
                        echo "<td>{$row['student_id']}</td>";
                        echo "<td>{$row['subject_id']}</td>";
                        echo "<td>{$row['category']}</td>";
                        echo "<td>{$row['session_id']}</td>";
                        echo "<td>{$row['created_at']}</td>";
                        echo "</tr>";
                    }
                    
                    echo "</table>";
                } else {
                    echo "<p>No data found after insert.</p>";
                }
            } else {
                echo "<p>Error inserting test data: " . $conn->error . "</p>";
            }
        } else {
            echo "<p>No session found.</p>";
        }
    } else {
        echo "<p>No subject found.</p>";
    }
} else {
    echo "<p>Student not found.</p>";
}

echo "<h2>Table Recreation Complete</h2>";
echo "<p><a href='admin/student_subject_selection.php?id=STD-601523'>Go to Student Subject Selection</a></p>";
?>
