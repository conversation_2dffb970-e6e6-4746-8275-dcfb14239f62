<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    die('Unauthorized access');
}

echo "<h1>PHP Information</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .info{background:#f0f8ff;padding:15px;border-radius:5px;margin:10px 0;}</style>";

echo "<div class='info'>";
echo "<h2>📊 PHP Version Information</h2>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>PHP Major Version:</strong> " . PHP_MAJOR_VERSION . "</p>";
echo "<p><strong>PHP Minor Version:</strong> " . PHP_MINOR_VERSION . "</p>";
echo "<p><strong>Server Software:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p><strong>Operating System:</strong> " . PHP_OS . "</p>";
echo "</div>";

echo "<div class='info'>";
echo "<h2>🔧 PHP Features Check</h2>";
echo "<ul>";
echo "<li><strong>Null Coalescing Operator (??):</strong> " . (version_compare(PHP_VERSION, '7.0.0') >= 0 ? "✅ Supported" : "❌ Not Supported (PHP 7.0+ required)") . "</li>";
echo "<li><strong>MySQLi Extension:</strong> " . (extension_loaded('mysqli') ? "✅ Loaded" : "❌ Not Loaded") . "</li>";
echo "<li><strong>Session Support:</strong> " . (function_exists('session_start') ? "✅ Available" : "❌ Not Available") . "</li>";
echo "<li><strong>JSON Support:</strong> " . (function_exists('json_encode') ? "✅ Available" : "❌ Not Available") . "</li>";
echo "</ul>";
echo "</div>";

echo "<div class='info'>";
echo "<h2>📁 File Paths</h2>";
echo "<ul>";
echo "<li><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</li>";
echo "<li><strong>Script Filename:</strong> " . $_SERVER['SCRIPT_FILENAME'] . "</li>";
echo "<li><strong>Current Directory:</strong> " . getcwd() . "</li>";
echo "</ul>";
echo "</div>";

echo "<div class='info'>";
echo "<h2>🔗 Quick Links</h2>";
echo "<a href='check-database.php' style='background:#007bff;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;margin:5px;display:inline-block;'>Database Check</a>";
echo "<a href='test-fee-add.php' style='background:#28a745;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;margin:5px;display:inline-block;'>Test Fee Add</a>";
echo "<a href='fee_management.php' style='background:#ffc107;color:black;padding:10px 20px;text-decoration:none;border-radius:5px;margin:5px;display:inline-block;'>Fee Management</a>";
echo "</div>";

// Show limited phpinfo for debugging
echo "<div class='info'>";
echo "<h2>🔍 PHP Configuration (Limited)</h2>";
echo "<p><strong>Memory Limit:</strong> " . ini_get('memory_limit') . "</p>";
echo "<p><strong>Max Execution Time:</strong> " . ini_get('max_execution_time') . " seconds</p>";
echo "<p><strong>Upload Max Filesize:</strong> " . ini_get('upload_max_filesize') . "</p>";
echo "<p><strong>Post Max Size:</strong> " . ini_get('post_max_size') . "</p>";
echo "<p><strong>Display Errors:</strong> " . (ini_get('display_errors') ? 'On' : 'Off') . "</p>";
echo "<p><strong>Error Reporting:</strong> " . ini_get('error_reporting') . "</p>";
echo "</div>";

if (isset($_GET['full']) && $_GET['full'] == '1') {
    echo "<hr>";
    echo "<h2>📋 Full PHP Info</h2>";
    phpinfo();
}

echo "<br><br>";
echo "<a href='?full=1' style='background:#dc3545;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>Show Full PHP Info</a>";
?>
