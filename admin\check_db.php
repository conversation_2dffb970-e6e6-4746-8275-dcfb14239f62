<?php
session_start();
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

echo "<h2>ডাটাবেস চেক রিপোর্ট</h2>";

// Check which tables exist
$tables = ['payments', 'fee_payments', 'fees', 'students', 'classes'];
foreach ($tables as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "<h3>✅ $table টেবিল পাওয়া গেছে</h3>";
        
        // Show count
        $count = $conn->query("SELECT COUNT(*) as count FROM $table");
        $countRow = $count->fetch_assoc();
        echo "<p>মোট রেকর্ড: " . $countRow['count'] . "</p>";
        
        // Show sample data for payments tables
        if ($table === 'fee_payments') {
            echo "<h4>fee_payments টেবিলের নমুনা ডেটা:</h4>";
            $sample = $conn->query("SELECT fp.*, f.fee_type, s.first_name, s.last_name, s.roll_no, c.class_name 
                                   FROM fee_payments fp 
                                   LEFT JOIN fees f ON fp.fee_id = f.id 
                                   LEFT JOIN students s ON f.student_id = s.id 
                                   LEFT JOIN classes c ON s.class_id = c.id 
                                   ORDER BY fp.created_at DESC LIMIT 5");
            if ($sample->num_rows > 0) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr><th>রিসিপ্ট নং</th><th>পরিমাণ</th><th>তারিখ</th><th>শিক্ষার্থী</th><th>রোল</th><th>ক্লাস</th><th>ফি ধরন</th></tr>";
                while ($row = $sample->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($row['receipt_no'] ?? 'N/A') . "</td>";
                    echo "<td>৳" . number_format($row['amount'], 2) . "</td>";
                    echo "<td>" . date('d/m/Y', strtotime($row['payment_date'])) . "</td>";
                    echo "<td>" . htmlspecialchars(($row['first_name'] ?? '') . ' ' . ($row['last_name'] ?? '')) . "</td>";
                    echo "<td>" . htmlspecialchars($row['roll_no'] ?? 'N/A') . "</td>";
                    echo "<td>" . htmlspecialchars($row['class_name'] ?? 'N/A') . "</td>";
                    echo "<td>" . htmlspecialchars($row['fee_type'] ?? 'N/A') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p>কোন ডেটা পাওয়া যায়নি</p>";
            }
        }
        
        if ($table === 'students') {
            echo "<h4>students টেবিলের নমুনা ডেটা:</h4>";
            $sample = $conn->query("SELECT s.*, c.class_name FROM students s LEFT JOIN classes c ON s.class_id = c.id LIMIT 5");
            if ($sample->num_rows > 0) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr><th>ID</th><th>নাম</th><th>রোল</th><th>ক্লাস</th><th>ইমেইল</th></tr>";
                while ($row = $sample->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td>" . $row['id'] . "</td>";
                    echo "<td>" . htmlspecialchars(($row['first_name'] ?? '') . ' ' . ($row['last_name'] ?? '')) . "</td>";
                    echo "<td>" . htmlspecialchars($row['roll_no'] ?? $row['student_id'] ?? 'N/A') . "</td>";
                    echo "<td>" . htmlspecialchars($row['class_name'] ?? 'N/A') . "</td>";
                    echo "<td>" . htmlspecialchars($row['email'] ?? 'N/A') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        }
        
        if ($table === 'fees') {
            echo "<h4>fees টেবিলের নমুনা ডেটা:</h4>";
            $sample = $conn->query("SELECT f.*, s.first_name, s.last_name FROM fees f LEFT JOIN students s ON f.student_id = s.id LIMIT 5");
            if ($sample->num_rows > 0) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr><th>ID</th><th>শিক্ষার্থী</th><th>ফি ধরন</th><th>পরিমাণ</th><th>পরিশোধিত</th><th>স্ট্যাটাস</th></tr>";
                while ($row = $sample->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td>" . $row['id'] . "</td>";
                    echo "<td>" . htmlspecialchars(($row['first_name'] ?? '') . ' ' . ($row['last_name'] ?? '')) . "</td>";
                    echo "<td>" . htmlspecialchars($row['fee_type']) . "</td>";
                    echo "<td>৳" . number_format($row['amount'], 2) . "</td>";
                    echo "<td>৳" . number_format($row['paid'], 2) . "</td>";
                    echo "<td>" . htmlspecialchars($row['payment_status']) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        }
        
    } else {
        echo "<h3>❌ $table টেবিল পাওয়া যায়নি</h3>";
    }
}

// Create a test payment if needed
echo "<h3>টেস্ট পেমেন্ট তৈরি</h3>";
try {
    // Check if we have the necessary data
    $studentCheck = $conn->query("SELECT COUNT(*) as count FROM students");
    $studentCount = $studentCheck->fetch_assoc()['count'];
    
    $feeCheck = $conn->query("SELECT COUNT(*) as count FROM fees");
    $feeCount = $feeCheck->fetch_assoc()['count'];
    
    echo "<p>স্টুডেন্ট: $studentCount, ফি: $feeCount</p>";
    
    if ($studentCount > 0 && $feeCount > 0) {
        // Get first student and fee
        $student = $conn->query("SELECT * FROM students LIMIT 1")->fetch_assoc();
        $fee = $conn->query("SELECT * FROM fees LIMIT 1")->fetch_assoc();
        
        if ($student && $fee) {
            $receiptNo = 'TEST-' . date('Ymd') . '-' . rand(1000, 9999);
            
            // Check if fee_payments table exists, if not create it
            $checkTable = $conn->query("SHOW TABLES LIKE 'fee_payments'");
            if ($checkTable->num_rows == 0) {
                $createTable = "CREATE TABLE fee_payments (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    fee_id INT NOT NULL,
                    amount DECIMAL(10,2) NOT NULL,
                    payment_date DATE NOT NULL,
                    payment_method VARCHAR(50) NOT NULL,
                    receipt_no VARCHAR(50) DEFAULT NULL,
                    notes TEXT DEFAULT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (fee_id) REFERENCES fees(id) ON DELETE CASCADE
                )";
                $conn->query($createTable);
                echo "<p>fee_payments টেবিল তৈরি করা হয়েছে</p>";
            }
            
            $insertPayment = "INSERT INTO fee_payments (fee_id, receipt_no, amount, payment_date, payment_method) 
                             VALUES (?, ?, ?, ?, 'cash')";
            $stmt = $conn->prepare($insertPayment);
            $amount = 1500.00;
            $paymentDate = date('Y-m-d');
            $stmt->bind_param('isds', $fee['id'], $receiptNo, $amount, $paymentDate);
            
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✅ টেস্ট পেমেন্ট তৈরি হয়েছে: <a href='receipt_final.php?receipt_no=$receiptNo' target='_blank'>$receiptNo</a></p>";
                echo "<p><a href='receipt_final.php?receipt_no=$receiptNo&debug=1' target='_blank'>ডিবাগ মোডে দেখুন</a></p>";
            } else {
                echo "<p style='color: red;'>❌ টেস্ট পেমেন্ট তৈরি করতে সমস্যা: " . $stmt->error . "</p>";
            }
        }
    } else {
        echo "<p style='color: orange;'>⚠️ প্রথমে স্টুডেন্ট এবং ফি যোগ করুন</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<br><a href='fee_management.php'>ফি ম্যানেজমেন্টে ফিরে যান</a>";
?>
