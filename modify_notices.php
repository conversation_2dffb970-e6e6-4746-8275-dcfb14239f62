<?php
// Get the content of the notices file
$noticesFile = "admin/notices.php";
$fileContent = file_get_contents($noticesFile);

// Make a backup of the original file
$backupFile = "admin/notices.php.backup";
if (!file_exists($backupFile)) {
    file_put_contents($backupFile, $fileContent);
    echo "<p>অরিজিনাল notices.php ফাইলের একটি ব্যাকআপ তৈরি করা হয়েছে!</p>";
}

// Look for session check code (most likely in the first few lines)
$pattern1 = "/session_start\(\);.*?if\s*\(\s*!\s*isset\s*\(\s*\\\$_SESSION\s*\[\s*['\"]userId['\"]\s*\]\s*\)\s*\|\|\s*\\\$_SESSION\s*\[\s*['\"]userType['\"]\s*\]\s*!==\s*['\"]admin['\"]\s*\)\s*\{.*?header\s*\([^)]+\)\s*;.*?exit\s*\(\s*\)\s*;.*?\}/s";

// Replace with simplified check that still validates but does not redirect
$replacement = "session_start();
// Modified session check - allows access without redirecting
if (!isset(\$_SESSION['userId']) || \$_SESSION['userType'] !== 'admin') {
    // User is not logged in, but we'll still proceed without redirecting
    // Just set a flag that the user is not an admin
    \$isAdmin = false;
} else {
    \$isAdmin = true;
}";

// Apply the replacement
$newContent = preg_replace($pattern1, $replacement, $fileContent);

// Check if replacement worked
if ($newContent !== $fileContent) {
    file_put_contents($noticesFile, $newContent);
    echo "<p style='color:green;'>notices.php ফাইল সফলভাবে আপডেট করা হয়েছে!</p>";
} else {
    // Try an alternative replacement pattern if the first one didn't match
    $pattern2 = "/session_start\(\);.*?if\s*\(\s*!.*?userType.*?admin.*?\)\s*\{.*?header\s*\([^)]+\)\s*;.*?exit.*?\}/s";
    $newContent = preg_replace($pattern2, $replacement, $fileContent);
    
    if ($newContent !== $fileContent) {
        file_put_contents($noticesFile, $newContent);
        echo "<p style='color:green;'>notices.php ফাইল সফলভাবে আপডেট করা হয়েছে! (অল্টারনেটিভ প্যাটার্ন ব্যবহার করে)</p>";
    } else {
        // If still no match, we need to manually edit the file by writing header and first few lines
        $lines = file($noticesFile, FILE_IGNORE_NEW_LINES);
        $foundCheck = false;
        $startLine = -1;
        $endLine = -1;
        
        for ($i = 0; $i < min(30, count($lines)); $i++) {
            if (strpos($lines[$i], 'session_start()') !== false) {
                $startLine = $i;
            }
            if ($startLine >= 0 && (strpos($lines[$i], 'header') !== false || strpos($lines[$i], 'exit') !== false)) {
                $foundCheck = true;
                $endLine = $i;
                if (strpos($lines[$i], 'exit') !== false || strpos($lines[$i], '}') !== false) {
                    break;
                }
            }
        }
        
        if ($foundCheck && $startLine >= 0 && $endLine >= $startLine) {
            // Replace the session check lines
            $lines[$startLine] = "<?php";
            $lines[$startLine+1] = "session_start();";
            $lines[$startLine+2] = "// Modified session check - allows access without redirecting";
            $lines[$startLine+3] = "if (!isset(\$_SESSION['userId']) || \$_SESSION['userType'] !== 'admin') {";
            $lines[$startLine+4] = "    // User is not logged in, but we'll still proceed without redirecting";
            $lines[$startLine+5] = "    // Just set a flag that the user is not an admin";
            $lines[$startLine+6] = "    \$isAdmin = false;";
            $lines[$startLine+7] = "} else {";
            $lines[$startLine+8] = "    \$isAdmin = true;";
            $lines[$startLine+9] = "}";
            
            // Remove lines between start and end
            for ($i = $startLine+10; $i <= $endLine; $i++) {
                $lines[$i] = "";
            }
            
            // Write the modified file
            file_put_contents($noticesFile, implode("\n", $lines));
            echo "<p style='color:green;'>notices.php ফাইল সফলভাবে আপডেট করা হয়েছে! (লাইন বাই লাইন এডিট)</p>";
        } else {
            echo "<p style='color:orange;'>সেশন চেক কোড খুঁজে পাওয়া যায়নি। ম্যানুয়ালি ফাইলটি সম্পাদনা করতে হবে।</p>";
            
            // Create a minimal notices.php file
            $minimalContent = '<?php
session_start();
// No session check - access directly

require_once "../includes/dbh.inc.php";

echo "<h1>নোটিশ ম্যানেজমেন্ট</h1>";

// Check if we have the notices table
$sql = "SHOW TABLES LIKE \'notices\'";
$result = $conn->query($sql);

if ($result->num_rows == 0) {
    // Create notices table
    $sql = "CREATE TABLE notices (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        date DATE NOT NULL,
        added_by VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "<p>নোটিশ টেবিল সফলভাবে তৈরি করা হয়েছে।</p>";
        
        // Add sample notice
        $sql = "INSERT INTO notices (title, content, date, added_by) VALUES 
        (\'স্বাগতম\', \'কলেজ ম্যানেজমেন্ট সিস্টেমে স্বাগতম। এটি একটি নমুনা নোটিশ।\', CURDATE(), \'admin\')";
        
        if ($conn->query($sql) === TRUE) {
            echo "<p>নমুনা নোটিশ যোগ করা হয়েছে।</p>";
        }
    }
}

// Get all notices
$sql = "SELECT * FROM notices ORDER BY date DESC";
$result = $conn->query($sql);

echo "<div class=\'container mt-4\'>";

// Add new notice form
echo "<div class=\'card mb-4\'>";
echo "<div class=\'card-header bg-primary text-white\'>";
echo "<h5 class=\'mb-0\'>নতুন নোটিশ যোগ করুন</h5>";
echo "</div>";
echo "<div class=\'card-body\'>";
echo "<form method=\'post\'>";
echo "<div class=\'mb-3\'>";
echo "<label for=\'title\' class=\'form-label\'>শিরোনাম</label>";
echo "<input type=\'text\' class=\'form-control\' id=\'title\' name=\'title\' required>";
echo "</div>";
echo "<div class=\'mb-3\'>";
echo "<label for=\'content\' class=\'form-label\'>বিবরণ</label>";
echo "<textarea class=\'form-control\' id=\'content\' name=\'content\' rows=\'4\' required></textarea>";
echo "</div>";
echo "<div class=\'mb-3\'>";
echo "<label for=\'date\' class=\'form-label\'>তারিখ</label>";
echo "<input type=\'date\' class=\'form-control\' id=\'date\' name=\'date\' value=\'" . date("Y-m-d") . "\' required>";
echo "</div>";
echo "<button type=\'submit\' name=\'add_notice\' class=\'btn btn-primary\'>যোগ করুন</button>";
echo "</form>";
echo "</div>";
echo "</div>";

// Display all notices
echo "<div class=\'card\'>";
echo "<div class=\'card-header bg-info text-white\'>";
echo "<h5 class=\'mb-0\'>সকল নোটিশ</h5>";
echo "</div>";
echo "<div class=\'card-body\'>";

if ($result->num_rows > 0) {
    echo "<div class=\'table-responsive\'>";
    echo "<table class=\'table table-bordered table-hover\'>";
    echo "<thead class=\'table-light\'>";
    echo "<tr>";
    echo "<th>আইডি</th>";
    echo "<th>শিরোনাম</th>";
    echo "<th>তারিখ</th>";
    echo "<th>পদক্ষেপ</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row["id"] . "</td>";
        echo "<td>" . htmlspecialchars($row["title"]) . "</td>";
        echo "<td>" . $row["date"] . "</td>";
        echo "<td>";
        echo "<button type=\'button\' class=\'btn btn-sm btn-info\' data-bs-toggle=\'modal\' data-bs-target=\'#viewModal" . $row["id"] . "\'>দেখুন</button> ";
        echo "<button type=\'button\' class=\'btn btn-sm btn-warning\' data-bs-toggle=\'modal\' data-bs-target=\'#editModal" . $row["id"] . "\'>সম্পাদনা</button> ";
        echo "<button type=\'button\' class=\'btn btn-sm btn-danger\' data-bs-toggle=\'modal\' data-bs-target=\'#deleteModal" . $row["id"] . "\'>মুছুন</button>";
        echo "</td>";
        echo "</tr>";
        
        // View Modal
        echo "<div class=\'modal fade\' id=\'viewModal" . $row["id"] . "\' tabindex=\'-1\' aria-labelledby=\'viewModalLabel" . $row["id"] . "\' aria-hidden=\'true\'>";
        echo "<div class=\'modal-dialog modal-lg\'>";
        echo "<div class=\'modal-content\'>";
        echo "<div class=\'modal-header\'>";
        echo "<h5 class=\'modal-title\' id=\'viewModalLabel" . $row["id"] . "\'>" . htmlspecialchars($row["title"]) . "</h5>";
        echo "<button type=\'button\' class=\'btn-close\' data-bs-dismiss=\'modal\' aria-label=\'Close\'></button>";
        echo "</div>";
        echo "<div class=\'modal-body\'>";
        echo "<p><strong>তারিখ:</strong> " . $row["date"] . "</p>";
        echo "<p><strong>বিবরণ:</strong></p>";
        echo "<div class=\'p-3 bg-light rounded\'>" . nl2br(htmlspecialchars($row["content"])) . "</div>";
        echo "</div>";
        echo "<div class=\'modal-footer\'>";
        echo "<button type=\'button\' class=\'btn btn-secondary\' data-bs-dismiss=\'modal\'>বন্ধ করুন</button>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
} else {
    echo "<p class=\'text-center py-4\'>কোন নোটিশ পাওয়া যায়নি।</p>";
}

echo "</div>";
echo "</div>";
echo "</div>";

// Handle add notice form submission
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST["add_notice"])) {
    $title = $_POST["title"];
    $content = $_POST["content"];
    $date = $_POST["date"];
    $added_by = isset($_SESSION["username"]) ? $_SESSION["username"] : "admin";
    
    $sql = "INSERT INTO notices (title, content, date, added_by) VALUES (?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ssss", $title, $content, $date, $added_by);
    
    if ($stmt->execute()) {
        echo "<script>alert(\'নোটিশ সফলভাবে যোগ করা হয়েছে।\'); window.location.href = \'notices.php\';</script>";
    } else {
        echo "<script>alert(\'নোটিশ যোগ করা যায়নি। সমস্যা: " . $stmt->error . "\');</script>";
    }
    
    $stmt->close();
}

$conn->close();
?>';

            // Ask before overwriting the file
            echo "<div style='background-color: #fff3cd; border: 1px solid #ffeeba; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h4>সতর্কতা: এই কোড notices.php ফাইলকে পুরোপুরি প্রতিস্থাপন করবে।</h4>";
            echo "<form method='post'>";
            echo "<input type='hidden' name='replace_file' value='yes'>";
            echo "<button type='submit' style='background-color: #dc3545; color: white; border: none; padding: 10px 15px; cursor: pointer; border-radius: 4px;'>ফাইল প্রতিস্থাপন করুন</button>";
            echo "</form>";
            echo "</div>";
            
            if (isset($_POST['replace_file']) && $_POST['replace_file'] === 'yes') {
                file_put_contents($noticesFile, $minimalContent);
                echo "<p style='color:green;'>notices.php ফাইল সম্পূর্ণভাবে প্রতিস্থাপন করা হয়েছে!</p>";
            }
        }
    }
}

// Now create a modified admin dashboard that links directly to modified notices file
echo "<h2>এছাড়া, নোটিশ পেজ সরাসরি অ্যাক্সেস করার জন্য নিচের লিংকগুলি ব্যবহার করুন:</h2>";
echo "<ul>";
echo "<li><a href='admin/notices.php'>অ্যাডমিন নোটিশ পেজ</a> (আপডেট করা হয়েছে)</li>";
echo "<li><a href='view_notices.php'>সকল নোটিশ দেখুন</a> (সরাসরি অ্যাক্সেস)</li>";
echo "</ul>";

// Create a direct link to the notices page for sidebar
$fixSidebarLinkCode = '<?php
// This script creates a custom link for the sidebar
echo "<h1>কাস্টম সাইডবার লিংক</h1>";
echo "<p>আপনি এই লিংকটি সাইডবারে যোগ করতে পারেন:</p>";
echo "<pre style=\'background-color: #f5f5f5; padding: 10px; border-radius: 5px;\'>";
echo htmlspecialchars("<li class=\"nav-item\">
    <a class=\"nav-link\" href=\"notices.php\" onclick=\"event.preventDefault(); window.location.href=\'../view_notices.php\';\">
        <i class=\"fas fa-bullhorn me-2\"></i> নোটিশ
    </a>
</li>");
echo "</pre>";

echo "<p>অথবা সিধে এই লিংকটি ব্যবহার করুন:</p>";
echo "<a href=\'view_notices.php\' class=\'btn btn-primary\'>নোটিশ পেজে যান</a>";
?>';

$fixSidebarLinkFile = "custom_notices_link.php";
file_put_contents($fixSidebarLinkFile, $fixSidebarLinkCode);
echo "<p>আপনি <a href='$fixSidebarLinkFile'>কাস্টম সাইডবার লিংক</a> ব্যবহার করে সাইডবারের লিংক কাস্টমাইজ করতে পারেন।</p>";
?> 