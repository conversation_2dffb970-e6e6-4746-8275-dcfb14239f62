/**
 * Prevent page from automatically reloading and fix title bar buffering
 */
document.addEventListener('DOMContentLoaded', function() {
    // Clear any existing intervals that might be causing reloads or buffering
    for (let i = 1; i < 9999; i++) {
        clearInterval(i);
        clearTimeout(i);
    }

    // Stop any marquee animations that might cause buffering
    const marquees = document.querySelectorAll('marquee');
    marquees.forEach(function(marquee) {
        marquee.stop();
        setTimeout(function() {
            marquee.start();
        }, 100);
    });

    // Prevent automatic page refresh
    if (window.location && window.location.reload) {
        const originalReload = window.location.reload;
        window.location.reload = function() {
            console.log('Automatic reload prevented');
        };
    }

    // Fix title buffering by ensuring document title is set properly
    if (document.title && document.title.includes('নিশাত এডুকেশন সেন্টার')) {
        document.title = 'নিশাত এডুকেশন সেন্টার';
    }

    // Disable any auto-refresh meta tags
    const metaRefresh = document.querySelector('meta[http-equiv="refresh"]');
    if (metaRefresh) {
        metaRefresh.remove();
    }
});
