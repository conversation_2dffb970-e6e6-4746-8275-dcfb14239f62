<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get students with filtering by department, class, and session
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$itemsPerPage = 20;
$offset = ($page - 1) * $itemsPerPage;

// Search and filter parameters
$searchTerm = isset($_GET['search']) ? $_GET['search'] : '';
$departmentFilter = isset($_GET['department']) ? $_GET['department'] : '';
$classFilter = isset($_GET['class']) ? $_GET['class'] : '';
$sessionFilter = isset($_GET['session']) ? $_GET['session'] : '';

$whereClause = "";
$params = [];
$types = "";

if (!empty($searchTerm)) {
    $whereClause .= " WHERE (s.student_id LIKE ? OR s.first_name LIKE ? OR s.last_name LIKE ? OR s.email LIKE ?)";
    $searchParam = "%$searchTerm%";
    $params = [$searchParam, $searchParam, $searchParam, $searchParam];
    $types = "ssss";
}

if (!empty($departmentFilter)) {
    $whereClause = empty($whereClause) ? " WHERE s.department_id = ?" : $whereClause . " AND s.department_id = ?";
    $params[] = $departmentFilter;
    $types .= "i";
}

if (!empty($classFilter)) {
    $whereClause = empty($whereClause) ? " WHERE s.class_id = ?" : $whereClause . " AND s.class_id = ?";
    $params[] = $classFilter;
    $types .= "i";
}

if (!empty($sessionFilter)) {
    $whereClause = empty($whereClause) ? " WHERE s.session_id = ?" : $whereClause . " AND s.session_id = ?";
    $params[] = $sessionFilter;
    $types .= "i";
}

// Get total students count for pagination
$countQuery = "SELECT COUNT(*) as total FROM students s" . $whereClause;
$stmt = $conn->prepare($countQuery);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$totalResult = $stmt->get_result();
$totalRows = $totalResult->fetch_assoc()['total'];
$totalPages = ceil($totalRows / $itemsPerPage);

// Get students for current page
$studentsQuery = "SELECT s.*, u.username, d.department_name, c.class_name, ss.session_name FROM students s
                LEFT JOIN users u ON s.user_id = u.id
                LEFT JOIN departments d ON s.department_id = d.id
                LEFT JOIN classes c ON s.class_id = c.id
                LEFT JOIN sessions ss ON s.session_id = ss.id" .
                $whereClause .
                " ORDER BY s.first_name, s.last_name
                LIMIT ? OFFSET ?";

$params[] = $itemsPerPage;
$params[] = $offset;
$types .= "ii";

$stmt = $conn->prepare($studentsQuery);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$students = $stmt->get_result();

// Get departments for filter
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Get classes for filter
$classesQuery = "SELECT * FROM classes ORDER BY class_name";
$classes = $conn->query($classesQuery);

// Get sessions for filter
$sessionsQuery = "SELECT * FROM sessions ORDER BY session_name";
$sessions = $conn->query($sessionsQuery);

// Get count of selected subjects for each student
$studentSubjectCounts = [];
$subjectCountQuery = "SELECT student_id, COUNT(*) as subject_count FROM student_subjects GROUP BY student_id";
$subjectCountResult = $conn->query($subjectCountQuery);
if ($subjectCountResult && $subjectCountResult->num_rows > 0) {
    while ($row = $subjectCountResult->fetch_assoc()) {
        $studentSubjectCounts[$row['student_id']] = $row['subject_count'];
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>শিক্ষার্থী বিষয় নির্বাচন - কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* Search and Filter Styles */
        .search-container {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .search-container > div {
            flex: 1;
            min-width: 200px;
        }

        /* Button and Action Styles */
        .table-actions {
            white-space: nowrap;
        }

        .table-actions .btn {
            margin-right: 5px;
        }

        .badge-subject-count {
            font-size: 0.8rem;
            padding: 0.3rem 0.5rem;
        }

        /* View Toggle Styles */
        .view-toggle .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        .view-toggle .btn.active {
            background-color: #fff;
            color: #0d6efd;
            border-color: #fff;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        }

        /* Grid View Styles */
        .student-card {
            transition: all 0.3s ease;
            margin-bottom: 20px;
            height: 100%;
        }

        .student-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .student-card .card-body {
            padding: 1.25rem;
        }

        .student-card .student-name {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .student-card .student-info {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .student-card .card-footer {
            background-color: rgba(0,0,0,0.03);
            padding: 0.75rem 1.25rem;
            border-top: 1px solid rgba(0,0,0,0.125);
        }

        /* List View Styles */
        .list-view .list-group-item {
            transition: all 0.3s ease;
            margin-bottom: 0.5rem;
            border-radius: 0.25rem;
        }

        .list-view .list-group-item:hover {
            background-color: #f8f9fa;
            transform: translateX(5px);
        }

        /* Title View Styles */
        .title-view .card {
            transition: all 0.3s ease;
        }

        .title-view .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .title-view .card-header {
            background-color: #f8f9fa;
            padding: 0.75rem 1rem;
        }

        /* Table View Styles */
        .table-view table {
            border-collapse: separate;
            border-spacing: 0;
        }

        .table-view th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
        }

        .table-view tr {
            transition: all 0.3s ease;
        }

        .table-view tr:hover {
            background-color: #f1f3f5;
        }

        /* Animation for view transitions */
        .view-container {
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="groups.php">
                            <i class="fas fa-layer-group me-2"></i> গ্রুপ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sessions.php">
                            <i class="fas fa-calendar-alt me-2"></i> সেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>শিক্ষার্থী বিষয় নির্বাচন</h2>
                        <p class="text-muted">শিক্ষার্থী নির্বাচন করে তাদের বিষয় নির্বাচন করুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="students.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>শিক্ষার্থী তালিকায় ফিরে যান
                        </a>
                    </div>
                </div>

                <!-- Search and Filter -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" action="student_subject_selection_list.php">
                            <div class="search-container">
                                <div class="flex-grow-1">
                                    <input type="text" name="search" class="form-control" placeholder="আইডি, নাম, বা ইমেইল দিয়ে খুঁজুন" value="<?php echo htmlspecialchars($searchTerm); ?>">
                                </div>
                                <div>
                                    <select name="department" class="form-select">
                                        <option value="">সব বিভাগ</option>
                                        <?php if ($departments && $departments->num_rows > 0): ?>
                                            <?php while ($dept = $departments->fetch_assoc()): ?>
                                                <option value="<?php echo $dept['id']; ?>" <?php echo ($departmentFilter == $dept['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $dept['department_name']; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div>
                                    <select name="class" class="form-select">
                                        <option value="">সব ক্লাস</option>
                                        <?php if ($classes && $classes->num_rows > 0): ?>
                                            <?php while ($class = $classes->fetch_assoc()): ?>
                                                <option value="<?php echo $class['id']; ?>" <?php echo ($classFilter == $class['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $class['class_name']; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div>
                                    <select name="session" class="form-select">
                                        <option value="">সব সেশন</option>
                                        <?php if ($sessions && $sessions->num_rows > 0): ?>
                                            <?php while ($session = $sessions->fetch_assoc()): ?>
                                                <option value="<?php echo $session['id']; ?>" <?php echo ($sessionFilter == $session['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $session['session_name']; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>খুঁজুন
                                    </button>
                                    <?php if (!empty($searchTerm) || !empty($departmentFilter) || !empty($classFilter) || !empty($sessionFilter)): ?>
                                        <a href="student_subject_selection_list.php" class="btn btn-secondary ms-2">রিসেট</a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Students List -->
                <div class="card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0"><i class="fas fa-users me-2"></i>শিক্ষার্থী তালিকা</h5>

                        <!-- View Toggle Buttons -->
                        <div class="btn-group view-toggle" role="group" aria-label="View Options">
                            <button type="button" class="btn btn-sm btn-light view-option active" data-view="grid" title="গ্রীড ভিউ">
                                <i class="fas fa-th-large"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-light view-option" data-view="list" title="লিস্ট ভিউ">
                                <i class="fas fa-list"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-light view-option" data-view="title" title="টাইটেল ভিউ">
                                <i class="fas fa-th-list"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-light view-option" data-view="table" title="টেবিল ভিউ">
                                <i class="fas fa-table"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Grid View -->
                        <div class="view-container grid-view">
                            <div class="row">
                                <?php if ($students && $students->num_rows > 0): ?>
                                    <?php
                                    // Reset the pointer to the beginning
                                    $students->data_seek(0);
                                    while ($student = $students->fetch_assoc()):
                                        $subjectCount = isset($studentSubjectCounts[$student['id']]) ? $studentSubjectCounts[$student['id']] : 0;
                                        $badgeClass = $subjectCount > 0 ? 'bg-success' : 'bg-warning';
                                        $badgeText = $subjectCount > 0 ? "$subjectCount টি বিষয় নির্বাচিত" : "কোন বিষয় নির্বাচিত নেই";
                                    ?>
                                        <div class="col-md-6 col-lg-4">
                                            <div class="card student-card">
                                                <div class="card-body">
                                                    <div class="d-flex align-items-center mb-3">
                                                        <?php if (!empty($student['profile_photo'])): ?>
                                                            <img src="../<?php echo $student['profile_photo']; ?>" alt="প্রোফাইল ছবি" class="rounded-circle me-3" width="50" height="50" style="object-fit: cover;">
                                                        <?php else: ?>
                                                            <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                                                <i class="fas fa-user"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                        <div>
                                                            <h5 class="student-name"><?php echo $student['first_name'] . ' ' . $student['last_name']; ?></h5>
                                                            <div class="student-info">
                                                                <i class="fas fa-id-card me-1"></i> <?php echo $student['student_id']; ?>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="student-info">
                                                        <i class="fas fa-envelope me-1"></i> <?php echo $student['email'] ?? 'N/A'; ?>
                                                    </div>
                                                    <div class="student-info">
                                                        <i class="fas fa-phone me-1"></i> <?php echo $student['phone'] ?? 'N/A'; ?>
                                                    </div>
                                                    <div class="student-info">
                                                        <i class="fas fa-building me-1"></i> <?php echo $student['department_name'] ?? 'N/A'; ?>
                                                    </div>
                                                    <div class="student-info">
                                                        <i class="fas fa-chalkboard me-1"></i> <?php echo $student['class_name'] ?? 'N/A'; ?>
                                                    </div>
                                                    <div class="student-info">
                                                        <i class="fas fa-calendar-alt me-1"></i> <?php echo $student['session_name'] ?? 'N/A'; ?>
                                                    </div>

                                                    <div class="mt-3">
                                                        <span class="badge <?php echo $badgeClass; ?> badge-subject-count">
                                                            <i class="fas fa-book me-1"></i> <?php echo $badgeText; ?>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="card-footer">
                                                    <a href="student_subject_selection.php?id=<?php echo $student['student_id']; ?>" class="btn btn-primary w-100">
                                                        <i class="fas fa-edit me-2"></i>বিষয় নির্বাচন করুন
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <div class="col-12">
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>কোন শিক্ষার্থী পাওয়া যায়নি
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- List View -->
                        <div class="view-container list-view d-none">
                            <div class="list-group">
                                <?php if ($students && $students->num_rows > 0): ?>
                                    <?php
                                    // Reset the pointer to the beginning
                                    $students->data_seek(0);
                                    while ($student = $students->fetch_assoc()):
                                        $subjectCount = isset($studentSubjectCounts[$student['id']]) ? $studentSubjectCounts[$student['id']] : 0;
                                        $badgeClass = $subjectCount > 0 ? 'bg-success' : 'bg-warning';
                                        $badgeText = $subjectCount > 0 ? "$subjectCount টি বিষয় নির্বাচিত" : "কোন বিষয় নির্বাচিত নেই";
                                    ?>
                                        <div class="list-group-item list-group-item-action">
                                            <div class="d-flex w-100 justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <?php if (!empty($student['profile_photo'])): ?>
                                                        <img src="../<?php echo $student['profile_photo']; ?>" alt="প্রোফাইল ছবি" class="rounded-circle me-3" width="50" height="50" style="object-fit: cover;">
                                                    <?php else: ?>
                                                        <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                                            <i class="fas fa-user"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div>
                                                        <h5 class="mb-1"><?php echo $student['first_name'] . ' ' . $student['last_name']; ?></h5>
                                                        <div class="d-flex flex-wrap">
                                                            <small class="me-3"><i class="fas fa-id-card me-1"></i> <?php echo $student['student_id']; ?></small>
                                                            <small class="me-3"><i class="fas fa-building me-1"></i> <?php echo $student['department_name'] ?? 'N/A'; ?></small>
                                                            <small class="me-3"><i class="fas fa-calendar-alt me-1"></i> <?php echo $student['session_name'] ?? 'N/A'; ?></small>
                                                            <small><span class="badge <?php echo $badgeClass; ?> badge-subject-count">
                                                                <i class="fas fa-book me-1"></i> <?php echo $badgeText; ?>
                                                            </span></small>
                                                        </div>
                                                    </div>
                                                </div>
                                                <a href="student_subject_selection.php?id=<?php echo $student['student_id']; ?>" class="btn btn-primary btn-sm">
                                                    <i class="fas fa-edit me-2"></i>বিষয় নির্বাচন
                                                </a>
                                            </div>
                                        </div>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>কোন শিক্ষার্থী পাওয়া যায়নি
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Title View -->
                        <div class="view-container title-view d-none">
                            <div class="row">
                                <?php if ($students && $students->num_rows > 0): ?>
                                    <?php
                                    // Reset the pointer to the beginning
                                    $students->data_seek(0);
                                    while ($student = $students->fetch_assoc()):
                                        $subjectCount = isset($studentSubjectCounts[$student['id']]) ? $studentSubjectCounts[$student['id']] : 0;
                                        $badgeClass = $subjectCount > 0 ? 'bg-success' : 'bg-warning';
                                        $badgeText = $subjectCount > 0 ? "$subjectCount টি বিষয় নির্বাচিত" : "কোন বিষয় নির্বাচিত নেই";
                                    ?>
                                        <div class="col-md-6 col-lg-4 mb-3">
                                            <div class="card h-100">
                                                <div class="card-header">
                                                    <h6 class="mb-0"><?php echo $student['first_name'] . ' ' . $student['last_name']; ?></h6>
                                                </div>
                                                <div class="card-body py-2">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <small class="d-block"><i class="fas fa-id-card me-1"></i> <?php echo $student['student_id']; ?></small>
                                                            <small class="d-block"><i class="fas fa-building me-1"></i> <?php echo $student['department_name'] ?? 'N/A'; ?></small>
                                                        </div>
                                                        <span class="badge <?php echo $badgeClass; ?> badge-subject-count">
                                                            <i class="fas fa-book me-1"></i> <?php echo $badgeText; ?>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="card-footer p-2 text-center">
                                                    <a href="student_subject_selection.php?id=<?php echo $student['student_id']; ?>" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-edit me-1"></i>বিষয় নির্বাচন
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <div class="col-12">
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>কোন শিক্ষার্থী পাওয়া যায়নি
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Table View -->
                        <div class="view-container table-view d-none">
                            <div class="table-responsive">
                                <table class="table table-hover table-striped">
                                    <thead class="table-light">
                                        <tr>
                                            <th>আইডি</th>
                                            <th>নাম</th>
                                            <th>বিভাগ</th>
                                            <th>সেশন</th>
                                            <th>নির্বাচিত বিষয়</th>
                                            <th>অ্যাকশন</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if ($students && $students->num_rows > 0): ?>
                                            <?php
                                            // Reset the pointer to the beginning
                                            $students->data_seek(0);
                                            while ($student = $students->fetch_assoc()):
                                                $subjectCount = isset($studentSubjectCounts[$student['id']]) ? $studentSubjectCounts[$student['id']] : 0;
                                                $badgeClass = $subjectCount > 0 ? 'bg-success' : 'bg-warning';
                                                $badgeText = $subjectCount > 0 ? "$subjectCount টি বিষয় নির্বাচিত" : "কোন বিষয় নির্বাচিত নেই";
                                            ?>
                                                <tr>
                                                    <td><?php echo $student['student_id']; ?></td>
                                                    <td><?php echo $student['first_name'] . ' ' . $student['last_name']; ?></td>
                                                    <td><?php echo $student['department_name'] ?? 'N/A'; ?></td>
                                                    <td><?php echo $student['session_name'] ?? 'N/A'; ?></td>
                                                    <td>
                                                        <span class="badge <?php echo $badgeClass; ?> badge-subject-count">
                                                            <i class="fas fa-book me-1"></i> <?php echo $badgeText; ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <a href="student_subject_selection.php?id=<?php echo $student['student_id']; ?>" class="btn btn-sm btn-primary">
                                                            <i class="fas fa-edit me-1"></i>বিষয় নির্বাচন
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endwhile; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="6" class="text-center">কোন শিক্ষার্থী পাওয়া যায়নি</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <?php if (!$students || $students->num_rows === 0): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>কোন শিক্ষার্থী পাওয়া যায়নি
                            </div>
                        <?php endif; ?>
                        </div>

                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                            <nav class="mt-4">
                                <ul class="pagination justify-content-center">
                                    <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($searchTerm); ?>&department=<?php echo urlencode($departmentFilter); ?>&class=<?php echo urlencode($classFilter); ?>&session=<?php echo urlencode($sessionFilter); ?>">
                                            পূর্ববর্তী
                                        </a>
                                    </li>

                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                        <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($searchTerm); ?>&department=<?php echo urlencode($departmentFilter); ?>&class=<?php echo urlencode($classFilter); ?>&session=<?php echo urlencode($sessionFilter); ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>

                                    <li class="page-item <?php echo ($page >= $totalPages) ? 'disabled' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($searchTerm); ?>&department=<?php echo urlencode($departmentFilter); ?>&class=<?php echo urlencode($classFilter); ?>&session=<?php echo urlencode($sessionFilter); ?>">
                                            পরবর্তী
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get all view toggle buttons
            const viewOptions = document.querySelectorAll('.view-option');

            // Get all view containers
            const viewContainers = document.querySelectorAll('.view-container');

            // Add click event to all view options
            viewOptions.forEach(button => {
                button.addEventListener('click', function() {
                    const viewType = this.getAttribute('data-view');

                    // Update active button
                    viewOptions.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    // Hide all view containers
                    viewContainers.forEach(container => {
                        container.classList.add('d-none');
                    });

                    // Show the selected view container
                    const selectedView = document.querySelector(`.${viewType}-view`);
                    if (selectedView) {
                        selectedView.classList.remove('d-none');
                    }

                    // Save the current view preference in localStorage
                    localStorage.setItem('studentSubjectSelectionView', viewType);
                });
            });

            // Load saved view preference from localStorage
            const savedView = localStorage.getItem('studentSubjectSelectionView');
            if (savedView) {
                const savedViewButton = document.querySelector(`.view-option[data-view="${savedView}"]`);
                if (savedViewButton) {
                    savedViewButton.click();
                }
            }

            // Add tooltips to buttons
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
</body>
</html>
