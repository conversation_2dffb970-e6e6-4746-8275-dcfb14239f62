<?php
// Super simple test - no session, no authentication
error_reporting(E_ALL);
ini_set('display_errors', 1);
?>
<!DOCTYPE html>
<html>
<head>
    <title>Super Simple Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .form-group { margin: 10px 0; }
        input, select { padding: 5px; margin: 5px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>

<h1>🧪 Super Simple Fee Test</h1>
<p class="info">No authentication required - for testing only!</p>

<?php
try {
    echo "<p class='success'>✅ PHP is working</p>";
    echo "<p>PHP Version: " . PHP_VERSION . "</p>";
    echo "<p>Current Time: " . date('Y-m-d H:i:s') . "</p>";
    
    // Database connection
    $servername = "127.0.0.1";
    $username = "root";
    $password = "";
    $dbname = "zfaw";
    
    $conn = new mysqli($servername, $username, $password, $dbname);
    
    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }
    
    echo "<p class='success'>✅ Database connected successfully</p>";
    
    // Check tables
    $tables = ['students', 'sessions', 'classes', 'fees'];
    echo "<h2>📊 Database Tables Status</h2>";
    echo "<table>";
    echo "<tr><th>Table</th><th>Exists</th><th>Record Count</th></tr>";
    
    foreach ($tables as $table) {
        $exists = false;
        $count = 0;
        
        // Check if table exists
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            $exists = true;
            
            // Count records
            $countResult = $conn->query("SELECT COUNT(*) as count FROM $table");
            if ($countResult) {
                $count = $countResult->fetch_assoc()['count'];
            }
        }
        
        echo "<tr>";
        echo "<td>$table</td>";
        echo "<td>" . ($exists ? "✅ Yes" : "❌ No") . "</td>";
        echo "<td>$count</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Handle form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_test_fee'])) {
        echo "<h2>🔄 Processing Fee Addition...</h2>";
        
        $studentId = intval($_POST['student_id']);
        $feeType = $_POST['fee_type'];
        $amount = floatval($_POST['amount']);
        $dueDate = $_POST['due_date'];
        
        echo "<div style='background:#f0f8ff;padding:10px;border-radius:5px;margin:10px 0;'>";
        echo "<p><strong>Form Data Received:</strong></p>";
        echo "<ul>";
        echo "<li>Student ID: $studentId</li>";
        echo "<li>Fee Type: $feeType</li>";
        echo "<li>Amount: $amount</li>";
        echo "<li>Due Date: $dueDate</li>";
        echo "</ul>";
        echo "</div>";
        
        if ($studentId > 0 && !empty($feeType) && $amount > 0 && !empty($dueDate)) {
            // Get student info first
            $studentQuery = "SELECT s.*, c.class_name, sess.session_name 
                           FROM students s 
                           LEFT JOIN classes c ON s.class_id = c.id 
                           LEFT JOIN sessions sess ON s.session_id = sess.id 
                           WHERE s.id = $studentId";
            $studentResult = $conn->query($studentQuery);
            
            if ($studentResult && $studentResult->num_rows > 0) {
                $student = $studentResult->fetch_assoc();
                $studentName = $student['first_name'] . ' ' . $student['last_name'];
                echo "<p class='success'>✅ Student found: " . $studentName . " (Class: " . ($student['class_name'] ? $student['class_name'] : 'N/A') . ")</p>";
                
                // Insert fee
                $sessionId = $student['session_id'] ? $student['session_id'] : 1;
                $classId = $student['class_id'] ? $student['class_id'] : 1;
                
                $sql = "INSERT INTO fees (student_id, session_id, class_id, fee_type, amount, due_date, status, created_at) 
                        VALUES ($studentId, $sessionId, $classId, '$feeType', $amount, '$dueDate', 'unpaid', NOW())";
                
                echo "<p class='info'>SQL Query: $sql</p>";
                
                if ($conn->query($sql)) {
                    $feeId = $conn->insert_id;
                    echo "<p class='success'>🎉 Fee added successfully! Fee ID: $feeId</p>";
                } else {
                    echo "<p class='error'>❌ Error adding fee: " . $conn->error . "</p>";
                }
            } else {
                echo "<p class='error'>❌ Student not found with ID: $studentId</p>";
            }
        } else {
            echo "<p class='error'>❌ Invalid form data. Please check all fields.</p>";
        }
    }
    
    // Show students for form
    $studentsQuery = "SELECT s.*, c.class_name FROM students s LEFT JOIN classes c ON s.class_id = c.id ORDER BY s.first_name, s.last_name LIMIT 20";
    $studentsResult = $conn->query($studentsQuery);
    
    if ($studentsResult && $studentsResult->num_rows > 0) {
        echo "<h2>📝 Add Fee Form</h2>";
        echo "<form method='post'>";
        
        echo "<div class='form-group'>";
        echo "<label><strong>Select Student:</strong></label><br>";
        echo "<select name='student_id' required>";
        echo "<option value=''>Choose Student</option>";
        while ($student = $studentsResult->fetch_assoc()) {
            $studentName = $student['first_name'] . ' ' . $student['last_name'];
            $rollNumber = isset($student['roll_number']) ? $student['roll_number'] : (isset($student['student_id']) ? $student['student_id'] : 'N/A');
            echo "<option value='" . $student['id'] . "'>";
            echo $studentName . " (Roll: " . $rollNumber . ")";
            if ($student['class_name']) {
                echo " - " . $student['class_name'];
            }
            echo "</option>";
        }
        echo "</select>";
        echo "</div>";
        
        echo "<div class='form-group'>";
        echo "<label><strong>Fee Type:</strong></label><br>";
        echo "<select name='fee_type' required>";
        echo "<option value=''>Choose Fee Type</option>";
        echo "<option value='tuition'>Tuition Fee</option>";
        echo "<option value='admission'>Admission Fee</option>";
        echo "<option value='exam'>Exam Fee</option>";
        echo "<option value='library'>Library Fee</option>";
        echo "<option value='transport'>Transport Fee</option>";
        echo "</select>";
        echo "</div>";
        
        echo "<div class='form-group'>";
        echo "<label><strong>Amount:</strong></label><br>";
        echo "<input type='number' name='amount' value='1000' step='0.01' required>";
        echo "</div>";
        
        echo "<div class='form-group'>";
        echo "<label><strong>Due Date:</strong></label><br>";
        echo "<input type='date' name='due_date' value='" . date('Y-m-d', strtotime('+30 days')) . "' required>";
        echo "</div>";
        
        echo "<div class='form-group'>";
        echo "<button type='submit' name='add_test_fee'>🧪 Add Test Fee</button>";
        echo "</div>";
        
        echo "</form>";
    } else {
        echo "<h2>❌ No Students Found</h2>";
        echo "<p>Please add students first before adding fees.</p>";
        echo "<p><a href='../students.php'>Add Students</a></p>";
    }
    
    // Show recent fees
    $feesQuery = "SELECT f.*, CONCAT(s.first_name, ' ', s.last_name) as student_name, c.class_name
                  FROM fees f
                  LEFT JOIN students s ON f.student_id = s.id
                  LEFT JOIN classes c ON f.class_id = c.id
                  ORDER BY f.id DESC LIMIT 10";
    $feesResult = $conn->query($feesQuery);
    
    if ($feesResult && $feesResult->num_rows > 0) {
        echo "<h2>📋 Recent Fees</h2>";
        echo "<table>";
        echo "<tr><th>ID</th><th>Student</th><th>Class</th><th>Fee Type</th><th>Amount</th><th>Due Date</th><th>Status</th><th>Created</th></tr>";
        while ($fee = $feesResult->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $fee['id'] . "</td>";
            echo "<td>" . ($fee['student_name'] ? $fee['student_name'] : 'N/A') . "</td>";
            echo "<td>" . ($fee['class_name'] ? $fee['class_name'] : 'N/A') . "</td>";
            echo "<td>" . $fee['fee_type'] . "</td>";
            echo "<td>" . $fee['amount'] . "</td>";
            echo "<td>" . $fee['due_date'] . "</td>";
            echo "<td>" . $fee['status'] . "</td>";
            echo "<td>" . $fee['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<h2>📋 No Fees Found</h2>";
        echo "<p>No fees have been added yet.</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database connection and table structure.</p>";
}
?>

<h2>🔗 Navigation</h2>
<p>
    <a href="fee_management.php">Fee Management</a> | 
    <a href="dashboard.php">Dashboard</a> | 
    <a href="../index.php">Home</a>
</p>

<h2>📝 Notes</h2>
<ul>
    <li>This is a testing page without authentication</li>
    <li>Use this to test if fee addition works at database level</li>
    <li>If this works, the problem is in the main fee management page</li>
    <li>If this doesn't work, there's a database or PHP configuration issue</li>
</ul>

</body>
</html>
