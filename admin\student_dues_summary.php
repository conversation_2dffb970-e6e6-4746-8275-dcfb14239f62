<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Get filter parameters
$classId = $_GET['class_id'] ?? '';
$sessionId = $_GET['session_id'] ?? '';
$departmentId = $_GET['department_id'] ?? '';
$searchTerm = $_GET['search'] ?? '';
$sortBy = $_GET['sort_by'] ?? 'total_due_desc'; // total_due_desc, total_due_asc, name_asc, name_desc
$viewType = $_GET['view'] ?? 'cards'; // cards, table, list

// Build query for student dues summary
$query = "SELECT 
    s.id as student_id,
    s.first_name,
    s.last_name,
    s.student_id as roll,
    c.class_name,
    d.department_name,
    ss.session_name,
    COUNT(f.id) as total_fees,
    SUM(f.amount) as total_amount,
    SUM(f.paid) as total_paid,
    SUM(f.amount - f.paid) as total_due,
    SUM(CASE WHEN f.payment_status = 'due' THEN 1 ELSE 0 END) as unpaid_fees,
    SUM(CASE WHEN f.payment_status = 'partial' THEN 1 ELSE 0 END) as partial_fees,
    SUM(CASE WHEN f.payment_status = 'paid' THEN 1 ELSE 0 END) as paid_fees
FROM students s
LEFT JOIN classes c ON s.class_id = c.id
LEFT JOIN departments d ON s.department_id = d.id
LEFT JOIN sessions ss ON s.session_id = ss.id
LEFT JOIN fees f ON s.id = f.student_id
WHERE 1=1";

$params = [];
$types = "";

// Add filters
if (!empty($classId)) {
    $query .= " AND s.class_id = ?";
    $params[] = $classId;
    $types .= "i";
}

if (!empty($sessionId)) {
    $query .= " AND s.session_id = ?";
    $params[] = $sessionId;
    $types .= "i";
}

if (!empty($departmentId)) {
    $query .= " AND s.department_id = ?";
    $params[] = $departmentId;
    $types .= "i";
}

if (!empty($searchTerm)) {
    $query .= " AND (s.first_name LIKE ? OR s.last_name LIKE ? OR s.student_id LIKE ?)";
    $searchParam = "%$searchTerm%";
    $params[] = $searchParam;
    $params[] = $searchParam;
    $params[] = $searchParam;
    $types .= "sss";
}

$query .= " GROUP BY s.id";

// Add sorting
switch ($sortBy) {
    case 'total_due_asc':
        $query .= " ORDER BY total_due ASC";
        break;
    case 'name_asc':
        $query .= " ORDER BY s.first_name ASC, s.last_name ASC";
        break;
    case 'name_desc':
        $query .= " ORDER BY s.first_name DESC, s.last_name DESC";
        break;
    case 'total_due_desc':
    default:
        $query .= " ORDER BY total_due DESC";
        break;
}

// Execute query
$stmt = $conn->prepare($query);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();

// Calculate overall totals
$overallTotalAmount = 0;
$overallTotalPaid = 0;
$overallTotalDue = 0;
$totalStudents = 0;
$studentsWithDues = 0;

$studentsData = [];
while ($row = $result->fetch_assoc()) {
    // Data validation
    $totalAmount = floatval($row['total_amount'] ?? 0);
    $totalPaid = floatval($row['total_paid'] ?? 0);
    
    // Ensure paid doesn't exceed amount
    if ($totalPaid > $totalAmount) {
        $totalPaid = $totalAmount;
    }
    
    $totalDue = $totalAmount - $totalPaid;
    
    $row['total_amount'] = $totalAmount;
    $row['total_paid'] = $totalPaid;
    $row['total_due'] = $totalDue;
    
    $overallTotalAmount += $totalAmount;
    $overallTotalPaid += $totalPaid;
    $overallTotalDue += $totalDue;
    $totalStudents++;
    
    if ($totalDue > 0) {
        $studentsWithDues++;
    }
    
    $studentsData[] = $row;
}

// Get filter options
$classesQuery = "SELECT * FROM classes ORDER BY class_name";
$classesResult = $conn->query($classesQuery);

$sessionsQuery = "SELECT * FROM sessions ORDER BY session_name DESC";
$sessionsResult = $conn->query($sessionsQuery);

$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departmentsResult = $conn->query($departmentsQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ছাত্র/ছাত্রী ভিত্তিক বকেয়া তালিকা</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .student-card {
            transition: all 0.3s ease;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .student-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .due-amount {
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .due-high {
            color: #dc3545;
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
        }
        
        .due-medium {
            color: #fd7e14;
            background: linear-gradient(135deg, #fff3e0, #ffe0b2);
        }
        
        .due-low {
            color: #ffc107;
            background: linear-gradient(135deg, #fffbf0, #fff8e1);
        }
        
        .no-due {
            color: #28a745;
            background: linear-gradient(135deg, #f1f8e9, #dcedc8);
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        
        .filter-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .student-info {
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .fee-breakdown {
            font-size: 0.8rem;
            margin-top: 10px;
        }
        
        .badge-custom {
            font-size: 0.7rem;
            padding: 0.25rem 0.5rem;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4 bg-white p-3 rounded shadow-sm">
                    <div>
                        <h2 class="mb-0">
                            <i class="fas fa-users text-primary me-2"></i>
                            ছাত্র/ছাত্রী ভিত্তিক বকেয়া তালিকা
                        </h2>
                        <small class="text-muted">প্রতিটি ছাত্রের মোট বকেয়া ও ফি বিবরণ</small>
                    </div>
                    <div>
                        <!-- View Toggle Buttons -->
                        <div class="btn-group me-3" role="group">
                            <input type="radio" class="btn-check" name="viewType" id="cardsView" <?php echo ($viewType == 'cards') ? 'checked' : ''; ?>>
                            <label class="btn btn-outline-primary" for="cardsView" onclick="changeView('cards')">
                                <i class="fas fa-th-large me-1"></i> কার্ড
                            </label>

                            <input type="radio" class="btn-check" name="viewType" id="tableView" <?php echo ($viewType == 'table') ? 'checked' : ''; ?>>
                            <label class="btn btn-outline-primary" for="tableView" onclick="changeView('table')">
                                <i class="fas fa-table me-1"></i> টেবিল
                            </label>

                            <input type="radio" class="btn-check" name="viewType" id="listView" <?php echo ($viewType == 'list') ? 'checked' : ''; ?>>
                            <label class="btn btn-outline-primary" for="listView" onclick="changeView('list')">
                                <i class="fas fa-list me-1"></i> লিস্ট
                            </label>
                        </div>

                        <a href="fee_management.php" class="btn btn-secondary me-2">
                            <i class="fas fa-arrow-left me-1"></i> ফিরে যান
                        </a>
                        <div class="btn-group">
                            <button onclick="printTable()" class="btn btn-primary">
                                <i class="fas fa-print me-1"></i> তালিকা প্রিন্ট করুন
                            </button>
                            <button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown">
                                <span class="visually-hidden">Toggle Dropdown</span>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="printTable()">
                                    <i class="fas fa-table me-2"></i> টেবিল প্রিন্ট করুন
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="printCurrentView()">
                                    <i class="fas fa-print me-2"></i> বর্তমান ভিউ প্রিন্ট করুন
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="exportToCSV()">
                                    <i class="fas fa-file-csv me-2"></i> CSV এক্সপোর্ট করুন
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Overall Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h4><?php echo $totalStudents; ?></h4>
                        <p class="mb-0">মোট ছাত্র/ছাত্রী</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <h4><?php echo $studentsWithDues; ?></h4>
                        <p class="mb-0">বকেয়া আছে</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                        <h4>৳ <?php echo number_format($overallTotalAmount, 2); ?></h4>
                        <p class="mb-0">মোট ফি</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <h4>৳ <?php echo number_format($overallTotalDue, 2); ?></h4>
                        <p class="mb-0">মোট বকেয়া</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="filter-section">
            <form method="GET" action="student_dues_summary.php" id="filterForm">
                <input type="hidden" name="view" value="<?php echo htmlspecialchars($viewType); ?>" id="viewInput">
                <div class="row">
                    <div class="col-md-2">
                        <label class="form-label">শ্রেণী</label>
                        <select name="class_id" class="form-select">
                            <option value="">সকল শ্রেণী</option>
                            <?php while ($class = $classesResult->fetch_assoc()): ?>
                                <option value="<?php echo $class['id']; ?>" <?php echo ($classId == $class['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($class['class_name']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">সেশন</label>
                        <select name="session_id" class="form-select">
                            <option value="">সকল সেশন</option>
                            <?php while ($session = $sessionsResult->fetch_assoc()): ?>
                                <option value="<?php echo $session['id']; ?>" <?php echo ($sessionId == $session['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($session['session_name']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">বিভাগ</label>
                        <select name="department_id" class="form-select">
                            <option value="">সকল বিভাগ</option>
                            <?php while ($department = $departmentsResult->fetch_assoc()): ?>
                                <option value="<?php echo $department['id']; ?>" <?php echo ($departmentId == $department['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($department['department_name']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">সাজানো</label>
                        <select name="sort_by" class="form-select">
                            <option value="total_due_desc" <?php echo ($sortBy == 'total_due_desc') ? 'selected' : ''; ?>>বেশি বকেয়া আগে</option>
                            <option value="total_due_asc" <?php echo ($sortBy == 'total_due_asc') ? 'selected' : ''; ?>>কম বকেয়া আগে</option>
                            <option value="name_asc" <?php echo ($sortBy == 'name_asc') ? 'selected' : ''; ?>>নাম (ক-য)</option>
                            <option value="name_desc" <?php echo ($sortBy == 'name_desc') ? 'selected' : ''; ?>>নাম (য-ক)</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">খুঁজুন</label>
                        <input type="text" name="search" class="form-control" placeholder="নাম বা রোল নম্বর" value="<?php echo htmlspecialchars($searchTerm); ?>">
                    </div>
                    
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Students Data Display -->

        <!-- Cards View -->
        <div class="row" id="cardsContainer" style="display: <?php echo ($viewType == 'cards') ? 'flex' : 'none'; ?>;">
            <?php if (!empty($studentsData)): ?>
                <?php foreach ($studentsData as $student): ?>
                    <?php
                    $dueAmount = $student['total_due'];
                    $cardClass = '';
                    $dueIcon = '';

                    if ($dueAmount <= 0) {
                        $cardClass = 'no-due';
                        $dueIcon = 'fas fa-check-circle';
                    } elseif ($dueAmount <= 500) {
                        $cardClass = 'due-low';
                        $dueIcon = 'fas fa-exclamation-circle';
                    } elseif ($dueAmount <= 1000) {
                        $cardClass = 'due-medium';
                        $dueIcon = 'fas fa-exclamation-triangle';
                    } else {
                        $cardClass = 'due-high';
                        $dueIcon = 'fas fa-times-circle';
                    }
                    ?>

                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card student-card <?php echo $cardClass; ?> h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <div>
                                        <h5 class="card-title mb-1">
                                            <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>
                                        </h5>
                                        <div class="student-info">
                                            <i class="fas fa-id-card me-1"></i> রোল: <?php echo htmlspecialchars($student['roll']); ?><br>
                                            <i class="fas fa-graduation-cap me-1"></i> <?php echo htmlspecialchars($student['class_name'] ?? 'N/A'); ?><br>
                                            <?php if (!empty($student['department_name'])): ?>
                                                <i class="fas fa-building me-1"></i> <?php echo htmlspecialchars($student['department_name']); ?><br>
                                            <?php endif; ?>
                                            <i class="fas fa-calendar me-1"></i> <?php echo htmlspecialchars($student['session_name'] ?? 'N/A'); ?>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <i class="<?php echo $dueIcon; ?> fa-2x"></i>
                                    </div>
                                </div>

                                <div class="row text-center mb-3">
                                    <div class="col-4">
                                        <div class="border-end">
                                            <div class="fw-bold">৳ <?php echo number_format($student['total_amount'], 0); ?></div>
                                            <small>মোট ফি</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="border-end">
                                            <div class="fw-bold text-success">৳ <?php echo number_format($student['total_paid'], 0); ?></div>
                                            <small>প্রদান</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="due-amount">৳ <?php echo number_format($dueAmount, 0); ?></div>
                                        <small>বকেয়া</small>
                                    </div>
                                </div>

                                <div class="fee-breakdown">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>মোট ফি:</span>
                                        <span class="badge bg-primary badge-custom"><?php echo $student['total_fees']; ?> টি</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>পরিশোধিত:</span>
                                        <span class="badge bg-success badge-custom"><?php echo $student['paid_fees']; ?> টি</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>আংশিক:</span>
                                        <span class="badge bg-warning badge-custom"><?php echo $student['partial_fees']; ?> টি</span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>বকেয়া:</span>
                                        <span class="badge bg-danger badge-custom"><?php echo $student['unpaid_fees']; ?> টি</span>
                                    </div>
                                </div>

                                <div class="mt-3 d-flex gap-2">
                                    <a href="fee_memo_report.php?type=student_wise&student_id=<?php echo $student['student_id']; ?>"
                                       class="btn btn-sm btn-outline-primary flex-fill" target="_blank">
                                        <i class="fas fa-file-invoice me-1"></i> মেমো
                                    </a>
                                    <a href="fee_management.php?student_id=<?php echo $student['student_id']; ?>"
                                       class="btn btn-sm btn-outline-secondary flex-fill">
                                        <i class="fas fa-eye me-1"></i> বিস্তারিত
                                    </a>
                                    <?php if ($dueAmount > 0): ?>
                                        <a href="student_payment_form.php?student_id=<?php echo $student['student_id']; ?>"
                                           class="btn btn-sm btn-outline-success flex-fill">
                                            <i class="fas fa-money-bill me-1"></i> পেমেন্ট
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">কোন ছাত্র/ছাত্রী পাওয়া যায়নি</h4>
                            <p class="text-muted">ফিল্টার পরিবর্তন করে আবার চেষ্টা করুন</p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Table View -->
        <div class="row" id="tableContainer" style="display: <?php echo ($viewType == 'table') ? 'block' : 'none'; ?>;">
            <div class="col-12">
                <?php if (!empty($studentsData)): ?>
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>ক্রম</th>
                                            <th>নাম</th>
                                            <th>রোল</th>
                                            <th>শ্রেণী</th>
                                            <th>বিভাগ</th>
                                            <th>সেশন</th>
                                            <th>মোট ফি</th>
                                            <th>প্রদান</th>
                                            <th>বকেয়া</th>
                                            <th>ফি স্ট্যাটাস</th>
                                            <th>অ্যাকশন</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $serial = 1; ?>
                                        <?php foreach ($studentsData as $student): ?>
                                            <?php
                                            $dueAmount = $student['total_due'];
                                            $rowClass = '';

                                            if ($dueAmount <= 0) {
                                                $rowClass = 'table-success';
                                            } elseif ($dueAmount <= 500) {
                                                $rowClass = 'table-warning';
                                            } elseif ($dueAmount <= 1000) {
                                                $rowClass = 'table-info';
                                            } else {
                                                $rowClass = 'table-danger';
                                            }
                                            ?>
                                            <tr class="<?php echo $rowClass; ?>">
                                                <td><?php echo $serial++; ?></td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></strong>
                                                </td>
                                                <td><?php echo htmlspecialchars($student['roll']); ?></td>
                                                <td><?php echo htmlspecialchars($student['class_name'] ?? 'N/A'); ?></td>
                                                <td><?php echo htmlspecialchars($student['department_name'] ?? 'N/A'); ?></td>
                                                <td><?php echo htmlspecialchars($student['session_name'] ?? 'N/A'); ?></td>
                                                <td>৳ <?php echo number_format($student['total_amount'], 2); ?></td>
                                                <td class="text-success">৳ <?php echo number_format($student['total_paid'], 2); ?></td>
                                                <td class="text-danger">
                                                    <strong>৳ <?php echo number_format($dueAmount, 2); ?></strong>
                                                </td>
                                                <td>
                                                    <span class="badge bg-success"><?php echo $student['paid_fees']; ?> পরিশোধিত</span>
                                                    <span class="badge bg-warning"><?php echo $student['partial_fees']; ?> আংশিক</span>
                                                    <span class="badge bg-danger"><?php echo $student['unpaid_fees']; ?> বকেয়া</span>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="fee_memo_report.php?type=student_wise&student_id=<?php echo $student['student_id']; ?>"
                                                           class="btn btn-outline-primary" target="_blank" title="মেমো">
                                                            <i class="fas fa-file-invoice"></i>
                                                        </a>
                                                        <a href="fee_management.php?student_id=<?php echo $student['student_id']; ?>"
                                                           class="btn btn-outline-secondary" title="বিস্তারিত">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <?php if ($dueAmount > 0): ?>
                                                            <a href="student_payment_form.php?student_id=<?php echo $student['student_id']; ?>"
                                                               class="btn btn-outline-success" title="পেমেন্ট">
                                                                <i class="fas fa-money-bill"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">কোন ছাত্র/ছাত্রী পাওয়া যায়নি</h4>
                            <p class="text-muted">ফিল্টার পরিবর্তন করে আবার চেষ্টা করুন</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- List View -->
        <div class="row" id="listContainer" style="display: <?php echo ($viewType == 'list') ? 'block' : 'none'; ?>;">
            <div class="col-12">
                <?php if (!empty($studentsData)): ?>
                    <?php foreach ($studentsData as $student): ?>
                        <?php
                        $dueAmount = $student['total_due'];
                        $listClass = '';
                        $dueIcon = '';

                        if ($dueAmount <= 0) {
                            $listClass = 'border-success';
                            $dueIcon = 'fas fa-check-circle text-success';
                        } elseif ($dueAmount <= 500) {
                            $listClass = 'border-warning';
                            $dueIcon = 'fas fa-exclamation-circle text-warning';
                        } elseif ($dueAmount <= 1000) {
                            $listClass = 'border-info';
                            $dueIcon = 'fas fa-exclamation-triangle text-info';
                        } else {
                            $listClass = 'border-danger';
                            $dueIcon = 'fas fa-times-circle text-danger';
                        }
                        ?>

                        <div class="card mb-3 <?php echo $listClass; ?> border-2">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-3">
                                        <div class="d-flex align-items-center">
                                            <i class="<?php echo $dueIcon; ?> fa-2x me-3"></i>
                                            <div>
                                                <h5 class="mb-1"><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h5>
                                                <small class="text-muted">
                                                    <i class="fas fa-id-card me-1"></i> রোল: <?php echo htmlspecialchars($student['roll']); ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-2">
                                        <small class="text-muted d-block">শ্রেণী</small>
                                        <strong><?php echo htmlspecialchars($student['class_name'] ?? 'N/A'); ?></strong>
                                        <?php if (!empty($student['department_name'])): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($student['department_name']); ?></small>
                                        <?php endif; ?>
                                    </div>

                                    <div class="col-md-2">
                                        <small class="text-muted d-block">সেশন</small>
                                        <strong><?php echo htmlspecialchars($student['session_name'] ?? 'N/A'); ?></strong>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="row text-center">
                                            <div class="col-4">
                                                <small class="text-muted d-block">মোট ফি</small>
                                                <strong>৳ <?php echo number_format($student['total_amount'], 0); ?></strong>
                                            </div>
                                            <div class="col-4">
                                                <small class="text-muted d-block">প্রদান</small>
                                                <strong class="text-success">৳ <?php echo number_format($student['total_paid'], 0); ?></strong>
                                            </div>
                                            <div class="col-4">
                                                <small class="text-muted d-block">বকেয়া</small>
                                                <strong class="text-danger">৳ <?php echo number_format($dueAmount, 0); ?></strong>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-2">
                                        <div class="d-flex flex-column gap-1">
                                            <a href="fee_memo_report.php?type=student_wise&student_id=<?php echo $student['student_id']; ?>"
                                               class="btn btn-sm btn-outline-primary" target="_blank">
                                                <i class="fas fa-file-invoice me-1"></i> মেমো
                                            </a>
                                            <a href="fee_management.php?student_id=<?php echo $student['student_id']; ?>"
                                               class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-eye me-1"></i> বিস্তারিত
                                            </a>
                                            <?php if ($dueAmount > 0): ?>
                                                <a href="student_payment_form.php?student_id=<?php echo $student['student_id']; ?>"
                                                   class="btn btn-sm btn-outline-success">
                                                    <i class="fas fa-money-bill me-1"></i> পেমেন্ট
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-2">
                                    <div class="col-12">
                                        <small class="text-muted">ফি স্ট্যাটাস: </small>
                                        <span class="badge bg-success badge-sm"><?php echo $student['paid_fees']; ?> পরিশোধিত</span>
                                        <span class="badge bg-warning badge-sm"><?php echo $student['partial_fees']; ?> আংশিক</span>
                                        <span class="badge bg-danger badge-sm"><?php echo $student['unpaid_fees']; ?> বকেয়া</span>
                                        <span class="badge bg-primary badge-sm"><?php echo $student['total_fees']; ?> মোট ফি</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">কোন ছাত্র/ছাত্রী পাওয়া যায়নি</h4>
                            <p class="text-muted">ফিল্টার পরিবর্তন করে আবার চেষ্টা করুন</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // View switching functionality
        function changeView(viewType) {
            // Hide all containers
            document.getElementById('cardsContainer').style.display = 'none';
            document.getElementById('tableContainer').style.display = 'none';
            document.getElementById('listContainer').style.display = 'none';

            // Show selected container
            if (viewType === 'cards') {
                document.getElementById('cardsContainer').style.display = 'flex';
            } else {
                document.getElementById(viewType + 'Container').style.display = 'block';
            }

            // Update hidden input
            document.getElementById('viewInput').value = viewType;

            // Update URL without reloading
            const url = new URL(window.location);
            url.searchParams.set('view', viewType);
            window.history.replaceState({}, '', url);
        }

        // Auto-submit form on filter change
        document.querySelectorAll('select[name="class_id"], select[name="session_id"], select[name="department_id"], select[name="sort_by"]').forEach(function(select) {
            select.addEventListener('change', function() {
                this.form.submit();
            });
        });

        // Print functionality
        window.addEventListener('beforeprint', function() {
            document.body.classList.add('printing');

            // Set print date
            const now = new Date();
            const printDate = now.toLocaleDateString('bn-BD') + ' ' + now.toLocaleTimeString('bn-BD');
            document.body.setAttribute('data-print-date', printDate);

            // Force table view for printing
            document.getElementById('cardsContainer').style.display = 'none';
            document.getElementById('listContainer').style.display = 'none';
            document.getElementById('tableContainer').style.display = 'block';
        });

        window.addEventListener('afterprint', function() {
            document.body.classList.remove('printing');

            // Restore original view
            const currentView = '<?php echo $viewType; ?>';
            changeView(currentView);
        });

        // Print table function
        function printTable() {
            // Store current view
            const currentView = '<?php echo $viewType; ?>';

            // Switch to table view temporarily
            changeView('table');

            // Wait a moment for view to change, then print
            setTimeout(function() {
                window.print();

                // Restore original view after print dialog closes
                setTimeout(function() {
                    changeView(currentView);
                }, 1000);
            }, 100);
        }

        // Print current view function
        function printCurrentView() {
            window.print();
        }

        // Export to CSV function
        function exportToCSV() {
            const table = document.querySelector('#tableContainer table');
            if (!table) {
                alert('টেবিল ভিউতে যান CSV এক্সপোর্ট করার জন্য');
                return;
            }

            let csv = [];
            const rows = table.querySelectorAll('tr');

            for (let i = 0; i < rows.length; i++) {
                const row = [];
                const cols = rows[i].querySelectorAll('td, th');

                for (let j = 0; j < cols.length - 1; j++) { // Exclude last column (actions)
                    let cellText = cols[j].innerText.replace(/"/g, '""');
                    row.push('"' + cellText + '"');
                }
                csv.push(row.join(','));
            }

            const csvContent = csv.join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');

            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', 'student_dues_summary.csv');
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }

        // Initialize view on page load
        document.addEventListener('DOMContentLoaded', function() {
            const currentView = '<?php echo $viewType; ?>';
            changeView(currentView);
        });
    </script>

    <style>
        .badge-sm {
            font-size: 0.7rem;
            padding: 0.2rem 0.4rem;
        }

        @media print {
            /* Hide all non-essential elements */
            .btn, .filter-section, .btn-group,
            .d-flex.justify-content-between.align-items-center,
            .row:first-child,
            .row:nth-child(2),
            .card-header {
                display: none !important;
            }

            /* Hide navigation and header */
            .container-fluid > .row:first-child,
            .container-fluid > .row:nth-child(2) {
                display: none !important;
            }

            /* Show only the active view */
            #cardsContainer, #listContainer {
                display: none !important;
            }

            /* Force table view to show */
            #tableContainer {
                display: block !important;
            }

            /* Print table styling */
            #tableContainer .card {
                border: none !important;
                box-shadow: none !important;
            }

            #tableContainer .card-body {
                padding: 0 !important;
            }

            #tableContainer table {
                font-size: 10px !important;
                width: 100% !important;
            }

            #tableContainer th,
            #tableContainer td {
                padding: 4px !important;
                border: 1px solid #000 !important;
            }

            #tableContainer .btn-group {
                display: none !important;
            }

            /* Hide action column */
            #tableContainer th:last-child,
            #tableContainer td:last-child {
                display: none !important;
            }

            /* Print header */
            body::before {
                content: "ছাত্র/ছাত্রী ভিত্তিক বকেয়া তালিকা";
                display: block;
                text-align: center;
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 2px solid #000;
            }

            /* Print footer */
            body::after {
                content: "প্রিন্ট তারিখ: " attr(data-print-date);
                display: block;
                text-align: center;
                font-size: 10px;
                margin-top: 20px;
                padding-top: 10px;
                border-top: 1px solid #000;
            }

            /* Page settings */
            body {
                font-size: 12px !important;
                margin: 0 !important;
                padding: 10px !important;
            }

            /* Remove margins and padding from containers */
            .container-fluid {
                margin: 0 !important;
                padding: 0 !important;
                max-width: none !important;
            }

            /* Table specific print styles */
            .table-responsive {
                overflow: visible !important;
            }

            /* Color adjustments for print */
            .table-success { background-color: #d4edda !important; }
            .table-warning { background-color: #fff3cd !important; }
            .table-info { background-color: #d1ecf1 !important; }
            .table-danger { background-color: #f8d7da !important; }

            /* Badge styles for print */
            .badge {
                border: 1px solid #000 !important;
                color: #000 !important;
                background-color: transparent !important;
            }
        }
    </style>
</body>
</html>
