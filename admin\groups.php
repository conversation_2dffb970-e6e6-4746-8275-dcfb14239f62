<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Handle group actions
$successMessage = '';
$errorMessage = '';

// Check if groups table exists
$groupsTableExists = $conn->query("SHOW TABLES LIKE 'groups'")->num_rows > 0;

// Create groups table if it doesn't exist
if (!$groupsTableExists) {
    $tableQuery = "CREATE TABLE IF NOT EXISTS groups (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        group_name VARCHAR(255) NOT NULL,
        is_active TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $conn->query($tableQuery);
    $groupsTableExists = true;
}

// Check if group_code column exists
$checkColumnQuery = "SHOW COLUMNS FROM groups LIKE 'group_code'";
$columnExists = $conn->query($checkColumnQuery)->num_rows > 0;

if ($columnExists) {
    // Alter the table to remove the group_code column
    $alterQuery = "ALTER TABLE groups DROP COLUMN group_code";
    $conn->query($alterQuery);
}

// Check if description column exists
$checkColumnQuery = "SHOW COLUMNS FROM groups LIKE 'description'";
$columnExists = $conn->query($checkColumnQuery)->num_rows > 0;

if ($columnExists) {
    // Alter the table to remove the description column
    $alterQuery = "ALTER TABLE groups DROP COLUMN description";
    $conn->query($alterQuery);
}

// Insert default groups if they don't exist
$defaultGroups = [
    'বিজ্ঞান',
    'মানবিক',
    'ব্যবসায়',
    'সাধারণ'
];

// Remove old default groups if they exist
$oldGroupsToRemove = ['বাণিজ্য', 'সকল'];
foreach ($oldGroupsToRemove as $oldGroupName) {
    $checkQuery = "SELECT id FROM groups WHERE group_name = '$oldGroupName'";
    $result = $conn->query($checkQuery);
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $oldGroupId = $row['id'];

        // Delete from subject_groups
        $deleteSubjectGroupsQuery = "DELETE FROM subject_groups WHERE group_id = $oldGroupId";
        $conn->query($deleteSubjectGroupsQuery);

        // Delete the group
        $deleteGroupQuery = "DELETE FROM groups WHERE id = $oldGroupId";
        $conn->query($deleteGroupQuery);
    }
}

foreach ($defaultGroups as $groupName) {
    $checkQuery = "SELECT COUNT(*) as count FROM groups WHERE group_name = '$groupName'";
    $result = $conn->query($checkQuery);
    $row = $result->fetch_assoc();

    if ($row['count'] == 0) {
        $insertQuery = "INSERT INTO groups (group_name) VALUES ('$groupName')";
        $conn->query($insertQuery);
    }
}

// Add new group
if (isset($_POST['add_group'])) {
    $group_name = trim($_POST['group_name'] ?? '');
    $is_active = isset($_POST['is_active']) ? 1 : 0;

    // Validate input
    if (empty($group_name)) {
        $errorMessage = "গ্রুপের নাম অবশ্যই পূরণ করতে হবে!";
    } else {
        // Check if group name already exists
        $checkQuery = "SELECT COUNT(*) as count FROM groups WHERE group_name = '$group_name'";
        $result = $conn->query($checkQuery);
        $row = $result->fetch_assoc();

        if ($row['count'] > 0) {
            $errorMessage = "এই গ্রুপের নাম ইতিমধ্যে বিদ্যমান!";
        } else {
            // Insert new group
            $insertQuery = "INSERT INTO groups (group_name, is_active) VALUES ('$group_name', $is_active)";

            if ($conn->query($insertQuery)) {
                header("Location: groups.php?success=1");
                exit();
            } else {
                $errorMessage = "গ্রুপ যোগ করতে সমস্যা হয়েছে: " . $conn->error;
            }
        }
    }
}

// Update group
if (isset($_POST['update_group'])) {
    $group_id = isset($_POST['group_id']) ? (int)$_POST['group_id'] : 0;
    $group_name = trim($_POST['group_name'] ?? '');
    $is_active = isset($_POST['is_active']) ? 1 : 0;

    // Validate input
    if (empty($group_name)) {
        header("Location: groups.php?error=" . urlencode("গ্রুপের নাম অবশ্যই পূরণ করতে হবে!"));
        exit();
    }

    if ($group_id <= 0) {
        header("Location: groups.php?error=" . urlencode("অবৈধ গ্রুপ আইডি!"));
        exit();
    }

    // Check if group exists
    $checkExistsQuery = "SELECT * FROM groups WHERE id = $group_id";
    $result = $conn->query($checkExistsQuery);

    if ($result->num_rows == 0) {
        header("Location: groups.php?error=" . urlencode("গ্রুপ খুঁজে পাওয়া যায়নি!"));
        exit();
    }

    // Check if this is a default group and trying to change name
    $existingGroup = $result->fetch_assoc();
    $defaultGroups = ['বিজ্ঞান', 'মানবিক', 'ব্যবসায়', 'সাধারণ'];

    // Prevent changing default group names
    if (in_array($existingGroup['group_name'], $defaultGroups) &&
        $existingGroup['group_name'] != $group_name) {
        header("Location: groups.php?error=" . urlencode("ডিফল্ট গ্রুপের নাম পরিবর্তন করা যাবে না!"));
        exit();
    }

    // Check if group name already exists for other groups
    $checkQuery = "SELECT * FROM groups WHERE group_name = '$group_name' AND id != $group_id";
    $result = $conn->query($checkQuery);

    if ($result->num_rows > 0) {
        header("Location: groups.php?error=" . urlencode("এই গ্রুপের নাম ইতিমধ্যে বিদ্যমান!"));
        exit();
    }

    // Update group
    $updateQuery = "UPDATE groups SET group_name = '$group_name', is_active = $is_active WHERE id = $group_id";

    if ($conn->query($updateQuery)) {
        header("Location: groups.php?success=2");
        exit();
    } else {
        header("Location: groups.php?error=" . urlencode("গ্রুপ আপডেট করতে সমস্যা হয়েছে: " . $conn->error));
        exit();
    }
}

// Delete group
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $group_id = $_GET['delete'];

    // Check if this is a default group
    $checkQuery = "SELECT group_name FROM groups WHERE id = $group_id";
    $result = $conn->query($checkQuery);
    $group = $result->fetch_assoc();

    $defaultGroups = ['বিজ্ঞান', 'মানবিক', 'ব্যবসায়', 'সাধারণ'];

    if (in_array($group['group_name'], $defaultGroups)) {
        header("Location: groups.php?error=" . urlencode("ডিফল্ট গ্রুপ মুছা যাবে না!"));
        exit();
    }

    // Check if group is used in subject_groups
    $checkQuery = "SELECT COUNT(*) as count FROM subject_groups WHERE group_id = $group_id";
    $result = $conn->query($checkQuery);
    $row = $result->fetch_assoc();

    if ($row['count'] > 0) {
        header("Location: groups.php?error=" . urlencode("এই গ্রুপটি বিষয়ের সাথে সম্পর্কিত! আগে সেই সম্পর্ক মুছে ফেলুন।"));
        exit();
    }

    // Delete the group
    $deleteQuery = "DELETE FROM groups WHERE id = $group_id";

    if ($conn->query($deleteQuery)) {
        header("Location: groups.php?success=3");
        exit();
    } else {
        header("Location: groups.php?error=" . urlencode("গ্রুপ মুছতে সমস্যা হয়েছে: " . $conn->error));
        exit();
    }
}

// Toggle group status
if (isset($_GET['toggle']) && is_numeric($_GET['toggle'])) {
    $group_id = $_GET['toggle'];

    // Get current status
    $statusQuery = "SELECT is_active FROM groups WHERE id = $group_id";
    $result = $conn->query($statusQuery);

    if ($result->num_rows > 0) {
        $group = $result->fetch_assoc();
        $new_status = $group['is_active'] ? 0 : 1;

        // Update status
        $updateQuery = "UPDATE groups SET is_active = $new_status WHERE id = $group_id";

        if ($conn->query($updateQuery)) {
            header("Location: groups.php?success=2");
            exit();
        } else {
            header("Location: groups.php?error=" . urlencode("অবস্থা পরিবর্তন করতে সমস্যা হয়েছে: " . $conn->error));
            exit();
        }
    }
}

// Check for success message from redirect
if (isset($_GET['success'])) {
    switch ($_GET['success']) {
        case '1':
            $successMessage = "গ্রুপ সফলভাবে যোগ করা হয়েছে!";
            break;
        case '2':
            $successMessage = "গ্রুপ সফলভাবে আপডেট করা হয়েছে!";
            break;
        case '3':
            $successMessage = "গ্রুপ সফলভাবে মুছে ফেলা হয়েছে!";
            break;
        default:
            $successMessage = "অপারেশন সফল হয়েছে!";
    }
}

// Check for error message from redirect
if (isset($_GET['error'])) {
    $errorMessage = urldecode($_GET['error']);
}

// Get all groups
$query = "SELECT * FROM groups ORDER BY id";
$groups = $conn->query($query);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>গ্রুপ ব্যবস্থাপনা - কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .card-hover {
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .group-card {
            border-left: 4px solid;
            transition: all 0.3s ease;
        }
        .group-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .group-card.active {
            border-left-color: #198754;
        }
        .group-card.inactive {
            border-left-color: #dc3545;
        }
        .search-container {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }
        .action-btn {
            width: 32px;
            height: 32px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            margin: 0 2px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="groups.php">
                            <i class="fas fa-layer-group me-2"></i> গ্রুপ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sessions.php">
                            <i class="fas fa-calendar-alt me-2"></i> সেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>গ্রুপ ব্যবস্থাপনা</h2>
                        <p class="text-muted">সকল গ্রুপ দেখুন, যোগ করুন, সম্পাদনা করুন এবং ব্যবস্থাপনা করুন</p>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addGroupModal">
                            <i class="fas fa-plus-circle me-2"></i>নতুন গ্রুপ
                        </button>
                    </div>
                </div>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i><?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php
                // Check if required tables exist
                if (!$groupsTableExists):
                ?>
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <h5 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i>টেবিল সেটআপ প্রয়োজন!</h5>
                        <p>গ্রুপ ব্যবস্থাপনার জন্য প্রয়োজনীয় টেবিল তৈরি করা হয়নি। সম্পূর্ণ ফাংশনালিটি পেতে টেবিল সেটআপ করুন।</p>
                        <hr>
                        <a href="create_groups_tables.php" class="btn btn-warning">
                            <i class="fas fa-database me-2"></i>টেবিল সেটআপ করুন
                        </a>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Groups Table -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-users me-2"></i>গ্রুপ তালিকা</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th width="5%">#</th>
                                        <th>গ্রুপের নাম</th>
                                        <th width="15%">অবস্থা</th>
                                        <th width="20%" class="text-center">অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($groups && $groups->num_rows > 0): ?>
                                        <?php $i = 1; while ($group = $groups->fetch_assoc()): ?>
                                            <tr>
                                                <td><?php echo $i++; ?></td>
                                                <td><?php echo htmlspecialchars($group['group_name']); ?></td>
                                                <td>
                                                    <?php if ($group['is_active']): ?>
                                                        <span class="badge bg-success">সক্রিয়</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">নিষ্ক্রিয়</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="text-center">
                                                    <button class="btn btn-sm btn-info action-btn edit-group-btn"
                                                            title="সম্পাদনা করুন"
                                                            data-id="<?php echo $group['id']; ?>"
                                                            data-name="<?php echo htmlspecialchars($group['group_name']); ?>"
                                                            data-original-name="<?php echo htmlspecialchars($group['group_name']); ?>"
                                                            data-active="<?php echo $group['is_active']; ?>"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#editGroupModal">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <a href="groups.php?toggle=<?php echo $group['id']; ?>" class="btn btn-sm <?php echo $group['is_active'] ? 'btn-warning' : 'btn-success'; ?> action-btn" title="<?php echo $group['is_active'] ? 'নিষ্ক্রিয় করুন' : 'সক্রিয় করুন'; ?>">
                                                        <i class="fas <?php echo $group['is_active'] ? 'fa-times' : 'fa-check'; ?>"></i>
                                                    </a>
                                                    <?php
                                                    $defaultGroups = ['বিজ্ঞান', 'মানবিক', 'ব্যবসায়', 'সাধারণ'];

                                                    if (!in_array($group['group_name'], $defaultGroups)):
                                                    ?>
                                                    <a href="groups.php?delete=<?php echo $group['id']; ?>" class="btn btn-sm btn-danger action-btn" title="মুছে ফেলুন" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই গ্রুপটি মুছতে চান?');">
                                                        <i class="fas fa-trash-alt"></i>
                                                    </a>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="4" class="text-center py-4">
                                                <i class="fas fa-search fa-2x mb-3 text-muted"></i>
                                                <p class="mb-0">কোন গ্রুপ পাওয়া যায়নি</p>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card card-hover h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-link fa-3x text-primary mb-3"></i>
                                <h5>বিষয়-গ্রুপ সংযোগ</h5>
                                <p>বিষয়গুলিকে গ্রুপের সাথে সংযুক্ত করুন</p>
                                <a href="subject_groups.php" class="btn btn-outline-primary">সংযোগ ব্যবস্থাপনা</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card card-hover h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-book-open fa-3x text-success mb-3"></i>
                                <h5>বিষয় ব্যবস্থাপনা</h5>
                                <p>বিষয় যোগ করুন, সম্পাদনা করুন এবং ব্যবস্থাপনা করুন</p>
                                <a href="subjects.php" class="btn btn-outline-success">বিষয় ব্যবস্থাপনা</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card card-hover h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-file-export fa-3x text-info mb-3"></i>
                                <h5>গ্রুপ এক্সপোর্ট</h5>
                                <p>গ্রুপের তালিকা এক্সপোর্ট করুন (PDF/Excel)</p>
                                <a href="group_export.php" class="btn btn-outline-info">এক্সপোর্ট করুন</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Group Modal -->
    <div class="modal fade" id="addGroupModal" tabindex="-1" aria-labelledby="addGroupModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="addGroupModalLabel"><i class="fas fa-plus-circle me-2"></i>নতুন গ্রুপ যোগ করুন</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="groups.php">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="group_name" class="form-label">গ্রুপের নাম <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="group_name" name="group_name" required>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                <label class="form-check-label" for="is_active">সক্রিয়</label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>বাতিল করুন
                        </button>
                        <button type="submit" name="add_group" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>সংরক্ষণ করুন
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Group Modal -->
    <div class="modal fade" id="editGroupModal" tabindex="-1" aria-labelledby="editGroupModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title" id="editGroupModalLabel"><i class="fas fa-edit me-2"></i>গ্রুপ সম্পাদনা করুন</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="groups.php" id="editGroupForm">
                    <div class="modal-body">
                        <input type="hidden" id="edit_group_id" name="group_id">
                        <div class="mb-3">
                            <label for="edit_group_name" class="form-label">গ্রুপের নাম <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit_group_name" name="group_name" required>
                            <div id="default-group-warning" class="alert alert-warning mt-2" style="display: none;">
                                <i class="fas fa-exclamation-triangle me-2"></i>ডিফল্ট গ্রুপের নাম পরিবর্তন করা যাবে না!
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                                <label class="form-check-label" for="edit_is_active">সক্রিয়</label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>বাতিল করুন
                        </button>
                        <button type="submit" name="update_group" class="btn btn-info" id="updateGroupBtn">
                            <i class="fas fa-save me-2"></i>আপডেট করুন
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert-dismissible');
                alerts.forEach(function(alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);

            // Handle edit group button click
            const editButtons = document.querySelectorAll('.edit-group-btn');
            editButtons.forEach(function(button) {
                button.addEventListener('click', function() {
                    const id = this.getAttribute('data-id');
                    const name = this.getAttribute('data-name');
                    const originalName = this.getAttribute('data-original-name');
                    const active = this.getAttribute('data-active') === '1';

                    const idField = document.getElementById('edit_group_id');
                    const nameField = document.getElementById('edit_group_name');
                    const activeField = document.getElementById('edit_is_active');

                    if (idField) idField.value = id;
                    if (nameField) {
                        nameField.value = name;
                        nameField.setAttribute('data-original-name', originalName);
                    }
                    if (activeField) activeField.checked = active;

                    // Check if this is a default group
                    const defaultGroups = ['বিজ্ঞান', 'মানবিক', 'ব্যবসায়', 'সাধারণ'];

                    if (defaultGroups.includes(originalName)) {
                        // Disable name field for default groups
                        nameField.readOnly = true;
                        nameField.classList.add('bg-light');

                        // Add warning message
                        const warningMsg = document.getElementById('default-group-warning');
                        if (warningMsg) {
                            warningMsg.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>ডিফল্ট গ্রুপের নাম পরিবর্তন করা যাবে না!';
                            warningMsg.className = 'alert alert-warning mt-2';
                            warningMsg.style.display = 'block';
                        }
                    } else {
                        // Enable name field for non-default groups
                        nameField.readOnly = false;
                        nameField.classList.remove('bg-light');

                        // Hide warning message
                        const warningMsg = document.getElementById('default-group-warning');
                        if (warningMsg) {
                            warningMsg.style.display = 'none';
                        }
                    }
                });
            });

            // Handle form submission
            const editGroupForm = document.getElementById('editGroupForm');
            if (editGroupForm) {
                editGroupForm.addEventListener('submit', function(e) {
                    const idField = document.getElementById('edit_group_id');
                    const nameField = document.getElementById('edit_group_name');

                    if (!idField || !idField.value || idField.value === '0') {
                        e.preventDefault();
                        alert('গ্রুপ আইডি পাওয়া যায়নি! পুনরায় চেষ্টা করুন।');
                        return false;
                    }

                    // Check if this is a default group
                    const defaultGroups = ['বিজ্ঞান', 'মানবিক', 'ব্যবসায়', 'সাধারণ'];
                    const originalName = nameField.getAttribute('data-original-name');

                    // Prevent changing default group names
                    if (defaultGroups.includes(originalName) && originalName !== nameField.value) {
                        e.preventDefault();
                        alert('ডিফল্ট গ্রুপের নাম পরিবর্তন করা যাবে না!');
                        nameField.value = originalName;
                        return false;
                    }

                    return true;
                });
            }
        });
    </script>
</body>
</html>
