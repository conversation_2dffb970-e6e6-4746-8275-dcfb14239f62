<?php
// Database Connection
require_once 'includes/dbh.inc.php';

// Default admin credentials
$adminUsername = "admin";
$adminPassword = "admin123"; // Default password
$adminType = "admin";

// Check if form is submitted for custom password
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['new_password'])) {
    $adminPassword = $_POST['new_password'];
}

// Generate new password hash
$passwordHash = password_hash($adminPassword, PASSWORD_DEFAULT);

echo "<h1>Admin User Fix Tool</h1>";

// Check if admin user exists
$sql = "SELECT * FROM users WHERE username=? AND user_type=?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("ss", $adminUsername, $adminType);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    // Update existing admin user
    $admin = $result->fetch_assoc();
    $sql = "UPDATE users SET password=? WHERE id=?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("si", $passwordHash, $admin['id']);
    
    if ($stmt->execute()) {
        echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
        echo "<h3>Admin Password Updated Successfully</h3>";
        echo "<p>Admin username: <strong>admin</strong></p>";
        echo "<p>New admin password: <strong>" . htmlspecialchars($adminPassword) . "</strong></p>";
        echo "</div>";
    } else {
        echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
        echo "<h3>Password Update Failed</h3>";
        echo "<p>Error: " . $stmt->error . "</p>";
        echo "</div>";
    }
} else {
    // Create new admin user
    $sql = "INSERT INTO users (username, password, user_type) VALUES (?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sss", $adminUsername, $passwordHash, $adminType);
    
    if ($stmt->execute()) {
        echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
        echo "<h3>Admin User Created Successfully</h3>";
        echo "<p>Admin username: <strong>admin</strong></p>";
        echo "<p>Admin password: <strong>" . htmlspecialchars($adminPassword) . "</strong></p>";
        echo "</div>";
    } else {
        echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
        echo "<h3>Admin User Creation Failed</h3>";
        echo "<p>Error: " . $stmt->error . "</p>";
        echo "</div>";
    }
}

// Custom password form
echo "<h3>Set Custom Admin Password</h3>";
echo "<form method='post' action=''>";
echo "<div style='margin-bottom: 15px;'>";
echo "<label for='new_password' style='display: block; margin-bottom: 5px;'>New Password:</label>";
echo "<input type='text' id='new_password' name='new_password' style='padding: 8px; width: 300px;' required>";
echo "</div>";
echo "<button type='submit' style='background-color: #007bff; color: white; border: none; padding: 10px 15px; cursor: pointer;'>Update Password</button>";
echo "</form>";

echo "<div style='margin-top: 20px;'>";
echo "<a href='check_admin.php' style='margin-right: 15px;'>Check Admin User</a>";
echo "<a href='index.php'>Go to Login Page</a>";
echo "</div>";

$stmt->close();
$conn->close();
?> 