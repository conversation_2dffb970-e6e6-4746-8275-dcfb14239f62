<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Database connection parameters
$servername = "127.0.0.1";
$username = "root";
$password = "";

// Create connection without database
$conn = new mysqli($servername, $username, $password);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "<h1>Complete Database Setup</h1>";
echo "<pre>";

// Create database if it doesn't exist
$dbname = "zfaw";
$sql = "CREATE DATABASE IF NOT EXISTS $dbname CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
if ($conn->query($sql) === TRUE) {
    echo "Database '$dbname' created or already exists\n";
} else {
    echo "Error creating database: " . $conn->error . "\n";
    die();
}

// Select the database
$conn->select_db($dbname);

// Create sessions table
$sql = "CREATE TABLE IF NOT EXISTS sessions (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    session_name VARCHAR(100) NOT NULL,
    start_date DATE DEFAULT NULL,
    end_date DATE DEFAULT NULL,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($sql) === TRUE) {
    echo "Table 'sessions' created or already exists\n";
} else {
    echo "Error creating sessions table: " . $conn->error . "\n";
}

// Create classes table
$sql = "CREATE TABLE IF NOT EXISTS classes (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    class_name VARCHAR(100) NOT NULL,
    department_id INT(11) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($sql) === TRUE) {
    echo "Table 'classes' created or already exists\n";
} else {
    echo "Error creating classes table: " . $conn->error . "\n";
}

// Create departments table
$sql = "CREATE TABLE IF NOT EXISTS departments (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    department_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($sql) === TRUE) {
    echo "Table 'departments' created or already exists\n";
} else {
    echo "Error creating departments table: " . $conn->error . "\n";
}

// Create students table
$sql = "CREATE TABLE IF NOT EXISTS students (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id VARCHAR(20) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    roll_no VARCHAR(20) DEFAULT NULL,
    class_id INT(11) NOT NULL,
    session_id INT(11) NOT NULL,
    department_id INT(11) DEFAULT NULL,
    gender ENUM('male', 'female', 'other') DEFAULT NULL,
    dob DATE DEFAULT NULL,
    email VARCHAR(100) DEFAULT NULL,
    phone VARCHAR(20) DEFAULT NULL,
    address TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($sql) === TRUE) {
    echo "Table 'students' created or already exists\n";
} else {
    echo "Error creating students table: " . $conn->error . "\n";
}



// Check if sessions table is empty
$result = $conn->query("SELECT COUNT(*) as count FROM sessions");
$row = $result->fetch_assoc();
if ($row['count'] == 0) {
    // Insert default sessions
    $currentYear = date('Y');
    $defaultSessions = [
        "$currentYear-" . ($currentYear + 1),
        ($currentYear - 1) . "-$currentYear",
        ($currentYear + 1) . "-" . ($currentYear + 2),
        ($currentYear - 2) . "-" . ($currentYear - 1)
    ];

    $insertSessionQuery = "INSERT INTO sessions (session_name) VALUES (?)";
    $stmt = $conn->prepare($insertSessionQuery);

    foreach ($defaultSessions as $sessionName) {
        $stmt->bind_param("s", $sessionName);
        $stmt->execute();
    }

    echo "Added default sessions\n";
}

// Check if classes table is empty
$result = $conn->query("SELECT COUNT(*) as count FROM classes");
$row = $result->fetch_assoc();
if ($row['count'] == 0) {
    // Insert default classes
    $defaultClasses = [
        "ষষ্ঠ শ্রেণী",
        "সপ্তম শ্রেণী",
        "অষ্টম শ্রেণী",
        "নবম শ্রেণী",
        "দশম শ্রেণী",
        "একাদশ শ্রেণী",
        "দ্বাদশ শ্রেণী"
    ];

    $insertClassQuery = "INSERT INTO classes (class_name) VALUES (?)";
    $stmt = $conn->prepare($insertClassQuery);

    foreach ($defaultClasses as $className) {
        $stmt->bind_param("s", $className);
        $stmt->execute();
    }

    echo "Added default classes\n";
}

// Check if departments table is empty
$result = $conn->query("SELECT COUNT(*) as count FROM departments");
$row = $result->fetch_assoc();
if ($row['count'] == 0) {
    // Insert default departments
    $defaultDepartments = [
        "বিজ্ঞান",
        "মানবিক",
        "বাণিজ্য",
        "সাধারণ"
    ];

    $insertDeptQuery = "INSERT INTO departments (department_name) VALUES (?)";
    $stmt = $conn->prepare($insertDeptQuery);

    foreach ($defaultDepartments as $deptName) {
        $stmt->bind_param("s", $deptName);
        $stmt->execute();
    }

    echo "Added default departments\n";
}

// Check if students table is empty
$result = $conn->query("SELECT COUNT(*) as count FROM students");
$row = $result->fetch_assoc();
if ($row['count'] == 0) {
    // Get first session
    $sessionResult = $conn->query("SELECT id FROM sessions ORDER BY id LIMIT 1");
    $sessionId = $sessionResult->fetch_assoc()['id'];

    // Insert sample students for each class
    $classesResult = $conn->query("SELECT id FROM classes ORDER BY id");
    while ($class = $classesResult->fetch_assoc()) {
        $classId = $class['id'];

        // Get first department
        $deptResult = $conn->query("SELECT id FROM departments ORDER BY id LIMIT 1");
        $deptId = $deptResult->fetch_assoc()['id'];

        // Sample students for this class
        $sampleStudents = [
            ['Karim', 'Ahmed', '101', 'male', '<EMAIL>', '01712345678'],
            ['Fatima', 'Begum', '102', 'female', '<EMAIL>', '01712345679'],
            ['Rahim', 'Khan', '103', 'male', '<EMAIL>', '01712345680']
        ];

        $insertStudentQuery = "INSERT INTO students (student_id, first_name, last_name, roll_no, class_id, session_id, department_id, gender, email, phone) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($insertStudentQuery);

        foreach ($sampleStudents as $student) {
            // Generate a random 6-digit student ID
            $studentId = 'S' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
            $rollNo = $student[2];

            $stmt->bind_param("ssssiiisss",
                $studentId,
                $student[0], // first_name
                $student[1], // last_name
                $rollNo,     // roll_no
                $classId,    // class_id
                $sessionId,  // session_id
                $deptId,     // department_id
                $student[3], // gender
                $student[4], // email
                $student[5]  // phone
            );

            $stmt->execute();
        }
    }

    echo "Added sample students for all classes\n";
}

// Display current data
echo "\nCurrent Sessions:\n";
$result = $conn->query("SELECT * FROM sessions ORDER BY id");
while ($row = $result->fetch_assoc()) {
    echo "ID: {$row['id']}, Name: {$row['session_name']}\n";
}

echo "\nCurrent Classes:\n";
$result = $conn->query("SELECT * FROM classes ORDER BY id");
while ($row = $result->fetch_assoc()) {
    echo "ID: {$row['id']}, Name: {$row['class_name']}\n";
}

echo "\nCurrent Departments:\n";
$result = $conn->query("SELECT * FROM departments ORDER BY id");
while ($row = $result->fetch_assoc()) {
    echo "ID: {$row['id']}, Name: {$row['department_name']}\n";
}

echo "\nSample Students (first 10):\n";
$result = $conn->query("SELECT s.id, s.student_id, s.first_name, s.last_name, c.class_name, d.department_name, ss.session_name
                        FROM students s
                        JOIN classes c ON s.class_id = c.id
                        JOIN departments d ON s.department_id = d.id
                        JOIN sessions ss ON s.session_id = ss.id
                        ORDER BY s.id LIMIT 10");
while ($row = $result->fetch_assoc()) {
    echo "ID: {$row['id']}, Name: {$row['first_name']} {$row['last_name']}, ";
    echo "Class: {$row['class_name']}, Dept: {$row['department_name']}, Session: {$row['session_name']}\n";
}

echo "</pre>";
echo "<p>Database setup completed. You can now <a href='admin/dashboard.php'>go to the dashboard</a>.</p>";

// Close connection
$conn->close();
?>
