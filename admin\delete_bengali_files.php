<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

$files_to_delete = [
    __DIR__ . '/bengali_certificate.php',
    __DIR__ . '/certificate_templates/bengali_certificate.php'
];

$deleted_files = [];
$failed_files = [];

foreach ($files_to_delete as $file) {
    if (file_exists($file)) {
        if (unlink($file)) {
            $deleted_files[] = $file;
        } else {
            $failed_files[] = $file;
        }
    } else {
        // File doesn't exist, consider it as already deleted
        $deleted_files[] = $file;
    }
}

if (count($failed_files) > 0) {
    $_SESSION['error_message'] = "কিছু ফাইল মুছতে সমস্যা হয়েছে: " . implode(", ", $failed_files);
} else {
    $_SESSION['success_message'] = "সমস্ত বাংলা সার্টিফিকেট ফাইল সফলভাবে মুছে ফেলা হয়েছে।";
}

// Redirect back to the removal page
header("Location: remove_bengali_certificates.php");
exit();
?>
