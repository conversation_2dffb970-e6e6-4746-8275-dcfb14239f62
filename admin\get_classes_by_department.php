<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized access']);
    exit();
}

require_once '../includes/dbh.inc.php';

// Get department ID from request
$departmentId = isset($_GET['department_id']) ? intval($_GET['department_id']) : 0;

if ($departmentId <= 0) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Invalid department ID', 'classes' => []]);
    exit();
}

// Get classes for the specified department
$classesQuery = "SELECT id, class_name FROM classes WHERE department_id = ? ORDER BY class_name";
$stmt = $conn->prepare($classesQuery);
$stmt->bind_param("i", $departmentId);
$stmt->execute();
$result = $stmt->get_result();

$classes = [];
if ($result && $result->num_rows > 0) {
    while ($class = $result->fetch_assoc()) {
        $classes[] = [
            'id' => $class['id'],
            'class_name' => $class['class_name']
        ];
    }
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode(['classes' => $classes]);
