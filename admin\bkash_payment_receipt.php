<?php
session_start();
require_once '../includes/dbh.inc.php';
require_once '../includes/bkash_config.php';
require_once '../includes/bkash_functions.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Check if payment ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = 'পেমেন্ট আইডি প্রদান করা হয়নি!';
    header('Location: bkash_payment_list.php');
    exit();
}

$paymentId = intval($_GET['id']);

// Get payment details
$paymentQuery = "SELECT bp.*, f.fee_type, f.amount as fee_amount, f.paid, 
                s.first_name, s.last_name, s.student_id as roll, c.class_name
                FROM bkash_payments bp
                LEFT JOIN fees f ON bp.fee_id = f.id
                LEFT JOIN students s ON f.student_id = s.id
                LEFT JOIN classes c ON s.class_id = c.id
                WHERE bp.id = ?";
$stmt = $conn->prepare($paymentQuery);
$stmt->bind_param('i', $paymentId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    $_SESSION['error'] = 'পেমেন্ট রেকর্ড খুঁজে পাওয়া যায়নি!';
    header('Location: bkash_payment_list.php');
    exit();
}

$payment = $result->fetch_assoc();

// Check if payment is completed
if ($payment['status'] !== 'Completed') {
    $_SESSION['error'] = 'শুধুমাত্র সফল পেমেন্টের জন্য রসিদ দেখা যাবে!';
    header("Location: bkash_payment_details.php?id=$paymentId");
    exit();
}

// Get school information
$schoolQuery = "SELECT * FROM school_info LIMIT 1";
$schoolResult = $conn->query($schoolQuery);
$school = $schoolResult->fetch_assoc();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>বিকাশ পেমেন্ট রসিদ - <?= $payment['trx_id'] ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .receipt-container {
            max-width: 800px;
            margin: 30px auto;
            background-color: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        .receipt-header {
            background-color: #198754;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .receipt-body {
            padding: 30px;
        }
        .receipt-footer {
            background-color: #f8f9fa;
            padding: 15px;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }
        .school-logo {
            max-width: 80px;
            height: auto;
        }
        .bkash-logo {
            max-width: 100px;
            height: auto;
        }
        .receipt-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .receipt-subtitle {
            font-size: 16px;
            margin-bottom: 0;
        }
        .info-section {
            margin-bottom: 25px;
        }
        .info-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 5px;
        }
        .info-row {
            display: flex;
            margin-bottom: 8px;
        }
        .info-label {
            width: 40%;
            font-weight: 500;
            color: #6c757d;
        }
        .info-value {
            width: 60%;
            font-weight: 400;
        }
        .amount-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .amount-table th, .amount-table td {
            padding: 12px 15px;
            border: 1px solid #dee2e6;
        }
        .amount-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .amount-table tfoot {
            font-weight: bold;
        }
        .verification-code {
            font-family: monospace;
            font-size: 18px;
            letter-spacing: 2px;
            background-color: #f8f9fa;
            padding: 8px;
            border-radius: 5px;
            display: inline-block;
            margin-top: 5px;
        }
        .success-icon {
            font-size: 48px;
            color: #198754;
            margin-bottom: 15px;
        }
        .print-btn {
            background-color: #198754;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        .print-btn:hover {
            background-color: #146c43;
        }
        .back-btn {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.3s;
            text-decoration: none;
            display: inline-block;
            margin-left: 10px;
        }
        .back-btn:hover {
            background-color: #5a6268;
            text-decoration: none;
            color: white;
        }
        @media print {
            body {
                background-color: #fff;
            }
            .receipt-container {
                box-shadow: none;
                margin: 0;
                max-width: 100%;
            }
            .no-print {
                display: none !important;
            }
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="receipt-container">
        <div class="receipt-header">
            <div class="row align-items-center">
                <div class="col-3 text-start">
                    <?php if (!empty($school['logo'])): ?>
                        <img src="<?= $school['logo'] ?>" alt="School Logo" class="school-logo">
                    <?php else: ?>
                        <i class="fas fa-school fa-3x"></i>
                    <?php endif; ?>
                </div>
                <div class="col-6 text-center">
                    <h1 class="receipt-title">পেমেন্ট রসিদ</h1>
                    <p class="receipt-subtitle">বিকাশ পেমেন্ট গেটওয়ে</p>
                </div>
                <div class="col-3 text-end">
                    <img src="https://www.bkash.com/sites/all/themes/bkash/logo.png" alt="bKash Logo" class="bkash-logo">
                </div>
            </div>
        </div>
        
        <div class="receipt-body">
            <div class="text-center mb-4">
                <i class="fas fa-check-circle success-icon"></i>
                <h3>পেমেন্ট সফল</h3>
                <p class="mb-0">ট্রানজেকশন আইডি: <strong><?= $payment['trx_id'] ?></strong></p>
                <p>তারিখ: <?= date('d F, Y h:i A', strtotime($payment['payment_date'])) ?></p>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="info-section">
                        <h5 class="info-title"><i class="fas fa-user-graduate me-2"></i>শিক্ষার্থী তথ্য</h5>
                        <div class="info-row">
                            <div class="info-label">নাম:</div>
                            <div class="info-value"><?= htmlspecialchars($payment['first_name'] . ' ' . $payment['last_name']) ?></div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">রোল:</div>
                            <div class="info-value"><?= $payment['roll'] ?></div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">শ্রেণী:</div>
                            <div class="info-value"><?= htmlspecialchars($payment['class_name']) ?></div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="info-section">
                        <h5 class="info-title"><i class="fas fa-file-invoice-dollar me-2"></i>পেমেন্ট তথ্য</h5>
                        <div class="info-row">
                            <div class="info-label">পেমেন্ট আইডি:</div>
                            <div class="info-value"><?= $payment['payment_id'] ?></div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">ট্রানজেকশন আইডি:</div>
                            <div class="info-value"><?= $payment['trx_id'] ?></div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">পেমেন্ট তারিখ:</div>
                            <div class="info-value"><?= date('d/m/Y h:i A', strtotime($payment['payment_date'])) ?></div>
                        </div>
                        <?php if (!empty($payment['payer_reference'])): ?>
                            <div class="info-row">
                                <div class="info-label">পেয়ার রেফারেন্স:</div>
                                <div class="info-value"><?= htmlspecialchars($payment['payer_reference']) ?></div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="info-section">
                <h5 class="info-title"><i class="fas fa-money-bill-wave me-2"></i>পেমেন্ট বিবরণ</h5>
                <table class="amount-table">
                    <thead>
                        <tr>
                            <th>বিবরণ</th>
                            <th class="text-end">পরিমাণ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><?= htmlspecialchars($payment['fee_type']) ?></td>
                            <td class="text-end">৳ <?= number_format($payment['amount'], 2) ?></td>
                        </tr>
                    </tbody>
                    <tfoot>
                        <tr>
                            <td>মোট পরিমাণ</td>
                            <td class="text-end">৳ <?= number_format($payment['amount'], 2) ?></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
            
            <div class="info-section text-center">
                <h5 class="info-title"><i class="fas fa-shield-alt me-2"></i>ভেরিফিকেশন</h5>
                <p>এই রসিদটি বিকাশ পেমেন্ট গেটওয়ে দ্বারা প্রক্রিয়াকৃত একটি বৈধ পেমেন্টের প্রমাণ।</p>
                <div class="verification-code"><?= strtoupper(substr(md5($payment['trx_id']), 0, 8)) ?></div>
            </div>
        </div>
        
        <div class="receipt-footer">
            <div class="mb-3">
                <p class="mb-0">
                    <?php if (!empty($school['name'])): ?>
                        <?= htmlspecialchars($school['name']) ?>
                    <?php else: ?>
                        স্কুল ম্যানেজমেন্ট সিস্টেম
                    <?php endif; ?>
                </p>
                <p class="mb-0">
                    <?php if (!empty($school['address'])): ?>
                        <?= htmlspecialchars($school['address']) ?>
                    <?php endif; ?>
                </p>
            </div>
            
            <div class="no-print">
                <button class="print-btn" onclick="window.print()">
                    <i class="fas fa-print me-2"></i> প্রিন্ট করুন
                </button>
                <a href="bkash_payment_details.php?id=<?= $paymentId ?>" class="back-btn">
                    <i class="fas fa-arrow-left me-2"></i> ফিরে যান
                </a>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
