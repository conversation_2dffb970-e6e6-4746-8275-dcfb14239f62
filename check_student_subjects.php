<?php
// Include database connection
require_once 'includes/dbh.inc.php';

// Get student information
$studentId = 'STD-601523';
echo "<h2>Student Information</h2>";
$query = "SELECT s.*, d.department_name, d.id as dept_id 
          FROM students s 
          LEFT JOIN departments d ON s.department_id = d.id 
          WHERE s.student_id = '$studentId'";
$result = $conn->query($query);

if ($result && $result->num_rows > 0) {
    $student = $result->fetch_assoc();
    echo "<pre>";
    print_r($student);
    echo "</pre>";
    
    $departmentId = $student['department_id'];
    $studentDbId = $student['id'];
    
    // Check if department exists in groups table
    echo "<h2>Department/Group Mapping</h2>";
    $groupQuery = "SELECT * FROM groups WHERE group_name = '{$student['department_name']}'";
    $groupResult = $conn->query($groupQuery);
    
    if ($groupResult && $groupResult->num_rows > 0) {
        $group = $groupResult->fetch_assoc();
        echo "<p>Found matching group for department:</p>";
        echo "<pre>";
        print_r($group);
        echo "</pre>";
        
        $groupId = $group['id'];
    } else {
        echo "<p>No matching group found for department: {$student['department_name']}</p>";
        
        // List all groups
        echo "<h3>All Groups</h3>";
        $allGroupsQuery = "SELECT * FROM groups";
        $allGroupsResult = $conn->query($allGroupsQuery);
        
        if ($allGroupsResult && $allGroupsResult->num_rows > 0) {
            echo "<ul>";
            while ($group = $allGroupsResult->fetch_assoc()) {
                echo "<li>{$group['id']}: {$group['group_name']}</li>";
            }
            echo "</ul>";
        }
    }
    
    // Check subjects for the department/group
    echo "<h2>Subjects for Group</h2>";
    if (isset($groupId)) {
        // Get required subjects
        $requiredSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code, sg.subject_type
                                FROM subjects s
                                JOIN subject_groups sg ON s.id = sg.subject_id
                                WHERE sg.group_id = $groupId AND s.is_active = 1
                                AND sg.subject_type = 'required' AND sg.is_applicable = 1
                                GROUP BY s.id
                                ORDER BY s.subject_name";
        $requiredSubjects = $conn->query($requiredSubjectsQuery);
        
        echo "<h3>Required Subjects</h3>";
        if ($requiredSubjects && $requiredSubjects->num_rows > 0) {
            echo "<ul>";
            while ($subject = $requiredSubjects->fetch_assoc()) {
                echo "<li>{$subject['subject_name']} ({$subject['subject_code']})</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>No required subjects found for this group.</p>";
        }
        
        // Get optional subjects
        $optionalSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code, sg.subject_type
                                FROM subjects s
                                JOIN subject_groups sg ON s.id = sg.subject_id
                                WHERE sg.group_id = $groupId AND s.is_active = 1
                                AND sg.subject_type = 'optional' AND sg.is_applicable = 1
                                GROUP BY s.id
                                ORDER BY s.subject_name";
        $optionalSubjects = $conn->query($optionalSubjectsQuery);
        
        echo "<h3>Optional Subjects</h3>";
        if ($optionalSubjects && $optionalSubjects->num_rows > 0) {
            echo "<ul>";
            while ($subject = $optionalSubjects->fetch_assoc()) {
                echo "<li>{$subject['subject_name']} ({$subject['subject_code']})</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>No optional subjects found for this group.</p>";
        }
        
        // Get fourth subjects
        $fourthSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code, sg.subject_type
                              FROM subjects s
                              JOIN subject_groups sg ON s.id = sg.subject_id
                              WHERE sg.group_id = $groupId AND s.is_active = 1
                              AND sg.subject_type = 'fourth' AND sg.is_applicable = 1
                              GROUP BY s.id
                              ORDER BY s.subject_name";
        $fourthSubjects = $conn->query($fourthSubjectsQuery);
        
        echo "<h3>Fourth Subjects</h3>";
        if ($fourthSubjects && $fourthSubjects->num_rows > 0) {
            echo "<ul>";
            while ($subject = $fourthSubjects->fetch_assoc()) {
                echo "<li>{$subject['subject_name']} ({$subject['subject_code']})</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>No fourth subjects found for this group.</p>";
        }
    }
    
    // Check if student has any selected subjects
    echo "<h2>Student's Selected Subjects</h2>";
    $selectedSubjectsQuery = "SELECT ss.id, ss.category, s.id as subject_id, s.subject_name, s.subject_code
                             FROM student_subjects ss
                             JOIN subjects s ON ss.subject_id = s.id
                             WHERE ss.student_id = ?
                             ORDER BY ss.category, s.subject_name";
    $stmt = $conn->prepare($selectedSubjectsQuery);
    $stmt->bind_param("i", $studentDbId);
    $stmt->execute();
    $selectedSubjects = $stmt->get_result();
    
    if ($selectedSubjects && $selectedSubjects->num_rows > 0) {
        echo "<ul>";
        while ($subject = $selectedSubjects->fetch_assoc()) {
            echo "<li>{$subject['subject_name']} ({$subject['subject_code']}) - {$subject['category']}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No subjects selected for this student.</p>";
    }
    
    // Check all subject_groups entries
    echo "<h2>All Subject-Group Mappings</h2>";
    $allMappingsQuery = "SELECT sg.*, s.subject_name, g.group_name 
                        FROM subject_groups sg 
                        JOIN subjects s ON sg.subject_id = s.id 
                        JOIN groups g ON sg.group_id = g.id
                        ORDER BY g.group_name, s.subject_name";
    $allMappings = $conn->query($allMappingsQuery);
    
    if ($allMappings && $allMappings->num_rows > 0) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Group</th><th>Subject</th><th>Type</th><th>Applicable</th></tr>";
        
        while ($mapping = $allMappings->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$mapping['group_name']}</td>";
            echo "<td>{$mapping['subject_name']}</td>";
            echo "<td>{$mapping['subject_type']}</td>";
            echo "<td>" . ($mapping['is_applicable'] ? 'Yes' : 'No') . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>No subject-group mappings found.</p>";
    }
    
} else {
    echo "<p>Student not found with ID: $studentId</p>";
}
?>
