<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Function to check if a student has passed based on minimum passing marks
function checkPassStatus($cq_marks, $mcq_marks, $practical_marks, $min_cq_pass, $min_mcq_pass, $min_practical_pass) {
    $cq_passed = ($cq_marks >= $min_cq_pass);
    $mcq_passed = ($mcq_marks >= $min_mcq_pass);
    $practical_passed = true; // Default to true

    // Only check practical if there's a minimum requirement
    if ($min_practical_pass > 0) {
        $practical_passed = ($practical_marks >= $min_practical_pass);
    }

    // Overall pass status - must pass all components
    $is_passed = $cq_passed && $mcq_passed && $practical_passed;

    return [
        'is_passed' => $is_passed,
        'cq_passed' => $cq_passed,
        'mcq_passed' => $mcq_passed,
        'practical_passed' => $practical_passed
    ];
}

// Function to calculate grade
function calculateGrade($percentage, $pass_status = true) {
    // If student failed any component, return F regardless of percentage
    if (!$pass_status) {
        return 'F';
    }

    if ($percentage >= 80) {
        return 'A+';
    } elseif ($percentage >= 70) {
        return 'A';
    } elseif ($percentage >= 60) {
        return 'A-';
    } elseif ($percentage >= 50) {
        return 'B';
    } elseif ($percentage >= 40) {
        return 'C';
    } elseif ($percentage >= 33) {
        return 'D';
    } else {
        return 'F';
    }
}

$successCount = 0;
$errorCount = 0;
$message = '';

// Get all results
$resultsQuery = "SELECT dr.id, dr.student_id, dr.exam_id, dr.subject_id,
                dr.cq_marks, dr.mcq_marks, dr.practical_marks, dr.total_obtained
                FROM detailed_results dr";
$results = $conn->query($resultsQuery);

if ($results && $results->num_rows > 0) {
    // Begin transaction
    $conn->begin_transaction();

    try {
        while ($result = $results->fetch_assoc()) {
            // Get marks distribution for the subject
            $distQuery = "SELECT * FROM subject_marks_distribution WHERE subject_id = ?";
            $stmt = $conn->prepare($distQuery);
            $stmt->bind_param("i", $result['subject_id']);
            $stmt->execute();
            $distResult = $stmt->get_result();

            if ($distResult->num_rows > 0) {
                $dist = $distResult->fetch_assoc();

                // Get minimum passing marks
                $min_cq_pass = isset($dist['min_cq_pass_marks']) ? $dist['min_cq_pass_marks'] : round($dist['cq_marks'] * 0.33, 2);
                $min_mcq_pass = isset($dist['min_mcq_pass_marks']) ? $dist['min_mcq_pass_marks'] : round($dist['mcq_marks'] * 0.33, 2);
                $min_practical_pass = isset($dist['min_practical_pass_marks']) ? $dist['min_practical_pass_marks'] : round($dist['practical_marks'] * 0.33, 2);

                // Check pass status for each component
                $pass_status = checkPassStatus(
                    $result['cq_marks'],
                    $result['mcq_marks'],
                    $result['practical_marks'],
                    $min_cq_pass,
                    $min_mcq_pass,
                    $min_practical_pass
                );

                // Calculate percentage and grade
                $percentage = ($result['total_obtained'] / $dist['total_marks']) * 100;
                $grade = calculateGrade($percentage, $pass_status['is_passed']);

                // Update result
                $updateQuery = "UPDATE detailed_results
                               SET grade = ?,
                                   is_passed = ?,
                                   cq_passed = ?,
                                   mcq_passed = ?,
                                   practical_passed = ?
                               WHERE id = ?";
                $stmt = $conn->prepare($updateQuery);
                $stmt->bind_param("siiiii",
                                $grade,
                                $pass_status['is_passed'],
                                $pass_status['cq_passed'],
                                $pass_status['mcq_passed'],
                                $pass_status['practical_passed'],
                                $result['id']);

                if ($stmt->execute()) {
                    $successCount++;
                } else {
                    $errorCount++;
                }
            } else {
                // If no distribution found, use default values
                $min_cq_pass = 23.10; // 33% of 70
                $min_mcq_pass = 9.90; // 33% of 30
                $min_practical_pass = 0;

                // Check pass status for each component
                $pass_status = checkPassStatus(
                    $result['cq_marks'],
                    $result['mcq_marks'],
                    $result['practical_marks'],
                    $min_cq_pass,
                    $min_mcq_pass,
                    $min_practical_pass
                );

                // Calculate percentage and grade (assuming total marks is 100)
                $percentage = $result['total_obtained'];
                $grade = calculateGrade($percentage, $pass_status['is_passed']);

                // Update result
                $updateQuery = "UPDATE detailed_results
                               SET grade = ?,
                                   is_passed = ?,
                                   cq_passed = ?,
                                   mcq_passed = ?,
                                   practical_passed = ?
                               WHERE id = ?";
                $stmt = $conn->prepare($updateQuery);
                $stmt->bind_param("siiiii",
                                $grade,
                                $pass_status['is_passed'],
                                $pass_status['cq_passed'],
                                $pass_status['mcq_passed'],
                                $pass_status['practical_passed'],
                                $result['id']);

                if ($stmt->execute()) {
                    $successCount++;
                } else {
                    $errorCount++;
                }
            }
        }

        // If all operations were successful, commit the transaction
        if ($errorCount == 0) {
            $conn->commit();
            $message = "সফলভাবে $successCount টি ফলাফল আপডেট করা হয়েছে!";
        } else {
            // If there were errors, roll back the transaction
            $conn->rollback();
            $message = "ত্রুটি: $errorCount টি ফলাফল আপডেট করা যায়নি।";
        }
    } catch (Exception $e) {
        // An exception occurred, roll back the transaction
        $conn->rollback();
        $message = "ফলাফল আপডেট করতে সমস্যা হয়েছে: " . $e->getMessage();
    }
} else {
    $message = "কোন ফলাফল পাওয়া যায়নি।";
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>ফলাফল আপডেট - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>ফলাফল পাস/ফেইল স্ট্যাটাস আপডেট</h2>
                        <p class="text-muted">সকল ফলাফলের পাস/ফেইল স্ট্যাটাস আপডেট করুন</p>
                    </div>
                </div>

                <div class="card">
                    <div class="card-body">
                        <div class="alert <?php echo ($errorCount > 0) ? 'alert-danger' : 'alert-success'; ?>">
                            <?php echo $message; ?>
                        </div>

                        <div class="mt-3">
                            <a href="results.php" class="btn btn-primary">ফলাফল পেজে ফিরে যান</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
