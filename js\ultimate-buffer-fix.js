/**
 * Ultimate Buffer Fix for ZFAW
 * This is the final solution to completely eliminate title bar buffering
 */

(function() {
    'use strict';
    
    console.log('🔥 Ultimate Buffer Fix: Starting...');
    
    // Immediately set the correct title
    document.title = 'নিশাত এডুকেশন সেন্টার';
    
    // Store references to original functions
    const originalSetInterval = window.setInterval;
    const originalSetTimeout = window.setTimeout;
    const originalRequestAnimationFrame = window.requestAnimationFrame;
    
    // Counter for blocked operations
    let blockedIntervals = 0;
    let blockedTimeouts = 0;
    let blockedAnimations = 0;
    
    // Override setInterval to completely block it
    window.setInterval = function(callback, delay) {
        blockedIntervals++;
        console.log(`🚫 Blocked setInterval #${blockedIntervals} (delay: ${delay}ms)`);
        return -1; // Return invalid ID
    };
    
    // Override setTimeout to only allow very short delays
    window.setTimeout = function(callback, delay) {
        if (delay > 100) {
            blockedTimeouts++;
            console.log(`🚫 Blocked setTimeout #${blockedTimeouts} (delay: ${delay}ms)`);
            return -1;
        }
        return originalSetTimeout.call(this, callback, delay);
    };
    
    // Override requestAnimationFrame to block animations
    window.requestAnimationFrame = function(callback) {
        blockedAnimations++;
        console.log(`🚫 Blocked requestAnimationFrame #${blockedAnimations}`);
        return -1;
    };
    
    // Function to aggressively clear all timers
    function nukAllTimers() {
        // Clear intervals from 1 to 50000
        for (let i = 1; i <= 50000; i++) {
            try {
                clearInterval(i);
                clearTimeout(i);
            } catch (e) {
                // Ignore errors
            }
        }
        console.log('💥 Nuked all timers (1-50000)');
    }
    
    // Function to disable all CSS animations and transitions
    function disableAllAnimations() {
        const style = document.createElement('style');
        style.id = 'ultimate-animation-killer';
        style.textContent = `
            *, *::before, *::after {
                animation-duration: 0s !important;
                animation-delay: 0s !important;
                animation-iteration-count: 1 !important;
                animation-fill-mode: forwards !important;
                transition-duration: 0s !important;
                transition-delay: 0s !important;
                transform: none !important;
            }
            
            /* Kill specific animations */
            @keyframes scroll-left { 0%, 100% { transform: none; } }
            
            /* Kill marquee */
            marquee {
                animation: none !important;
                -webkit-animation: none !important;
                -moz-animation: none !important;
                -o-animation: none !important;
                -ms-animation: none !important;
            }
            
            /* Kill any loading spinners */
            .spinner, .loader, [class*="spin"], [class*="load"], [class*="animat"] {
                animation: none !important;
                display: none !important;
            }
        `;
        
        // Remove existing style if present
        const existing = document.getElementById('ultimate-animation-killer');
        if (existing) existing.remove();
        
        document.head.appendChild(style);
        console.log('🎭 All animations and transitions disabled');
    }
    
    // Function to stop marquee elements
    function stopMarquees() {
        const marquees = document.querySelectorAll('marquee');
        marquees.forEach((marquee, index) => {
            if (marquee.stop) marquee.stop();
            marquee.scrollAmount = 0;
            marquee.style.animation = 'none';
            marquee.style.webkitAnimation = 'none';
            console.log(`🛑 Stopped marquee #${index + 1}`);
        });
    }
    
    // Function to kill problematic elements
    function killProblematicElements() {
        const selectors = [
            '[class*="loader"]',
            '[id*="loader"]', 
            '[class*="loading"]',
            '[id*="loading"]',
            '[class*="spinner"]',
            '[id*="spinner"]',
            '[class*="preloader"]',
            '[id*="preloader"]',
            '.ajax-loading',
            '.progress-bar'
        ];
        
        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                el.style.display = 'none';
                el.style.visibility = 'hidden';
                el.style.opacity = '0';
                el.style.height = '0';
                el.style.width = '0';
            });
        });
        
        console.log('💀 Killed problematic elements');
    }
    
    // Function to override problematic methods
    function overrideProblematicMethods() {
        // Override location.reload
        if (window.location && window.location.reload) {
            window.location.reload = function() {
                console.log('🚫 Blocked location.reload()');
                return false;
            };
        }
        
        // Override history methods that might cause issues
        if (window.history) {
            const originalPushState = window.history.pushState;
            const originalReplaceState = window.history.replaceState;
            
            window.history.pushState = function() {
                console.log('🚫 Blocked history.pushState()');
                return false;
            };
            
            window.history.replaceState = function() {
                console.log('🚫 Blocked history.replaceState()');
                return false;
            };
        }
        
        console.log('🔒 Overrode problematic methods');
    }
    
    // Function to fix title permanently
    function fixTitlePermanently() {
        const correctTitle = 'নিশাত এডুকেশন সেন্টার';
        
        // Set title immediately
        document.title = correctTitle;
        
        // Override title setter
        Object.defineProperty(document, 'title', {
            get: function() {
                return correctTitle;
            },
            set: function(value) {
                if (value !== correctTitle) {
                    console.log(`🚫 Blocked title change to: ${value}`);
                    return;
                }
                // Allow setting to correct title
                document.getElementsByTagName('title')[0].textContent = correctTitle;
            },
            configurable: false
        });
        
        console.log('📝 Title permanently fixed');
    }
    
    // Main execution function
    function executeUltimateFix() {
        console.log('🚀 Executing ultimate buffer fix...');
        
        // Execute all fixes
        nukAllTimers();
        disableAllAnimations();
        stopMarquees();
        killProblematicElements();
        overrideProblematicMethods();
        fixTitlePermanently();
        
        // Force page to be visible
        document.body.style.display = 'block';
        document.body.style.visibility = 'visible';
        document.body.style.opacity = '1';
        
        console.log('✅ Ultimate buffer fix complete!');
        console.log(`📊 Blocked: ${blockedIntervals} intervals, ${blockedTimeouts} timeouts, ${blockedAnimations} animations`);
    }
    
    // Execute immediately
    executeUltimateFix();
    
    // Execute on DOM ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', executeUltimateFix);
    }
    
    // Execute on window load
    window.addEventListener('load', executeUltimateFix);
    
    // Execute with delays to catch late-loading scripts
    originalSetTimeout(executeUltimateFix, 50);
    originalSetTimeout(executeUltimateFix, 200);
    originalSetTimeout(executeUltimateFix, 500);
    
    // Expose debug info
    window.ultimateBufferFix = {
        blockedIntervals: () => blockedIntervals,
        blockedTimeouts: () => blockedTimeouts,
        blockedAnimations: () => blockedAnimations,
        reapply: executeUltimateFix
    };
    
    console.log('🔧 Ultimate Buffer Fix loaded! Use window.ultimateBufferFix for debug info');
    
})();
