<?php
// Initialize variables
$message = '';
$error = '';

// Define default values
$footer_title = "ZFAW";
$footer_description = "উচ্চমানের শিক্ষা প্রদানের মাধ্যমে শিক্ষার্থীদের ভবিষ্যৎ গড়তে সাহায্য করা আমাদের লক্ষ্য।";
$contact_title = "যোগাযোগ";
$contact_address = "১২৩, মেইন রোড, ঢাকা";
$contact_phone = "+৮৮০১৭১২৩৪৫৬৭৮";
$contact_email = "<EMAIL>";
$links_title = "লিঙ্কসমূহ";
$copyright_text = "ZFAW. সর্বস্বত্ব সংরক্ষিত।";

// Get the active tab from the URL or default to 'both'
$active_tab = isset($_GET['tab']) ? $_GET['tab'] : 'both';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $footer_title = $_POST['footer_title'] ?? $footer_title;
    $footer_description = $_POST['footer_description'] ?? $footer_description;
    $contact_title = $_POST['contact_title'] ?? $contact_title;
    $contact_address = $_POST['contact_address'] ?? $contact_address;
    $contact_phone = $_POST['contact_phone'] ?? $contact_phone;
    $contact_email = $_POST['contact_email'] ?? $contact_email;
    $links_title = $_POST['links_title'] ?? $links_title;
    $copyright_text = $_POST['copyright_text'] ?? $copyright_text;

    // Get the active tab from the form
    $active_tab = $_POST['active_tab'] ?? 'both';

    // Update about.php if requested
    if ($active_tab == 'about' || $active_tab == 'both') {
        if (file_exists('about.php')) {
            $about_content = file_get_contents('about.php');

            // Update footer title and description
            $about_content = preg_replace('/<div class="col-md-4 mb-3">\s*<h5>([^<]*)<\/h5>\s*<p>([^<]*)<\/p>\s*<\/div>/s',
                '<div class="col-md-4 mb-3">' . "\n" .
                '                    <h5>' . $footer_title . '</h5>' . "\n" .
                '                    <p>' . $footer_description . '</p>' . "\n" .
                '                </div>',
                $about_content, 1);

            // Update contact information
            $about_content = preg_replace('/<div class="col-md-4 mb-3">\s*<h5>([^<]*)<\/h5>\s*<address>\s*<p><i class="fas fa-map-marker-alt me-2"><\/i> ([^<]*)<\/p>\s*<p><i class="fas fa-phone me-2"><\/i> ([^<]*)<\/p>\s*<p><i class="fas fa-envelope me-2"><\/i> ([^<]*)<\/p>\s*<\/address>\s*<\/div>/s',
                '<div class="col-md-4 mb-3">' . "\n" .
                '                    <h5>' . $contact_title . '</h5>' . "\n" .
                '                    <address>' . "\n" .
                '                        <p><i class="fas fa-map-marker-alt me-2"></i> ' . $contact_address . '</p>' . "\n" .
                '                        <p><i class="fas fa-phone me-2"></i> ' . $contact_phone . '</p>' . "\n" .
                '                        <p><i class="fas fa-envelope me-2"></i> ' . $contact_email . '</p>' . "\n" .
                '                    </address>' . "\n" .
                '                </div>',
                $about_content, 1);

            // Update links title
            $about_content = preg_replace('/<div class="col-md-4 mb-3">\s*<h5>([^<]*)<\/h5>\s*<ul class="list-unstyled">/s',
                '<div class="col-md-4 mb-3">' . "\n" .
                '                    <h5>' . $links_title . '</h5>' . "\n" .
                '                    <ul class="list-unstyled">',
                $about_content, 1);

            // Update copyright text
            $about_content = preg_replace('/<p>&copy; [^<]* ([^<]*)<\/p>/s',
                '<p>&copy; ' . date('Y') . ' ' . $copyright_text . '</p>',
                $about_content, 1);

            // Save the updated about.php file
            if (file_put_contents('about.php', $about_content)) {
                $message = "about.php ফুটার সফলভাবে আপডেট করা হয়েছে।";
            } else {
                $error = "about.php ফাইল আপডেট করতে সমস্যা হয়েছে।";
            }
        } else {
            $error = "about.php ফাইল পাওয়া যায়নি।";
        }
    }

    // Update index.php if requested
    if ($active_tab == 'index' || $active_tab == 'both') {
        if (file_exists('index.php')) {
            $index_content = file_get_contents('index.php');

            // Update first footer section (title and description)
            $index_content = preg_replace('/<div class="col-lg-4 mb-4">\s*<h5>([^<]*)<\/h5>\s*<p>([^<]*)<\/p>/s',
                '<div class="col-lg-4 mb-4">' . "\n" .
                '                    <h5>' . $footer_title . '</h5>' . "\n" .
                '                    <p>' . $footer_description . '</p>',
                $index_content, 1);

            // Update contact title
            $index_content = preg_replace('/<div class="col-lg-4 mb-4">\s*<h5>([^<]*)<\/h5>\s*<ul class="footer-links">\s*<li><i class="fas fa-map-marker-alt/s',
                '<div class="col-lg-4 mb-4">' . "\n" .
                '                    <h5>' . $contact_title . '</h5>' . "\n" .
                '                    <ul class="footer-links">' . "\n" .
                '                        <li><i class="fas fa-map-marker-alt',
                $index_content, 1);

            // Update contact address
            $index_content = preg_replace('/<li><i class="fas fa-map-marker-alt me-2"><\/i> [^<]*<\/li>/s',
                '<li><i class="fas fa-map-marker-alt me-2"></i> ' . $contact_address . '</li>',
                $index_content, 1);

            // Update contact phone
            $index_content = preg_replace('/<li><i class="fas fa-phone me-2"><\/i> [^<]*<\/li>/s',
                '<li><i class="fas fa-phone me-2"></i> ' . $contact_phone . '</li>',
                $index_content, 1);

            // Update contact email
            $index_content = preg_replace('/<li><i class="fas fa-envelope me-2"><\/i> [^<]*<\/li>/s',
                '<li><i class="fas fa-envelope me-2"></i> ' . $contact_email . '</li>',
                $index_content, 1);

            // Update links section (second column)
            $index_content = preg_replace('/<div class="col-lg-4 mb-4">\s*<h5>([^<]*)<\/h5>\s*<ul class="footer-links">\s*<li><a href="index.php">/s',
                '<div class="col-lg-4 mb-4">' . "\n" .
                '                    <h5>' . $links_title . '</h5>' . "\n" .
                '                    <ul class="footer-links">' . "\n" .
                '                        <li><a href="index.php">',
                $index_content, 1);

            // Update copyright text
            $index_content = preg_replace('/<p class="mb-0">&copy; [^<]* ([^<]*?)(<\/p>|<a)/s',
                '<p class="mb-0">&copy; ' . date('Y') . ' ' . $copyright_text . '$2',
                $index_content, 1);

            // Save the updated index.php file
            if (file_put_contents('index.php', $index_content)) {
                $message .= ($message ? " " : "") . "index.php ফাইলও আপডেট করা হয়েছে।";
            } else {
                $error .= ($error ? " " : "") . "index.php ফাইল আপডেট করতে সমস্যা হয়েছে।";
            }
        } else {
            $error .= ($error ? " " : "") . "index.php ফাইল পাওয়া যায়নি।";
        }
    }
} else {
    // Determine which file to load values from based on active tab
    if ($active_tab == 'index') {
        // Load values from index.php
        if (file_exists('index.php')) {
            $index_content = file_get_contents('index.php');

            // Extract footer title and description
            if (preg_match('/<div class="col-lg-4 mb-4">\s*<h5>([^<]*)<\/h5>\s*<p>([^<]*)<\/p>/s', $index_content, $matches)) {
                $footer_title = $matches[1];
                $footer_description = $matches[2];
            }

            // Extract contact title
            if (preg_match('/<div class="col-lg-4 mb-4">\s*<h5>([^<]*)<\/h5>\s*<ul class="footer-links">\s*<li><i class="fas fa-map-marker-alt/s', $index_content, $matches)) {
                $contact_title = $matches[1];
            }

            // Extract contact address
            if (preg_match('/<li><i class="fas fa-map-marker-alt me-2"><\/i> ([^<]*)<\/li>/s', $index_content, $matches)) {
                $contact_address = $matches[1];
            }

            // Extract contact phone
            if (preg_match('/<li><i class="fas fa-phone me-2"><\/i> ([^<]*)<\/li>/s', $index_content, $matches)) {
                $contact_phone = $matches[1];
            }

            // Extract contact email
            if (preg_match('/<li><i class="fas fa-envelope me-2"><\/i> ([^<]*)<\/li>/s', $index_content, $matches)) {
                $contact_email = $matches[1];
            }

            // Extract links title
            if (preg_match('/<div class="col-lg-4 mb-4">\s*<h5>([^<]*)<\/h5>\s*<ul class="footer-links">\s*<li><a href="index.php">/s', $index_content, $matches)) {
                $links_title = $matches[1];
            }

            // Extract copyright text
            if (preg_match('/<p class="mb-0">&copy; [^<]* ([^<]*?)(<\/p>|<a)/s', $index_content, $matches)) {
                $copyright_text = $matches[1];
            }
        }
    } else {
        // Load values from about.php (default)
        if (file_exists('about.php')) {
            $about_content = file_get_contents('about.php');

            // Extract footer title and description
            if (preg_match('/<div class="col-md-4 mb-3">\s*<h5>([^<]*)<\/h5>\s*<p>([^<]*)<\/p>\s*<\/div>/s', $about_content, $matches)) {
                $footer_title = $matches[1];
                $footer_description = $matches[2];
            }

            // Extract contact information
            if (preg_match('/<div class="col-md-4 mb-3">\s*<h5>([^<]*)<\/h5>\s*<address>\s*<p><i class="fas fa-map-marker-alt me-2"><\/i> ([^<]*)<\/p>\s*<p><i class="fas fa-phone me-2"><\/i> ([^<]*)<\/p>\s*<p><i class="fas fa-envelope me-2"><\/i> ([^<]*)<\/p>\s*<\/address>\s*<\/div>/s', $about_content, $matches)) {
                $contact_title = $matches[1];
                $contact_address = $matches[2];
                $contact_phone = $matches[3];
                $contact_email = $matches[4];
            }

            // Extract links title
            if (preg_match('/<div class="col-md-4 mb-3">\s*<h5>([^<]*)<\/h5>\s*<ul class="list-unstyled">/s', $about_content, $matches)) {
                $links_title = $matches[1];
            }

            // Extract copyright text
            if (preg_match('/<p>&copy; [^<]* ([^<]*)<\/p>/s', $about_content, $matches)) {
                $copyright_text = $matches[1];
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>ফুটার আপডেট - ZFAW</title>

    <!-- Bootstrap CSS -->
    

    <!-- Custom CSS -->
    

    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: #f8f9fa;
        }

        .form-container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-top: 30px;
            margin-bottom: 30px;
        }

        .section-title {
            border-left: 4px solid #00a65a;
            padding-left: 10px;
            margin-bottom: 20px;
            color: #00a65a;
        }

        .form-label {
            font-weight: 500;
        }

        .btn-primary {
            background-color: #00a65a;
            border-color: #00a65a;
        }

        .btn-primary:hover {
            background-color: #008d4c;
            border-color: #008d4c;
        }

        /* Custom Tab Styles */
        .nav-tabs .nav-link {
            color: #495057;
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
            padding: 0.75rem 1.25rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-tabs .nav-link:hover {
            color: #00a65a;
            border-color: #e9ecef #e9ecef #dee2e6;
        }

        .nav-tabs .nav-link.active {
            color: #00a65a;
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
            border-top: 3px solid #00a65a;
        }

        .footer-preview {
            background-color: #222d32;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .footer-preview h5 {
            color: white;
            margin-bottom: 15px;
        }

        .footer-preview p {
            color: #adb5bd;
        }

        .footer-preview ul {
            list-style: none;
            padding-left: 0;
        }

        .footer-preview ul li {
            margin-bottom: 8px;
        }

        .footer-preview ul li a {
            color: #adb5bd;
            text-decoration: none;
        }

        .footer-preview ul li a:hover {
            color: white;
        }

        .footer-preview hr {
            border-color: #495057;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background-color: #00a65a;">
        <div class="container">
            <a class="navbar-brand" href="index.php">ZFAW</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">হোম</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">বিষয়সমূহ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">আমাদের সম্পর্কে</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">যোগাযোগ</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <a href="admin/dashboard.php" class="btn btn-outline-light">ড্যাশবোর্ড</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="form-container">
                    <h2 class="text-center mb-4">ফুটার আপডেট করুন</h2>

                    <?php if (!empty($message)): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $error; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Navigation Tabs -->
                    <ul class="nav nav-tabs mb-4" id="footerTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <a class="nav-link <?php echo ($active_tab == 'both') ? 'active' : ''; ?>" id="both-tab" href="?tab=both" role="tab" aria-controls="both" aria-selected="<?php echo ($active_tab == 'both') ? 'true' : 'false'; ?>">
                                <i class="fas fa-globe me-2"></i> উভয় পেজ
                            </a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link <?php echo ($active_tab == 'index') ? 'active' : ''; ?>" id="index-tab" href="?tab=index" role="tab" aria-controls="index" aria-selected="<?php echo ($active_tab == 'index') ? 'true' : 'false'; ?>">
                                <i class="fas fa-home me-2"></i> হোম পেজ (index.php)
                            </a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link <?php echo ($active_tab == 'about') ? 'active' : ''; ?>" id="about-tab" href="?tab=about" role="tab" aria-controls="about" aria-selected="<?php echo ($active_tab == 'about') ? 'true' : 'false'; ?>">
                                <i class="fas fa-info-circle me-2"></i> আমাদের সম্পর্কে (about.php)
                            </a>
                        </li>
                    </ul>

                    <div class="alert alert-info mb-4">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php if ($active_tab == 'both'): ?>
                            আপনি উভয় পেজের ফুটার একসাথে আপডেট করছেন।
                        <?php elseif ($active_tab == 'index'): ?>
                            আপনি শুধুমাত্র হোম পেজের (index.php) ফুটার আপডেট করছেন।
                        <?php elseif ($active_tab == 'about'): ?>
                            আপনি শুধুমাত্র আমাদের সম্পর্কে পেজের (about.php) ফুটার আপডেট করছেন।
                        <?php endif; ?>
                    </div>

                    <form method="post" action="">
                        <input type="hidden" name="active_tab" value="<?php echo $active_tab; ?>">
                        <!-- Footer Information Section -->
                        <div class="mb-4">
                            <h4 class="section-title">প্রতিষ্ঠানের তথ্য</h4>

                            <div class="mb-3">
                                <label for="footer_title" class="form-label">শিরোনাম</label>
                                <input type="text" class="form-control" id="footer_title" name="footer_title" value="<?php echo htmlspecialchars($footer_title); ?>">
                            </div>

                            <div class="mb-3">
                                <label for="footer_description" class="form-label">বিবরণ</label>
                                <textarea class="form-control" id="footer_description" name="footer_description" rows="3"><?php echo htmlspecialchars($footer_description); ?></textarea>
                            </div>
                        </div>

                        <!-- Contact Information Section -->
                        <div class="mb-4">
                            <h4 class="section-title">যোগাযোগের তথ্য</h4>

                            <div class="mb-3">
                                <label for="contact_title" class="form-label">শিরোনাম</label>
                                <input type="text" class="form-control" id="contact_title" name="contact_title" value="<?php echo htmlspecialchars($contact_title); ?>">
                            </div>

                            <div class="mb-3">
                                <label for="contact_address" class="form-label">ঠিকানা</label>
                                <input type="text" class="form-control" id="contact_address" name="contact_address" value="<?php echo htmlspecialchars($contact_address); ?>">
                            </div>

                            <div class="mb-3">
                                <label for="contact_phone" class="form-label">ফোন নম্বর</label>
                                <input type="text" class="form-control" id="contact_phone" name="contact_phone" value="<?php echo htmlspecialchars($contact_phone); ?>">
                            </div>

                            <div class="mb-3">
                                <label for="contact_email" class="form-label">ইমেইল</label>
                                <input type="email" class="form-control" id="contact_email" name="contact_email" value="<?php echo htmlspecialchars($contact_email); ?>">
                            </div>
                        </div>

                        <!-- Links Section -->
                        <div class="mb-4">
                            <h4 class="section-title">লিঙ্কসমূহ</h4>

                            <div class="mb-3">
                                <label for="links_title" class="form-label">শিরোনাম</label>
                                <input type="text" class="form-control" id="links_title" name="links_title" value="<?php echo htmlspecialchars($links_title); ?>">
                            </div>

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> লিঙ্কসমূহ পরিবর্তন করতে চাইলে, আপনাকে সরাসরি ফাইল এডিট করতে হবে।
                            </div>
                        </div>

                        <!-- Copyright Section -->
                        <div class="mb-4">
                            <h4 class="section-title">কপিরাইট তথ্য</h4>

                            <div class="mb-3">
                                <label for="copyright_text" class="form-label">কপিরাইট টেক্সট</label>
                                <input type="text" class="form-control" id="copyright_text" name="copyright_text" value="<?php echo htmlspecialchars($copyright_text); ?>">
                                <small class="text-muted">বছর স্বয়ংক্রিয়ভাবে যোগ করা হবে। উদাহরণ: &copy; <?php echo date('Y'); ?> <?php echo htmlspecialchars($copyright_text); ?></small>
                            </div>
                        </div>

                        <!-- Footer Preview -->
                        <div class="mb-4">
                            <h4 class="section-title">ফুটার প্রিভিউ</h4>

                            <?php if ($active_tab == 'both' || $active_tab == 'about'): ?>
                            <!-- About.php Footer Preview -->
                            <div class="mb-3">
                                <?php if ($active_tab == 'both'): ?>
                                <h5 class="text-primary mb-2"><i class="fas fa-info-circle me-2"></i>আমাদের সম্পর্কে (about.php) ফুটার প্রিভিউ</h5>
                                <?php endif; ?>
                                <div class="footer-preview">
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <h5 id="preview_footer_title_about"><?php echo htmlspecialchars($footer_title); ?></h5>
                                            <p id="preview_footer_description_about"><?php echo htmlspecialchars($footer_description); ?></p>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <h5 id="preview_contact_title_about"><?php echo htmlspecialchars($contact_title); ?></h5>
                                            <address>
                                                <p><i class="fas fa-map-marker-alt me-2"></i> <span id="preview_contact_address_about"><?php echo htmlspecialchars($contact_address); ?></span></p>
                                                <p><i class="fas fa-phone me-2"></i> <span id="preview_contact_phone_about"><?php echo htmlspecialchars($contact_phone); ?></span></p>
                                                <p><i class="fas fa-envelope me-2"></i> <span id="preview_contact_email_about"><?php echo htmlspecialchars($contact_email); ?></span></p>
                                            </address>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <h5 id="preview_links_title_about"><?php echo htmlspecialchars($links_title); ?></h5>
                                            <ul class="list-unstyled">
                                                <li><a href="#">হোম</a></li>
                                                <li><a href="#">বিষয়সমূহ</a></li>
                                                <li><a href="#">আমাদের সম্পর্কে</a></li>
                                                <li><a href="#">যোগাযোগ</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="text-center">
                                        <p>&copy; <?php echo date('Y'); ?> <span id="preview_copyright_text_about"><?php echo htmlspecialchars($copyright_text); ?></span></p>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <?php if ($active_tab == 'both' || $active_tab == 'index'): ?>
                            <!-- Index.php Footer Preview -->
                            <div>
                                <?php if ($active_tab == 'both'): ?>
                                <h5 class="text-primary mb-2"><i class="fas fa-home me-2"></i>হোম পেজ (index.php) ফুটার প্রিভিউ</h5>
                                <?php endif; ?>
                                <div class="footer-preview">
                                    <div class="row">
                                        <div class="col-lg-4 mb-4">
                                            <h5 id="preview_footer_title_index"><?php echo htmlspecialchars($footer_title); ?></h5>
                                            <p id="preview_footer_description_index"><?php echo htmlspecialchars($footer_description); ?></p>
                                        </div>
                                        <div class="col-lg-4 mb-4">
                                            <h5 id="preview_contact_title_index"><?php echo htmlspecialchars($contact_title); ?></h5>
                                            <ul class="footer-links">
                                                <li><i class="fas fa-map-marker-alt me-2"></i> <span id="preview_contact_address_index"><?php echo htmlspecialchars($contact_address); ?></span></li>
                                                <li><i class="fas fa-phone me-2"></i> <span id="preview_contact_phone_index"><?php echo htmlspecialchars($contact_phone); ?></span></li>
                                                <li><i class="fas fa-envelope me-2"></i> <span id="preview_contact_email_index"><?php echo htmlspecialchars($contact_email); ?></span></li>
                                            </ul>
                                        </div>
                                        <div class="col-lg-4 mb-4">
                                            <h5 id="preview_links_title_index"><?php echo htmlspecialchars($links_title); ?></h5>
                                            <ul class="footer-links">
                                                <li><a href="#">হোম</a></li>
                                                <li><a href="#">বিষয়সমূহ</a></li>
                                                <li><a href="#">আমাদের সম্পর্কে</a></li>
                                                <li><a href="#">যোগাযোগ</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="text-center">
                                        <p class="mb-0">&copy; <?php echo date('Y'); ?> <span id="preview_copyright_text_index"><?php echo htmlspecialchars($copyright_text); ?></span></p>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg px-5">আপডেট করুন</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript for Live Preview -->
    <script>
        // Function to update preview
        function updatePreview() {
            const footerTitle = document.getElementById('footer_title').value;
            const footerDescription = document.getElementById('footer_description').value;
            const contactTitle = document.getElementById('contact_title').value;
            const contactAddress = document.getElementById('contact_address').value;
            const contactPhone = document.getElementById('contact_phone').value;
            const contactEmail = document.getElementById('contact_email').value;
            const linksTitle = document.getElementById('links_title').value;
            const copyrightText = document.getElementById('copyright_text').value;

            // Update about.php preview elements if they exist
            const aboutElements = [
                { id: 'preview_footer_title_about', value: footerTitle },
                { id: 'preview_footer_description_about', value: footerDescription },
                { id: 'preview_contact_title_about', value: contactTitle },
                { id: 'preview_contact_address_about', value: contactAddress },
                { id: 'preview_contact_phone_about', value: contactPhone },
                { id: 'preview_contact_email_about', value: contactEmail },
                { id: 'preview_links_title_about', value: linksTitle },
                { id: 'preview_copyright_text_about', value: copyrightText }
            ];

            // Update index.php preview elements if they exist
            const indexElements = [
                { id: 'preview_footer_title_index', value: footerTitle },
                { id: 'preview_footer_description_index', value: footerDescription },
                { id: 'preview_contact_title_index', value: contactTitle },
                { id: 'preview_contact_address_index', value: contactAddress },
                { id: 'preview_contact_phone_index', value: contactPhone },
                { id: 'preview_contact_email_index', value: contactEmail },
                { id: 'preview_links_title_index', value: linksTitle },
                { id: 'preview_copyright_text_index', value: copyrightText }
            ];

            // Update all elements that exist in the DOM
            [...aboutElements, ...indexElements].forEach(item => {
                const element = document.getElementById(item.id);
                if (element) {
                    element.textContent = item.value;
                }
            });
        }

        // Add event listeners to form fields
        const formFields = [
            'footer_title',
            'footer_description',
            'contact_title',
            'contact_address',
            'contact_phone',
            'contact_email',
            'links_title',
            'copyright_text'
        ];

        formFields.forEach(fieldId => {
            const element = document.getElementById(fieldId);
            if (element) {
                element.addEventListener('input', updatePreview);
            }
        });
    </script>
</body>
</html>
