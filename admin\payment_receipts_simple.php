<?php
session_start();
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Initialize variables
$payments = [];
$errorMessage = '';
$searchTerm = $_GET['search'] ?? '';

try {
    // Check if fee_payments table exists
    $tableCheck = $conn->query("SHOW TABLES LIKE 'fee_payments'");
    
    if ($tableCheck->num_rows == 0) {
        $errorMessage = 'fee_payments টেবিল পাওয়া যায়নি। প্রথমে সেটআপ চালান।';
    } else {
        // Simple query to get payments
        if (!empty($searchTerm)) {
            $query = "SELECT fp.*, 'Unknown Student' as student_name, 'Unknown Fee' as fee_type 
                     FROM fee_payments fp 
                     WHERE fp.receipt_no LIKE ? 
                     ORDER BY fp.created_at DESC LIMIT 20";
            $stmt = $conn->prepare($query);
            $searchParam = "%$searchTerm%";
            $stmt->bind_param("s", $searchParam);
            $stmt->execute();
            $result = $stmt->get_result();
        } else {
            $query = "SELECT fp.*, 'Unknown Student' as student_name, 'Unknown Fee' as fee_type 
                     FROM fee_payments fp 
                     ORDER BY fp.created_at DESC LIMIT 20";
            $result = $conn->query($query);
        }
        
        if ($result) {
            $payments = $result->fetch_all(MYSQLI_ASSOC);
            
            // Try to get additional info for each payment
            foreach ($payments as &$payment) {
                try {
                    // First get fee info
                    $feeQuery = "SELECT fee_type, student_id FROM fees WHERE id = ?";
                    $feeStmt = $conn->prepare($feeQuery);
                    $feeStmt->bind_param("i", $payment['fee_id']);
                    $feeStmt->execute();
                    $feeResult = $feeStmt->get_result();

                    if ($feeResult->num_rows > 0) {
                        $feeData = $feeResult->fetch_assoc();
                        $payment['fee_type'] = $feeData['fee_type'];

                        // Then get student info - check what columns exist
                        $studentColumns = [];
                        $columnsResult = $conn->query("DESCRIBE students");
                        while ($col = $columnsResult->fetch_assoc()) {
                            $studentColumns[] = $col['Field'];
                        }

                        // Build student query based on available columns
                        $studentFields = ['first_name', 'last_name'];
                        $optionalFields = ['roll_no', 'student_id', 'class_id'];

                        foreach ($optionalFields as $field) {
                            if (in_array($field, $studentColumns)) {
                                $studentFields[] = $field;
                            }
                        }

                        $studentQuery = "SELECT " . implode(', ', $studentFields) . " FROM students WHERE id = ?";
                        $studentStmt = $conn->prepare($studentQuery);
                        $studentStmt->bind_param("i", $feeData['student_id']);
                        $studentStmt->execute();
                        $studentResult = $studentStmt->get_result();

                        if ($studentResult->num_rows > 0) {
                            $studentData = $studentResult->fetch_assoc();
                            $payment['student_name'] = trim($studentData['first_name'] . ' ' . $studentData['last_name']);
                            $payment['roll_no'] = $studentData['roll_no'] ?? $studentData['student_id'] ?? 'N/A';

                            // Get class name if class_id exists
                            if (isset($studentData['class_id']) && !empty($studentData['class_id'])) {
                                $classQuery = "SELECT class_name FROM classes WHERE id = ?";
                                $classStmt = $conn->prepare($classQuery);
                                $classStmt->bind_param("i", $studentData['class_id']);
                                $classStmt->execute();
                                $classResult = $classStmt->get_result();
                                if ($classResult->num_rows > 0) {
                                    $classData = $classResult->fetch_assoc();
                                    $payment['class_name'] = $classData['class_name'];
                                }
                            }
                        }
                    }
                } catch (Exception $e) {
                    // Keep default values if error
                    $payment['student_name'] = 'Unknown Student';
                    $payment['fee_type'] = 'Unknown Fee';
                }
            }
        }
    }
} catch (Exception $e) {
    $errorMessage = 'ডাটাবেস এরর: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পেমেন্ট রিসিপ্ট</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', Arial, sans-serif;
            background-color: #f8f9fa;
        }
        .payment-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 20px;
        }
        .receipt-badge {
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .amount-display {
            font-size: 24px;
            font-weight: bold;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-receipt me-2"></i>পেমেন্ট রিসিপ্ট</h2>
                    <div>
                        <a href="setup_sample_data.php" class="btn btn-info me-2">
                            <i class="fas fa-database me-2"></i>সেটআপ
                        </a>
                        <a href="fee_management.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>ফিরে যান
                        </a>
                    </div>
                </div>

                <?php if (!empty($errorMessage)): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?= htmlspecialchars($errorMessage) ?>
                    <br><small><a href="setup_sample_data.php" class="alert-link">সেটআপ চালান</a></small>
                </div>
                <?php endif; ?>

                <!-- Search -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-8">
                                <input type="text" class="form-control" name="search" 
                                       value="<?= htmlspecialchars($searchTerm) ?>" 
                                       placeholder="রিসিপ্ট নম্বর দিয়ে খুঁজুন...">
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>খুঁজুন
                                </button>
                                <a href="payment_receipts_simple.php" class="btn btn-outline-secondary ms-1">
                                    <i class="fas fa-refresh"></i>
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Payments -->
                <div class="row">
                    <?php if (empty($payments)): ?>
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-receipt fa-3x mb-3 text-muted"></i>
                            <h4>কোন পেমেন্ট পাওয়া যায়নি</h4>
                            <?php if (empty($errorMessage)): ?>
                            <p>প্রথমে <a href="setup_sample_data.php">সেটআপ চালান</a> নমুনা ডেটার জন্য</p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php else: ?>
                    <?php foreach ($payments as $payment): ?>
                    <div class="col-md-6 col-lg-4">
                        <div class="payment-card">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span class="receipt-badge"><?= htmlspecialchars($payment['receipt_no'] ?? 'N/A') ?></span>
                                <small class="text-muted"><?= date('d/m/Y', strtotime($payment['payment_date'])) ?></small>
                            </div>
                            
                            <h6 class="mb-2">
                                <i class="fas fa-user me-2"></i><?= htmlspecialchars($payment['student_name']) ?>
                            </h6>
                            
                            <?php if (!empty($payment['roll_no'])): ?>
                            <p class="mb-1"><small>রোল: <?= htmlspecialchars($payment['roll_no']) ?></small></p>
                            <?php endif; ?>
                            
                            <?php if (!empty($payment['class_name'])): ?>
                            <p class="mb-2"><small>ক্লাস: <?= htmlspecialchars($payment['class_name']) ?></small></p>
                            <?php endif; ?>
                            
                            <p class="mb-2">
                                <strong>ফি ধরন:</strong> <?= htmlspecialchars($payment['fee_type']) ?>
                            </p>
                            
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="amount-display">৳<?= number_format($payment['amount'], 2) ?></div>
                                <span class="badge bg-info">
                                    <?= $payment['payment_method'] === 'cash' ? 'নগদ' : htmlspecialchars($payment['payment_method']) ?>
                                </span>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <a href="receipt_view.php?receipt_no=<?= urlencode($payment['receipt_no']) ?>" 
                                   target="_blank" class="btn btn-success">
                                    <i class="fas fa-receipt me-2"></i>রিসিপ্ট দেখুন
                                </a>
                                <a href="receipt_final.php?receipt_no=<?= urlencode($payment['receipt_no']) ?>" 
                                   target="_blank" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-print me-2"></i>প্রিন্ট ভার্সন
                                </a>
                            </div>
                            
                            <?php if (!empty($payment['notes'])): ?>
                            <div class="mt-2">
                                <small class="text-muted">নোট: <?= htmlspecialchars($payment['notes']) ?></small>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <!-- Stats -->
                <?php if (!empty($payments)): ?>
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-primary"><?= count($payments) ?></h3>
                                <p class="mb-0">পেমেন্ট পাওয়া গেছে</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-success">৳<?= number_format(array_sum(array_column($payments, 'amount')), 2) ?></h3>
                                <p class="mb-0">মোট পরিমাণ</p>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
