<?php
// Include database connection
require_once "../includes/dbh.inc.php";

// Get department info
$departmentId = 2; // Assuming 2 is the ID for Science department
$departmentQuery = "SELECT * FROM departments WHERE department_name LIKE '%বিজ্ঞান%' OR department_name LIKE '%Science%'";
$departmentResult = $conn->query($departmentQuery);

if ($departmentResult && $departmentResult->num_rows > 0) {
    $department = $departmentResult->fetch_assoc();
    $departmentId = $department['id'];
    $pageTitle = $department['department_name'];
} else {
    $pageTitle = "বিজ্ঞান বিভাগ";
}

// Get required subjects
$requiredSubjectsQuery = "SELECT DISTINCT s.*
                         FROM subjects s
                         INNER JOIN subject_departments sd ON s.id = sd.subject_id
                         WHERE sd.department_id = $departmentId
                         AND s.is_active = 1
                         AND (s.category = 'required' OR s.category LIKE '%required%')
                         ORDER BY s.subject_name";
$requiredSubjects = $conn->query($requiredSubjectsQuery);

// Get optional subjects
$optionalSubjectsQuery = "SELECT DISTINCT s.*
                         FROM subjects s
                         INNER JOIN subject_departments sd ON s.id = sd.subject_id
                         WHERE sd.department_id = $departmentId
                         AND s.is_active = 1
                         AND (s.category = 'optional' OR s.category LIKE '%optional%')
                         ORDER BY s.subject_name";
$optionalSubjects = $conn->query($optionalSubjectsQuery);

// Get fourth subjects
$fourthSubjectsQuery = "SELECT DISTINCT s.*
                       FROM subjects s
                       INNER JOIN subject_departments sd ON s.id = sd.subject_id
                       WHERE sd.department_id = $departmentId
                       AND s.is_active = 1
                       AND (s.category = 'fourth' OR s.category LIKE '%fourth%')
                       ORDER BY s.subject_name";
$fourthSubjects = $conn->query($fourthSubjectsQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include '../includes/global-head.php'; ?>
    <title><?php echo $pageTitle; ?> | ZFAW</title>

    <!-- Bootstrap CSS -->


    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: #f8f9fa;
        }

        .header {
            background-color: #006A4E;
            color: white;
            padding: 20px 0;
        }

        .subject-card {
            transition: transform 0.3s, box-shadow 0.3s;
            height: 100%;
        }

        .subject-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .subject-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
        }

        .section-title {
            position: relative;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }

        .section-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background-color: #006A4E;
        }

        .group-header {
            background-color: #f0f0f0;
            padding: 10px 15px;
            border-left: 4px solid #006A4E;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="../index.php">ZFAW</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">হোম</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../subjects.php">বিষয়সমূহ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../teachers.php">শিক্ষকবৃন্দ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../students.php">শিক্ষার্থীবৃন্দ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../login.php">লগইন</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><?php echo $pageTitle; ?></h1>
                    <p class="lead mb-0">বিজ্ঞান বিভাগের সকল বিষয়সমূহ এবং তাদের বিবরণ</p>
                </div>
                <div class="col-md-4 text-end">
                    <i class="fas fa-flask fa-5x text-white-50"></i>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container py-5">
        <div class="row mb-4">
            <div class="col-12">
                <h2 class="section-title">বিজ্ঞান বিভাগের বিষয়সমূহ</h2>
                <p>বিজ্ঞান বিভাগে শিক্ষার্থীদের জন্য নিম্নলিখিত বিষয়সমূহ পড়ানো হয়। এই বিষয়গুলি আবশ্যিক, নির্বাচিত এবং চতুর্থ বিষয় হিসেবে বিভক্ত করা হয়েছে।</p>
            </div>
        </div>

        <!-- আবশ্যিক বিষয় -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="group-header">
                    <h3 class="mb-0"><i class="fas fa-star me-2"></i> আবশ্যিক বিষয়</h3>
                </div>
            </div>

            <?php
            $colors = ['primary', 'success', 'info', 'secondary', 'danger', 'warning', 'dark'];
            $icons = ['book', 'language', 'calculator', 'desktop', 'atom', 'flask', 'dna', 'globe-asia'];
            $colorIndex = 0;
            $iconIndex = 0;

            if ($requiredSubjects && $requiredSubjects->num_rows > 0):
                while ($subject = $requiredSubjects->fetch_assoc()):
                    $color = $colors[$colorIndex % count($colors)];
                    $icon = $icons[$iconIndex % count($icons)];
                    $colorIndex++;
                    $iconIndex++;
            ?>
                <div class="col-md-3 mb-4">
                    <div class="card subject-card">
                        <div class="card-body text-center">
                            <div class="subject-icon bg-<?php echo $color; ?> text-white">
                                <i class="fas fa-<?php echo $icon; ?> fa-2x"></i>
                            </div>
                            <h4 class="card-title"><?php echo $subject['subject_name']; ?></h4>
                            <p class="card-text"><?php echo $subject['subject_code']; ?></p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-<?php echo $color; ?>"><?php echo $subject['category']; ?></span>
                                <a href="#" class="btn btn-sm btn-outline-<?php echo $color; ?>">বিস্তারিত</a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php
                endwhile;
            else:
            ?>
                <div class="col-12">
                    <div class="alert alert-info">
                        কোন আবশ্যিক বিষয় পাওয়া যায়নি।
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- নির্বাচিত বিষয় -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="group-header">
                    <h3 class="mb-0"><i class="fas fa-check-circle me-2"></i> নির্বাচিত বিষয়</h3>
                </div>
            </div>

            <?php
            $colorIndex = 0;
            $iconIndex = 0;

            if ($optionalSubjects && $optionalSubjects->num_rows > 0):
                while ($subject = $optionalSubjects->fetch_assoc()):
                    $color = $colors[$colorIndex % count($colors)];
                    $icon = $icons[$iconIndex % count($icons)];
                    $colorIndex++;
                    $iconIndex++;
            ?>
                <div class="col-md-3 mb-4">
                    <div class="card subject-card">
                        <div class="card-body text-center">
                            <div class="subject-icon bg-<?php echo $color; ?> text-white">
                                <i class="fas fa-<?php echo $icon; ?> fa-2x"></i>
                            </div>
                            <h4 class="card-title"><?php echo $subject['subject_name']; ?></h4>
                            <p class="card-text"><?php echo $subject['subject_code']; ?></p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-<?php echo $color; ?>"><?php echo $subject['category']; ?></span>
                                <a href="#" class="btn btn-sm btn-outline-<?php echo $color; ?>">বিস্তারিত</a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php
                endwhile;
            else:
            ?>
                <div class="col-12">
                    <div class="alert alert-info">
                        কোন নির্বাচিত বিষয় পাওয়া যায়নি।
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- চতুর্থ বিষয় -->
        <div class="row">
            <div class="col-12">
                <div class="group-header">
                    <h3 class="mb-0"><i class="fas fa-plus-circle me-2"></i> চতুর্থ বিষয়</h3>
                </div>
            </div>

            <?php
            $colorIndex = 0;
            $iconIndex = 0;

            if ($fourthSubjects && $fourthSubjects->num_rows > 0):
                while ($subject = $fourthSubjects->fetch_assoc()):
                    $color = $colors[$colorIndex % count($colors)];
                    $icon = $icons[$iconIndex % count($icons)];
                    $colorIndex++;
                    $iconIndex++;
            ?>
                <div class="col-md-3 mb-4">
                    <div class="card subject-card">
                        <div class="card-body text-center">
                            <div class="subject-icon bg-<?php echo $color; ?> text-white">
                                <i class="fas fa-<?php echo $icon; ?> fa-2x"></i>
                            </div>
                            <h4 class="card-title"><?php echo $subject['subject_name']; ?></h4>
                            <p class="card-text"><?php echo $subject['subject_code']; ?></p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-<?php echo $color; ?>"><?php echo $subject['category']; ?></span>
                                <a href="#" class="btn btn-sm btn-outline-<?php echo $color; ?>">বিস্তারিত</a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php
                endwhile;
            else:
            ?>
                <div class="col-12">
                    <div class="alert alert-info">
                        কোন চতুর্থ বিষয় পাওয়া যায়নি।
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>ZFAW School Management System</h5>
                    <p>একটি সম্পূর্ণ শিক্ষা প্রতিষ্ঠান ব্যবস্থাপনা সমাধান</p>
                </div>
                <div class="col-md-3">
                    <h5>দ্রুত লিঙ্ক</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">হোম</a></li>
                        <li><a href="../subjects.php" class="text-white">বিষয়সমূহ</a></li>
                        <li><a href="../teachers.php" class="text-white">শিক্ষকবৃন্দ</a></li>
                        <li><a href="../students.php" class="text-white">শিক্ষার্থীবৃন্দ</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>যোগাযোগ</h5>
                    <address>
                        <p><i class="fas fa-map-marker-alt me-2"></i> ঢাকা, বাংলাদেশ</p>
                        <p><i class="fas fa-phone me-2"></i> +৮৮০১৭১২৩৪৫৬৭৮</p>
                        <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p class="mb-0">&copy; <?php echo date('Y'); ?> ZFAW School Management System. সর্বস্বত্ব সংরক্ষিত।</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
