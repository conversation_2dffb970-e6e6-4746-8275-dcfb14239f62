<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';
require_once 'certificate_templates/simple_certificate.php';

// Get list of students for dropdowns
$studentsQuery = $conn->query("SELECT id, student_id, first_name, last_name FROM students ORDER BY first_name");
$studentsList = [];
while ($row = $studentsQuery->fetch_assoc()) {
    $studentsList[$row['id']] = $row['student_id'] . ' - ' . $row['first_name'] . ' ' . $row['last_name'];
}

$errorMessage = '';
$successMessage = '';
$previewHtml = '';

// Process certificate generation
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'preview') {
        $student_id = $_POST['student_id'];
        $certificate_title = $_POST['certificate_title'];
        $institute_name = $_POST['institute_name'];
        $logo_text = isset($_POST['logo_text']) ? $_POST['logo_text'] : $institute_name;
        $issued_by = $_POST['issued_by'];

        // Get student details
        $studentQuery = $conn->prepare("SELECT first_name, last_name, student_id as roll_number FROM students WHERE id = ?");
        $studentQuery->bind_param("i", $student_id);
        $studentQuery->execute();
        $studentResult = $studentQuery->get_result();
        $studentData = $studentResult->fetch_assoc();

        $student_name = $studentData['first_name'] . ' ' . $studentData['last_name'];
        $student_roll = $studentData['roll_number'] ?? $_POST['student_roll'];
        $student_class = $_POST['student_class'];
        $father_name = $_POST['father_name'];
        $mother_name = $_POST['mother_name'];

        // Generate certificate HTML with custom logo text
        $previewHtml = generate_simple_certificate(
            $student_name,
            $student_roll,
            $student_class,
            $father_name,
            $mother_name,
            $certificate_title,
            $institute_name,
            $issued_by,
            $logo_text
        );

        $successMessage = 'সার্টিফিকেট প্রিভিউ তৈরী হয়েছে। প্রিন্ট করতে নিচের বাটনে ক্লিক করুন।';
    }

    // Save certificate to database
    if (isset($_POST['action']) && $_POST['action'] === 'save') {
        $student_id = $_POST['student_id'];
        $certificate_title = $_POST['certificate_title'];
        $institute_name = $_POST['institute_name'];
        $logo_text = isset($_POST['logo_text']) ? $_POST['logo_text'] : $institute_name;
        $issued_by = $_POST['issued_by'];
        $student_roll = $_POST['student_roll'];
        $student_class = $_POST['student_class'];
        $father_name = $_POST['father_name'];
        $mother_name = $_POST['mother_name'];

        $description = "শ্রেণী: " . $student_class . ", রোল: " . $student_roll .
                      ", পিতার নাম: " . $father_name . ", মাতার নাম: " . $mother_name;

        // Check if certificates table exists
        $tableExists = false;
        $tablesResult = $conn->query("SHOW TABLES LIKE 'certificates'");
        if ($tablesResult && $tablesResult->num_rows > 0) {
            $tableExists = true;
        }

        // Create certificates table if it doesn't exist
        if (!$tableExists) {
            $createTableSQL = "CREATE TABLE certificates (
                id INT(11) NOT NULL AUTO_INCREMENT,
                student_id INT(11) NOT NULL,
                title VARCHAR(255) NOT NULL,
                description TEXT NOT NULL,
                certificate_date DATE NOT NULL,
                issued_by VARCHAR(255) NOT NULL,
                certificate_type VARCHAR(50) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id)
            )";

            if ($conn->query($createTableSQL)) {
                $successMessage = 'সার্টিফিকেট টেবিল সফলভাবে তৈরী করা হয়েছে।';
                $tableExists = true;
            } else {
                $errorMessage = 'সার্টিফিকেট টেবিল তৈরী করতে সমস্যা হয়েছে: ' . $conn->error;
            }
        }

        if ($tableExists) {
            // Insert certificate
            $stmt = $conn->prepare("INSERT INTO certificates (student_id, title, description, certificate_date, issued_by, certificate_type) VALUES (?, ?, ?, CURDATE(), ?, 'simple')");
            $stmt->bind_param("isss", $student_id, $certificate_title, $description, $issued_by);

            if ($stmt->execute()) {
                $successMessage = 'সার্টিফিকেট সফলভাবে সংরক্ষণ করা হয়েছে।';
            } else {
                $errorMessage = 'সার্টিফিকেট সংরক্ষণ করতে সমস্যা হয়েছে: ' . $conn->error;
            }

            $stmt->close();
        }
    }
}

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>সাধারণ সার্টিফিকেট - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .certificate-form {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .preview-container {
            margin-top: 30px;
            text-align: center;
        }
        .preview-frame {
            width: 100%;
            height: 800px;
            border: 1px solid #ddd;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .print-buttons {
            margin-top: 20px;
            text-align: center;
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="certificates.php">
                            <i class="fas fa-certificate me-2"></i> সার্টিফিকেট
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link active" href="simple_certificate.php">
                            <i class="fas fa-file-alt me-2"></i> সাধারণ সার্টিফিকেট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="upload_watermark.php">
                            <i class="fas fa-image me-2"></i> ওয়াটারমার্ক আপলোড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="remove_bengali_certificates.php">
                            <i class="fas fa-trash me-2"></i> বাংলা সার্টিফিকেট মুছুন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>সাধারণ সার্টিফিকেট তৈরী করুন</h2>
                        <p class="text-muted">শিক্ষার্থীর জন্য সাধারণ সার্টিফিকেট তৈরী করুন</p>
                    </div>
                </div>

                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Certificate Form -->
                <div class="certificate-form">
                    <form method="POST" action="simple_certificate.php" id="certificateForm">
                        <input type="hidden" name="action" value="preview">

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="institute_name" class="form-label">প্রতিষ্ঠানের নাম*</label>
                                <input type="text" class="form-control" id="institute_name" name="institute_name" value="প্রাথমিক শিক্ষা অধিদপ্তর" required>
                            </div>
                            <div class="col-md-6">
                                <label for="certificate_title" class="form-label">সার্টিফিকেটের শিরোনাম*</label>
                                <input type="text" class="form-control" id="certificate_title" name="certificate_title" value="প্রত্যয়নপত্র" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label for="logo_text" class="form-label">লোগো টেক্সট (ওয়াটারমার্ক)</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="logo_text" name="logo_text" value="প্রাথমিক শিক্ষা অধিদপ্তর">
                                    <a href="upload_watermark.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-image me-2"></i>ওয়াটারমার্ক ছবি আপলোড করুন
                                    </a>
                                </div>
                                <div class="form-text">
                                    এই টেক্সট সার্টিফিকেটের পিছনে ওয়াটারমার্ক হিসেবে প্রদর্শিত হবে। খালি রাখলে ডিফল্ট লোগো ব্যবহার করা হবে।<br>
                                    <strong>নোট:</strong> যদি আপনি ওয়াটারমার্ক ছবি আপলোড করেন, তাহলে ছবিটি টেক্সটের পরিবর্তে ব্যবহার করা হবে।
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="student_id" class="form-label">শিক্ষার্থী নির্বাচন করুন*</label>
                                <select class="form-select" id="student_id" name="student_id" required>
                                    <option value="">শিক্ষার্থী নির্বাচন করুন</option>
                                    <?php foreach ($studentsList as $id => $name): ?>
                                        <option value="<?php echo $id; ?>"><?php echo htmlspecialchars($name); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="student_roll" class="form-label">রোল নম্বর*</label>
                                <input type="text" class="form-control" id="student_roll" name="student_roll" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="student_class" class="form-label">শ্রেণী*</label>
                                <input type="text" class="form-control" id="student_class" name="student_class" required>
                            </div>
                            <div class="col-md-6">
                                <label for="father_name" class="form-label">পিতার নাম*</label>
                                <input type="text" class="form-control" id="father_name" name="father_name" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="mother_name" class="form-label">মাতার নাম*</label>
                                <input type="text" class="form-control" id="mother_name" name="mother_name" required>
                            </div>
                            <div class="col-md-6">
                                <label for="issued_by" class="form-label">ইস্যুকারী*</label>
                                <input type="text" class="form-control" id="issued_by" name="issued_by" placeholder="প্রধান শিক্ষক / অধ্যক্ষ" required>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-eye me-2"></i>প্রিভিউ দেখুন
                            </button>
                        </div>
                    </form>
                </div>

                <?php if (!empty($previewHtml)): ?>
                    <!-- Certificate Preview -->
                    <div class="preview-container">
                        <h3 class="mb-3">সার্টিফিকেট প্রিভিউ</h3>

                        <iframe id="previewFrame" class="preview-frame" srcdoc="<?php echo htmlspecialchars($previewHtml); ?>"></iframe>

                        <div class="print-buttons">
                            <form method="POST" action="simple_certificate.php" class="d-inline-block">
                                <input type="hidden" name="action" value="save">
                                <input type="hidden" name="student_id" value="<?php echo $_POST['student_id']; ?>">
                                <input type="hidden" name="student_roll" value="<?php echo $_POST['student_roll']; ?>">
                                <input type="hidden" name="student_class" value="<?php echo $_POST['student_class']; ?>">
                                <input type="hidden" name="father_name" value="<?php echo $_POST['father_name']; ?>">
                                <input type="hidden" name="mother_name" value="<?php echo $_POST['mother_name']; ?>">
                                <input type="hidden" name="certificate_title" value="<?php echo $_POST['certificate_title']; ?>">
                                <input type="hidden" name="institute_name" value="<?php echo $_POST['institute_name']; ?>">
                                <input type="hidden" name="logo_text" value="<?php echo $_POST['logo_text']; ?>">
                                <input type="hidden" name="issued_by" value="<?php echo $_POST['issued_by']; ?>">

                                <button type="submit" class="btn btn-success me-2">
                                    <i class="fas fa-save me-2"></i>সংরক্ষণ করুন
                                </button>
                            </form>

                            <button type="button" class="btn btn-primary" onclick="printCertificate()">
                                <i class="fas fa-print me-2"></i>প্রিন্ট করুন
                            </button>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Print Certificate Iframe (hidden) -->
    <iframe id="printFrame" style="display:none;"></iframe>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        // Auto-fill student information when a student is selected
        document.addEventListener('DOMContentLoaded', function() {
            const studentSelect = document.getElementById('student_id');

            // Create a map of student data
            const studentData = {};
            <?php
            // Get all student data
            $allStudentsQuery = $conn->query("SELECT id, student_id, first_name, last_name, father_name, mother_name, class FROM students");
            if ($allStudentsQuery) {
                while ($student = $allStudentsQuery->fetch_assoc()) {
                    echo "studentData[" . $student['id'] . "] = {";
                    echo "roll: '" . addslashes($student['student_id']) . "',";
                    echo "class: '" . addslashes($student['class'] ?? '') . "',";
                    echo "fatherName: '" . addslashes($student['father_name'] ?? '') . "',";
                    echo "motherName: '" . addslashes($student['mother_name'] ?? '') . "'";
                    echo "};\n";
                }
            }
            ?>

            // Add event listener to student select
            studentSelect.addEventListener('change', function() {
                const studentId = this.value;
                if (studentId && studentData[studentId]) {
                    // Auto-fill student information
                    document.getElementById('student_roll').value = studentData[studentId].roll || '';
                    document.getElementById('student_class').value = studentData[studentId].class || '';
                    document.getElementById('father_name').value = studentData[studentId].fatherName || '';
                    document.getElementById('mother_name').value = studentData[studentId].motherName || '';
                }
            });
        });

        function printCertificate() {
            var iframe = document.getElementById('previewFrame');
            var content = iframe.srcdoc;

            var printFrame = document.getElementById('printFrame');
            printFrame.contentDocument.open();
            printFrame.contentDocument.write(content);
            printFrame.contentDocument.close();

            setTimeout(function() {
                printFrame.contentWindow.print();
            }, 500);
        }
    </script>
</body>
</html>
