<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check and create fee_payments table if needed
$checkTable = $conn->query("SHOW TABLES LIKE 'fee_payments'");
if ($checkTable->num_rows == 0) {
    $createTable = "CREATE TABLE fee_payments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        student_id INT NOT NULL,
        fee_id INT NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        payment_date DATE NOT NULL,
        payment_method VARCHAR(50) NOT NULL,
        receipt_no VARCHAR(50) DEFAULT NULL,
        transaction_id VARCHAR(100) DEFAULT NULL,
        status VARCHAR(20) NOT NULL DEFAULT 'Pending',
        notes TEXT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREI<PERSON><PERSON> KEY (fee_id) REFERENCES fees(id) ON DELETE CASCADE
    )";
    $conn->query($createTable);
}

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Get student ID from request
$studentId = isset($_GET['student_id']) ? intval($_GET['student_id']) : 0;

// Basic validation
if ($studentId <= 0) {
    header("Location: fee_management.php");
    exit();
}

// Handle payment submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['process_payment'])) {
    $selectedFees = $_POST['selected_fees'] ?? [];
    $paymentAmounts = $_POST['payment_amounts'] ?? [];
    $paymentMethod = $_POST['payment_method'] ?? 'cash';
    $paymentNote = $_POST['payment_note'] ?? '';
    
    if (!empty($selectedFees)) {
        $conn->begin_transaction();
        
        try {
            $totalPaid = 0;
            $processedFees = [];
            
            foreach ($selectedFees as $feeId) {
                $feeId = intval($feeId);
                $paymentAmount = floatval($paymentAmounts[$feeId] ?? 0);
                
                if ($paymentAmount > 0) {
                    // Get current fee details
                    $feeQuery = "SELECT * FROM fees WHERE id = ? AND student_id = ?";
                    $feeStmt = $conn->prepare($feeQuery);
                    $feeStmt->bind_param("ii", $feeId, $studentId);
                    $feeStmt->execute();
                    $feeResult = $feeStmt->get_result();
                    
                    if ($fee = $feeResult->fetch_assoc()) {
                        $currentPaid = floatval($fee['paid']);
                        $totalAmount = floatval($fee['amount']);
                        $maxPayable = $totalAmount - $currentPaid;
                        
                        // Ensure payment doesn't exceed due amount
                        $actualPayment = min($paymentAmount, $maxPayable);
                        
                        if ($actualPayment > 0) {
                            $newPaidAmount = $currentPaid + $actualPayment;
                            $newStatus = ($newPaidAmount >= $totalAmount) ? 'paid' : 'partial';
                            
                            // Update fee
                            $updateQuery = "UPDATE fees SET paid = ?, payment_status = ?, payment_date = NOW() WHERE id = ?";
                            $updateStmt = $conn->prepare($updateQuery);
                            $updateStmt->bind_param("dsi", $newPaidAmount, $newStatus, $feeId);
                            $updateStmt->execute();
                            
                            // Generate receipt number
                            $receiptNo = 'RCPT-' . date('Ymd') . '-' . rand(1000, 9999);

                            // Insert payment record
                            $paymentQuery = "INSERT INTO fee_payments (fee_id, student_id, amount, payment_method, payment_date, receipt_no, notes, status) VALUES (?, ?, ?, ?, NOW(), ?, ?, 'Completed')";
                            $paymentStmt = $conn->prepare($paymentQuery);
                            $paymentStmt->bind_param("iidsss", $feeId, $studentId, $actualPayment, $paymentMethod, $receiptNo, $paymentNote);
                            $paymentStmt->execute();
                            
                            $totalPaid += $actualPayment;
                            $processedFees[] = $fee['fee_type'] . ' (৳' . number_format($actualPayment, 2) . ' - রসিদ: ' . $receiptNo . ')';
                        }
                    }
                }
            }
            
            $conn->commit();
            $successMessage = "সফলভাবে ৳" . number_format($totalPaid, 2) . " পরিশোধ করা হয়েছে। প্রক্রিয়াকৃত ফি: " . implode(', ', $processedFees);

            // Redirect after 3 seconds to refresh the page
            echo "<script>
                setTimeout(function() {
                    window.location.href = 'student_payment_form.php?student_id=$studentId';
                }, 3000);
            </script>";
            
        } catch (Exception $e) {
            $conn->rollback();
            $errorMessage = "পেমেন্ট প্রক্রিয়াকরণে ত্রুটি: " . $e->getMessage();
        }
    } else {
        $errorMessage = "কোন ফি নির্বাচন করা হয়নি।";
    }
}

// Get student information
$studentQuery = "SELECT s.*, c.class_name, d.department_name, ss.session_name 
                FROM students s 
                LEFT JOIN classes c ON s.class_id = c.id 
                LEFT JOIN departments d ON s.department_id = d.id 
                LEFT JOIN sessions ss ON s.session_id = ss.id 
                WHERE s.id = ?";
$stmt = $conn->prepare($studentQuery);
$stmt->bind_param("i", $studentId);
$stmt->execute();
$studentResult = $stmt->get_result();

if ($studentResult->num_rows === 0) {
    header("Location: fee_management.php");
    exit();
}

$student = $studentResult->fetch_assoc();

// Get all due fees for the student
$feesQuery = "SELECT f.*, c.class_name, ss.session_name 
             FROM fees f
             LEFT JOIN students s ON f.student_id = s.id
             LEFT JOIN classes c ON s.class_id = c.id
             LEFT JOIN sessions ss ON s.session_id = ss.id
             WHERE f.student_id = ? AND (f.payment_status = 'due' OR f.payment_status = 'partial')
             ORDER BY f.due_date ASC";

$stmt = $conn->prepare($feesQuery);
$stmt->bind_param("i", $studentId);
$stmt->execute();
$feesResult = $stmt->get_result();

$fees = [];
$totalDue = 0;
while ($fee = $feesResult->fetch_assoc()) {
    $dueAmount = $fee['amount'] - $fee['paid'];
    if ($dueAmount > 0) {
        $fee['due_amount'] = $dueAmount;
        $fees[] = $fee;
        $totalDue += $dueAmount;
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ছাত্র পেমেন্ট ফর্ম - <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: "Hind Siliguri", sans-serif;
        }
        
        .student-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .fee-card {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }
        
        .fee-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .fee-card.selected {
            border-color: #007bff;
            background-color: #f8f9ff;
        }
        
        .payment-summary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            position: sticky;
            top: 20px;
        }
        
        .amount-input {
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .amount-input:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        
        .btn-modern {
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .status-badge {
            border-radius: 20px;
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .alert-modern {
            border: none;
            border-radius: 10px;
            padding: 1rem 1.5rem;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4 bg-white p-3 rounded shadow-sm">
                    <div>
                        <h2 class="mb-0">
                            <i class="fas fa-credit-card text-primary me-2"></i>
                            ছাত্র পেমেন্ট ফর্ম
                        </h2>
                        <small class="text-muted">বকেয়া ফি পরিশোধ করুন</small>
                    </div>
                    <div>
                        <a href="student_dues_summary.php" class="btn btn-secondary me-2">
                            <i class="fas fa-arrow-left me-1"></i> বকেয়া তালিকা
                        </a>
                        <a href="fee_management.php" class="btn btn-outline-secondary">
                            <i class="fas fa-list me-1"></i> ফি ম্যানেজমেন্ট
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($successMessage)): ?>
            <div class="alert alert-success alert-modern">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $successMessage; ?>
            </div>
        <?php endif; ?>

        <?php if (isset($errorMessage)): ?>
            <div class="alert alert-danger alert-modern">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $errorMessage; ?>
            </div>
        <?php endif; ?>

        <!-- Student Information -->
        <div class="student-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h3 class="mb-2">
                        <i class="fas fa-user-graduate me-2"></i>
                        <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>
                    </h3>
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-1"><i class="fas fa-id-card me-2"></i> রোল: <?php echo htmlspecialchars($student['student_id']); ?></p>
                            <p class="mb-1"><i class="fas fa-graduation-cap me-2"></i> শ্রেণী: <?php echo htmlspecialchars($student['class_name'] ?? 'N/A'); ?></p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-1"><i class="fas fa-building me-2"></i> বিভাগ: <?php echo htmlspecialchars($student['department_name'] ?? 'N/A'); ?></p>
                            <p class="mb-1"><i class="fas fa-calendar me-2"></i> সেশন: <?php echo htmlspecialchars($student['session_name'] ?? 'N/A'); ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <div class="bg-white bg-opacity-20 rounded p-3">
                        <h4 class="mb-1">মোট বকেয়া</h4>
                        <h2 class="mb-0">৳ <?php echo number_format($totalDue, 2); ?></h2>
                        <small><?php echo count($fees); ?> টি বকেয়া ফি</small>
                    </div>
                </div>
            </div>
        </div>

        <?php if (!empty($fees)): ?>
        <!-- Payment Form -->
        <form method="POST" action="" id="paymentForm">
            <input type="hidden" name="process_payment" value="1">

            <div class="row">
                <!-- Fees List -->
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2"></i>
                                    বকেয়া ফি তালিকা
                                </h5>
                                <div>
                                    <button type="button" class="btn btn-sm btn-light" id="selectAllBtn">
                                        <i class="fas fa-check-square me-1"></i> সব নির্বাচন
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-light" id="clearAllBtn">
                                        <i class="fas fa-times me-1"></i> সব বাতিল
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <?php foreach ($fees as $index => $fee): ?>
                                <?php
                                $dueAmount = $fee['due_amount'];
                                $statusClass = '';
                                $statusText = '';

                                if ($fee['payment_status'] === 'due') {
                                    $statusClass = 'bg-danger';
                                    $statusText = 'বকেয়া';
                                } elseif ($fee['payment_status'] === 'partial') {
                                    $statusClass = 'bg-warning';
                                    $statusText = 'আংশিক';
                                }
                                ?>

                                <div class="fee-card p-3 border-bottom" data-fee-id="<?php echo $fee['id']; ?>">
                                    <div class="row align-items-center">
                                        <div class="col-md-1">
                                            <div class="form-check">
                                                <input class="form-check-input fee-checkbox" type="checkbox"
                                                       name="selected_fees[]" value="<?php echo $fee['id']; ?>"
                                                       id="fee_<?php echo $fee['id']; ?>">
                                                <label class="form-check-label" for="fee_<?php echo $fee['id']; ?>"></label>
                                            </div>
                                        </div>

                                        <div class="col-md-4">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($fee['fee_type']); ?></h6>
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                বকেয়া তারিখ: <?php echo date('d/m/Y', strtotime($fee['due_date'])); ?>
                                            </small>
                                        </div>

                                        <div class="col-md-2">
                                            <div class="text-center">
                                                <small class="text-muted d-block">মোট ফি</small>
                                                <strong>৳ <?php echo number_format($fee['amount'], 2); ?></strong>
                                            </div>
                                        </div>

                                        <div class="col-md-2">
                                            <div class="text-center">
                                                <small class="text-muted d-block">প্রদান</small>
                                                <strong class="text-success">৳ <?php echo number_format($fee['paid'], 2); ?></strong>
                                            </div>
                                        </div>

                                        <div class="col-md-2">
                                            <div class="text-center">
                                                <small class="text-muted d-block">বকেয়া</small>
                                                <strong class="text-danger">৳ <?php echo number_format($dueAmount, 2); ?></strong>
                                            </div>
                                        </div>

                                        <div class="col-md-1">
                                            <span class="badge status-badge <?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                        </div>
                                    </div>

                                    <div class="row mt-3" style="display: none;" id="payment_row_<?php echo $fee['id']; ?>">
                                        <div class="col-md-6 offset-md-1">
                                            <label class="form-label">পরিশোধের পরিমাণ</label>
                                            <div class="input-group">
                                                <span class="input-group-text">৳</span>
                                                <input type="number" class="form-control amount-input"
                                                       name="payment_amounts[<?php echo $fee['id']; ?>]"
                                                       id="amount_<?php echo $fee['id']; ?>"
                                                       min="0" max="<?php echo $dueAmount; ?>"
                                                       step="0.01" value="<?php echo $dueAmount; ?>"
                                                       placeholder="পরিমাণ লিখুন">
                                                <button type="button" class="btn btn-outline-secondary"
                                                        onclick="setFullAmount(<?php echo $fee['id']; ?>, <?php echo $dueAmount; ?>)">
                                                    সম্পূর্ণ
                                                </button>
                                            </div>
                                            <small class="text-muted">সর্বোচ্চ: ৳ <?php echo number_format($dueAmount, 2); ?></small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <!-- Payment Summary -->
                <div class="col-lg-4">
                    <div class="payment-summary">
                        <h5 class="mb-3">
                            <i class="fas fa-calculator me-2"></i>
                            পেমেন্ট সারসংক্ষেপ
                        </h5>

                        <div class="mb-3">
                            <div class="d-flex justify-content-between mb-2">
                                <span>নির্বাচিত ফি:</span>
                                <span id="selectedCount">0</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>মোট পরিমাণ:</span>
                                <span id="totalAmount">৳ 0.00</span>
                            </div>
                            <hr class="bg-white">
                            <div class="d-flex justify-content-between">
                                <strong>পরিশোধযোগ্য:</strong>
                                <strong id="payableAmount">৳ 0.00</strong>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">পেমেন্ট পদ্ধতি</label>
                            <select class="form-select" name="payment_method" required>
                                <option value="cash">নগদ</option>
                                <option value="bank">ব্যাংক ট্রান্সফার</option>
                                <option value="mobile_banking">মোবাইল ব্যাংকিং</option>
                                <option value="card">কার্ড</option>
                                <option value="cheque">চেক</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">নোট (ঐচ্ছিক)</label>
                            <textarea class="form-control" name="payment_note" rows="3"
                                      placeholder="পেমেন্ট সম্পর্কে কোন বিশেষ তথ্য..."></textarea>
                        </div>

                        <button type="submit" class="btn btn-success btn-modern w-100" id="paymentBtn" disabled>
                            <i class="fas fa-credit-card me-2"></i>
                            পেমেন্ট প্রক্রিয়া করুন
                        </button>

                        <div class="mt-3 text-center">
                            <small class="text-white-50">
                                <i class="fas fa-shield-alt me-1"></i>
                                নিরাপদ পেমেন্ট সিস্টেম
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <?php else: ?>
        <!-- No Dues Message -->
        <div class="row">
            <div class="col-12">
                <div class="card text-center py-5">
                    <div class="card-body">
                        <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                        <h4 class="text-success">কোন বকেয়া নেই!</h4>
                        <p class="text-muted">এই ছাত্রের কোন বকেয়া ফি নেই।</p>
                        <a href="student_dues_summary.php" class="btn btn-primary btn-modern">
                            <i class="fas fa-arrow-left me-1"></i> বকেয়া তালিকায় ফিরে যান
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        let selectedFees = new Set();

        // Set full amount for a fee
        function setFullAmount(feeId, maxAmount) {
            document.getElementById('amount_' + feeId).value = maxAmount;
            updateSummary();
        }

        // Update payment summary
        function updateSummary() {
            let selectedCount = 0;
            let totalAmount = 0;

            document.querySelectorAll('.fee-checkbox:checked').forEach(checkbox => {
                selectedCount++;
                const feeId = checkbox.value;
                const amountInput = document.getElementById('amount_' + feeId);
                if (amountInput) {
                    totalAmount += parseFloat(amountInput.value) || 0;
                }
            });

            document.getElementById('selectedCount').textContent = selectedCount;
            document.getElementById('totalAmount').textContent = '৳ ' + totalAmount.toFixed(2);
            document.getElementById('payableAmount').textContent = '৳ ' + totalAmount.toFixed(2);

            // Enable/disable payment button
            const paymentBtn = document.getElementById('paymentBtn');
            paymentBtn.disabled = selectedCount === 0 || totalAmount <= 0;
        }

        // Handle fee selection
        document.addEventListener('DOMContentLoaded', function() {
            // Fee checkbox change handler
            document.querySelectorAll('.fee-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const feeId = this.value;
                    const paymentRow = document.getElementById('payment_row_' + feeId);
                    const feeCard = this.closest('.fee-card');

                    if (this.checked) {
                        paymentRow.style.display = 'block';
                        feeCard.classList.add('selected');
                        selectedFees.add(feeId);
                    } else {
                        paymentRow.style.display = 'none';
                        feeCard.classList.remove('selected');
                        selectedFees.delete(feeId);
                    }

                    updateSummary();
                });
            });

            // Amount input change handler
            document.querySelectorAll('.amount-input').forEach(input => {
                input.addEventListener('input', updateSummary);
            });

            // Select all button
            document.getElementById('selectAllBtn').addEventListener('click', function() {
                document.querySelectorAll('.fee-checkbox').forEach(checkbox => {
                    if (!checkbox.checked) {
                        checkbox.click();
                    }
                });
            });

            // Clear all button
            document.getElementById('clearAllBtn').addEventListener('click', function() {
                document.querySelectorAll('.fee-checkbox:checked').forEach(checkbox => {
                    checkbox.click();
                });
            });

            // Form submission confirmation
            document.getElementById('paymentForm').addEventListener('submit', function(e) {
                const selectedCount = document.querySelectorAll('.fee-checkbox:checked').length;
                const totalAmount = document.getElementById('payableAmount').textContent;

                if (selectedCount === 0) {
                    e.preventDefault();
                    alert('অনুগ্রহ করে কমপক্ষে একটি ফি নির্বাচন করুন।');
                    return;
                }

                const confirmed = confirm(`আপনি কি ${totalAmount} পরিশোধ করতে চান? (${selectedCount}টি ফি নির্বাচিত)`);
                if (!confirmed) {
                    e.preventDefault();
                }
            });
        });
    </script>
</body>
</html>
