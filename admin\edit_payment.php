<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Check if ID was provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = 'পেমেন্ট আইডি প্রদান করা হয়নি!';
    header('Location: payment_dashboard.php');
    exit;
}

$paymentId = intval($_GET['id']);

// Get the payment details
$paymentQuery = "SELECT fp.*, f.fee_type, f.payment_status as fee_status, f.paid as fee_paid, f.amount as fee_amount, 
                s.first_name, s.last_name, s.student_id as roll, c.class_name
                FROM fee_payments fp
                JOIN fees f ON fp.fee_id = f.id
                JOIN students s ON f.student_id = s.id
                JOIN classes c ON s.class_id = c.id
                WHERE fp.id = ?";
$stmt = $conn->prepare($paymentQuery);
$stmt->bind_param('i', $paymentId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    $_SESSION['error'] = 'পেমেন্ট রেকর্ড খুঁজে পাওয়া যায়নি!';
    header('Location: payment_dashboard.php');
    exit;
}

$payment = $result->fetch_assoc();
$feeId = $payment['fee_id'];
$originalAmount = $payment['amount'];

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $newAmount = floatval($_POST['amount'] ?? 0);
    $paymentDate = $_POST['payment_date'] ?? date('Y-m-d');
    $paymentMethod = $_POST['payment_method'] ?? 'cash';
    $notes = $_POST['notes'] ?? '';
    
    // Validate data
    if ($newAmount <= 0) {
        $error = 'পেমেন্ট পরিমাণ শূন্যের বেশি হতে হবে।';
    } else {
        // Begin transaction
        $conn->begin_transaction();
        
        try {
            // Update the payment record
            $updatePaymentQuery = "UPDATE fee_payments SET amount = ?, payment_date = ?, payment_method = ?, notes = ? WHERE id = ?";
            $updateStmt = $conn->prepare($updatePaymentQuery);
            $updateStmt->bind_param('dsssi', $newAmount, $paymentDate, $paymentMethod, $notes, $paymentId);
            $updateStmt->execute();
            
            // Calculate the difference in amount
            $amountDifference = $newAmount - $originalAmount;
            
            // Update the fee record if amount changed
            if ($amountDifference != 0) {
                // Get current fee info
                $feeQuery = "SELECT * FROM fees WHERE id = ?";
                $feeStmt = $conn->prepare($feeQuery);
                $feeStmt->bind_param('i', $feeId);
                $feeStmt->execute();
                $feeResult = $feeStmt->get_result();
                $fee = $feeResult->fetch_assoc();
                
                // Calculate new paid amount
                $newPaidAmount = $fee['paid'] + $amountDifference;
                
                // Ensure paid amount can't be negative
                $newPaidAmount = max(0, $newPaidAmount);
                
                // Determine new payment status
                $newPaymentStatus = 'due';
                if ($newPaidAmount >= $fee['amount']) {
                    $newPaymentStatus = 'paid';
                } elseif ($newPaidAmount > 0) {
                    $newPaymentStatus = 'partial';
                }
                
                // Update fee record
                $updateFeeQuery = "UPDATE fees SET paid = ?, payment_status = ? WHERE id = ?";
                $updateFeeStmt = $conn->prepare($updateFeeQuery);
                $updateFeeStmt->bind_param('dsi', $newPaidAmount, $newPaymentStatus, $feeId);
                $updateFeeStmt->execute();
            }
            
            // Commit transaction
            $conn->commit();
            
            $_SESSION['success'] = 'পেমেন্ট সফলভাবে আপডেট করা হয়েছে!';
            header('Location: payment_dashboard.php');
            exit;
            
        } catch (Exception $e) {
            // Roll back transaction on error
            $conn->rollback();
            $error = 'পেমেন্ট আপডেট করতে সমস্যা: ' . $e->getMessage();
        }
    }
}

// Format date for form input
$formattedPaymentDate = date('Y-m-d', strtotime($payment['payment_date']));
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>পেমেন্ট সম্পাদনা - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <!-- Bootstrap CSS -->
    
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            border-radius: 10px 10px 0 0 !important;
            font-weight: bold;
        }
        .student-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .form-label {
            font-weight: 500;
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি ম্যানেজমেন্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="payment_dashboard.php">
                            <i class="fas fa-chart-line me-2"></i> পেমেন্ট ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-edit me-2"></i> পেমেন্ট সম্পাদনা</h5>
                            <a href="payment_dashboard.php" class="btn btn-light btn-sm">
                                <i class="fas fa-arrow-left me-1"></i> পেমেন্ট ড্যাশবোর্ডে ফিরে যান
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger">
                                <?= $error ?>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Student Info -->
                        <div class="student-info">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>শিক্ষার্থী:</strong> <?= htmlspecialchars($payment['first_name'] . ' ' . $payment['last_name']) ?> (<?= $payment['roll'] ?>)</p>
                                    <p><strong>ক্লাস:</strong> <?= htmlspecialchars($payment['class_name']) ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>ফি টাইপ:</strong> <?= htmlspecialchars($payment['fee_type']) ?></p>
                                    <p><strong>মোট ফি:</strong> ৳ <?= number_format($payment['fee_amount'], 2) ?></p>
                                    <p><strong>ফি পেমেন্ট স্ট্যাটাস:</strong> 
                                        <span class="badge <?= $payment['fee_status'] === 'paid' ? 'bg-success' : ($payment['fee_status'] === 'partial' ? 'bg-warning' : 'bg-danger') ?>">
                                            <?= $payment['fee_status'] === 'paid' ? 'পরিশোধিত' : ($payment['fee_status'] === 'partial' ? 'আংশিক' : 'বাকি') ?>
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payment Edit Form -->
                        <form method="POST" action="">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="amount" class="form-label">পরিমাণ (৳)</label>
                                    <input type="number" class="form-control" id="amount" name="amount" value="<?= $payment['amount'] ?>" min="1" step="0.01" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="payment_date" class="form-label">পেমেন্ট তারিখ</label>
                                    <input type="date" class="form-control" id="payment_date" name="payment_date" value="<?= $formattedPaymentDate ?>" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="payment_method" class="form-label">পেমেন্ট পদ্ধতি</label>
                                <select class="form-select" id="payment_method" name="payment_method" required>
                                    <option value="cash" <?= $payment['payment_method'] === 'cash' ? 'selected' : '' ?>>Cash</option>
                                    <option value="bank" <?= $payment['payment_method'] === 'bank' ? 'selected' : '' ?>>Bank</option>
                                    <option value="bkash" <?= $payment['payment_method'] === 'bkash' ? 'selected' : '' ?>>bKash</option>
                                    <option value="nagad" <?= $payment['payment_method'] === 'nagad' ? 'selected' : '' ?>>Nagad</option>
                                    <option value="rocket" <?= $payment['payment_method'] === 'rocket' ? 'selected' : '' ?>>Rocket</option>
                                    <option value="other" <?= $payment['payment_method'] === 'other' ? 'selected' : '' ?>>Other</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="notes" class="form-label">নোট</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"><?= htmlspecialchars($payment['notes']) ?></textarea>
                            </div>
                            <div class="d-flex justify-content-between">
                                <a href="payment_dashboard.php" class="btn btn-secondary">বাতিল করুন</a>
                                <button type="submit" class="btn btn-primary">পেমেন্ট আপডেট করুন</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 