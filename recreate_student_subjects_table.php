<?php
// Include database connection
require_once 'includes/dbh.inc.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Recreate Student Subjects Table</h1>";

// Check if student_subjects table exists
$tableExistsQuery = "SHOW TABLES LIKE 'student_subjects'";
$tableExists = $conn->query($tableExistsQuery)->num_rows > 0;

if ($tableExists) {
    echo "<p>student_subjects table exists. Dropping it...</p>";
    
    // Drop the table
    $dropTableQuery = "DROP TABLE student_subjects";
    
    if ($conn->query($dropTableQuery)) {
        echo "<p>student_subjects table dropped successfully!</p>";
    } else {
        echo "<p>Error dropping student_subjects table: " . $conn->error . "</p>";
        exit;
    }
}

// Create the table
echo "<p>Creating student_subjects table...</p>";

$createTableQuery = "CREATE TABLE student_subjects (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    subject_id INT(11) NOT NULL,
    category ENUM('required', 'optional', 'fourth') NOT NULL DEFAULT 'optional',
    session_id INT(11) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($createTableQuery)) {
    echo "<p>student_subjects table created successfully!</p>";
} else {
    echo "<p>Error creating student_subjects table: " . $conn->error . "</p>";
    exit;
}

// Add foreign keys
echo "<p>Adding foreign keys...</p>";

$addForeignKeysQuery = "ALTER TABLE student_subjects 
    ADD CONSTRAINT fk_student_subjects_student FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    ADD CONSTRAINT fk_student_subjects_subject FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    ADD CONSTRAINT fk_student_subjects_session FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE";

if ($conn->query($addForeignKeysQuery)) {
    echo "<p>Foreign keys added successfully!</p>";
} else {
    echo "<p>Error adding foreign keys: " . $conn->error . "</p>";
    
    // If adding all foreign keys at once fails, try adding them one by one
    echo "<p>Trying to add foreign keys one by one...</p>";
    
    $addStudentFKQuery = "ALTER TABLE student_subjects 
        ADD CONSTRAINT fk_student_subjects_student FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE";
    
    if ($conn->query($addStudentFKQuery)) {
        echo "<p>Student foreign key added successfully!</p>";
    } else {
        echo "<p>Error adding student foreign key: " . $conn->error . "</p>";
    }
    
    $addSubjectFKQuery = "ALTER TABLE student_subjects 
        ADD CONSTRAINT fk_student_subjects_subject FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE";
    
    if ($conn->query($addSubjectFKQuery)) {
        echo "<p>Subject foreign key added successfully!</p>";
    } else {
        echo "<p>Error adding subject foreign key: " . $conn->error . "</p>";
    }
    
    $addSessionFKQuery = "ALTER TABLE student_subjects 
        ADD CONSTRAINT fk_student_subjects_session FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE";
    
    if ($conn->query($addSessionFKQuery)) {
        echo "<p>Session foreign key added successfully!</p>";
    } else {
        echo "<p>Error adding session foreign key: " . $conn->error . "</p>";
    }
}

// Check the final table structure
echo "<h2>Final Table Structure</h2>";

$tableStructureQuery = "DESCRIBE student_subjects";
$tableStructure = $conn->query($tableStructureQuery);

if ($tableStructure) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($column = $tableStructure->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>Error checking table structure: " . $conn->error . "</p>";
}

// Check foreign keys
echo "<h2>Foreign Keys</h2>";

$foreignKeysQuery = "SELECT * FROM information_schema.KEY_COLUMN_USAGE 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'student_subjects' 
                    AND REFERENCED_TABLE_NAME IS NOT NULL";
$foreignKeys = $conn->query($foreignKeysQuery);

if ($foreignKeys && $foreignKeys->num_rows > 0) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Column</th><th>Referenced Table</th><th>Referenced Column</th></tr>";
    
    while ($fk = $foreignKeys->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$fk['COLUMN_NAME']}</td>";
        echo "<td>{$fk['REFERENCED_TABLE_NAME']}</td>";
        echo "<td>{$fk['REFERENCED_COLUMN_NAME']}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>No foreign keys found or error checking foreign keys: " . $conn->error . "</p>";
}

echo "<h2>Table Recreation Complete</h2>";
echo "<p>The student_subjects table has been recreated.</p>";
echo "<p><a href='admin/student_subject_selection.php?id=STD-601523'>Go to Student Subject Selection</a></p>";
?>
