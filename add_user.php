<?php
require_once 'includes/dbh.inc.php';

echo "<h2>User Creation Tool</h2>";

// Only process if form is submitted
if (isset($_POST['submit'])) {
    $username = $_POST['username'];
    $password = $_POST['password'];
    $userType = $_POST['userType'];
    
    // Basic validation
    if (empty($username) || empty($password)) {
        echo "<p style='color:red;'>Please fill in all fields.</p>";
    } else {
        // Check if username already exists
        $checkSql = "SELECT * FROM users WHERE username = ?";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->bind_param("s", $username);
        $checkStmt->execute();
        $result = $checkStmt->get_result();
        
        if ($result->num_rows > 0) {
            echo "<p style='color:red;'>Username already exists. Please choose another one.</p>";
        } else {
            // Hash the password
            $hashedPwd = password_hash($password, PASSWORD_DEFAULT);
            
            // Insert user
            $sql = "INSERT INTO users (username, password, user_type) VALUES (?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("sss", $username, $hashedPwd, $userType);
            
            if ($stmt->execute()) {
                $userId = $stmt->insert_id;
                echo "<p style='color:green;'>User created successfully!</p>";
                
                // If student type, create a placeholder student record
                if ($userType == 'student') {
                    $studentId = 'STU' . date('Y') . str_pad($userId, 4, '0', STR_PAD_LEFT);
                    $studentSql = "INSERT INTO students (student_id, first_name, last_name, gender, user_id) 
                                   VALUES (?, ?, ?, 'male', ?)";
                    $studentStmt = $conn->prepare($studentSql);
                    $firstName = $username; // Use username as placeholder
                    $lastName = "Student"; // Placeholder
                    $studentStmt->bind_param("sssi", $studentId, $firstName, $lastName, $userId);
                    
                    if ($studentStmt->execute()) {
                        echo "<p style='color:green;'>Student record created with ID: $studentId</p>";
                    } else {
                        echo "<p style='color:red;'>Error creating student record: " . $studentStmt->error . "</p>";
                    }
                }
            } else {
                echo "<p style='color:red;'>Error: " . $stmt->error . "</p>";
            }
        }
    }
}

// Display current users
$sql = "SELECT * FROM users";
$result = $conn->query($sql);

echo "<h3>Current Users:</h3>";
if ($result->num_rows > 0) {
    echo "<ul>";
    while ($row = $result->fetch_assoc()) {
        echo "<li>ID: " . $row['id'] . ", Username: " . $row['username'] . ", User Type: " . $row['user_type'] . "</li>";
    }
    echo "</ul>";
} else {
    echo "<p>No users found.</p>";
}
?>

<h3>Add New User</h3>
<form method="post" action="">
    <div>
        <label for="username">Username:</label>
        <input type="text" name="username" id="username" required>
    </div>
    <div style="margin-top: 10px;">
        <label for="password">Password:</label>
        <input type="password" name="password" id="password" required>
    </div>
    <div style="margin-top: 10px;">
        <label for="userType">User Type:</label>
        <select name="userType" id="userType">
            <option value="admin">Admin</option>
            <option value="teacher">Teacher</option>
            <option value="student">Student</option>
            <option value="staff">Staff</option>
        </select>
    </div>
    <div style="margin-top: 15px;">
        <input type="submit" name="submit" value="Create User">
    </div>
</form>

<p><a href="index.php">Back to Home</a></p> 