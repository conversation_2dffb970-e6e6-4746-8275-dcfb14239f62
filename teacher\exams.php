<?php
session_start();

// Check if user is logged in and is a teacher
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'teacher') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get teacher information with department name
$userId = $_SESSION['userId'];
$username = $_SESSION['username'] ?? '';

// First try to get teacher by user_id
$sql = "SELECT t.*, d.department_name
        FROM teachers t
        LEFT JOIN departments d ON t.department_id = d.id
        WHERE t.user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();

// If no results, try to get by username
if ($result->num_rows === 0 && !empty($username)) {
    $sql = "SELECT t.*, d.department_name
            FROM teachers t
            LEFT JOIN departments d ON t.department_id = d.id
            WHERE t.username = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
}

$teacher = $result->fetch_assoc();

// If still no teacher found, redirect to login
if (!$teacher) {
    header("Location: ../index.php");
    exit();
}

// Create exams table if it doesn't exist
$examsTableQuery = "CREATE TABLE IF NOT EXISTS exams (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    exam_name VARCHAR(100) NOT NULL,
    exam_type VARCHAR(50) NOT NULL,
    subject_id INT(11),
    class_id INT(11),
    department_id INT(11),
    exam_date DATE NOT NULL,
    start_time TIME,
    end_time TIME,
    total_marks INT(11) NOT NULL,
    passing_marks INT(11),
    created_by INT(11),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";
$conn->query($examsTableQuery);

// Check if department_id column exists in exams table
$checkDepartmentIdColumnQuery = "SHOW COLUMNS FROM exams LIKE 'department_id'";
$departmentIdColumnResult = $conn->query($checkDepartmentIdColumnQuery);
if ($departmentIdColumnResult->num_rows == 0) {
    // Add department_id column if it doesn't exist
    $addDepartmentIdColumnQuery = "ALTER TABLE exams ADD COLUMN department_id INT(11) NULL";
    $conn->query($addDepartmentIdColumnQuery);
}

// Check if subject_id column exists in exams table
$checkSubjectIdColumnQuery = "SHOW COLUMNS FROM exams LIKE 'subject_id'";
$subjectIdColumnResult = $conn->query($checkSubjectIdColumnQuery);
if ($subjectIdColumnResult->num_rows == 0) {
    // Add subject_id column if it doesn't exist
    $addSubjectIdColumnQuery = "ALTER TABLE exams ADD COLUMN subject_id INT(11) NULL";
    $conn->query($addSubjectIdColumnQuery);
}

// Check if class_id column exists in exams table
$checkClassIdColumnQuery = "SHOW COLUMNS FROM exams LIKE 'class_id'";
$classIdColumnResult = $conn->query($checkClassIdColumnQuery);
if ($classIdColumnResult->num_rows == 0) {
    // Add class_id column if it doesn't exist
    $addClassIdColumnQuery = "ALTER TABLE exams ADD COLUMN class_id INT(11) NULL";
    $conn->query($addClassIdColumnQuery);
}

// Handle exam addition
$success_msg = '';
$error_msg = '';

if (isset($_POST['add_exam'])) {
    $examName = $conn->real_escape_string($_POST['exam_name']);
    $examType = $conn->real_escape_string($_POST['exam_type']);
    $subjectId = $_POST['subject_id'] ? intval($_POST['subject_id']) : null;
    $classId = $_POST['class_id'] ? intval($_POST['class_id']) : null;
    $departmentId = $teacher['department_id'];
    $examDate = $conn->real_escape_string($_POST['exam_date']);
    $startTime = $conn->real_escape_string($_POST['start_time'] ?? null);
    $endTime = $conn->real_escape_string($_POST['end_time'] ?? null);
    $totalMarks = intval($_POST['total_marks']);
    $passingMarks = $_POST['passing_marks'] ? intval($_POST['passing_marks']) : null;
    $createdBy = $teacher['id'];

    if (empty($examName) || empty($examType) || empty($examDate) || empty($totalMarks)) {
        $error_msg = "পরীক্ষার নাম, ধরন, তারিখ এবং মোট নম্বর আবশ্যক";
    } else {
        $insertQuery = "INSERT INTO exams (exam_name, exam_type, subject_id, class_id, department_id, exam_date, start_time, end_time, total_marks, passing_marks, created_by)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($insertQuery);
        $stmt->bind_param("ssiissssiii", $examName, $examType, $subjectId, $classId, $departmentId, $examDate, $startTime, $endTime, $totalMarks, $passingMarks, $createdBy);

        if ($stmt->execute()) {
            $success_msg = "পরীক্ষা সফলভাবে যোগ করা হয়েছে";
        } else {
            $error_msg = "পরীক্ষা যোগ করতে সমস্যা হয়েছে: " . $conn->error;
        }
    }
}

// Handle exam deletion
if (isset($_GET['delete'])) {
    $examId = intval($_GET['delete']);

    // Check if exam belongs to this teacher's department
    $checkQuery = "SELECT id FROM exams WHERE id = ? AND department_id = ?";
    $checkStmt = $conn->prepare($checkQuery);
    $checkStmt->bind_param("ii", $examId, $teacher['department_id']);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();

    if ($checkResult->num_rows > 0) {
        $deleteQuery = "DELETE FROM exams WHERE id = ?";
        $deleteStmt = $conn->prepare($deleteQuery);
        $deleteStmt->bind_param("i", $examId);

        if ($deleteStmt->execute()) {
            $success_msg = "পরীক্ষা সফলভাবে মুছে ফেলা হয়েছে";
        } else {
            $error_msg = "পরীক্ষা মুছে ফেলতে সমস্যা হয়েছে: " . $conn->error;
        }
    } else {
        $error_msg = "পরীক্ষা মুছে ফেলার অনুমতি নেই";
    }
}

// Get all exams for this teacher's department
try {
    // Check if subjects table exists
    $checkSubjectsTableQuery = "SHOW TABLES LIKE 'subjects'";
    $subjectsTableResult = $conn->query($checkSubjectsTableQuery);
    $subjectsTableExists = $subjectsTableResult->num_rows > 0;

    // Check if classes table exists
    $checkClassesTableQuery = "SHOW TABLES LIKE 'classes'";
    $classesTableResult = $conn->query($checkClassesTableQuery);
    $classesTableExists = $classesTableResult->num_rows > 0;

    // Build query based on existing tables
    $examsQuery = "SELECT e.*";

    if ($subjectsTableExists) {
        $examsQuery .= ", s.subject_name";
    }

    if ($classesTableExists) {
        $examsQuery .= ", c.class_name";
    }

    $examsQuery .= " FROM exams e";

    if ($subjectsTableExists) {
        $examsQuery .= " LEFT JOIN subjects s ON e.subject_id = s.id";
    }

    if ($classesTableExists) {
        $examsQuery .= " LEFT JOIN classes c ON e.class_id = c.id";
    }

    // First check if there are any exams with department_id
    $checkExamsQuery = "SELECT COUNT(*) as count FROM exams WHERE department_id IS NOT NULL";
    $checkResult = $conn->query($checkExamsQuery);
    $checkData = $checkResult->fetch_assoc();

    if ($checkData['count'] > 0 && !empty($teacher['department_id'])) {
        // If there are exams with department_id, get them for this teacher's department
        $examsQuery .= " WHERE e.department_id = ? ORDER BY e.exam_date DESC";
        $stmt = $conn->prepare($examsQuery);
        $stmt->bind_param("i", $teacher['department_id']);
    } else {
        // If no exams with department_id, get all exams (temporary solution)
        $examsQuery .= " ORDER BY e.exam_date DESC";
        $stmt = $conn->prepare($examsQuery);
    }

    $stmt->execute();
    $exams = $stmt->get_result();
} catch (Exception $e) {
    $error_msg = "পরীক্ষার তথ্য লোড করতে সমস্যা হয়েছে: " . $e->getMessage();
    $exams = null;
}

// Get subjects for dropdown
try {
    // Check if subjects table exists
    $checkSubjectsTableQuery = "SHOW TABLES LIKE 'subjects'";
    $subjectsTableResult = $conn->query($checkSubjectsTableQuery);

    if ($subjectsTableResult->num_rows > 0) {
        // If subjects table exists, get subjects for this teacher's department
        $subjectsQuery = "SELECT id, subject_name, subject_code
                         FROM subjects
                         WHERE department_id = ?
                         ORDER BY subject_name";
        $stmt = $conn->prepare($subjectsQuery);
        $stmt->bind_param("i", $teacher['department_id']);
        $stmt->execute();
        $subjects = $stmt->get_result();
    } else {
        $subjects = null;
    }
} catch (Exception $e) {
    $subjects = null;
}

// Get classes for dropdown
try {
    // Check if classes table exists
    $checkClassesTableQuery = "SHOW TABLES LIKE 'classes'";
    $classesTableResult = $conn->query($checkClassesTableQuery);

    if ($classesTableResult->num_rows > 0) {
        // If classes table exists, get all classes
        $classesQuery = "SELECT id, class_name
                        FROM classes
                        ORDER BY class_name";
        $stmt = $conn->prepare($classesQuery);
        $stmt->execute();
        $classes = $stmt->get_result();
    } else {
        $classes = null;
    }
} catch (Exception $e) {
    $classes = null;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include __DIR__ . '/includes/global-head.php'; ?>
    <title>Exams - Teacher Panel</title>

    <style>
        body, html, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, select, textarea, label, li, th, td {
            font-family: 'Hind Siliguri', sans-serif;
        }

        .table-responsive {
            overflow-x: auto;
        }

        .upcoming {
            background-color: #d1ecf1;
        }

        .past {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include_once 'sidebar.php'; ?>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h2 class="mb-4">পরীক্ষা ব্যবস্থাপনা</h2>

                        <?php if (!empty($success_msg)): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <?php echo $success_msg; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($error_msg)): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <?php echo $error_msg; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>

                        <!-- Add Exam Form -->
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">নতুন পরীক্ষা যোগ করুন</h5>
                            </div>
                            <div class="card-body">
                                <form action="" method="post">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="exam_name" class="form-label">পরীক্ষার নাম*</label>
                                            <input type="text" class="form-control" id="exam_name" name="exam_name" required>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="exam_type" class="form-label">পরীক্ষার ধরন*</label>
                                            <select class="form-control" id="exam_type" name="exam_type" required>
                                                <option value="">পরীক্ষার ধরন নির্বাচন করুন</option>
                                                <option value="midterm">মিডটার্ম</option>
                                                <option value="final">ফাইনাল</option>
                                                <option value="quiz">কুইজ</option>
                                                <option value="assignment">অ্যাসাইনমেন্ট</option>
                                                <option value="practical">প্র্যাকটিক্যাল</option>
                                                <option value="other">অন্যান্য</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="subject_id" class="form-label">বিষয়</label>
                                            <select class="form-control" id="subject_id" name="subject_id">
                                                <option value="">বিষয় নির্বাচন করুন</option>
                                                <?php if ($subjects && $subjects->num_rows > 0): ?>
                                                    <?php while ($subject = $subjects->fetch_assoc()): ?>
                                                        <option value="<?php echo $subject['id']; ?>">
                                                            <?php echo $subject['subject_name'] . ' (' . $subject['subject_code'] . ')'; ?>
                                                        </option>
                                                    <?php endwhile; ?>
                                                <?php endif; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="class_id" class="form-label">শ্রেণী</label>
                                            <select class="form-control" id="class_id" name="class_id">
                                                <option value="">শ্রেণী নির্বাচন করুন</option>
                                                <?php if ($classes && $classes->num_rows > 0): ?>
                                                    <?php while ($class = $classes->fetch_assoc()): ?>
                                                        <option value="<?php echo $class['id']; ?>">
                                                            <?php echo $class['class_name']; ?>
                                                        </option>
                                                    <?php endwhile; ?>
                                                <?php endif; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-4">
                                            <label for="exam_date" class="form-label">পরীক্ষার তারিখ*</label>
                                            <input type="date" class="form-control" id="exam_date" name="exam_date" required>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="start_time" class="form-label">শুরুর সময়</label>
                                            <input type="time" class="form-control" id="start_time" name="start_time">
                                        </div>
                                        <div class="col-md-4">
                                            <label for="end_time" class="form-label">শেষের সময়</label>
                                            <input type="time" class="form-control" id="end_time" name="end_time">
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="total_marks" class="form-label">মোট নম্বর*</label>
                                            <input type="number" class="form-control" id="total_marks" name="total_marks" min="1" required>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="passing_marks" class="form-label">পাস নম্বর</label>
                                            <input type="number" class="form-control" id="passing_marks" name="passing_marks" min="1">
                                        </div>
                                    </div>
                                    <button type="submit" name="add_exam" class="btn btn-primary">পরীক্ষা যোগ করুন</button>
                                </form>
                            </div>
                        </div>

                        <!-- Exams List -->
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0">পরীক্ষার তালিকা</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>পরীক্ষার নাম</th>
                                                <th>ধরন</th>
                                                <th>বিষয়</th>
                                                <th>শ্রেণী</th>
                                                <th>তারিখ</th>
                                                <th>সময়</th>
                                                <th>মোট নম্বর</th>
                                                <th>পাস নম্বর</th>
                                                <th>অ্যাকশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (isset($exams) && $exams->num_rows > 0): ?>
                                                <?php while ($exam = $exams->fetch_assoc()):
                                                    $isUpcoming = strtotime($exam['exam_date']) >= strtotime(date('Y-m-d'));
                                                    $rowClass = $isUpcoming ? 'upcoming' : 'past';
                                                ?>
                                                    <tr class="<?php echo $rowClass; ?>">
                                                        <td><?php echo htmlspecialchars($exam['exam_name']); ?></td>
                                                        <td>
                                                            <?php
                                                                $examTypeText = '';
                                                                switch ($exam['exam_type']) {
                                                                    case 'midterm':
                                                                        $examTypeText = 'মিডটার্ম';
                                                                        break;
                                                                    case 'final':
                                                                        $examTypeText = 'ফাইনাল';
                                                                        break;
                                                                    case 'quiz':
                                                                        $examTypeText = 'কুইজ';
                                                                        break;
                                                                    case 'assignment':
                                                                        $examTypeText = 'অ্যাসাইনমেন্ট';
                                                                        break;
                                                                    case 'practical':
                                                                        $examTypeText = 'প্র্যাকটিক্যাল';
                                                                        break;
                                                                    default:
                                                                        $examTypeText = 'অন্যান্য';
                                                                }
                                                                echo $examTypeText;
                                                            ?>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($exam['subject_name'] ?? 'N/A'); ?></td>
                                                        <td><?php echo htmlspecialchars($exam['class_name'] ?? 'N/A'); ?></td>
                                                        <td><?php echo date('d M Y', strtotime($exam['exam_date'])); ?></td>
                                                        <td>
                                                            <?php
                                                                if (!empty($exam['start_time']) && !empty($exam['end_time'])) {
                                                                    echo date('h:i A', strtotime($exam['start_time'])) . ' - ' . date('h:i A', strtotime($exam['end_time']));
                                                                } else {
                                                                    echo 'N/A';
                                                                }
                                                            ?>
                                                        </td>
                                                        <td><?php echo $exam['total_marks']; ?></td>
                                                        <td><?php echo $exam['passing_marks'] ?? 'N/A'; ?></td>
                                                        <td>
                                                            <a href="edit_exam.php?id=<?php echo $exam['id']; ?>" class="btn btn-sm btn-warning">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="exams.php?delete=<?php echo $exam['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই পরীক্ষা মুছে ফেলতে চান?')">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                            <?php if ($isUpcoming): ?>
                                                                <a href="add_results.php?exam_id=<?php echo $exam['id']; ?>" class="btn btn-sm btn-success">
                                                                    <i class="fas fa-plus-circle"></i> ফলাফল
                                                                </a>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="9" class="text-center">কোন পরীক্ষা খুঁজে পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Set default date to today
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('exam_date').value = today;

            // Validate end time is after start time
            const startTimeInput = document.getElementById('start_time');
            const endTimeInput = document.getElementById('end_time');

            endTimeInput.addEventListener('change', function() {
                if (startTimeInput.value && endTimeInput.value) {
                    if (endTimeInput.value <= startTimeInput.value) {
                        alert('শেষের সময় অবশ্যই শুরুর সময়ের পরে হতে হবে');
                        endTimeInput.value = '';
                    }
                }
            });

            // Validate passing marks is less than total marks
            const totalMarksInput = document.getElementById('total_marks');
            const passingMarksInput = document.getElementById('passing_marks');

            passingMarksInput.addEventListener('change', function() {
                if (parseInt(passingMarksInput.value) > parseInt(totalMarksInput.value)) {
                    alert('পাস নম্বর অবশ্যই মোট নম্বরের চেয়ে কম হতে হবে');
                    passingMarksInput.value = '';
                }
            });

            totalMarksInput.addEventListener('change', function() {
                if (passingMarksInput.value && parseInt(passingMarksInput.value) > parseInt(totalMarksInput.value)) {
                    passingMarksInput.value = '';
                }
            });
        });
    </script>
</body>
</html>
