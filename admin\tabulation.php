<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get parameters
$examId = isset($_GET['exam_id']) ? intval($_GET['exam_id']) : 0;
$classGroup = isset($_GET['class_group']) ? $_GET['class_group'] : '';

if (!$examId) {
    die("পরীক্ষা ID প্রয়োজন।");
}

// Initialize default values
$exam = [
    'exam_name' => 'নমুনা পরীক্ষা',
    'subject_name' => 'বাংলা',
    'class_name' => 'ক্লাস ১',
    'class_id' => 1
];

$exams = [];
$students = [];
$allResults = [];

try {
    // Get exam details
    $examTableName = 'exams_primary_lower';
    $resultsTableName = 'results_primary_lower';

    $examQuery = "SELECT e.*, c.class_name, s.subject_name
                  FROM $examTableName e
                  LEFT JOIN classes c ON e.class_id = c.id
                  LEFT JOIN subjects s ON e.subject_id = s.id
                  WHERE e.id = $examId";
    $examResult = $conn->query($examQuery);

    if ($examResult && $examResult->num_rows > 0) {
        $exam = $examResult->fetch_assoc();
    }
} catch (Exception $e) {
    // Use default values if database error
}

try {
    // Get all exams for this class to show comprehensive tabulation
    $allExamsQuery = "SELECT e.*, s.subject_name
                      FROM $examTableName e
                      LEFT JOIN subjects s ON e.subject_id = s.id
                      WHERE e.class_id = {$exam['class_id']}
                      ORDER BY e.exam_date, e.id";
    $allExamsResult = $conn->query($allExamsQuery);

    if ($allExamsResult) {
        while ($examRow = $allExamsResult->fetch_assoc()) {
            $exams[] = $examRow;
        }
    }

    // Get students with all their marks
    $studentsQuery = "SELECT s.*,
                      COALESCE(s.student_name, s.first_name) as name,
                      COALESCE(s.roll_number, s.student_id) as roll
                      FROM students s
                      WHERE s.class_id = {$exam['class_id']}
                      ORDER BY CAST(COALESCE(s.roll_number, s.student_id) AS UNSIGNED)";
    $studentsResult = $conn->query($studentsQuery);

    if ($studentsResult) {
        while ($student = $studentsResult->fetch_assoc()) {
            $students[] = $student;
        }
    }

    // Get all results for tabulation
    foreach ($exams as $examRow) {
        $resultsQuery = "SELECT student_id, obtained_marks, attendance
                         FROM $resultsTableName
                         WHERE exam_id = {$examRow['id']}";
        $resultsResult = $conn->query($resultsQuery);

        if ($resultsResult) {
            while ($result = $resultsResult->fetch_assoc()) {
                $allResults[$examRow['id']][$result['student_id']] = $result;
            }
        }
    }
} catch (Exception $e) {
    // Use default values if database error
    $exams = [
        ['id' => 1, 'subject_name' => 'বাংলা', 'total_marks' => 100, 'passing_marks' => 33],
        ['id' => 2, 'subject_name' => 'ইংরেজি', 'total_marks' => 100, 'passing_marks' => 33],
        ['id' => 3, 'subject_name' => 'গণিত', 'total_marks' => 100, 'passing_marks' => 33]
    ];

    $students = [
        ['id' => 1, 'roll' => '001', 'name' => 'রহিম আহমেদ'],
        ['id' => 2, 'roll' => '002', 'name' => 'করিম হাসান'],
        ['id' => 3, 'roll' => '003', 'name' => 'ফাতেমা খাতুন']
    ];
}

function calculateGrade($obtainedMarks, $totalMarks, $passingMarks) {
    if ($obtainedMarks < $passingMarks) return 'F';
    
    $percentage = ($obtainedMarks / $totalMarks) * 100;
    
    if ($percentage >= 80) return 'A+';
    if ($percentage >= 70) return 'A';
    if ($percentage >= 60) return 'A-';
    if ($percentage >= 50) return 'B';
    if ($percentage >= 40) return 'C';
    return 'D';
}

function getGradePoint($grade) {
    switch ($grade) {
        case 'A+': return 5.00;
        case 'A': return 4.00;
        case 'A-': return 3.50;
        case 'B': return 3.00;
        case 'C': return 2.00;
        case 'D': return 1.00;
        case 'F': return 0.00;
        default: return 0.00;
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>টেবুলেশন - <?php echo htmlspecialchars($exam['class_name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: #f8f9fa;
            font-size: 12px;
        }
        .tabulation-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
        }
        .school-name {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .tabulation-title {
            font-size: 20px;
            font-weight: 600;
            color: #007bff;
            margin-bottom: 10px;
        }
        .class-info {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .tabulation-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .tabulation-table th {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            font-weight: 600;
            text-align: center;
            padding: 10px 5px;
            border: 1px solid #fff;
            font-size: 11px;
            vertical-align: middle;
        }
        .tabulation-table td {
            padding: 8px 5px;
            text-align: center;
            border: 1px solid #dee2e6;
            vertical-align: middle;
            font-size: 11px;
        }
        .student-info {
            background: #f8f9fa;
            font-weight: 600;
        }
        .marks-cell {
            font-weight: 600;
            color: #007bff;
        }
        .grade-cell {
            font-weight: 600;
            font-size: 10px;
        }
        .total-cell {
            background: #e3f2fd;
            font-weight: 700;
            color: #1976d2;
        }
        .average-cell {
            background: #f3e5f5;
            font-weight: 700;
            color: #7b1fa2;
        }
        .final-grade {
            background: #e8f5e8;
            font-weight: 700;
            color: #2e7d32;
            font-size: 12px;
        }
        .absent-mark {
            color: #dc3545;
            font-weight: 600;
        }
        .subject-header {
            writing-mode: vertical-rl;
            text-orientation: mixed;
            min-width: 40px;
            max-width: 40px;
        }
        @media print {
            body { 
                background: white !important; 
                font-size: 10px !important;
            }
            .no-print { display: none !important; }
            .tabulation-table { box-shadow: none !important; }
            .tabulation-table th { font-size: 9px !important; }
            .tabulation-table td { font-size: 9px !important; }
        }
    </style>
</head>
<body>
    <div class="container-fluid py-3">
        <!-- Print Button -->
        <div class="no-print mb-3">
            <button onclick="window.print()" class="btn btn-primary btn-sm">
                <i class="fas fa-print"></i> প্রিন্ট করুন
            </button>
            <button onclick="window.close()" class="btn btn-secondary btn-sm ms-2">
                <i class="fas fa-times"></i> বন্ধ করুন
            </button>
        </div>

        <!-- Header -->
        <div class="tabulation-header">
            <div class="school-name">আপনার স্কুলের নাম</div>
            <div class="tabulation-title">টেবুলেশন শীট</div>
        </div>

        <!-- Class Info -->
        <div class="class-info">
            <strong>শ্রেণি:</strong> <?php echo htmlspecialchars($exam['class_name']); ?> |
            <strong>মোট পরীক্ষা:</strong> <?php echo count($exams); ?>টি |
            <strong>মোট ছাত্র/ছাত্রী:</strong> <?php echo count($students); ?>জন |
            <strong>তৈরির তারিখ:</strong> <?php echo date('d/m/Y'); ?>
        </div>

        <!-- Tabulation Table -->
        <div class="tabulation-table">
            <table class="table table-bordered mb-0">
                <thead>
                    <tr>
                        <th rowspan="2" style="width: 5%;">ক্রমিক</th>
                        <th rowspan="2" style="width: 8%;">রোল নং</th>
                        <th rowspan="2" style="width: 20%;">ছাত্র/ছাত্রীর নাম</th>
                        
                        <?php foreach ($exams as $examRow): ?>
                            <th colspan="2" style="width: <?php echo count($exams) > 0 ? (50 / count($exams)) : 10; ?>%;">
                                <div class="subject-header">
                                    <?php echo htmlspecialchars($examRow['subject_name'] ?? 'বিষয়'); ?>
                                    <br><small>(<?php echo $examRow['total_marks']; ?>)</small>
                                </div>
                            </th>
                        <?php endforeach; ?>
                        
                        <th rowspan="2" style="width: 6%;">মোট নম্বর</th>
                        <th rowspan="2" style="width: 6%;">গড়</th>
                        <th rowspan="2" style="width: 5%;">গ্রেড</th>
                        <th rowspan="2" style="width: 5%;">জিপিএ</th>
                    </tr>
                    <tr>
                        <?php foreach ($exams as $examRow): ?>
                            <th style="font-size: 9px;">নম্বর</th>
                            <th style="font-size: 9px;">গ্রেড</th>
                        <?php endforeach; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    if (!empty($students)):
                        $serial = 1;
                        foreach ($students as $student): 
                            $totalMarks = 0;
                            $totalPossible = 0;
                            $validExams = 0;
                            $totalGradePoints = 0;
                    ?>
                        <tr>
                            <td class="student-info"><?php echo $serial++; ?></td>
                            <td class="student-info"><strong><?php echo htmlspecialchars($student['roll'] ?? 'N/A'); ?></strong></td>
                            <td class="student-info" style="text-align: left; padding-left: 10px;">
                                <?php echo htmlspecialchars($student['name'] ?? 'নাম নেই'); ?>
                            </td>
                            
                            <?php foreach ($exams as $examRow): 
                                $result = $allResults[$examRow['id']][$student['id']] ?? null;
                                $obtainedMarks = $result ? floatval($result['obtained_marks']) : 0;
                                $attendance = $result ? $result['attendance'] : 'absent';
                                
                                if ($attendance === 'present' && $obtainedMarks > 0) {
                                    $totalMarks += $obtainedMarks;
                                    $totalPossible += $examRow['total_marks'];
                                    $validExams++;
                                    
                                    $grade = calculateGrade($obtainedMarks, $examRow['total_marks'], $examRow['passing_marks']);
                                    $totalGradePoints += getGradePoint($grade);
                                } else {
                                    $grade = 'Ab';
                                }
                            ?>
                                <td class="marks-cell">
                                    <?php if ($attendance === 'present'): ?>
                                        <?php echo $obtainedMarks; ?>
                                    <?php else: ?>
                                        <span class="absent-mark">Ab</span>
                                    <?php endif; ?>
                                </td>
                                <td class="grade-cell">
                                    <?php if ($attendance === 'present'): ?>
                                        <?php echo $grade; ?>
                                    <?php else: ?>
                                        <span class="absent-mark">-</span>
                                    <?php endif; ?>
                                </td>
                            <?php endforeach; ?>
                            
                            <?php 
                            $averageMarks = $validExams > 0 ? $totalMarks / $validExams : 0;
                            $averageGPA = $validExams > 0 ? $totalGradePoints / $validExams : 0;
                            
                            // Calculate overall grade based on average
                            if ($validExams > 0) {
                                $overallGrade = '';
                                if ($averageGPA >= 4.5) $overallGrade = 'A+';
                                elseif ($averageGPA >= 3.5) $overallGrade = 'A';
                                elseif ($averageGPA >= 3.0) $overallGrade = 'A-';
                                elseif ($averageGPA >= 2.5) $overallGrade = 'B';
                                elseif ($averageGPA >= 2.0) $overallGrade = 'C';
                                elseif ($averageGPA >= 1.0) $overallGrade = 'D';
                                else $overallGrade = 'F';
                            } else {
                                $overallGrade = 'N/A';
                            }
                            ?>
                            
                            <td class="total-cell"><?php echo $validExams > 0 ? number_format($totalMarks, 1) : '-'; ?></td>
                            <td class="average-cell"><?php echo $validExams > 0 ? number_format($averageMarks, 1) : '-'; ?></td>
                            <td class="final-grade"><?php echo $overallGrade; ?></td>
                            <td class="final-grade"><?php echo $validExams > 0 ? number_format($averageGPA, 2) : '-'; ?></td>
                        </tr>
                    <?php 
                        endforeach;
                    else: 
                    ?>
                        <tr>
                            <td colspan="<?php echo 7 + (count($exams) * 2); ?>" class="text-center text-muted py-4">
                                কোন ছাত্র/ছাত্রী পাওয়া যায়নি।
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Legend -->
        <div class="mt-3 p-3" style="background: #f8f9fa; border-radius: 8px; font-size: 11px;">
            <div class="row">
                <div class="col-md-6">
                    <strong>গ্রেড স্কেল:</strong>
                    <span class="ms-2">A+ (80-100) | A (70-79) | A- (60-69) | B (50-59) | C (40-49) | D (33-39) | F (0-32)</span>
                </div>
                <div class="col-md-6">
                    <strong>সংকেত:</strong>
                    <span class="ms-2">Ab = অনুপস্থিত | - = প্রযোজ্য নয়</span>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="mt-3 text-center" style="font-size: 11px; color: #6c757d;">
            <p>এই টেবুলেশন শীট <?php echo date('d/m/Y h:i A'); ?> তারিখে তৈরি করা হয়েছে।</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php if (isset($conn)) $conn->close(); ?>
