<?php
session_start();
require_once 'includes/dbh.inc.php';

// Check if user is logged in
if (!isset($_SESSION['userId'])) {
    header("Location: login.php");
    exit();
}

// Get user information
$userId = $_SESSION['userId'];
$userType = $_SESSION['userType'];

// Get user data
$userQuery = "SELECT * FROM users WHERE id = ?";
$stmt = $conn->prepare($userQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$userResult = $stmt->get_result();
$user = $userResult->fetch_assoc();

// Create settings table if it doesn't exist
$createSettingsTableQuery = "CREATE TABLE IF NOT EXISTS user_settings (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11) NOT NULL,
    theme VARCHAR(50) DEFAULT 'light',
    language VARCHAR(50) DEFAULT 'bn',
    notifications TINYINT(1) DEFAULT 1,
    email_notifications TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY(user_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
)";
$conn->query($createSettingsTableQuery);

// Get user settings
$settingsQuery = "SELECT * FROM user_settings WHERE user_id = ?";
$stmt = $conn->prepare($settingsQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$settingsResult = $stmt->get_result();

// If settings don't exist, create default settings
if ($settingsResult->num_rows === 0) {
    $insertSettingsQuery = "INSERT INTO user_settings (user_id) VALUES (?)";
    $stmt = $conn->prepare($insertSettingsQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    
    // Fetch the newly created settings
    $stmt = $conn->prepare($settingsQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $settingsResult = $stmt->get_result();
}

$settings = $settingsResult->fetch_assoc();

// Handle settings update
$successMessage = '';
$errorMessage = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_settings'])) {
    $theme = $_POST['theme'] ?? 'light';
    $language = $_POST['language'] ?? 'bn';
    $notifications = isset($_POST['notifications']) ? 1 : 0;
    $emailNotifications = isset($_POST['email_notifications']) ? 1 : 0;
    
    // Update settings
    $updateSettingsQuery = "UPDATE user_settings SET 
                          theme = ?,
                          language = ?,
                          notifications = ?,
                          email_notifications = ?
                          WHERE user_id = ?";
    $stmt = $conn->prepare($updateSettingsQuery);
    $stmt->bind_param("ssiii", $theme, $language, $notifications, $emailNotifications, $userId);
    
    if ($stmt->execute()) {
        $successMessage = "সেটিংস সফলভাবে আপডেট করা হয়েছে!";
        
        // Refresh settings
        $stmt = $conn->prepare($settingsQuery);
        $stmt->bind_param("i", $userId);
        $stmt->execute();
        $settingsResult = $stmt->get_result();
        $settings = $settingsResult->fetch_assoc();
    } else {
        $errorMessage = "সেটিংস আপডেট করতে সমস্যা হয়েছে!";
    }
}

// Handle notification preferences update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_notification_preferences'])) {
    $examNotifications = isset($_POST['exam_notifications']) ? 1 : 0;
    $resultNotifications = isset($_POST['result_notifications']) ? 1 : 0;
    $feeNotifications = isset($_POST['fee_notifications']) ? 1 : 0;
    $eventNotifications = isset($_POST['event_notifications']) ? 1 : 0;
    
    // Check if notification_preferences table exists
    $checkTableQuery = "SHOW TABLES LIKE 'notification_preferences'";
    $tableResult = $conn->query($checkTableQuery);
    
    if ($tableResult->num_rows === 0) {
        // Create notification_preferences table
        $createTableQuery = "CREATE TABLE IF NOT EXISTS notification_preferences (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            user_id INT(11) NOT NULL,
            exam_notifications TINYINT(1) DEFAULT 1,
            result_notifications TINYINT(1) DEFAULT 1,
            fee_notifications TINYINT(1) DEFAULT 1,
            event_notifications TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY(user_id),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )";
        $conn->query($createTableQuery);
    }
    
    // Check if user has notification preferences
    $checkPreferencesQuery = "SELECT * FROM notification_preferences WHERE user_id = ?";
    $stmt = $conn->prepare($checkPreferencesQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $preferencesResult = $stmt->get_result();
    
    if ($preferencesResult->num_rows === 0) {
        // Insert new preferences
        $insertPreferencesQuery = "INSERT INTO notification_preferences 
                                 (user_id, exam_notifications, result_notifications, fee_notifications, event_notifications) 
                                 VALUES (?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($insertPreferencesQuery);
        $stmt->bind_param("iiiii", $userId, $examNotifications, $resultNotifications, $feeNotifications, $eventNotifications);
    } else {
        // Update existing preferences
        $updatePreferencesQuery = "UPDATE notification_preferences SET 
                                 exam_notifications = ?,
                                 result_notifications = ?,
                                 fee_notifications = ?,
                                 event_notifications = ?
                                 WHERE user_id = ?";
        $stmt = $conn->prepare($updatePreferencesQuery);
        $stmt->bind_param("iiiii", $examNotifications, $resultNotifications, $feeNotifications, $eventNotifications, $userId);
    }
    
    if ($stmt->execute()) {
        $successMessage = "নোটিফিকেশন সেটিংস সফলভাবে আপডেট করা হয়েছে!";
    } else {
        $errorMessage = "নোটিফিকেশন সেটিংস আপডেট করতে সমস্যা হয়েছে!";
    }
}

// Get notification preferences
$notificationPreferences = null;
$checkPreferencesQuery = "SHOW TABLES LIKE 'notification_preferences'";
$tableResult = $conn->query($checkPreferencesQuery);

if ($tableResult->num_rows > 0) {
    $preferencesQuery = "SELECT * FROM notification_preferences WHERE user_id = ?";
    $stmt = $conn->prepare($preferencesQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $preferencesResult = $stmt->get_result();
    
    if ($preferencesResult->num_rows > 0) {
        $notificationPreferences = $preferencesResult->fetch_assoc();
    }
}

// Include header
include_once 'includes/header.php';
?>

<div class="container mt-4 mb-5">
    <div class="row">
        <div class="col-md-12">
            <h2 class="text-center mb-4">সেটিংস</h2>
            <p class="text-center text-muted mb-4">আপনার অ্যাকাউন্ট সেটিংস পরিবর্তন করুন</p>
        </div>
    </div>
    
    <?php if (!empty($successMessage)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i> <?php echo $successMessage; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($errorMessage)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i> <?php echo $errorMessage; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <div class="row">
        <div class="col-md-3 mb-4">
            <!-- Settings Navigation -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">সেটিংস মেনু</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush" id="settings-tabs" role="tablist">
                        <a class="list-group-item list-group-item-action active" id="general-tab" data-bs-toggle="list" href="#general" role="tab" aria-controls="general">
                            <i class="fas fa-cog me-2"></i> সাধারণ সেটিংস
                        </a>
                        <a class="list-group-item list-group-item-action" id="notifications-tab" data-bs-toggle="list" href="#notifications" role="tab" aria-controls="notifications">
                            <i class="fas fa-bell me-2"></i> নোটিফিকেশন সেটিংস
                        </a>
                        <a class="list-group-item list-group-item-action" id="privacy-tab" data-bs-toggle="list" href="#privacy" role="tab" aria-controls="privacy">
                            <i class="fas fa-user-shield me-2"></i> প্রাইভেসি সেটিংস
                        </a>
                        <a class="list-group-item list-group-item-action" id="help-tab" data-bs-toggle="list" href="#help" role="tab" aria-controls="help">
                            <i class="fas fa-question-circle me-2"></i> সাহায্য
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- User Info -->
            <div class="card mt-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">ইউজার তথ্য</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="avatar-circle mx-auto">
                            <i class="fas fa-user fa-2x"></i>
                        </div>
                    </div>
                    <p><strong>ইউজারনেম:</strong> <?php echo $user['username']; ?></p>
                    <p><strong>ইউজার টাইপ:</strong> 
                        <?php 
                        switch ($userType) {
                            case 'student':
                                echo 'শিক্ষার্থী';
                                break;
                            case 'teacher':
                                echo 'শিক্ষক';
                                break;
                            case 'admin':
                                echo 'অ্যাডমিন';
                                break;
                            case 'staff':
                                echo 'কর্মচারী';
                                break;
                            default:
                                echo $userType;
                        }
                        ?>
                    </p>
                    <p><strong>অ্যাকাউন্ট তৈরির তারিখ:</strong> <?php echo date('d M Y', strtotime($user['created_at'])); ?></p>
                    <div class="text-center mt-3">
                        <a href="profile.php" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-user me-1"></i> প্রোফাইল দেখুন
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <div class="tab-content">
                <!-- General Settings -->
                <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">সাধারণ সেটিংস</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="">
                                <div class="mb-3">
                                    <label for="theme" class="form-label">থিম</label>
                                    <select class="form-select" id="theme" name="theme">
                                        <option value="light" <?php echo ($settings['theme'] === 'light') ? 'selected' : ''; ?>>লাইট</option>
                                        <option value="dark" <?php echo ($settings['theme'] === 'dark') ? 'selected' : ''; ?>>ডার্ক</option>
                                        <option value="system" <?php echo ($settings['theme'] === 'system') ? 'selected' : ''; ?>>সিস্টেম ডিফল্ট</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="language" class="form-label">ভাষা</label>
                                    <select class="form-select" id="language" name="language">
                                        <option value="bn" <?php echo ($settings['language'] === 'bn') ? 'selected' : ''; ?>>বাংলা</option>
                                        <option value="en" <?php echo ($settings['language'] === 'en') ? 'selected' : ''; ?>>English</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="notifications" name="notifications" <?php echo ($settings['notifications'] == 1) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="notifications">নোটিফিকেশন সক্রিয় করুন</label>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="email_notifications" name="email_notifications" <?php echo ($settings['email_notifications'] == 1) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="email_notifications">ইমেইল নোটিফিকেশন সক্রিয় করুন</label>
                                </div>
                                
                                <div class="text-end">
                                    <button type="submit" name="update_settings" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i> সেটিংস সংরক্ষণ করুন
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Notification Settings -->
                <div class="tab-pane fade" id="notifications" role="tabpanel" aria-labelledby="notifications-tab">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">নোটিফিকেশন সেটিংস</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="">
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="exam_notifications" name="exam_notifications" 
                                        <?php echo (isset($notificationPreferences['exam_notifications']) && $notificationPreferences['exam_notifications'] == 1) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="exam_notifications">পরীক্ষা সংক্রান্ত নোটিফিকেশন</label>
                                    <div class="form-text">পরীক্ষার সময়সূচী, পরিবর্তন ইত্যাদি সম্পর্কে নোটিফিকেশন পান</div>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="result_notifications" name="result_notifications"
                                        <?php echo (isset($notificationPreferences['result_notifications']) && $notificationPreferences['result_notifications'] == 1) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="result_notifications">ফলাফল সংক্রান্ত নোটিফিকেশন</label>
                                    <div class="form-text">পরীক্ষার ফলাফল প্রকাশিত হলে নোটিফিকেশন পান</div>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="fee_notifications" name="fee_notifications"
                                        <?php echo (isset($notificationPreferences['fee_notifications']) && $notificationPreferences['fee_notifications'] == 1) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="fee_notifications">ফি সংক্রান্ত নোটিফিকেশন</label>
                                    <div class="form-text">ফি পরিশোধের শেষ তারিখ, নতুন ফি ইত্যাদি সম্পর্কে নোটিফিকেশন পান</div>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="event_notifications" name="event_notifications"
                                        <?php echo (isset($notificationPreferences['event_notifications']) && $notificationPreferences['event_notifications'] == 1) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="event_notifications">ইভেন্ট সংক্রান্ত নোটিফিকেশন</label>
                                    <div class="form-text">আসন্ন ইভেন্ট, অনুষ্ঠান ইত্যাদি সম্পর্কে নোটিফিকেশন পান</div>
                                </div>
                                
                                <div class="text-end">
                                    <button type="submit" name="update_notification_preferences" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i> সেটিংস সংরক্ষণ করুন
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Privacy Settings -->
                <div class="tab-pane fade" id="privacy" role="tabpanel" aria-labelledby="privacy-tab">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">প্রাইভেসি সেটিংস</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> প্রাইভেসি সেটিংস বর্তমানে উপলব্ধ নয়। আমরা শীঘ্রই এই ফিচারটি যোগ করব।
                            </div>
                            
                            <h6 class="mt-4">আপনার ডাটা</h6>
                            <p>আমরা আপনার ব্যক্তিগত তথ্য সুরক্ষিত রাখি এবং কোন তৃতীয় পক্ষের সাথে শেয়ার করি না। আপনার ডাটা সম্পর্কে আরও জানতে, অনুগ্রহ করে প্রশাসনের সাথে যোগাযোগ করুন।</p>
                            
                            <div class="text-end">
                                <a href="#" class="btn btn-outline-danger disabled">
                                    <i class="fas fa-trash-alt me-2"></i> আমার অ্যাকাউন্ট মুছুন
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Help Section -->
                <div class="tab-pane fade" id="help" role="tabpanel" aria-labelledby="help-tab">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">সাহায্য এবং সাপোর্ট</h5>
                        </div>
                        <div class="card-body">
                            <h6>প্রায়শই জিজ্ঞাসিত প্রশ্ন</h6>
                            
                            <div class="accordion mt-3" id="faqAccordion">
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="faqOne">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="false" aria-controls="collapseOne">
                                            আমি কিভাবে আমার পাসওয়ার্ড পরিবর্তন করব?
                                        </button>
                                    </h2>
                                    <div id="collapseOne" class="accordion-collapse collapse" aria-labelledby="faqOne" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            আপনি প্রোফাইল পেজে গিয়ে পাসওয়ার্ড পরিবর্তন করতে পারেন। প্রোফাইল পেজে "পাসওয়ার্ড পরিবর্তন" সেকশনে আপনার বর্তমান পাসওয়ার্ড এবং নতুন পাসওয়ার্ড দিয়ে ফর্মটি পূরণ করুন।
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="faqTwo">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                            আমি কিভাবে আমার প্রোফাইল ছবি পরিবর্তন করব?
                                        </button>
                                    </h2>
                                    <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="faqTwo" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            প্রোফাইল ছবি পরিবর্তন করতে প্রোফাইল পেজে যান। সেখানে "প্রোফাইল আপডেট" সেকশনে আপনি আপনার ছবি আপলোড করতে পারবেন।
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="faqThree">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                            আমি কিভাবে নোটিফিকেশন সেটিংস পরিবর্তন করব?
                                        </button>
                                    </h2>
                                    <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="faqThree" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            নোটিফিকেশন সেটিংস পরিবর্তন করতে সেটিংস পেজের "নোটিফিকেশন সেটিংস" ট্যাবে যান। সেখানে আপনি বিভিন্ন ধরনের নোটিফিকেশন সক্রিয় বা নিষ্ক্রিয় করতে পারবেন।
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <h6 class="mt-4">যোগাযোগ করুন</h6>
                            <p>আপনার যদি কোন প্রশ্ন বা সমস্যা থাকে, অনুগ্রহ করে আমাদের সাথে যোগাযোগ করুন:</p>
                            <ul>
                                <li>ইমেইল: <EMAIL></li>
                                <li>ফোন: 01XXXXXXXXX</li>
                                <li>সময়: সকাল ৯টা থেকে বিকাল ৫টা (শনি-বৃহস্পতিবার)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .avatar-circle {
        width: 80px;
        height: 80px;
        background-color: #e9ecef;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
    }
    
    .list-group-item.active {
        background-color: #006A4E;
        border-color: #006A4E;
    }
    
    .form-check-input:checked {
        background-color: #006A4E;
        border-color: #006A4E;
    }
</style>

<?php
// Include footer
include_once 'includes/footer.php';
?>
