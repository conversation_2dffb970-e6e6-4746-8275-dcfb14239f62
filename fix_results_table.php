<?php
// Database Connection
require_once 'includes/dbh.inc.php';

echo "<h1>Fix Results Table Structure</h1>";

// Check if results table exists
$tableCheck = $conn->query("SHOW TABLES LIKE 'results'");
if ($tableCheck->num_rows == 0) {
    echo "<p style='color:red;'>Results table does not exist!</p>";
    
    // Create the results table with all required columns
    $createTableQuery = "CREATE TABLE results (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        exam_id INT(11) NOT NULL,
        student_id INT(11) NOT NULL,
        subject_id INT(11) NULL,
        marks_obtained FLOAT NOT NULL,
        total_marks FLOAT NOT NULL DEFAULT 100,
        grade VARCHAR(10) NULL,
        remarks TEXT NULL,
        date DATE NULL,
        created_by INT(11) NULL,
        cq_marks DECIMAL(10,2) DEFAULT 0,
        mcq_marks DECIMAL(10,2) DEFAULT 0,
        practical_marks DECIMAL(10,2) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($createTableQuery)) {
        echo "<p style='color:green;'>Results table created successfully with all required columns!</p>";
    } else {
        echo "<p style='color:red;'>Error creating results table: " . $conn->error . "</p>";
        exit;
    }
} else {
    echo "<p>Results table exists. Checking for missing columns...</p>";
    
    // Define all required columns with their definitions
    $requiredColumns = [
        'id' => "INT(11) AUTO_INCREMENT PRIMARY KEY",
        'exam_id' => "INT(11) NOT NULL",
        'student_id' => "INT(11) NOT NULL",
        'subject_id' => "INT(11) NULL",
        'marks_obtained' => "FLOAT NOT NULL",
        'total_marks' => "FLOAT NOT NULL DEFAULT 100",
        'grade' => "VARCHAR(10) NULL",
        'remarks' => "TEXT NULL",
        'date' => "DATE NULL",
        'created_by' => "INT(11) NULL",
        'cq_marks' => "DECIMAL(10,2) DEFAULT 0",
        'mcq_marks' => "DECIMAL(10,2) DEFAULT 0",
        'practical_marks' => "DECIMAL(10,2) DEFAULT 0",
        'created_at' => "TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
        'updated_at' => "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    ];
    
    // Get existing columns
    $existingColumnsResult = $conn->query("SHOW COLUMNS FROM results");
    $existingColumns = [];
    while ($column = $existingColumnsResult->fetch_assoc()) {
        $existingColumns[] = $column['Field'];
    }
    
    // Check for missing columns and add them
    $addedColumns = [];
    $errorColumns = [];
    
    foreach ($requiredColumns as $column => $definition) {
        if (!in_array($column, $existingColumns)) {
            // Column is missing, add it
            $alterQuery = "ALTER TABLE results ADD COLUMN $column $definition";
            
            if ($conn->query($alterQuery)) {
                $addedColumns[] = $column;
            } else {
                $errorColumns[] = "$column: " . $conn->error;
            }
        }
    }
    
    // Display results
    if (count($addedColumns) > 0) {
        echo "<p style='color:green;'>Added the following columns: " . implode(", ", $addedColumns) . "</p>";
    } else {
        echo "<p style='color:green;'>All required columns already exist!</p>";
    }
    
    if (count($errorColumns) > 0) {
        echo "<p style='color:red;'>Errors adding columns: " . implode("; ", $errorColumns) . "</p>";
    }
    
    // Update existing records with default values for new columns
    $currentDate = date('Y-m-d');
    $updateQueries = [
        "UPDATE results SET total_marks = 100 WHERE total_marks IS NULL OR total_marks = 0",
        "UPDATE results SET date = '$currentDate' WHERE date IS NULL"
    ];
    
    foreach ($updateQueries as $query) {
        $conn->query($query);
    }
}

// Show the updated table structure
echo "<h2>Current Table Structure</h2>";
$columnsQuery = "SHOW COLUMNS FROM results";
$columnsResult = $conn->query($columnsQuery);

if ($columnsResult) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($column = $columnsResult->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>Error getting columns: " . $conn->error . "</p>";
}

echo "<p><a href='admin/marks_entry.php'>Go to Marks Entry Page</a></p>";

$conn->close();
?>
