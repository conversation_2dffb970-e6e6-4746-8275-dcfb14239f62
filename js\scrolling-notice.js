/**
 * Scrolling Notice JavaScript
 * Handles animation and visibility of the scrolling notice
 */

document.addEventListener('DOMContentLoaded', function() {
    // Force redraw of the scrolling text element
    var scrollingText = document.getElementById('scrolling-text');
    if (scrollingText) {
        // Temporarily hide and show to force redraw
        scrollingText.style.display = 'none';
        setTimeout(function() {
            scrollingText.style.display = 'inline-block';
        }, 10);
        
        // Restart animation
        scrollingText.style.animationName = 'none';
        setTimeout(function() {
            scrollingText.style.animationName = 'scrollText';
        }, 50);
    }
    
    // Pause animation on hover
    var noticeContainer = document.querySelector('.scrolling-notice-container');
    if (noticeContainer && scrollingText) {
        noticeContainer.addEventListener('mouseenter', function() {
            scrollingText.style.animationPlayState = 'paused';
        });
        
        noticeContainer.addEventListener('mouseleave', function() {
            scrollingText.style.animationPlayState = 'running';
        });
    }
    
    // Restart animation when window is resized
    window.addEventListener('resize', function() {
        if (scrollingText) {
            scrollingText.style.animationName = 'none';
            setTimeout(function() {
                scrollingText.style.animationName = 'scrollText';
            }, 50);
        }
    });
});
