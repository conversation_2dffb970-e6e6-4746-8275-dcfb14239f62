-- Portable Database Backup for zfaw - 2025-05-05 20:21:38
-- This backup can be used on any computer



CREATE TABLE `bkash_payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fee_id` int(11) NOT NULL,
  `payment_id` varchar(100) NOT NULL,
  `trx_id` varchar(100) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status` varchar(50) NOT NULL,
  `payer_reference` varchar(100) DEFAULT NULL,
  `payment_date` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `classes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `class_name` varchar(50) NOT NULL,
  `department_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `department_id` (`department_id`),
  CONSTRAINT `classes_ibfk_1` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `classes` VALUES("1","একাদশ",NULL,"2025-05-04 21:59:04");
INSERT INTO `classes` VALUES("2","দ্বাদশ",NULL,"2025-05-04 21:59:13");




CREATE TABLE `committee_members` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `position` varchar(255) NOT NULL,
  `details` text DEFAULT NULL,
  `photo` varchar(255) DEFAULT NULL,
  `priority` int(11) DEFAULT 100,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `committee_members` VALUES("1","মাননীয় অধ্যক্ষ","সভাপতি","অভিজ্ঞ শিক্ষাবিদ এবং প্রশাসক, ২০১০ সাল থেকে সংস্থার সাথে যুক্ত।",NULL,"1","2025-05-04 22:14:12");
INSERT INTO `committee_members` VALUES("2","মাননীয় সচিব","সদস্য সচিব","অভিজ্ঞ প্রশাসক এবং শিক্ষাবিদ, ২০১২ সাল থেকে সংস্থার সাথে যুক্ত।",NULL,"2","2025-05-04 22:14:12");
INSERT INTO `committee_members` VALUES("3","মোঃ আব্দুল কাদের","সদস্য","বিশিষ্ট ব্যবসায়ী এবং সমাজসেবক, ২০১৪ সাল থেকে সংস্থার সাথে যুক্ত।",NULL,"3","2025-05-04 22:14:12");




CREATE TABLE `department_subject_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `department_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `subject_type` enum('required','optional','fourth') NOT NULL DEFAULT 'optional',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `department_id` (`department_id`,`subject_id`),
  KEY `subject_id` (`subject_id`),
  CONSTRAINT `department_subject_types_ibfk_1` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE CASCADE,
  CONSTRAINT `department_subject_types_ibfk_2` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=281 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `department_subject_types` VALUES("264","5","22","required","2025-05-05 23:03:42");
INSERT INTO `department_subject_types` VALUES("265","5","23","required","2025-05-05 23:03:42");
INSERT INTO `department_subject_types` VALUES("266","5","24","required","2025-05-05 23:03:42");
INSERT INTO `department_subject_types` VALUES("267","5","25","required","2025-05-05 23:03:42");
INSERT INTO `department_subject_types` VALUES("268","5","26","required","2025-05-05 23:03:42");
INSERT INTO `department_subject_types` VALUES("269","5","27","required","2025-05-05 23:03:42");
INSERT INTO `department_subject_types` VALUES("270","5","28","required","2025-05-05 23:03:42");
INSERT INTO `department_subject_types` VALUES("271","5","29","required","2025-05-05 23:03:42");
INSERT INTO `department_subject_types` VALUES("272","5","30","required","2025-05-05 23:03:42");
INSERT INTO `department_subject_types` VALUES("273","5","31","required","2025-05-05 23:03:42");
INSERT INTO `department_subject_types` VALUES("274","5","32","required","2025-05-05 23:03:42");
INSERT INTO `department_subject_types` VALUES("275","5","33","required","2025-05-05 23:03:43");
INSERT INTO `department_subject_types` VALUES("276","5","34","required","2025-05-05 23:03:43");
INSERT INTO `department_subject_types` VALUES("277","5","35","required","2025-05-05 23:03:43");
INSERT INTO `department_subject_types` VALUES("278","5","36","required","2025-05-05 23:03:43");
INSERT INTO `department_subject_types` VALUES("279","5","37","required","2025-05-05 23:03:43");
INSERT INTO `department_subject_types` VALUES("280","5","38","required","2025-05-05 23:03:43");




CREATE TABLE `departments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `department_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `department_code` varchar(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `department_name` (`department_name`),
  UNIQUE KEY `department_code` (`department_code`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `departments` VALUES("1","সাধারন","সাধারন","2025-05-04 18:16:51","সা-০");
INSERT INTO `departments` VALUES("2","বিজ্ঞান","বিজ্ঞান","2025-05-04 21:59:32","বি-১");
INSERT INTO `departments` VALUES("5","মানবিক","মানবিক","2025-05-04 22:05:42","মা-২");
INSERT INTO `departments` VALUES("6","ব্যবসায়","ব্যবসায়","2025-05-04 22:06:00","ব্য-৩");
INSERT INTO `departments` VALUES("11","ব্যবসায়",NULL,"2025-05-06 00:04:38","");




CREATE TABLE `exam_results` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `exam_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `marks_obtained` decimal(5,2) NOT NULL,
  `grade` varchar(10) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `exam_id` (`exam_id`),
  KEY `student_id` (`student_id`),
  KEY `subject_id` (`subject_id`),
  CONSTRAINT `exam_results_ibfk_1` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`id`) ON DELETE CASCADE,
  CONSTRAINT `exam_results_ibfk_2` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE,
  CONSTRAINT `exam_results_ibfk_3` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `exam_subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `exam_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `total_marks` int(11) NOT NULL DEFAULT 100,
  `passing_marks` int(11) DEFAULT 33,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `exam_id` (`exam_id`),
  KEY `subject_id` (`subject_id`),
  CONSTRAINT `exam_subjects_ibfk_1` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`id`) ON DELETE CASCADE,
  CONSTRAINT `exam_subjects_ibfk_2` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `exam_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `exam_types` VALUES("1","সামায়িক","সামায়িক পরীক্ষা","1","2025-05-04 21:17:00");
INSERT INTO `exam_types` VALUES("2","অর্ধ-বার্ষিক","অর্ধ-বার্ষিক পরীক্ষা","1","2025-05-04 21:17:00");
INSERT INTO `exam_types` VALUES("3","বার্ষিক","বার্ষিক পরীক্ষা","1","2025-05-04 21:17:00");
INSERT INTO `exam_types` VALUES("4","মডেল টেস্ট","মডেল টেস্ট পরীক্ষা","1","2025-05-04 21:17:00");
INSERT INTO `exam_types` VALUES("5","নির্বাচনী","নির্বাচনী পরীক্ষা","1","2025-05-04 21:17:00");
INSERT INTO `exam_types` VALUES("6","সাপ্তাহিক","সাপ্তাহিক পরীক্ষা","1","2025-05-04 21:17:00");
INSERT INTO `exam_types` VALUES("7","মাসিক","মাসিক পরীক্ষা","1","2025-05-04 21:17:00");
INSERT INTO `exam_types` VALUES("8","অন্যান্য","অন্যান্য পরীক্ষা","1","2025-05-04 21:17:00");
INSERT INTO `exam_types` VALUES("9","প্রাক-নির্বাচনী","প্রাক-নির্বাচনী পরীক্ষা","1","2025-05-04 21:17:47");




CREATE TABLE `exams` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `exam_name` varchar(100) NOT NULL,
  `exam_date` date NOT NULL,
  `exam_type` varchar(100) DEFAULT NULL,
  `total_marks` int(11) NOT NULL DEFAULT 100,
  `passing_marks` int(11) DEFAULT 33,
  `class_id` int(11) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `subject_id` int(11) DEFAULT NULL,
  `subject_name` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `class_id` (`class_id`),
  KEY `department_id` (`department_id`),
  KEY `subject_id` (`subject_id`),
  CONSTRAINT `exams_ibfk_1` FOREIGN KEY (`class_id`) REFERENCES `classes` (`id`) ON DELETE SET NULL,
  CONSTRAINT `exams_ibfk_2` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL,
  CONSTRAINT `exams_ibfk_3` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `fee_map_class` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fee_type_id` int(11) NOT NULL,
  `class_id` int(11) NOT NULL,
  `academic_year` varchar(20) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `fee_type_id` (`fee_type_id`),
  KEY `class_id` (`class_id`),
  CONSTRAINT `fee_map_class_ibfk_1` FOREIGN KEY (`fee_type_id`) REFERENCES `fee_types` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fee_map_class_ibfk_2` FOREIGN KEY (`class_id`) REFERENCES `classes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `fee_map_session` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fee_type_id` int(11) NOT NULL,
  `session_name` varchar(50) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `fee_type_id` (`fee_type_id`),
  CONSTRAINT `fee_map_session_ibfk_1` FOREIGN KEY (`fee_type_id`) REFERENCES `fee_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `fee_map_student` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fee_type_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `fee_type_id` (`fee_type_id`),
  KEY `student_id` (`student_id`),
  CONSTRAINT `fee_map_student_ibfk_1` FOREIGN KEY (`fee_type_id`) REFERENCES `fee_types` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fee_map_student_ibfk_2` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `fee_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_recurring` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `fees` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `fee_type` varchar(100) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `paid` decimal(10,2) DEFAULT 0.00,
  `due_date` date NOT NULL,
  `payment_status` enum('due','partial','paid') DEFAULT 'due',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `student_id` (`student_id`),
  CONSTRAINT `fees_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_name` varchar(255) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `groups` VALUES("1","বিজ্ঞান","1","2025-05-05 07:10:43");
INSERT INTO `groups` VALUES("2","মানবিক","1","2025-05-05 07:10:43");
INSERT INTO `groups` VALUES("11","ব্যবসায়","1","2025-05-05 19:48:24");
INSERT INTO `groups` VALUES("12","সাধারণ","1","2025-05-05 19:48:24");




CREATE TABLE `marks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `exam_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `cq_marks` decimal(10,2) DEFAULT 0.00,
  `mcq_marks` decimal(10,2) DEFAULT 0.00,
  `practical_marks` decimal(10,2) DEFAULT 0.00,
  `total_marks` decimal(10,2) DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `exam_id` (`exam_id`),
  KEY `subject_id` (`subject_id`),
  KEY `student_id` (`student_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `notices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `date` date NOT NULL,
  `target_audience` enum('all','students','teachers','staff') NOT NULL DEFAULT 'all',
  `expiry_date` date NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `notices` VALUES("1","স্বাগতম আমাদের কলেজ ম্যানেজমেন্ট সিস্টেমে","এই সিস্টেমটি ব্যবহার করে আপনি সহজেই আপনার কলেজের সকল কার্যক্রম পরিচালনা করতে পারবেন।","2025-05-04","all","2025-06-03","2025-05-04 18:31:41","2025-05-04 18:31:41");




CREATE TABLE `passing_marks_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` int(11) DEFAULT NULL,
  `min_percentage` decimal(5,2) NOT NULL,
  `max_percentage` decimal(5,2) NOT NULL,
  `passing_mark` decimal(5,2) NOT NULL,
  `cq_passing_percent` decimal(5,2) NOT NULL DEFAULT 33.00,
  `mcq_passing_percent` decimal(5,2) NOT NULL DEFAULT 33.00,
  `practical_passing_percent` decimal(5,2) NOT NULL DEFAULT 33.00,
  `grade` varchar(5) NOT NULL,
  `grade_point` decimal(3,2) NOT NULL,
  `description` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `subject_id` (`subject_id`,`min_percentage`,`max_percentage`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `passing_marks_config` VALUES("1",NULL,"80.00","100.00","80.00","40.00","40.00","40.00","A+","5.00","শ্রেষ্ঠত্ব (Excellence)","2025-05-04 22:13:55","2025-05-04 22:13:55");
INSERT INTO `passing_marks_config` VALUES("2",NULL,"70.00","79.99","70.00","35.00","35.00","35.00","A","4.00","অতি উত্তম (Very Good)","2025-05-04 22:13:55","2025-05-04 22:13:55");
INSERT INTO `passing_marks_config` VALUES("3",NULL,"60.00","69.99","60.00","33.00","33.00","33.00","A-","3.50","উত্তম (Good)","2025-05-04 22:13:55","2025-05-04 22:13:55");
INSERT INTO `passing_marks_config` VALUES("4",NULL,"50.00","59.99","50.00","33.00","33.00","33.00","B","3.00","ভালো (Satisfactory)","2025-05-04 22:13:55","2025-05-04 22:13:55");
INSERT INTO `passing_marks_config` VALUES("5",NULL,"40.00","49.99","40.00","33.00","33.00","33.00","C","2.00","মোটামুটি (Average)","2025-05-04 22:13:55","2025-05-04 22:13:55");
INSERT INTO `passing_marks_config` VALUES("6",NULL,"33.00","39.99","33.00","33.00","33.00","33.00","D","1.00","নিম্নমান (Poor)","2025-05-04 22:13:55","2025-05-04 22:13:55");
INSERT INTO `passing_marks_config` VALUES("7",NULL,"0.00","32.99","0.00","0.00","0.00","0.00","F","0.00","অকৃতকার্য (Fail)","2025-05-04 22:13:55","2025-05-04 22:13:55");




CREATE TABLE `sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_name` varchar(50) NOT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `is_current` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `session_name` (`session_name`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `sessions` VALUES("1","2025-26","2025-05-01","2026-05-12","0","2025-05-04 22:08:26");
INSERT INTO `sessions` VALUES("2","2026-27","2026-05-04","2027-12-31","0","2025-05-04 22:09:14");




CREATE TABLE `staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` varchar(20) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `gender` enum('male','female','other') NOT NULL,
  `dob` date DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(50) DEFAULT NULL,
  `state` varchar(50) DEFAULT NULL,
  `postal_code` varchar(20) DEFAULT NULL,
  `country` varchar(50) DEFAULT 'Bangladesh',
  `joining_date` date DEFAULT NULL,
  `profile_photo` varchar(255) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `designation` varchar(100) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `role` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `staff_id` (`staff_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `staff` VALUES("1","STF-001","Jamal","Hossain","<EMAIL>","01912345678","male",NULL,NULL,NULL,NULL,NULL,"Bangladesh","2025-05-04",NULL,"1","Office Assistant",NULL,"2025-05-04 18:23:13",NULL);




CREATE TABLE `student_subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `category` varchar(20) NOT NULL DEFAULT 'optional',
  `session_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `student_subjects` VALUES("4","1","22","required","2","2025-05-05 23:27:43");
INSERT INTO `student_subjects` VALUES("5","1","34","required","2","2025-05-05 23:27:43");
INSERT INTO `student_subjects` VALUES("6","1","35","required","2","2025-05-05 23:27:43");
INSERT INTO `student_subjects` VALUES("7","1","29","optional","2","2025-05-05 23:27:43");
INSERT INTO `student_subjects` VALUES("8","1","37","optional","2","2025-05-05 23:27:43");
INSERT INTO `student_subjects` VALUES("9","1","38","fourth","2","2025-05-05 23:27:43");
INSERT INTO `student_subjects` VALUES("10","2","22","required","2","2025-05-05 23:28:47");
INSERT INTO `student_subjects` VALUES("11","2","29","optional","2","2025-05-05 23:28:47");
INSERT INTO `student_subjects` VALUES("12","2","32","optional","2","2025-05-05 23:28:47");
INSERT INTO `student_subjects` VALUES("13","2","38","fourth","2","2025-05-05 23:28:47");
INSERT INTO `student_subjects` VALUES("14","3","22","required","2","2025-05-05 23:37:49");
INSERT INTO `student_subjects` VALUES("15","3","24","required","2","2025-05-05 23:37:49");
INSERT INTO `student_subjects` VALUES("16","3","34","required","2","2025-05-05 23:37:49");
INSERT INTO `student_subjects` VALUES("17","3","35","required","2","2025-05-05 23:37:49");
INSERT INTO `student_subjects` VALUES("18","3","23","required","2","2025-05-05 23:37:49");
INSERT INTO `student_subjects` VALUES("19","3","26","optional","2","2025-05-05 23:37:49");
INSERT INTO `student_subjects` VALUES("20","3","25","fourth","2","2025-05-05 23:37:49");
INSERT INTO `student_subjects` VALUES("28","4","30","required","2","2025-05-06 00:18:38");
INSERT INTO `student_subjects` VALUES("29","4","22","required","2","2025-05-06 00:18:38");
INSERT INTO `student_subjects` VALUES("30","4","34","required","2","2025-05-06 00:18:38");
INSERT INTO `student_subjects` VALUES("31","4","28","required","2","2025-05-06 00:18:38");
INSERT INTO `student_subjects` VALUES("32","4","35","required","2","2025-05-06 00:18:38");
INSERT INTO `student_subjects` VALUES("33","4","27","fourth","2","2025-05-06 00:18:38");




CREATE TABLE `students` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` varchar(20) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `gender` enum('male','female','other') NOT NULL,
  `dob` date DEFAULT NULL,
  `address` text DEFAULT NULL,
  `class_id` int(11) DEFAULT NULL,
  `session_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `admission_date` date DEFAULT NULL,
  `profile_photo` varchar(255) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `batch` varchar(20) DEFAULT NULL,
  `group_name` varchar(50) DEFAULT NULL,
  `role` varchar(50) DEFAULT NULL,
  `roll_number` varchar(20) DEFAULT NULL,
  `guardian_name` varchar(100) DEFAULT NULL,
  `guardian_relation` varchar(50) DEFAULT NULL,
  `guardian_phone` varchar(20) DEFAULT NULL,
  `guardian_email` varchar(100) DEFAULT NULL,
  `guardian_address` text DEFAULT NULL,
  `guardian_occupation` varchar(100) DEFAULT NULL,
  `father_name` varchar(100) DEFAULT NULL,
  `father_phone` varchar(20) DEFAULT NULL,
  `father_email` varchar(100) DEFAULT NULL,
  `father_occupation` varchar(100) DEFAULT NULL,
  `father_income` varchar(50) DEFAULT NULL,
  `mother_name` varchar(100) DEFAULT NULL,
  `mother_phone` varchar(20) DEFAULT NULL,
  `mother_email` varchar(100) DEFAULT NULL,
  `mother_occupation` varchar(100) DEFAULT NULL,
  `mother_income` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `student_id` (`student_id`),
  KEY `class_id` (`class_id`),
  KEY `session_id` (`session_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `students_ibfk_1` FOREIGN KEY (`class_id`) REFERENCES `classes` (`id`) ON DELETE SET NULL,
  CONSTRAINT `students_ibfk_2` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`id`) ON DELETE SET NULL,
  CONSTRAINT `students_ibfk_3` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `students` VALUES("1","STD-601523","Tanvir","Rahman",NULL,"01711000111","male","2000-05-05","Damurhuda, Chuadanga","1","1","5","2025-05-05 09:20:27","2025-05-05","uploads/profile_photos/68182e7bcc166.jpg","5","2526",NULL,"Regular Student","252601",NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO `students` VALUES("2","STD-443687","Noyon","Tara","<EMAIL>","01977861762","female","1999-05-05","Chuadanga","1","1","6","2025-05-05 09:22:06","0000-00-00","uploads/profile_photos/68182ede6e4c5.png","5","2526",NULL,"regular","252602",NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO `students` VALUES("3","STD-310268","ALI","RAHMAN","<EMAIL>","01717861762","male","2005-05-05","Chuadanga","1","1","7","2025-05-05 23:36:31","2025-05-05","uploads/profile_photos/6818f71f51b29.jpg","2","2025",NULL,"Regular Student","252604",NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
INSERT INTO `students` VALUES("4","STD-833027","KUMARI","RANI","<EMAIL>","01711000111","female","2004-05-05","চুয়াডাঙ্গা বাংলাদেশ","1","1","8","2025-05-05 23:40:57","2025-05-05","uploads/profile_photos/6818f82991cfe.jpg","11","2526",NULL,"Regular Student","252602",NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);




CREATE TABLE `subject_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `category_name` (`category_name`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `subject_categories` VALUES("1","required","আবশ্যিক বিষয়সমূহ","2025-05-04 20:58:37");
INSERT INTO `subject_categories` VALUES("2","optional","ঐচ্ছিক বিষয়সমূহ","2025-05-04 20:58:37");
INSERT INTO `subject_categories` VALUES("3","fourth","৪র্থ বিষয়","2025-05-04 20:58:37");




CREATE TABLE `subject_departments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` int(11) NOT NULL,
  `department_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `subject_id` (`subject_id`,`department_id`)
) ENGINE=InnoDB AUTO_INCREMENT=199 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `subject_departments` VALUES("1","1","1");
INSERT INTO `subject_departments` VALUES("62","2","1");
INSERT INTO `subject_departments` VALUES("2","2","2");
INSERT INTO `subject_departments` VALUES("42","2","5");
INSERT INTO `subject_departments` VALUES("22","2","6");
INSERT INTO `subject_departments` VALUES("63","3","1");
INSERT INTO `subject_departments` VALUES("3","3","2");
INSERT INTO `subject_departments` VALUES("43","3","5");
INSERT INTO `subject_departments` VALUES("23","3","6");
INSERT INTO `subject_departments` VALUES("64","4","1");
INSERT INTO `subject_departments` VALUES("4","4","2");
INSERT INTO `subject_departments` VALUES("44","4","5");
INSERT INTO `subject_departments` VALUES("24","4","6");
INSERT INTO `subject_departments` VALUES("65","5","1");
INSERT INTO `subject_departments` VALUES("5","5","2");
INSERT INTO `subject_departments` VALUES("45","5","5");
INSERT INTO `subject_departments` VALUES("25","5","6");
INSERT INTO `subject_departments` VALUES("66","6","1");
INSERT INTO `subject_departments` VALUES("6","6","2");
INSERT INTO `subject_departments` VALUES("46","6","5");
INSERT INTO `subject_departments` VALUES("26","6","6");
INSERT INTO `subject_departments` VALUES("67","7","1");
INSERT INTO `subject_departments` VALUES("7","7","2");
INSERT INTO `subject_departments` VALUES("47","7","5");
INSERT INTO `subject_departments` VALUES("27","7","6");
INSERT INTO `subject_departments` VALUES("68","8","1");
INSERT INTO `subject_departments` VALUES("8","8","2");
INSERT INTO `subject_departments` VALUES("48","8","5");
INSERT INTO `subject_departments` VALUES("28","8","6");
INSERT INTO `subject_departments` VALUES("69","9","1");
INSERT INTO `subject_departments` VALUES("9","9","2");
INSERT INTO `subject_departments` VALUES("49","9","5");
INSERT INTO `subject_departments` VALUES("29","9","6");
INSERT INTO `subject_departments` VALUES("70","10","1");
INSERT INTO `subject_departments` VALUES("10","10","2");
INSERT INTO `subject_departments` VALUES("50","10","5");
INSERT INTO `subject_departments` VALUES("30","10","6");
INSERT INTO `subject_departments` VALUES("71","11","1");
INSERT INTO `subject_departments` VALUES("11","11","2");
INSERT INTO `subject_departments` VALUES("51","11","5");
INSERT INTO `subject_departments` VALUES("31","11","6");
INSERT INTO `subject_departments` VALUES("72","12","1");
INSERT INTO `subject_departments` VALUES("12","12","2");
INSERT INTO `subject_departments` VALUES("52","12","5");
INSERT INTO `subject_departments` VALUES("32","12","6");
INSERT INTO `subject_departments` VALUES("73","13","1");
INSERT INTO `subject_departments` VALUES("13","13","2");
INSERT INTO `subject_departments` VALUES("53","13","5");
INSERT INTO `subject_departments` VALUES("33","13","6");
INSERT INTO `subject_departments` VALUES("74","14","1");
INSERT INTO `subject_departments` VALUES("14","14","2");
INSERT INTO `subject_departments` VALUES("54","14","5");
INSERT INTO `subject_departments` VALUES("34","14","6");
INSERT INTO `subject_departments` VALUES("75","15","1");
INSERT INTO `subject_departments` VALUES("15","15","2");
INSERT INTO `subject_departments` VALUES("55","15","5");
INSERT INTO `subject_departments` VALUES("35","15","6");
INSERT INTO `subject_departments` VALUES("76","16","1");
INSERT INTO `subject_departments` VALUES("16","16","2");
INSERT INTO `subject_departments` VALUES("56","16","5");
INSERT INTO `subject_departments` VALUES("36","16","6");
INSERT INTO `subject_departments` VALUES("77","17","1");
INSERT INTO `subject_departments` VALUES("17","17","2");
INSERT INTO `subject_departments` VALUES("57","17","5");
INSERT INTO `subject_departments` VALUES("37","17","6");
INSERT INTO `subject_departments` VALUES("78","18","1");
INSERT INTO `subject_departments` VALUES("18","18","2");
INSERT INTO `subject_departments` VALUES("58","18","5");
INSERT INTO `subject_departments` VALUES("38","18","6");
INSERT INTO `subject_departments` VALUES("79","19","1");
INSERT INTO `subject_departments` VALUES("19","19","2");
INSERT INTO `subject_departments` VALUES("59","19","5");
INSERT INTO `subject_departments` VALUES("39","19","6");
INSERT INTO `subject_departments` VALUES("80","20","1");
INSERT INTO `subject_departments` VALUES("20","20","2");
INSERT INTO `subject_departments` VALUES("60","20","5");
INSERT INTO `subject_departments` VALUES("40","20","6");
INSERT INTO `subject_departments` VALUES("81","21","1");
INSERT INTO `subject_departments` VALUES("21","21","2");
INSERT INTO `subject_departments` VALUES("61","21","5");
INSERT INTO `subject_departments` VALUES("41","21","6");
INSERT INTO `subject_departments` VALUES("182","22","5");
INSERT INTO `subject_departments` VALUES("183","23","5");
INSERT INTO `subject_departments` VALUES("184","24","5");
INSERT INTO `subject_departments` VALUES("185","25","5");
INSERT INTO `subject_departments` VALUES("186","26","5");
INSERT INTO `subject_departments` VALUES("187","27","5");
INSERT INTO `subject_departments` VALUES("188","28","5");
INSERT INTO `subject_departments` VALUES("189","29","5");
INSERT INTO `subject_departments` VALUES("190","30","5");
INSERT INTO `subject_departments` VALUES("191","31","5");
INSERT INTO `subject_departments` VALUES("192","32","5");
INSERT INTO `subject_departments` VALUES("193","33","5");
INSERT INTO `subject_departments` VALUES("194","34","5");
INSERT INTO `subject_departments` VALUES("195","35","5");
INSERT INTO `subject_departments` VALUES("196","36","5");
INSERT INTO `subject_departments` VALUES("197","37","5");
INSERT INTO `subject_departments` VALUES("198","38","5");




CREATE TABLE `subject_exam_pattern` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` int(11) NOT NULL,
  `has_cq` tinyint(1) DEFAULT 1,
  `has_mcq` tinyint(1) DEFAULT 1,
  `has_practical` tinyint(1) DEFAULT 0,
  `cq_marks` float DEFAULT 70,
  `mcq_marks` float DEFAULT 30,
  `practical_marks` float DEFAULT 0,
  `total_marks` float DEFAULT 100,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `subject_id` (`subject_id`),
  KEY `subject_id_2` (`subject_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `subject_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` int(11) NOT NULL,
  `group_id` int(11) NOT NULL,
  `subject_type` enum('required','optional','fourth') NOT NULL DEFAULT 'optional',
  `is_applicable` tinyint(1) DEFAULT 1,
  `category_id` int(11) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `subject_id` (`subject_id`),
  KEY `group_id` (`group_id`),
  CONSTRAINT `subject_groups_ibfk_1` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE,
  CONSTRAINT `subject_groups_ibfk_2` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=150 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `subject_groups` VALUES("108","30","11","required","1","1","2025-05-06 00:07:25");
INSERT INTO `subject_groups` VALUES("109","22","1","required","1","1","2025-05-06 00:07:25");
INSERT INTO `subject_groups` VALUES("110","22","2","required","1","1","2025-05-06 00:07:25");
INSERT INTO `subject_groups` VALUES("111","22","11","required","1","1","2025-05-06 00:07:25");
INSERT INTO `subject_groups` VALUES("112","26","1","optional","1","1","2025-05-06 00:07:25");
INSERT INTO `subject_groups` VALUES("113","26","1","fourth","1","2","2025-05-06 00:07:25");
INSERT INTO `subject_groups` VALUES("114","26","1","optional","1","3","2025-05-06 00:07:25");
INSERT INTO `subject_groups` VALUES("115","31","11","optional","1","1","2025-05-06 00:07:25");
INSERT INTO `subject_groups` VALUES("116","31","11","fourth","1","2","2025-05-06 00:07:25");
INSERT INTO `subject_groups` VALUES("117","24","1","required","1","1","2025-05-06 00:07:25");
INSERT INTO `subject_groups` VALUES("118","29","2","optional","1","1","2025-05-06 00:07:25");
INSERT INTO `subject_groups` VALUES("119","29","2","fourth","1","2","2025-05-06 00:07:25");
INSERT INTO `subject_groups` VALUES("120","29","2","optional","1","3","2025-05-06 00:07:25");
INSERT INTO `subject_groups` VALUES("121","34","1","required","1","1","2025-05-06 00:07:25");
INSERT INTO `subject_groups` VALUES("122","34","2","required","1","1","2025-05-06 00:07:26");
INSERT INTO `subject_groups` VALUES("123","34","11","required","1","1","2025-05-06 00:07:26");
INSERT INTO `subject_groups` VALUES("124","28","11","required","1","1","2025-05-06 00:07:26");
INSERT INTO `subject_groups` VALUES("125","36","2","optional","1","1","2025-05-06 00:07:26");
INSERT INTO `subject_groups` VALUES("126","36","2","fourth","1","2","2025-05-06 00:07:26");
INSERT INTO `subject_groups` VALUES("127","36","2","optional","1","3","2025-05-06 00:07:26");
INSERT INTO `subject_groups` VALUES("128","35","1","required","1","1","2025-05-06 00:07:26");
INSERT INTO `subject_groups` VALUES("129","35","2","required","1","1","2025-05-06 00:07:26");
INSERT INTO `subject_groups` VALUES("130","35","11","required","1","1","2025-05-06 00:07:26");
INSERT INTO `subject_groups` VALUES("131","37","2","optional","1","1","2025-05-06 00:07:26");
INSERT INTO `subject_groups` VALUES("132","37","2","fourth","1","2","2025-05-06 00:07:26");
INSERT INTO `subject_groups` VALUES("133","37","2","optional","1","3","2025-05-06 00:07:26");
INSERT INTO `subject_groups` VALUES("134","25","1","optional","1","1","2025-05-06 00:07:26");
INSERT INTO `subject_groups` VALUES("135","25","1","fourth","1","2","2025-05-06 00:07:26");
INSERT INTO `subject_groups` VALUES("136","25","1","optional","1","3","2025-05-06 00:07:26");
INSERT INTO `subject_groups` VALUES("137","23","1","required","1","1","2025-05-06 00:07:26");
INSERT INTO `subject_groups` VALUES("138","33","2","optional","1","1","2025-05-06 00:07:26");
INSERT INTO `subject_groups` VALUES("139","33","2","fourth","1","2","2025-05-06 00:07:26");
INSERT INTO `subject_groups` VALUES("140","33","2","optional","1","3","2025-05-06 00:07:26");
INSERT INTO `subject_groups` VALUES("141","32","2","optional","1","1","2025-05-06 00:07:26");
INSERT INTO `subject_groups` VALUES("142","32","2","fourth","1","2","2025-05-06 00:07:27");
INSERT INTO `subject_groups` VALUES("143","32","2","optional","1","3","2025-05-06 00:07:27");
INSERT INTO `subject_groups` VALUES("144","27","1","fourth","1","1","2025-05-06 00:07:27");
INSERT INTO `subject_groups` VALUES("145","27","2","fourth","1","1","2025-05-06 00:07:27");
INSERT INTO `subject_groups` VALUES("146","27","11","optional","1","1","2025-05-06 00:07:27");
INSERT INTO `subject_groups` VALUES("147","27","11","fourth","1","2","2025-05-06 00:07:27");
INSERT INTO `subject_groups` VALUES("148","38","2","fourth","1","1","2025-05-06 00:07:27");
INSERT INTO `subject_groups` VALUES("149","31","11","optional","1","3","2025-05-06 00:07:27");




CREATE TABLE `subject_marks_distribution` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` int(11) NOT NULL,
  `cq_marks` float DEFAULT 70,
  `mcq_marks` float DEFAULT 30,
  `practical_marks` float DEFAULT 0,
  `total_marks` float DEFAULT 100,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `subject_id` (`subject_id`),
  KEY `subject_id_2` (`subject_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `subject_minimum_pass` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` int(11) NOT NULL,
  `cq_min_marks` float DEFAULT 0,
  `mcq_min_marks` float DEFAULT 0,
  `practical_min_marks` float DEFAULT 0,
  `total_min_marks` float DEFAULT 33,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `subject_id` (`subject_id`),
  KEY `subject_id_2` (`subject_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_name` varchar(100) NOT NULL,
  `subject_code` varchar(20) NOT NULL,
  `category` varchar(255) DEFAULT 'required',
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `subjects` VALUES("22","BANGLA","101","required",NULL,"1","2025-05-05 18:50:47");
INSERT INTO `subjects` VALUES("23","PHYSICS","174-175","required",NULL,"1","2025-05-05 19:54:30");
INSERT INTO `subjects` VALUES("24","CHEMISTRY","176-177","required",NULL,"1","2025-05-05 19:54:44");
INSERT INTO `subjects` VALUES("25","MATH","265-266","required",NULL,"1","2025-05-05 19:54:58");
INSERT INTO `subjects` VALUES("26","BIOLOGY","178-179","required",NULL,"1","2025-05-05 19:55:20");
INSERT INTO `subjects` VALUES("27","STATISTICS","129-130","required",NULL,"1","2025-05-05 19:55:39");
INSERT INTO `subjects` VALUES("28","FBI","277-278","required",NULL,"1","2025-05-05 19:56:06");
INSERT INTO `subjects` VALUES("29","CIVICS","269-270","required",NULL,"1","2025-05-05 21:51:22");
INSERT INTO `subjects` VALUES("30","ACCOUNTING","253-254","required",NULL,"1","2025-05-05 22:17:02");
INSERT INTO `subjects` VALUES("31","BOM","286-287","required",NULL,"1","2025-05-05 22:17:21");
INSERT INTO `subjects` VALUES("32","SOCIOLOGY","117-118","required",NULL,"1","2025-05-05 22:17:32");
INSERT INTO `subjects` VALUES("33","SOCIAL WORK","271-272","required",NULL,"1","2025-05-05 22:17:51");
INSERT INTO `subjects` VALUES("34","ENGLISH","107-108","required",NULL,"1","2025-05-05 22:18:13");
INSERT INTO `subjects` VALUES("35","ICT","275","required",NULL,"1","2025-05-05 22:18:30");
INSERT INTO `subjects` VALUES("36","HISTORY","304-305","required",NULL,"1","2025-05-05 22:18:45");
INSERT INTO `subjects` VALUES("37","ISLAMIC HISTORY","267-268","required",NULL,"1","2025-05-05 22:19:17");
INSERT INTO `subjects` VALUES("38","STUDY OF ISLAM","249-250","required",NULL,"1","2025-05-05 22:19:33");




CREATE TABLE `teacher_subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `teacher_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `session_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `teacher_id` (`teacher_id`,`subject_id`,`session_id`),
  KEY `subject_id` (`subject_id`),
  KEY `session_id` (`session_id`),
  CONSTRAINT `teacher_subjects_ibfk_1` FOREIGN KEY (`teacher_id`) REFERENCES `teachers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `teacher_subjects_ibfk_2` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE,
  CONSTRAINT `teacher_subjects_ibfk_3` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `teachers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `teacher_id` varchar(20) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `gender` enum('male','female','other') NOT NULL,
  `dob` date DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(50) DEFAULT NULL,
  `state` varchar(50) DEFAULT NULL,
  `postal_code` varchar(20) DEFAULT NULL,
  `country` varchar(50) DEFAULT 'Bangladesh',
  `joining_date` date DEFAULT NULL,
  `profile_photo` varchar(255) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `subject` varchar(100) DEFAULT NULL,
  `designation` varchar(100) DEFAULT NULL,
  `username` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `teacher_id` (`teacher_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `user_type` enum('admin','teacher','student','staff') NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `role` varchar(20) NOT NULL DEFAULT 'student',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `users` VALUES("1","admin","$2y$10$C6EA2xrSbOEX9fKd9sR/Tucy6f8rmK4/eu2WzdPwgk6JSpfamJRXi","admin","<EMAIL>","2025-05-04 17:57:58","student");
INSERT INTO `users` VALUES("5","tanvir","$2y$10$srJMXXivx3yu/3vNY/lvhuO4NLLTHmFVYO2DdL/FuqyOkFtq/mxw6","admin",NULL,"2025-05-05 09:20:27","student");
INSERT INTO `users` VALUES("6","nayan","$2y$10$ru0fPe6Qg328KSybel3zueHjA0SEUYkgJZ8JoyDCDCZq4o8K7KIUi","admin",NULL,"2025-05-05 09:22:06","student");
INSERT INTO `users` VALUES("7","ali","$2y$10$cz9veGg8Qlh1uJLSkicH9uufzBPILimPW5Hb3iEDuWTKRpy094QZG","admin",NULL,"2025-05-05 23:36:31","student");
INSERT INTO `users` VALUES("8","rani","$2y$10$j0.ZukgnhKKd/sSrHIzewO8jJ8s0y7jBdlg0/kXGIP1KNB.fNbfeC","admin",NULL,"2025-05-05 23:40:57","student");


