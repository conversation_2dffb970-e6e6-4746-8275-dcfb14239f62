<?php
/**
 * Heading Management Functions
 *
 * This file contains functions for managing and retrieving heading settings
 */

/**
 * Get a setting value from the school_settings table
 *
 * @param string $key The setting key to retrieve
 * @param string $default Default value if setting is not found
 * @return string The setting value or default if not found
 */
function get_setting($key, $default = '') {
    global $conn;

    // Check if connection is valid
    if (!$conn || $conn->connect_error) {
        // Try to reconnect
        require_once __DIR__ . '/dbh.inc.php';

        // If still not connected, return default
        if (!$conn || $conn->connect_error) {
            return $default;
        }
    }

    // Sanitize the key
    $key = $conn->real_escape_string($key);

    // Check if the table has the setting_key column
    $columnCheck = $conn->query("SHOW COLUMNS FROM school_settings LIKE 'setting_key'");
    if ($columnCheck && $columnCheck->num_rows > 0) {
        // New structure - query by setting_key
        $query = "SELECT setting_value FROM school_settings WHERE setting_key = '$key'";
        $result = $conn->query($query);

        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();
            return $row['setting_value'];
        }
    } else {
        // Old structure - try to get by field name
        // For backward compatibility with the old structure
        switch ($key) {
            case 'school_name':
                $query = "SELECT school_name FROM school_settings LIMIT 1";
                break;
            case 'school_address':
                $query = "SELECT school_address FROM school_settings LIMIT 1";
                break;
            case 'attendance_title':
                return $default; // Not in old structure
            default:
                return $default;
        }

        $result = $conn->query($query);
        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();
            return $row[$key];
        }
    }

    return $default;
}

/**
 * Get all heading settings
 *
 * @return array Array of heading settings
 */
function get_heading_settings() {
    $settings = [
        'school_name' => get_setting('school_name', 'স্কুল ম্যানেজমেন্ট সিস্টেম'),
        'school_address' => get_setting('school_address', 'বাংলাদেশ'),
        'attendance_title' => get_setting('attendance_title', 'পরীক্ষায় শিক্ষার্থীর হাজিরা পত্র')
    ];

    return $settings;
}

/**
 * Check if institution logo exists
 *
 * @return bool True if logo exists, false otherwise
 */
function has_institution_logo() {
    $logo_path = __DIR__ . '/../uploads/signatures/institution_logo.png';
    return file_exists($logo_path);
}

/**
 * Get institution logo URL
 *
 * @return string URL to the institution logo or empty string if not found
 */
function get_institution_logo_url() {
    if (has_institution_logo()) {
        // Add timestamp to prevent caching
        return '../uploads/signatures/institution_logo.png?v=' . time();
    }

    return '';
}

/**
 * Render the heading HTML
 *
 * @param bool $include_logo Whether to include the logo
 * @return string HTML for the heading
 */
function render_heading($include_logo = true) {
    $settings = get_heading_settings();
    $has_logo = has_institution_logo();

    $html = '<div class="header-container" style="margin-bottom: 10px;">
        <div class="row align-items-center">
            <div class="col-3 text-start">';

    if ($include_logo && $has_logo) {
        $html .= '<div class="header-logo-container text-start">
            <img src="' . get_institution_logo_url() . '" alt="প্রতিষ্ঠানের লোগো" class="header-institution-logo" style="margin: 0; max-width: 80px; max-height: 80px;">
        </div>';
    }

    $html .= '</div>
            <div class="col-6 text-center">
                <div class="school-info" style="margin-bottom: 5px;">
                    <div class="school-name" style="font-size: 22px;">' . htmlspecialchars($settings['school_name']) . '</div>
                    <div class="school-address" style="font-size: 14px;">' . htmlspecialchars($settings['school_address']) . '</div>
                    <div class="attendance-title" style="font-size: 18px; margin-top: 5px;">' . htmlspecialchars($settings['attendance_title']) . '</div>
                </div>
            </div>
            <div class="col-3">
                <!-- Right column empty -->
            </div>
        </div>
    </div>';

    return $html;
}
