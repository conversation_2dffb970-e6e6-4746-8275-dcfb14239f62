<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get classes for filter
$classesQuery = "SELECT * FROM classes ORDER BY class_name";
$classes = $conn->query($classesQuery);

// Get departments for filter
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Get sessions for filter
$sessionsQuery = "SELECT * FROM sessions ORDER BY session_name";
$sessions = $conn->query($sessionsQuery);

// Get fee types for filter
$feeTypesQuery = "SELECT DISTINCT fee_type FROM fees ORDER BY fee_type";
$feeTypes = $conn->query($feeTypesQuery);

// Check if a return URL is provided
$returnUrl = isset($_GET['return']) ? $_GET['return'] : 'print_receipt.php';
$target = isset($_GET['target']) ? $_GET['target'] : '_self';
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>শিক্ষার্থী নির্বাচন ও রিসিপ্ট জেনারেশন - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f7fa;
        }
        .content-wrapper {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            border-radius: 10px 10px 0 0;
            font-weight: bold;
        }
        .student-list {
            max-height: 500px;
            overflow-y: auto;
        }
        .student-item {
            cursor: pointer;
            padding: 10px;
            border-radius: 5px;
            transition: all 0.2s;
        }
        .student-item:hover {
            background-color: #f0f7ff;
        }
        .student-item.selected {
            background-color: #e7f1ff;
            border-left: 4px solid #0d6efd;
        }
        .search-count {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 8px 15px;
        }
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 100;
            border-radius: 10px;
        }
        .receipt-options {
            display: none;
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="content-wrapper">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="text-primary">
                <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী নির্বাচন ও রিসিপ্ট জেনারেশন
            </h2>
            <a href="<?= htmlspecialchars($returnUrl) ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> ফিরে যান
            </a>
        </div>
        
        <div class="row">
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-filter me-2"></i> ফিল্টার অপশন
                    </div>
                    <div class="card-body">
                        <form id="filterForm">
                            <div class="mb-3">
                                <label for="session_id" class="form-label">সেশন</label>
                                <select class="form-select" id="session_id" name="session_id">
                                    <option value="">সব সেশন</option>
                                    <?php while ($session = $sessions->fetch_assoc()): ?>
                                        <option value="<?= $session['id'] ?>"><?= htmlspecialchars($session['session_name']) ?></option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="class_id" class="form-label">ক্লাস</label>
                                <select class="form-select" id="class_id" name="class_id">
                                    <option value="">সব ক্লাস</option>
                                    <?php while ($class = $classes->fetch_assoc()): ?>
                                        <option value="<?= $class['id'] ?>"><?= htmlspecialchars($class['class_name']) ?></option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="department_id" class="form-label">বিভাগ</label>
                                <select class="form-select" id="department_id" name="department_id">
                                    <option value="">সব বিভাগ</option>
                                    <?php while ($department = $departments->fetch_assoc()): ?>
                                        <option value="<?= $department['id'] ?>"><?= htmlspecialchars($department['department_name']) ?></option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="search_term" class="form-label">শিক্ষার্থী খুঁজুন</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="search_term" name="search_term" placeholder="নাম/আইডি/ফোন" autocomplete="off">
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Receipt Options Card (will be shown when a student is selected) -->
                <div class="card receipt-options" id="receiptOptionsCard">
                    <div class="card-header bg-success text-white">
                        <i class="fas fa-file-invoice me-2"></i> রিসিপ্ট অপশন
                    </div>
                    <div class="card-body">
                        <form id="receiptForm" action="generate_receipt.php" method="GET" target="_blank">
                            <input type="hidden" id="selected_student_id" name="student_id" value="">
                            
                            <div class="mb-3">
                                <label class="form-label">পেমেন্ট অপশন</label>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="radio" name="include_all_payments" id="option_all" value="1" checked>
                                    <label class="form-check-label" for="option_all">
                                        সমস্ত পেমেন্ট অন্তর্ভুক্ত করুন
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="include_all_payments" id="option_date_range" value="0">
                                    <label class="form-check-label" for="option_date_range">
                                        তারিখ অনুযায়ী পেমেন্ট অন্তর্ভুক্ত করুন
                                    </label>
                                </div>
                            </div>
                            
                            <div class="date-range mb-3" style="display: none;">
                                <label for="start_date" class="form-label">শুরুর তারিখ</label>
                                <input type="date" class="form-control mb-2" id="start_date" name="start_date">
                                <label for="end_date" class="form-label">শেষ তারিখ</label>
                                <input type="date" class="form-control" id="end_date" name="end_date">
                            </div>
                            
                            <div class="mb-3">
                                <label for="fee_type" class="form-label">নির্দিষ্ট ফি টাইপ</label>
                                <select class="form-select" id="fee_type" name="fee_type">
                                    <option value="">সব ফি টাইপ</option>
                                    <?php if ($feeTypes && $feeTypes->num_rows > 0): ?>
                                        <?php while ($type = $feeTypes->fetch_assoc()): ?>
                                            <option value="<?= htmlspecialchars($type['fee_type']) ?>">
                                                <?= htmlspecialchars($type['fee_type']) ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="payment_method" class="form-label">পেমেন্ট পদ্ধতি</label>
                                <select class="form-select" id="payment_method" name="payment_method">
                                    <option value="">সব পদ্ধতি</option>
                                    <option value="cash">নগদ</option>
                                    <option value="bank">ব্যাংক ট্রান্সফার</option>
                                    <option value="bkash">বিকাশ</option>
                                    <option value="nagad">নগদ (ডিজিটাল)</option>
                                    <option value="rocket">রকেট</option>
                                </select>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-print me-1"></i> রিসিপ্ট জেনারেট করুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <i class="fas fa-info-circle me-2"></i> ব্যবহার নির্দেশনা
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-chevron-right text-primary me-2"></i> সেশন, ক্লাস, বিভাগ সিলেক্ট করুন
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-chevron-right text-primary me-2"></i> নাম/আইডি দিয়ে অনুসন্ধান করুন
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-chevron-right text-primary me-2"></i> শিক্ষার্থী নির্বাচন করুন
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-chevron-right text-primary me-2"></i> রিসিপ্ট অপশন সিলেক্ট করুন
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-chevron-right text-primary me-2"></i> "রিসিপ্ট জেনারেট করুন" বাটন ক্লিক করুন
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-8">
                <div class="card position-relative">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-users me-2"></i> শিক্ষার্থী তালিকা
                        </div>
                        <div class="search-count">
                            <span id="studentCount">0</span> জন শিক্ষার্থী পাওয়া গেছে
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="studentList" class="student-list">
                            <div class="text-center py-5">
                                <i class="fas fa-filter fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">ফিল্টার সিলেক্ট করুন</h5>
                                <p class="text-muted">সেশন, ক্লাস অথবা বিভাগ সিলেক্ট করলে শিক্ষার্থী তালিকা দেখা যাবে</p>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-light">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div id="selectedStudentInfo" class="text-muted">
                                    কোন শিক্ষার্থী নির্বাচন করা হয়নি
                                </div>
                            </div>
                            <div>
                                <a href="<?= htmlspecialchars($returnUrl) ?>" class="btn btn-outline-secondary me-2">
                                    বাতিল করুন
                                </a>
                                <button id="generateReceiptBtn" class="btn btn-success" disabled>
                                    <i class="fas fa-print me-1"></i> রিসিপ্ট জেনারেট করুন
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Loading overlay -->
                    <div id="loadingOverlay" class="loading-overlay" style="display: none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            let selectedStudentId = null;
            let selectedStudentName = '';
            const returnUrl = '<?= $returnUrl ?>';
            const target = '<?= $target ?>';
            
            // Date range display toggle
            $('input[name="include_all_payments"]').change(function() {
                if ($('#option_date_range').is(':checked')) {
                    $('.date-range').show();
                } else {
                    $('.date-range').hide();
                }
            });
            
            // Set default dates
            const today = new Date();
            const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            
            $('#start_date').val(firstDayOfMonth.toISOString().split('T')[0]);
            $('#end_date').val(today.toISOString().split('T')[0]);
            
            // Function to search students
            function searchStudents() {
                $('#loadingOverlay').show();
                
                $.ajax({
                    url: 'select_student_ajax.php',
                    type: 'GET',
                    data: {
                        session_id: $('#session_id').val(),
                        class_id: $('#class_id').val(),
                        department_id: $('#department_id').val(),
                        search_term: $('#search_term').val()
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            $('#studentCount').text(response.count);
                            
                            if (response.count > 0) {
                                let html = '';
                                
                                response.students.forEach(function(student) {
                                    html += `
                                        <div class="student-item" data-id="${student.id}" data-name="${student.name}">
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <div class="d-flex flex-column">
                                                        <strong>${student.name}</strong>
                                                        <div class="text-muted small">
                                                            আইডি: ${student.student_id}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="d-flex flex-column">
                                                        <span class="badge bg-primary mb-1">${student.class_name}</span>
                                                        <span class="badge bg-secondary">${student.department_name}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <hr class="my-1">
                                    `;
                                });
                                
                                $('#studentList').html(html);
                                
                                // Enable student selection
                                $('.student-item').click(function() {
                                    $('.student-item').removeClass('selected');
                                    $(this).addClass('selected');
                                    selectedStudentId = $(this).data('id');
                                    selectedStudentName = $(this).data('name');
                                    
                                    // Update the selected student info
                                    $('#selectedStudentInfo').html(`<strong>নির্বাচিত শিক্ষার্থী:</strong> ${selectedStudentName}`);
                                    
                                    // Show receipt options
                                    $('#receiptOptionsCard').show();
                                    
                                    // Enable generate receipt button
                                    $('#generateReceiptBtn').prop('disabled', false);
                                    
                                    // Update the hidden input for the form
                                    $('#selected_student_id').val(selectedStudentId);
                                });
                            } else {
                                $('#studentList').html(`
                                    <div class="text-center py-5">
                                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">কোন শিক্ষার্থী পাওয়া যায়নি</h5>
                                        <p class="text-muted">অন্য ফিল্টার অপশন ব্যবহার করুন</p>
                                    </div>
                                `);
                                $('#generateReceiptBtn').prop('disabled', true);
                                $('#receiptOptionsCard').hide();
                            }
                        } else {
                            $('#studentList').html(`
                                <div class="text-center py-5">
                                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                                    <h5 class="text-muted">সমস্যা হয়েছে</h5>
                                    <p class="text-muted">${response.error || 'অনুসন্ধান করতে সমস্যা হয়েছে'}</p>
                                </div>
                            `);
                            $('#generateReceiptBtn').prop('disabled', true);
                            $('#receiptOptionsCard').hide();
                        }
                        
                        $('#loadingOverlay').hide();
                    },
                    error: function() {
                        $('#studentList').html(`
                            <div class="text-center py-5">
                                <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                                <h5 class="text-muted">সার্ভার সমস্যা</h5>
                                <p class="text-muted">অনুসন্ধান করতে সমস্যা হয়েছে। দয়া করে আবার চেষ্টা করুন।</p>
                            </div>
                        `);
                        $('#studentCount').text('0');
                        $('#generateReceiptBtn').prop('disabled', true);
                        $('#receiptOptionsCard').hide();
                        $('#loadingOverlay').hide();
                    }
                });
            }
            
            // Function to generate receipt
            function generateReceipt() {
                // Submit the form
                $('#receiptForm').submit();
            }
            
            // Trigger search when filters change
            $('#session_id, #class_id, #department_id').change(function() {
                searchStudents();
            });
            
            // Trigger search when search term changes (with debounce)
            let searchTimeout;
            $('#search_term').on('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    searchStudents();
                }, 500);
            });
            
            // Generate receipt button
            $('#generateReceiptBtn').click(function() {
                generateReceipt();
            });
            
            // Initial search if any filter is selected
            if ($('#session_id').val() || $('#class_id').val() || $('#department_id').val()) {
                searchStudents();
            }
        });
    </script>
</body>
</html> 