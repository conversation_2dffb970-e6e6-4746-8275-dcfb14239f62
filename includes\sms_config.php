<?php
/**
 * SMS Gateway Configuration
 *
 * This file contains the configuration settings for SMS gateway integration.
 * These settings can be modified through the admin panel.
 */

// Check if the configuration file exists, if not create with default values
$config_file = __DIR__ . '/sms_config_data.php';

if (!file_exists($config_file)) {
    // Default configuration
    $sms_config = [
        'api_key' => '',
        'sender_id' => '',
        'provider' => 'bulksmsbd', // Options: bulksmsbd, greenweb, onnorokom, etc.
        'is_enabled' => false,
        'test_mode' => true,
        'last_updated' => date('Y-m-d H:i:s')
    ];

    // Save default configuration
    file_put_contents($config_file, '<?php return ' . var_export($sms_config, true) . ';');
} else {
    // Load existing configuration
    $sms_config = include($config_file);
}

/**
 * Get SMS configuration
 *
 * @return array SMS configuration array
 */
function get_sms_config() {
    global $sms_config;
    return $sms_config;
}

/**
 * Update SMS configuration
 *
 * @param array $new_config New configuration values
 * @return boolean True if update successful, false otherwise
 */
function update_sms_config($new_config) {
    global $sms_config;

    // Merge new config with existing config
    $sms_config = array_merge($sms_config, $new_config);

    // Update last updated timestamp
    $sms_config['last_updated'] = date('Y-m-d H:i:s');

    // Set is_enabled to true if all required fields are filled
    $required_fields = ['api_key', 'sender_id', 'provider'];
    $is_enabled = true;

    foreach ($required_fields as $field) {
        if (empty($sms_config[$field])) {
            $is_enabled = false;
            break;
        }
    }

    $sms_config['is_enabled'] = $is_enabled;

    // Save updated configuration
    $config_file = __DIR__ . '/sms_config_data.php';
    return file_put_contents($config_file, '<?php return ' . var_export($sms_config, true) . ';');
}

/**
 * Check if SMS is configured
 *
 * @return boolean True if configured, false otherwise
 */
function is_sms_configured() {
    global $sms_config;
    return $sms_config['is_enabled'];
}

/**
 * Send SMS using configured gateway
 *
 * @param string $to Recipient phone number
 * @param string $message SMS message content
 * @return array Response with status and message
 */
function send_sms($to, $message) {
    global $sms_config;

    // Check if SMS is enabled
    if (!$sms_config['is_enabled']) {
        return [
            'status' => false,
            'message' => 'SMS service is not configured or enabled.'
        ];
    }

    // Check if in test mode
    if ($sms_config['test_mode']) {
        return [
            'status' => true,
            'message' => 'Test mode: SMS would be sent to ' . $to . ' with message: ' . $message
        ];
    }

    // Format phone number (ensure it starts with 88 for Bangladesh)
    $formatted_number = format_phone_number($to);

    // Check if number is valid
    if ($formatted_number === false) {
        return [
            'status' => false,
            'message' => 'Invalid phone number format. Please use a valid Bangladesh mobile number (e.g., 01XXXXXXXXX).',
            'error_details' => 'Phone number validation failed for: ' . $to
        ];
    }

    // Send SMS based on provider
    switch ($sms_config['provider']) {
        case 'bulksmsbd':
            return send_sms_bulksmsbd($formatted_number, $message);

        case 'greenweb':
            return send_sms_greenweb($formatted_number, $message);

        case 'onnorokom':
            return send_sms_onnorokom($formatted_number, $message);

        default:
            return [
                'status' => false,
                'message' => 'Unknown SMS provider: ' . $sms_config['provider']
            ];
    }
}

/**
 * Format phone number for Bangladesh
 *
 * @param string $phone Phone number
 * @return string Formatted phone number
 */
function format_phone_number($phone) {
    // Remove any non-numeric characters
    $phone = preg_replace('/[^0-9]/', '', $phone);

    // Check if the number is valid
    if (strlen($phone) < 10) {
        return false; // Invalid number
    }

    // If the number starts with '01', add '88' prefix
    if (substr($phone, 0, 2) === '01') {
        $phone = '88' . $phone;
    }
    // If the number starts with '1' and is 10 digits, add '880' prefix
    elseif (substr($phone, 0, 1) === '1' && strlen($phone) === 10) {
        $phone = '880' . $phone;
    }
    // If the number doesn't start with '88', add it
    elseif (substr($phone, 0, 2) !== '88') {
        $phone = '88' . $phone;
    }

    // Final validation - should be 13 digits for Bangladesh numbers with country code
    if (strlen($phone) !== 13 || substr($phone, 0, 3) !== '880' || substr($phone, 3, 1) !== '1') {
        return false; // Invalid number format
    }

    return $phone;
}

/**
 * Send SMS using BulkSMSBD
 *
 * @param string $to Recipient phone number
 * @param string $message SMS message content
 * @return array Response with status and message
 */
function send_sms_bulksmsbd($to, $message) {
    global $sms_config;

    $url = "http://bulksmsbd.net/api/smsapi";

    // Make sure sender_id is not empty
    $sender_id = !empty($sms_config['sender_id']) ? $sms_config['sender_id'] : 'ZFAW';

    $data = [
        "api_key" => $sms_config['api_key'],
        "type" => "text",
        "number" => $to,
        "senderid" => $sender_id,
        "message" => $message
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);

    if(curl_errno($ch)) {
        return [
            'status' => false,
            'message' => 'cURL Error: ' . curl_error($ch)
        ];
    }

    curl_close($ch);

    // Parse response
    $response_data = json_decode($response, true);

    if (isset($response_data['status']) && $response_data['status'] == 'success') {
        return [
            'status' => true,
            'message' => 'SMS sent successfully. ' . ($response_data['message'] ?? '')
        ];
    } else {
        // Check for IP whitelist error
        if (strpos($response, 'not Whitelisted') !== false ||
            (isset($response_data['response_code']) && $response_data['response_code'] == 1032)) {
            return [
                'status' => false,
                'message' => 'IP Whitelist Error: আপনার IP অ্যাড্রেস SMS প্রোভাইডারের হোয়াইটলিস্টে নেই। ' .
                             'আপনার SMS প্রোভাইডারের ড্যাশবোর্ডে লগইন করে IP অ্যাড্রেস (' . $_SERVER['REMOTE_ADDR'] . ') ' .
                             'হোয়াইটলিস্ট করুন অথবা টেস্ট মোড ব্যবহার করুন।',
                'error_details' => $response
            ];
        }
        // Check for sender ID error
        else if (strpos($response, 'sender id is disabled') !== false ||
                (isset($response_data['response_code']) && $response_data['response_code'] == 1002)) {
            return [
                'status' => false,
                'message' => 'Sender ID Error: আপনার ব্যবহৃত সেন্ডার আইডি অবৈধ বা নিষ্ক্রিয়। ' .
                             'আপনার SMS প্রোভাইডারের ড্যাশবোর্ডে লগইন করে একটি বৈধ সেন্ডার আইডি সেট করুন।',
                'error_details' => $response
            ];
        } else {
            return [
                'status' => false,
                'message' => 'Failed to send SMS. ' . ($response_data['message'] ?? $response),
                'error_details' => $response
            ];
        }
    }
}

/**
 * Send SMS using GreenWeb
 *
 * @param string $to Recipient phone number
 * @param string $message SMS message content
 * @return array Response with status and message
 */
function send_sms_greenweb($to, $message) {
    global $sms_config;

    $url = "http://api.greenweb.com.bd/api.php";
    $data = [
        "api_key" => $sms_config['api_key'],
        "type" => "text",
        "to" => $to,
        "message" => $message
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);

    if(curl_errno($ch)) {
        return [
            'status' => false,
            'message' => 'cURL Error: ' . curl_error($ch)
        ];
    }

    curl_close($ch);

    // Check response
    if (strpos($response, 'SMS SUBMITTED') !== false) {
        return [
            'status' => true,
            'message' => 'SMS sent successfully. Response: ' . $response
        ];
    } else {
        return [
            'status' => false,
            'message' => 'Failed to send SMS. Response: ' . $response
        ];
    }
}

/**
 * Send SMS using Onnorokom SMS
 *
 * @param string $to Recipient phone number
 * @param string $message SMS message content
 * @return array Response with status and message
 */
function send_sms_onnorokom($to, $message) {
    global $sms_config;

    $url = "https://api2.onnorokomsms.com/HttpSendSms.ashx";
    $data = [
        "op" => "OneToOne",
        "type" => "TEXT",
        "mobile" => $to,
        "smsText" => $message,
        "username" => $sms_config['api_key'],
        "password" => $sms_config['sender_id'],
        "campaignName" => "ZFAW"
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);

    if(curl_errno($ch)) {
        return [
            'status' => false,
            'message' => 'cURL Error: ' . curl_error($ch)
        ];
    }

    curl_close($ch);

    // Check response
    if (strpos($response, 'Success') !== false) {
        return [
            'status' => true,
            'message' => 'SMS sent successfully. Response: ' . $response
        ];
    } else {
        return [
            'status' => false,
            'message' => 'Failed to send SMS. Response: ' . $response
        ];
    }
}
