<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if staff table exists
$tableCheck = $conn->query("SHOW TABLES LIKE 'staff'");
if ($tableCheck->num_rows == 0) {
    header("Location: ../create_staff_table.php");
    exit();
}

// Get departments for dropdown
$departments = [];
$departmentsQuery = "SHOW TABLES LIKE 'departments'";
$departmentsTableExists = $conn->query($departmentsQuery)->num_rows > 0;

if ($departmentsTableExists) {
    $departmentsResult = $conn->query("SELECT * FROM departments ORDER BY department_name");
    if ($departmentsResult && $departmentsResult->num_rows > 0) {
        while ($row = $departmentsResult->fetch_assoc()) {
            $departments[] = $row;
        }
    }
}

// Process form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $staff_id = $_POST['staff_id'];
    $first_name = $_POST['first_name'];
    $last_name = $_POST['last_name'];
    $email = $_POST['email'] ?? '';
    $phone = $_POST['phone'] ?? '';
    $gender = $_POST['gender'];
    $dob = $_POST['dob'] ?? null;
    $address = $_POST['address'] ?? '';
    $city = $_POST['city'] ?? '';
    $state = $_POST['state'] ?? '';
    $postal_code = $_POST['postal_code'] ?? '';
    $country = $_POST['country'] ?? 'Bangladesh';
    $joining_date = $_POST['joining_date'] ?? null;
    $department_id = $_POST['department_id'] ?? null;
    $designation = $_POST['designation'] ?? '';

    // Validate required fields
    if (empty($staff_id) || empty($first_name) || empty($last_name) || empty($gender)) {
        $error_message = "সকল প্রয়োজনীয় ফিল্ড পূরণ করুন।";
    } else {
        // Check if staff_id already exists
        $checkStaffId = $conn->query("SELECT * FROM staff WHERE staff_id = '$staff_id'");
        if ($checkStaffId->num_rows > 0) {
            $error_message = "এই স্টাফ আইডি ইতিমধ্যে ব্যবহৃত হয়েছে। অন্য একটি আইডি ব্যবহার করুন।";
        } else {
            // Prepare SQL query based on available columns
            $columns = [];
            $values = [];

            // Check each field and add to columns/values if it exists
            $columnsResult = $conn->query("SHOW COLUMNS FROM staff");
            $availableColumns = [];
            while ($column = $columnsResult->fetch_assoc()) {
                $availableColumns[] = $column['Field'];
            }

            // Add required fields
            $columns[] = 'staff_id';
            $values[] = "'$staff_id'";

            $columns[] = 'first_name';
            $values[] = "'$first_name'";

            $columns[] = 'last_name';
            $values[] = "'$last_name'";

            // Add optional fields if they exist in the table
            if (in_array('email', $availableColumns) && !empty($email)) {
                $columns[] = 'email';
                $values[] = "'$email'";
            }

            if (in_array('phone', $availableColumns) && !empty($phone)) {
                $columns[] = 'phone';
                $values[] = "'$phone'";
            }

            if (in_array('gender', $availableColumns)) {
                $columns[] = 'gender';
                $values[] = "'$gender'";
            }

            if (in_array('dob', $availableColumns) && !empty($dob)) {
                $columns[] = 'dob';
                $values[] = "'$dob'";
            }

            if (in_array('address', $availableColumns) && !empty($address)) {
                $columns[] = 'address';
                $values[] = "'$address'";
            }

            if (in_array('city', $availableColumns) && !empty($city)) {
                $columns[] = 'city';
                $values[] = "'$city'";
            }

            if (in_array('state', $availableColumns) && !empty($state)) {
                $columns[] = 'state';
                $values[] = "'$state'";
            }

            if (in_array('postal_code', $availableColumns) && !empty($postal_code)) {
                $columns[] = 'postal_code';
                $values[] = "'$postal_code'";
            }

            if (in_array('country', $availableColumns) && !empty($country)) {
                $columns[] = 'country';
                $values[] = "'$country'";
            }

            if (in_array('joining_date', $availableColumns) && !empty($joining_date)) {
                $columns[] = 'joining_date';
                $values[] = "'$joining_date'";
            }

            if (in_array('department_id', $availableColumns) && !empty($department_id)) {
                $columns[] = 'department_id';
                $values[] = "'$department_id'";
            }

            if (in_array('designation', $availableColumns) && !empty($designation)) {
                $columns[] = 'designation';
                $values[] = "'$designation'";
            }

            // Build and execute the query
            $columnsStr = implode(', ', $columns);
            $valuesStr = implode(', ', $values);

            $insertQuery = "INSERT INTO staff ($columnsStr) VALUES ($valuesStr)";

            if ($conn->query($insertQuery)) {
                $success_message = "স্টাফ সফলভাবে যোগ করা হয়েছে!";

                // Create user account if requested
                if (isset($_POST['create_account']) && $_POST['create_account'] == 1) {
                    $username = $staff_id;
                    $password = password_hash($staff_id, PASSWORD_DEFAULT); // Default password is staff_id

                    $userInsertQuery = "INSERT INTO users (username, password, user_type) VALUES ('$username', '$password', 'staff')";

                    if ($conn->query($userInsertQuery)) {
                        $userId = $conn->insert_id;

                        // Update staff record with user_id
                        $conn->query("UPDATE staff SET user_id = $userId WHERE staff_id = '$staff_id'");

                        $success_message .= " ইউজার অ্যাকাউন্টও তৈরি করা হয়েছে। ইউজারনেম: $username";
                    } else {
                        $error_message = "স্টাফ যোগ করা হয়েছে কিন্তু ইউজার অ্যাকাউন্ট তৈরি করতে সমস্যা হয়েছে: " . $conn->error;
                    }
                }
            } else {
                $error_message = "স্টাফ যোগ করতে সমস্যা হয়েছে: " . $conn->error;
            }
        }
    }
}

// Generate random 6-digit staff ID
$nextStaffId = "STF-" . mt_rand(100000, 999999);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>নতুন স্টাফ যোগ করুন - কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css">

    <style>
        /* Font Settings */
        body, html, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, select, textarea, label, li, table, th, td {
            font-family: 'Hind Siliguri', sans-serif !important;
        }

        /* Sidebar Styles */
        .sidebar {
            background-color: #2c3e50;
            color: white;
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            width: 16.66%;
            overflow-y: auto;
            padding-top: 20px;
            padding-bottom: 60px;
            z-index: 100;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 10px 20px;
            margin-bottom: 5px;
            border-radius: 5px;
            transition: all 0.2s ease;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .sidebar .nav-link.active {
            background-color: #3498db;
            color: white;
        }

        .main-content {
            margin-left: 16.66%;
            padding: 20px;
        }

        @media (max-width: 991.98px) {
            .sidebar {
                width: 25%;
            }

            .main-content {
                margin-left: 25%;
            }
        }

        @media (max-width: 767.98px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }

            .main-content {
                margin-left: 0;
            }
        }

        /* Form Styles */
        .form-label {
            font-weight: 500;
        }

        .required-field::after {
            content: " *";
            color: red;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> স্টাফ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-school me-2"></i> শ্রেণী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">
                            <i class="fas fa-calendar-check me-2"></i> উপস্থিতি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="notices.php">
                            <i class="fas fa-bullhorn me-2"></i> নোটিশ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">
                            <i class="fas fa-cog me-2"></i> সেটিংস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2><i class="fas fa-user-plus me-2"></i> নতুন স্টাফ যোগ করুন</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">ড্যাশবোর্ড</a></li>
                                <li class="breadcrumb-item"><a href="staff.php">স্টাফ</a></li>
                                <li class="breadcrumb-item active" aria-current="page">নতুন স্টাফ যোগ করুন</li>
                            </ol>
                        </nav>
                    </div>
                </div>

                <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-body">
                        <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <h4 class="card-title mb-3">প্রাথমিক তথ্য</h4>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="staff_id" class="form-label required-field">স্টাফ আইডি</label>
                                    <input type="text" class="form-control" id="staff_id" name="staff_id" value="<?php echo $nextStaffId; ?>" required>
                                    <small class="text-muted">সিস্টেম স্বয়ংক্রিয়ভাবে একটি ৬ ডিজিটের রেন্ডম আইডি তৈরি করেছে। প্রয়োজনে পরিবর্তন করুন।</small>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="first_name" class="form-label required-field">নামের প্রথম অংশ</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" required>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="last_name" class="form-label required-field">নামের শেষ অংশ</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" required>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="gender" class="form-label required-field">লিঙ্গ</label>
                                    <select class="form-select" id="gender" name="gender" required>
                                        <option value="">লিঙ্গ নির্বাচন করুন</option>
                                        <option value="male">পুরুষ</option>
                                        <option value="female">মহিলা</option>
                                        <option value="other">অন্যান্য</option>
                                    </select>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="dob" class="form-label">জন্ম তারিখ</label>
                                    <input type="date" class="form-control" id="dob" name="dob">
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="joining_date" class="form-label">যোগদানের তারিখ</label>
                                    <input type="date" class="form-control" id="joining_date" name="joining_date" value="<?php echo date('Y-m-d'); ?>">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <h4 class="card-title mb-3">যোগাযোগের তথ্য</h4>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">ইমেইল</label>
                                    <input type="email" class="form-control" id="email" name="email">
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">ফোন নম্বর</label>
                                    <input type="text" class="form-control" id="phone" name="phone">
                                </div>

                                <div class="col-md-12 mb-3">
                                    <label for="address" class="form-label">ঠিকানা</label>
                                    <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                                </div>

                                <div class="col-md-3 mb-3">
                                    <label for="city" class="form-label">শহর</label>
                                    <input type="text" class="form-control" id="city" name="city">
                                </div>

                                <div class="col-md-3 mb-3">
                                    <label for="state" class="form-label">জেলা</label>
                                    <input type="text" class="form-control" id="state" name="state">
                                </div>

                                <div class="col-md-3 mb-3">
                                    <label for="postal_code" class="form-label">পোস্টাল কোড</label>
                                    <input type="text" class="form-control" id="postal_code" name="postal_code">
                                </div>

                                <div class="col-md-3 mb-3">
                                    <label for="country" class="form-label">দেশ</label>
                                    <input type="text" class="form-control" id="country" name="country" value="Bangladesh">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <h4 class="card-title mb-3">পেশাগত তথ্য</h4>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="department_id" class="form-label">বিভাগ</label>
                                    <select class="form-select" id="department_id" name="department_id">
                                        <option value="">বিভাগ নির্বাচন করুন</option>
                                        <?php foreach ($departments as $department): ?>
                                        <option value="<?php echo $department['id']; ?>"><?php echo $department['department_name']; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="designation" class="form-label">পদবি</label>
                                    <input type="text" class="form-control" id="designation" name="designation">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <h4 class="card-title mb-3">অ্যাকাউন্ট তথ্য</h4>
                                </div>

                                <div class="col-md-12 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="1" id="create_account" name="create_account">
                                        <label class="form-check-label" for="create_account">
                                            স্টাফের জন্য লগইন অ্যাকাউন্ট তৈরি করুন
                                        </label>
                                        <div class="form-text">
                                            অ্যাকাউন্ট তৈরি করলে, ইউজারনেম হবে স্টাফ আইডি এবং পাসওয়ার্ড হবে স্টাফ আইডি।
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i> স্টাফ যোগ করুন
                                    </button>
                                    <a href="staff.php" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i> বাতিল করুন
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
