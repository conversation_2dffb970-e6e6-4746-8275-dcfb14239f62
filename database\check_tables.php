<?php
require_once '../includes/dbh.inc.php';

echo "<h1>Database Tables Check</h1>";

// List of all expected tables
$expectedTables = [
    'users',
    'departments',
    'sessions',
    'classes',
    'students',
    'teachers',
    'staff',
    'subjects',
    'subject_departments',
    'student_subjects',
    'notices',
    'exams',
    'results',
    'fees',
    'fee_payments',
    'bkash_payments',
    'certificates',
    'gb_members',
    'school_settings',
    'subject_passing_config',
    'subject_exam_pattern'
];

// Check each table
$missingTables = [];
$existingTables = [];

foreach ($expectedTables as $table) {
    $tableCheck = $conn->query("SHOW TABLES LIKE '$table'");
    if ($tableCheck->num_rows == 0) {
        $missingTables[] = $table;
    } else {
        $existingTables[] = $table;
    }
}

// Display results
echo "<h2>Database Status</h2>";

if (empty($missingTables)) {
    echo "<div style='color: green; font-weight: bold;'>All expected tables exist in the database!</div>";
} else {
    echo "<div style='color: red; font-weight: bold;'>Missing tables:</div>";
    echo "<ul>";
    foreach ($missingTables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul>";
    
    echo "<p><a href='setup_all.php' style='background-color: #4CAF50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>Run Complete Database Setup</a></p>";
}

echo "<h2>Existing Tables</h2>";
echo "<ul>";
foreach ($existingTables as $table) {
    echo "<li>$table</li>";
}
echo "</ul>";

// Check for admin user
$adminCheck = $conn->query("SELECT * FROM users WHERE username = 'admin' AND user_type = 'admin'");
if ($adminCheck->num_rows > 0) {
    echo "<div style='color: green; font-weight: bold;'>Admin user exists!</div>";
} else {
    echo "<div style='color: red; font-weight: bold;'>Admin user does not exist!</div>";
    echo "<p><a href='setup_all.php' style='background-color: #4CAF50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>Run Complete Database Setup</a></p>";
}

echo "<p><a href='../admin/dashboard.php' style='background-color: #2196F3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>Go to Admin Dashboard</a></p>";
?>
