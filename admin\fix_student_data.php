<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Function to check and fix student data issues
function checkStudentDataIntegrity($conn) {
    $results = [];
    
    try {
        // 1. Check students table
        $studentsCount = $conn->query("SELECT COUNT(*) as count FROM students")->fetch_assoc()['count'];
        $results['students_count'] = $studentsCount;
        
        // 2. Check fees with invalid student references
        $invalidFeesQuery = "SELECT f.id, f.student_id, f.fee_type, f.amount 
                            FROM fees f 
                            LEFT JOIN students s ON f.student_id = s.id 
                            WHERE s.id IS NULL";
        $invalidFeesResult = $conn->query($invalidFeesQuery);
        $invalidFees = [];
        while ($row = $invalidFeesResult->fetch_assoc()) {
            $invalidFees[] = $row;
        }
        $results['invalid_fees'] = $invalidFees;
        
        // 3. Check fee_payments with invalid student references
        $invalidPaymentsQuery = "SELECT fp.id, fp.fee_id, fp.amount, f.student_id 
                                FROM fee_payments fp 
                                LEFT JOIN fees f ON fp.fee_id = f.id 
                                LEFT JOIN students s ON f.student_id = s.id 
                                WHERE s.id IS NULL";
        $invalidPaymentsResult = $conn->query($invalidPaymentsQuery);
        $invalidPayments = [];
        while ($row = $invalidPaymentsResult->fetch_assoc()) {
            $invalidPayments[] = $row;
        }
        $results['invalid_payments'] = $invalidPayments;
        
        // 4. Get sample valid students (handle missing name column)
        try {
            // First check if name column exists
            $columnsResult = $conn->query("SHOW COLUMNS FROM students LIKE 'name'");
            $hasNameColumn = $columnsResult->num_rows > 0;

            if ($hasNameColumn) {
                $validStudentsQuery = "SELECT id, student_id, name, class, section FROM students ORDER BY id LIMIT 10";
            } else {
                // Use first_name and last_name if name doesn't exist
                $columnsResult = $conn->query("SHOW COLUMNS FROM students LIKE 'first_name'");
                $hasFirstName = $columnsResult->num_rows > 0;

                if ($hasFirstName) {
                    $validStudentsQuery = "SELECT id, student_id, CONCAT(COALESCE(first_name, ''), ' ', COALESCE(last_name, '')) as name, class, section FROM students ORDER BY id LIMIT 10";
                } else {
                    $validStudentsQuery = "SELECT id, student_id, 'Unknown' as name, class, section FROM students ORDER BY id LIMIT 10";
                }
            }

            $validStudentsResult = $conn->query($validStudentsQuery);
            $validStudents = [];
            while ($row = $validStudentsResult->fetch_assoc()) {
                $validStudents[] = $row;
            }
            $results['valid_students'] = $validStudents;
        } catch (Exception $e) {
            $results['valid_students'] = [];
            $results['students_error'] = $e->getMessage();
        }
        
        return $results;
        
    } catch (Exception $e) {
        return [
            'error' => $e->getMessage()
        ];
    }
}

// Function to create sample students if none exist
function createSampleStudents($conn) {
    try {
        $conn->begin_transaction();
        
        $sampleStudents = [
            ['student_id' => 'STD001', 'name' => 'আহমেদ আলী', 'class' => '১০', 'section' => 'ক'],
            ['student_id' => 'STD002', 'name' => 'ফাতিমা খাতুন', 'class' => '৯', 'section' => 'খ'],
            ['student_id' => 'STD003', 'name' => 'মোহাম্মদ রহিম', 'class' => '৮', 'section' => 'গ'],
            ['student_id' => 'STD004', 'name' => 'আয়েশা বেগম', 'class' => '৭', 'section' => 'ক'],
            ['student_id' => 'STD005', 'name' => 'আবদুল করিম', 'class' => '৬', 'section' => 'খ']
        ];
        
        $createdCount = 0;
        foreach ($sampleStudents as $student) {
            // Check if student already exists
            $checkQuery = "SELECT id FROM students WHERE student_id = ?";
            $checkStmt = $conn->prepare($checkQuery);
            $checkStmt->bind_param('s', $student['student_id']);
            $checkStmt->execute();
            
            if ($checkStmt->get_result()->num_rows == 0) {
                // Insert new student
                $insertQuery = "INSERT INTO students (student_id, name, class, section, created_at) VALUES (?, ?, ?, ?, NOW())";
                $insertStmt = $conn->prepare($insertQuery);
                $insertStmt->bind_param('ssss', $student['student_id'], $student['name'], $student['class'], $student['section']);
                
                if ($insertStmt->execute()) {
                    $createdCount++;
                }
            }
        }
        
        $conn->commit();
        
        return [
            'success' => true,
            'created_count' => $createdCount
        ];
        
    } catch (Exception $e) {
        $conn->rollback();
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// Function to create sample fees for students
function createSampleFees($conn) {
    try {
        $conn->begin_transaction();
        
        // Get all students
        $studentsQuery = "SELECT id FROM students";
        $studentsResult = $conn->query($studentsQuery);
        
        $createdCount = 0;
        while ($student = $studentsResult->fetch_assoc()) {
            // Check if student already has fees
            $checkFeesQuery = "SELECT id FROM fees WHERE student_id = ?";
            $checkStmt = $conn->prepare($checkFeesQuery);
            $checkStmt->bind_param('i', $student['id']);
            $checkStmt->execute();
            
            if ($checkStmt->get_result()->num_rows == 0) {
                // Create sample fees
                $sampleFees = [
                    ['fee_type' => 'মাসিক বেতন', 'amount' => 1500.00],
                    ['fee_type' => 'পরীক্ষার ফি', 'amount' => 500.00],
                    ['fee_type' => 'বই ফি', 'amount' => 800.00]
                ];
                
                foreach ($sampleFees as $fee) {
                    $insertFeeQuery = "INSERT INTO fees (student_id, fee_type, amount, due_date, payment_status, created_at) 
                                      VALUES (?, ?, ?, DATE_ADD(NOW(), INTERVAL 30 DAY), 'due', NOW())";
                    $insertStmt = $conn->prepare($insertFeeQuery);
                    $insertStmt->bind_param('isd', $student['id'], $fee['fee_type'], $fee['amount']);
                    
                    if ($insertStmt->execute()) {
                        $createdCount++;
                    }
                }
            }
        }
        
        $conn->commit();
        
        return [
            'success' => true,
            'created_count' => $createdCount
        ];
        
    } catch (Exception $e) {
        $conn->rollback();
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// Handle form submissions
$checkResults = null;
$createStudentsResults = null;
$createFeesResults = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['check_data'])) {
        $checkResults = checkStudentDataIntegrity($conn);
    } elseif (isset($_POST['create_students'])) {
        $createStudentsResults = createSampleStudents($conn);
    } elseif (isset($_POST['create_fees'])) {
        $createFeesResults = createSampleFees($conn);
    }
}

// Get current status
$currentStatus = checkStudentDataIntegrity($conn);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Data ঠিক করুন</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: "Hind Siliguri", sans-serif;
        }
        
        .status-card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .data-good {
            background-color: #d1e7dd;
            border-left: 4px solid #198754;
        }
        
        .data-bad {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        
        .data-warning {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4 bg-white p-3 rounded shadow-sm">
                    <div>
                        <h2 class="mb-0">
                            <i class="fas fa-user-graduate text-primary me-2"></i>
                            Student Data ঠিক করুন
                        </h2>
                        <small class="text-muted">Student data integrity check এবং সমস্যা সমাধান</small>
                    </div>
                    <div>
                        <a href="debug_student_id.php" class="btn btn-secondary me-2">
                            <i class="fas fa-bug me-1"></i> Debug
                        </a>
                        <a href="fee_management.php" class="btn btn-primary">
                            <i class="fas fa-money-bill-wave me-1"></i> ফি ম্যানেজমেন্ট
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Status -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">বর্তমান অবস্থা</h5>
                    </div>
                    <div class="card-body">
                        <?php if (isset($currentStatus['error'])): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                <?php echo htmlspecialchars($currentStatus['error']); ?>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="card status-card <?php echo $currentStatus['students_count'] > 0 ? 'data-good' : 'data-bad'; ?>">
                                        <div class="card-body text-center">
                                            <i class="fas fa-users fa-2x mb-2"></i>
                                            <h6>Students</h6>
                                            <h4><?php echo $currentStatus['students_count']; ?></h4>
                                            <small><?php echo $currentStatus['students_count'] > 0 ? 'টি student আছে' : 'কোন student নেই'; ?></small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card status-card <?php echo empty($currentStatus['invalid_fees']) ? 'data-good' : 'data-bad'; ?>">
                                        <div class="card-body text-center">
                                            <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                                            <h6>Invalid Fees</h6>
                                            <h4><?php echo count($currentStatus['invalid_fees']); ?></h4>
                                            <small><?php echo empty($currentStatus['invalid_fees']) ? 'কোন সমস্যা নেই' : 'টি invalid fee'; ?></small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card status-card <?php echo empty($currentStatus['invalid_payments']) ? 'data-good' : 'data-bad'; ?>">
                                        <div class="card-body text-center">
                                            <i class="fas fa-credit-card fa-2x mb-2"></i>
                                            <h6>Invalid Payments</h6>
                                            <h4><?php echo count($currentStatus['invalid_payments']); ?></h4>
                                            <small><?php echo empty($currentStatus['invalid_payments']) ? 'কোন সমস্যা নেই' : 'টি invalid payment'; ?></small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card status-card <?php echo count($currentStatus['valid_students']) > 0 ? 'data-good' : 'data-warning'; ?>">
                                        <div class="card-body text-center">
                                            <i class="fas fa-check-circle fa-2x mb-2"></i>
                                            <h6>Valid Students</h6>
                                            <h4><?php echo count($currentStatus['valid_students']); ?></h4>
                                            <small>টি valid student</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <form method="POST" style="display: inline;">
                            <button type="submit" name="check_data" class="btn btn-info btn-lg me-3">
                                <i class="fas fa-search me-2"></i>
                                Data চেক করুন
                            </button>
                        </form>
                        <form method="POST" style="display: inline;">
                            <button type="submit" name="create_students" class="btn btn-success btn-lg me-3"
                                    onclick="return confirm('আপনি কি নিশ্চিত যে sample students তৈরি করতে চান?')">
                                <i class="fas fa-user-plus me-2"></i>
                                Sample Students তৈরি করুন
                            </button>
                        </form>
                        <form method="POST" style="display: inline;">
                            <button type="submit" name="create_fees" class="btn btn-warning btn-lg"
                                    onclick="return confirm('আপনি কি নিশ্চিত যে sample fees তৈরি করতে চান?')">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                Sample Fees তৈরি করুন
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Display -->
        <?php if ($createStudentsResults): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <?php if ($createStudentsResults['success']): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>সফল!</strong> <?php echo $createStudentsResults['created_count']; ?> টি sample student তৈরি করা হয়েছে।
                        </div>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong>ত্রুটি!</strong> <?php echo htmlspecialchars($createStudentsResults['error']); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($createFeesResults): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <?php if ($createFeesResults['success']): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>সফল!</strong> <?php echo $createFeesResults['created_count']; ?> টি sample fee তৈরি করা হয়েছে।
                        </div>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong>ত্রুটি!</strong> <?php echo htmlspecialchars($createFeesResults['error']); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Valid Students List -->
        <?php if (!empty($currentStatus['valid_students'])): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Valid Students (Test Links)</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Student ID</th>
                                            <th>Name</th>
                                            <th>Class</th>
                                            <th>Section</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($currentStatus['valid_students'] as $student): ?>
                                            <tr>
                                                <td><?php echo $student['id']; ?></td>
                                                <td><?php echo htmlspecialchars($student['student_id']); ?></td>
                                                <td><?php echo htmlspecialchars($student['name']); ?></td>
                                                <td><?php echo htmlspecialchars($student['class']); ?></td>
                                                <td><?php echo htmlspecialchars($student['section']); ?></td>
                                                <td>
                                                    <a href="student_payment_receipt.php?student_id=<?php echo $student['id']; ?>"
                                                       class="btn btn-sm btn-primary" target="_blank">
                                                        <i class="fas fa-receipt me-1"></i> Receipt Test
                                                    </a>
                                                    <a href="debug_student_id.php?student_id=<?php echo $student['id']; ?>"
                                                       class="btn btn-sm btn-info" target="_blank">
                                                        <i class="fas fa-bug me-1"></i> Debug
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Instructions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">নির্দেশনা</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>সমস্যা সমাধানের ধাপ:</h6>
                            <ol>
                                <li><strong>Data চেক করুন</strong> - বর্তমান student data অবস্থা দেখুন</li>
                                <li><strong>Sample Students তৈরি করুন</strong> - যদি কোন student না থাকে</li>
                                <li><strong>Sample Fees তৈরি করুন</strong> - students এর জন্য fees তৈরি করুন</li>
                                <li><strong>Test Links</strong> ব্যবহার করে receipt generation test করুন</li>
                            </ol>
                        </div>

                        <div class="alert alert-success">
                            <h6><i class="fas fa-lightbulb me-2"></i>Quick Fix:</h6>
                            <p class="mb-0">যদি কোন student না থাকে, তাহলে "Sample Students তৈরি করুন" button click করুন। এটি ৫টি sample student তৈরি করবে যা দিয়ে আপনি system test করতে পারবেন।</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
