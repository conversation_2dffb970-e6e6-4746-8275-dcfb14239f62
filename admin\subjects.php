<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Initialize messages
$successMessage = '';
$errorMessage = '';

// Check for success message from redirect
if (isset($_GET['success'])) {
    $successMessage = "বিষয় সফলভাবে যোগ করা হয়েছে!";
}

// Check for error message from redirect
if (isset($_GET['error'])) {
    $errorMessage = urldecode($_GET['error']);
}

// Create subjects table if it doesn't exist
$tableQuery = "CREATE TABLE IF NOT EXISTS subjects (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    subject_name VARCHAR(255) NOT NULL,
    subject_code VARCHAR(50) NOT NULL UNIQUE,
    category VARCHAR(255) DEFAULT 'optional',
    description TEXT,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";
$conn->query($tableQuery);

// Check if department_id column exists in subjects table and remove it
$checkColumnQuery = "SHOW COLUMNS FROM subjects LIKE 'department_id'";
$columnExists = $conn->query($checkColumnQuery)->num_rows > 0;

if ($columnExists) {
    // Remove department_id column from subjects table
    $alterQuery = "ALTER TABLE subjects DROP COLUMN department_id";
    $conn->query($alterQuery);
}

// Create subject_departments table if it doesn't exist
$tableQuery = "CREATE TABLE IF NOT EXISTS subject_departments (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    subject_id INT(11) NOT NULL,
    department_id INT(11) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY(subject_id, department_id)
)";
$conn->query($tableQuery);

// Create department_subject_types table if it doesn't exist
$tableQuery = "CREATE TABLE IF NOT EXISTS department_subject_types (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    subject_id INT(11) NOT NULL,
    department_id INT(11) NOT NULL,
    subject_type ENUM('required', 'optional', 'fourth') DEFAULT 'optional',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY(subject_id, department_id)
)";
$conn->query($tableQuery);

// Create class_subjects table if it doesn't exist
$tableQuery = "CREATE TABLE IF NOT EXISTS class_subjects (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    class_id INT(11) NOT NULL,
    department_id INT(11) DEFAULT NULL,
    subject_id INT(11) NOT NULL,
    subject_type ENUM('required', 'optional', 'fourth') DEFAULT 'optional',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_class_subject (class_id, department_id, subject_id)
)";
$conn->query($tableQuery);

// Add new subject
if (isset($_POST['add_subject'])) {
    $subject_name = trim($_POST['subject_name'] ?? '');
    $subject_code = trim($_POST['subject_code'] ?? '');
    $category = trim($_POST['category'] ?? 'optional');
    $description = trim($_POST['description'] ?? '');
    $is_active = isset($_POST['is_active']) ? 1 : 0;

    // Validate input
    if (empty($subject_name) || empty($subject_code)) {
        header("Location: subjects.php?error=" . urlencode("বিষয়ের নাম এবং কোড অবশ্যই পূরণ করতে হবে!"));
        exit();
    }

    // Check if subject code already exists
    $checkQuery = "SELECT COUNT(*) as count FROM subjects WHERE subject_code = '$subject_code'";
    $result = $conn->query($checkQuery);
    $row = $result->fetch_assoc();

    if ($row['count'] > 0) {
        header("Location: subjects.php?error=" . urlencode("এই বিষয় কোড ইতিমধ্যে বিদ্যমান!"));
        exit();
    }

    // Insert new subject
    $insertQuery = "INSERT INTO subjects (subject_name, subject_code, category, description, is_active, created_at)
                   VALUES ('$subject_name', '$subject_code', '$category', '$description', $is_active, NOW())";

    if ($conn->query($insertQuery)) {
        header("Location: subjects.php?success=1");
        exit();
    } else {
        header("Location: subjects.php?error=" . urlencode("বিষয় যোগ করতে সমস্যা হয়েছে: " . $conn->error));
        exit();
    }
}

// Delete subject
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $subject_id = $_GET['delete'];

    // Delete the subject
    $deleteQuery = "DELETE FROM subjects WHERE id = $subject_id";

    if ($conn->query($deleteQuery)) {
        header("Location: subjects.php?success=2");
        exit();
    } else {
        header("Location: subjects.php?error=" . urlencode("বিষয় মুছতে সমস্যা হয়েছে: " . $conn->error));
        exit();
    }
}

// Toggle subject status
if (isset($_GET['toggle']) && is_numeric($_GET['toggle'])) {
    $subject_id = $_GET['toggle'];

    // Get current status
    $statusQuery = "SELECT is_active FROM subjects WHERE id = $subject_id";
    $result = $conn->query($statusQuery);

    if ($result->num_rows > 0) {
        $subject = $result->fetch_assoc();
        $new_status = $subject['is_active'] ? 0 : 1;

        // Update status
        $updateQuery = "UPDATE subjects SET is_active = $new_status WHERE id = $subject_id";

        if ($conn->query($updateQuery)) {
            header("Location: subjects.php?success=3");
            exit();
        } else {
            header("Location: subjects.php?error=" . urlencode("অবস্থা পরিবর্তন করতে সমস্যা হয়েছে: " . $conn->error));
            exit();
        }
    }
}

// Check if department_subject_types table exists
$subjectTypesTableExists = $conn->query("SHOW TABLES LIKE 'department_subject_types'")->num_rows > 0;

// Define variables for compatibility with existing code
$department_filter = '';
$search = '';
$status_filter = '';

// Get all subjects
$query = "SELECT * FROM subjects ORDER BY subject_name";
$subjects = $conn->query($query);

// Get departments for dropdown
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>বিষয় ব্যবস্থাপনা - কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Modern Styling for Subjects Page -->
    <link rel="stylesheet" href="css/admin-style.css">
    <link rel="stylesheet" href="css/modern-subjects.css">

    <!-- Inline CSS for Action Buttons -->
    <style>
        .action-btn-group .btn {
            width: 35px !important;
            height: 35px !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            margin: 0 2px !important;
            border-radius: 6px !important;
            transition: all 0.3s ease !important;
        }

        .action-btn-group .btn:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
        }

        .action-btn-group .btn i {
            font-size: 14px !important;
        }

        /* Ensure buttons are clickable */
        .action-btn-group .btn {
            pointer-events: auto !important;
            cursor: pointer !important;
        }

        /* Debug styles */
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4 mt-2">
                    <h3 class="text-white">অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="subjects.php">
                            <i class="fas fa-book-open"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sessions.php">
                            <i class="fas fa-calendar-alt"></i> সেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt"></i> পরীক্ষা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave"></i> ফি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-file-pdf"></i> রিপোর্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2 class="mb-2">বিষয় ব্যবস্থাপনা</h2>
                        <p class="text-muted">সকল বিষয় দেখুন, যোগ করুন, সম্পাদনা করুন এবং ব্যবস্থাপনা করুন</p>
                    </div>
                    <div class="col-auto d-flex align-items-center">
                        <button class="btn btn-info me-2" data-bs-toggle="modal" data-bs-target="#classSubjectSelectionModal">
                            <i class="fas fa-graduation-cap me-2"></i>ক্লাস অনুযায়ী বিষয় নির্বাচন
                        </button>
                        <a href="subject_groups.php" class="btn btn-warning me-2">
                            <i class="fas fa-layer-group me-2"></i>গ্রুপ অনুযায়ী বিষয়
                        </a>
                        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addSubjectModal">
                            <i class="fas fa-plus-circle me-2"></i>নতুন বিষয়
                        </button>
                    </div>
                </div>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i><?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php
                // Check if required tables exist
                $subjectsTableExists = $conn->query("SHOW TABLES LIKE 'subjects'")->num_rows > 0;
                $teacherSubjectsTableExists = $conn->query("SHOW TABLES LIKE 'teacher_subjects'")->num_rows > 0;
                $studentSubjectsTableExists = $conn->query("SHOW TABLES LIKE 'student_subjects'")->num_rows > 0;
                $subjectDepartmentsTableExists = $conn->query("SHOW TABLES LIKE 'subject_departments'")->num_rows > 0;
                $departmentSubjectTypesTableExists = $conn->query("SHOW TABLES LIKE 'department_subject_types'")->num_rows > 0;

                // Create subject_departments table if it doesn't exist
                if (!$subjectDepartmentsTableExists) {
                    $createTableQuery = "CREATE TABLE IF NOT EXISTS subject_departments (
                        id INT(11) AUTO_INCREMENT PRIMARY KEY,
                        subject_id INT(11) NOT NULL,
                        department_id INT(11) NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE KEY(subject_id, department_id),
                        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
                        FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE
                    )";
                    $conn->query($createTableQuery);
                }

                // Create department_subject_types table if it doesn't exist
                if (!$departmentSubjectTypesTableExists) {
                    $createTableQuery = "CREATE TABLE IF NOT EXISTS department_subject_types (
                        id INT(11) AUTO_INCREMENT PRIMARY KEY,
                        subject_id INT(11) NOT NULL,
                        department_id INT(11) NOT NULL,
                        subject_type ENUM('required', 'optional', 'fourth') DEFAULT 'optional',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE KEY(subject_id, department_id),
                        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
                        FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE
                    )";
                    $conn->query($createTableQuery);
                }

                if (!$subjectsTableExists || !$teacherSubjectsTableExists || !$studentSubjectsTableExists):
                ?>
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <h5 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i>টেবিল সেটআপ প্রয়োজন!</h5>
                        <p>বিষয় ব্যবস্থাপনার জন্য প্রয়োজনীয় কিছু টেবিল তৈরি করা হয়নি। সম্পূর্ণ ফাংশনালিটি পেতে টেবিল সেটআপ করুন।</p>
                        <hr>
                        <a href="create_subject_tables.php" class="btn btn-warning">
                            <i class="fas fa-database me-2"></i>টেবিল সেটআপ করুন
                        </a>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Class-wise Subject Selection Card -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-graduation-cap me-2"></i>ক্লাস অনুযায়ী বিষয় নির্বাচন
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-3">পরীক্ষার ফলাফল তৈরির জন্য প্রতিটি ক্লাসের বিষয় নির্ধারণ করুন।</p>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#classSubjectSelectionModal">
                                        <i class="fas fa-cog me-2"></i>বিষয় কনফিগার করুন
                                    </button>
                                    <button class="btn btn-outline-info" onclick="viewClassSubjects()">
                                        <i class="fas fa-eye me-2"></i>বর্তমান কনফিগারেশন দেখুন
                                    </button>
                                    <a href="test_class_subjects.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-test-tube me-2"></i>টেস্ট করুন
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="bg-light p-3 rounded">
                                    <h6 class="text-muted mb-2">
                                        <i class="fas fa-info-circle me-1"></i>গুরুত্বপূর্ণ তথ্য:
                                    </h6>
                                    <ul class="mb-0 small">
                                        <li>প্রতিটি ক্লাসের জন্য আবশ্যিক ও ঐচ্ছিক বিষয় নির্ধারণ করুন</li>
                                        <li>পরীক্ষার ফলাফল শুধুমাত্র নির্ধারিত বিষয়ের উপর ভিত্তি করে তৈরি হবে</li>
                                        <li>বিষয় পরিবর্তন করলে পূর্বের ফলাফল প্রভাবিত হতে পারে</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Subjects Table -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-list-ul me-2"></i>বিষয় তালিকা</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>বিষয় কোড</th>
                                        <th>বিষয়ের নাম</th>
                                        <th>ধরন</th>
                                        <th>অবস্থা</th>
                                        <th class="text-center">অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($subjects && $subjects->num_rows > 0): ?>
                                        <?php while ($subject = $subjects->fetch_assoc()): ?>
                                            <tr>
                                                <td><strong><?php echo htmlspecialchars($subject['subject_code']); ?></strong></td>
                                                <td><?php echo htmlspecialchars($subject['subject_name']); ?></td>
                                                <td>
                                                    <?php
                                                    $category = $subject['category'] ?? 'optional';
                                                    $categoryText = '';
                                                    $categoryClass = '';
                                                    switch($category) {
                                                        case 'required':
                                                            $categoryText = 'আবশ্যিক';
                                                            $categoryClass = 'bg-success';
                                                            break;
                                                        case 'fourth':
                                                            $categoryText = 'চতুর্থ বিষয়';
                                                            $categoryClass = 'bg-info';
                                                            break;
                                                        default:
                                                            $categoryText = 'ঐচ্ছিক';
                                                            $categoryClass = 'bg-warning';
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $categoryClass; ?>"><?php echo $categoryText; ?></span>
                                                </td>
                                                <td>
                                                    <?php if ($subject['is_active']): ?>
                                                        <span class="badge bg-success">সক্রিয়</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">নিষ্ক্রিয়</span>
                                                    <?php endif; ?>
                                                </td>

                                                <td class="text-center">
                                                    <div class="action-btn-group d-flex justify-content-center">
                                                        <!-- Debug Info -->
                                                        <div class="debug-info d-none">
                                                            Subject ID: <?php echo $subject['id']; ?><br>
                                                            Name: <?php echo htmlspecialchars($subject['subject_name']); ?><br>
                                                            Status: <?php echo $subject['is_active'] ? 'Active' : 'Inactive'; ?>
                                                        </div>

                                                        <!-- Edit Button -->
                                                        <a href="edit_subject.php?id=<?php echo $subject['id']; ?>"
                                                           class="btn btn-info btn-sm"
                                                           title="সম্পাদনা করুন"
                                                           onclick="console.log('Edit clicked for ID: <?php echo $subject['id']; ?>'); alert('Edit button কাজ করছে! ID: <?php echo $subject['id']; ?>'); return true;">
                                                            <i class="fas fa-edit"></i>
                                                        </a>

                                                        <!-- Toggle Status Button -->
                                                        <a href="subjects.php?toggle=<?php echo $subject['id']; ?>"
                                                           class="btn btn-<?php echo $subject['is_active'] ? 'warning' : 'success'; ?> btn-sm"
                                                           title="<?php echo $subject['is_active'] ? 'নিষ্ক্রিয় করুন' : 'সক্রিয় করুন'; ?>"
                                                           onclick="console.log('Toggle clicked for ID: <?php echo $subject['id']; ?>'); return confirm('আপনি কি নিশ্চিত যে <?php echo htmlspecialchars($subject['subject_name']); ?> বিষয়টি <?php echo $subject['is_active'] ? 'নিষ্ক্রিয়' : 'সক্রিয়'; ?> করতে চান?');">
                                                            <i class="fas <?php echo $subject['is_active'] ? 'fa-times' : 'fa-check'; ?>"></i>
                                                        </a>

                                                        <!-- Assignment Button -->
                                                        <a href="subject_assignment.php?subject_id=<?php echo $subject['id']; ?>"
                                                           class="btn btn-primary btn-sm"
                                                           title="শিক্ষক বরাদ্দ করুন"
                                                           onclick="console.log('Assignment clicked for ID: <?php echo $subject['id']; ?>'); alert('Assignment button কাজ করছে! ID: <?php echo $subject['id']; ?>'); return true;">
                                                            <i class="fas fa-user-check"></i>
                                                        </a>

                                                        <!-- Delete Button -->
                                                        <a href="subjects.php?delete=<?php echo $subject['id']; ?>"
                                                           class="btn btn-danger btn-sm"
                                                           title="মুছে ফেলুন"
                                                           onclick="console.log('Delete clicked for ID: <?php echo $subject['id']; ?>'); return confirm('আপনি কি নিশ্চিত যে <?php echo htmlspecialchars($subject['subject_name']); ?> বিষয়টি মুছে ফেলতে চান?\n\nএই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।');">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </a>

                                                        <!-- Test Button -->
                                                        <button type="button"
                                                                class="btn btn-secondary btn-sm"
                                                                title="টেস্ট বাটন"
                                                                onclick="alert('টেস্ট বাটন কাজ করছে! Subject ID: <?php echo $subject['id']; ?>');">
                                                            <i class="fas fa-bug"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="5" class="text-center py-4">
                                                <i class="fas fa-search fa-2x mb-3 text-muted"></i>
                                                <p class="mb-0">কোন বিষয় পাওয়া যায়নি</p>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="row justify-content-center">
                    <div class="col-md-4 mb-4">
                        <div class="card card-hover h-100">
                            <div class="card-body text-center p-4">
                                <div class="icon-circle bg-warning-light mb-4 mx-auto">
                                    <i class="fas fa-layer-group fa-2x text-warning"></i>
                                </div>
                                <h5 class="mb-3">গ্রুপ অনুযায়ী বিষয়ের ধরন</h5>
                                <p class="mb-4">বিভিন্ন গ্রুপের জন্য বিষয়ের ধরন (আবশ্যিক/ঐচ্ছিক/৪র্থ) নির্ধারণ করুন</p>
                                <a href="subject_groups.php" class="btn btn-outline-warning">
                                    <i class="fas fa-link me-2"></i>গ্রুপ সংযোগ করুন
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card card-hover h-100">
                            <div class="card-body text-center p-4">
                                <div class="icon-circle bg-primary-light mb-4 mx-auto">
                                    <i class="fas fa-users fa-2x text-primary"></i>
                                </div>
                                <h5 class="mb-3">গ্রুপ ব্যবস্থাপনা</h5>
                                <p class="mb-4">গ্রুপ যোগ করুন, সম্পাদনা করুন এবং ব্যবস্থাপনা করুন</p>
                                <a href="groups.php" class="btn btn-outline-primary">
                                    <i class="fas fa-cog me-2"></i>গ্রুপ ব্যবস্থাপনা
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card card-hover h-100">
                            <div class="card-body text-center p-4">
                                <div class="icon-circle bg-success-light mb-4 mx-auto">
                                    <i class="fas fa-chalkboard-teacher fa-2x text-success"></i>
                                </div>
                                <h5 class="mb-3">শিক্ষক বরাদ্দকরণ</h5>
                                <p class="mb-4">বিষয় অনুযায়ী শিক্ষক বরাদ্দ করুন</p>
                                <a href="subject_assignment.php" class="btn btn-outline-success">
                                    <i class="fas fa-user-plus me-2"></i>শিক্ষক বরাদ্দ করুন
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <style>
                    .icon-circle {
                        width: 70px;
                        height: 70px;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                    .bg-primary-light {
                        background-color: rgba(67, 97, 238, 0.1);
                    }
                    .bg-warning-light {
                        background-color: rgba(245, 158, 11, 0.1);
                    }
                    .bg-success-light {
                        background-color: rgba(16, 185, 129, 0.1);
                    }

                    /* Class Subject Selection Styles */
                    .subject-card {
                        transition: all 0.3s ease;
                        border: 2px solid #e9ecef;
                        cursor: pointer;
                    }

                    .subject-card:hover {
                        border-color: #007bff;
                        box-shadow: 0 4px 8px rgba(0,123,255,0.1);
                    }

                    .subject-card.selected {
                        border-color: #28a745;
                        background-color: #f8fff9;
                        box-shadow: 0 4px 12px rgba(40,167,69,0.2);
                    }

                    .subject-type-select:disabled {
                        background-color: #f8f9fa;
                        opacity: 0.6;
                    }

                    .modal-xl {
                        max-width: 1200px;
                    }

                    .form-check-input:checked {
                        background-color: #28a745;
                        border-color: #28a745;
                    }
                </style>
            </div>
        </div>
    </div>

    <!-- Add Subject Modal -->
    <div class="modal fade" id="addSubjectModal" tabindex="-1" aria-labelledby="addSubjectModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="addSubjectModalLabel"><i class="fas fa-plus-circle me-2"></i>নতুন বিষয় যোগ করুন</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="subjects.php">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="subject_name" class="form-label">বিষয়ের নাম <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="subject_name" name="subject_name" required>
                        </div>
                        <div class="mb-3">
                            <label for="subject_code" class="form-label">বিষয় কোড <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="subject_code" name="subject_code" required>
                        </div>
                        <div class="mb-3">
                            <label for="category" class="form-label">বিষয়ের ধরন</label>
                            <select class="form-select" id="category" name="category">
                                <option value="required">আবশ্যিক</option>
                                <option value="optional" selected>ঐচ্ছিক</option>
                                <option value="fourth">চতুর্থ বিষয়</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">বিবরণ</label>
                            <textarea class="form-control" id="description" name="description" rows="2" placeholder="বিষয় সম্পর্কে অতিরিক্ত তথ্য..."></textarea>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                <label class="form-check-label" for="is_active">সক্রিয়</label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>বাতিল করুন
                        </button>
                        <button type="submit" name="add_subject" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>সংরক্ষণ করুন
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Class Subject Selection Modal -->
    <div class="modal fade" id="classSubjectSelectionModal" tabindex="-1" aria-labelledby="classSubjectSelectionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title" id="classSubjectSelectionModalLabel">
                        <i class="fas fa-graduation-cap me-2"></i>ক্লাস অনুযায়ী বিষয় নির্বাচন
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="classSubjectForm" method="POST" action="save_class_subjects.php">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="class_select" class="form-label">ক্লাস নির্বাচন করুন <span class="text-danger">*</span></label>
                                <select class="form-select" id="class_select" name="class_id" required onchange="loadClassSubjects()">
                                    <option value="">-- ক্লাস নির্বাচন করুন --</option>
                                    <?php
                                    // Get all classes (prevent duplicates)
                                    $classesQuery = "SELECT DISTINCT id, class_name FROM classes ORDER BY class_name";
                                    $classesResult = $conn->query($classesQuery);
                                    if ($classesResult && $classesResult->num_rows > 0) {
                                        $seenClasses = [];
                                        while ($class = $classesResult->fetch_assoc()) {
                                            // Prevent duplicate class names
                                            if (!in_array($class['class_name'], $seenClasses)) {
                                                echo "<option value='" . $class['id'] . "'>" . htmlspecialchars($class['class_name']) . "</option>";
                                                $seenClasses[] = $class['class_name'];
                                            }
                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="department_select" class="form-label">বিভাগ নির্বাচন করুন</label>
                                <select class="form-select" id="department_select" name="department_id" onchange="loadClassSubjects()">
                                    <option value="">-- সকল বিভাগ --</option>
                                    <?php
                                    // Get all departments
                                    $departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
                                    $departmentsResult = $conn->query($departmentsQuery);
                                    if ($departmentsResult && $departmentsResult->num_rows > 0) {
                                        while ($department = $departmentsResult->fetch_assoc()) {
                                            echo "<option value='" . $department['id'] . "'>" . htmlspecialchars($department['department_name']) . "</option>";
                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>

                        <div id="subjectsContainer" style="display: none;">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                নিচের বিষয়গুলি থেকে নির্বাচন করুন এবং প্রতিটি বিষয়ের ধরন নির্ধারণ করুন:
                            </div>

                            <div class="row" id="subjectsList">
                                <!-- Subjects will be loaded here via AJAX -->
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>বাতিল করুন
                    </button>
                    <button type="button" class="btn btn-info" onclick="saveClassSubjects()">
                        <i class="fas fa-save me-2"></i>সংরক্ষণ করুন
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- View Class Subjects Modal -->
    <div class="modal fade" id="viewClassSubjectsModal" tabindex="-1" aria-labelledby="viewClassSubjectsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="viewClassSubjectsModalLabel">
                        <i class="fas fa-eye me-2"></i>ক্লাস অনুযায়ী বিষয় কনফিগারেশন
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="classSubjectsView">
                    <!-- Class subjects configuration will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>বন্ধ করুন
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, checking action buttons...');

            // Check if action buttons exist
            const actionButtons = document.querySelectorAll('.action-btn');
            console.log('Found action buttons:', actionButtons.length);

            // Check if CSS is loaded
            const testElement = document.createElement('div');
            testElement.className = 'action-btn';
            document.body.appendChild(testElement);
            const styles = window.getComputedStyle(testElement);
            console.log('Action button width:', styles.width);
            console.log('Action button height:', styles.height);
            document.body.removeChild(testElement);

            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert-dismissible');
                alerts.forEach(function(alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);
        });

        // Test function for debugging
        function testActionButton(type, id) {
            console.log('Action button clicked:', type, 'ID:', id);
            alert('অ্যাকশন বাটন কাজ করছে! ধরন: ' + type + ', আইডি: ' + id);
            return false; // Prevent default action for testing
        }

        // Load subjects for selected class and department
        function loadClassSubjects() {
            const classId = document.getElementById('class_select').value;
            const departmentId = document.getElementById('department_select').value;

            if (!classId) {
                document.getElementById('subjectsContainer').style.display = 'none';
                return;
            }

            // Show loading
            document.getElementById('subjectsList').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> লোড হচ্ছে...</div>';
            document.getElementById('subjectsContainer').style.display = 'block';

            // Fetch subjects via AJAX
            fetch('get_class_subjects.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `class_id=${classId}&department_id=${departmentId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displaySubjects(data.subjects, data.assigned_subjects);
                } else {
                    document.getElementById('subjectsList').innerHTML = '<div class="alert alert-danger">বিষয় লোড করতে সমস্যা হয়েছে।</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('subjectsList').innerHTML = '<div class="alert alert-danger">নেটওয়ার্ক সমস্যা হয়েছে।</div>';
            });
        }

        // Display subjects in cards
        function displaySubjects(subjects, assignedSubjects) {
            let html = '';

            subjects.forEach(subject => {
                const isAssigned = assignedSubjects.find(as => as.subject_id == subject.id);
                const subjectType = isAssigned ? isAssigned.subject_type : 'optional';
                const isChecked = isAssigned ? 'checked' : '';

                html += `
                    <div class="col-md-4 mb-3">
                        <div class="card subject-card ${isChecked ? 'selected' : ''}">
                            <div class="card-body">
                                <div class="form-check mb-2">
                                    <input class="form-check-input subject-checkbox" type="checkbox"
                                           value="${subject.id}" id="subject_${subject.id}" ${isChecked}
                                           onchange="toggleSubjectCard(this)">
                                    <label class="form-check-label fw-bold" for="subject_${subject.id}">
                                        ${subject.subject_code}
                                    </label>
                                </div>
                                <h6 class="card-title">${subject.subject_name}</h6>
                                <select class="form-select form-select-sm subject-type-select"
                                        name="subject_types[${subject.id}]" ${!isChecked ? 'disabled' : ''}>
                                    <option value="required" ${subjectType === 'required' ? 'selected' : ''}>আবশ্যিক</option>
                                    <option value="optional" ${subjectType === 'optional' ? 'selected' : ''}>ঐচ্ছিক</option>
                                    <option value="fourth" ${subjectType === 'fourth' ? 'selected' : ''}>চতুর্থ বিষয়</option>
                                </select>
                            </div>
                        </div>
                    </div>
                `;
            });

            document.getElementById('subjectsList').innerHTML = html;
        }

        // Toggle subject card selection
        function toggleSubjectCard(checkbox) {
            const card = checkbox.closest('.subject-card');
            const typeSelect = card.querySelector('.subject-type-select');

            if (checkbox.checked) {
                card.classList.add('selected');
                typeSelect.disabled = false;
            } else {
                card.classList.remove('selected');
                typeSelect.disabled = true;
            }
        }

        // Save class subjects configuration
        function saveClassSubjects() {
            const classId = document.getElementById('class_select').value;
            const departmentId = document.getElementById('department_select').value;

            if (!classId) {
                alert('অনুগ্রহ করে একটি ক্লাস নির্বাচন করুন।');
                return;
            }

            const selectedSubjects = [];
            const checkboxes = document.querySelectorAll('.subject-checkbox:checked');

            checkboxes.forEach(checkbox => {
                const subjectId = checkbox.value;
                const typeSelect = checkbox.closest('.card').querySelector('.subject-type-select');
                const subjectType = typeSelect.value;

                selectedSubjects.push({
                    subject_id: subjectId,
                    subject_type: subjectType
                });
            });

            if (selectedSubjects.length === 0) {
                alert('অনুগ্রহ করে অন্তত একটি বিষয় নির্বাচন করুন।');
                return;
            }

            // Save via AJAX
            fetch('save_class_subjects.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    class_id: classId,
                    department_id: departmentId,
                    subjects: selectedSubjects
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('বিষয় কনফিগারেশন সফলভাবে সংরক্ষিত হয়েছে!');
                    document.getElementById('classSubjectSelectionModal').querySelector('.btn-close').click();
                } else {
                    alert('সংরক্ষণ করতে সমস্যা হয়েছে: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('নেটওয়ার্ক সমস্যা হয়েছে।');
            });
        }

        // View class subjects configuration
        function viewClassSubjects() {
            fetch('view_class_subjects.php')
            .then(response => response.text())
            .then(html => {
                document.getElementById('classSubjectsView').innerHTML = html;
                new bootstrap.Modal(document.getElementById('viewClassSubjectsModal')).show();
            })
            .catch(error => {
                console.error('Error:', error);
                alert('কনফিগারেশন লোড করতে সমস্যা হয়েছে।');
            });
        }
    </script>
</body>
</html>
