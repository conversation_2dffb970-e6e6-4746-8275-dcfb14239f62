# নেটওয়ার্ক অ্যাক্সেস নির্দেশাবলী

আপনার প্রজেক্ট এখন অন্য কম্পিউটার থেকে অ্যাক্সেস করা যাবে। নিচের নির্দেশাবলী অনুসরণ করুন।

## সার্ভার স্টার্ট/স্টপ করা

- সার্ভার স্টার্ট করতে: `start_server.bat` ফাইলে ডাবল-ক্লিক করুন
- সার্ভার স্টপ করতে: `stop_server.bat` ফাইলে ডাবল-ক্লিক করুন

## অন্য কম্পিউটার থেকে অ্যাক্সেস করা

1. নিশ্চিত করুন যে উভয় কম্পিউটার একই নেটওয়ার্কে সংযুক্ত আছে (একই WiFi বা LAN)
2. আপনার কম্পিউটারের IP অ্যাড্রেস জানতে `start_server.bat` ফাইল চালান এবং আউটপুট দেখুন
3. অন্য কম্পিউটারের ব্রাউজারে এই URL টাইপ করুন: `http://**************/zfaw`

## সমস্যা সমাধান

যদি অন্য কম্পিউটার থেকে অ্যাক্সেস করতে না পারেন:

1. উভয় কম্পিউটার একই নেটওয়ার্কে আছে কিনা নিশ্চিত করুন
2. Windows ফায়ারওয়াল Apache-কে ব্লক করছে কিনা চেক করুন
3. XAMPP কন্ট্রোল প্যানেল থেকে Apache এবং MySQL সার্ভিস রিস্টার্ট করুন

## সিকিউরিটি টিপস

1. অ্যাডমিন অ্যাকাউন্টের জন্য শক্তিশালী পাসওয়ার্ড ব্যবহার করুন
2. নিয়মিত ব্যাকআপ নিন
3. অপ্রয়োজনীয় সময়ে সার্ভার বন্ধ রাখুন
