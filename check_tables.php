<?php
require_once 'd:/xampp/htdocs/zfaw/includes/dbh.inc.php';

// Check if student_subjects table exists
$result = $conn->query("SHOW TABLES LIKE 'student_subjects'");
if ($result->num_rows > 0) {
    echo "student_subjects table exists\n";
    
    // Show table structure
    $structure = $conn->query("DESCRIBE student_subjects");
    echo "Table structure:\n";
    while ($row = $structure->fetch_assoc()) {
        echo $row['Field'] . " - " . $row['Type'] . " - " . $row['Null'] . " - " . $row['Key'] . "\n";
    }
} else {
    echo "student_subjects table does not exist\n";
}

// Check if subject_groups table exists
$result = $conn->query("SHOW TABLES LIKE 'subject_groups'");
if ($result->num_rows > 0) {
    echo "\nsubject_groups table exists\n";
    
    // Show table structure
    $structure = $conn->query("DESCRIBE subject_groups");
    echo "Table structure:\n";
    while ($row = $structure->fetch_assoc()) {
        echo $row['Field'] . " - " . $row['Type'] . " - " . $row['Null'] . " - " . $row['Key'] . "\n";
    }
} else {
    echo "\nsubject_groups table does not exist\n";
}
