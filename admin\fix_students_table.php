<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Function to check students table structure
function checkStudentsTableStructure($conn) {
    $results = [];
    
    try {
        // Check if students table exists
        $tablesResult = $conn->query("SHOW TABLES LIKE 'students'");
        $results['table_exists'] = $tablesResult->num_rows > 0;
        
        if ($results['table_exists']) {
            // Get column information
            $columnsResult = $conn->query("SHOW COLUMNS FROM students");
            $columns = [];
            while ($column = $columnsResult->fetch_assoc()) {
                $columns[$column['Field']] = $column;
            }
            $results['columns'] = $columns;
            
            // Check for specific columns
            $results['has_name'] = isset($columns['name']);
            $results['has_first_name'] = isset($columns['first_name']);
            $results['has_last_name'] = isset($columns['last_name']);
            $results['has_student_id'] = isset($columns['student_id']);
            
            // Get row count
            $countResult = $conn->query("SELECT COUNT(*) as count FROM students");
            $results['row_count'] = $countResult->fetch_assoc()['count'];
        }
        
        return $results;
        
    } catch (Exception $e) {
        return [
            'error' => $e->getMessage()
        ];
    }
}

// Function to fix students table
function fixStudentsTable($conn) {
    try {
        $conn->begin_transaction();
        $actions = [];
        
        // Check if students table exists
        $tablesResult = $conn->query("SHOW TABLES LIKE 'students'");
        
        if ($tablesResult->num_rows == 0) {
            // Create students table
            $createTableQuery = "CREATE TABLE students (
                id INT AUTO_INCREMENT PRIMARY KEY,
                student_id VARCHAR(50) UNIQUE NOT NULL,
                name VARCHAR(255) NOT NULL,
                first_name VARCHAR(100),
                last_name VARCHAR(100),
                class VARCHAR(50),
                section VARCHAR(50),
                roll_number VARCHAR(50),
                phone VARCHAR(20),
                email VARCHAR(100),
                address TEXT,
                date_of_birth DATE,
                gender ENUM('male', 'female', 'other'),
                guardian_name VARCHAR(255),
                guardian_phone VARCHAR(20),
                admission_date DATE,
                status ENUM('active', 'inactive', 'graduated') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )";
            
            if ($conn->query($createTableQuery)) {
                $actions[] = "Students table তৈরি করা হয়েছে";
            }
        } else {
            // Check and add missing columns
            $columnsResult = $conn->query("SHOW COLUMNS FROM students");
            $existingColumns = [];
            while ($column = $columnsResult->fetch_assoc()) {
                $existingColumns[] = $column['Field'];
            }
            
            // Add name column if missing
            if (!in_array('name', $existingColumns)) {
                if (in_array('first_name', $existingColumns) && in_array('last_name', $existingColumns)) {
                    // Create name from first_name and last_name
                    $addNameQuery = "ALTER TABLE students ADD COLUMN name VARCHAR(255) AFTER student_id";
                    if ($conn->query($addNameQuery)) {
                        $actions[] = "name column যোগ করা হয়েছে";
                        
                        // Update name column with combined first_name and last_name
                        $updateNameQuery = "UPDATE students SET name = CONCAT(COALESCE(first_name, ''), ' ', COALESCE(last_name, '')) WHERE name IS NULL OR name = ''";
                        if ($conn->query($updateNameQuery)) {
                            $actions[] = "name column এ first_name + last_name data যোগ করা হয়েছে";
                        }
                    }
                } else {
                    // Just add empty name column
                    $addNameQuery = "ALTER TABLE students ADD COLUMN name VARCHAR(255) NOT NULL DEFAULT 'Unknown' AFTER student_id";
                    if ($conn->query($addNameQuery)) {
                        $actions[] = "name column যোগ করা হয়েছে (default value সহ)";
                    }
                }
            }
            
            // Add other missing columns
            $requiredColumns = [
                'student_id' => "VARCHAR(50) UNIQUE",
                'class' => "VARCHAR(50)",
                'section' => "VARCHAR(50)",
                'created_at' => "TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
                'updated_at' => "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
            ];
            
            foreach ($requiredColumns as $columnName => $columnDef) {
                if (!in_array($columnName, $existingColumns)) {
                    $addColumnQuery = "ALTER TABLE students ADD COLUMN $columnName $columnDef";
                    if ($conn->query($addColumnQuery)) {
                        $actions[] = "$columnName column যোগ করা হয়েছে";
                    }
                }
            }
        }
        
        $conn->commit();
        
        return [
            'success' => true,
            'actions' => $actions
        ];
        
    } catch (Exception $e) {
        $conn->rollback();
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// Function to create sample students
function createSampleStudents($conn) {
    try {
        $conn->begin_transaction();
        
        $sampleStudents = [
            ['student_id' => 'STD001', 'name' => 'আহমেদ আলী', 'first_name' => 'আহমেদ', 'last_name' => 'আলী', 'class' => '১০', 'section' => 'ক'],
            ['student_id' => 'STD002', 'name' => 'ফাতিমা খাতুন', 'first_name' => 'ফাতিমা', 'last_name' => 'খাতুন', 'class' => '৯', 'section' => 'খ'],
            ['student_id' => 'STD003', 'name' => 'মোহাম্মদ রহিম', 'first_name' => 'মোহাম্মদ', 'last_name' => 'রহিম', 'class' => '৮', 'section' => 'গ'],
            ['student_id' => 'STD004', 'name' => 'আয়েশা বেগম', 'first_name' => 'আয়েশা', 'last_name' => 'বেগম', 'class' => '৭', 'section' => 'ক'],
            ['student_id' => 'STD005', 'name' => 'আবদুল করিম', 'first_name' => 'আবদুল', 'last_name' => 'করিম', 'class' => '৬', 'section' => 'খ']
        ];
        
        $createdCount = 0;
        foreach ($sampleStudents as $student) {
            // Check if student already exists
            $checkQuery = "SELECT id FROM students WHERE student_id = ?";
            $checkStmt = $conn->prepare($checkQuery);
            $checkStmt->bind_param('s', $student['student_id']);
            $checkStmt->execute();
            
            if ($checkStmt->get_result()->num_rows == 0) {
                // Insert new student
                $insertQuery = "INSERT INTO students (student_id, name, first_name, last_name, class, section, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())";
                $insertStmt = $conn->prepare($insertQuery);
                $insertStmt->bind_param('ssssss', 
                    $student['student_id'], 
                    $student['name'], 
                    $student['first_name'], 
                    $student['last_name'], 
                    $student['class'], 
                    $student['section']
                );
                
                if ($insertStmt->execute()) {
                    $createdCount++;
                }
            }
        }
        
        $conn->commit();
        
        return [
            'success' => true,
            'created_count' => $createdCount
        ];
        
    } catch (Exception $e) {
        $conn->rollback();
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// Handle form submissions
$checkResults = null;
$fixResults = null;
$createResults = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['check_table'])) {
        $checkResults = checkStudentsTableStructure($conn);
    } elseif (isset($_POST['fix_table'])) {
        $fixResults = fixStudentsTable($conn);
    } elseif (isset($_POST['create_students'])) {
        $createResults = createSampleStudents($conn);
    }
}

// Get current status
$currentStatus = checkStudentsTableStructure($conn);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Students Table ঠিক করুন</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: "Hind Siliguri", sans-serif;
        }
        
        .status-card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .table-good {
            background-color: #d1e7dd;
            border-left: 4px solid #198754;
        }
        
        .table-bad {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        
        .table-warning {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4 bg-white p-3 rounded shadow-sm">
                    <div>
                        <h2 class="mb-0">
                            <i class="fas fa-table text-primary me-2"></i>
                            Students Table ঠিক করুন
                        </h2>
                        <small class="text-muted">Students table structure এবং data সমস্যা সমাধান</small>
                    </div>
                    <div>
                        <a href="check_students_table.php" class="btn btn-secondary me-2">
                            <i class="fas fa-search me-1"></i> Table Check
                        </a>
                        <a href="fee_management.php" class="btn btn-primary">
                            <i class="fas fa-money-bill-wave me-1"></i> ফি ম্যানেজমেন্ট
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Status -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">বর্তমান অবস্থা</h5>
                    </div>
                    <div class="card-body">
                        <?php if (isset($currentStatus['error'])): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                <?php echo htmlspecialchars($currentStatus['error']); ?>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="card status-card <?php echo $currentStatus['table_exists'] ? 'table-good' : 'table-bad'; ?>">
                                        <div class="card-body text-center">
                                            <i class="fas fa-table fa-2x mb-2"></i>
                                            <h6>Table Exists</h6>
                                            <span class="badge <?php echo $currentStatus['table_exists'] ? 'bg-success' : 'bg-danger'; ?>">
                                                <?php echo $currentStatus['table_exists'] ? 'আছে' : 'নেই'; ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <?php if ($currentStatus['table_exists']): ?>
                                    <div class="col-md-3">
                                        <div class="card status-card <?php echo $currentStatus['has_name'] ? 'table-good' : 'table-bad'; ?>">
                                            <div class="card-body text-center">
                                                <i class="fas fa-user fa-2x mb-2"></i>
                                                <h6>Name Column</h6>
                                                <span class="badge <?php echo $currentStatus['has_name'] ? 'bg-success' : 'bg-danger'; ?>">
                                                    <?php echo $currentStatus['has_name'] ? 'আছে' : 'নেই'; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card status-card <?php echo $currentStatus['has_student_id'] ? 'table-good' : 'table-warning'; ?>">
                                            <div class="card-body text-center">
                                                <i class="fas fa-id-card fa-2x mb-2"></i>
                                                <h6>Student ID Column</h6>
                                                <span class="badge <?php echo $currentStatus['has_student_id'] ? 'bg-success' : 'bg-warning'; ?>">
                                                    <?php echo $currentStatus['has_student_id'] ? 'আছে' : 'নেই'; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card status-card <?php echo $currentStatus['row_count'] > 0 ? 'table-good' : 'table-warning'; ?>">
                                            <div class="card-body text-center">
                                                <i class="fas fa-users fa-2x mb-2"></i>
                                                <h6>Students Count</h6>
                                                <h4><?php echo $currentStatus['row_count']; ?></h4>
                                                <small><?php echo $currentStatus['row_count'] > 0 ? 'টি student' : 'কোন student নেই'; ?></small>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <form method="POST" style="display: inline;">
                            <button type="submit" name="check_table" class="btn btn-info btn-lg me-3">
                                <i class="fas fa-search me-2"></i>
                                Table চেক করুন
                            </button>
                        </form>
                        <form method="POST" style="display: inline;">
                            <button type="submit" name="fix_table" class="btn btn-warning btn-lg me-3"
                                    onclick="return confirm('আপনি কি নিশ্চিত যে students table ঠিক করতে চান?')">
                                <i class="fas fa-wrench me-2"></i>
                                Table ঠিক করুন
                            </button>
                        </form>
                        <form method="POST" style="display: inline;">
                            <button type="submit" name="create_students" class="btn btn-success btn-lg"
                                    onclick="return confirm('আপনি কি নিশ্চিত যে sample students তৈরি করতে চান?')">
                                <i class="fas fa-user-plus me-2"></i>
                                Sample Students তৈরি করুন
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Display -->
        <?php if ($fixResults): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <?php if ($fixResults['success']): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>সফল!</strong> নিম্নলিখিত actions সম্পন্ন হয়েছে:
                            <ul class="mb-0 mt-2">
                                <?php foreach ($fixResults['actions'] as $action): ?>
                                    <li><?php echo htmlspecialchars($action); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong>ত্রুটি!</strong> <?php echo htmlspecialchars($fixResults['error']); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($createResults): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <?php if ($createResults['success']): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>সফল!</strong> <?php echo $createResults['created_count']; ?> টি sample student তৈরি করা হয়েছে।
                        </div>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong>ত্রুটি!</strong> <?php echo htmlspecialchars($createResults['error']); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Instructions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">নির্দেশনা</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>সমস্যা সমাধানের ধাপ:</h6>
                            <ol>
                                <li><strong>Table চেক করুন</strong> - বর্তমান table structure দেখুন</li>
                                <li><strong>Table ঠিক করুন</strong> - Missing columns যোগ করুন বা table তৈরি করুন</li>
                                <li><strong>Sample Students তৈরি করুন</strong> - Test data যোগ করুন</li>
                                <li><strong>Fee Management</strong> এ গিয়ে test করুন</li>
                            </ol>
                        </div>

                        <div class="alert alert-success">
                            <h6><i class="fas fa-lightbulb me-2"></i>Quick Fix:</h6>
                            <p class="mb-0">যদি "Unknown column 'name'" error আসে, তাহলে "Table ঠিক করুন" button click করুন। এটি automatically missing columns যোগ করবে।</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
