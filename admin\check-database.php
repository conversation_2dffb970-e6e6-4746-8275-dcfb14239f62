<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    die('Unauthorized access');
}

echo "<h1>Database Check for Fee Management</h1>";
echo "<style>body{font-family:Arial;margin:20px;} table{border-collapse:collapse;width:100%;} th,td{border:1px solid #ddd;padding:8px;} th{background:#f2f2f2;}</style>";

// Check sessions
echo "<h2>📅 Sessions</h2>";
$sessionsQuery = "SELECT * FROM sessions ORDER BY id DESC LIMIT 10";
$result = $conn->query($sessionsQuery);
if ($result && $result->num_rows > 0) {
    echo "<table><tr><th>ID</th><th>Session Name</th><th>Start Date</th><th>End Date</th><th>Status</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td>{$row['session_name']}</td>";
        echo "<td>{$row['start_date']}</td>";
        echo "<td>{$row['end_date']}</td>";
        echo "<td>{$row['status']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color:red;'>❌ No sessions found!</p>";
}

// Check classes
echo "<h2>🏫 Classes</h2>";
$classesQuery = "SELECT * FROM classes ORDER BY id LIMIT 10";
$result = $conn->query($classesQuery);
if ($result && $result->num_rows > 0) {
    echo "<table><tr><th>ID</th><th>Class Name</th><th>Description</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td>{$row['class_name']}</td>";
        echo "<td>" . (isset($row['description']) ? $row['description'] : 'N/A') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color:red;'>❌ No classes found!</p>";
}

// Check students
echo "<h2>👥 Students</h2>";
$studentsQuery = "SELECT s.*, c.class_name, sess.session_name 
                  FROM students s 
                  LEFT JOIN classes c ON s.class_id = c.id 
                  LEFT JOIN sessions sess ON s.session_id = sess.id 
                  ORDER BY s.id DESC LIMIT 10";
$result = $conn->query($studentsQuery);
if ($result && $result->num_rows > 0) {
    echo "<table><tr><th>ID</th><th>Name</th><th>Roll</th><th>Class</th><th>Session</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td>{$row['name']}</td>";
        echo "<td>{$row['roll_number']}</td>";
        echo "<td>" . (isset($row['class_name']) ? $row['class_name'] : 'N/A') . "</td>";
        echo "<td>" . (isset($row['session_name']) ? $row['session_name'] : 'N/A') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color:red;'>❌ No students found!</p>";
}

// Check fees table structure
echo "<h2>💰 Fees Table Structure</h2>";
$feesStructure = "DESCRIBE fees";
$result = $conn->query($feesStructure);
if ($result) {
    echo "<table><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$row['Key']}</td>";
        echo "<td>" . (isset($row['Default']) ? $row['Default'] : 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color:red;'>❌ Fees table not found!</p>";
}

// Check existing fees
echo "<h2>💳 Existing Fees (Last 10)</h2>";
$feesQuery = "SELECT f.*, s.name as student_name, c.class_name 
              FROM fees f 
              LEFT JOIN students s ON f.student_id = s.id 
              LEFT JOIN classes c ON f.class_id = c.id 
              ORDER BY f.id DESC LIMIT 10";
$result = $conn->query($feesQuery);
if ($result && $result->num_rows > 0) {
    echo "<table><tr><th>ID</th><th>Student</th><th>Class</th><th>Fee Type</th><th>Amount</th><th>Due Date</th><th>Status</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td>" . (isset($row['student_name']) ? $row['student_name'] : 'N/A') . "</td>";
        echo "<td>" . (isset($row['class_name']) ? $row['class_name'] : 'N/A') . "</td>";
        echo "<td>{$row['fee_type']}</td>";
        echo "<td>{$row['amount']}</td>";
        echo "<td>{$row['due_date']}</td>";
        echo "<td>{$row['status']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color:orange;'>⚠️ No fees found in database</p>";
}

// Test AJAX endpoint
echo "<h2>🔗 AJAX Test</h2>";
echo "<p>Testing get_students.php endpoint:</p>";

// Get first session and class for testing
$testQuery = "SELECT s.id as session_id, c.id as class_id 
              FROM sessions s, classes c 
              LIMIT 1";
$result = $conn->query($testQuery);
if ($result && $row = $result->fetch_assoc()) {
    $sessionId = $row['session_id'];
    $classId = $row['class_id'];
    
    echo "<p>Testing with Session ID: $sessionId, Class ID: $classId</p>";
    echo "<a href='ajax/get_students.php?session_id=$sessionId&class_id=$classId' target='_blank' class='btn'>Test AJAX Call</a>";
} else {
    echo "<p style='color:red;'>❌ No sessions or classes available for testing</p>";
}

echo "<br><br>";
echo "<a href='fee_management.php' style='background:#007bff;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>Back to Fee Management</a>";
echo "&nbsp;&nbsp;";
echo "<a href='debug-fee.php' style='background:#28a745;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>Debug Tool</a>";

$conn->close();
?>
