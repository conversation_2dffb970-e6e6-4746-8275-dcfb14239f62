<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get format from URL
$format = $_GET['format'] ?? 'excel';

// Check if student_shortcodes table exists
$check_shortcodes_table = "SHOW TABLES LIKE 'student_shortcodes'";
$shortcodes_table_exists = $conn->query($check_shortcodes_table)->num_rows > 0;

if (!$shortcodes_table_exists) {
    echo "সর্টকোড টেবিল পাওয়া যায়নি।";
    exit;
}

// Check students table structure
$check_students_columns = "SHOW COLUMNS FROM students";
$students_columns_result = $conn->query($check_students_columns);
$has_first_name = false;
$has_last_name = false;
$has_student_id = false;
$has_class_id = false;
$has_phone = false;

if ($students_columns_result) {
    while ($column = $students_columns_result->fetch_assoc()) {
        if ($column['Field'] == 'first_name') $has_first_name = true;
        if ($column['Field'] == 'last_name') $has_last_name = true;
        if ($column['Field'] == 'student_id') $has_student_id = true;
        if ($column['Field'] == 'class_id') $has_class_id = true;
        if ($column['Field'] == 'phone') $has_phone = true;
    }
}

// Build select fields based on available columns
$select_fields = "sc.shortcode";
if ($has_first_name) $select_fields .= ", s.first_name";
if ($has_last_name) $select_fields .= ", s.last_name";
if ($has_student_id) $select_fields .= ", s.student_id";
if ($has_phone) $select_fields .= ", s.phone";

// Check if classes table exists
$check_classes_table = "SHOW TABLES LIKE 'classes'";
$classes_table_exists = $conn->query($check_classes_table)->num_rows > 0;

$join_clause = "";
if ($classes_table_exists && $has_class_id) {
    $join_clause = "LEFT JOIN classes c ON s.class_id = c.id";
    $select_fields .= ", c.class_name";
}

// Get shortcodes data
$shortcodes_query = "SELECT $select_fields
                    FROM student_shortcodes sc
                    JOIN students s ON sc.student_id = s.id
                    $join_clause
                    ORDER BY sc.shortcode";
$shortcodes_result = $conn->query($shortcodes_query);

// Prepare data
$data = [];
if ($shortcodes_result && $shortcodes_result->num_rows > 0) {
    while ($row = $shortcodes_result->fetch_assoc()) {
        $name = '';
        if (isset($row['first_name'])) {
            $name .= $row['first_name'];
        }
        if (isset($row['last_name'])) {
            $name .= ' ' . $row['last_name'];
        }

        $phone = isset($row['phone']) ? $row['phone'] : '';
        $roll = isset($row['student_id']) ? $row['student_id'] : 'N/A';
        $class = isset($row['class_name']) ? $row['class_name'] : 'N/A';

        $whatsapp_link = '';
        if (!empty($phone)) {
            $whatsapp_link = 'https://wa.me/' . (substr($phone, 0, 1) === '0' ? '88' . $phone : $phone) . '?text=শিক্ষার্থীর সর্টকোড: ' . $row['shortcode'];
        } else {
            $whatsapp_link = 'https://wa.me/?text=শিক্ষার্থীর সর্টকোড: ' . $row['shortcode'];
        }

        $data[] = [
            'shortcode' => $row['shortcode'],
            'name' => $name,
            'roll' => $roll,
            'class' => $class,
            'phone' => $phone,
            'whatsapp_link' => $whatsapp_link
        ];
    }
}

// Export based on format
switch ($format) {
    case 'csv':
        // Set headers for CSV download
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="student_shortcodes.csv"');

        // Create CSV file
        $output = fopen('php://output', 'w');

        // Add BOM for UTF-8
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

        // Add header row
        fputcsv($output, ['সর্টকোড', 'শিক্ষার্থীর নাম', 'রোল', 'শ্রেণী', 'ফোন নম্বর', 'WhatsApp লিংক']);

        // Add data rows
        foreach ($data as $row) {
            fputcsv($output, [
                $row['shortcode'],
                $row['name'],
                $row['roll'],
                $row['class'],
                $row['phone'],
                $row['whatsapp_link']
            ]);
        }

        fclose($output);
        break;

    case 'pdf':
        // Simple HTML output with PDF-like styling
        header('Content-Type: text/html; charset=utf-8');

        echo '<!DOCTYPE html>
        <html>
        <head>
    <?php include 'includes/global-head.php'; ?>
    <title>শিক্ষার্থী সর্টকোড তালিকা</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 20px;
                    background-color: #f5f5f5;
                }
                h1 {
                    text-align: center;
                    font-size: 24px;
                    margin-bottom: 20px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                    background-color: white;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: left;
                }
                th {
                    background-color: #f2f2f2;
                    font-weight: bold;
                }
                .shortcode {
                    font-weight: bold;
                    color: #0066cc;
                }
                .print-btn {
                    display: block;
                    width: 200px;
                    margin: 20px auto;
                    padding: 10px;
                    background-color: #4CAF50;
                    color: white;
                    text-align: center;
                    text-decoration: none;
                    border-radius: 4px;
                    cursor: pointer;
                }
                @media print {
                    .print-btn {
                        display: none;
                    }
                    body {
                        background-color: white;
                    }
                }
            </style>
        
    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
        <body>
            <button class="print-btn" onclick="window.print()">প্রিন্ট করুন</button>
            <h1>শিক্ষার্থী সর্টকোড তালিকা</h1>
            <table>
                <thead>
                    <tr>
                        <th>সর্টকোড</th>
                        <th>শিক্ষার্থীর নাম</th>
                        <th>রোল</th>
                        <th>শ্রেণী</th>
                        <th>ফোন নম্বর</th>
                    </tr>
                </thead>
                <tbody>';

        foreach ($data as $row) {
            echo '<tr>
                <td class="shortcode">' . $row['shortcode'] . '</td>
                <td>' . $row['name'] . '</td>
                <td>' . $row['roll'] . '</td>
                <td>' . $row['class'] . '</td>
                <td>' . $row['phone'] . '</td>
            </tr>';
        }

        echo '</tbody>
            </table>
            <button class="print-btn" onclick="window.print()">প্রিন্ট করুন</button>
        </body>
        </html>';
        exit;
        break;

    default: // Excel format
        // Set headers for Excel download
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="student_shortcodes.xlsx"');
        header('Cache-Control: max-age=0');

        // If you have PhpSpreadsheet installed, use it
        // For simplicity, we'll output a CSV with Excel extension
        $output = fopen('php://output', 'w');

        // Add BOM for UTF-8
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

        // Add header row
        fputcsv($output, ['সর্টকোড', 'শিক্ষার্থীর নাম', 'রোল', 'শ্রেণী', 'ফোন নম্বর', 'WhatsApp লিংক']);

        // Add data rows
        foreach ($data as $row) {
            fputcsv($output, [
                $row['shortcode'],
                $row['name'],
                $row['roll'],
                $row['class'],
                $row['phone'],
                $row['whatsapp_link']
            ]);
        }

        fclose($output);
        break;
}

exit;
