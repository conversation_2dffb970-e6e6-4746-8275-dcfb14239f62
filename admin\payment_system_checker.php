<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Function to check payment system integrity
function checkPaymentSystemIntegrity($conn) {
    $results = [];
    
    try {
        // 1. Check table existence
        $tables = ['students', 'fees', 'fee_payments', 'payments'];
        $tableStatus = [];
        
        foreach ($tables as $table) {
            $checkTable = $conn->query("SHOW TABLES LIKE '$table'");
            $tableStatus[$table] = $checkTable->num_rows > 0;
        }
        $results['table_status'] = $tableStatus;
        
        // 2. Check data consistency
        $dataConsistency = [];
        
        // Check fees vs payments
        if ($tableStatus['fees'] && $tableStatus['fee_payments']) {
            $inconsistentQuery = "SELECT f.id, f.student_id, f.fee_type, f.amount, f.paid, 
                                  COALESCE(SUM(fp.amount), 0) as actual_payments,
                                  ABS(f.paid - COALESCE(SUM(fp.amount), 0)) as difference
                                  FROM fees f 
                                  LEFT JOIN fee_payments fp ON f.id = fp.fee_id 
                                  GROUP BY f.id 
                                  HAVING ABS(f.paid - COALESCE(SUM(fp.amount), 0)) > 0.01";
            
            $inconsistentResult = $conn->query($inconsistentQuery);
            $inconsistentFees = [];
            while ($row = $inconsistentResult->fetch_assoc()) {
                $inconsistentFees[] = $row;
            }
            $dataConsistency['inconsistent_fees'] = $inconsistentFees;
        }
        
        // Check orphaned payments
        if ($tableStatus['fee_payments'] && $tableStatus['fees']) {
            $orphanedQuery = "SELECT fp.* FROM fee_payments fp 
                             LEFT JOIN fees f ON fp.fee_id = f.id 
                             WHERE f.id IS NULL";
            
            $orphanedResult = $conn->query($orphanedQuery);
            $orphanedPayments = [];
            while ($row = $orphanedResult->fetch_assoc()) {
                $orphanedPayments[] = $row;
            }
            $dataConsistency['orphaned_payments'] = $orphanedPayments;
        }
        
        // Check invalid student references
        if ($tableStatus['fees'] && $tableStatus['students']) {
            $invalidStudentQuery = "SELECT f.* FROM fees f 
                                   LEFT JOIN students s ON f.student_id = s.id 
                                   WHERE s.id IS NULL";
            
            $invalidStudentResult = $conn->query($invalidStudentQuery);
            $invalidStudentFees = [];
            while ($row = $invalidStudentResult->fetch_assoc()) {
                $invalidStudentFees[] = $row;
            }
            $dataConsistency['invalid_student_fees'] = $invalidStudentFees;
        }
        
        $results['data_consistency'] = $dataConsistency;
        
        // 3. Check payment statistics
        $statistics = [];
        
        if ($tableStatus['fees']) {
            $feeStatsQuery = "SELECT 
                             COUNT(*) as total_fees,
                             SUM(amount) as total_amount,
                             SUM(paid) as total_paid,
                             SUM(amount - paid) as total_due,
                             COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_fees,
                             COUNT(CASE WHEN payment_status = 'partial' THEN 1 END) as partial_fees,
                             COUNT(CASE WHEN payment_status = 'due' THEN 1 END) as due_fees
                             FROM fees";
            
            $feeStatsResult = $conn->query($feeStatsQuery);
            $statistics['fee_stats'] = $feeStatsResult->fetch_assoc();
        }
        
        if ($tableStatus['fee_payments']) {
            $paymentStatsQuery = "SELECT 
                                 COUNT(*) as total_payments,
                                 SUM(amount) as total_payment_amount,
                                 COUNT(DISTINCT fee_id) as unique_fees_paid,
                                 MIN(payment_date) as earliest_payment,
                                 MAX(payment_date) as latest_payment
                                 FROM fee_payments";
            
            $paymentStatsResult = $conn->query($paymentStatsQuery);
            $statistics['payment_stats'] = $paymentStatsResult->fetch_assoc();
        }
        
        $results['statistics'] = $statistics;
        
        return $results;
        
    } catch (Exception $e) {
        return [
            'error' => $e->getMessage()
        ];
    }
}

// Function to fix common issues
function fixCommonIssues($conn) {
    try {
        $conn->begin_transaction();
        $fixedIssues = [];
        
        // 1. Fix fee paid amounts based on actual payments
        $updateQuery = "UPDATE fees f 
                       SET paid = (
                           SELECT COALESCE(SUM(fp.amount), 0) 
                           FROM fee_payments fp 
                           WHERE fp.fee_id = f.id
                       )";
        
        if ($conn->query($updateQuery)) {
            $fixedIssues[] = "Fee paid amounts updated based on actual payments";
        }
        
        // 2. Update payment status based on paid amounts
        $statusUpdateQuery = "UPDATE fees 
                             SET payment_status = CASE 
                                 WHEN paid >= amount THEN 'paid'
                                 WHEN paid > 0 THEN 'partial'
                                 ELSE 'due'
                             END";
        
        if ($conn->query($statusUpdateQuery)) {
            $fixedIssues[] = "Payment statuses updated";
        }
        
        $conn->commit();
        
        return [
            'success' => true,
            'fixed_issues' => $fixedIssues
        ];
        
    } catch (Exception $e) {
        $conn->rollback();
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// Handle form submissions
$checkResults = null;
$fixResults = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['check_system'])) {
        $checkResults = checkPaymentSystemIntegrity($conn);
    } elseif (isset($_POST['fix_issues'])) {
        $fixResults = fixCommonIssues($conn);
    }
}

// Get current system status
$systemStatus = checkPaymentSystemIntegrity($conn);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment System Checker</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: "Hind Siliguri", sans-serif;
        }
        
        .status-card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }
        
        .status-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .table-exists {
            background: linear-gradient(135deg, #d1e7dd 0%, #a3d9a4 100%);
            border-left: 4px solid #198754;
        }
        
        .table-missing {
            background: linear-gradient(135deg, #f8d7da 0%, #f1aeb5 100%);
            border-left: 4px solid #dc3545;
        }
        
        .issue-card {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        
        .success-card {
            background-color: #d1e7dd;
            border-left: 4px solid #198754;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4 bg-white p-3 rounded shadow-sm">
                    <div>
                        <h2 class="mb-0">
                            <i class="fas fa-stethoscope text-primary me-2"></i>
                            Payment System Checker
                        </h2>
                        <small class="text-muted">Payment system এর সম্পূর্ণ health check এবং সমস্যা সমাধান</small>
                    </div>
                    <div>
                        <a href="fee_management.php" class="btn btn-secondary me-2">
                            <i class="fas fa-arrow-left me-1"></i> ফি ম্যানেজমেন্ট
                        </a>
                        <a href="fix_payment_tables.php" class="btn btn-warning">
                            <i class="fas fa-tools me-1"></i> Table Fix
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Status Overview -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-heartbeat me-2"></i>
                            System Health Overview
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php if (isset($systemStatus['table_status'])): ?>
                                <?php foreach ($systemStatus['table_status'] as $table => $exists): ?>
                                    <div class="col-md-3">
                                        <div class="card status-card <?php echo $exists ? 'table-exists' : 'table-missing'; ?>">
                                            <div class="card-body text-center">
                                                <i class="fas fa-database fa-2x mb-2"></i>
                                                <h6><?php echo ucfirst($table); ?></h6>
                                                <span class="badge <?php echo $exists ? 'bg-success' : 'bg-danger'; ?>">
                                                    <?php echo $exists ? 'বিদ্যমান' : 'অনুপস্থিত'; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <form method="POST" style="display: inline;">
                            <button type="submit" name="check_system" class="btn btn-primary btn-lg me-3">
                                <i class="fas fa-search me-2"></i>
                                সম্পূর্ণ System Check করুন
                            </button>
                        </form>
                        <form method="POST" style="display: inline;">
                            <button type="submit" name="fix_issues" class="btn btn-success btn-lg"
                                    onclick="return confirm('আপনি কি নিশ্চিত যে সাধারণ সমস্যাগুলো ঠিক করতে চান?')">
                                <i class="fas fa-wrench me-2"></i>
                                সাধারণ সমস্যা ঠিক করুন
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Display -->
        <?php if ($checkResults): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-clipboard-check me-2"></i>
                                System Check Results
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (isset($checkResults['error'])): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    <?php echo htmlspecialchars($checkResults['error']); ?>
                                </div>
                            <?php else: ?>
                                <!-- Data Consistency Issues -->
                                <?php if (isset($checkResults['data_consistency'])): ?>
                                    <div class="row mb-4">
                                        <div class="col-12">
                                            <h6><i class="fas fa-balance-scale me-2"></i>Data Consistency</h6>

                                            <?php if (!empty($checkResults['data_consistency']['inconsistent_fees'])): ?>
                                                <div class="card issue-card mb-3">
                                                    <div class="card-body">
                                                        <h6 class="text-warning">
                                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                                            Inconsistent Fees (<?php echo count($checkResults['data_consistency']['inconsistent_fees']); ?>)
                                                        </h6>
                                                        <div class="table-responsive">
                                                            <table class="table table-sm">
                                                                <thead>
                                                                    <tr>
                                                                        <th>Fee ID</th>
                                                                        <th>Fee Type</th>
                                                                        <th>Recorded Paid</th>
                                                                        <th>Actual Payments</th>
                                                                        <th>Difference</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <?php foreach (array_slice($checkResults['data_consistency']['inconsistent_fees'], 0, 10) as $fee): ?>
                                                                        <tr>
                                                                            <td><?php echo $fee['id']; ?></td>
                                                                            <td><?php echo htmlspecialchars($fee['fee_type']); ?></td>
                                                                            <td>৳<?php echo number_format($fee['paid'], 2); ?></td>
                                                                            <td>৳<?php echo number_format($fee['actual_payments'], 2); ?></td>
                                                                            <td class="text-danger">৳<?php echo number_format($fee['difference'], 2); ?></td>
                                                                        </tr>
                                                                    <?php endforeach; ?>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>

                                            <?php if (!empty($checkResults['data_consistency']['orphaned_payments'])): ?>
                                                <div class="card issue-card mb-3">
                                                    <div class="card-body">
                                                        <h6 class="text-warning">
                                                            <i class="fas fa-unlink me-2"></i>
                                                            Orphaned Payments (<?php echo count($checkResults['data_consistency']['orphaned_payments']); ?>)
                                                        </h6>
                                                        <p class="text-muted">এই payments গুলোর সাথে কোন valid fee নেই</p>
                                                    </div>
                                                </div>
                                            <?php endif; ?>

                                            <?php if (!empty($checkResults['data_consistency']['invalid_student_fees'])): ?>
                                                <div class="card issue-card mb-3">
                                                    <div class="card-body">
                                                        <h6 class="text-warning">
                                                            <i class="fas fa-user-times me-2"></i>
                                                            Invalid Student References (<?php echo count($checkResults['data_consistency']['invalid_student_fees']); ?>)
                                                        </h6>
                                                        <p class="text-muted">এই fees গুলোর সাথে কোন valid student নেই</p>
                                                    </div>
                                                </div>
                                            <?php endif; ?>

                                            <?php if (empty($checkResults['data_consistency']['inconsistent_fees']) &&
                                                     empty($checkResults['data_consistency']['orphaned_payments']) &&
                                                     empty($checkResults['data_consistency']['invalid_student_fees'])): ?>
                                                <div class="card success-card">
                                                    <div class="card-body">
                                                        <h6 class="text-success">
                                                            <i class="fas fa-check-circle me-2"></i>
                                                            কোন Data Consistency সমস্যা নেই!
                                                        </h6>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Statistics -->
                                <?php if (isset($checkResults['statistics'])): ?>
                                    <div class="row">
                                        <?php if (isset($checkResults['statistics']['fee_stats'])): ?>
                                            <div class="col-md-6">
                                                <div class="card stat-card">
                                                    <div class="card-body">
                                                        <h6><i class="fas fa-chart-bar me-2"></i>Fee Statistics</h6>
                                                        <?php $feeStats = $checkResults['statistics']['fee_stats']; ?>
                                                        <ul class="list-unstyled mb-0">
                                                            <li><strong>মোট ফি:</strong> <?php echo $feeStats['total_fees']; ?> টি</li>
                                                            <li><strong>মোট পরিমাণ:</strong> ৳<?php echo number_format($feeStats['total_amount'], 2); ?></li>
                                                            <li><strong>পরিশোধিত:</strong> ৳<?php echo number_format($feeStats['total_paid'], 2); ?></li>
                                                            <li><strong>বকেয়া:</strong> ৳<?php echo number_format($feeStats['total_due'], 2); ?></li>
                                                            <li><strong>পরিশোধিত ফি:</strong> <?php echo $feeStats['paid_fees']; ?> টি</li>
                                                            <li><strong>আংশিক ফি:</strong> <?php echo $feeStats['partial_fees']; ?> টি</li>
                                                            <li><strong>বকেয়া ফি:</strong> <?php echo $feeStats['due_fees']; ?> টি</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endif; ?>

                                        <?php if (isset($checkResults['statistics']['payment_stats'])): ?>
                                            <div class="col-md-6">
                                                <div class="card stat-card">
                                                    <div class="card-body">
                                                        <h6><i class="fas fa-money-bill-wave me-2"></i>Payment Statistics</h6>
                                                        <?php $paymentStats = $checkResults['statistics']['payment_stats']; ?>
                                                        <ul class="list-unstyled mb-0">
                                                            <li><strong>মোট পেমেন্ট:</strong> <?php echo $paymentStats['total_payments']; ?> টি</li>
                                                            <li><strong>মোট পরিমাণ:</strong> ৳<?php echo number_format($paymentStats['total_payment_amount'], 2); ?></li>
                                                            <li><strong>Unique Fees:</strong> <?php echo $paymentStats['unique_fees_paid']; ?> টি</li>
                                                            <li><strong>প্রথম পেমেন্ট:</strong> <?php echo $paymentStats['earliest_payment']; ?></li>
                                                            <li><strong>শেষ পেমেন্ট:</strong> <?php echo $paymentStats['latest_payment']; ?></li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($fixResults): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <?php if ($fixResults['success']): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>সফল!</strong> নিম্নলিখিত সমস্যাগুলো ঠিক করা হয়েছে:
                            <ul class="mb-0 mt-2">
                                <?php foreach ($fixResults['fixed_issues'] as $issue): ?>
                                    <li><?php echo htmlspecialchars($issue); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong>ত্রুটি!</strong> <?php echo htmlspecialchars($fixResults['error']); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="fix_fee_data.php" class="btn btn-warning w-100 mb-2">
                                    <i class="fas fa-tools me-1"></i>
                                    Fee Data Fix
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="fix_payment_tables.php" class="btn btn-info w-100 mb-2">
                                    <i class="fas fa-database me-1"></i>
                                    Table Migration
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="fee_management.php" class="btn btn-primary w-100 mb-2">
                                    <i class="fas fa-money-bill-wave me-1"></i>
                                    Fee Management
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="student_dues_summary.php" class="btn btn-secondary w-100 mb-2">
                                    <i class="fas fa-list me-1"></i>
                                    Dues Summary
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
