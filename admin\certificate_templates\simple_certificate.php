<?php
// This file contains the simple certificate template based on the provided sample

function generate_simple_certificate($student_name, $student_roll, $student_class, $father_name, $mother_name, $certificate_title, $institute_name, $issued_by, $logo_text = '') {
    // If logo text is empty, use institute name
    if (empty($logo_text)) {
        $logo_text = $institute_name;
    }
    // HTML for the certificate
    $html = '<!DOCTYPE html>
<html>
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>Certificate</title>
    <style>
        @page {
            size: 29.7cm 21cm landscape;
            margin: 0;
        }
        body {
            margin: 0;
            padding: 0;
            font-family: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", Arial, sans-serif;
            background-color: #ffffff;
        }
        .certificate-container {
            width: 29.7cm;
            height: 21cm;
            position: relative;
            margin: 0 auto;
            background-color: #ffffff;
            overflow: hidden;
        }
        .background-logo {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 70%;
            height: auto;
            opacity: 0.15; /* Increased opacity for better visibility */
            z-index: 0;
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .background-logo img {
            max-width: 100%;
            max-height: 400px;
            object-fit: contain;
        }
        .border {
            position: absolute;
            top: 1.5cm;
            left: 1.5cm;
            right: 1.5cm;
            bottom: 0.5cm; /* Extended at the bottom */
            border: 5px solid #1a237e;
            box-sizing: border-box;
            padding: 20px;
            background-color: #ffffff;
            z-index: 1;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
        }
        .border-inner {
            width: 100%;
            height: 100%;
            border: 1px solid #4a5fb4;
            box-sizing: border-box;
            padding: 20px;
            position: relative;
        }
        .border-pattern {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 15px;
            background: repeating-linear-gradient(
                45deg,
                #e8eaf6,
                #e8eaf6 10px,
                #c5cae9 10px,
                #c5cae9 20px
            );
        }
        .border-pattern-bottom {
            top: auto;
            bottom: 0;
        }
        .content {
            padding: 30px 20px;
            text-align: center;
            position: relative;
            z-index: 1;
        }
        .title {
            font-size: 32px;
            color: #1a237e;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.1);
        }
        .subtitle {
            font-size: 36px;
            color: #1a237e;
            margin: 30px 0 50px;
            text-align: center;
            font-weight: bold;
            padding: 5px 30px;
            border-bottom: 2px solid #1a237e;
            display: inline-block;
            position: relative;
        }
        .subtitle:before, .subtitle:after {
            content: "★";
            color: #1a237e;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
        }
        .subtitle:before {
            left: 0;
        }
        .subtitle:after {
            right: 0;
        }
        .certificate-text {
            font-size: 22px;
            line-height: 2.5;
            color: #000000;
            text-align: left;
            margin: 40px 30px;
            position: relative;
        }
        .dotted-line {
            border-bottom: 1px dotted #000000;
            display: inline-block;
            min-width: 150px;
            padding: 0 5px;
        }
        .signature-container {
            position: absolute;
            bottom: 2cm; /* Moved up to be inside the border */
            right: 80px;
            z-index: 10;
        }
        .signature {
            text-align: center;
            background-color: white;
            padding: 10px 20px;
            border: 1px solid #1a237e;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .signature-line {
            width: 200px;
            border-top: 1px solid #000000;
            margin-bottom: 10px;
        }
        .signature-title {
            font-size: 18px;
            color: #000000;
            font-weight: bold;
        }
        .certificate-number {
            position: absolute;
            top: 20px;
            right: 40px;
            font-size: 16px;
            color: #1a237e;
        }
        .date {
            position: absolute;
            top: 20px;
            left: 40px;
            font-size: 16px;
            color: #1a237e;
        }
        .decorative-corner {
            position: absolute;
            width: 30px;
            height: 30px;
            border-style: solid;
            border-color: #1a237e;
            border-width: 0;
        }
        .corner-top-left {
            top: 10px;
            left: 10px;
            border-left-width: 3px;
            border-top-width: 3px;
        }
        .corner-top-right {
            top: 10px;
            right: 10px;
            border-right-width: 3px;
            border-top-width: 3px;
        }
        .corner-bottom-left {
            bottom: 10px;
            left: 10px;
            border-left-width: 3px;
            border-bottom-width: 3px;
        }
        .corner-bottom-right {
            bottom: 10px;
            right: 10px;
            border-right-width: 3px;
            border-bottom-width: 3px;
        }
        @media print {
            body {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="certificate-container">
        <div class="background-logo">
            ' . (file_exists($_SERVER['DOCUMENT_ROOT'] . "/zfaw/uploads/watermark.png") ? '<img src="../uploads/watermark.png?v=' . time() . '" alt="Watermark">' : '<div style="font-size: 48px; font-weight: bold; color: #1a237e; text-align: center; opacity: 0.7;">' . $logo_text . '</div>') . '
        </div>

        <div class="border">
            <div class="border-inner">
                <div class="border-pattern"></div>
                <div class="border-pattern border-pattern-bottom"></div>

                <div class="decorative-corner corner-top-left"></div>
                <div class="decorative-corner corner-top-right"></div>
                <div class="decorative-corner corner-bottom-left"></div>
                <div class="decorative-corner corner-bottom-right"></div>

                <div class="content">
                    <div class="title">' . $institute_name . '</div>

                    <div class="subtitle">' . $certificate_title . '</div>

                    <div class="certificate-text">
                        এতদ্বারা প্রত্যয়ন করা যাচ্ছে যে, <span class="dotted-line">' . $student_name . '</span> পিতা: <span class="dotted-line">' . $father_name . '</span><br>
                        মাতা: <span class="dotted-line">' . $mother_name . '</span> রোল নং: <span class="dotted-line">' . $student_roll . '</span> শ্রেণী: <span class="dotted-line">' . $student_class . '</span><br>
                        আমাদের বিদ্যালয়ের একজন নিয়মিত ছাত্র/ছাত্রী। সে চরিত্রবান/চরিত্রবতী এবং অধ্যয়নে মনোযোগী।<br>
                        আমি তার উজ্জ্বল ভবিষ্যৎ কামনা করি।
                    </div>

                    <div class="certificate-number">সনদ নং: ' . date('Y') . '/' . rand(1000, 9999) . '</div>
                    <div class="date">তারিখ: ' . date('d/m/Y') . '</div>

                    <div class="signature-container">
                        <div class="signature">
                            <div class="signature-line"></div>
                            <div class="signature-title">' . $issued_by . '</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>';

    return $html;
}
?>
