<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';
require_once '../includes/functions.php';
require_once '../includes/sms_config.php';

// Initialize variables
$success_message = $error_message = '';
$generated_message = '';
$phone_number = '';
$message_type = 'fee_reminder';
$custom_prompt = '';

// Get SMS configuration
$sms_config = get_sms_config();
$is_sms_enabled = $sms_config['is_enabled'] ?? false;
$test_mode = $sms_config['test_mode'] ?? true;

// Handle form submission for generating message
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['generate_message'])) {
    $message_type = $_POST['message_type'];
    $custom_prompt = trim($_POST['custom_prompt']);

    // Generate message based on type
    switch ($message_type) {
        case 'fee_reminder':
            $generated_message = "প্রিয় অভিভাবক, আপনার সন্তানের বকেয়া স্কুল ফি পরিশোধ করার জন্য অনুরোধ করা হচ্ছে। অনুগ্রহ করে যত দ্রুত সম্ভব ফি পরিশোধ করুন। ধন্যবাদ।";
            break;

        case 'exam_notice':
            $generated_message = "প্রিয় অভিভাবক, আগামী সোমবার থেকে মধ্য-বার্ষিক পরীক্ষা শুরু হবে। অনুগ্রহ করে আপনার সন্তানকে পরীক্ষার জন্য প্রস্তুত করুন। ধন্যবাদ।";
            break;

        case 'holiday_notice':
            $generated_message = "প্রিয় অভিভাবক, আগামীকাল স্কুল বন্ধ থাকবে। অনুগ্রহ করে নোট করুন। ধন্যবাদ।";
            break;

        case 'parent_meeting':
            $generated_message = "প্রিয় অভিভাবক, আগামী শুক্রবার সকাল ১০টায় অভিভাবক সভা অনুষ্ঠিত হবে। আপনার উপস্থিতি একান্ত কাম্য। ধন্যবাদ।";
            break;

        case 'custom':
            if (empty($custom_prompt)) {
                $error_message = "কাস্টম মেসেজ জেনারেট করতে প্রম্পট লিখুন।";
            } else {
                // Simple AI message generation based on prompt
                $generated_message = generate_ai_message($custom_prompt);
            }
            break;
    }
}

// Handle form submission for sending SMS
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_sms'])) {
    $phone_number = trim($_POST['phone_number']);
    $generated_message = trim($_POST['generated_message']);

    if (empty($phone_number)) {
        $error_message = "ফোন নম্বর দিন।";
    } elseif (empty($generated_message)) {
        $error_message = "মেসেজ খালি রাখা যাবে না।";
    } else {
        // Send SMS
        $result = send_sms($phone_number, $generated_message);

        if ($result['status']) {
            $success_message = "SMS সফলভাবে পাঠানো হয়েছে। " . $result['message'];
        } else {
            $error_message = "SMS পাঠাতে সমস্যা: " . $result['message'];

            // Show detailed error information for admins
            if (isset($result['error_details'])) {
                $error_details = $result['error_details'];
                $error_message .= '<div class="mt-2 small"><strong>বিস্তারিত এরর:</strong> <pre class="p-2 bg-light">' . htmlspecialchars($error_details) . '</pre></div>';
            }
        }
    }
}

/**
 * Generate AI message based on prompt
 *
 * @param string $prompt User prompt
 * @return string Generated message
 */
function generate_ai_message($prompt) {
    // Simple AI message generation based on keywords in prompt
    $prompt = strtolower($prompt);

    if (strpos($prompt, 'ফি') !== false || strpos($prompt, 'fee') !== false) {
        return "প্রিয় অভিভাবক, আপনার সন্তানের " . date('F') . " মাসের স্কুল ফি বকেয়া আছে। অনুগ্রহ করে যত দ্রুত সম্ভব পরিশোধ করুন। ধন্যবাদ।";
    } elseif (strpos($prompt, 'পরীক্ষা') !== false || strpos($prompt, 'exam') !== false) {
        return "প্রিয় অভিভাবক, আগামী " . date('d/m/Y', strtotime('+7 days')) . " তারিখ থেকে বার্ষিক পরীক্ষা শুরু হবে। অনুগ্রহ করে আপনার সন্তানকে প্রস্তুত করুন। ধন্যবাদ।";
    } elseif (strpos($prompt, 'ছুটি') !== false || strpos($prompt, 'holiday') !== false) {
        return "প্রিয় অভিভাবক, আগামীকাল " . date('d/m/Y', strtotime('+1 day')) . " তারিখে স্কুল বন্ধ থাকবে। অনুগ্রহ করে নোট করুন। ধন্যবাদ।";
    } elseif (strpos($prompt, 'সভা') !== false || strpos($prompt, 'meeting') !== false) {
        return "প্রিয় অভিভাবক, আগামী " . date('d/m/Y', strtotime('+3 days')) . " তারিখে সকাল ১০টায় অভিভাবক সভা অনুষ্ঠিত হবে। আপনার উপস্থিতি একান্ত কাম্য। ধন্যবাদ।";
    } else {
        return "প্রিয় অভিভাবক, " . $prompt . " ধন্যবাদ।";
    }
}

// Set current page for sidebar highlighting
$currentPage = 'sms_ai_tools.php';
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>SMS AI টুলস - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    
    <style>
        .sms-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .message-preview {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            min-height: 100px;
        }
        .form-label {
            font-weight: 500;
        }
        .required-field::after {
            content: "*";
            color: red;
            margin-left: 4px;
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>SMS AI টুলস</h2>
                        <p class="text-muted">AI এর সাহায্যে SMS তৈরি করুন এবং পাঠান</p>
                    </div>
                    <div class="col-auto">
                        <a href="sms_setup.php" class="btn btn-secondary">
                            <i class="fas fa-cog me-2"></i>SMS সেটআপ
                        </a>
                    </div>
                </div>

                <?php if (!$is_sms_enabled): ?>
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>সতর্কতা:</strong> SMS সার্ভিস এখনও কনফিগার করা হয়নি। SMS পাঠাতে
                        <a href="sms_setup.php" class="alert-link">SMS সেটআপ</a> পেজে গিয়ে কনফিগারেশন সম্পন্ন করুন।
                    </div>
                <?php endif; ?>

                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- Message Generation Card -->
                    <div class="col-md-6 mb-4">
                        <div class="card sms-card h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-robot me-2"></i>AI মেসেজ জেনারেটর
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="post" action="" id="generateForm">
                                    <div class="mb-3">
                                        <label for="message_type" class="form-label required-field">মেসেজের ধরন</label>
                                        <select class="form-select" id="message_type" name="message_type" required>
                                            <option value="fee_reminder" <?php echo ($message_type == 'fee_reminder') ? 'selected' : ''; ?>>ফি রিমাইন্ডার</option>
                                            <option value="exam_notice" <?php echo ($message_type == 'exam_notice') ? 'selected' : ''; ?>>পরীক্ষার নোটিশ</option>
                                            <option value="holiday_notice" <?php echo ($message_type == 'holiday_notice') ? 'selected' : ''; ?>>ছুটির নোটিশ</option>
                                            <option value="parent_meeting" <?php echo ($message_type == 'parent_meeting') ? 'selected' : ''; ?>>অভিভাবক সভা</option>
                                            <option value="custom" <?php echo ($message_type == 'custom') ? 'selected' : ''; ?>>কাস্টম মেসেজ</option>
                                        </select>
                                    </div>

                                    <div class="mb-3" id="customPromptContainer" style="display: <?php echo ($message_type == 'custom') ? 'block' : 'none'; ?>;">
                                        <label for="custom_prompt" class="form-label">কাস্টম প্রম্পট</label>
                                        <textarea class="form-control" id="custom_prompt" name="custom_prompt" rows="3" placeholder="কি ধরনের মেসেজ তৈরি করতে চান তা লিখুন..."><?php echo htmlspecialchars($custom_prompt); ?></textarea>
                                        <div class="form-text">উদাহরণ: "ফি পরিশোধের রিমাইন্ডার" বা "পরীক্ষার নোটিশ"</div>
                                    </div>

                                    <div class="text-center">
                                        <button type="submit" name="generate_message" class="btn btn-primary">
                                            <i class="fas fa-magic me-2"></i>মেসেজ তৈরি করুন
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Message Preview and Send Card -->
                    <div class="col-md-6 mb-4">
                        <div class="card sms-card h-100">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-paper-plane me-2"></i>মেসেজ প্রিভিউ এবং পাঠান
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="post" action="" id="sendForm">
                                    <div class="mb-3">
                                        <label for="generated_message" class="form-label required-field">মেসেজ</label>
                                        <div class="message-preview mb-2">
                                            <?php echo !empty($generated_message) ? nl2br(htmlspecialchars($generated_message)) : '<span class="text-muted">এখানে মেসেজ প্রিভিউ দেখা যাবে...</span>'; ?>
                                        </div>
                                        <textarea class="form-control" id="generated_message" name="generated_message" rows="4" required><?php echo htmlspecialchars($generated_message); ?></textarea>
                                        <div class="d-flex justify-content-between mt-1">
                                            <div class="form-text">মেসেজ সম্পাদনা করতে পারেন</div>
                                            <div class="form-text" id="character_count">0/160 অক্ষর</div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="phone_number" class="form-label required-field">ফোন নম্বর</label>
                                        <input type="text" class="form-control" id="phone_number" name="phone_number" placeholder="01XXXXXXXXX" value="<?php echo htmlspecialchars($phone_number); ?>" pattern="(01[3-9]\d{8}|8801[3-9]\d{8})" required>
                                        <div class="form-text">যে নম্বরে SMS পাঠাতে চান (উদাহরণ: 01712345678)</div>
                                        <div class="invalid-feedback">সঠিক বাংলাদেশী মোবাইল নম্বর দিন (01XXXXXXXXX)</div>
                                    </div>
                                    <div class="alert alert-info mb-3">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>ফোন নম্বর ফরম্যাট:</strong> বাংলাদেশী মোবাইল নম্বর 01 দিয়ে শুরু করতে হবে, যেমন 01712345678
                                    </div>

                                    <div class="text-center">
                                        <button type="submit" name="send_sms" class="btn btn-success" <?php echo (!$is_sms_enabled) ? 'disabled' : ''; ?>>
                                            <i class="fas fa-paper-plane me-2"></i>SMS পাঠান
                                        </button>
                                    </div>

                                    <?php if ($test_mode && $is_sms_enabled): ?>
                                        <div class="alert alert-info mt-3 mb-0">
                                            <i class="fas fa-info-circle me-2"></i>
                                            <strong>টেস্ট মোড:</strong> SMS আসলে পাঠানো হবে না। প্রকৃত SMS পাঠাতে <a href="sms_setup.php" class="alert-link">SMS সেটআপ</a> পেজে গিয়ে টেস্ট মোড বন্ধ করুন।
                                        </div>
                                    <?php endif; ?>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SMS Templates Card -->
                <div class="card sms-card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>SMS টেমপ্লেট
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="card-title mb-0">ফি রিমাইন্ডার</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>প্রিয় অভিভাবক, আপনার সন্তানের বকেয়া স্কুল ফি পরিশোধ করার জন্য অনুরোধ করা হচ্ছে। অনুগ্রহ করে যত দ্রুত সম্ভব ফি পরিশোধ করুন। ধন্যবাদ।</p>
                                        <button type="button" class="btn btn-sm btn-outline-primary use-template" data-template="fee_reminder">
                                            <i class="fas fa-plus-circle me-2"></i>ব্যবহার করুন
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="card-title mb-0">পরীক্ষার নোটিশ</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>প্রিয় অভিভাবক, আগামী সোমবার থেকে মধ্য-বার্ষিক পরীক্ষা শুরু হবে। অনুগ্রহ করে আপনার সন্তানকে পরীক্ষার জন্য প্রস্তুত করুন। ধন্যবাদ।</p>
                                        <button type="button" class="btn btn-sm btn-outline-primary use-template" data-template="exam_notice">
                                            <i class="fas fa-plus-circle me-2"></i>ব্যবহার করুন
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="card-title mb-0">ছুটির নোটিশ</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>প্রিয় অভিভাবক, আগামীকাল স্কুল বন্ধ থাকবে। অনুগ্রহ করে নোট করুন। ধন্যবাদ।</p>
                                        <button type="button" class="btn btn-sm btn-outline-primary use-template" data-template="holiday_notice">
                                            <i class="fas fa-plus-circle me-2"></i>ব্যবহার করুন
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="card-title mb-0">অভিভাবক সভা</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>প্রিয় অভিভাবক, আগামী শুক্রবার সকাল ১০টায় অভিভাবক সভা অনুষ্ঠিত হবে। আপনার উপস্থিতি একান্ত কাম্য। ধন্যবাদ।</p>
                                        <button type="button" class="btn btn-sm btn-outline-primary use-template" data-template="parent_meeting">
                                            <i class="fas fa-plus-circle me-2"></i>ব্যবহার করুন
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Show/hide custom prompt based on message type
            $('#message_type').change(function() {
                if ($(this).val() === 'custom') {
                    $('#customPromptContainer').show();
                } else {
                    $('#customPromptContainer').hide();
                }
            });

            // Character count for SMS
            $('#generated_message').on('input', function() {
                var charCount = $(this).val().length;
                $('#character_count').text(charCount + '/160 অক্ষর');

                if (charCount > 160) {
                    $('#character_count').addClass('text-danger');
                } else {
                    $('#character_count').removeClass('text-danger');
                }
            });

            // Trigger character count on page load
            $('#generated_message').trigger('input');

            // Use template buttons
            $('.use-template').click(function() {
                var template = $(this).data('template');
                $('#message_type').val(template);
                $('#generateForm').submit();
            });

            // Phone number validation
            $('#sendForm').submit(function(e) {
                var phoneNumber = $('#phone_number').val().trim();
                var phonePattern = /^(01[3-9]\d{8}|8801[3-9]\d{8})$/;

                if (!phonePattern.test(phoneNumber)) {
                    e.preventDefault();
                    $('#phone_number').addClass('is-invalid');
                    return false;
                } else {
                    $('#phone_number').removeClass('is-invalid');
                }
            });

            // Phone number format helper
            $('#phone_number').on('input', function() {
                var phoneNumber = $(this).val().trim();
                var phonePattern = /^(01[3-9]\d{8}|8801[3-9]\d{8})$/;

                if (phonePattern.test(phoneNumber)) {
                    $(this).removeClass('is-invalid').addClass('is-valid');
                } else {
                    $(this).removeClass('is-valid');
                    if (phoneNumber.length >= 11) {
                        $(this).addClass('is-invalid');
                    }
                }
            });
        });
    </script>
</body>
</html>
