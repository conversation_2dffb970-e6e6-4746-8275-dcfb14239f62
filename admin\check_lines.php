<?php
// Read the file
$content = file_get_contents('detailed_marks_entry.php');
$lines = explode("\n", $content);

// Check specific range around line 1040
$start = 1030;
$end = 1050;

echo "Lines $start to $end:\n";
for ($i = $start - 1; $i < min($end, count($lines)); $i++) {
    echo ($i + 1) . ": " . $lines[$i] . "\n";
}

// Count total lines
echo "\nTotal lines in file: " . count($lines) . "\n";
?> 