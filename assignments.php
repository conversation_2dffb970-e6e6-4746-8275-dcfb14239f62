<?php
session_start();
require_once 'includes/dbh.inc.php';

// If user is logged in as a student, redirect to student assignments page
if (isset($_SESSION['userId']) && $_SESSION['userType'] === 'student') {
    header("Location: student/assignments.php");
    exit();
}

// Get all assignments for public viewing
$assignmentsQuery = "SELECT a.*, s.subject_name, c.class_name, d.department_name
                    FROM assignments a
                    JOIN subjects s ON a.subject_id = s.id
                    JOIN classes c ON a.class_id = c.id
                    LEFT JOIN departments d ON a.department_id = d.id
                    ORDER BY a.due_date ASC";

// Check if assignments table exists
$tableExists = false;
$assignments = null;
$checkTable = $conn->query("SHOW TABLES LIKE 'assignments'");
if ($checkTable->num_rows > 0) {
    $tableExists = true;
    $assignments = $conn->query($assignmentsQuery);
}

// Get classes for filter
$classesQuery = "SELECT id, class_name FROM classes ORDER BY class_name";
$classes = $conn->query($classesQuery);

// Get subjects for filter
$subjectsQuery = "SELECT id, subject_name FROM subjects ORDER BY subject_name";
$subjects = $conn->query($subjectsQuery);

// Handle filters
$filterApplied = false;
$filteredAssignments = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['filter_assignments'])) {
    $filterApplied = true;
    $filterClass = $_POST['class_id'] ?? '';
    $filterSubject = $_POST['subject_id'] ?? '';
    $filterDueDate = $_POST['due_date'] ?? '';

    $filteredAssignmentsQuery = "SELECT a.*, s.subject_name, c.class_name, d.department_name
                               FROM assignments a
                               JOIN subjects s ON a.subject_id = s.id
                               JOIN classes c ON a.class_id = c.id
                               LEFT JOIN departments d ON a.department_id = d.id
                               WHERE 1=1";

    if (!empty($filterClass)) {
        $filteredAssignmentsQuery .= " AND a.class_id = '$filterClass'";
    }

    if (!empty($filterSubject)) {
        $filteredAssignmentsQuery .= " AND a.subject_id = '$filterSubject'";
    }

    if (!empty($filterDueDate)) {
        $filteredAssignmentsQuery .= " AND a.due_date = '$filterDueDate'";
    }

    $filteredAssignmentsQuery .= " ORDER BY a.due_date ASC";
    $filteredAssignments = $conn->query($filteredAssignmentsQuery);
}

// Count assignments by status
$upcomingCount = 0;
$overdueCount = 0;
$totalCount = 0;

if ($tableExists && ($assignments && $assignments->num_rows > 0)) {
    $totalCount = $assignments->num_rows;

    // Reset the pointer
    $assignments->data_seek(0);

    // Count upcoming and overdue assignments
    while ($assignment = $assignments->fetch_assoc()) {
        if (strtotime($assignment['due_date']) >= time()) {
            $upcomingCount++;
        } else {
            $overdueCount++;
        }
    }

    // Reset the pointer again
    $assignments->data_seek(0);
}

// Set page title
$page_title = "অ্যাসাইনমেন্ট";
$school_name = "নিশাত এডুকেশন সেন্টার";
$school_address = "চুয়াডাঙ্গা, বাংলাদেশ";
$school_logo = "img/logo.jpg";
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title><?php echo $page_title; ?> - <?php echo $school_name; ?></title>

    <!-- Bootstrap CSS -->
    

    <!-- Custom Fonts CSS -->
    

    <!-- Custom CSS -->
    

    <style>
        :root {
            --primary-color: #006A4E; /* Deep Green */
            --secondary-color: #00563B; /* Darker Green */
            --accent-color: #F39C12; /* Amber/Gold */
            --dark-color: #2C3E50; /* Dark Blue-Gray */
            --light-color: #F5F5F5; /* Off-White */
            --text-color: #333333; /* Dark Gray */
            --light-text: #FFFFFF; /* White */
            --highlight-color: #E74C3C; /* Red Accent */
            --soft-color: #E3F2FD; /* Soft Blue */
        }

        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: var(--light-color);
            color: var(--text-color);
        }

        /* Header Styles */
        .header-top {
            background-color: white;
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .school-logo {
            width: 80px;
            height: 80px;
            object-fit: contain;
        }

        .school-title {
            color: var(--primary-color);
            font-weight: 700;
            font-size: 1.8rem;
        }

        .school-subtitle {
            color: var(--dark-color);
            font-weight: 500;
        }

        /* Navigation Styles */
        .main-nav {
            background-color: var(--primary-color);
            padding: 0;
        }

        .main-nav .nav-link {
            color: var(--light-text);
            font-weight: 500;
            padding: 15px 20px;
            transition: all 0.3s;
            border-radius: 0;
        }

        .main-nav .nav-link:hover,
        .main-nav .nav-link.active {
            background-color: var(--secondary-color);
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 60px 0;
            margin-bottom: 30px;
        }

        .hero-title {
            font-weight: 700;
            margin-bottom: 20px;
            color: white;
        }

        .hero-text {
            font-size: 1.1rem;
            margin-bottom: 30px;
            opacity: 0.9;
            color: white;
        }

        /* Card Styles */
        .card {
            border: none;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            height: 100%;
            background-color: white;
            margin-bottom: 20px;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .card-header {
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
            padding: 15px 20px;
            border: none;
        }

        .card-header.warning-header {
            background-color: var(--accent-color);
            color: var(--dark-color);
        }

        .card-body {
            padding: 25px;
        }

        /* Assignment Card Styles */
        .assignment-card {
            transition: all 0.3s ease;
            border-left: 5px solid #006A4E;
        }

        .assignment-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .due-date {
            font-size: 0.9rem;
            color: #6c757d;
        }

        .text-danger {
            color: #dc3545 !important;
            font-weight: bold;
        }

        /* Footer Styles */
        .footer {
            background-color: var(--dark-color);
            color: white;
            padding: 40px 0 20px;
            margin-top: 50px;
        }

        .footer h5 {
            color: var(--light-color);
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--accent-color);
            display: inline-block;
        }

        .footer-links {
            list-style: none;
            padding: 0;
        }

        .footer-links li {
            margin-bottom: 10px;
        }

        .footer-links a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s;
        }

        .footer-links a:hover {
            color: white;
            padding-left: 5px;
        }

        .social-icons a {
            display: inline-block;
            width: 36px;
            height: 36px;
            background-color: rgba(255,255,255,0.1);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 36px;
            margin-right: 10px;
            transition: all 0.3s;
        }

        .social-icons a:hover {
            background-color: var(--accent-color);
            transform: translateY(-3px);
        }

        .copyright {
            background-color: rgba(0,0,0,0.2);
            padding: 15px 0;
            margin-top: 30px;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .school-title {
                font-size: 1.4rem;
            }

            .school-subtitle {
                font-size: 1rem;
            }

            .hero-section {
                padding: 40px 0;
            }

            .hero-img {
                margin-top: 30px;
            }
        }
    </style>
</head>
<body>
    <!-- Header Top with Logo and Title -->
    <div class="header-top">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-2 text-center text-md-start mb-3 mb-md-0">
                    <img src="<?php echo $school_logo; ?>" alt="School Logo" class="school-logo" onerror="this.src='https://via.placeholder.com/80?text=ZFAW'">
                </div>
                <div class="col-md-10 text-center text-md-start">
                    <h1 class="school-title mb-1"><?php echo $school_name; ?></h1>
                    <h2 class="school-subtitle fs-5"><?php echo $school_address; ?></h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Navigation -->
    <nav class="main-nav">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <ul class="nav">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php"><i class="fas fa-home me-1"></i> হোম</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="subjects.php"><i class="fas fa-book me-1"></i> বিষয়সমূহ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="teachers.php"><i class="fas fa-chalkboard-teacher me-1"></i> শিক্ষকবৃন্দ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="students.php"><i class="fas fa-user-graduate me-1"></i> শিক্ষার্থীবৃন্দ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="notices.php"><i class="fas fa-bullhorn me-1"></i> নোটিশ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="assignments.php"><i class="fas fa-tasks me-1"></i> অ্যাসাইনমেন্ট</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="exams.php"><i class="fas fa-file-alt me-1"></i> পরীক্ষা</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="results.php"><i class="fas fa-chart-bar me-1"></i> ফলাফল</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="hero-title">অ্যাসাইনমেন্ট</h1>
                <p class="hero-text">আমাদের প্রতিষ্ঠানের অ্যাসাইনমেন্টসমূহ এখানে দেখতে পারবেন। নির্ধারিত সময়ের মধ্যে অ্যাসাইনমেন্ট জমা দিন।</p>
                <a href="index.php" class="btn btn-lg" style="background-color: #00a65a; color: white; font-weight: 500; box-shadow: 0 4px 15px rgba(0,0,0,0.2); transition: all 0.3s ease;">হোমপেজে ফিরুন <i class="fas fa-arrow-right ms-2"></i></a>
            </div>
            <div class="col-lg-6 text-center">
                <img src="img/assignment.svg" alt="Assignment" class="img-fluid hero-img" style="max-width: 400px;" onerror="this.src='https://via.placeholder.com/400x300?text=Assignment'">
            </div>
        </div>
    </div>
</section>

<!-- Login Notice for Students -->
<section class="container mt-4">
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i> শিক্ষার্থীরা অ্যাসাইনমেন্ট জমা দিতে <a href="login.php" class="alert-link">লগইন</a> করুন।
    </div>
</section>

<!-- Main Content Section -->
<section class="container mt-4 mb-5">
    <div class="row">
        <!-- Main Column - Assignments List -->
        <div class="col-md-12">
            <!-- Filter Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-header text-white" style="background-color: #00a65a;">
                    <h5 class="mb-0" style="color: #ffffff;"><i class="fas fa-filter me-2"></i>অ্যাসাইনমেন্ট ফিল্টার</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="" class="row g-3">
                        <div class="col-md-3 mb-3">
                            <label for="class_id" class="form-label">শ্রেণী</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-graduation-cap"></i></span>
                                <select name="class_id" id="class_id" class="form-select">
                                    <option value="">সকল শ্রেণী</option>
                                    <?php if ($classes && $classes->num_rows > 0): ?>
                                        <?php while ($class = $classes->fetch_assoc()): ?>
                                            <option value="<?php echo $class['id']; ?>"><?php echo $class['class_name']; ?></option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="subject_id" class="form-label">বিষয়</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-book"></i></span>
                                <select name="subject_id" id="subject_id" class="form-select">
                                    <option value="">সকল বিষয়</option>
                                    <?php if ($subjects && $subjects->num_rows > 0): ?>
                                        <?php while ($subject = $subjects->fetch_assoc()): ?>
                                            <option value="<?php echo $subject['id']; ?>"><?php echo $subject['subject_name']; ?></option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="due_date" class="form-label">শেষ তারিখ</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                                <input type="date" name="due_date" id="due_date" class="form-control">
                            </div>
                        </div>
                        <div class="col-md-3 mb-3 d-flex align-items-end">
                            <div class="d-grid gap-2 w-100">
                                <button type="submit" name="filter_assignments" class="btn" style="background-color: #00a65a; color: white;">
                                    <i class="fas fa-filter me-2"></i> ফিল্টার করুন
                                </button>
                                <a href="assignments.php" class="btn btn-secondary">
                                    <i class="fas fa-sync-alt me-2"></i> রিসেট করুন
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <!-- Main Content Card with Tabs -->
            <div class="card shadow-sm">
                <div class="card-header text-white" style="background-color: #00a65a;">
                    <ul class="nav nav-tabs card-header-tabs" id="assignmentTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active text-dark" id="assignments-tab" data-bs-toggle="tab" data-bs-target="#assignments" type="button" role="tab" aria-controls="assignments" aria-selected="true">
                                <i class="fas fa-tasks me-2"></i>অ্যাসাইনমেন্ট তালিকা
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link text-dark" id="instructions-tab" data-bs-toggle="tab" data-bs-target="#instructions" type="button" role="tab" aria-controls="instructions" aria-selected="false">
                                <i class="fas fa-info-circle me-2"></i>নির্দেশনা
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="assignmentTabsContent">
                        <!-- Assignments Tab -->
                        <div class="tab-pane fade show active" id="assignments" role="tabpanel" aria-labelledby="assignments-tab">
                            <?php
                            $assignmentsList = $filterApplied ? $filteredAssignments : $assignments;
                            if ($tableExists && $assignmentsList && $assignmentsList->num_rows > 0):
                            ?>
                                <div class="row">
                                <?php while ($assignment = $assignmentsList->fetch_assoc()): ?>
                                    <div class="col-md-12 mb-4">
                                        <div class="card assignment-card h-100">
                                            <div class="card-header bg-light">
                                                <h5 class="card-title mb-0"><?php echo htmlspecialchars($assignment['title']); ?></h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <h6 class="card-subtitle mb-2 text-muted">
                                                            <?php echo htmlspecialchars($assignment['subject_name']); ?> |
                                                            <?php echo htmlspecialchars($assignment['class_name']); ?>
                                                            <?php if (!empty($assignment['department_name'])): ?>
                                                                | <?php echo htmlspecialchars($assignment['department_name']); ?>
                                                            <?php endif; ?>
                                                        </h6>
                                                        <p class="card-text"><?php echo nl2br(htmlspecialchars(substr($assignment['description'], 0, 150))); ?>...</p>
                                                    </div>
                                                    <div class="col-md-4 text-center">
                                                        <div class="due-date-box p-3 rounded" style="background-color: #f8f9fa;">
                                                            <h6 class="<?php echo (strtotime($assignment['due_date']) < time()) ? 'text-danger' : ''; ?>">শেষ তারিখ</h6>
                                                            <h4 class="<?php echo (strtotime($assignment['due_date']) < time()) ? 'text-danger' : ''; ?>"><?php echo date('d F, Y', strtotime($assignment['due_date'])); ?></h4>
                                                            <?php if (strtotime($assignment['due_date']) < time()): ?>
                                                                <span class="badge bg-danger">সময় শেষ</span>
                                                            <?php else: ?>
                                                                <span class="badge" style="background-color: #00a65a;">বাকি আছে</span>
                                                            <?php endif; ?>
                                                        </div>

                                                        <div class="mt-3">
                                                            <a href="login.php" class="btn" style="background-color: #00a65a; color: white;">
                                                                <i class="fas fa-sign-in-alt me-2"></i> জমা দিতে লগইন করুন
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endwhile; ?>
                                </div>
                            <?php elseif (!$tableExists): ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i> অ্যাসাইনমেন্ট সিস্টেম এখনও সেটআপ করা হয়নি।
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i> বর্তমানে কোন অ্যাসাইনমেন্ট নেই।
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Instructions Tab -->
                        <div class="tab-pane fade" id="instructions" role="tabpanel" aria-labelledby="instructions-tab">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5 class="mb-3"><i class="fas fa-info-circle me-2"></i> অ্যাসাইনমেন্ট জমা দেওয়ার নিয়মাবলী:</h5>
                                    <div class="card mb-4">
                                        <div class="card-body">
                                            <ul class="list-group list-group-flush">
                                                <li class="list-group-item"><i class="fas fa-check-circle text-success me-2"></i> অ্যাসাইনমেন্ট জমা দেওয়ার জন্য শিক্ষার্থীদের অবশ্যই লগইন করতে হবে।</li>
                                                <li class="list-group-item"><i class="fas fa-check-circle text-success me-2"></i> অ্যাসাইনমেন্ট শেষ তারিখের মধ্যে জমা দিতে হবে।</li>
                                                <li class="list-group-item"><i class="fas fa-check-circle text-success me-2"></i> অ্যাসাইনমেন্ট জমা দেওয়ার পর সংশোধন করা যাবে না।</li>
                                                <li class="list-group-item"><i class="fas fa-check-circle text-success me-2"></i> অ্যাসাইনমেন্ট জমা দেওয়ার সময় ফাইল আপলোড করা যাবে।</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h5 class="mb-3"><i class="fas fa-question-circle me-2"></i> প্রায়শই জিজ্ঞাসিত প্রশ্ন:</h5>
                                    <div class="accordion" id="assignmentFAQ">
                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="headingOne">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="false" aria-controls="collapseOne">
                                                    অ্যাসাইনমেন্ট কিভাবে জমা দিব?
                                                </button>
                                            </h2>
                                            <div id="collapseOne" class="accordion-collapse collapse" aria-labelledby="headingOne" data-bs-parent="#assignmentFAQ">
                                                <div class="accordion-body">
                                                    অ্যাসাইনমেন্ট জমা দিতে প্রথমে লগইন করুন। তারপর অ্যাসাইনমেন্ট পেজে গিয়ে "জমা দিন" বাটনে ক্লিক করুন।
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="headingTwo">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                                    শেষ তারিখ পার হয়ে গেলে কি অ্যাসাইনমেন্ট জমা দেওয়া যাবে?
                                                </button>
                                            </h2>
                                            <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#assignmentFAQ">
                                                <div class="accordion-body">
                                                    শেষ তারিখ পার হয়ে গেলে সাধারণত অ্যাসাইনমেন্ট জমা দেওয়া যায় না। বিশেষ ক্ষেত্রে শিক্ষকের অনুমতি নিয়ে জমা দেওয়া যেতে পারে।
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="headingThree">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                                    অ্যাসাইনমেন্টের ফাইল কি ফরম্যাটে আপলোড করতে হবে?
                                                </button>
                                            </h2>
                                            <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#assignmentFAQ">
                                                <div class="accordion-body">
                                                    অ্যাসাইনমেন্ট PDF, DOC, DOCX, JPG, PNG ফরম্যাটে আপলোড করতে পারবেন। ফাইলের সাইজ সর্বোচ্চ 10MB হতে হবে।
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Back to Top Button -->
<a href="#" class="btn back-to-top position-fixed bottom-0 end-0 m-4 rounded-circle" style="width: 45px; height: 45px; line-height: 45px; display: none; background-color: #F39C12; color: #2C3E50; z-index: 1000;">
    <i class="fas fa-arrow-up"></i>
</a>

<!-- Footer Section -->
<footer class="footer">
    <div class="container">
        <div class="row">
            <div class="col-md-4 mb-4 mb-md-0">
                <h5>আমাদের সম্পর্কে</h5>
                <p class="text-light opacity-75">নিশাত এডুকেশন সেন্টার একটি আধুনিক শিক্ষা প্রতিষ্ঠান যা উচ্চমানের শিক্ষা প্রদানের জন্য প্রতিশ্রুতিবদ্ধ।</p>
                <div class="social-icons mt-3">
                    <a href="#"><i class="fab fa-facebook-f"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                    <a href="#"><i class="fab fa-youtube"></i></a>
                </div>
            </div>
            <div class="col-md-4 mb-4 mb-md-0">
                <h5>দ্রুত লিঙ্ক</h5>
                <ul class="footer-links">
                    <li><a href="index.php"><i class="fas fa-angle-right me-2"></i> হোম</a></li>
                    <li><a href="about.php"><i class="fas fa-angle-right me-2"></i> আমাদের সম্পর্কে</a></li>
                    <li><a href="teachers.php"><i class="fas fa-angle-right me-2"></i> শিক্ষকবৃন্দ</a></li>
                    <li><a href="notices.php"><i class="fas fa-angle-right me-2"></i> নোটিশ</a></li>
                    <li><a href="contact.php"><i class="fas fa-angle-right me-2"></i> যোগাযোগ</a></li>
                </ul>
            </div>
            <div class="col-md-4">
                <h5>যোগাযোগ করুন</h5>
                <ul class="footer-links">
                    <li><i class="fas fa-map-marker-alt me-2"></i> চুয়াডাঙ্গা, বাংলাদেশ</li>
                    <li><i class="fas fa-phone me-2"></i> +880 1234-567890</li>
                    <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                    <li><i class="fas fa-clock me-2"></i> সকাল ৯টা - বিকাল ৫টা</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="copyright text-center">
        <div class="container">
            <p class="mb-0">&copy; <?php echo date('Y'); ?> নিশাত এডুকেশন সেন্টার - সর্বসত্ত্ব সংরক্ষিত</p>
        </div>
    </div>
</footer>

<style>
    .assignment-card {
        transition: all 0.3s ease;
        border-left: 5px solid #006A4E;
    }

    .assignment-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .due-date {
        font-size: 0.9rem;
        color: #6c757d;
    }

    .text-danger {
        color: #dc3545 !important;
        font-weight: bold;
    }

    .due-date-box {
        border: 1px solid #eee;
        transition: all 0.3s ease;
    }

    .due-date-box:hover {
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
</style>

<!-- Custom JavaScript -->
<script>
    // Back to top button
    window.addEventListener('scroll', function() {
        var backToTopBtn = document.querySelector('.back-to-top');
        if (backToTopBtn) {
            if (window.pageYOffset > 300) {
                backToTopBtn.style.display = 'flex';
                backToTopBtn.style.justifyContent = 'center';
                backToTopBtn.style.alignItems = 'center';
            } else {
                backToTopBtn.style.display = 'none';
            }
        }
    });
</script>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
