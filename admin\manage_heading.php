<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Initialize variables
$success_message = '';
$error_message = '';

// Check if school_settings table exists
$tableCheck = $conn->query("SHOW TABLES LIKE 'school_settings'");
if ($tableCheck->num_rows == 0) {
    // Create school_settings table if it doesn't exist
    $createTableQuery = "CREATE TABLE IF NOT EXISTS school_settings (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) NOT NULL UNIQUE,
        setting_value TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";

    if (!$conn->query($createTableQuery)) {
        $error_message = "Error creating settings table: " . $conn->error;
    }
} else {
    // Check if the table has the correct structure (setting_key column)
    $columnCheck = $conn->query("SHOW COLUMNS FROM school_settings LIKE 'setting_key'");
    if ($columnCheck->num_rows == 0) {
        // The table exists but has the old structure, alter it
        $alterTableQuery = "ALTER TABLE school_settings
            ADD COLUMN setting_key VARCHAR(100) NOT NULL UNIQUE AFTER id,
            ADD COLUMN setting_value TEXT NOT NULL AFTER setting_key,
            MODIFY COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP";

        if (!$conn->query($alterTableQuery)) {
            $error_message = "Error updating settings table structure: " . $conn->error;
        } else {
            // Migrate existing data if any
            $migrateDataQuery = "UPDATE school_settings SET
                setting_key = 'school_name' WHERE id = 1";
            $conn->query($migrateDataQuery);
        }
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize and validate input
    $school_name = isset($_POST['school_name']) ? trim($conn->real_escape_string($_POST['school_name'])) : '';
    $school_address = isset($_POST['school_address']) ? trim($conn->real_escape_string($_POST['school_address'])) : '';
    $attendance_title = isset($_POST['attendance_title']) ? trim($conn->real_escape_string($_POST['attendance_title'])) : '';

    // Process logo upload if a file was submitted
    $logo_path = '';
    if (isset($_FILES['school_logo']) && $_FILES['school_logo']['error'] == 0) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $max_size = 2 * 1024 * 1024; // 2MB

        if (!in_array($_FILES['school_logo']['type'], $allowed_types)) {
            $error_message = "Invalid file type. Only JPG, PNG and GIF are allowed.";
        } elseif ($_FILES['school_logo']['size'] > $max_size) {
            $error_message = "File size too large. Maximum size is 2MB.";
        } else {
            // Create uploads directory if it doesn't exist
            $upload_dir = '../uploads/signatures/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }

            // Save the file
            $logo_path = 'institution_logo.png';
            $upload_path = $upload_dir . $logo_path;

            if (move_uploaded_file($_FILES['school_logo']['tmp_name'], $upload_path)) {
                // File uploaded successfully
            } else {
                $error_message = "Failed to upload logo. Please try again.";
            }
        }
    }

    if (empty($error_message)) {
        // Update or insert settings
        $settings = [
            'school_name' => $school_name,
            'school_address' => $school_address,
            'attendance_title' => $attendance_title
        ];

        foreach ($settings as $key => $value) {
            if (empty($value)) continue;

            // Check if setting exists
            $checkQuery = "SELECT id FROM school_settings WHERE setting_key = '$key'";
            $result = $conn->query($checkQuery);

            if ($result->num_rows > 0) {
                // Update existing setting
                $updateQuery = "UPDATE school_settings SET setting_value = '$value' WHERE setting_key = '$key'";
                if (!$conn->query($updateQuery)) {
                    $error_message = "Error updating $key: " . $conn->error;
                    break;
                }
            } else {
                // Insert new setting
                $insertQuery = "INSERT INTO school_settings (setting_key, setting_value) VALUES ('$key', '$value')";
                if (!$conn->query($insertQuery)) {
                    $error_message = "Error inserting $key: " . $conn->error;
                    break;
                }
            }
        }

        if (empty($error_message)) {
            $success_message = "Settings updated successfully!";
        }
    }
}

// Get current settings
$settings = [
    'school_name' => 'স্কুল ম্যানেজমেন্ট সিস্টেম',
    'school_address' => 'বাংলাদেশ',
    'attendance_title' => 'পরীক্ষায় শিক্ষার্থীর হাজিরা পত্র'
];

$query = "SELECT setting_key, setting_value FROM school_settings WHERE setting_key IN ('school_name', 'school_address', 'attendance_title')";
$result = $conn->query($query);

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
}

// Check if logo exists
$logo_exists = file_exists('../uploads/signatures/institution_logo.png');
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>হেডিং ম্যানেজমেন্ট - অ্যাডমিন প্যানেল</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/admin-style.css">
    <!-- Google Fonts - Hind Siliguri for Bengali -->
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Animate.css for animations -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <style>
        /* Global Font Settings */
        body, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, select, textarea, label, li, table, th, td {
            font-family: 'Hind Siliguri', sans-serif !important;
        }

        /* Card Styling */
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
        }

        .card-header {
            background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
            color: white;
            border-bottom: none;
            padding: 20px;
            border-radius: 15px 15px 0 0 !important;
        }

        .card-header h5 {
            margin: 0;
            font-weight: 600;
            font-size: 1.25rem;
        }

        .card-body {
            padding: 25px;
        }

        /* Form Controls */
        .form-control, .form-select {
            border-radius: 10px;
            padding: 12px 15px;
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            box-shadow: 0 0 0 3px rgba(75, 108, 183, 0.15);
            border-color: #4b6cb7;
        }

        .form-label {
            font-weight: 500;
            margin-bottom: 8px;
            color: #333;
        }

        /* Button Styling */
        .btn-primary {
            background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(75, 108, 183, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(75, 108, 183, 0.4);
            background: linear-gradient(135deg, #5d7ec9 0%, #263b6a 100%);
        }

        /* Preview Container */
        .preview-container {
            border: none;
            padding: 30px;
            margin-top: 20px;
            border-radius: 15px;
            background-color: white;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .preview-container:hover {
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
        }

        .preview-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #4b6cb7, #182848);
        }

        .logo-preview {
            max-width: 100px;
            max-height: 100px;
            margin-bottom: 10px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .logo-preview:hover {
            transform: scale(1.05);
        }

        .preview-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
            color: #182848;
            transition: all 0.3s ease;
        }

        .preview-address {
            font-size: 15px;
            color: #6c757d;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .preview-attendance-title {
            font-size: 18px;
            font-weight: 600;
            padding-bottom: 8px;
            border-bottom: 3px solid #4b6cb7;
            display: inline-block;
            color: #333;
            transition: all 0.3s ease;
        }

        /* Alert Styling */
        .alert {
            border-radius: 10px;
            border: none;
            padding: 15px 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
        }

        /* File Input Styling */
        .form-control[type="file"] {
            padding: 10px;
            background-color: #f8f9fa;
        }

        .form-control[type="file"]:hover {
            background-color: #e9ecef;
        }

        /* Image Thumbnail */
        .img-thumbnail {
            border-radius: 10px;
            border: 2px solid #e0e0e0;
            transition: all 0.3s ease;
        }

        .img-thumbnail:hover {
            border-color: #4b6cb7;
            transform: scale(1.05);
        }

        /* Info Alert */
        .alert-info {
            background-color: #e1f5fe;
            color: #0c5460;
            border-left: 5px solid #4b6cb7;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <?php include('includes/sidebar.php'); ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-4 pb-3 mb-4 border-bottom">
                    <h1 class="h2 animate__animated animate__fadeInLeft">
                        <i class="fas fa-heading me-2 text-primary"></i> হেডিং ম্যানেজমেন্ট
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0 animate__animated animate__fadeInRight">
                        <div class="btn-group me-2">
                            <a href="index.php" class="btn btn-outline-primary">
                                <i class="fas fa-tachometer-alt me-1"></i> ড্যাশবোর্ড
                            </a>
                        </div>
                    </div>
                </div>

                <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
                    <i class="fas fa-check-circle me-2"></i> <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-6 animate__animated animate__fadeInLeft">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-cog me-2"></i>হেডিং সেটিংস</h5>
                            </div>
                            <div class="card-body">
                                <form action="" method="POST" enctype="multipart/form-data">
                                    <div class="mb-4">
                                        <label for="school_name" class="form-label">
                                            <i class="fas fa-school me-1 text-primary"></i> প্রতিষ্ঠানের নাম
                                        </label>
                                        <input type="text" class="form-control" id="school_name" name="school_name"
                                            value="<?php echo htmlspecialchars($settings['school_name']); ?>"
                                            placeholder="আপনার প্রতিষ্ঠানের নাম লিখুন">
                                    </div>

                                    <div class="mb-4">
                                        <label for="school_address" class="form-label">
                                            <i class="fas fa-map-marker-alt me-1 text-primary"></i> প্রতিষ্ঠানের ঠিকানা
                                        </label>
                                        <input type="text" class="form-control" id="school_address" name="school_address"
                                            value="<?php echo htmlspecialchars($settings['school_address']); ?>"
                                            placeholder="আপনার প্রতিষ্ঠানের ঠিকানা লিখুন">
                                    </div>

                                    <div class="mb-4">
                                        <label for="attendance_title" class="form-label">
                                            <i class="fas fa-clipboard-list me-1 text-primary"></i> হাজিরা শীট টাইটেল
                                        </label>
                                        <input type="text" class="form-control" id="attendance_title" name="attendance_title"
                                            value="<?php echo htmlspecialchars($settings['attendance_title']); ?>"
                                            placeholder="হাজিরা শীটের শিরোনাম লিখুন">
                                    </div>

                                    <div class="mb-4">
                                        <label for="school_logo" class="form-label">
                                            <i class="fas fa-image me-1 text-primary"></i> প্রতিষ্ঠানের লোগো
                                        </label>
                                        <input type="file" class="form-control" id="school_logo" name="school_logo">
                                        <div class="form-text mt-1">
                                            <i class="fas fa-info-circle me-1"></i> সর্বোচ্চ ফাইল সাইজ: 2MB. অনুমোদিত ফরম্যাট: JPG, PNG, GIF
                                        </div>
                                        <?php if ($logo_exists): ?>
                                        <div class="mt-3 text-center">
                                            <p class="mb-2 text-muted"><small>বর্তমান লোগো</small></p>
                                            <img src="../uploads/signatures/institution_logo.png?v=<?php echo time(); ?>"
                                                alt="বর্তমান লোগো" class="img-thumbnail" style="max-width: 100px;">
                                        </div>
                                        <?php endif; ?>
                                    </div>

                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-save me-2"></i> সেটিংস সংরক্ষণ করুন
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 animate__animated animate__fadeInRight">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-eye me-2"></i>প্রিভিউ</h5>
                            </div>
                            <div class="card-body">
                                <div class="preview-container">
                                    <div class="row align-items-center">
                                        <div class="col-3 text-start">
                                            <?php if ($logo_exists): ?>
                                            <img src="../uploads/signatures/institution_logo.png?v=<?php echo time(); ?>"
                                                alt="প্রতিষ্ঠানের লোগো" class="logo-preview">
                                            <?php else: ?>
                                            <div class="text-center text-muted">
                                                <i class="fas fa-image fa-3x mb-2"></i>
                                                <p><small>লোগো আপলোড করুন</small></p>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="col-6 text-center">
                                            <div class="preview-title"><?php echo htmlspecialchars($settings['school_name']); ?></div>
                                            <div class="preview-address"><?php echo htmlspecialchars($settings['school_address']); ?></div>
                                            <div class="preview-attendance-title"><?php echo htmlspecialchars($settings['attendance_title']); ?></div>
                                        </div>
                                        <div class="col-3">
                                            <!-- Right column empty -->
                                        </div>
                                    </div>
                                </div>

                                <div class="alert alert-info mt-4">
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <i class="fas fa-info-circle fa-2x"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-1 fw-bold">সিস্টেম হেডিং</h6>
                                            <p class="mb-0">এই সেটিংস পরিবর্তন করলে সিস্টেমের সকল হেডিং আপডেট হবে। আপনার পরিবর্তন সকল পেজে দেখা যাবে।</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- SweetAlert2 for better alerts -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // Preview functionality with enhanced effects
        document.addEventListener('DOMContentLoaded', function() {
            const schoolNameInput = document.getElementById('school_name');
            const schoolAddressInput = document.getElementById('school_address');
            const attendanceTitleInput = document.getElementById('attendance_title');
            const logoInput = document.getElementById('school_logo');

            const previewTitle = document.querySelector('.preview-title');
            const previewAddress = document.querySelector('.preview-address');
            const previewAttendanceTitle = document.querySelector('.preview-attendance-title');
            const previewContainer = document.querySelector('.preview-container');

            // Update preview on input change with animation
            function updateWithAnimation(element, value) {
                // Add fade out animation
                element.style.opacity = '0';
                element.style.transform = 'translateY(-10px)';
                element.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

                // Update content after a short delay
                setTimeout(() => {
                    element.textContent = value;
                    // Add fade in animation
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, 300);
            }

            // Update preview on input change
            schoolNameInput.addEventListener('input', function() {
                updateWithAnimation(previewTitle, this.value);
            });

            schoolAddressInput.addEventListener('input', function() {
                updateWithAnimation(previewAddress, this.value);
            });

            attendanceTitleInput.addEventListener('input', function() {
                updateWithAnimation(previewAttendanceTitle, this.value);
            });

            // Preview logo with enhanced effects
            logoInput.addEventListener('change', function() {
                const file = this.files[0];
                if (file) {
                    // Validate file type
                    const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
                    if (!validTypes.includes(file.type)) {
                        Swal.fire({
                            title: 'ফাইল টাইপ সমস্যা!',
                            text: 'শুধুমাত্র JPG, PNG, এবং GIF ফাইল আপলোড করা যাবে।',
                            icon: 'error',
                            confirmButtonText: 'ঠিক আছে'
                        });
                        return;
                    }

                    // Validate file size (max 2MB)
                    if (file.size > 2 * 1024 * 1024) {
                        Swal.fire({
                            title: 'ফাইল সাইজ বেশি!',
                            text: 'সর্বোচ্চ ফাইল সাইজ 2MB।',
                            icon: 'error',
                            confirmButtonText: 'ঠিক আছে'
                        });
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const previewContainer = document.querySelector('.col-3.text-start');

                        // Clear the container first
                        previewContainer.innerHTML = '';

                        // Create new image with animation
                        const previewImg = document.createElement('img');
                        previewImg.className = 'logo-preview animate__animated animate__fadeIn';
                        previewImg.src = e.target.result;
                        previewImg.alt = 'প্রতিষ্ঠানের লোগো';
                        previewContainer.appendChild(previewImg);
                    };
                    reader.readAsDataURL(file);
                }
            });

            // Add hover effect to preview container
            previewContainer.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.02)';
            });

            previewContainer.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });

            // Form submission with validation
            const form = document.querySelector('form');
            form.addEventListener('submit', function(e) {
                if (!schoolNameInput.value.trim()) {
                    e.preventDefault();
                    Swal.fire({
                        title: 'তথ্য প্রয়োজন!',
                        text: 'প্রতিষ্ঠানের নাম অবশ্যই দিতে হবে।',
                        icon: 'warning',
                        confirmButtonText: 'ঠিক আছে'
                    });
                }
            });
        });
    </script>
</body>
</html>
