<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'includes/dbh.inc.php';

echo "<h1>Database Tables and Data Check</h1>";

// Check sessions table
echo "<h2>Sessions Table</h2>";
$result = $conn->query("SELECT * FROM sessions ORDER BY id");
if ($result && $result->num_rows > 0) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Session Name</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr><td>{$row['id']}</td><td>{$row['session_name']}</td></tr>";
    }
    echo "</table>";
} else {
    echo "No sessions found or table doesn't exist.";
}

// Check departments table
echo "<h2>Departments Table</h2>";
$result = $conn->query("SELECT * FROM departments ORDER BY id");
if ($result && $result->num_rows > 0) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Department Name</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr><td>{$row['id']}</td><td>{$row['department_name']}</td></tr>";
    }
    echo "</table>";
} else {
    echo "No departments found or table doesn't exist.";
}

// Check classes table
echo "<h2>Classes Table</h2>";
$result = $conn->query("SELECT * FROM classes ORDER BY id");
if ($result && $result->num_rows > 0) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Class Name</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr><td>{$row['id']}</td><td>{$row['class_name']}</td></tr>";
    }
    echo "</table>";
} else {
    echo "No classes found or table doesn't exist.";
}

// Check students table structure
echo "<h2>Students Table Structure</h2>";
$result = $conn->query("DESCRIBE students");
if ($result && $result->num_rows > 0) {
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$row['Key']}</td>";
        echo "<td>{$row['Default']}</td>";
        echo "<td>{$row['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Students table doesn't exist or cannot be described.";
}

// Check student count
echo "<h2>Student Count</h2>";
$result = $conn->query("SELECT COUNT(*) as count FROM students");
if ($result) {
    $row = $result->fetch_assoc();
    echo "Total students: {$row['count']}";
} else {
    echo "Cannot count students.";
}

$conn->close();
?>
