<?php
// Start the session
session_start();

// Store information about what was cleared
$cleared = array();

// Check if user was logged in
if (isset($_SESSION['userId'])) {
    $userType = $_SESSION['userType'] ?? 'unknown';
    $username = $_SESSION['username'] ?? 'unknown';
    $cleared[] = "User session (Type: $userType, Username: $username)";
}

// Clear login attempts tracking
if (isset($_SESSION['login_attempts'])) {
    $attemptCount = 0;
    foreach ($_SESSION['login_attempts'] as $ip => $attempts) {
        $attemptCount += count($attempts);
    }
    $cleared[] = "Login attempts tracking ($attemptCount attempts)";
}

// Clear CSRF token
if (isset($_SESSION['csrf_token'])) {
    $cleared[] = "CSRF token";
}

// Clear any other session variables
$otherVars = array_diff(array_keys($_SESSION), ['userId', 'userType', 'username', 'login_attempts', 'csrf_token']);
if (!empty($otherVars)) {
    $cleared[] = "Other session variables: " . implode(', ', $otherVars);
}

// Clear the entire session
session_unset();
session_destroy();

// Start a new session to set message
session_start();
$_SESSION['session_cleared'] = true;
$_SESSION['cleared_items'] = $cleared;

// Set client-side cookie removal
setcookie(session_name(), '', time() - 3600, '/');

// Redirect to the login page with a success message
header("Location: index.php?message=session_cleared#login-section");
exit();
?> 