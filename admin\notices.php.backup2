<?php
<?php
session_start();
// Modified session check - allows access without redirecting
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    // User is not logged in, but we'll still proceed without redirecting
    // Just set a flag that the user is not an admin
    $isAdmin = false;
} else {
    $isAdmin = true;
}
// Handle notice creation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'create') {
        $title = $conn->real_escape_string($_POST['title']);
        $content = $conn->real_escape_string($_POST['content']);
        $target_audience = $conn->real_escape_string($_POST['target_audience']);
        $expiry_date = $conn->real_escape_string($_POST['expiry_date']);
        
        $query = "INSERT INTO notices (title, content, target_audience, expiry_date, created_at) 
                 VALUES ('$title', '$content', '$target_audience', '$expiry_date', NOW())";
        
        if ($conn->query($query)) {
            $_SESSION['success'] = "নোটিশ সফলভাবে তৈরি করা হয়েছে।";
        } else {
            $_SESSION['error'] = "নোটিশ তৈরি করতে সমস্যা হয়েছে।";
        }
    } elseif ($_POST['action'] === 'delete' && isset($_POST['notice_id'])) {
        $notice_id = (int)$_POST['notice_id'];
        $query = "DELETE FROM notices WHERE id = $notice_id";
        
        if ($conn->query($query)) {
            $_SESSION['success'] = "নোটিশ সফলভাবে মুছে ফেলা হয়েছে।";
        } else {
            $_SESSION['error'] = "নোটিশ মুছতে সমস্যা হয়েছে।";
        }
    }
    
    header("Location: notices.php");
    exit();
}

// Fetch all notices
$query = "SELECT * FROM notices ORDER BY created_at DESC";
$result = $conn->query($query);

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">নোটিশ ম্যানেজমেন্ট</h1>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createNoticeModal">
                    নতুন নোটিশ
                </button>
            </div>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php 
                    echo $_SESSION['success'];
                    unset($_SESSION['success']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php 
                    echo $_SESSION['error'];
                    unset($_SESSION['error']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>শিরোনাম</th>
                            <th>বিষয়বস্তু</th>
                            <th>টার্গেট অডিয়েন্স</th>
                            <th>মেয়াদ শেষ</th>
                            <th>তৈরি হয়েছে</th>
                            <th>অ্যাকশন</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($result && $result->num_rows > 0): ?>
                            <?php while ($notice = $result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($notice['title']); ?></td>
                                    <td><?php echo nl2br(htmlspecialchars($notice['content'])); ?></td>
                                    <td><?php echo htmlspecialchars($notice['target_audience']); ?></td>
                                    <td><?php echo date('Y-m-d', strtotime($notice['expiry_date'])); ?></td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($notice['created_at'])); ?></td>
                                    <td>
                                        <form method="POST" class="d-inline" onsubmit="return confirm('আপনি কি নিশ্চিত যে আপনি এই নোটিশটি মুছতে চান?');">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="notice_id" value="<?php echo $notice['id']; ?>">
                                            <button type="submit" class="btn btn-danger btn-sm">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="6" class="text-center">কোন নোটিশ পাওয়া যায়নি</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </main>
    </div>
</div>

<!-- Create Notice Modal -->
<div class="modal fade" id="createNoticeModal" tabindex="-1" aria-labelledby="createNoticeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createNoticeModalLabel">নতুন নোটিশ তৈরি করুন</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create">
                    
                    <div class="mb-3">
                        <label for="title" class="form-label">শিরোনাম</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="content" class="form-label">বিষয়বস্তু</label>
                        <textarea class="form-control" id="content" name="content" rows="4" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="target_audience" class="form-label">টার্গেট অডিয়েন্স</label>
                        <select class="form-select" id="target_audience" name="target_audience" required>
                            <option value="all">সকল</option>
                            <option value="students">শিক্ষার্থী</option>
                            <option value="teachers">শিক্ষক</option>
                            <option value="staff">স্টাফ</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="expiry_date" class="form-label">মেয়াদ শেষের তারিখ</label>
                        <input type="date" class="form-control" id="expiry_date" name="expiry_date" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="submit" class="btn btn-primary">সেভ করুন</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?> 