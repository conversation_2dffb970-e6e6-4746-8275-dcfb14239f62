<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Initialize messages
$messages = [];

// Step 1: Check if there's a group with the name "ব্যবসায়"
$checkGroupQuery = "SELECT * FROM groups WHERE group_name = 'ব্যবসায়'";
$groupResult = $conn->query($checkGroupQuery);

if ($groupResult->num_rows == 0) {
    // Group doesn't exist, create it
    $createGroupQuery = "INSERT INTO groups (group_name) VALUES ('ব্যবসায়')";
    if ($conn->query($createGroupQuery)) {
        $businessGroupId = $conn->insert_id;
        $messages[] = "Created new group 'ব্যবসায়' with ID: " . $businessGroupId;
    } else {
        $messages[] = "Error creating group: " . $conn->error;
    }
} else {
    // Group exists
    $businessGroup = $groupResult->fetch_assoc();
    $businessGroupId = $businessGroup['id'];
    $messages[] = "Found existing group 'ব্যবসায়' with ID: " . $businessGroupId;
}

// Step 2: Check if there's a department with the name "ব্যবসায়"
$checkDeptQuery = "SELECT * FROM departments WHERE department_name = 'ব্যবসায়'";
$deptResult = $conn->query($checkDeptQuery);

if ($deptResult->num_rows == 0) {
    // Department doesn't exist, check for similar names
    $checkSimilarDeptQuery = "SELECT * FROM departments WHERE department_name LIKE '%ব্যবসায়%' OR department_name LIKE '%বাবসায়%' OR department_name LIKE '%বানিজ্য%' OR department_name LIKE '%Business%'";
    $similarDeptResult = $conn->query($checkSimilarDeptQuery);

    if ($similarDeptResult->num_rows > 0) {
        $businessDept = $similarDeptResult->fetch_assoc();
        $businessDeptId = $businessDept['id'];
        $messages[] = "Found similar department '" . $businessDept['department_name'] . "' with ID: " . $businessDeptId;

        // Update department name to match exactly
        $updateDeptQuery = "UPDATE departments SET department_name = 'ব্যবসায়' WHERE id = ?";
        $stmt = $conn->prepare($updateDeptQuery);
        $stmt->bind_param("i", $businessDeptId);
        if ($stmt->execute()) {
            $messages[] = "Updated department name to 'ব্যবসায়'";
        } else {
            $messages[] = "Error updating department name: " . $stmt->error;
        }
    } else {
        // No similar department found, create new one
        $createDeptQuery = "INSERT INTO departments (department_name) VALUES ('ব্যবসায়')";
        if ($conn->query($createDeptQuery)) {
            $businessDeptId = $conn->insert_id;
            $messages[] = "Created new department 'ব্যবসায়' with ID: " . $businessDeptId;
        } else {
            $messages[] = "Error creating department: " . $conn->error;
        }
    }
} else {
    // Department exists
    $businessDept = $deptResult->fetch_assoc();
    $businessDeptId = $businessDept['id'];
    $messages[] = "Found existing department 'ব্যবসায়' with ID: " . $businessDeptId;
}

// Step 3: Get all active subjects
$subjectsQuery = "SELECT * FROM subjects WHERE is_active = 1";
$subjects = $conn->query($subjectsQuery);
$subjectCount = $subjects->num_rows;
$messages[] = "Found " . $subjectCount . " active subjects";

// Step 4: Assign subjects to the business group
if (isset($businessGroupId) && $subjects->num_rows > 0) {
    // First, check if there are already subjects assigned to this group
    $checkAssignedQuery = "SELECT COUNT(*) as count FROM subject_groups WHERE group_id = ? AND is_applicable = 1";
    $stmt = $conn->prepare($checkAssignedQuery);
    $stmt->bind_param("i", $businessGroupId);
    $stmt->execute();
    $result = $stmt->get_result();
    $assignedCount = $result->fetch_assoc()['count'];

    if ($assignedCount > 0) {
        $messages[] = "Group already has " . $assignedCount . " subjects assigned. No changes made.";
    } else {
        // No subjects assigned, let's assign some
        $requiredSubjects = [];
        $optionalSubjects = [];
        $fourthSubjects = [];

        // Categorize subjects (this is a simple example, you may want to customize this)
        while ($subject = $subjects->fetch_assoc()) {
            // For demonstration, we'll assign the first 3 subjects as required,
            // the next 3 as optional, and the rest as fourth
            if (count($requiredSubjects) < 3) {
                $requiredSubjects[] = $subject['id'];
            } elseif (count($optionalSubjects) < 3) {
                $optionalSubjects[] = $subject['id'];
            } else {
                $fourthSubjects[] = $subject['id'];
            }
        }

        // Assign required subjects
        foreach ($requiredSubjects as $subjectId) {
            $insertQuery = "INSERT INTO subject_groups (subject_id, group_id, subject_type, is_applicable)
                           VALUES (?, ?, 'required', 1)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param("ii", $subjectId, $businessGroupId);
            $stmt->execute();
        }
        $messages[] = "Assigned " . count($requiredSubjects) . " required subjects to the business group";

        // Assign optional subjects
        foreach ($optionalSubjects as $subjectId) {
            $insertQuery = "INSERT INTO subject_groups (subject_id, group_id, subject_type, is_applicable)
                           VALUES (?, ?, 'optional', 1)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param("ii", $subjectId, $businessGroupId);
            $stmt->execute();
        }
        $messages[] = "Assigned " . count($optionalSubjects) . " optional subjects to the business group";

        // Assign fourth subjects
        foreach ($fourthSubjects as $subjectId) {
            $insertQuery = "INSERT INTO subject_groups (subject_id, group_id, subject_type, is_applicable)
                           VALUES (?, ?, 'fourth', 1)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param("ii", $subjectId, $businessGroupId);
            $stmt->execute();
        }
        $messages[] = "Assigned " . count($fourthSubjects) . " fourth subjects to the business group";
    }
}

// Step 5: Update student with ID STD-833027 to use the correct department
$updateStudentQuery = "UPDATE students SET department_id = ? WHERE student_id = 'STD-833027'";
$stmt = $conn->prepare($updateStudentQuery);
$stmt->bind_param("i", $businessDeptId);
if ($stmt->execute()) {
    $messages[] = "Updated student STD-833027 to use department ID: " . $businessDeptId;
} else {
    $messages[] = "Error updating student: " . $stmt->error;
}

// Display results
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>Fix Business Group - College Management System</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', 'Noto Sans Bengali', sans-serif;
            background-color: #f0f2f5;
        }
        .container {
            max-width: 800px;
            margin: 50px auto;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .card-header {
            background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
            color: white;
            padding: 20px;
        }
        .card-body {
            padding: 30px;
        }
        .list-group-item {
            padding: 15px;
            border-left: 5px solid #4361ee;
            margin-bottom: 10px;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .btn-primary {
            background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0"><i class="fas fa-wrench me-2"></i>ব্যবসায় গ্রুপ ফিক্স</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>ব্যবসায় গ্রুপ ফিক্স সম্পন্ন হয়েছে!
                </div>

                <h5 class="mb-3">পরিবর্তনসমূহ:</h5>
                <ul class="list-group">
                    <?php foreach ($messages as $message): ?>
                        <li class="list-group-item">
                            <i class="fas fa-arrow-right me-2 text-primary"></i><?php echo $message; ?>
                        </li>
                    <?php endforeach; ?>
                </ul>

                <div class="mt-4 d-flex justify-content-between">
                    <a href="student_subject_selection.php?id=STD-833027" class="btn btn-primary">
                        <i class="fas fa-user-graduate me-2"></i>শিক্ষার্থী বিষয় নির্বাচন পেজে যান
                    </a>
                    <a href="configure_business_subjects.php" class="btn btn-info">
                        <i class="fas fa-cog me-2"></i>ব্যবসায় বিষয় কনফিগার করুন
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
