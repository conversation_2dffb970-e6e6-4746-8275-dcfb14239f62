<?php
// Include database connection
require_once 'includes/dbh.inc.php';

// Check student_subjects table structure
echo "<h2>student_subjects Table Structure</h2>";
$tableStructureQuery = "DESCRIBE student_subjects";
$tableStructure = $conn->query($tableStructureQuery);

if ($tableStructure) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($column = $tableStructure->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>Error checking table structure: " . $conn->error . "</p>";
}

// Check if student_subjects table has any data
echo "<h2>student_subjects Table Data</h2>";
$tableDataQuery = "SELECT * FROM student_subjects LIMIT 10";
$tableData = $conn->query($tableDataQuery);

if ($tableData) {
    if ($tableData->num_rows > 0) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr>";
        
        // Get column names
        $firstRow = $tableData->fetch_assoc();
        foreach ($firstRow as $column => $value) {
            echo "<th>{$column}</th>";
        }
        echo "</tr>";
        
        // Reset pointer and show data
        $tableData->data_seek(0);
        
        while ($row = $tableData->fetch_assoc()) {
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>{$value}</td>";
            }
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>No data found in student_subjects table.</p>";
    }
} else {
    echo "<p>Error checking table data: " . $conn->error . "</p>";
}

// Check for the specific student
$studentId = 'STD-601523';
echo "<h2>Student Information</h2>";
$studentQuery = "SELECT s.*, d.department_name 
                FROM students s 
                LEFT JOIN departments d ON s.department_id = d.id 
                WHERE s.student_id = '$studentId'";
$studentResult = $conn->query($studentQuery);

if ($studentResult && $studentResult->num_rows > 0) {
    $student = $studentResult->fetch_assoc();
    echo "<p>Student ID: {$student['student_id']}</p>";
    echo "<p>Student Database ID: {$student['id']}</p>";
    echo "<p>Name: {$student['first_name']} {$student['last_name']}</p>";
    echo "<p>Department: {$student['department_name']} (ID: {$student['department_id']})</p>";
    
    // Check if this student has any subjects
    $studentSubjectsQuery = "SELECT ss.*, s.subject_name, s.subject_code 
                            FROM student_subjects ss 
                            JOIN subjects s ON ss.subject_id = s.id 
                            WHERE ss.student_id = {$student['id']}";
    $studentSubjects = $conn->query($studentSubjectsQuery);
    
    if ($studentSubjects && $studentSubjects->num_rows > 0) {
        echo "<h3>Selected Subjects</h3>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Subject ID</th><th>Subject Name</th><th>Subject Code</th><th>Category</th></tr>";
        
        while ($subject = $studentSubjects->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$subject['subject_id']}</td>";
            echo "<td>{$subject['subject_name']}</td>";
            echo "<td>{$subject['subject_code']}</td>";
            echo "<td>{$subject['category']}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>No subjects selected for this student.</p>";
    }
} else {
    echo "<p>Student not found.</p>";
}

// Check for any errors in the student_subjects table
echo "<h2>Checking for Errors</h2>";

// Check for duplicate entries
$duplicateQuery = "SELECT student_id, subject_id, COUNT(*) as count 
                  FROM student_subjects 
                  GROUP BY student_id, subject_id 
                  HAVING COUNT(*) > 1";
$duplicateResult = $conn->query($duplicateQuery);

if ($duplicateResult) {
    if ($duplicateResult->num_rows > 0) {
        echo "<p>Found duplicate entries in student_subjects table:</p>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Student ID</th><th>Subject ID</th><th>Count</th></tr>";
        
        while ($row = $duplicateResult->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['student_id']}</td>";
            echo "<td>{$row['subject_id']}</td>";
            echo "<td>{$row['count']}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>No duplicate entries found.</p>";
    }
} else {
    echo "<p>Error checking for duplicates: " . $conn->error . "</p>";
}

// Check for invalid student IDs
$invalidStudentQuery = "SELECT ss.* 
                       FROM student_subjects ss 
                       LEFT JOIN students s ON ss.student_id = s.id 
                       WHERE s.id IS NULL";
$invalidStudentResult = $conn->query($invalidStudentQuery);

if ($invalidStudentResult) {
    if ($invalidStudentResult->num_rows > 0) {
        echo "<p>Found entries with invalid student IDs:</p>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Student ID</th><th>Subject ID</th></tr>";
        
        while ($row = $invalidStudentResult->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['student_id']}</td>";
            echo "<td>{$row['subject_id']}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>No entries with invalid student IDs found.</p>";
    }
} else {
    echo "<p>Error checking for invalid student IDs: " . $conn->error . "</p>";
}

// Check for invalid subject IDs
$invalidSubjectQuery = "SELECT ss.* 
                       FROM student_subjects ss 
                       LEFT JOIN subjects s ON ss.subject_id = s.id 
                       WHERE s.id IS NULL";
$invalidSubjectResult = $conn->query($invalidSubjectQuery);

if ($invalidSubjectResult) {
    if ($invalidSubjectResult->num_rows > 0) {
        echo "<p>Found entries with invalid subject IDs:</p>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Student ID</th><th>Subject ID</th></tr>";
        
        while ($row = $invalidSubjectResult->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['student_id']}</td>";
            echo "<td>{$row['subject_id']}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>No entries with invalid subject IDs found.</p>";
    }
} else {
    echo "<p>Error checking for invalid subject IDs: " . $conn->error . "</p>";
}
?>
