/* Admin Panel CSS */

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 0.875rem;
    background-color: #f8f9fa;
    color: #333;
}

.sidebar {
    min-height: 100vh;
    background-color: #343a40;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.6);
    transition: all 0.3s;
}

.sidebar .nav-link:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link.active {
    color: #fff;
    font-weight: 500;
    background-color: rgba(255, 255, 255, 0.1);
}

/* Card Styles */
.card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
    font-weight: 500;
}

/* Form Styles */
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Button Styles */
.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

/* Table Styles */
.table th {
    background-color: #f8f9fa;
    font-weight: 500;
}

.table-hover tbody tr:hover {
    background-color: #f1f3f5;
}

/* Status Badges */
.badge-success {
    background-color: #28a745;
}

.badge-warning {
    background-color: #ffc107;
    color: #343a40;
}

.badge-danger {
    background-color: #dc3545;
}

/* Search Box */
.search-box {
    position: relative;
}

.search-box i {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.search-box input {
    padding-left: 30px;
}

/* Dashboard Cards */
.dashboard-card {
    transition: all 0.3s;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.dashboard-card .icon {
    font-size: 2rem;
    color: #007bff;
}

/* Logo Preview */
.logo-preview {
    max-width: 200px;
    max-height: 200px;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 5px;
    margin-top: 10px;
}

.logo-placeholder {
    width: 200px;
    height: 200px;
    border: 2px dashed #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #aaa;
    margin-top: 10px;
} 