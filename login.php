<?php
// Start output buffering FIRST to prevent header issues
ob_start();

// Start session
session_start();

// Include database connection
require_once 'includes/dbh.inc.php';

// Check if form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Sanitize inputs
    $username = htmlspecialchars(trim($_POST['username']));
    $password = $_POST['password']; // Don't sanitize password
    $userType = isset($_POST['userType']) ? htmlspecialchars($_POST['userType']) : '';

    // Check if inputs are empty
    if (empty($username) || empty($password) || empty($userType)) {
        $error_message = "সব ফিল্ড পূরণ করুন।";
    } else {
        // Query to check user credentials
        $sql = "SELECT * FROM users WHERE username = ? AND user_type = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ss", $username, $userType);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($row = $result->fetch_assoc()) {
            if (password_verify($password, $row['password'])) {
                $_SESSION['loggedin'] = true;
                $_SESSION['userId'] = $row['id'];
                $_SESSION['username'] = $row['username'];
                $_SESSION['userType'] = $row['user_type'];

                // Redirect based on user type
                switch($row['user_type']) {
                    case 'admin':
                        header("Location: admin/dashboard.php");
                        exit();
                    case 'teacher':
                        header("Location: teacher/dashboard.php");
                        exit();
                    case 'student':
                        header("Location: student/dashboard.php");
                        exit();
                    case 'staff':
                        header("Location: staff/dashboard.php");
                        exit();
                    default:
                        header("Location: index.php");
                        exit();
                }
            } else {
                $error_message = "ভুল পাসওয়ার্ড। দয়া করে আবার চেষ্টা করুন।";
            }
        } else {
            $error_message = "ইউজারনেম খুঁজে পাওয়া যায়নি।";
        }
        $stmt->close();
    }
}

// Check for error parameter in URL
if (empty($error_message) && isset($_GET['error'])) {
    switch($_GET['error']) {
        case 'wrongpassword':
            $error_message = "ভুল পাসওয়ার্ড। দয়া করে আবার চেষ্টা করুন।";
            break;
        case 'emptyfields':
            $error_message = "সব ফিল্ড পূরণ করুন।";
            break;
        case 'dbconnect':
            $error_message = "ডাটাবেস কানেকশন ত্রুটি।";
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>লগইন</title>
    <!-- Hind Siliguri Font -->


    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        .login-container {
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            width: 300px;
        }
        h2 {
            text-align: center;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"], select.form-control {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-family: 'Hind Siliguri', sans-serif;
            appearance: auto;
            -webkit-appearance: auto;
            -moz-appearance: auto;
            background-color: #fff;
        }
        .btn {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
        }
        .btn:hover {
            background-color: #45a049;
        }
        .error {
            color: red;
            margin-bottom: 15px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="login-container" id="login-section">
        <h2>লগইন</h2>
        <?php if (!empty($error_message)): ?>
            <div class="error"><?php echo $error_message; ?></div>
        <?php endif; ?>
        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
            <div class="form-group">
                <label for="username">ইউজারনেম:</label>
                <input type="text" id="username" name="username" required>
            </div>
            <div class="form-group">
                <label for="password">পাসওয়ার্ড:</label>
                <input type="password" id="password" name="password" required>
            </div>
            <div class="form-group">
                <label for="userType">ব্যবহারকারীর ধরন:</label>
                <select id="userType" name="userType" class="form-control" required style="height: auto; padding: 8px 12px; font-size: 16px;">
                    <option value="">ব্যবহারকারীর ধরন নির্বাচন করুন</option>
                    <option value="admin">অ্যাডমিন</option>
                    <option value="teacher">শিক্ষক</option>
                    <option value="student">শিক্ষার্থী</option>
                    <option value="staff">কর্মচারী</option>
                </select>
            </div>
            <button type="submit" class="btn">লগইন</button>
        </form>
    </div>

    <!-- Prevent Page Looping Script -->
    <script>
        // Prevent page looping and auto-refresh
        document.addEventListener('DOMContentLoaded', function() {
            // Set proper title
            document.title = 'লগইন - নিশাত এডুকেশন সেন্টার';

            // Remove any meta refresh tags that might cause looping
            const metaRefresh = document.querySelectorAll('meta[http-equiv="refresh"]');
            metaRefresh.forEach(meta => meta.remove());

            // Prevent form auto-submission
            const form = document.querySelector('form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    // Only allow user-initiated submissions
                    if (!e.isTrusted) {
                        e.preventDefault();
                        return false;
                    }
                });
            }

            // Clear any existing intervals that might cause issues
            for (let i = 1; i < 1000; i++) {
                clearInterval(i);
                clearTimeout(i);
            }

            // Prevent automatic redirects
            if (window.location.hash === '#') {
                history.replaceState(null, null, window.location.pathname);
            }

            console.log('Login page loaded successfully - no looping');
        });

        // Prevent back button issues
        window.addEventListener('pageshow', function(event) {
            if (event.persisted) {
                window.location.reload();
            }
        });
    </script>
</body>
</html>
<?php
// Complete the response properly to stop page looping
$content = ob_get_contents();
$content_length = strlen($content);
ob_end_clean();

// Send proper headers
header('Content-Type: text/html; charset=UTF-8');
header('Cache-Control: no-cache, must-revalidate');
header('Connection: close');
header('Content-Length: ' . $content_length);

// Output content
echo $content;

// Ensure complete response
if (function_exists('fastcgi_finish_request')) {
    fastcgi_finish_request();
} else {
    flush();
}

// Explicitly exit to prevent any additional output
exit();