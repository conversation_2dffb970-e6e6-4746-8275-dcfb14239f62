<?php
require_once '../includes/dbh.inc.php';

// Check for duplicate mark types
$duplicatesQuery = "
    SELECT type_name, COUNT(*) as count
    FROM marks_types
    GROUP BY type_name
    HAVING COUNT(*) > 1
";

$result = $conn->query($duplicatesQuery);
echo "<h2>Duplicate Mark Types</h2>";

if ($result && $result->num_rows > 0) {
    echo "<table border='1'>";
    echo "<tr><th>Type Name</th><th>Count</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['type_name']) . "</td>";
        echo "<td>" . $row['count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "No duplicates found.";
}

// Get all mark types
$allTypesQuery = "SELECT id, type_name FROM marks_types ORDER BY type_name";
$allTypes = $conn->query($allTypesQuery);

echo "<h2>All Mark Types</h2>";
if ($allTypes && $allTypes->num_rows > 0) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Type Name</th></tr>";
    while ($type = $allTypes->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $type['id'] . "</td>";
        echo "<td>" . htmlspecialchars($type['type_name']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "No mark types found.";
}
?> 