/* Modern Bengali Fonts Integration */
@import url('https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Baloo+Da+2:wght@400;500;600;700&display=swap');

/* Global Font Settings */
:root {
    --primary-font: 'Hind Siliguri', 'Noto Sans Bengali', sans-serif;
    --secondary-font: 'Baloo Da 2', 'Noto Sans Bengali', sans-serif;
    --certificate-font: '<PERSON><PERSON>', 'Noto Sans Bengali', sans-serif;
}

/* Base Font Settings */
body, html {
    font-family: var(--primary-font);
    font-size: 16px;
    line-height: 1.6;
    color: #333;
}

/* Typography Elements */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--primary-font);
    font-weight: 600;
    line-height: 1.3;
}

p, span, div, li, td, th {
    font-family: var(--primary-font);
}

/* Form Elements */
input, select, textarea, button, .btn, .form-control {
    font-family: var(--primary-font);
}

/* Navigation Elements */
.nav-link, .dropdown-item, .navbar-brand, .menu-item {
    font-family: var(--primary-font);
}

/* Special Elements */
.hero-title, .section-title, .page-title {
    font-family: var(--secondary-font);
    font-weight: 700;
}

.card-title, .feature-title {
    font-family: var(--secondary-font);
    font-weight: 600;
}

/* Certificate Specific */
.certificate-text, .certificate-title {
    font-family: var(--certificate-font);
}

/* Font Weight Classes */
.font-light {
    font-weight: 300;
}

.font-regular {
    font-weight: 400;
}

.font-medium {
    font-weight: 500;
}

.font-semibold {
    font-weight: 600;
}

.font-bold {
    font-weight: 700;
}

/* Font Size Classes */
.text-xs {
    font-size: 0.75rem;
}

.text-sm {
    font-size: 0.875rem;
}

.text-base {
    font-size: 1rem;
}

.text-lg {
    font-size: 1.125rem;
}

.text-xl {
    font-size: 1.25rem;
}

.text-2xl {
    font-size: 1.5rem;
}

.text-3xl {
    font-size: 1.875rem;
}

.text-4xl {
    font-size: 2.25rem;
}

/* Font Smoothing */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}
