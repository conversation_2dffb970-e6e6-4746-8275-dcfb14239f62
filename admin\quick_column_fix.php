<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

echo "<!DOCTYPE html>
<html lang='bn'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Quick Column Fix</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap' rel='stylesheet'>
    <style>
        * { font-family: 'Hind Siliguri', sans-serif; }
    </style>
</head>
<body class='bg-light'>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-md-8'>
            <div class='card'>
                <div class='card-header'>
                    <h4 class='mb-0'><i class='fas fa-tools me-2'></i>Quick Column Fix</h4>
                </div>
                <div class='card-body'>";

try {
    $conn->begin_transaction();
    
    $results = [];
    
    // Check and fix payments table
    $paymentsExists = $conn->query("SHOW TABLES LIKE 'payments'")->num_rows > 0;
    if ($paymentsExists) {
        // Check if receipt_number column exists
        $checkColumn = $conn->query("SHOW COLUMNS FROM payments LIKE 'receipt_number'");
        if ($checkColumn->num_rows > 0) {
            // Check if receipt_no already exists
            $checkReceiptNo = $conn->query("SHOW COLUMNS FROM payments LIKE 'receipt_no'");
            if ($checkReceiptNo->num_rows == 0) {
                // Rename receipt_number to receipt_no
                $renameQuery = "ALTER TABLE payments CHANGE COLUMN receipt_number receipt_no VARCHAR(100)";
                if ($conn->query($renameQuery)) {
                    $results[] = "✅ payments.receipt_number → receipt_no (সফল)";
                } else {
                    $results[] = "❌ payments table rename failed: " . $conn->error;
                }
            } else {
                $results[] = "⚠️ payments table এ receipt_no column ইতিমধ্যে আছে";
            }
        } else {
            $results[] = "✅ payments table এ receipt_number column নেই (ঠিক আছে)";
        }
    } else {
        $results[] = "⚠️ payments table খুঁজে পাওয়া যায়নি";
    }
    
    // Check and fix fee_payments table
    $feePaymentsExists = $conn->query("SHOW TABLES LIKE 'fee_payments'")->num_rows > 0;
    if ($feePaymentsExists) {
        // Check if receipt_number column exists
        $checkColumn = $conn->query("SHOW COLUMNS FROM fee_payments LIKE 'receipt_number'");
        if ($checkColumn->num_rows > 0) {
            // Check if receipt_no already exists
            $checkReceiptNo = $conn->query("SHOW COLUMNS FROM fee_payments LIKE 'receipt_no'");
            if ($checkReceiptNo->num_rows == 0) {
                // Rename receipt_number to receipt_no
                $renameQuery = "ALTER TABLE fee_payments CHANGE COLUMN receipt_number receipt_no VARCHAR(100)";
                if ($conn->query($renameQuery)) {
                    $results[] = "✅ fee_payments.receipt_number → receipt_no (সফল)";
                } else {
                    $results[] = "❌ fee_payments table rename failed: " . $conn->error;
                }
            } else {
                $results[] = "⚠️ fee_payments table এ receipt_no column ইতিমধ্যে আছে";
            }
        } else {
            $results[] = "✅ fee_payments table এ receipt_number column নেই (ঠিক আছে)";
        }
    } else {
        $results[] = "⚠️ fee_payments table খুঁজে পাওয়া যায়নি";
    }
    
    // Ensure fee_payments table has proper structure
    $createFeePaymentsTable = "CREATE TABLE IF NOT EXISTS fee_payments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        fee_id INT NOT NULL,
        student_id INT,
        amount DECIMAL(10,2) NOT NULL,
        payment_date DATE NOT NULL,
        payment_method VARCHAR(50) NOT NULL DEFAULT 'cash',
        receipt_no VARCHAR(50) DEFAULT NULL,
        transaction_id VARCHAR(100) DEFAULT NULL,
        notes TEXT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($createFeePaymentsTable)) {
        $results[] = "✅ fee_payments table structure ensured";
    } else {
        $results[] = "❌ fee_payments table creation failed: " . $conn->error;
    }
    
    $conn->commit();
    
    echo "<div class='alert alert-success'>";
    echo "<h5><i class='fas fa-check-circle me-2'></i>Column Fix সম্পন্ন!</h5>";
    echo "<ul class='mb-0'>";
    foreach ($results as $result) {
        echo "<li>$result</li>";
    }
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    $conn->rollback();
    echo "<div class='alert alert-danger'>";
    echo "<h5><i class='fas fa-exclamation-circle me-2'></i>ত্রুটি!</h5>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Show current table structures
echo "<div class='mt-4'>";
echo "<h5>বর্তমান Table Structures:</h5>";

// Show payments table structure
$paymentsExists = $conn->query("SHOW TABLES LIKE 'payments'")->num_rows > 0;
if ($paymentsExists) {
    echo "<h6>Payments Table:</h6>";
    $columnsResult = $conn->query("SHOW COLUMNS FROM payments");
    echo "<div class='table-responsive'>";
    echo "<table class='table table-sm table-bordered'>";
    echo "<thead><tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr></thead>";
    echo "<tbody>";
    while ($column = $columnsResult->fetch_assoc()) {
        $highlight = ($column['Field'] == 'receipt_no') ? 'table-success' : 
                    (($column['Field'] == 'receipt_number') ? 'table-warning' : '');
        echo "<tr class='$highlight'>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    echo "</div>";
}

// Show fee_payments table structure
$feePaymentsExists = $conn->query("SHOW TABLES LIKE 'fee_payments'")->num_rows > 0;
if ($feePaymentsExists) {
    echo "<h6>Fee Payments Table:</h6>";
    $columnsResult = $conn->query("SHOW COLUMNS FROM fee_payments");
    echo "<div class='table-responsive'>";
    echo "<table class='table table-sm table-bordered'>";
    echo "<thead><tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr></thead>";
    echo "<tbody>";
    while ($column = $columnsResult->fetch_assoc()) {
        $highlight = ($column['Field'] == 'receipt_no') ? 'table-success' : 
                    (($column['Field'] == 'receipt_number') ? 'table-warning' : '');
        echo "<tr class='$highlight'>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    echo "</div>";
}

echo "</div>";

echo "<div class='mt-4 text-center'>";
echo "<a href='fee_management.php' class='btn btn-primary me-2'>";
echo "<i class='fas fa-money-bill-wave me-1'></i> ফি ম্যানেজমেন্ট";
echo "</a>";
echo "<a href='payment_system_checker.php' class='btn btn-secondary me-2'>";
echo "<i class='fas fa-stethoscope me-1'></i> System Checker";
echo "</a>";
echo "<a href='fix_column_names.php' class='btn btn-info'>";
echo "<i class='fas fa-columns me-1'></i> Column Fixer";
echo "</a>";
echo "</div>";

echo "                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>";
?>
