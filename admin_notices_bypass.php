<?php
session_start();
// This script temporarily sets admin session to access notices
$_SESSION["userId"] = 1;
$_SESSION["username"] = "admin";
$_SESSION["userType"] = "admin";
$_SESSION["lastActivity"] = time();

echo "<h2>Admin Session Created</h2>";
echo "<p>You now have a temporary admin session to access the notices page.</p>";
echo "<p>Try accessing: <a href='admin/notices.php' target='_blank'>admin/notices.php</a></p>";
echo "<p>When finished: <a href='clear_session.php'>Clear Session</a></p>";
?>