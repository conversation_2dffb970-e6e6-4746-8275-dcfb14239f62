<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';
require_once 'class_subjects_helper.php';

// Check if subject_exam_pattern table exists
$tableCheckQuery = "SHOW TABLES LIKE 'subject_exam_pattern'";
$tableResult = $conn->query($tableCheckQuery);
$tableExists = $tableResult->num_rows > 0;

if (!$tableExists) {
    // Create the table
    $createTableQuery = "CREATE TABLE subject_exam_pattern (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        subject_id INT(11) NOT NULL,
        has_cq TINYINT(1) DEFAULT 1,
        has_mcq TINYINT(1) DEFAULT 1,
        has_practical TINYINT(1) DEFAULT 0,
        cq_marks DECIMAL(5,2) DEFAULT 70.00,
        mcq_marks DECIMAL(5,2) DEFAULT 30.00,
        practical_marks DECIMAL(5,2) DEFAULT 0.00,
        total_marks DECIMAL(5,2) DEFAULT 100.00,
        is_active TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY (subject_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $conn->query($createTableQuery);
}

// Get all subjects
$subjectsQuery = "SELECT * FROM subjects ORDER BY subject_name";
$subjects = $conn->query($subjectsQuery);

// Get all classes
$classesQuery = "SELECT * FROM classes ORDER BY class_name";
$classes = $conn->query($classesQuery);

// Get all departments
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Get class level templates
$templatesQuery = "SELECT * FROM class_level_templates WHERE is_active = 1 ORDER BY class_level";
$classTemplates = $conn->query($templatesQuery);

// Get all exam patterns
$patternsQuery = "SELECT p.*, s.subject_name, s.subject_code
                 FROM subject_exam_pattern p
                 JOIN subjects s ON p.subject_id = s.id
                 ORDER BY s.subject_name";
$patterns = $conn->query($patternsQuery);

// Handle form submissions
$success_message = "";
$error_message = "";
$edit_data = null;

// Edit exam pattern
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $edit_id = $_GET['edit'];
    $editQuery = "SELECT * FROM subject_exam_pattern WHERE id = ?";
    $stmt = $conn->prepare($editQuery);
    $stmt->bind_param("i", $edit_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $edit_data = $result->fetch_assoc();
    }
}

// Add or update exam pattern
if (isset($_POST['add_pattern'])) {
    $subject_id = $_POST['subject_id'];

    // Check which components are enabled
    $has_cq = isset($_POST['has_cq']) ? 1 : 0;
    $has_mcq = isset($_POST['has_mcq']) ? 1 : 0;
    $has_practical = isset($_POST['has_practical']) ? 1 : 0;

    // Only count marks for enabled components
    $cq_marks = $has_cq ? ($_POST['cq_marks'] ?? 70) : 0;
    $mcq_marks = $has_mcq ? ($_POST['mcq_marks'] ?? 30) : 0;
    $practical_marks = $has_practical ? ($_POST['practical_marks'] ?? 0) : 0;

    $total_marks = $_POST['total_marks'] ?? 100;
    $is_active = isset($_POST['is_active']) ? 1 : 0;

    // Check if pattern already exists for this subject
    $checkQuery = "SELECT id FROM subject_exam_pattern WHERE subject_id = ?";
    $stmt = $conn->prepare($checkQuery);
    $stmt->bind_param("i", $subject_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0 && !isset($_POST['edit_id'])) {
        $error_message = "এই বিষয়ের জন্য পরীক্ষার প্যাটার্ন ইতিমধ্যে বিদ্যমান। আপডেট করতে চাইলে সম্পাদনা করুন।";
    } else {
        if (isset($_POST['edit_id'])) {
            // Update existing pattern
            $edit_id = $_POST['edit_id'];
            $updateQuery = "UPDATE subject_exam_pattern
                           SET subject_id = ?, has_cq = ?, has_mcq = ?, has_practical = ?,
                               cq_marks = ?, mcq_marks = ?, practical_marks = ?,
                               total_marks = ?, is_active = ?
                           WHERE id = ?";
            $stmt = $conn->prepare($updateQuery);
            $stmt->bind_param("iiidddiii", $subject_id, $has_cq, $has_mcq, $has_practical,
                             $cq_marks, $mcq_marks, $practical_marks, $total_marks, $is_active, $edit_id);

            if ($stmt->execute()) {
                $success_message = "পরীক্ষার প্যাটার্ন সফলভাবে আপডেট করা হয়েছে।";
                header("Location: subject_exam_pattern.php?success=updated");
                exit();
            } else {
                $error_message = "পরীক্ষার প্যাটার্ন আপডেট করতে সমস্যা হয়েছে: " . $conn->error;
            }
        } else {
            // Add new pattern
            $insertQuery = "INSERT INTO subject_exam_pattern
                           (subject_id, has_cq, has_mcq, has_practical, cq_marks, mcq_marks, practical_marks, total_marks, is_active)
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param("iiidddddi", $subject_id, $has_cq, $has_mcq, $has_practical,
                             $cq_marks, $mcq_marks, $practical_marks, $total_marks, $is_active);

            if ($stmt->execute()) {
                $success_message = "পরীক্ষার প্যাটার্ন সফলভাবে যোগ করা হয়েছে।";
                header("Location: subject_exam_pattern.php?success=added");
                exit();
            } else {
                $error_message = "পরীক্ষার প্যাটার্ন যোগ করতে সমস্যা হয়েছে: " . $conn->error;
            }
        }
    }
}

// Delete exam pattern
if (isset($_POST['delete_pattern'])) {
    $pattern_id = $_POST['pattern_id'];

    $deleteQuery = "DELETE FROM subject_exam_pattern WHERE id = ?";
    $stmt = $conn->prepare($deleteQuery);
    $stmt->bind_param("i", $pattern_id);

    if ($stmt->execute()) {
        $success_message = "পরীক্ষার প্যাটার্ন সফলভাবে মুছে ফেলা হয়েছে।";
        header("Location: subject_exam_pattern.php?success=deleted");
        exit();
    } else {
        $error_message = "পরীক্ষার প্যাটার্ন মুছতে সমস্যা হয়েছে: " . $conn->error;
    }
}

// Toggle status
if (isset($_POST['toggle_status'])) {
    $pattern_id = $_POST['pattern_id'];
    $new_status = $_POST['new_status'];

    $updateQuery = "UPDATE subject_exam_pattern SET is_active = ? WHERE id = ?";
    $stmt = $conn->prepare($updateQuery);
    $stmt->bind_param("ii", $new_status, $pattern_id);

    if ($stmt->execute()) {
        $status_text = $new_status ? "সক্রিয়" : "নিষ্ক্রিয়";
        $success_message = "পরীক্ষার প্যাটার্ন সফলভাবে $status_text করা হয়েছে।";
        header("Location: subject_exam_pattern.php?success=status_updated");
        exit();
    } else {
        $error_message = "স্ট্যাটাস পরিবর্তন করতে সমস্যা হয়েছে: " . $conn->error;
    }
}

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta name="description" content="বাংলাদেশ শিক্ষা বোর্ড অনুযায়ী ক্লাস ভিত্তিক বিষয়ের মার্কস প্যাটার্ন নির্ধারণ। প্রাথমিক, জুনিয়র, SSC ও HSC স্তরের জন্য আলাদা মার্কস বিতরণ।">
    <title>বিষয় পরীক্ষার প্যাটার্ন | ক্লাস ভিত্তিক মার্কস বিতরণ | কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Hind Siliguri Font -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap">

    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: #f8f9fa;
            color: #333;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        /* Fix loading issues */
        body.loading,
        body.loaded {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        #page-loader,
        .loader,
        .loading-spinner,
        .loader-spinner,
        .loader-text,
        [id*="loader"],
        [class*="loader"],
        [class*="loading"] {
            display: none !important;
            opacity: 0 !important;
            visibility: hidden !important;
            height: 0 !important;
            width: 0 !important;
            position: absolute !important;
            z-index: -9999 !important;
            pointer-events: none !important;
        }

        .card {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            border-bottom: none;
            padding: 15px 20px;
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
            padding: 8px 16px;
            transition: all 0.3s;
        }

        .btn-primary {
            background-color: #4361ee;
            border-color: #4361ee;
        }

        .btn-primary:hover {
            background-color: #3a56d4;
            border-color: #3a56d4;
        }

        .form-control, .form-select {
            border-radius: 8px;
            padding: 10px 15px;
            border: 1px solid #dee2e6;
            transition: all 0.3s;
        }

        .form-control:focus, .form-select:focus {
            box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
            border-color: #4361ee;
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .badge {
            padding: 6px 10px;
            font-weight: 500;
        }

        .icon-circle {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .progress {
            height: 10px;
            border-radius: 5px;
        }
    </style>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Simple fix for loading issues -->
    <script src="js/fix-loading.js"></script>

    <!-- Error checking script -->
    <script>
    window.addEventListener('error', function(e) {
        console.error('JavaScript Error:', e.error);
        console.error('File:', e.filename);
        console.error('Line:', e.lineno);
    });

    // Check if edit mode is active
    <?php if (isset($_GET['edit'])): ?>
    console.log('Edit mode active for ID:', <?php echo intval($_GET['edit']); ?>);
    <?php endif; ?>
    </script>
</head>
<body onload="document.body.classList.add('loaded')">
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h1 class="h3 mb-0 text-center">
                        <i class="fas fa-clipboard-list me-2 text-primary"></i> বিষয় পরীক্ষার প্যাটার্ন
                    </h1>
                </div>
            </div>
        </div>
    </div>

    <!-- Exam Navigation Buttons -->
    <?php include 'exam_buttons.php'; ?>

    <!-- Class-wise Subject Marks Pattern Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-graduation-cap me-2"></i>ক্লাস অনুযায়ী বিষয়ের মার্কস প্যাটার্ন নির্ধারণ
                        <span class="badge bg-light text-dark ms-2">বাংলাদেশ শিক্ষা বোর্ড অনুযায়ী</span>
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Class Level Templates Info -->
                    <div class="alert alert-info mb-4">
                        <h6><i class="fas fa-info-circle me-2"></i>শিক্ষা স্তর অনুযায়ী মার্কস বিতরণ:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="mb-0">
                                    <li><strong>প্রাথমিক (১-৫):</strong> শুধু লিখিত ১০০ নম্বর</li>
                                    <li><strong>জুনিয়র (৬-৮):</strong> মূলত লিখিত ১০০ নম্বর</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="mb-0">
                                    <li><strong>SSC (৯-১০):</strong> সৃজনশীল ৭০ + MCQ ৩০</li>
                                    <li><strong>HSC (১১-১২):</strong> সৃজনশীল ৭০ + MCQ ৩০ + ব্যবহারিক</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="class_filter" class="form-label">ক্লাস নির্বাচন করুন</label>
                                    <select class="form-select" id="class_filter" onchange="loadClassSubjects()">
                                        <option value="">-- ক্লাস নির্বাচন করুন --</option>
                                        <?php
                                        if ($classes && $classes->num_rows > 0) {
                                            while ($class = $classes->fetch_assoc()) {
                                                echo "<option value='" . $class['id'] . "'>" . htmlspecialchars($class['class_name']) . "</option>";
                                            }
                                        }
                                        ?>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="department_filter" class="form-label">বিভাগ (ঐচ্ছিক)</label>
                                    <select class="form-select" id="department_filter" onchange="loadClassSubjects()">
                                        <option value="">-- সকল বিভাগ --</option>
                                        <?php
                                        if ($departments && $departments->num_rows > 0) {
                                            while ($department = $departments->fetch_assoc()) {
                                                echo "<option value='" . $department['id'] . "'>" . htmlspecialchars($department['department_name']) . "</option>";
                                            }
                                        }
                                        ?>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="template_filter" class="form-label">মার্কস টেমপ্লেট</label>
                                    <select class="form-select" id="template_filter" onchange="loadTemplateInfo()">
                                        <option value="">-- অটো নির্বাচন --</option>
                                        <?php
                                        if ($classTemplates && $classTemplates->num_rows > 0) {
                                            while ($template = $classTemplates->fetch_assoc()) {
                                                echo "<option value='" . $template['class_level'] . "' data-template='" . htmlspecialchars(json_encode($template)) . "'>" . htmlspecialchars($template['class_level_name']) . "</option>";
                                            }
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>

                            <!-- Template Info Display -->
                            <div id="templateInfo" style="display: none;" class="alert alert-success mb-3">
                                <h6><i class="fas fa-template me-2"></i>নির্বাচিত টেমপ্লেট:</h6>
                                <div id="templateDetails"></div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="text-center">
                                <button class="btn btn-primary btn-lg mb-2" onclick="configureClassMarksPattern()" disabled id="configureBtn">
                                    <i class="fas fa-cog me-2"></i>মার্কস প্যাটার্ন কনফিগার করুন
                                </button>
                                <br>
                                <button class="btn btn-outline-info" onclick="viewClassMarksPattern()" disabled id="viewBtn">
                                    <i class="fas fa-eye me-2"></i>বর্তমান প্যাটার্ন দেখুন
                                </button>
                                <br>
                                <button class="btn btn-outline-success mt-2" onclick="showTemplateManager()">
                                    <i class="fas fa-cogs me-2"></i>টেমপ্লেট ম্যানেজার
                                </button>
                            </div>
                            <div class="mt-3" id="classSubjectsSummary" style="display: none;">
                                <!-- Class subjects summary will be displayed here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($_GET['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php
            switch ($_GET['success']) {
                case 'added':
                    echo "পরীক্ষার প্যাটার্ন সফলভাবে যোগ করা হয়েছে।";
                    break;
                case 'updated':
                    echo "পরীক্ষার প্যাটার্ন সফলভাবে আপডেট করা হয়েছে।";
                    break;
                case 'deleted':
                    echo "পরীক্ষার প্যাটার্ন সফলভাবে মুছে ফেলা হয়েছে।";
                    break;
                case 'status_updated':
                    echo "পরীক্ষার প্যাটার্নের স্ট্যাটাস সফলভাবে পরিবর্তন করা হয়েছে।";
                    break;
                default:
                    echo "অপারেশন সফল হয়েছে।";
            }
            ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i> <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Add/Edit Form -->
        <div class="col-md-5 mb-4">
            <?php include_once 'templates/simple_exam_pattern_form.php'; ?>
            <script src="js/simple_exam_pattern.js"></script>
        </div>

        <!-- Pattern List -->
        <div class="col-md-7">
            <div class="card animate__animated animate__fadeInRight shadow">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i> পরীক্ষার প্যাটার্ন তালিকা
                        </h5>
                        <div class="input-group input-group-sm" style="width: 200px;">
                            <input type="text" class="form-control" id="searchInput" placeholder="সার্চ করুন...">
                            <span class="input-group-text bg-white">
                                <i class="fas fa-search"></i>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="patternTable">
                            <thead class="table-light">
                                <tr>
                                    <th class="fw-bold">বিষয়</th>
                                    <th class="text-center fw-bold">সিকিউ</th>
                                    <th class="text-center fw-bold">এমসিকিউ</th>
                                    <th class="text-center fw-bold">ব্যবহারিক</th>
                                    <th class="text-center fw-bold">মোট</th>
                                    <th class="text-center fw-bold">স্ট্যাটাস</th>
                                    <th class="text-center fw-bold">অ্যাকশন</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($patterns && $patterns->num_rows > 0): ?>
                                    <?php while ($pattern = $patterns->fetch_assoc()): ?>
                                        <tr class="align-middle">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="icon-circle bg-primary text-white p-2 me-2 rounded-circle">
                                                        <i class="fas fa-book"></i>
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold"><?php echo htmlspecialchars($pattern['subject_name']); ?></div>
                                                        <div class="small text-muted"><?php echo htmlspecialchars($pattern['subject_code']); ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="text-center">
                                                <?php if ($pattern['has_cq']): ?>
                                                    <span class="badge bg-primary rounded-pill"><?php echo $pattern['cq_marks']; ?></span>
                                                <?php else: ?>
                                                    <span class="badge bg-light text-dark">নেই</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-center">
                                                <?php if ($pattern['has_mcq']): ?>
                                                    <span class="badge bg-success rounded-pill"><?php echo $pattern['mcq_marks']; ?></span>
                                                <?php else: ?>
                                                    <span class="badge bg-light text-dark">নেই</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-center">
                                                <?php if ($pattern['has_practical']): ?>
                                                    <span class="badge bg-info rounded-pill"><?php echo $pattern['practical_marks']; ?></span>
                                                <?php else: ?>
                                                    <span class="badge bg-light text-dark">নেই</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-dark rounded-pill"><?php echo $pattern['total_marks']; ?></span>
                                            </td>
                                            <td class="text-center">
                                                <?php
                                                $statusClass = isset($pattern['is_active']) && $pattern['is_active'] == 1 ? 'success' : 'danger';
                                                $statusText = isset($pattern['is_active']) && $pattern['is_active'] == 1 ? 'সক্রিয়' : 'নিষ্ক্রিয়';
                                                $statusIcon = isset($pattern['is_active']) && $pattern['is_active'] == 1 ? 'check-circle' : 'times-circle';
                                                $newStatus = isset($pattern['is_active']) && $pattern['is_active'] == 1 ? 0 : 1;
                                                ?>
                                                <form method="POST" action="" class="status-toggle-form">
                                                    <input type="hidden" name="pattern_id" value="<?php echo $pattern['id']; ?>">
                                                    <input type="hidden" name="new_status" value="<?php echo $newStatus; ?>">
                                                    <button type="submit" name="toggle_status" class="btn btn-sm btn-<?php echo $statusClass; ?>" data-bs-toggle="tooltip" title="স্ট্যাটাস পরিবর্তন করুন">
                                                        <i class="fas fa-<?php echo $statusIcon; ?> me-1"></i> <?php echo $statusText; ?>
                                                    </button>
                                                </form>
                                            </td>
                                            <td class="text-center">
                                                <div class="btn-group btn-group-sm">
                                                    <a href="subject_exam_pattern.php?edit=<?php echo $pattern['id']; ?>" class="btn btn-primary" data-bs-toggle="tooltip" title="সম্পাদনা করুন">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?php echo $pattern['id']; ?>" title="মুছে ফেলুন">
                                                        <i class="fas fa-trash-alt"></i>
                                                    </button>
                                                </div>

                                                <!-- Delete Modal -->
                                                <div class="modal fade" id="deleteModal<?php echo $pattern['id']; ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?php echo $pattern['id']; ?>" aria-hidden="true">
                                                    <div class="modal-dialog modal-dialog-centered">
                                                        <div class="modal-content">
                                                            <div class="modal-header bg-danger text-white">
                                                                <h5 class="modal-title" id="deleteModalLabel<?php echo $pattern['id']; ?>">
                                                                    <i class="fas fa-exclamation-triangle me-2"></i> নিশ্চিতকরণ
                                                                </h5>
                                                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                                            </div>
                                                            <div class="modal-body p-4">
                                                                <div class="text-center mb-4">
                                                                    <i class="fas fa-trash-alt fa-4x text-danger mb-3"></i>
                                                                    <h5 class="mb-2">আপনি কি নিশ্চিত যে আপনি এই পরীক্ষার প্যাটার্ন মুছে ফেলতে চান?</h5>
                                                                    <p class="text-muted">এই কাজটি অপরিবর্তনীয়। একবার মুছে ফেললে, এই ডাটা পুনরুদ্ধার করা যাবে না।</p>
                                                                    <div class="alert alert-warning">
                                                                        <strong>বিষয়:</strong> <?php echo htmlspecialchars($pattern['subject_name'] . ' (' . $pattern['subject_code'] . ')'); ?>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="modal-footer bg-light">
                                                                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                                                    <i class="fas fa-times me-2"></i> বাতিল করুন
                                                                </button>
                                                                <form method="POST" action="">
                                                                    <input type="hidden" name="pattern_id" value="<?php echo $pattern['id']; ?>">
                                                                    <button type="submit" name="delete_pattern" class="btn btn-danger">
                                                                        <i class="fas fa-trash-alt me-2"></i> মুছে ফেলুন
                                                                    </button>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="7" class="text-center py-5">
                                            <div class="py-4">
                                                <i class="fas fa-clipboard-list fa-4x text-muted mb-3"></i>
                                                <h5 class="text-muted">কোন পরীক্ষার প্যাটার্ন পাওয়া যায়নি</h5>
                                                <p class="text-muted">পরীক্ষার প্যাটার্ন যোগ করতে বাম দিকের ফর্মটি ব্যবহার করুন</p>
                                                <button class="btn btn-primary mt-2" onclick="document.getElementById('subject_id').focus()">
                                                    <i class="fas fa-plus-circle me-2"></i> নতুন প্যাটার্ন যোগ করুন
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Class Marks Pattern Configuration Modal -->
<div class="modal fade" id="classMarksPatternModal" tabindex="-1" aria-labelledby="classMarksPatternModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="classMarksPatternModalLabel">
                    <i class="fas fa-cog me-2"></i>ক্লাস অনুযায়ী মার্কস প্যাটার্ন কনফিগারেশন
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="classMarksPatternContent">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
                        <p class="mt-2">লোড হচ্ছে...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>বন্ধ করুন
                </button>
                <button type="button" class="btn btn-primary" onclick="saveClassMarksPattern()">
                    <i class="fas fa-save me-2"></i>সংরক্ষণ করুন
                </button>
            </div>
        </div>
    </div>
</div>

<!-- View Class Marks Pattern Modal -->
<div class="modal fade" id="viewClassMarksPatternModal" tabindex="-1" aria-labelledby="viewClassMarksPatternModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="viewClassMarksPatternModalLabel">
                    <i class="fas fa-eye me-2"></i>ক্লাসের মার্কস প্যাটার্ন দেখুন
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="viewClassMarksPatternContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>বন্ধ করুন
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Template Manager Modal -->
<div class="modal fade" id="templateManagerModal" tabindex="-1" aria-labelledby="templateManagerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="templateManagerModalLabel">
                    <i class="fas fa-cogs me-2"></i>মার্কস ডিস্ট্রিবিউশন টেমপ্লেট ম্যানেজার
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="templateManagerContent">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
                        <p class="mt-2">লোড হচ্ছে...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>বন্ধ করুন
                </button>
                <button type="button" class="btn btn-success" onclick="saveTemplateChanges()">
                    <i class="fas fa-save me-2"></i>পরিবর্তন সংরক্ষণ করুন
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Fix for auto-reload issue
if (window.history.replaceState) {
    window.history.replaceState(null, null, window.location.href);
}

// Immediately execute to fix loading issues
(function() {
    // Force hide all loading elements
    const hideLoaders = function() {
        // Remove any loading elements
        const loadingSelectors = [
            '#page-loader', '.loader', '.loading-spinner', '.loader-spinner', '.loader-text',
            '[id*="loader"]', '[class*="loader"]', '[class*="loading"]'
        ];

        loadingSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                if (element) {
                    element.style.display = 'none';
                    element.style.opacity = '0';
                    element.style.visibility = 'hidden';
                    element.style.height = '0';
                    element.style.width = '0';
                    element.style.position = 'absolute';
                    element.style.zIndex = '-9999';
                    element.style.pointerEvents = 'none';
                }
            });
        });

        // Add loaded class to body
        document.body.classList.add('loaded');
        document.body.classList.remove('loading');

        // Force page to be visible
        document.body.style.display = 'block';
        document.body.style.visibility = 'visible';
        document.body.style.opacity = '1';

        console.log('Loading elements removed');
    };

    // Execute immediately
    hideLoaders();

    // Also execute on DOMContentLoaded
    document.addEventListener('DOMContentLoaded', hideLoaders);

    // Also execute on load
    window.addEventListener('load', hideLoaders);

    // Set interval to keep checking and removing loaders
    setInterval(hideLoaders, 500);
})();

// Initialize tooltips if Bootstrap is loaded
if (typeof bootstrap !== 'undefined') {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
            animation: true,
            delay: { show: 100, hide: 100 }
        });
    });
} else {
    console.error('Bootstrap JS is not loaded');
}

// Calculate total sum of distribution components
function calculateSum() {
    // Check if all required elements exist
    const totalMarksElement = document.getElementById('total_marks');
    const cqMarksElement = document.getElementById('cq_marks');
    const mcqMarksElement = document.getElementById('mcq_marks');
    const practicalMarksElement = document.getElementById('practical_marks');
    const totalSumElement = document.getElementById('total-sum');

    // Progress elements
    const cqProgressElement = document.getElementById('cq-progress');
    const mcqProgressElement = document.getElementById('mcq-progress');
    const practicalProgressElement = document.getElementById('practical-progress');

    // Percentage display elements
    const cqPercentageElement = document.getElementById('cq-percentage');
    const mcqPercentageElement = document.getElementById('mcq-percentage');
    const practicalPercentageElement = document.getElementById('practical-percentage');

    // Toggle checkboxes
    const hasCqCheckbox = document.getElementById('has_cq');
    const hasMcqCheckbox = document.getElementById('has_mcq');
    const hasPracticalCheckbox = document.getElementById('has_practical');

    if (!totalMarksElement || !cqMarksElement || !mcqMarksElement ||
        !practicalMarksElement || !totalSumElement) {
        console.error('One or more required elements not found');
        return;
    }

    let sum = 0;
    const totalMarks = parseFloat(totalMarksElement.value) || 100;

    // Get values from all distribution components, but only count enabled ones
    let cqMarks = 0;
    let mcqMarks = 0;
    let practicalMarks = 0;

    // Only count marks if the component is enabled (checkbox is checked)
    if (hasCqCheckbox && hasCqCheckbox.checked) {
        cqMarks = parseFloat(cqMarksElement.value) || 0;
    }

    if (hasMcqCheckbox && hasMcqCheckbox.checked) {
        mcqMarks = parseFloat(mcqMarksElement.value) || 0;
    }

    if (hasPracticalCheckbox && hasPracticalCheckbox.checked) {
        practicalMarks = parseFloat(practicalMarksElement.value) || 0;
    }

    // Calculate sum of enabled components only
    sum = cqMarks + mcqMarks + practicalMarks;

    // Debug information
    console.log('CQ Enabled:', hasCqCheckbox ? hasCqCheckbox.checked : 'N/A', 'Value:', cqMarks);
    console.log('MCQ Enabled:', hasMcqCheckbox ? hasMcqCheckbox.checked : 'N/A', 'Value:', mcqMarks);
    console.log('Practical Enabled:', hasPracticalCheckbox ? hasPracticalCheckbox.checked : 'N/A', 'Value:', practicalMarks);
    console.log('Total Sum:', sum, 'Total Marks:', totalMarks);

    // Update total sum display
    totalSumElement.textContent = sum.toFixed(2);

    // Calculate percentages
    const cqPercentage = totalMarks > 0 ? (cqMarks / totalMarks) * 100 : 0;
    const mcqPercentage = totalMarks > 0 ? (mcqMarks / totalMarks) * 100 : 0;
    const practicalPercentage = totalMarks > 0 ? (practicalMarks / totalMarks) * 100 : 0;

    // Update percentage displays
    if (cqPercentageElement) cqPercentageElement.textContent = cqPercentage.toFixed(0) + '%';
    if (mcqPercentageElement) mcqPercentageElement.textContent = mcqPercentage.toFixed(0) + '%';
    if (practicalPercentageElement) practicalPercentageElement.textContent = practicalPercentage.toFixed(0) + '%';

    // Update progress bars
    if (cqProgressElement) {
        cqProgressElement.style.width = cqPercentage + '%';
        cqProgressElement.setAttribute('aria-valuenow', cqPercentage);
    }

    if (mcqProgressElement) {
        mcqProgressElement.style.width = mcqPercentage + '%';
        mcqProgressElement.setAttribute('aria-valuenow', mcqPercentage);
    }

    if (practicalProgressElement) {
        practicalProgressElement.style.width = practicalPercentage + '%';
        practicalProgressElement.setAttribute('aria-valuenow', practicalPercentage);
    }

    // Update total sum color based on whether it matches total marks
    if (totalSumElement) {
        if (Math.abs(sum - totalMarks) < 0.01) {
            totalSumElement.classList.remove('bg-danger', 'bg-warning');
            totalSumElement.classList.add('bg-success');
        } else if (sum > totalMarks) {
            totalSumElement.classList.remove('bg-success', 'bg-warning');
            totalSumElement.classList.add('bg-danger');
        } else {
            totalSumElement.classList.remove('bg-success', 'bg-danger');
            totalSumElement.classList.add('bg-warning');
        }
    }

    // Auto-adjust values if needed
    const formElement = document.querySelector('form.needs-validation');
    const submitButton = formElement ? formElement.querySelector('button[type="submit"]') : null;

    if (submitButton) {
        if (Math.abs(sum - totalMarks) < 0.01) {
            // Sum matches total marks, enable submit button
            submitButton.disabled = false;

            // Remove any error message
            const errorElement = document.getElementById('sum-error');
            if (errorElement) {
                errorElement.style.display = 'none';
            }
        } else {
            // Sum doesn't match total marks, disable submit button
            submitButton.disabled = true;

            // Show error message
            let errorElement = document.getElementById('sum-error');
            if (!errorElement) {
                errorElement = document.createElement('div');
                errorElement.id = 'sum-error';
                errorElement.className = 'alert alert-danger mt-3';
                const formGroup = totalSumElement.closest('.card-body');
                if (formGroup) {
                    formGroup.appendChild(errorElement);
                }
            }

            errorElement.style.display = 'block';
            errorElement.innerHTML = `
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>ত্রুটি!</strong> সমস্ত উপাদানের যোগফল মোট মার্কসের সমান হতে হবে। বর্তমান যোগফল: ${sum}, মোট মার্কস: ${totalMarks}
                <button type="button" class="btn btn-sm btn-primary ms-3" id="auto-adjust-button">
                    <i class="fas fa-magic me-1"></i> অটো-অ্যাডজাস্ট করুন
                </button>
            `;

            // Add event listener to auto-adjust button
            const autoAdjustButton = document.getElementById('auto-adjust-button');
            if (autoAdjustButton) {
                autoAdjustButton.addEventListener('click', function() {
                    adjustAllComponents();
                    calculateSum();
                });
            }
        }
    }
}

// Toggle component function
function toggleComponent(checkbox) {
    const targetId = checkbox.dataset.target;
    const targetElement = document.getElementById(targetId);

    if (targetElement) {
        targetElement.disabled = !checkbox.checked;

        if (!checkbox.checked) {
            // If disabled, set value to 0
            targetElement.value = '0';
        } else if (targetElement.value === '0') {
            // If enabled and value is 0, set default value based on how many components are enabled
            const enabledComponents = document.querySelectorAll('.component-toggle:checked').length;

            // Get total marks
            const totalMarksElement = document.getElementById('total_marks');
            const totalMarks = parseFloat(totalMarksElement.value) || 100;

            if (enabledComponents === 1) {
                // If this is the only enabled component, set it to total marks
                targetElement.value = totalMarks;
            } else if (enabledComponents === 2) {
                // If there are 2 enabled components, distribute marks
                if (targetId === 'cq_marks') targetElement.value = Math.round(totalMarks * 0.7);
                if (targetId === 'mcq_marks') targetElement.value = Math.round(totalMarks * 0.3);
                if (targetId === 'practical_marks') targetElement.value = Math.round(totalMarks * 0.3);

                // Adjust other enabled component to make sum equal to total
                adjustOtherComponents(targetId);
            } else {
                // Default distribution for 3 components
                if (targetId === 'cq_marks') targetElement.value = Math.round(totalMarks * 0.6);
                if (targetId === 'mcq_marks') targetElement.value = Math.round(totalMarks * 0.3);
                if (targetId === 'practical_marks') targetElement.value = Math.round(totalMarks * 0.1);

                // Adjust other components to make sum equal to total
                adjustAllComponents();
            }
        }

        // Update calculations
        calculateSum();

        // Update card visibility in summary
        const componentId = targetId.replace('_marks', '');
        const cardElement = document.getElementById(componentId.replace('cq', 'cq') + '-card');
        if (cardElement) {
            if (checkbox.checked) {
                cardElement.classList.remove('d-none');
            } else {
                cardElement.classList.add('d-none');
            }
        }
    }
}

// Adjust other enabled components to make sum equal to total
function adjustOtherComponents(currentTargetId) {
    const totalMarksElement = document.getElementById('total_marks');
    const totalMarks = parseFloat(totalMarksElement.value) || 100;

    const cqMarksElement = document.getElementById('cq_marks');
    const mcqMarksElement = document.getElementById('mcq_marks');
    const practicalMarksElement = document.getElementById('practical_marks');

    const hasCqCheckbox = document.getElementById('has_cq');
    const hasMcqCheckbox = document.getElementById('has_mcq');
    const hasPracticalCheckbox = document.getElementById('has_practical');

    let currentValue = 0;
    if (currentTargetId === 'cq_marks' && !cqMarksElement.disabled) {
        currentValue = parseFloat(cqMarksElement.value) || 0;
    } else if (currentTargetId === 'mcq_marks' && !mcqMarksElement.disabled) {
        currentValue = parseFloat(mcqMarksElement.value) || 0;
    } else if (currentTargetId === 'practical_marks' && !practicalMarksElement.disabled) {
        currentValue = parseFloat(practicalMarksElement.value) || 0;
    }

    const remainingValue = totalMarks - currentValue;

    // Find the other enabled component and set its value
    if (currentTargetId !== 'cq_marks' && hasCqCheckbox.checked) {
        cqMarksElement.value = remainingValue;
    } else if (currentTargetId !== 'mcq_marks' && hasMcqCheckbox.checked) {
        mcqMarksElement.value = remainingValue;
    } else if (currentTargetId !== 'practical_marks' && hasPracticalCheckbox.checked) {
        practicalMarksElement.value = remainingValue;
    }
}

// Adjust all components to make sum equal to total
function adjustAllComponents() {
    const totalMarksElement = document.getElementById('total_marks');
    const totalMarks = parseFloat(totalMarksElement.value) || 100;

    const cqMarksElement = document.getElementById('cq_marks');
    const mcqMarksElement = document.getElementById('mcq_marks');
    const practicalMarksElement = document.getElementById('practical_marks');

    const hasCqCheckbox = document.getElementById('has_cq');
    const hasMcqCheckbox = document.getElementById('has_mcq');
    const hasPracticalCheckbox = document.getElementById('has_practical');

    // Count enabled components
    let enabledCount = 0;
    if (hasCqCheckbox.checked) enabledCount++;
    if (hasMcqCheckbox.checked) enabledCount++;
    if (hasPracticalCheckbox.checked) enabledCount++;

    if (enabledCount === 0) return;

    // Calculate values based on enabled components
    if (enabledCount === 1) {
        // If only one component is enabled, set it to total marks
        if (hasCqCheckbox.checked) cqMarksElement.value = totalMarks;
        if (hasMcqCheckbox.checked) mcqMarksElement.value = totalMarks;
        if (hasPracticalCheckbox.checked) practicalMarksElement.value = totalMarks;
    } else if (enabledCount === 2) {
        // If two components are enabled, distribute 70/30
        let firstValue = Math.round(totalMarks * 0.7);
        let secondValue = totalMarks - firstValue;

        if (hasCqCheckbox.checked && hasMcqCheckbox.checked) {
            cqMarksElement.value = firstValue;
            mcqMarksElement.value = secondValue;
        } else if (hasCqCheckbox.checked && hasPracticalCheckbox.checked) {
            cqMarksElement.value = firstValue;
            practicalMarksElement.value = secondValue;
        } else if (hasMcqCheckbox.checked && hasPracticalCheckbox.checked) {
            mcqMarksElement.value = firstValue;
            practicalMarksElement.value = secondValue;
        }
    } else if (enabledCount === 3) {
        // If all three components are enabled, distribute 60/30/10
        let cqValue = Math.round(totalMarks * 0.6);
        let mcqValue = Math.round(totalMarks * 0.3);
        let practicalValue = totalMarks - cqValue - mcqValue;

        cqMarksElement.value = cqValue;
        mcqMarksElement.value = mcqValue;
        practicalMarksElement.value = practicalValue;
    }
}

// Add event listeners to all distribution components if they exist
const cqMarksElement = document.getElementById('cq_marks');
const mcqMarksElement = document.getElementById('mcq_marks');
const practicalMarksElement = document.getElementById('practical_marks');
const totalMarksElement = document.getElementById('total_marks');

// Add event listeners to input fields
if (cqMarksElement) cqMarksElement.addEventListener('input', calculateSum);
if (mcqMarksElement) mcqMarksElement.addEventListener('input', calculateSum);
if (practicalMarksElement) practicalMarksElement.addEventListener('input', calculateSum);
if (totalMarksElement) totalMarksElement.addEventListener('input', calculateSum);

// Add event listeners to toggle checkboxes
const toggleCheckboxes = document.querySelectorAll('.component-toggle');
toggleCheckboxes.forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        toggleComponent(this);
    });
});

// Initialize component states
toggleCheckboxes.forEach(checkbox => {
    toggleComponent(checkbox);
});

// Calculate initial sum
calculateSum();

// Search functionality
const searchInput = document.getElementById('searchInput');
if (searchInput) {
    searchInput.addEventListener('keyup', function() {
        const searchValue = this.value.toLowerCase();
        const table = document.getElementById('patternTable');
        const rows = table.getElementsByTagName('tr');

        for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            const cells = row.getElementsByTagName('td');

            if (cells.length > 0) {
                const subjectCell = cells[0].textContent.toLowerCase();

                if (subjectCell.includes(searchValue)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        }
    });
}

// Status toggle button animation
document.querySelectorAll('.status-toggle-form button').forEach(button => {
    button.addEventListener('click', function() {
        // Add loading spinner
        const originalContent = this.innerHTML;
        this.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> পরিবর্তন হচ্ছে...';
        this.disabled = true;

        // Submit the form
        this.closest('form').submit();
    });
});

// Class-wise subject marks pattern functions
function loadClassSubjects() {
    const classId = document.getElementById('class_filter').value;
    const departmentId = document.getElementById('department_filter').value;

    const configureBtn = document.getElementById('configureBtn');
    const viewBtn = document.getElementById('viewBtn');
    const summaryDiv = document.getElementById('classSubjectsSummary');

    if (!classId) {
        configureBtn.disabled = true;
        viewBtn.disabled = true;
        summaryDiv.style.display = 'none';
        // Auto-select template based on class
        autoSelectTemplate();
        return;
    }

    // Show loading in summary
    summaryDiv.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> লোড হচ্ছে...</div>';
    summaryDiv.style.display = 'block';

    // Auto-select template based on class
    autoSelectTemplate();

    // Fetch class subjects summary
    fetch('get_class_subjects_summary.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `class_id=${classId}&department_id=${departmentId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayClassSubjectsSummary(data);
            configureBtn.disabled = false;
            viewBtn.disabled = false;
        } else {
            summaryDiv.innerHTML = '<div class="alert alert-warning">এই ক্লাসের জন্য কোন বিষয় কনফিগার করা হয়নি।</div>';
            configureBtn.disabled = true;
            viewBtn.disabled = true;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        summaryDiv.innerHTML = '<div class="alert alert-danger">ডেটা লোড করতে সমস্যা হয়েছে।</div>';
        configureBtn.disabled = true;
        viewBtn.disabled = true;
    });
}

// Auto-select template based on class name
function autoSelectTemplate() {
    const classSelect = document.getElementById('class_filter');
    const templateSelect = document.getElementById('template_filter');

    if (!classSelect.value) {
        templateSelect.value = '';
        loadTemplateInfo();
        return;
    }

    const className = classSelect.options[classSelect.selectedIndex].text.toLowerCase();

    // Auto-select template based on class name patterns
    if (className.includes('১') || className.includes('২') || className.includes('৩') ||
        className.includes('৪') || className.includes('৫') || className.includes('1') ||
        className.includes('2') || className.includes('3') || className.includes('4') || className.includes('5')) {
        templateSelect.value = 'primary_1_5';
    } else if (className.includes('৬') || className.includes('৭') || className.includes('৮') ||
               className.includes('6') || className.includes('7') || className.includes('8')) {
        templateSelect.value = 'junior_6_8';
    } else if (className.includes('৯') || className.includes('১০') || className.includes('9') ||
               className.includes('10') || className.includes('ssc')) {
        templateSelect.value = 'ssc_9_10';
    } else if (className.includes('১১') || className.includes('১২') || className.includes('11') ||
               className.includes('12') || className.includes('hsc') || className.includes('একাদশ') || className.includes('দ্বাদশ')) {
        templateSelect.value = 'hsc_11_12';
    }

    loadTemplateInfo();
}

// Load template information
function loadTemplateInfo() {
    const templateSelect = document.getElementById('template_filter');
    const templateInfo = document.getElementById('templateInfo');
    const templateDetails = document.getElementById('templateDetails');

    if (!templateSelect.value) {
        templateInfo.style.display = 'none';
        return;
    }

    const selectedOption = templateSelect.options[templateSelect.selectedIndex];
    const templateData = JSON.parse(selectedOption.dataset.template);

    let html = '<div class="row">';
    html += '<div class="col-md-6">';
    html += '<strong>' + templateData.class_level_name + '</strong><br>';
    html += '<small class="text-muted">' + templateData.description + '</small>';
    html += '</div>';
    html += '<div class="col-md-6">';
    html += '<strong>ডিফল্ট বিতরণ:</strong><br>';

    if (templateData.has_cq == 1) {
        html += '<span class="badge bg-success me-1">সৃজনশীল: ' + templateData.cq_percentage + '%</span>';
    }
    if (templateData.has_mcq == 1) {
        html += '<span class="badge bg-info me-1">MCQ: ' + templateData.mcq_percentage + '%</span>';
    }
    if (templateData.has_practical == 1) {
        html += '<span class="badge bg-warning me-1">ব্যবহারিক: ' + templateData.practical_percentage + '%</span>';
    }

    html += '</div>';
    html += '</div>';

    templateDetails.innerHTML = html;
    templateInfo.style.display = 'block';
}

function displayClassSubjectsSummary(data) {
    const summaryDiv = document.getElementById('classSubjectsSummary');

    let html = '<div class="alert alert-info">';
    html += '<h6><i class="fas fa-info-circle me-2"></i>ক্লাসের বিষয় সারসংক্ষেপ:</h6>';
    html += '<ul class="mb-0">';
    html += `<li>মোট বিষয়: <strong>${data.total_subjects}</strong></li>`;
    html += `<li>আবশ্যিক বিষয়: <strong>${data.required_subjects}</strong></li>`;
    html += `<li>ঐচ্ছিক বিষয়: <strong>${data.optional_subjects}</strong></li>`;
    html += `<li>চতুর্থ বিষয়: <strong>${data.fourth_subjects}</strong></li>`;
    html += '</ul>';
    html += '</div>';

    summaryDiv.innerHTML = html;
}

function configureClassMarksPattern() {
    const classId = document.getElementById('class_filter').value;
    const departmentId = document.getElementById('department_filter').value;
    const templateId = document.getElementById('template_filter').value;

    if (!classId) {
        alert('অনুগ্রহ করে একটি ক্লাস নির্বাচন করুন।');
        return;
    }

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('classMarksPatternModal'));
    modal.show();

    // Load content
    const contentDiv = document.getElementById('classMarksPatternContent');
    contentDiv.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x text-primary"></i><p class="mt-2">লোড হচ্ছে...</p></div>';

    // Fetch class subjects for marks pattern configuration
    fetch('get_class_marks_pattern.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `class_id=${classId}&department_id=${departmentId}&template_id=${templateId}`
    })
    .then(response => response.text())
    .then(html => {
        contentDiv.innerHTML = html;
    })
    .catch(error => {
        console.error('Error:', error);
        contentDiv.innerHTML = '<div class="alert alert-danger">কনটেন্ট লোড করতে সমস্যা হয়েছে।</div>';
    });
}

// Show template manager
function showTemplateManager() {
    const modal = new bootstrap.Modal(document.getElementById('templateManagerModal'));
    modal.show();

    const contentDiv = document.getElementById('templateManagerContent');
    contentDiv.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x text-primary"></i><p class="mt-2">লোড হচ্ছে...</p></div>';

    fetch('template_manager.php')
    .then(response => response.text())
    .then(html => {
        contentDiv.innerHTML = html;
    })
    .catch(error => {
        console.error('Error:', error);
        contentDiv.innerHTML = '<div class="alert alert-danger">টেমপ্লেট ম্যানেজার লোড করতে সমস্যা হয়েছে।</div>';
    });
}

// Save template changes
function saveTemplateChanges() {
    // This function will be implemented in template_manager.php
    alert('টেমপ্লেট সংরক্ষণ ফাংশন শীঘ্রই যোগ করা হবে।');
}

function viewClassMarksPattern() {
    const classId = document.getElementById('class_filter').value;
    const departmentId = document.getElementById('department_filter').value;

    if (!classId) {
        alert('অনুগ্রহ করে একটি ক্লাস নির্বাচন করুন।');
        return;
    }

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('viewClassMarksPatternModal'));
    modal.show();

    // Load content
    const contentDiv = document.getElementById('viewClassMarksPatternContent');
    contentDiv.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x text-primary"></i><p class="mt-2">লোড হচ্ছে...</p></div>';

    // Fetch class marks pattern view
    fetch('view_class_marks_pattern.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `class_id=${classId}&department_id=${departmentId}`
    })
    .then(response => response.text())
    .then(html => {
        contentDiv.innerHTML = html;
    })
    .catch(error => {
        console.error('Error:', error);
        contentDiv.innerHTML = '<div class="alert alert-danger">কনটেন্ট লোড করতে সমস্যা হয়েছে।</div>';
    });
}

function saveClassMarksPattern() {
    const classId = document.getElementById('class_filter').value;
    const departmentId = document.getElementById('department_filter').value;

    if (!classId) {
        alert('অনুগ্রহ করে একটি ক্লাস নির্বাচন করুন।');
        return;
    }

    // Collect all marks pattern data from the modal
    const patterns = [];
    const patternForms = document.querySelectorAll('#classMarksPatternContent .marks-pattern-form');

    patternForms.forEach(form => {
        const subjectId = form.querySelector('input[name="subject_id"]').value;
        const hasCq = form.querySelector('input[name="has_cq"]').checked;
        const hasMcq = form.querySelector('input[name="has_mcq"]').checked;
        const hasPractical = form.querySelector('input[name="has_practical"]').checked;
        const cqMarks = form.querySelector('input[name="cq_marks"]').value;
        const mcqMarks = form.querySelector('input[name="mcq_marks"]').value;
        const practicalMarks = form.querySelector('input[name="practical_marks"]').value;
        const totalMarks = form.querySelector('input[name="total_marks"]').value;

        patterns.push({
            subject_id: subjectId,
            has_cq: hasCq,
            has_mcq: hasMcq,
            has_practical: hasPractical,
            cq_marks: cqMarks,
            mcq_marks: mcqMarks,
            practical_marks: practicalMarks,
            total_marks: totalMarks
        });
    });

    if (patterns.length === 0) {
        alert('কোন মার্কস প্যাটার্ন পাওয়া যায়নি।');
        return;
    }

    // Save via AJAX
    fetch('save_class_marks_pattern.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            class_id: classId,
            department_id: departmentId,
            patterns: patterns
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('মার্কস প্যাটার্ন সফলভাবে সংরক্ষিত হয়েছে!');
            document.getElementById('classMarksPatternModal').querySelector('.btn-close').click();
            // Refresh the page to show updated patterns
            location.reload();
        } else {
            alert('সংরক্ষণ করতে সমস্যা হয়েছে: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('নেটওয়ার্ক সমস্যা হয়েছে।');
    });
}
</script>

</body>
</html>

<?php include_once '../includes/footer.php'; ?>