<?php
// Database Connection
require_once 'includes/dbh.inc.php';

echo "<h1>Notices Table Check</h1>";

// Check if notices table exists
$sql = "SHOW TABLES LIKE 'notices'";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    echo "<p style='color:green;'>Notices table exists in the database!</p>";
    
    // Count notices
    $sql = "SELECT COUNT(*) as count FROM notices";
    $result = $conn->query($sql);
    
    if ($result) {
        $row = $result->fetch_assoc();
        echo "<p>Total notices: " . $row['count'] . "</p>";
        
        // Get notices structure
        $sql = "DESCRIBE notices";
        $result = $conn->query($sql);
        
        if ($result) {
            echo "<h3>Notices Table Structure:</h3>";
            echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
            
            while ($row = $result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['Field'] . "</td>";
                echo "<td>" . $row['Type'] . "</td>";
                echo "<td>" . $row['Null'] . "</td>";
                echo "<td>" . $row['Key'] . "</td>";
                echo "<td>" . $row['Default'] . "</td>";
                echo "<td>" . $row['Extra'] . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        }
        
        // Show some sample notices
        $sql = "SELECT * FROM notices ORDER BY id DESC LIMIT 5";
        $result = $conn->query($sql);
        
        if ($result && $result->num_rows > 0) {
            echo "<h3>Recent Notices:</h3>";
            echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Title</th><th>Date</th></tr>";
            
            while ($row = $result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['id'] . "</td>";
                echo "<td>" . htmlspecialchars($row['title'] ?? 'No Title') . "</td>";
                echo "<td>" . ($row['date'] ?? 'No Date') . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        } else {
            echo "<p style='color:orange;'>No notices found in the table.</p>";
        }
    } else {
        echo "<p style='color:red;'>Error counting notices: " . $conn->error . "</p>";
    }
} else {
    echo "<p style='color:red;'>Notices table does not exist in the database!</p>";
    
    // Show all tables
    $sql = "SHOW TABLES";
    $result = $conn->query($sql);
    
    if ($result) {
        echo "<h3>Available Tables:</h3>";
        echo "<ul>";
        
        while ($row = $result->fetch_row()) {
            echo "<li>" . $row[0] . "</li>";
        }
        
        echo "</ul>";
    }
}

// Check admin/notices.php file
$noticesFile = "admin/notices.php";
if (file_exists($noticesFile)) {
    echo "<p style='color:green;'>The admin/notices.php file exists.</p>";
    echo "<p>File size: " . filesize($noticesFile) . " bytes</p>";
} else {
    echo "<p style='color:red;'>The admin/notices.php file does not exist!</p>";
}

// Check if we can access notices.php from admin panel
echo "<p>Try accessing: <a href='admin/notices.php' target='_blank'>admin/notices.php</a></p>";

// Check if there's an announcements table (alternative to notices)
$sql = "SHOW TABLES LIKE 'announcements'";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    echo "<p style='color:green;'>Announcements table exists as an alternative to notices.</p>";
    
    // Show link to announcements
    echo "<p>Try accessing: <a href='admin/announcements.php' target='_blank'>admin/announcements.php</a></p>";
}

$conn->close();
?> 