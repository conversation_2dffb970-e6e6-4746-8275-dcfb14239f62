<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template Edit Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
<div class="container mt-4">
    <h2><i class="fas fa-cogs me-2"></i>Template Edit Functionality Test</h2>
    
    <?php
    session_start();
    require_once '../includes/dbh.inc.php';
    
    // Get all templates
    $templatesQuery = "SELECT * FROM class_level_templates ORDER BY class_level_name";
    $templates = $conn->query($templatesQuery);
    
    if ($templates && $templates->num_rows > 0) {
        echo "<div class='alert alert-info'>";
        echo "<i class='fas fa-info-circle me-2'></i>";
        echo "Found " . $templates->num_rows . " templates. Click any 'Edit' button to test edit functionality.";
        echo "</div>";
        
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped table-hover'>";
        echo "<thead class='table-dark'>";
        echo "<tr>";
        echo "<th>ID</th>";
        echo "<th>Template Name</th>";
        echo "<th>Total Marks</th>";
        echo "<th>CQ</th>";
        echo "<th>MCQ</th>";
        echo "<th>Practical</th>";
        echo "<th>Status</th>";
        echo "<th>Actions</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        while ($template = $templates->fetch_assoc()) {
            echo "<tr>";
            echo "<td><strong>" . $template['id'] . "</strong></td>";
            echo "<td>";
            echo "<strong>" . htmlspecialchars($template['class_level_name']) . "</strong><br>";
            echo "<small class='text-muted'>" . htmlspecialchars($template['class_level']) . "</small>";
            echo "</td>";
            echo "<td><span class='badge bg-primary'>" . $template['default_total_marks'] . "</span></td>";
            echo "<td>";
            if ($template['has_cq']) {
                echo "<span class='badge bg-success'>" . $template['cq_percentage'] . "%</span><br>";
                echo "<small>(" . $template['default_cq_marks'] . ")</small>";
            } else {
                echo "<span class='badge bg-secondary'>N/A</span>";
            }
            echo "</td>";
            echo "<td>";
            if ($template['has_mcq']) {
                echo "<span class='badge bg-info'>" . $template['mcq_percentage'] . "%</span><br>";
                echo "<small>(" . $template['default_mcq_marks'] . ")</small>";
            } else {
                echo "<span class='badge bg-secondary'>N/A</span>";
            }
            echo "</td>";
            echo "<td>";
            if ($template['has_practical']) {
                echo "<span class='badge bg-warning'>" . $template['practical_percentage'] . "%</span><br>";
                echo "<small>(" . $template['default_practical_marks'] . ")</small>";
            } else {
                echo "<span class='badge bg-secondary'>N/A</span>";
            }
            echo "</td>";
            echo "<td>";
            if ($template['is_active']) {
                echo "<span class='badge bg-success'>Active</span>";
            } else {
                echo "<span class='badge bg-danger'>Inactive</span>";
            }
            echo "</td>";
            echo "<td>";
            echo "<div class='btn-group btn-group-sm'>";
            echo "<button onclick=\"testEditTemplate(" . $template['id'] . ")\" class='btn btn-primary' title='Test Edit'>";
            echo "<i class='fas fa-edit'></i> Edit";
            echo "</button>";
            echo "<button onclick=\"testEditInNewTab(" . $template['id'] . ")\" class='btn btn-outline-primary' title='Edit in New Tab'>";
            echo "<i class='fas fa-external-link-alt'></i>";
            echo "</button>";
            echo "<button onclick=\"debugTemplate(" . $template['id'] . ")\" class='btn btn-info' title='Debug Template'>";
            echo "<i class='fas fa-bug'></i>";
            echo "</button>";
            echo "</div>";
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-warning'>";
        echo "<i class='fas fa-exclamation-triangle me-2'></i>";
        echo "No templates found in database.";
        echo "</div>";
    }
    ?>
    
    <div class="mt-4">
        <h4><i class="fas fa-tools me-2"></i>Quick Actions</h4>
        <div class="btn-group">
            <a href="template_manager.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Template Manager
            </a>
            <button onclick="testAddTemplate()" class="btn btn-success">
                <i class="fas fa-plus me-2"></i>Test Add Template
            </button>
            <button onclick="checkEditFunctionality()" class="btn btn-warning">
                <i class="fas fa-check me-2"></i>Check Edit Files
            </button>
        </div>
    </div>
    
    <!-- Debug Console -->
    <div class="mt-4">
        <h5><i class="fas fa-terminal me-2"></i>Debug Console</h5>
        <div id="debugConsole" class="bg-dark text-light p-3 rounded" style="height: 200px; overflow-y: auto; font-family: monospace;">
            <div class="text-success">Template edit test console initialized...</div>
        </div>
    </div>
</div>

<script>
// Debug console functions
function logToConsole(message, type = 'info') {
    const console = document.getElementById('debugConsole');
    const timestamp = new Date().toLocaleTimeString();
    const colorClass = type === 'error' ? 'text-danger' : type === 'success' ? 'text-success' : type === 'warning' ? 'text-warning' : 'text-info';
    
    console.innerHTML += `<div class="${colorClass}">[${timestamp}] ${message}</div>`;
    console.scrollTop = console.scrollHeight;
}

// Test edit template in same tab
function testEditTemplate(id) {
    logToConsole(`Testing edit for template ID: ${id}`, 'info');
    window.location.href = `edit_template.php?id=${id}`;
}

// Test edit template in new tab
function testEditInNewTab(id) {
    logToConsole(`Opening edit for template ID: ${id} in new tab`, 'info');
    window.open(`edit_template.php?id=${id}`, '_blank');
}

// Debug specific template
function debugTemplate(id) {
    logToConsole(`Debugging template ID: ${id}`, 'warning');
    
    // Fetch template data
    fetch(`edit_template.php?id=${id}`)
    .then(response => response.text())
    .then(html => {
        const hasForm = html.includes('<form');
        const hasInputs = html.includes('input');
        const hasSubmitButton = html.includes('type="submit"');
        
        if (hasForm && hasInputs && hasSubmitButton) {
            logToConsole(`Template ${id}: Edit form structure is correct`, 'success');
        } else {
            logToConsole(`Template ${id}: Edit form structure has issues`, 'error');
        }
    })
    .catch(error => {
        logToConsole(`Template ${id}: Error loading edit page - ${error.message}`, 'error');
    });
}

// Test add template functionality
function testAddTemplate() {
    logToConsole('Testing add template functionality...', 'info');
    window.open('template_manager.php', '_blank');
}

// Check edit functionality files
function checkEditFunctionality() {
    logToConsole('Checking edit functionality files...', 'warning');
    
    const filesToCheck = [
        'edit_template.php',
        'js/template_edit.js',
        'toggle_template_status.php',
        'add_template.php'
    ];
    
    filesToCheck.forEach(file => {
        fetch(file, { method: 'HEAD' })
        .then(response => {
            if (response.ok) {
                logToConsole(`✅ ${file} - File exists and accessible`, 'success');
            } else {
                logToConsole(`❌ ${file} - File not accessible (${response.status})`, 'error');
            }
        })
        .catch(error => {
            logToConsole(`❌ ${file} - Error: ${error.message}`, 'error');
        });
    });
}

// Initialize error monitoring
window.addEventListener('error', function(e) {
    logToConsole(`Global JS Error: ${e.message} at ${e.filename}:${e.lineno}`, 'error');
});

// Log page load
document.addEventListener('DOMContentLoaded', function() {
    logToConsole('Template edit test page loaded successfully', 'success');
    logToConsole('Ready to test template edit functionality', 'info');
    
    // Auto-check files on load
    setTimeout(checkEditFunctionality, 1000);
});
</script>

</body>
</html>
