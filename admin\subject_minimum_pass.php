<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Initialize messages
$successMessage = '';
$errorMessage = '';

// Process delete request
if (isset($_GET['delete']) && !empty($_GET['id'])) {
    $configId = intval($_GET['id']);

    // Get subject info before deleting for success message
    $getSubjectQuery = "SELECT s.subject_name FROM subject_minimum_pass p
                        JOIN subjects s ON p.subject_id = s.id
                        WHERE p.id = ?";
    $stmt = $conn->prepare($getSubjectQuery);
    $stmt->bind_param("i", $configId);
    $stmt->execute();
    $result = $stmt->get_result();
    $subjectInfo = $result->fetch_assoc();

    // Delete the configuration
    $deleteQuery = "DELETE FROM subject_minimum_pass WHERE id = ?";
    $stmt = $conn->prepare($deleteQuery);
    $stmt->bind_param("i", $configId);

    if ($stmt->execute()) {
        $successMessage = $subjectInfo ? $subjectInfo['subject_name'] . " এর ন্যূনতম পাস মার্কস সফলভাবে মুছে ফেলা হয়েছে!" : "ন্যূনতম পাস মার্কস সফলভাবে মুছে ফেলা হয়েছে!";
    } else {
        $errorMessage = "ন্যূনতম পাস মার্কস মুছতে সমস্যা হয়েছে: " . $conn->error;
    }
}

// Create subject_minimum_pass table if it doesn't exist
$tableQuery = "CREATE TABLE IF NOT EXISTS subject_minimum_pass (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    subject_id INT(11) NOT NULL,
    cq_min_marks FLOAT DEFAULT 0,
    mcq_min_marks FLOAT DEFAULT 0,
    practical_min_marks FLOAT DEFAULT 0,
    total_min_marks FLOAT DEFAULT 33,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY(subject_id),
    KEY subject_id_2 (subject_id)
)";

if (!$conn->query($tableQuery)) {
    $errorMessage = "টেবিল তৈরি করতে সমস্যা হয়েছে: " . $conn->error;
}

// Process form submission for adding/updating minimum pass marks
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_config'])) {
    $subjectId = $_POST['subject_id'];
    $cqMinMarks = isset($_POST['cq_min_marks']) ? floatval($_POST['cq_min_marks']) : 0;
    $mcqMinMarks = isset($_POST['mcq_min_marks']) ? floatval($_POST['mcq_min_marks']) : 0;
    $practicalMinMarks = isset($_POST['practical_min_marks']) ? floatval($_POST['practical_min_marks']) : 0;
    $totalMinMarks = isset($_POST['total_min_marks']) ? floatval($_POST['total_min_marks']) : 33;

    // Check if configuration already exists for this subject
    $checkQuery = "SELECT id FROM subject_minimum_pass WHERE subject_id = ?";
    $stmt = $conn->prepare($checkQuery);
    $stmt->bind_param("i", $subjectId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // Update existing configuration
        $updateQuery = "UPDATE subject_minimum_pass SET
            cq_min_marks = ?,
            mcq_min_marks = ?,
            practical_min_marks = ?,
            total_min_marks = ?,
            is_active = 1
            WHERE subject_id = ?";

        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("ddddi", $cqMinMarks, $mcqMinMarks, $practicalMinMarks, $totalMinMarks, $subjectId);

        if ($stmt->execute()) {
            $successMessage = "বিষয়ের ন্যূনতম পাস মার্কস সফলভাবে আপডেট করা হয়েছে!";
        } else {
            $errorMessage = "ন্যূনতম পাস মার্কস আপডেট করতে সমস্যা হয়েছে: " . $conn->error;
        }
    } else {
        // Insert new configuration
        $insertQuery = "INSERT INTO subject_minimum_pass (
            subject_id, cq_min_marks, mcq_min_marks, practical_min_marks, total_min_marks
        ) VALUES (?, ?, ?, ?, ?)";

        $stmt = $conn->prepare($insertQuery);
        $stmt->bind_param("idddd", $subjectId, $cqMinMarks, $mcqMinMarks, $practicalMinMarks, $totalMinMarks);

        if ($stmt->execute()) {
            $successMessage = "বিষয়ের ন্যূনতম পাস মার্কস সফলভাবে যোগ করা হয়েছে!";
        } else {
            $errorMessage = "ন্যূনতম পাস মার্কস যোগ করতে সমস্যা হয়েছে: " . $conn->error;
        }
    }
}

// Get all subjects
$subjectsQuery = "SELECT id, subject_name, subject_code FROM subjects WHERE is_active = 1 ORDER BY subject_name";
$subjects = $conn->query($subjectsQuery);

// Get all minimum pass configurations
$configsQuery = "SELECT p.*, s.subject_name, s.subject_code
               FROM subject_minimum_pass p
               JOIN subjects s ON p.subject_id = s.id
               WHERE p.is_active = 1
               ORDER BY s.subject_name";
$configs = $conn->query($configsQuery);

// Get subject exam patterns to show relevant fields
$patternsQuery = "SELECT subject_id, has_cq, has_mcq, has_practical
                FROM subject_exam_pattern
                WHERE is_active = 1";
$patterns = $conn->query($patternsQuery);

$subjectPatterns = [];
if ($patterns && $patterns->num_rows > 0) {
    while ($pattern = $patterns->fetch_assoc()) {
        $subjectPatterns[$pattern['subject_id']] = [
            'has_cq' => $pattern['has_cq'],
            'has_mcq' => $pattern['has_mcq'],
            'has_practical' => $pattern['has_practical']
        ];
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>বিষয় ন্যূনতম পাস মার্কস - কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --accent-color: #4895ef;
            --success-color: #4cc9f0;
            --warning-color: #f72585;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --border-radius: 12px;
            --box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Hind Siliguri', 'Noto Sans Bengali', sans-serif;
            background-color: #f0f2f5;
            color: var(--dark-color);
            line-height: 1.6;
        }

        .card {
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            transition: var(--transition);
            border: none;
            margin-bottom: 20px;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card-header {
            border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
            font-weight: 600;
        }

        .btn {
            border-radius: var(--border-radius);
            padding: 8px 16px;
            font-weight: 500;
            transition: var(--transition);
        }

        .table {
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
        }

        .form-control, .form-select {
            border-radius: var(--border-radius);
            padding: 10px 15px;
            border: 1px solid #ced4da;
        }

        .config-card {
            transition: var(--transition);
            border-left: 4px solid var(--primary-color);
        }

        .config-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }

        .mark-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .cq-badge {
            background-color: #e9f7fe;
            color: #3498db;
        }

        .mcq-badge {
            background-color: #fef5e9;
            color: #f39c12;
        }

        .practical-badge {
            background-color: #e9fef5;
            color: #2ecc71;
        }

        .total-badge {
            background-color: #f0e6ff;
            color: #8e44ad;
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 ms-sm-auto px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">বিষয় ন্যূনতম পাস মার্কস</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="dashboard.php" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="fas fa-tachometer-alt me-1"></i> ড্যাশবোর্ড
                        </a>
                        <a href="exam_dashboard.php" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-th-large me-1"></i> পরীক্ষা ড্যাশবোর্ড
                        </a>
                    </div>
                </div>

                <!-- Exam Navigation Buttons -->
                <?php include 'exam_buttons.php'; ?>

                <!-- Success/Error Messages -->
                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- Add/Edit Minimum Pass Marks Form -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <i class="fas fa-check-circle me-2"></i> ন্যূনতম পাস মার্কস সেটআপ
                            </div>
                            <div class="card-body">
                                <form method="POST" action="">
                                    <div class="mb-3">
                                        <label for="subject_id" class="form-label">বিষয় নির্বাচন করুন</label>
                                        <select class="form-select" id="subject_id" name="subject_id" required>
                                            <option value="">বিষয় নির্বাচন করুন</option>
                                            <?php while ($subject = $subjects->fetch_assoc()): ?>
                                                <option value="<?php echo $subject['id']; ?>"
                                                        data-has-cq="<?php echo isset($subjectPatterns[$subject['id']]) ? $subjectPatterns[$subject['id']]['has_cq'] : 1; ?>"
                                                        data-has-mcq="<?php echo isset($subjectPatterns[$subject['id']]) ? $subjectPatterns[$subject['id']]['has_mcq'] : 1; ?>"
                                                        data-has-practical="<?php echo isset($subjectPatterns[$subject['id']]) ? $subjectPatterns[$subject['id']]['has_practical'] : 0; ?>">
                                                    <?php echo $subject['subject_name']; ?> (<?php echo $subject['subject_code']; ?>)
                                                </option>
                                            <?php endwhile; ?>
                                        </select>
                                    </div>

                                    <div class="mb-3 cq-field">
                                        <label for="cq_min_marks" class="form-label">সৃজনশীল প্রশ্নের ন্যূনতম পাস মার্কস</label>
                                        <input type="number" class="form-control" id="cq_min_marks" name="cq_min_marks" value="0" min="0" step="0.01">
                                        <div class="form-text">০ দিলে কোন ন্যূনতম মার্কস প্রয়োজন হবে না</div>
                                    </div>

                                    <div class="mb-3 mcq-field">
                                        <label for="mcq_min_marks" class="form-label">বহুনির্বাচনী প্রশ্নের ন্যূনতম পাস মার্কস</label>
                                        <input type="number" class="form-control" id="mcq_min_marks" name="mcq_min_marks" value="0" min="0" step="0.01">
                                        <div class="form-text">০ দিলে কোন ন্যূনতম মার্কস প্রয়োজন হবে না</div>
                                    </div>

                                    <div class="mb-3 practical-field">
                                        <label for="practical_min_marks" class="form-label">ব্যবহারিক ন্যূনতম পাস মার্কস</label>
                                        <input type="number" class="form-control" id="practical_min_marks" name="practical_min_marks" value="0" min="0" step="0.01">
                                        <div class="form-text">০ দিলে কোন ন্যূনতম মার্কস প্রয়োজন হবে না</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="total_min_marks" class="form-label">মোট ন্যূনতম পাস মার্কস</label>
                                        <input type="number" class="form-control" id="total_min_marks" name="total_min_marks" value="33" min="0" max="100" step="0.01">
                                        <div class="form-text">সাধারণত ৩৩ বা ৪০ হয়ে থাকে</div>
                                    </div>

                                    <button type="submit" name="save_config" class="btn btn-primary w-100">
                                        <i class="fas fa-save me-2"></i> সংরক্ষণ করুন
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Minimum Pass Marks List -->
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <i class="fas fa-list me-2"></i> বিষয় ন্যূনতম পাস মার্কস তালিকা
                            </div>
                            <div class="card-body">
                                <?php if ($configs && $configs->num_rows > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>বিষয়</th>
                                                    <th>কোড</th>
                                                    <th>ন্যূনতম পাস মার্কস</th>
                                                    <th>অপশন</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php while ($config = $configs->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo $config['subject_name']; ?></td>
                                                        <td><?php echo $config['subject_code']; ?></td>
                                                        <td>
                                                            <div class="d-flex flex-wrap gap-2">
                                                                <?php if ($config['cq_min_marks'] > 0): ?>
                                                                    <span class="mark-badge cq-badge">CQ: <?php echo $config['cq_min_marks']; ?></span>
                                                                <?php endif; ?>

                                                                <?php if ($config['mcq_min_marks'] > 0): ?>
                                                                    <span class="mark-badge mcq-badge">MCQ: <?php echo $config['mcq_min_marks']; ?></span>
                                                                <?php endif; ?>

                                                                <?php if ($config['practical_min_marks'] > 0): ?>
                                                                    <span class="mark-badge practical-badge">Practical: <?php echo $config['practical_min_marks']; ?></span>
                                                                <?php endif; ?>

                                                                <span class="mark-badge total-badge">Total: <?php echo $config['total_min_marks']; ?></span>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group">
                                                                <button class="btn btn-sm btn-outline-primary edit-config"
                                                                        data-id="<?php echo $config['id']; ?>"
                                                                        data-subject-id="<?php echo $config['subject_id']; ?>"
                                                                        data-cq-min="<?php echo $config['cq_min_marks']; ?>"
                                                                        data-mcq-min="<?php echo $config['mcq_min_marks']; ?>"
                                                                        data-practical-min="<?php echo $config['practical_min_marks']; ?>"
                                                                        data-total-min="<?php echo $config['total_min_marks']; ?>">
                                                                    <i class="fas fa-edit"></i>
                                                                </button>
                                                                <a href="javascript:void(0);" class="btn btn-sm btn-outline-danger delete-config"
                                                                   data-id="<?php echo $config['id']; ?>"
                                                                   data-subject="<?php echo $config['subject_name']; ?>">
                                                                    <i class="fas fa-trash-alt"></i>
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i> কোন ন্যূনতম পাস মার্কস কনফিগারেশন যোগ করা হয়নি।
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Show/hide fields based on subject exam pattern
            function updateFields() {
                var selectedOption = $('#subject_id option:selected');
                var hasCQ = selectedOption.data('has-cq') == 1;
                var hasMCQ = selectedOption.data('has-mcq') == 1;
                var hasPractical = selectedOption.data('has-practical') == 1;

                if (hasCQ) {
                    $('.cq-field').show();
                } else {
                    $('.cq-field').hide();
                    $('#cq_min_marks').val(0);
                }

                if (hasMCQ) {
                    $('.mcq-field').show();
                } else {
                    $('.mcq-field').hide();
                    $('#mcq_min_marks').val(0);
                }

                if (hasPractical) {
                    $('.practical-field').show();
                } else {
                    $('.practical-field').hide();
                    $('#practical_min_marks').val(0);
                }
            }

            // Initial update
            updateFields();

            // Update on subject change
            $('#subject_id').on('change', function() {
                updateFields();
            });

            // Edit configuration button click
            $('.edit-config').on('click', function() {
                var subjectId = $(this).data('subject-id');
                var cqMin = $(this).data('cq-min');
                var mcqMin = $(this).data('mcq-min');
                var practicalMin = $(this).data('practical-min');
                var totalMin = $(this).data('total-min');

                $('#subject_id').val(subjectId);
                $('#cq_min_marks').val(cqMin);
                $('#mcq_min_marks').val(mcqMin);
                $('#practical_min_marks').val(practicalMin);
                $('#total_min_marks').val(totalMin);

                updateFields();

                // Scroll to form
                $('html, body').animate({
                    scrollTop: $("#subject_id").offset().top - 100
                }, 500);
            });

            // Delete configuration confirmation
            $('.delete-config').on('click', function() {
                var configId = $(this).data('id');
                var subjectName = $(this).data('subject');

                if (confirm('আপনি কি নিশ্চিত যে আপনি "' + subjectName + '" এর ন্যূনতম পাস মার্কস মুছতে চান?')) {
                    window.location.href = 'subject_minimum_pass.php?delete=1&id=' + configId;
                }
            });

            // Auto-hide success messages after 3 seconds
            setTimeout(function() {
                $('.alert-success').fadeOut('slow');
            }, 3000);
        });
    </script>
</body>
</html>
