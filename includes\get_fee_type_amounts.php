<?php
// Start session
session_start();

// Include database connection
require_once 'dbh.inc.php';

// Check if user is logged in
if (!isset($_SESSION['userId'])) {
    // Return empty array if not logged in
    header('Content-Type: application/json');
    echo json_encode([]);
    exit();
}

// Get all fee types with amounts
$query = "SELECT id, amount FROM fee_types";
$result = $conn->query($query);

$feeTypeAmounts = [];

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        // Convert Bengali numerals to English if needed
        $amount = $row['amount'];
        if (is_string($amount)) {
            $amount = str_replace(['০', '১', '২', '৩', '৪', '৫', '৬', '৭', '৮', '৯'], 
                                ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'], $amount);
        }
        
        $feeTypeAmounts[$row['id']] = $amount;
    }
}

// Return fee type amounts as JSON
header('Content-Type: application/json');
echo json_encode($feeTypeAmounts);
exit();
?>
