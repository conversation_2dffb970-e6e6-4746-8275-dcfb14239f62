<?php
session_start();
require_once '../includes/dbh.inc.php';
require_once '../includes/bkash_config.php';
require_once '../includes/bkash_functions.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Check if transaction ID and fee ID are provided
if (!isset($_GET['trx_id']) || !isset($_GET['fee_id'])) {
    $_SESSION['error'] = 'অবৈধ অনুরোধ!';
    header('Location: fees.php');
    exit();
}

$trxId = $_GET['trx_id'];
$feeId = intval($_GET['fee_id']);

// Get payment details from database
$paymentQuery = "SELECT bp.*, f.fee_type, f.amount as fee_amount, f.paid, 
                s.first_name, s.last_name, s.student_id as roll, c.class_name
                FROM bkash_payments bp
                JOIN fees f ON bp.fee_id = f.id
                JOIN students s ON f.student_id = s.id
                JOIN classes c ON s.class_id = c.id
                WHERE bp.trx_id = ? AND bp.fee_id = ?
                ORDER BY bp.payment_date DESC
                LIMIT 1";
$stmt = $conn->prepare($paymentQuery);
$stmt->bind_param('si', $trxId, $feeId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    $_SESSION['error'] = 'পেমেন্ট রেকর্ড খুঁজে পাওয়া যায়নি!';
    header('Location: fees.php');
    exit();
}

$payment = $result->fetch_assoc();

// Include header
include_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Include sidebar -->
        <?php include_once 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">বিকাশ পেমেন্ট সফল</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="fees.php" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> ফি তালিকায় ফিরে যান
                    </a>
                </div>
            </div>
            
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i> আপনার পেমেন্ট সফলভাবে সম্পন্ন হয়েছে!
            </div>
            
            <div class="row">
                <div class="col-md-8 mx-auto">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">পেমেন্ট রসিদ</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <h6 class="text-muted">শিক্ষার্থী তথ্য</h6>
                                    <p><strong>নাম:</strong> <?= $payment['first_name'] . ' ' . $payment['last_name'] ?></p>
                                    <p><strong>রোল:</strong> <?= $payment['roll'] ?></p>
                                    <p><strong>শ্রেণী:</strong> <?= $payment['class_name'] ?></p>
                                </div>
                                <div class="col-md-6 text-md-end">
                                    <h6 class="text-muted">পেমেন্ট তথ্য</h6>
                                    <p><strong>তারিখ:</strong> <?= date('d/m/Y h:i A', strtotime($payment['payment_date'])) ?></p>
                                    <p><strong>ট্রানজেকশন আইডি:</strong> <?= $payment['trx_id'] ?></p>
                                    <p><strong>পেমেন্ট আইডি:</strong> <?= $payment['payment_id'] ?></p>
                                </div>
                            </div>
                            
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>ফি টাইপ</th>
                                            <th class="text-end">পরিমাণ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><?= $payment['fee_type'] ?></td>
                                            <td class="text-end">৳ <?= number_format($payment['amount'], 2) ?></td>
                                        </tr>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th>মোট</th>
                                            <th class="text-end">৳ <?= number_format($payment['amount'], 2) ?></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            
                            <div class="mt-4 text-center">
                                <p class="text-muted">এই রসিদটি বিকাশ পেমেন্ট গেটওয়ে দ্বারা প্রক্রিয়াকৃত একটি বৈধ পেমেন্টের প্রমাণ।</p>
                                <p class="mb-0">ধন্যবাদ!</p>
                            </div>
                        </div>
                        <div class="card-footer text-center">
                            <button class="btn btn-primary" onclick="window.print()">
                                <i class="fas fa-print me-2"></i> প্রিন্ট করুন
                            </button>
                            <a href="payment_dashboard.php" class="btn btn-secondary ms-2">
                                <i class="fas fa-chart-line me-2"></i> পেমেন্ট ড্যাশবোর্ড
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<?php
// Include footer
include_once 'includes/footer.php';
?>
