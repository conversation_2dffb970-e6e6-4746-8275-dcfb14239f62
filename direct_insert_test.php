<?php
// Include database connection
require_once 'includes/dbh.inc.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Direct SQL Insert Test</h1>";

// Get student information
$studentId = 'STD-601523';
$studentQuery = "SELECT s.*, d.department_name 
                FROM students s 
                LEFT JOIN departments d ON s.department_id = d.id 
                WHERE s.student_id = '$studentId'";
$studentResult = $conn->query($studentQuery);

if ($studentResult && $studentResult->num_rows > 0) {
    $student = $studentResult->fetch_assoc();
    $studentDbId = $student['id'];
    
    echo "<p>Student ID: {$student['student_id']}</p>";
    echo "<p>Student Database ID: {$studentDbId}</p>";
    echo "<p>Name: {$student['first_name']} {$student['last_name']}</p>";
    echo "<p>Department: {$student['department_name']} (ID: {$student['department_id']})</p>";
    
    // Get current session
    $currentSessionQuery = "SELECT id, session_name FROM sessions ORDER BY id DESC LIMIT 1";
    $currentSessionResult = $conn->query($currentSessionQuery);
    
    if ($currentSessionResult && $currentSessionResult->num_rows > 0) {
        $currentSession = $currentSessionResult->fetch_assoc();
        echo "<p>Current Session: {$currentSession['session_name']} (ID: {$currentSession['id']})</p>";
        
        // Check if student_subjects table exists
        $tableExistsQuery = "SHOW TABLES LIKE 'student_subjects'";
        $tableExists = $conn->query($tableExistsQuery)->num_rows > 0;
        
        if (!$tableExists) {
            echo "<p>student_subjects table does not exist. Creating it now...</p>";
            
            // Create the table
            $createTableQuery = "CREATE TABLE student_subjects (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                student_id INT(11) NOT NULL,
                subject_id INT(11) NOT NULL,
                category ENUM('required', 'optional', 'fourth') NOT NULL DEFAULT 'optional',
                session_id INT(11) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            
            if ($conn->query($createTableQuery)) {
                echo "<p>student_subjects table created successfully!</p>";
            } else {
                echo "<p>Error creating student_subjects table: " . $conn->error . "</p>";
                exit;
            }
        } else {
            echo "<p>student_subjects table already exists.</p>";
        }
        
        // Try direct SQL insert
        echo "<h2>Testing Direct SQL Insert</h2>";
        
        // First, delete any existing records for this student
        $deleteQuery = "DELETE FROM student_subjects WHERE student_id = $studentDbId";
        
        if ($conn->query($deleteQuery)) {
            echo "<p>Deleted existing selections for student ID: $studentDbId</p>";
        } else {
            echo "<p>Error deleting existing selections: " . $conn->error . "</p>";
        }
        
        // Insert a test subject
        $testSubjectId = 1; // Assuming subject ID 1 exists
        $category = 'required';
        $sessionId = $currentSession['id'];
        
        $insertQuery = "INSERT INTO student_subjects (student_id, subject_id, category, session_id) 
                       VALUES ($studentDbId, $testSubjectId, '$category', $sessionId)";
        
        if ($conn->query($insertQuery)) {
            echo "<p style='color:green;'>Test subject inserted successfully!</p>";
        } else {
            echo "<p style='color:red;'>Error inserting test subject: " . $conn->error . "</p>";
            
            // Check if the subject exists
            $subjectQuery = "SELECT * FROM subjects WHERE id = $testSubjectId";
            $subjectResult = $conn->query($subjectQuery);
            
            if ($subjectResult && $subjectResult->num_rows > 0) {
                echo "<p>Subject ID $testSubjectId exists in the subjects table.</p>";
            } else {
                echo "<p>Subject ID $testSubjectId does not exist in the subjects table.</p>";
                
                // Get a valid subject ID
                $validSubjectQuery = "SELECT id FROM subjects LIMIT 1";
                $validSubjectResult = $conn->query($validSubjectQuery);
                
                if ($validSubjectResult && $validSubjectResult->num_rows > 0) {
                    $validSubjectId = $validSubjectResult->fetch_assoc()['id'];
                    echo "<p>Found valid subject ID: $validSubjectId</p>";
                    
                    // Try inserting with valid subject ID
                    $insertQuery = "INSERT INTO student_subjects (student_id, subject_id, category, session_id) 
                                   VALUES ($studentDbId, $validSubjectId, '$category', $sessionId)";
                    
                    if ($conn->query($insertQuery)) {
                        echo "<p style='color:green;'>Test subject inserted successfully with valid subject ID!</p>";
                    } else {
                        echo "<p style='color:red;'>Error inserting test subject with valid subject ID: " . $conn->error . "</p>";
                    }
                } else {
                    echo "<p>No valid subjects found in the subjects table.</p>";
                }
            }
        }
        
        // Check if the insert worked
        $checkQuery = "SELECT ss.*, s.subject_name, s.subject_code 
                      FROM student_subjects ss 
                      JOIN subjects s ON ss.subject_id = s.id 
                      WHERE ss.student_id = $studentDbId";
        $checkResult = $conn->query($checkQuery);
        
        if ($checkResult && $checkResult->num_rows > 0) {
            echo "<h3>Inserted Subjects:</h3>";
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>ID</th><th>Subject ID</th><th>Subject Name</th><th>Subject Code</th><th>Category</th><th>Session ID</th></tr>";
            
            while ($subject = $checkResult->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$subject['id']}</td>";
                echo "<td>{$subject['subject_id']}</td>";
                echo "<td>{$subject['subject_name']}</td>";
                echo "<td>{$subject['subject_code']}</td>";
                echo "<td>{$subject['category']}</td>";
                echo "<td>{$subject['session_id']}</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        } else {
            echo "<p>No subjects found for student ID: $studentDbId</p>";
        }
        
        // Try prepared statement
        echo "<h2>Testing Prepared Statement Insert</h2>";
        
        // Delete existing records again
        $conn->query("DELETE FROM student_subjects WHERE student_id = $studentDbId");
        
        // Get a valid subject ID
        $validSubjectQuery = "SELECT id FROM subjects LIMIT 1";
        $validSubjectResult = $conn->query($validSubjectQuery);
        
        if ($validSubjectResult && $validSubjectResult->num_rows > 0) {
            $validSubjectId = $validSubjectResult->fetch_assoc()['id'];
            
            // Prepare statement
            $stmt = $conn->prepare("INSERT INTO student_subjects (student_id, subject_id, category, session_id) VALUES (?, ?, ?, ?)");
            
            if ($stmt) {
                $stmt->bind_param("iisi", $studentDbId, $validSubjectId, $category, $sessionId);
                
                if ($stmt->execute()) {
                    echo "<p style='color:green;'>Test subject inserted successfully using prepared statement!</p>";
                } else {
                    echo "<p style='color:red;'>Error executing prepared statement: " . $stmt->error . "</p>";
                }
                
                $stmt->close();
            } else {
                echo "<p style='color:red;'>Error preparing statement: " . $conn->error . "</p>";
            }
            
            // Check if the insert worked
            $checkResult = $conn->query($checkQuery);
            
            if ($checkResult && $checkResult->num_rows > 0) {
                echo "<h3>Inserted Subjects (Prepared Statement):</h3>";
                echo "<table border='1' cellpadding='5'>";
                echo "<tr><th>ID</th><th>Subject ID</th><th>Subject Name</th><th>Subject Code</th><th>Category</th><th>Session ID</th></tr>";
                
                while ($subject = $checkResult->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td>{$subject['id']}</td>";
                    echo "<td>{$subject['subject_id']}</td>";
                    echo "<td>{$subject['subject_name']}</td>";
                    echo "<td>{$subject['subject_code']}</td>";
                    echo "<td>{$subject['category']}</td>";
                    echo "<td>{$subject['session_id']}</td>";
                    echo "</tr>";
                }
                
                echo "</table>";
            } else {
                echo "<p>No subjects found for student ID: $studentDbId after prepared statement insert</p>";
            }
        } else {
            echo "<p>No valid subjects found in the subjects table for prepared statement test.</p>";
        }
    } else {
        echo "<p>No active session found.</p>";
    }
} else {
    echo "<p>Student not found with ID: $studentId</p>";
}

echo "<h2>Test Complete</h2>";
echo "<p><a href='admin/student_subject_selection.php?id=STD-601523'>Go to Student Subject Selection</a></p>";
?>
