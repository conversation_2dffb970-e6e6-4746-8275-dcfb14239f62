<?php
// Database Connection
require_once 'includes/dbh.inc.php';

echo "<h1>Creating Staff Table</h1>";

// Create staff table
$staffTableQuery = "CREATE TABLE IF NOT EXISTS staff (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    staff_id VARCHAR(20) NOT NULL UNIQUE,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) NULL,
    phone VARCHAR(20) NULL,
    gender ENUM('male', 'female', 'other') NOT NULL,
    dob DATE NULL,
    address TEXT NULL,
    city VARCHAR(50) NULL,
    state VARCHAR(50) NULL,
    postal_code VARCHAR(20) NULL,
    country VARCHAR(50) NULL DEFAULT 'Bangladesh',
    joining_date DATE NULL,
    profile_photo VARCHAR(255) NULL,
    department_id INT(11) NULL,
    designation VARCHAR(100) NULL,
    user_id INT(11) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($staffTableQuery)) {
    echo "<p style='color:green'>Staff table created successfully!</p>";
    
    // Insert a sample staff member
    $checkStaff = $conn->query("SELECT * FROM staff LIMIT 1");
    if ($checkStaff && $checkStaff->num_rows == 0) {
        // Get the first department ID if it exists
        $deptResult = $conn->query("SELECT id FROM departments LIMIT 1");
        $deptId = ($deptResult && $deptResult->num_rows > 0) ? $deptResult->fetch_assoc()['id'] : 1;
        
        $insertStaff = $conn->query("INSERT INTO staff (staff_id, first_name, last_name, email, phone, gender, joining_date, department_id, designation) 
                                   VALUES ('STF-001', 'Jamal', 'Hossain', '<EMAIL>', '01912345678', 'male', CURDATE(), $deptId, 'Office Assistant')");
        if ($insertStaff) {
            echo "<p style='color:green'>Sample staff member created successfully!</p>";
        } else {
            echo "<p style='color:red'>Error creating sample staff member: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Staff records already exist.</p>";
    }
} else {
    echo "<p style='color:red'>Error creating staff table: " . $conn->error . "</p>";
}

echo "<p><a href='admin/dashboard.php' style='background-color: #4CAF50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>Go to Admin Dashboard</a></p>";

// Close connection
$conn->close();
?>
