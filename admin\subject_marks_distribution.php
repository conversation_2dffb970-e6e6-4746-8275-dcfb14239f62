<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if subject_marks_distribution table exists, if not create it
$tableCheckQuery = "SHOW TABLES LIKE 'subject_marks_distribution'";
$tableCheckResult = $conn->query($tableCheckQuery);

if (!$tableCheckResult) {
    $error_message = "টেবিল চেক করতে সমস্যা হয়েছে: " . $conn->error;
} else {
    $tableExists = $tableCheckResult->num_rows > 0;

    if (!$tableExists) {
        $createTableQuery = "CREATE TABLE subject_marks_distribution (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            subject_id INT(11) NOT NULL,
            cq_marks DECIMAL(5,2) DEFAULT 70.00,
            mcq_marks DECIMAL(5,2) DEFAULT 30.00,
            practical_marks DECIMAL(5,2) DEFAULT 0.00,
            total_marks DECIMAL(5,2) DEFAULT 100.00,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY (subject_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        if ($conn->query($createTableQuery)) {
            $success_message = "বিষয় মার্কস ডিস্ট্রিবিউশন টেবিল সফলভাবে তৈরি করা হয়েছে।";
        } else {
            $error_message = "টেবিল তৈরি করতে সমস্যা হয়েছে: " . $conn->error;
        }
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_distribution'])) {
        $subject_id = $_POST['subject_id'];

        // Check which components are enabled
        $enable_cq = isset($_POST['enable_cq']) ? 1 : 0;
        $enable_mcq = isset($_POST['enable_mcq']) ? 1 : 0;
        $enable_practical = isset($_POST['enable_practical']) ? 1 : 0;

        // Only count marks for enabled components
        $cq_marks = $enable_cq ? ($_POST['cq_marks'] ?? 70) : 0;
        $mcq_marks = $enable_mcq ? ($_POST['mcq_marks'] ?? 30) : 0;
        $practical_marks = $enable_practical ? ($_POST['practical_marks'] ?? 0) : 0;

        $total_marks = $_POST['total_marks'] ?? 100;
        $is_active = isset($_POST['is_active']) ? 1 : 0;

        // Debug information
        error_log("Subject ID: $subject_id");
        error_log("CQ Enabled: $enable_cq, CQ Marks: $cq_marks");
        error_log("MCQ Enabled: $enable_mcq, MCQ Marks: $mcq_marks");
        error_log("Practical Enabled: $enable_practical, Practical Marks: $practical_marks");
        error_log("Total Marks: $total_marks");

        // Check if total of all components equals total_marks
        $sum = $cq_marks + $mcq_marks + $practical_marks;

        if (abs($sum - $total_marks) > 0.01) { // Using a small epsilon for floating point comparison
            $error_message = "সমস্ত উপাদানের যোগফল মোট মার্কসের সমান হতে হবে। বর্তমান যোগফল: $sum, মোট মার্কস: $total_marks";
        } else {
            // Check if distribution already exists for this subject
            $checkQuery = "SELECT id FROM subject_marks_distribution WHERE subject_id = ?";
            $stmt = $conn->prepare($checkQuery);
            $stmt->bind_param("i", $subject_id);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                // Update existing distribution
                $updateQuery = "UPDATE subject_marks_distribution
                               SET cq_marks = ?, mcq_marks = ?, practical_marks = ?,
                                   total_marks = ?, is_active = ?, updated_at = NOW()
                               WHERE subject_id = ?";
                $stmt = $conn->prepare($updateQuery);
                $stmt->bind_param("ddddii", $cq_marks, $mcq_marks, $practical_marks,
                                 $total_marks, $is_active, $subject_id);

                if ($stmt->execute()) {
                    $success_message = "বিষয় মার্কস ডিস্ট্রিবিউশন সফলভাবে আপডেট করা হয়েছে।";
                } else {
                    $error_message = "আপডেট করতে সমস্যা হয়েছে: " . $stmt->error;
                }
            } else {
                // Insert new distribution
                $insertQuery = "INSERT INTO subject_marks_distribution
                               (subject_id, cq_marks, mcq_marks, practical_marks, total_marks, is_active)
                               VALUES (?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($insertQuery);
                $stmt->bind_param("iddddi", $subject_id, $cq_marks, $mcq_marks, $practical_marks,
                                $total_marks, $is_active);

                if ($stmt->execute()) {
                    $success_message = "বিষয় মার্কস ডিস্ট্রিবিউশন সফলভাবে যোগ করা হয়েছে।";
                } else {
                    $error_message = "যোগ করতে সমস্যা হয়েছে: " . $stmt->error;
                }
            }

            $stmt->close();
        }
    } elseif (isset($_POST['delete_distribution'])) {
        $distribution_id = $_POST['distribution_id'];

        $deleteQuery = "DELETE FROM subject_marks_distribution WHERE id = ?";
        $stmt = $conn->prepare($deleteQuery);
        $stmt->bind_param("i", $distribution_id);

        if ($stmt->execute()) {
            $success_message = "বিষয় মার্কস ডিস্ট্রিবিউশন সফলভাবে মুছে ফেলা হয়েছে।";
        } else {
            $error_message = "মুছে ফেলতে সমস্যা হয়েছে: " . $stmt->error;
        }

        $stmt->close();
    } elseif (isset($_POST['toggle_status'])) {
        $distribution_id = $_POST['distribution_id'];
        $new_status = $_POST['new_status'];

        $updateQuery = "UPDATE subject_marks_distribution SET is_active = ? WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("ii", $new_status, $distribution_id);

        if ($stmt->execute()) {
            $status_text = $new_status ? "সক্রিয়" : "নিষ্ক্রিয়";
            $success_message = "বিষয় মার্কস ডিস্ট্রিবিউশন সফলভাবে $status_text করা হয়েছে।";
        } else {
            $error_message = "স্ট্যাটাস আপডেট করতে সমস্যা হয়েছে: " . $stmt->error;
        }

        $stmt->close();
    }
}

// Check if subjects table exists
$subjectsTableCheckQuery = "SHOW TABLES LIKE 'subjects'";
$subjectsTableResult = $conn->query($subjectsTableCheckQuery);

if (!$subjectsTableResult) {
    $error_message = "বিষয় টেবিল চেক করতে সমস্যা হয়েছে: " . $conn->error;
    $subjects = null;
} else {
    $subjectsTableExists = $subjectsTableResult->num_rows > 0;

    if (!$subjectsTableExists) {
        $error_message = "বিষয় টেবিল পাওয়া যায়নি। আগে বিষয় যোগ করুন।";
        $subjects = null;
    } else {
        // Get all subjects
        $subjectsQuery = "SELECT id, subject_name, subject_code FROM subjects ORDER BY subject_name";
        $subjects = $conn->query($subjectsQuery);

        if (!$subjects) {
            $error_message = "বিষয় লোড করতে সমস্যা হয়েছে: " . $conn->error;
        }
    }
}

// Get all distributions with subject names if both tables exist
if ($tableExists && $subjectsTableExists) {
    $distributionsQuery = "SELECT d.*, s.subject_name, s.subject_code
                          FROM subject_marks_distribution d
                          JOIN subjects s ON d.subject_id = s.id
                          ORDER BY s.subject_name";
    $distributions = $conn->query($distributionsQuery);

    if (!$distributions) {
        $error_message = "মার্কস ডিস্ট্রিবিউশন লোড করতে সমস্যা হয়েছে: " . $conn->error;
    }
} else {
    $distributions = null;
}

// Get distribution for editing if edit parameter is set
$edit_id = isset($_GET['edit']) ? $_GET['edit'] : null;
$edit_data = null;

if ($edit_id && $tableExists && $subjectsTableExists) {
    $editQuery = "SELECT d.*, s.subject_name, s.subject_code
                 FROM subject_marks_distribution d
                 JOIN subjects s ON d.subject_id = s.id
                 WHERE d.id = ?";
    $stmt = $conn->prepare($editQuery);

    if ($stmt) {
        $stmt->bind_param("i", $edit_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result && $result->num_rows > 0) {
            $edit_data = $result->fetch_assoc();
        }

        $stmt->close();
    } else {
        $error_message = "সম্পাদনা কুয়েরি তৈরি করতে সমস্যা হয়েছে: " . $conn->error;
    }
}

// Include header
include_once '../includes/header.php';
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বিষয় মার্কস ডিস্ট্রিবিউশন</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Hind Siliguri Font -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap">

    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: #f8f9fa;
        }

        /* Fix loading issues - Force hide all loaders */
        body.loading,
        body.loaded {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        #page-loader,
        .loader,
        .loading-spinner,
        .loader-spinner,
        .loader-text,
        [id*="loader"],
        [class*="loader"],
        [class*="loading"] {
            display: none !important;
            opacity: 0 !important;
            visibility: hidden !important;
            height: 0 !important;
            width: 0 !important;
            position: absolute !important;
            z-index: -9999 !important;
            pointer-events: none !important;
        }

        .card {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            border-bottom: none;
            padding: 15px 20px;
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
            padding: 8px 16px;
            transition: all 0.3s;
        }

        .btn-primary {
            background-color: #4361ee;
            border-color: #4361ee;
        }

        .btn-primary:hover {
            background-color: #3a56d4;
            border-color: #3a56d4;
        }

        .form-control, .form-select {
            border-radius: 8px;
            padding: 10px 15px;
            border: 1px solid #dee2e6;
            transition: all 0.3s;
        }

        .form-control:focus, .form-select:focus {
            box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
            border-color: #4361ee;
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .badge {
            padding: 6px 10px;
            font-weight: 500;
        }

        .icon-circle {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .progress {
            height: 10px;
            border-radius: 5px;
        }

        .sidebar {
            background-color: #fff;
            border-right: 1px solid #e9ecef;
            min-height: 100vh;
        }

        .sidebar .nav-link {
            color: #495057;
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 5px;
            transition: all 0.3s;
        }

        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            background-color: #e9ecef;
            color: #4361ee;
        }

        .sidebar .nav-link.active {
            background-color: #4361ee;
            color: #fff;
        }
    </style>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- SheetJS (Excel Export) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>

<!-- Animate.css -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<!-- Debug Info -->
<div class="card mb-4 shadow-sm">
    <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i> সিস্টেম স্ট্যাটাস</h5>
        <button class="btn btn-sm btn-light" type="button" data-bs-toggle="collapse" data-bs-target="#debugInfo" aria-expanded="false">
            <i class="fas fa-chevron-down"></i>
        </button>
    </div>
    <div class="collapse show" id="debugInfo">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-database me-2 text-primary"></i> ডাটাবেস কানেকশন:</span>
                            <span class="badge bg-<?php echo $conn ? 'success' : 'danger'; ?> rounded-pill"><?php echo $conn ? 'সফল' : 'ব্যর্থ'; ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-table me-2 text-primary"></i> subjects টেবিল:</span>
                            <span class="badge bg-<?php echo isset($subjectsTableExists) && $subjectsTableExists ? 'success' : 'danger'; ?> rounded-pill"><?php echo isset($subjectsTableExists) && $subjectsTableExists ? 'আছে' : 'নেই'; ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-table me-2 text-primary"></i> subject_marks_distribution টেবিল:</span>
                            <span class="badge bg-<?php echo isset($tableExists) && $tableExists ? 'success' : 'danger'; ?> rounded-pill"><?php echo isset($tableExists) && $tableExists ? 'আছে' : 'নেই'; ?></span>
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-book me-2 text-primary"></i> বিষয় সংখ্যা:</span>
                            <span class="badge bg-<?php echo $subjects && $subjects->num_rows ? 'success' : 'warning'; ?> rounded-pill"><?php echo $subjects && $subjects->num_rows ? $subjects->num_rows : '0'; ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-chart-pie me-2 text-primary"></i> মার্কস ডিস্ট্রিবিউশন সংখ্যা:</span>
                            <span class="badge bg-<?php echo $distributions && $distributions->num_rows ? 'success' : 'warning'; ?> rounded-pill"><?php echo $distributions && $distributions->num_rows ? $distributions->num_rows : '0'; ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-clock me-2 text-primary"></i> সময়:</span>
                            <span class="badge bg-info rounded-pill"><?php echo date('d/m/Y h:i:s A'); ?></span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="d-flex gap-2 mt-3">
                <a href="check_subjects.php" class="btn btn-primary">
                    <i class="fas fa-book me-1"></i> বিষয় চেক করুন
                </a>
                <a href="fix_database.php" class="btn btn-warning">
                    <i class="fas fa-database me-1"></i> ডাটাবেস ঠিক করুন
                </a>
                <a href="reset_subject_marks_distribution.php" class="btn btn-danger">
                    <i class="fas fa-trash-alt me-1"></i> টেবিল রিসেট করুন
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="text-center mb-4">
                <h3>অ্যাডমিন প্যানেল</h3>
            </div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="students.php">
                        <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="teachers.php">
                        <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="classes.php">
                        <i class="fas fa-school me-2"></i> শ্রেণী
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="subjects.php">
                        <i class="fas fa-book me-2"></i> বিষয়
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="attendance.php">
                        <i class="fas fa-calendar-check me-2"></i> উপস্থিতি
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="manage_exams.php">
                        <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="student_exam_attendance.php">
                        <i class="fas fa-clipboard-check me-2"></i> শিক্ষার্থী হাজিরা পত্র
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="subject_exam_pattern.php">
                        <i class="fas fa-sliders-h me-2"></i> বিষয় পরীক্ষা প্যাটার্ন
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="subject_marks_distribution.php">
                        <i class="fas fa-chart-pie me-2"></i> বিষয় মার্কস ডিস্ট্রিবিউশন
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="subject_minimum_pass.php">
                        <i class="fas fa-check-circle me-2"></i> ন্যূনতম পাস মার্কস
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="results.php">
                        <i class="fas fa-chart-bar me-2"></i> ফলাফল
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="../includes/logout.inc.php">
                        <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                    </a>
                </li>
            </ul>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10 main-content">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-4 border-bottom">
                <h1 class="h2 animate__animated animate__fadeInDown">
                    <i class="fas fa-chart-pie text-primary me-2"></i> বিষয় মার্কস ডিস্ট্রিবিউশন
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0 animate__animated animate__fadeInRight">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="window.print()">
                            <i class="fas fa-print me-1"></i> প্রিন্ট
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportToExcel()">
                            <i class="fas fa-file-excel me-1"></i> এক্সেল
                        </button>
                    </div>
                </div>
            </div>

            <!-- Exam Navigation Buttons -->
            <?php include 'exam_buttons.php'; ?>

            <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeInUp shadow-sm" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle fa-2x me-3"></i>
                        <div>
                            <strong>সফল!</strong> <?php echo $success_message; ?>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeInUp shadow-sm" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                        <div>
                            <strong>ত্রুটি!</strong> <?php echo $error_message; ?>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- Add/Edit Form -->
                <div class="col-md-5 mb-4">
                    <div class="card animate__animated animate__fadeInLeft shadow">
                        <div class="card-header bg-primary text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-<?php echo $edit_data ? 'edit' : 'plus-circle'; ?> me-2"></i>
                                    <?php echo $edit_data ? 'মার্কস ডিস্ট্রিবিউশন আপডেট করুন' : 'নতুন মার্কস ডিস্ট্রিবিউশন যোগ করুন'; ?>
                                </h5>
                                <?php if ($edit_data): ?>
                                    <a href="subject_marks_distribution.php" class="btn btn-sm btn-light">
                                        <i class="fas fa-plus-circle me-1"></i> নতুন যোগ করুন
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="" class="needs-validation" novalidate>
                                <?php if ($edit_data): ?>
                                    <input type="hidden" name="distribution_id" value="<?php echo $edit_data['id']; ?>">
                                <?php endif; ?>

                                <div class="mb-4">
                                    <label for="subject_id" class="form-label">
                                        <i class="fas fa-book me-1 text-primary"></i> বিষয় নির্বাচন করুন
                                    </label>
                                    <select class="form-select shadow-sm" id="subject_id" name="subject_id" required <?php echo $edit_data ? 'disabled' : ''; ?>>
                                        <option value="">-- বিষয় নির্বাচন করুন --</option>
                                        <?php if ($subjects && $subjects->num_rows > 0): ?>
                                            <?php while ($subject = $subjects->fetch_assoc()): ?>
                                                <option value="<?php echo $subject['id']; ?>" <?php echo ($edit_data && $edit_data['subject_id'] == $subject['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($subject['subject_name'] . ' (' . $subject['subject_code'] . ')'); ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                    <div class="invalid-feedback">অনুগ্রহ করে একটি বিষয় নির্বাচন করুন</div>
                                    <?php if ($edit_data): ?>
                                        <input type="hidden" name="subject_id" value="<?php echo $edit_data['subject_id']; ?>">
                                        <div class="mt-2 text-primary">
                                            <i class="fas fa-info-circle me-1"></i> বর্তমানে সম্পাদনা করছেন:
                                            <strong><?php echo htmlspecialchars($edit_data['subject_name'] . ' (' . $edit_data['subject_code'] . ')'); ?></strong>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="mb-4">
                                    <label for="total_marks" class="form-label">
                                        <i class="fas fa-calculator me-1 text-primary"></i> মোট মার্কস
                                    </label>
                                    <div class="input-group shadow-sm">
                                        <input type="number" class="form-control" id="total_marks" name="total_marks" value="<?php echo $edit_data ? $edit_data['total_marks'] : '100'; ?>" min="0" step="0.01" required>
                                        <span class="input-group-text bg-light">পূর্ণ মার্কস</span>
                                    </div>
                                    <div class="invalid-feedback">মোট মার্কস প্রয়োজন</div>
                                </div>

                                <div class="mb-4">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" <?php echo (!$edit_data || ($edit_data && $edit_data['is_active'] == 1)) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="is_active">
                                            <i class="fas fa-toggle-on me-1 text-primary"></i> সক্রিয় স্ট্যাটাস
                                        </label>
                                    </div>
                                    <div class="small text-muted mt-1">
                                        <i class="fas fa-info-circle me-1"></i> নিষ্ক্রিয় করলে এই মার্কস ডিস্ট্রিবিউশন ব্যবহার করা যাবে না
                                    </div>
                                </div>

                                <div class="card mb-4 border-0 shadow-sm">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-sliders-h me-2 text-primary"></i> মার্কস বিভাজন</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row g-3">
                                            <div class="col-md-4">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <label for="cq_marks" class="form-label mb-0">
                                                        <i class="fas fa-pen me-1 text-primary"></i> সিকিউ (লিখিত)
                                                    </label>
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input component-toggle" type="checkbox" id="enable_cq" name="enable_cq" checked data-target="cq_marks">
                                                        <label class="form-check-label" for="enable_cq">সক্রিয়</label>
                                                    </div>
                                                </div>
                                                <div class="input-group shadow-sm">
                                                    <span class="input-group-text bg-primary text-white">
                                                        <i class="fas fa-edit"></i>
                                                    </span>
                                                    <input type="number" class="form-control distribution-component" id="cq_marks" name="cq_marks" value="<?php echo $edit_data ? $edit_data['cq_marks'] : '70'; ?>" min="0" step="0.01">
                                                    <span class="input-group-text bg-light">মার্কস</span>
                                                </div>
                                                <div class="form-text text-muted">
                                                    <i class="fas fa-info-circle me-1"></i> লিখিত পরীক্ষার মার্কস
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <label for="mcq_marks" class="form-label mb-0">
                                                        <i class="fas fa-tasks me-1 text-success"></i> এমসিকিউ
                                                    </label>
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input component-toggle" type="checkbox" id="enable_mcq" name="enable_mcq" data-target="mcq_marks">
                                                        <label class="form-check-label" for="enable_mcq">সক্রিয়</label>
                                                    </div>
                                                </div>
                                                <div class="input-group shadow-sm">
                                                    <span class="input-group-text bg-success text-white">
                                                        <i class="fas fa-check-square"></i>
                                                    </span>
                                                    <input type="number" class="form-control distribution-component" id="mcq_marks" name="mcq_marks" value="<?php echo $edit_data ? $edit_data['mcq_marks'] : '30'; ?>" min="0" step="0.01" disabled>
                                                    <span class="input-group-text bg-light">মার্কস</span>
                                                </div>
                                                <div class="form-text text-muted">
                                                    <i class="fas fa-info-circle me-1"></i> বহুনির্বাচনী প্রশ্নের মার্কস
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <label for="practical_marks" class="form-label mb-0">
                                                        <i class="fas fa-flask me-1 text-info"></i> ব্যবহারিক
                                                    </label>
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input component-toggle" type="checkbox" id="enable_practical" name="enable_practical" data-target="practical_marks">
                                                        <label class="form-check-label" for="enable_practical">সক্রিয়</label>
                                                    </div>
                                                </div>
                                                <div class="input-group shadow-sm">
                                                    <span class="input-group-text bg-info text-white">
                                                        <i class="fas fa-vial"></i>
                                                    </span>
                                                    <input type="number" class="form-control distribution-component" id="practical_marks" name="practical_marks" value="<?php echo $edit_data ? $edit_data['practical_marks'] : '0'; ?>" min="0" step="0.01" disabled>
                                                    <span class="input-group-text bg-light">মার্কস</span>
                                                </div>
                                                <div class="form-text text-muted">
                                                    <i class="fas fa-info-circle me-1"></i> ব্যবহারিক পরীক্ষার মার্কস
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mb-4 border-0 shadow-sm">
                                    <div class="card-body">
                                        <h6 class="mb-3 text-primary">
                                            <i class="fas fa-chart-pie me-2"></i> মার্কস বিতরণ সারাংশ
                                        </h6>

                                        <div class="row mb-3">
                                            <div class="col-md-4 text-center">
                                                <div class="card border-0 bg-light p-3" id="cq-card">
                                                    <div class="h5 text-primary">সিকিউ (লিখিত)</div>
                                                    <div class="display-6 fw-bold text-primary" id="cq-percentage">70%</div>
                                                </div>
                                            </div>
                                            <div class="col-md-4 text-center">
                                                <div class="card border-0 bg-light p-3 d-none" id="mcq-card">
                                                    <div class="h5 text-success">এমসিকিউ</div>
                                                    <div class="display-6 fw-bold text-success" id="mcq-percentage">30%</div>
                                                </div>
                                            </div>
                                            <div class="col-md-4 text-center">
                                                <div class="card border-0 bg-light p-3 d-none" id="practical-card">
                                                    <div class="h5 text-info">ব্যবহারিক</div>
                                                    <div class="display-6 fw-bold text-info" id="practical-percentage">0%</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="fw-bold">মোট যোগফল:</span>
                                            <span id="total-sum" class="badge bg-primary fs-6 px-3 py-2">100</span>
                                        </div>
                                        <div class="progress" style="height: 20px; border-radius: 10px; overflow: hidden;">
                                            <div id="cq-progress" class="progress-bar bg-primary" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                                            <div id="mcq-progress" class="progress-bar bg-success" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                            <div id="practical-progress" class="progress-bar bg-info" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                        <div class="small text-muted mt-2">
                                            <i class="fas fa-info-circle me-1"></i> সমস্ত উপাদানের যোগফল মোট মার্কসের সমান হতে হবে
                                        </div>
                                    </div>
                                </div>

                                <div class="d-grid gap-2">
                                    <button type="submit" name="add_distribution" class="btn btn-primary btn-lg" id="submitButton">
                                        <i class="fas fa-<?php echo $edit_data ? 'sync' : 'save'; ?> me-2"></i> <?php echo $edit_data ? 'আপডেট করুন' : 'সংরক্ষণ করুন'; ?>
                                    </button>

                                    <?php if ($edit_data): ?>
                                        <a href="subject_marks_distribution.php" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-2"></i> বাতিল করুন
                                        </a>
                                    <?php endif; ?>
                                </div>

                                <script>
                                    // Set form submission handler
                                    document.addEventListener('DOMContentLoaded', function() {
                                        const form = document.querySelector('form');
                                        if (form) {
                                            form.addEventListener('submit', function(e) {
                                                // Get checkbox states
                                                const enableCqCheckbox = document.getElementById('enable_cq');
                                                const enableMcqCheckbox = document.getElementById('enable_mcq');
                                                const enablePracticalCheckbox = document.getElementById('enable_practical');

                                                // Get input fields
                                                const cqMarksInput = document.getElementById('cq_marks');
                                                const mcqMarksInput = document.getElementById('mcq_marks');
                                                const practicalMarksInput = document.getElementById('practical_marks');

                                                // Set values to 0 for disabled components
                                                if (enableMcqCheckbox && !enableMcqCheckbox.checked && mcqMarksInput) {
                                                    mcqMarksInput.disabled = false; // Temporarily enable to allow form submission
                                                    mcqMarksInput.value = '0';
                                                }

                                                if (enablePracticalCheckbox && !enablePracticalCheckbox.checked && practicalMarksInput) {
                                                    practicalMarksInput.disabled = false; // Temporarily enable to allow form submission
                                                    practicalMarksInput.value = '0';
                                                }

                                                // Log values for debugging
                                                console.log('Form submission:');
                                                console.log('CQ Enabled:', enableCqCheckbox ? enableCqCheckbox.checked : 'N/A', 'Value:', cqMarksInput ? cqMarksInput.value : 'N/A');
                                                console.log('MCQ Enabled:', enableMcqCheckbox ? enableMcqCheckbox.checked : 'N/A', 'Value:', mcqMarksInput ? mcqMarksInput.value : 'N/A');
                                                console.log('Practical Enabled:', enablePracticalCheckbox ? enablePracticalCheckbox.checked : 'N/A', 'Value:', practicalMarksInput ? practicalMarksInput.value : 'N/A');
                                            });
                                        }
                                    });
                                </script>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Distribution List -->
                <div class="col-md-7">
                    <div class="card animate__animated animate__fadeInRight shadow">
                        <div class="card-header bg-primary text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-list me-2"></i> বিষয় মার্কস ডিস্ট্রিবিউশন তালিকা
                                </h5>
                                <div class="input-group input-group-sm" style="width: 200px;">
                                    <input type="text" class="form-control" id="searchInput" placeholder="সার্চ করুন...">
                                    <span class="input-group-text bg-white">
                                        <i class="fas fa-search"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover" id="distributionTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="fw-bold">বিষয়</th>
                                            <th class="text-center fw-bold">সিকিউ</th>
                                            <th class="text-center fw-bold">এমসিকিউ</th>
                                            <th class="text-center fw-bold">ব্যবহারিক</th>
                                            <th class="text-center fw-bold">মোট</th>
                                            <th class="text-center fw-bold">স্ট্যাটাস</th>
                                            <th class="text-center fw-bold">অ্যাকশন</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if ($distributions && $distributions->num_rows > 0): ?>
                                            <?php while ($distribution = $distributions->fetch_assoc()): ?>
                                                <tr class="align-middle">
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="icon-circle bg-primary text-white p-2 me-2 rounded-circle">
                                                                <i class="fas fa-book"></i>
                                                            </div>
                                                            <div>
                                                                <div class="fw-bold"><?php echo htmlspecialchars($distribution['subject_name']); ?></div>
                                                                <div class="small text-muted"><?php echo htmlspecialchars($distribution['subject_code']); ?></div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="badge bg-primary rounded-pill"><?php echo $distribution['cq_marks']; ?></span>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="badge bg-success rounded-pill"><?php echo $distribution['mcq_marks']; ?></span>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="badge bg-info rounded-pill"><?php echo $distribution['practical_marks']; ?></span>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="badge bg-dark rounded-pill"><?php echo $distribution['total_marks']; ?></span>
                                                    </td>
                                                    <td class="text-center">
                                                        <?php
                                                        $statusClass = isset($distribution['is_active']) && $distribution['is_active'] == 1 ? 'success' : 'danger';
                                                        $statusText = isset($distribution['is_active']) && $distribution['is_active'] == 1 ? 'সক্রিয়' : 'নিষ্ক্রিয়';
                                                        $statusIcon = isset($distribution['is_active']) && $distribution['is_active'] == 1 ? 'check-circle' : 'times-circle';
                                                        $newStatus = isset($distribution['is_active']) && $distribution['is_active'] == 1 ? 0 : 1;
                                                        ?>
                                                        <form method="POST" action="" class="status-toggle-form">
                                                            <input type="hidden" name="distribution_id" value="<?php echo $distribution['id']; ?>">
                                                            <input type="hidden" name="new_status" value="<?php echo $newStatus; ?>">
                                                            <button type="submit" name="toggle_status" class="btn btn-sm btn-<?php echo $statusClass; ?>" data-bs-toggle="tooltip" title="স্ট্যাটাস পরিবর্তন করুন">
                                                                <i class="fas fa-<?php echo $statusIcon; ?> me-1"></i> <?php echo $statusText; ?>
                                                            </button>
                                                        </form>
                                                    </td>
                                                    <td class="text-center">
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="subject_marks_distribution.php?edit=<?php echo $distribution['id']; ?>" class="btn btn-primary" data-bs-toggle="tooltip" title="সম্পাদনা করুন">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?php echo $distribution['id']; ?>" title="মুছে ফেলুন">
                                                                <i class="fas fa-trash-alt"></i>
                                                            </button>
                                                        </div>

                                                        <!-- Delete Modal -->
                                                        <div class="modal fade" id="deleteModal<?php echo $distribution['id']; ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?php echo $distribution['id']; ?>" aria-hidden="true">
                                                            <div class="modal-dialog modal-dialog-centered">
                                                                <div class="modal-content">
                                                                    <div class="modal-header bg-danger text-white">
                                                                        <h5 class="modal-title" id="deleteModalLabel<?php echo $distribution['id']; ?>">
                                                                            <i class="fas fa-exclamation-triangle me-2"></i> নিশ্চিতকরণ
                                                                        </h5>
                                                                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                    </div>
                                                                    <div class="modal-body p-4">
                                                                        <div class="text-center mb-4">
                                                                            <i class="fas fa-trash-alt fa-4x text-danger mb-3"></i>
                                                                            <h5 class="mb-2">আপনি কি নিশ্চিত যে আপনি এই মার্কস ডিস্ট্রিবিউশন মুছে ফেলতে চান?</h5>
                                                                            <p class="text-muted">এই কাজটি অপরিবর্তনীয়। একবার মুছে ফেললে, এই ডাটা পুনরুদ্ধার করা যাবে না।</p>
                                                                            <div class="alert alert-warning">
                                                                                <strong>বিষয়:</strong> <?php echo htmlspecialchars($distribution['subject_name'] . ' (' . $distribution['subject_code'] . ')'); ?>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="modal-footer bg-light">
                                                                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                                                            <i class="fas fa-times me-2"></i> বাতিল করুন
                                                                        </button>
                                                                        <form method="POST" action="">
                                                                            <input type="hidden" name="distribution_id" value="<?php echo $distribution['id']; ?>">
                                                                            <button type="submit" name="delete_distribution" class="btn btn-danger">
                                                                                <i class="fas fa-trash-alt me-2"></i> মুছে ফেলুন
                                                                            </button>
                                                                        </form>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endwhile; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="7" class="text-center py-5">
                                                    <div class="py-4">
                                                        <i class="fas fa-database fa-4x text-muted mb-3"></i>
                                                        <h5 class="text-muted">কোন মার্কস ডিস্ট্রিবিউশন পাওয়া যায়নি</h5>
                                                        <p class="text-muted">বিষয় মার্কস ডিস্ট্রিবিউশন যোগ করতে বাম দিকের ফর্মটি ব্যবহার করুন</p>
                                                        <button class="btn btn-primary mt-2" onclick="document.getElementById('subject_id').focus()">
                                                            <i class="fas fa-plus-circle me-2"></i> নতুন ডিস্ট্রিবিউশন যোগ করুন
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <div class="text-muted small">
                                    <span id="totalRecords">0</span> টি রেকর্ড পাওয়া গেছে
                                </div>
                                <nav aria-label="Page navigation">
                                    <ul class="pagination pagination-sm justify-content-end mb-0">
                                        <li class="page-item disabled">
                                            <a class="page-link" href="#" tabindex="-1" aria-disabled="true">পূর্ববর্তী</a>
                                        </li>
                                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                        <li class="page-item disabled">
                                            <a class="page-link" href="#">পরবর্তী</a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Fix for auto-reload issue
if (window.history.replaceState) {
    window.history.replaceState(null, null, window.location.href);
}

// Immediately execute to fix loading issues
(function() {
    // Force hide all loading elements
    const hideLoaders = function() {
        // Remove any loading elements
        const loadingSelectors = [
            '#page-loader', '.loader', '.loading-spinner', '.loader-spinner', '.loader-text',
            '[id*="loader"]', '[class*="loader"]', '[class*="loading"]'
        ];

        loadingSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                if (element) {
                    element.style.display = 'none';
                    element.style.opacity = '0';
                    element.style.visibility = 'hidden';
                    element.style.height = '0';
                    element.style.width = '0';
                    element.style.position = 'absolute';
                    element.style.zIndex = '-9999';
                    element.style.pointerEvents = 'none';
                }
            });
        });

        // Add loaded class to body
        document.body.classList.add('loaded');
        document.body.classList.remove('loading');

        // Force page to be visible
        document.body.style.display = 'block';
        document.body.style.visibility = 'visible';
        document.body.style.opacity = '1';

        console.log('Loading elements removed');
    };

    // Execute immediately
    hideLoaders();

    // Also execute on DOMContentLoaded
    document.addEventListener('DOMContentLoaded', hideLoaders);

    // Also execute on load
    window.addEventListener('load', hideLoaders);

    // Set interval to keep checking and removing loaders
    setInterval(hideLoaders, 500);
})();

    // Initialize tooltips if Bootstrap is loaded
    if (typeof bootstrap !== 'undefined') {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl, {
                animation: true,
                delay: { show: 100, hide: 100 }
            });
        });
    } else {
        console.error('Bootstrap JS is not loaded');
    }

    // Calculate total sum of distribution components
    function calculateSum() {
        // Check if all required elements exist
        const totalMarksElement = document.getElementById('total_marks');
        const cqMarksElement = document.getElementById('cq_marks');
        const mcqMarksElement = document.getElementById('mcq_marks');
        const practicalMarksElement = document.getElementById('practical_marks');
        const totalSumElement = document.getElementById('total-sum');

        // Progress elements
        const cqProgressElement = document.getElementById('cq-progress');
        const mcqProgressElement = document.getElementById('mcq-progress');
        const practicalProgressElement = document.getElementById('practical-progress');

        // Percentage display elements
        const cqPercentageElement = document.getElementById('cq-percentage');
        const mcqPercentageElement = document.getElementById('mcq-percentage');
        const practicalPercentageElement = document.getElementById('practical-percentage');

        // Toggle checkboxes
        const enableCqCheckbox = document.getElementById('enable_cq');
        const enableMcqCheckbox = document.getElementById('enable_mcq');
        const enablePracticalCheckbox = document.getElementById('enable_practical');

        if (!totalMarksElement || !cqMarksElement || !mcqMarksElement ||
            !practicalMarksElement || !totalSumElement) {
            console.error('One or more required elements not found');
            return;
        }

        let sum = 0;
        const totalMarks = parseFloat(totalMarksElement.value) || 100;

        // Get values from all distribution components, but only count enabled ones
        let cqMarks = 0;
        let mcqMarks = 0;
        let practicalMarks = 0;

        // Only count marks if the component is enabled (checkbox is checked)
        if (enableCqCheckbox && enableCqCheckbox.checked) {
            cqMarks = parseFloat(cqMarksElement.value) || 0;
        }

        if (enableMcqCheckbox && enableMcqCheckbox.checked) {
            mcqMarks = parseFloat(mcqMarksElement.value) || 0;
        }

        if (enablePracticalCheckbox && enablePracticalCheckbox.checked) {
            practicalMarks = parseFloat(practicalMarksElement.value) || 0;
        }

        // Calculate sum of enabled components only
        sum = cqMarks + mcqMarks + practicalMarks;

        // Debug information
        console.log('CQ Enabled:', enableCqCheckbox ? enableCqCheckbox.checked : 'N/A', 'Value:', cqMarks);
        console.log('MCQ Enabled:', enableMcqCheckbox ? enableMcqCheckbox.checked : 'N/A', 'Value:', mcqMarks);
        console.log('Practical Enabled:', enablePracticalCheckbox ? enablePracticalCheckbox.checked : 'N/A', 'Value:', practicalMarks);
        console.log('Total Sum:', sum, 'Total Marks:', totalMarks);

        // Update total sum display
        totalSumElement.textContent = sum.toFixed(2);

        // Calculate percentages
        const cqPercentage = totalMarks > 0 ? (cqMarks / totalMarks) * 100 : 0;
        const mcqPercentage = totalMarks > 0 ? (mcqMarks / totalMarks) * 100 : 0;
        const practicalPercentage = totalMarks > 0 ? (practicalMarks / totalMarks) * 100 : 0;

        // Update percentage displays
        if (cqPercentageElement) cqPercentageElement.textContent = cqPercentage.toFixed(0) + '%';
        if (mcqPercentageElement) mcqPercentageElement.textContent = mcqPercentage.toFixed(0) + '%';
        if (practicalPercentageElement) practicalPercentageElement.textContent = practicalPercentage.toFixed(0) + '%';

        // Update progress bars
        if (cqProgressElement) {
            cqProgressElement.style.width = cqPercentage + '%';
            cqProgressElement.setAttribute('aria-valuenow', cqPercentage);
        }

        if (mcqProgressElement) {
            mcqProgressElement.style.width = mcqPercentage + '%';
            mcqProgressElement.setAttribute('aria-valuenow', mcqPercentage);
        }

        if (practicalProgressElement) {
            practicalProgressElement.style.width = practicalPercentage + '%';
            practicalProgressElement.setAttribute('aria-valuenow', practicalPercentage);
        }

        // Update total sum color based on whether it matches total marks
        if (totalSumElement) {
            if (Math.abs(sum - totalMarks) < 0.01) {
                totalSumElement.classList.remove('bg-danger', 'bg-warning');
                totalSumElement.classList.add('bg-success');
            } else if (sum > totalMarks) {
                totalSumElement.classList.remove('bg-success', 'bg-warning');
                totalSumElement.classList.add('bg-danger');
            } else {
                totalSumElement.classList.remove('bg-success', 'bg-danger');
                totalSumElement.classList.add('bg-warning');
            }
        }

        // Auto-adjust values if needed
        const formElement = document.querySelector('form.needs-validation');
        const submitButton = formElement ? formElement.querySelector('button[type="submit"]') : null;

        if (submitButton) {
            if (Math.abs(sum - totalMarks) < 0.01) {
                // Sum matches total marks, enable submit button
                submitButton.disabled = false;

                // Remove any error message
                const errorElement = document.getElementById('sum-error');
                if (errorElement) {
                    errorElement.style.display = 'none';
                }
            } else {
                // Sum doesn't match total marks, disable submit button
                submitButton.disabled = true;

                // Show error message
                let errorElement = document.getElementById('sum-error');
                if (!errorElement) {
                    errorElement = document.createElement('div');
                    errorElement.id = 'sum-error';
                    errorElement.className = 'alert alert-danger mt-3';
                    const formGroup = totalSumElement.closest('.card-body');
                    if (formGroup) {
                        formGroup.appendChild(errorElement);
                    }
                }

                errorElement.style.display = 'block';
                errorElement.innerHTML = `
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>ত্রুটি!</strong> সমস্ত উপাদানের যোগফল মোট মার্কসের সমান হতে হবে। বর্তমান যোগফল: ${sum}, মোট মার্কস: ${totalMarks}
                    <button type="button" class="btn btn-sm btn-primary ms-3" id="auto-adjust-button">
                        <i class="fas fa-magic me-1"></i> অটো-অ্যাডজাস্ট করুন
                    </button>
                `;

                // Add event listener to auto-adjust button
                const autoAdjustButton = document.getElementById('auto-adjust-button');
                if (autoAdjustButton) {
                    autoAdjustButton.addEventListener('click', function() {
                        adjustAllComponents();
                        calculateSum();
                    });
                }
            }
        }
    }

    // Toggle component function
    function toggleComponent(checkbox) {
        const targetId = checkbox.dataset.target;
        const targetElement = document.getElementById(targetId);

        if (targetElement) {
            targetElement.disabled = !checkbox.checked;

            if (!checkbox.checked) {
                // If disabled, set value to 0
                targetElement.value = '0';
            } else if (targetElement.value === '0') {
                // If enabled and value is 0, set default value based on how many components are enabled
                const enabledComponents = document.querySelectorAll('.component-toggle:checked').length;

                // Get total marks
                const totalMarksElement = document.getElementById('total_marks');
                const totalMarks = parseFloat(totalMarksElement.value) || 100;

                if (enabledComponents === 1) {
                    // If this is the only enabled component, set it to total marks
                    targetElement.value = totalMarks;
                } else if (enabledComponents === 2) {
                    // If there are 2 enabled components, distribute marks
                    if (targetId === 'cq_marks') targetElement.value = Math.round(totalMarks * 0.7);
                    if (targetId === 'mcq_marks') targetElement.value = Math.round(totalMarks * 0.3);
                    if (targetId === 'practical_marks') targetElement.value = Math.round(totalMarks * 0.3);

                    // Adjust other enabled component to make sum equal to total
                    adjustOtherComponents(targetId);
                } else {
                    // Default distribution for 3 components
                    if (targetId === 'cq_marks') targetElement.value = Math.round(totalMarks * 0.6);
                    if (targetId === 'mcq_marks') targetElement.value = Math.round(totalMarks * 0.3);
                    if (targetId === 'practical_marks') targetElement.value = Math.round(totalMarks * 0.1);

                    // Adjust other components to make sum equal to total
                    adjustAllComponents();
                }
            }

            // Update calculations
            calculateSum();

            // Update card visibility in summary
            const componentId = targetId.replace('_marks', '');
            const cardElement = document.getElementById(componentId + '-card');
            if (cardElement) {
                if (checkbox.checked) {
                    cardElement.classList.remove('d-none');
                } else {
                    cardElement.classList.add('d-none');
                }
            }
        }
    }

    // Adjust other enabled components to make sum equal to total
    function adjustOtherComponents(currentTargetId) {
        const totalMarksElement = document.getElementById('total_marks');
        const totalMarks = parseFloat(totalMarksElement.value) || 100;

        const cqMarksElement = document.getElementById('cq_marks');
        const mcqMarksElement = document.getElementById('mcq_marks');
        const practicalMarksElement = document.getElementById('practical_marks');

        const enableCqCheckbox = document.getElementById('enable_cq');
        const enableMcqCheckbox = document.getElementById('enable_mcq');
        const enablePracticalCheckbox = document.getElementById('enable_practical');

        let currentValue = 0;
        if (currentTargetId === 'cq_marks' && !cqMarksElement.disabled) {
            currentValue = parseFloat(cqMarksElement.value) || 0;
        } else if (currentTargetId === 'mcq_marks' && !mcqMarksElement.disabled) {
            currentValue = parseFloat(mcqMarksElement.value) || 0;
        } else if (currentTargetId === 'practical_marks' && !practicalMarksElement.disabled) {
            currentValue = parseFloat(practicalMarksElement.value) || 0;
        }

        const remainingValue = totalMarks - currentValue;

        // Find the other enabled component and set its value
        if (currentTargetId !== 'cq_marks' && enableCqCheckbox.checked) {
            cqMarksElement.value = remainingValue;
        } else if (currentTargetId !== 'mcq_marks' && enableMcqCheckbox.checked) {
            mcqMarksElement.value = remainingValue;
        } else if (currentTargetId !== 'practical_marks' && enablePracticalCheckbox.checked) {
            practicalMarksElement.value = remainingValue;
        }
    }

    // Adjust all components to make sum equal to total
    function adjustAllComponents() {
        const totalMarksElement = document.getElementById('total_marks');
        const totalMarks = parseFloat(totalMarksElement.value) || 100;

        const cqMarksElement = document.getElementById('cq_marks');
        const mcqMarksElement = document.getElementById('mcq_marks');
        const practicalMarksElement = document.getElementById('practical_marks');

        const enableCqCheckbox = document.getElementById('enable_cq');
        const enableMcqCheckbox = document.getElementById('enable_mcq');
        const enablePracticalCheckbox = document.getElementById('enable_practical');

        // Count enabled components
        let enabledCount = 0;
        if (enableCqCheckbox.checked) enabledCount++;
        if (enableMcqCheckbox.checked) enabledCount++;
        if (enablePracticalCheckbox.checked) enabledCount++;

        if (enabledCount === 0) return;

        // Calculate values based on enabled components
        if (enabledCount === 1) {
            // If only one component is enabled, set it to total marks
            if (enableCqCheckbox.checked) cqMarksElement.value = totalMarks;
            if (enableMcqCheckbox.checked) mcqMarksElement.value = totalMarks;
            if (enablePracticalCheckbox.checked) practicalMarksElement.value = totalMarks;
        } else if (enabledCount === 2) {
            // If two components are enabled, distribute 70/30
            let firstValue = Math.round(totalMarks * 0.7);
            let secondValue = totalMarks - firstValue;

            if (enableCqCheckbox.checked && enableMcqCheckbox.checked) {
                cqMarksElement.value = firstValue;
                mcqMarksElement.value = secondValue;
            } else if (enableCqCheckbox.checked && enablePracticalCheckbox.checked) {
                cqMarksElement.value = firstValue;
                practicalMarksElement.value = secondValue;
            } else if (enableMcqCheckbox.checked && enablePracticalCheckbox.checked) {
                mcqMarksElement.value = firstValue;
                practicalMarksElement.value = secondValue;
            }
        } else if (enabledCount === 3) {
            // If all three components are enabled, distribute 60/30/10
            let cqValue = Math.round(totalMarks * 0.6);
            let mcqValue = Math.round(totalMarks * 0.3);
            let practicalValue = totalMarks - cqValue - mcqValue;

            cqMarksElement.value = cqValue;
            mcqMarksElement.value = mcqValue;
            practicalMarksElement.value = practicalValue;
        }
    }

    // Add event listeners to all distribution components if they exist
    const cqMarksElement = document.getElementById('cq_marks');
    const mcqMarksElement = document.getElementById('mcq_marks');
    const practicalMarksElement = document.getElementById('practical_marks');
    const totalMarksElement = document.getElementById('total_marks');

    // Add event listeners to input fields
    if (cqMarksElement) cqMarksElement.addEventListener('input', calculateSum);
    if (mcqMarksElement) mcqMarksElement.addEventListener('input', calculateSum);
    if (practicalMarksElement) practicalMarksElement.addEventListener('input', calculateSum);
    if (totalMarksElement) totalMarksElement.addEventListener('input', calculateSum);

    // Add event listeners to toggle checkboxes
    const toggleCheckboxes = document.querySelectorAll('.component-toggle');
    toggleCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            toggleComponent(this);
        });
    });

    // Initialize component states
    toggleCheckboxes.forEach(checkbox => {
        toggleComponent(checkbox);
    });

    // Calculate initial sum
    calculateSum();

    // Search functionality
    const searchInput = document.getElementById('searchInput');
    const table = document.getElementById('distributionTable');
    const totalRecordsElement = document.getElementById('totalRecords');

    if (searchInput && table && totalRecordsElement) {
        searchInput.addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = table.querySelectorAll('tbody tr');
            let visibleCount = 0;

            rows.forEach(function(row) {
                const text = row.textContent.toLowerCase();
                if (text.indexOf(searchTerm) > -1) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });

            totalRecordsElement.textContent = visibleCount;
        });

        // Initialize total records count
        const initialCount = table.querySelectorAll('tbody tr').length;
        totalRecordsElement.textContent = initialCount;
    }

    // Form validation
    const forms = document.querySelectorAll('.needs-validation');

    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }

            form.classList.add('was-validated');
        }, false);
    });

    // Status toggle button animation
    document.querySelectorAll('.status-toggle-form button').forEach(button => {
        button.addEventListener('click', function() {
            // Add loading spinner
            const originalContent = this.innerHTML;
            this.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> পরিবর্তন হচ্ছে...';
            this.disabled = true;

            // Submit the form
            this.closest('form').submit();
        });
    });

// Export to Excel function
function exportToExcel() {
    const table = document.getElementById('distributionTable');
    if (!table) {
        alert('টেবিল পাওয়া যায়নি। এক্সপোর্ট করা যাবে না।');
        return;
    }

    // Check if XLSX is loaded
    if (typeof XLSX === 'undefined') {
        alert('এক্সেল লাইব্রেরি লোড হয়নি। পেজটি রিফ্রেশ করে আবার চেষ্টা করুন।');
        return;
    }

    // Show loading indicator
    const loadingToast = document.createElement('div');
    loadingToast.className = 'position-fixed bottom-0 end-0 p-3';
    loadingToast.style.zIndex = '5000';
    loadingToast.innerHTML = `
        <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">এক্সপোর্ট হচ্ছে</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                <div class="d-flex align-items-center">
                    <div class="spinner-border spinner-border-sm me-2" role="status">
                        <span class="visually-hidden">লোড হচ্ছে...</span>
                    </div>
                    <span>এক্সেল ফাইল তৈরি করা হচ্ছে...</span>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(loadingToast);

    // Use setTimeout to allow UI to update
    setTimeout(() => {
        try {
            // Create a workbook
            const wb = XLSX.utils.book_new();

            // Convert table to worksheet
            const ws = XLSX.utils.table_to_sheet(table);

            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, 'বিষয় মার্কস ডিস্ট্রিবিউশন');

            // Generate Excel file and trigger download
            XLSX.writeFile(wb, 'বিষয় মার্কস ডিস্ট্রিবিউশন.xlsx');

            // Remove loading toast and show success
            document.body.removeChild(loadingToast);

            const successToast = document.createElement('div');
            successToast.className = 'position-fixed bottom-0 end-0 p-3';
            successToast.style.zIndex = '5000';
            successToast.innerHTML = `
                <div class="toast show bg-success text-white" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="toast-header bg-success text-white">
                        <strong class="me-auto">সফল</strong>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                    <div class="toast-body">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-check-circle me-2"></i>
                            <span>এক্সেল ফাইল সফলভাবে ডাউনলোড হয়েছে</span>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(successToast);

            // Remove success toast after 3 seconds
            setTimeout(() => {
                document.body.removeChild(successToast);
            }, 3000);

        } catch (error) {
            console.error('Excel export error:', error);

            // Remove loading toast and show error
            document.body.removeChild(loadingToast);

            const errorToast = document.createElement('div');
            errorToast.className = 'position-fixed bottom-0 end-0 p-3';
            errorToast.style.zIndex = '5000';
            errorToast.innerHTML = `
                <div class="toast show bg-danger text-white" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="toast-header bg-danger text-white">
                        <strong class="me-auto">ত্রুটি</strong>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                    <div class="toast-body">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <span>এক্সেল ফাইল তৈরি করতে সমস্যা হয়েছে</span>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(errorToast);

            // Remove error toast after 3 seconds
            setTimeout(() => {
                document.body.removeChild(errorToast);
            }, 3000);
        }
    }, 100);
}
</script>

<?php include_once '../includes/footer.php'; ?>
</body>
</html>
