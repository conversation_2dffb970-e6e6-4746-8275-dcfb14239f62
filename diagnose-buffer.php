<?php
// DIAGNOSTIC SCRIPT - Find the exact cause of buffering
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Force disable all buffering
while (ob_get_level()) {
    ob_end_clean();
}

// Set headers
header('Content-Type: text/html; charset=UTF-8');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Pragma: no-cache');
header('Expires: 0');

// Start output immediately
echo "<!DOCTYPE html>\n";
echo "<html><head><title>নিশাত এডুকেশন সেন্টার - ডায়াগনোসিস</title></head><body>\n";
echo "<h1>🔍 Buffer Diagnosis Tool</h1>\n";

// Flush immediately
flush();

echo "<div style='background: #000; color: #00ff00; padding: 20px; font-family: monospace; margin: 20px 0;'>\n";
echo "<h3>Real-time Diagnosis:</h3>\n";

// Test 1: Basic output
echo "[" . date('H:i:s') . "] Test 1: Basic output - SUCCESS<br>\n";
flush();
usleep(500000); // 0.5 second

// Test 2: PHP Info
echo "[" . date('H:i:s') . "] Test 2: PHP Version: " . PHP_VERSION . "<br>\n";
flush();
usleep(500000);

// Test 3: Buffer level
echo "[" . date('H:i:s') . "] Test 3: Buffer Level: " . ob_get_level() . "<br>\n";
flush();
usleep(500000);

// Test 4: Server info
echo "[" . date('H:i:s') . "] Test 4: Server: " . $_SERVER['SERVER_SOFTWARE'] . "<br>\n";
flush();
usleep(500000);

// Test 5: Memory usage
echo "[" . date('H:i:s') . "] Test 5: Memory: " . memory_get_usage(true) . " bytes<br>\n";
flush();
usleep(500000);

// Test 6: Check for problematic extensions
$problematic_extensions = ['xdebug', 'opcache', 'zlib'];
foreach ($problematic_extensions as $ext) {
    $loaded = extension_loaded($ext);
    echo "[" . date('H:i:s') . "] Test 6: Extension $ext: " . ($loaded ? "LOADED (potential issue)" : "Not loaded") . "<br>\n";
    flush();
    usleep(300000);
}

// Test 7: Check ini settings
$settings = [
    'output_buffering',
    'implicit_flush', 
    'zlib.output_compression',
    'opcache.enable',
    'xdebug.mode'
];

foreach ($settings as $setting) {
    $value = ini_get($setting);
    echo "[" . date('H:i:s') . "] Test 7: $setting = " . ($value ?: 'Off/Empty') . "<br>\n";
    flush();
    usleep(300000);
}

echo "</div>\n";

// Test 8: JavaScript detection
echo "<div id='js-test' style='background: #ffcccc; padding: 10px; margin: 10px 0;'>JavaScript NOT working</div>\n";
echo "<script>\n";
echo "document.getElementById('js-test').innerHTML = '✅ JavaScript working';\n";
echo "document.getElementById('js-test').style.background = '#ccffcc';\n";
echo "</script>\n";

// Test 9: Title monitoring
echo "<div id='title-monitor' style='background: #ffffcc; padding: 10px; margin: 10px 0;'>Title: Loading...</div>\n";
echo "<script>\n";
echo "function updateTitleMonitor() {\n";
echo "    const monitor = document.getElementById('title-monitor');\n";
echo "    monitor.innerHTML = 'Title: ' + document.title;\n";
echo "    if (document.title === 'নিশাত এডুকেশন সেন্টার - ডায়াগনোসিস') {\n";
echo "        monitor.style.background = '#ccffcc';\n";
echo "        monitor.innerHTML += ' ✅ CORRECT';\n";
echo "    } else {\n";
echo "        monitor.style.background = '#ffcccc';\n";
echo "        monitor.innerHTML += ' ❌ WRONG';\n";
echo "    }\n";
echo "}\n";
echo "updateTitleMonitor();\n";
echo "setInterval(updateTitleMonitor, 1000);\n";
echo "</script>\n";

// Test 10: Network monitoring
echo "<div id='network-test' style='background: #ccccff; padding: 10px; margin: 10px 0;'>Network test...</div>\n";
echo "<script>\n";
echo "fetch('ultra-minimal.html')\n";
echo ".then(response => {\n";
echo "    document.getElementById('network-test').innerHTML = '✅ Network OK - Response: ' + response.status;\n";
echo "    document.getElementById('network-test').style.background = '#ccffcc';\n";
echo "})\n";
echo ".catch(error => {\n";
echo "    document.getElementById('network-test').innerHTML = '❌ Network Error: ' + error;\n";
echo "    document.getElementById('network-test').style.background = '#ffcccc';\n";
echo "});\n";
echo "</script>\n";

echo "<h2>🎯 Instructions:</h2>\n";
echo "<ol>\n";
echo "<li>Watch the title bar of this page</li>\n";
echo "<li>If it shows 'নিশাত এডুকেশন সেন্টার - ডায়াগনোসিস' without buffering, the issue is in other files</li>\n";
echo "<li>If this page also buffers, the issue is in Apache/PHP configuration</li>\n";
echo "<li>Check the real-time diagnosis above for clues</li>\n";
echo "</ol>\n";

echo "<h2>🔧 Quick Actions:</h2>\n";
echo "<button onclick='location.reload()'>Reload This Page</button>\n";
echo "<button onclick='window.open(\"ultra-minimal.html\", \"_blank\")'>Test Static HTML</button>\n";
echo "<button onclick='window.open(\"index.php\", \"_blank\")'>Test Main Page</button>\n";

echo "<h2>📊 Live Stats:</h2>\n";
echo "<div id='live-stats' style='background: #f0f0f0; padding: 10px; margin: 10px 0; font-family: monospace;'>\n";
echo "Loading stats...\n";
echo "</div>\n";

echo "<script>\n";
echo "function updateStats() {\n";
echo "    const stats = document.getElementById('live-stats');\n";
echo "    const now = new Date();\n";
echo "    stats.innerHTML = `\n";
echo "        Current Time: \${now.toLocaleString()}<br>\n";
echo "        Page Title: \${document.title}<br>\n";
echo "        URL: \${window.location.href}<br>\n";
echo "        User Agent: \${navigator.userAgent.substring(0, 50)}...<br>\n";
echo "        Page Load Time: \${(performance.now() / 1000).toFixed(2)} seconds\n";
echo "    `;\n";
echo "}\n";
echo "updateStats();\n";
echo "setInterval(updateStats, 2000);\n";
echo "</script>\n";

echo "</body></html>\n";

// Final flush
flush();
if (function_exists('fastcgi_finish_request')) {
    fastcgi_finish_request();
}
?>
