<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Include header
include_once 'includes/header.php';

// Function to add sample data
function addSampleData($conn) {
    $messages = [];
    
    // Add sample sessions
    $sessions = [
        ['session_name' => '২০২৩-২০২৪'],
        ['session_name' => '২০২২-২০২৩'],
        ['session_name' => '২০২১-২০২২']
    ];
    
    $sessionsAdded = 0;
    foreach ($sessions as $session) {
        // Check if session already exists
        $checkQuery = "SELECT id FROM sessions WHERE session_name = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param('s', $session['session_name']);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            // Insert new session
            $insertQuery = "INSERT INTO sessions (session_name) VALUES (?)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param('s', $session['session_name']);
            if ($stmt->execute()) {
                $sessionsAdded++;
            }
        }
    }
    
    if ($sessionsAdded > 0) {
        $messages[] = [
            'type' => 'success',
            'message' => $sessionsAdded . ' টি নতুন সেশন যোগ করা হয়েছে।'
        ];
    } else {
        $messages[] = [
            'type' => 'info',
            'message' => 'সকল সেশন ইতিমধ্যে বিদ্যমান।'
        ];
    }
    
    // Add sample classes
    $classes = [
        ['class_name' => 'ষষ্ঠ শ্রেণী'],
        ['class_name' => 'সপ্তম শ্রেণী'],
        ['class_name' => 'অষ্টম শ্রেণী'],
        ['class_name' => 'নবম শ্রেণী'],
        ['class_name' => 'দশম শ্রেণী']
    ];
    
    $classesAdded = 0;
    foreach ($classes as $class) {
        // Check if class already exists
        $checkQuery = "SELECT id FROM classes WHERE class_name = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param('s', $class['class_name']);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            // Insert new class
            $insertQuery = "INSERT INTO classes (class_name) VALUES (?)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param('s', $class['class_name']);
            if ($stmt->execute()) {
                $classesAdded++;
            }
        }
    }
    
    if ($classesAdded > 0) {
        $messages[] = [
            'type' => 'success',
            'message' => $classesAdded . ' টি নতুন শ্রেণী যোগ করা হয়েছে।'
        ];
    } else {
        $messages[] = [
            'type' => 'info',
            'message' => 'সকল শ্রেণী ইতিমধ্যে বিদ্যমান।'
        ];
    }
    
    // Add sample departments
    $departments = [
        ['department_name' => 'বিজ্ঞান'],
        ['department_name' => 'মানবিক'],
        ['department_name' => 'বাণিজ্য'],
        ['department_name' => 'সকল বিভাগ']
    ];
    
    $departmentsAdded = 0;
    foreach ($departments as $department) {
        // Check if department already exists
        $checkQuery = "SELECT id FROM departments WHERE department_name = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param('s', $department['department_name']);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            // Insert new department
            $insertQuery = "INSERT INTO departments (department_name) VALUES (?)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param('s', $department['department_name']);
            if ($stmt->execute()) {
                $departmentsAdded++;
            }
        }
    }
    
    if ($departmentsAdded > 0) {
        $messages[] = [
            'type' => 'success',
            'message' => $departmentsAdded . ' টি নতুন বিভাগ যোগ করা হয়েছে।'
        ];
    } else {
        $messages[] = [
            'type' => 'info',
            'message' => 'সকল বিভাগ ইতিমধ্যে বিদ্যমান।'
        ];
    }
    
    return $messages;
}

// Process form submission
$messages = [];
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_sample_data'])) {
    $messages = addSampleData($conn);
}

// Get current counts
$sessionsQuery = "SELECT COUNT(*) as count FROM sessions";
$sessionsResult = $conn->query($sessionsQuery);
$sessionsCount = ($sessionsResult && $sessionsResult->num_rows > 0) ? $sessionsResult->fetch_assoc()['count'] : 0;

$classesQuery = "SELECT COUNT(*) as count FROM classes";
$classesResult = $conn->query($classesQuery);
$classesCount = ($classesResult && $classesResult->num_rows > 0) ? $classesResult->fetch_assoc()['count'] : 0;

$departmentsQuery = "SELECT COUNT(*) as count FROM departments";
$departmentsResult = $conn->query($departmentsQuery);
$departmentsCount = ($departmentsResult && $departmentsResult->num_rows > 0) ? $departmentsResult->fetch_assoc()['count'] : 0;
?>

<div class="container-fluid">
    <div class="row">
        <!-- Include sidebar -->
        <?php include_once 'includes/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">নমুনা ডাটা যোগ করুন</h1>
            </div>

            <?php foreach ($messages as $message): ?>
                <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show" role="alert">
                    <?php echo $message['message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endforeach; ?>

            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">নমুনা ডাটা যোগ করুন</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 class="card-title">সেশন</h5>
                                    <p class="display-4"><?php echo $sessionsCount; ?></p>
                                    <p class="text-muted">বর্তমান রেকর্ড সংখ্যা</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 class="card-title">শ্রেণী</h5>
                                    <p class="display-4"><?php echo $classesCount; ?></p>
                                    <p class="text-muted">বর্তমান রেকর্ড সংখ্যা</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 class="card-title">বিভাগ</h5>
                                    <p class="display-4"><?php echo $departmentsCount; ?></p>
                                    <p class="text-muted">বর্তমান রেকর্ড সংখ্যা</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form method="post" action="">
                        <div class="alert alert-info">
                            <p><i class="fas fa-info-circle me-2"></i> এই পেজটি ব্যবহার করে আপনি সিস্টেমে নমুনা ডাটা যোগ করতে পারেন। এটি শুধুমাত্র টেস্টিং উদ্দেশ্যে ব্যবহার করা উচিত।</p>
                            <p>নিম্নলিখিত নমুনা ডাটা যোগ করা হবে:</p>
                            <ul>
                                <li>সেশন: ২০২৩-২০২৪, ২০২২-২০২৩, ২০২১-২০২২</li>
                                <li>শ্রেণী: ষষ্ঠ শ্রেণী, সপ্তম শ্রেণী, অষ্টম শ্রেণী, নবম শ্রেণী, দশম শ্রেণী</li>
                                <li>বিভাগ: বিজ্ঞান, মানবিক, বাণিজ্য, সকল বিভাগ</li>
                            </ul>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="check_data.php" class="btn btn-secondary me-md-2">
                                <i class="fas fa-search me-1"></i> ডাটা চেক করুন
                            </a>
                            <button type="submit" name="add_sample_data" class="btn btn-primary">
                                <i class="fas fa-plus-circle me-1"></i> নমুনা ডাটা যোগ করুন
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="mt-3">
                <a href="fee_management.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i> ফিরে যান
                </a>
            </div>
        </main>
    </div>
</div>

<?php
// Include footer
include_once 'includes/footer.php';
?>
