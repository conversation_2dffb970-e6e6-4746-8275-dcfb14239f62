<?php
session_start();
?>
<!DOCTYPE html>
<html>
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>Simple Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input, select, button {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <h1>Simple Login Form</h1>
    
    <?php if (isset($_GET['error'])): ?>
    <div style="background-color: #f8d7da; color: #721c24; padding: 10px; margin-bottom: 15px;">
        <?php 
            $error = $_GET['error'];
            if ($error == 'emptyfields') {
                echo "Please fill in all fields.";
            } elseif ($error == 'wrongpassword') {
                echo "Wrong password. Please try again.";
            } elseif ($error == 'nouser') {
                echo "No user found with this username and user type.";
            } elseif ($error == 'sqlerror') {
                echo "Database error - please try again later.";
            } else {
                echo "An error occurred. Please try again.";
            }
        ?>
    </div>
    <?php endif; ?>
    
    <form action="includes/login.inc.php" method="post">
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" name="username" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" required>
        </div>
        
        <div class="form-group">
            <label for="userType">User Type:</label>
            <select id="userType" name="userType" required>
                <option value="admin">Admin</option>
                <option value="teacher">Teacher</option>
                <option value="student" selected>Student</option>
                <option value="staff">Staff</option>
            </select>
        </div>
        
        <?php
        // Generate CSRF token if not already set
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        ?>
        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
        <button type="submit" name="login-submit">Login</button>
    </form>
    
    <p style="margin-top: 20px;">
        <a href="index.php">Back to main page</a> | 
        <a href="create_student.php">Create student user</a>
    </p>
</body>
</html> 