<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: index.php");
    exit();
}

require_once 'includes/dbh.inc.php';

// Initialize message variables
$success = false;
$message = '';

// Process drop request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_drop'])) {
    // Begin transaction to ensure all operations succeed or fail together
    $conn->begin_transaction();
    
    try {
        // Disable foreign key checks to avoid constraint issues
        $conn->query("SET FOREIGN_KEY_CHECKS = 0");
        
        // List of fee-related tables to drop
        $feeTables = [
            'fees',
            'fee_payments',
            'fee_types',
            'fee_map_class',
            'fee_map_session',
            'fee_map_student',
            'bkash_payments'
        ];
        
        $droppedTables = [];
        $notExistTables = [];
        
        // Try to drop each table
        foreach ($feeTables as $table) {
            // Check if table exists
            $checkTable = $conn->query("SHOW TABLES LIKE '$table'");
            
            if ($checkTable->num_rows > 0) {
                // Table exists, drop it
                $dropQuery = "DROP TABLE IF EXISTS $table";
                $conn->query($dropQuery);
                $droppedTables[] = $table;
                error_log("Dropped table: $table");
            } else {
                $notExistTables[] = $table;
                error_log("Table does not exist: $table");
            }
        }
        
        // Re-enable foreign key checks
        $conn->query("SET FOREIGN_KEY_CHECKS = 1");
        
        // Commit the transaction
        $conn->commit();
        
        $success = true;
        $message = "সমস্ত ফি সম্পর্কিত টেবিল সফলভাবে মুছে ফেলা হয়েছে!";
        if (!empty($notExistTables)) {
            $message .= " নিম্নলিখিত টেবিলগুলি ইতিমধ্যে বিদ্যমান ছিল না: " . implode(", ", $notExistTables);
        }
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        
        // Make sure to re-enable foreign key checks even on error
        $conn->query("SET FOREIGN_KEY_CHECKS = 1");
        
        error_log("Error dropping fee tables: " . $e->getMessage());
        $message = "টেবিল মুছে ফেলতে সমস্যা হয়েছে: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>ফি টেবিল মুছে ফেলুন - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        .danger-zone {
            border: 2px dashed #dc3545;
            background-color: #f8d7da;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .danger-icon {
            font-size: 48px;
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-danger text-white">
                        <h3 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>ফি টেবিল মুছে ফেলুন</h3>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($message)): ?>
                            <div class="alert alert-<?php echo $success ? 'success' : 'danger'; ?>">
                                <?php echo $message; ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="danger-zone text-center">
                            <div class="mb-3">
                                <i class="fas fa-exclamation-triangle danger-icon"></i>
                            </div>
                            <h4 class="text-danger">সতর্কতা: এই অপারেশন অপরিবর্তনীয়!</h4>
                            <p>আপনি সমস্ত ফি সম্পর্কিত টেবিল মুছে ফেলতে যাচ্ছেন। এই অপারেশন সম্পন্ন হলে, সমস্ত ফি ডাটা স্থায়ীভাবে মুছে যাবে এবং পুনরুদ্ধার করা যাবে না।</p>
                            <p><strong>মুছে ফেলা হবে এমন টেবিল:</strong> fees, fee_payments, fee_types, fee_map_class, fee_map_session, fee_map_student, bkash_payments</p>
                        </div>
                        
                        <form method="POST" action="">
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="confirm_checkbox" required>
                                <label class="form-check-label" for="confirm_checkbox">
                                    আমি নিশ্চিত করছি যে আমি সমস্ত ফি সম্পর্কিত টেবিল মুছে ফেলতে চাই এবং বুঝতে পারি যে এই পদক্ষেপটি পূর্বাবস্থায় ফেরানো যাবে না।
                                </label>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="admin/dashboard.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i> ফিরে যান
                                </a>
                                <button type="submit" name="confirm_drop" class="btn btn-danger" id="drop_btn" disabled>
                                    <i class="fas fa-trash-alt me-1"></i> সমস্ত ফি টেবিল মুছে ফেলুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Enable the drop button only when the checkbox is checked
        document.getElementById('confirm_checkbox').addEventListener('change', function() {
            document.getElementById('drop_btn').disabled = !this.checked;
        });
    </script>
</body>
</html>
