<?php
// Include database connection
require_once "includes/dbh.inc.php";

// Get latest notice
$notice_text = "বর্তমানে কোন নোটিশ নেই।";
try {
    // Ensure we have a valid connection
    $conn = ensure_connection();

    // Check if notices table exists
    $latest_notice_query = "SHOW TABLES LIKE 'notices'";
    $latest_notice_result = $conn->query($latest_notice_query);

    if ($latest_notice_result && $latest_notice_result->num_rows > 0) {
        // Get latest notice
        $sql = "SELECT * FROM notices ORDER BY date DESC LIMIT 1";
        $result = $conn->query($sql);

        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $notice_text = htmlspecialchars($row['title']) . ' - ' . htmlspecialchars(substr($row['content'], 0, 150)) .
                 (strlen($row['content']) > 150 ? '...' : '');
        }
    }
} catch (Exception $e) {
    $notice_text = "নোটিশ লোড করতে সমস্যা হচ্ছে। পরে আবার চেষ্টা করুন।";
    error_log('Notice Error: ' . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>স্ক্রোলিং নোটিশ</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        .scrolling-notice-container {
            background-color: #f8f9fa;
            padding: 0;
            border-bottom: 2px solid #dee2e6;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            width: 100%;
            height: 80px;
            display: flex;
            align-items: center;
        }
        .scrolling-notice {
            width: 100%;
            overflow: hidden;
            position: relative;
            min-height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .notice-content {
            white-space: nowrap;
            display: inline-block;
            animation: scrollText 30s linear infinite;
            padding: 5px 0;
            color: #333;
            font-weight: 600;
            font-size: 16px;
            line-height: 1.5;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
        }
        @keyframes scrollText {
            0% { transform: translate(100%, -50%); }
            100% { transform: translate(-100%, -50%); }
        }
        @-webkit-keyframes scrollText {
            0% { -webkit-transform: translate(100%, -50%); }
            100% { -webkit-transform: translate(-100%, -50%); }
        }
        @-moz-keyframes scrollText {
            0% { -moz-transform: translate(100%, -50%); }
            100% { -moz-transform: translate(-100%, -50%); }
        }
        @-o-keyframes scrollText {
            0% { -o-transform: translate(100%, -50%); }
            100% { -o-transform: translate(-100%, -50%); }
        }

        /* Make icons more visible */
        .notice-content i {
            color: #007bff;
            margin-right: 5px;
        }

        /* Make strong text more visible */
        .notice-content strong {
            color: #212529;
            font-weight: 700;
        }
    </style>
</head>
<body>
    <div class="scrolling-notice-container">
        <div class="scrolling-notice">
            <div class="notice-content" id="scrolling-text">
                <i class="fas fa-bullhorn"></i> <strong>সর্বশেষ নোটিশ:</strong>
                <?php echo $notice_text; ?>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <i class="fas fa-calendar-alt"></i> <strong>আজকের তারিখ:</strong> <?php echo date('d/m/Y'); ?>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <i class="fas fa-graduation-cap"></i> <strong>ভর্তি চলছে:</strong> নতুন শিক্ষার্থীদের জন্য ভর্তি চলছে। আজই যোগাযোগ করুন।
            </div>
        </div>
    </div>

    <script>
        // Enhanced scrolling notice animation with vertical centering
        document.addEventListener('DOMContentLoaded', function() {
            var scrollingText = document.getElementById('scrolling-text');
            if (scrollingText) {
                // Force redraw
                scrollingText.style.display = 'none';
                setTimeout(function() {
                    scrollingText.style.display = 'inline-block';
                }, 10);

                // Restart animation
                scrollingText.style.animationName = 'none';
                setTimeout(function() {
                    scrollingText.style.animationName = 'scrollText';
                }, 50);

                // Ensure vertical centering
                scrollingText.style.top = '50%';
                scrollingText.style.transform = 'translateY(-50%)';

                // Adjust container height if needed
                var container = document.querySelector('.scrolling-notice-container');
                if (container) {
                    container.style.height = '80px';
                    container.style.display = 'flex';
                    container.style.alignItems = 'center';
                }

                // Adjust notice wrapper
                var noticeWrapper = document.querySelector('.scrolling-notice');
                if (noticeWrapper) {
                    noticeWrapper.style.display = 'flex';
                    noticeWrapper.style.alignItems = 'center';
                    noticeWrapper.style.height = '100%';
                }
            }
        });
    </script>
</body>
</html>
