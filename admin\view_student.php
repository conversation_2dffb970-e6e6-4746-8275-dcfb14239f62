<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get student ID from URL
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: students.php");
    exit();
}

$studentId = $_GET['id'];

// Get student data with user information and department name
$studentQuery = "SELECT s.*, u.username, u.created_at as account_created,
                d.department_name as department
                FROM students s
                LEFT JOIN users u ON s.user_id = u.id
                LEFT JOIN departments d ON s.department_id = d.id
                WHERE s.student_id = ?";
$stmt = $conn->prepare($studentQuery);
$stmt->bind_param("s", $studentId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header("Location: students.php");
    exit();
}

$student = $result->fetch_assoc();

// Get courses enrolled by student (if they exist in the database)
$coursesQuery = "SELECT s.subject_name, s.subject_code, ss.category
                FROM subjects s
                JOIN student_subjects ss ON s.id = ss.subject_id
                WHERE ss.student_id = ?
                ORDER BY s.subject_name";
$stmt = $conn->prepare($coursesQuery);
$stmt->bind_param("i", $student['id']);
$stmt->execute();
$courses = $stmt->get_result();



// Courses table has been removed, so set these to null
$attendance = null;
$results = null;

// Old queries that referenced the courses table
// $attendanceQuery = "SELECT c.course_name, a.date, a.status
//                   FROM attendance a
//                   JOIN courses c ON a.course_id = c.id
//                   WHERE a.student_id = ?
//                   ORDER BY a.date DESC
//                   LIMIT 15";
// $stmt = $conn->prepare($attendanceQuery);
// $stmt->bind_param("i", $student['id']);
// $stmt->execute();
// $attendance = $stmt->get_result();

// $resultsQuery = "SELECT e.exam_name, c.course_name, r.marks_obtained, r.grade
//                FROM results r
//                JOIN exams e ON r.exam_id = e.id
//                JOIN courses c ON e.course_id = c.id
//                WHERE r.student_id = ?
//                ORDER BY e.exam_date DESC";
// $stmt = $conn->prepare($resultsQuery);
// $stmt->bind_param("i", $student['id']);
// $stmt->execute();
// $results = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>শিক্ষার্থী প্রোফাইল - কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .student-img {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 50%;
            border: 4px solid #f8f9fa;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .profile-header {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .student-details {
            margin-top: 20px;
        }
        .detail-item {
            margin-bottom: 10px;
        }
        .tab-pane {
            padding: 20px 0;
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> কোর্স
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="departments.php">
                            <i class="fas fa-building me-2"></i> বিভাগ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-file-pdf me-2"></i> রিপোর্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>শিক্ষার্থী প্রোফাইল</h2>
                        <p class="text-muted">শিক্ষার্থীর বিস্তারিত তথ্য দেখুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="students.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>শিক্ষার্থী তালিকায় ফিরে যান
                        </a>
                        <a href="edit_student.php?id=<?php echo $student['id']; ?>" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>শিক্ষার্থী সম্পাদনা করুন
                        </a>
                    </div>
                </div>

                <!-- Profile Header -->
                <div class="profile-header">
                    <div class="row align-items-center">
                        <div class="col-md-2 text-center mb-3 mb-md-0">
                            <?php if (!empty($student['profile_photo'])): ?>
                                <img src="../<?php echo $student['profile_photo']; ?>" alt="শিক্ষার্থীর ছবি" class="student-img">
                            <?php else: ?>
                                <div class="student-img d-flex align-items-center justify-content-center bg-secondary text-white">
                                    <i class="fas fa-user fa-4x"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-10">
                            <h3><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h3>
                            <p class="text-muted mb-2">শিক্ষার্থী আইডি: <?php echo htmlspecialchars($student['student_id']); ?></p>
                            <p class="mb-2"><i class="fas fa-graduation-cap me-2"></i> <?php echo htmlspecialchars($student['department'] ?? 'N/A'); ?> - ব্যাচ <?php echo htmlspecialchars($student['batch'] ?? 'N/A'); ?></p>
                            <p class="mb-2"><i class="fas fa-envelope me-2"></i> <?php echo htmlspecialchars($student['email']); ?></p>
                            <p class="mb-0"><i class="fas fa-phone me-2"></i> <?php echo htmlspecialchars($student['phone']); ?></p>
                        </div>
                    </div>
                </div>

                <!-- Student Information Tabs -->
                <div class="card">
                    <div class="card-body">
                        <ul class="nav nav-tabs" id="studentTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="personal-tab" data-bs-toggle="tab" data-bs-target="#personal" type="button" role="tab" aria-controls="personal" aria-selected="true">ব্যক্তিগত তথ্য</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="academic-tab" data-bs-toggle="tab" data-bs-target="#academic" type="button" role="tab" aria-controls="academic" aria-selected="false">একাডেমিক তথ্য</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="courses-tab" data-bs-toggle="tab" data-bs-target="#courses" type="button" role="tab" aria-controls="courses" aria-selected="false">বিষয়সমূহ</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="results-tab" data-bs-toggle="tab" data-bs-target="#results" type="button" role="tab" aria-controls="results" aria-selected="false">ফলাফল</button>
                            </li>

                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="attendance-tab" data-bs-toggle="tab" data-bs-target="#attendance" type="button" role="tab" aria-controls="attendance" aria-selected="false">উপস্থিতি</button>
                            </li>
                        </ul>

                        <div class="tab-content" id="studentTabsContent">
                            <!-- Personal Information Tab -->
                            <div class="tab-pane fade show active" id="personal" role="tabpanel" aria-labelledby="personal-tab">
                                <div class="row student-details">
                                    <div class="col-md-6">
                                        <h5 class="mb-3">ব্যক্তিগত তথ্য</h5>
                                        <div class="detail-item">
                                            <strong>শিক্ষার্থী আইডি:</strong> <?php echo htmlspecialchars($student['student_id']); ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>পূর্ণ নাম:</strong> <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>ইমেইল:</strong> <?php echo htmlspecialchars($student['email']); ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>ফোন:</strong> <?php echo htmlspecialchars($student['phone']); ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>জন্ম তারিখ:</strong> <?php echo !empty($student['dob']) ? date('d M Y', strtotime($student['dob'])) : 'N/A'; ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>লিঙ্গ:</strong> <?php echo htmlspecialchars($student['gender']); ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>ঠিকানা:</strong> <?php echo htmlspecialchars($student['address']); ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>ইউজারনেম:</strong> <?php echo htmlspecialchars($student['username']); ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>অ্যাকাউন্ট তৈরির তারিখ:</strong> <?php echo date('d M Y H:i', strtotime($student['account_created'])); ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h5 class="mb-3">পিতার তথ্য</h5>
                                        <?php if (!empty($student['father_name']) || !empty($student['father_phone'])): ?>
                                        <div class="detail-item">
                                            <strong>পিতার নাম:</strong> <?php echo !empty($student['father_name']) ? htmlspecialchars($student['father_name']) : 'N/A'; ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>পেশা:</strong> <?php echo !empty($student['father_occupation']) ? htmlspecialchars($student['father_occupation']) : 'N/A'; ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>ফোন:</strong> <?php echo !empty($student['father_phone']) ? htmlspecialchars($student['father_phone']) : 'N/A'; ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>ইমেইল:</strong> <?php echo !empty($student['father_email']) ? htmlspecialchars($student['father_email']) : 'N/A'; ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>মাসিক আয়:</strong> <?php echo !empty($student['father_income']) ? htmlspecialchars($student['father_income']) : 'N/A'; ?>
                                        </div>
                                        <?php else: ?>
                                        <div class="alert alert-info">
                                            পিতার কোন তথ্য পাওয়া যায়নি।
                                        </div>
                                        <?php endif; ?>

                                        <h5 class="mb-3 mt-4">মাতার তথ্য</h5>
                                        <?php if (!empty($student['mother_name']) || !empty($student['mother_phone'])): ?>
                                        <div class="detail-item">
                                            <strong>মাতার নাম:</strong> <?php echo !empty($student['mother_name']) ? htmlspecialchars($student['mother_name']) : 'N/A'; ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>পেশা:</strong> <?php echo !empty($student['mother_occupation']) ? htmlspecialchars($student['mother_occupation']) : 'N/A'; ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>ফোন:</strong> <?php echo !empty($student['mother_phone']) ? htmlspecialchars($student['mother_phone']) : 'N/A'; ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>ইমেইল:</strong> <?php echo !empty($student['mother_email']) ? htmlspecialchars($student['mother_email']) : 'N/A'; ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>মাসিক আয়:</strong> <?php echo !empty($student['mother_income']) ? htmlspecialchars($student['mother_income']) : 'N/A'; ?>
                                        </div>
                                        <?php else: ?>
                                        <div class="alert alert-info">
                                            মাতার কোন তথ্য পাওয়া যায়নি।
                                        </div>
                                        <?php endif; ?>

                                        <h5 class="mb-3 mt-4">অভিভাবকের তথ্য</h5>
                                        <?php if (!empty($student['guardian_name']) || !empty($student['guardian_phone'])): ?>
                                        <div class="detail-item">
                                            <strong>অভিভাবকের নাম:</strong> <?php echo !empty($student['guardian_name']) ? htmlspecialchars($student['guardian_name']) : 'N/A'; ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>সম্পর্ক:</strong> <?php echo !empty($student['guardian_relation']) ? htmlspecialchars($student['guardian_relation']) : 'N/A'; ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>ফোন:</strong> <?php echo !empty($student['guardian_phone']) ? htmlspecialchars($student['guardian_phone']) : 'N/A'; ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>ইমেইল:</strong> <?php echo !empty($student['guardian_email']) ? htmlspecialchars($student['guardian_email']) : 'N/A'; ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>ঠিকানা:</strong> <?php echo !empty($student['guardian_address']) ? htmlspecialchars($student['guardian_address']) : 'N/A'; ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>পেশা:</strong> <?php echo !empty($student['guardian_occupation']) ? htmlspecialchars($student['guardian_occupation']) : 'N/A'; ?>
                                        </div>
                                        <?php else: ?>
                                        <div class="alert alert-info">
                                            অভিভাবকের কোন তথ্য পাওয়া যায়নি।
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Academic Information Tab -->
                            <div class="tab-pane fade" id="academic" role="tabpanel" aria-labelledby="academic-tab">
                                <div class="row student-details">
                                    <div class="col-md-6">
                                        <h5 class="mb-3">একাডেমিক তথ্য</h5>
                                        <div class="detail-item">
                                            <strong>বিভাগ:</strong> <?php echo htmlspecialchars($student['department'] ?? 'N/A'); ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>ব্যাচ:</strong> <?php echo htmlspecialchars($student['batch'] ?? 'N/A'); ?>
                                        </div>
                                        <div class="detail-item">
                                            <strong>ভর্তির তারিখ:</strong> <?php echo !empty($student['admission_date']) ? date('d M Y', strtotime($student['admission_date'])) : 'N/A'; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Courses Tab -->
                            <div class="tab-pane fade" id="courses" role="tabpanel" aria-labelledby="courses-tab">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>বিষয় কোড</th>
                                                <th>বিষয়ের নাম</th>
                                                <th>ক্যাটাগরি</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($courses && $courses->num_rows > 0): ?>
                                                <?php while ($course = $courses->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($course['subject_code']); ?></td>
                                                        <td><?php echo htmlspecialchars($course['subject_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($course['category']); ?></td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="3" class="text-center">কোন বিষয় পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Results Tab -->
                            <div class="tab-pane fade" id="results" role="tabpanel" aria-labelledby="results-tab">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>পরীক্ষা</th>
                                                <th>কোর্স</th>
                                                <th>নম্বর</th>
                                                <th>গ্রেড</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($results && $results->num_rows > 0): ?>
                                                <?php while ($result = $results->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($result['exam_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($result['course_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($result['marks_obtained']); ?></td>
                                                        <td><?php echo htmlspecialchars($result['grade']); ?></td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="4" class="text-center">কোন ফলাফল পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>



                            <!-- Attendance Tab -->
                            <div class="tab-pane fade" id="attendance" role="tabpanel" aria-labelledby="attendance-tab">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>কোর্স</th>
                                                <th>তারিখ</th>
                                                <th>স্ট্যাটাস</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($attendance && $attendance->num_rows > 0): ?>
                                                <?php while ($attend = $attendance->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($attend['course_name']); ?></td>
                                                        <td><?php echo date('d M Y', strtotime($attend['date'])); ?></td>
                                                        <td>
                                                            <?php
                                                                $statusClass = '';
                                                                switch($attend['status']) {
                                                                    case 'Present': $statusClass = 'badge bg-success'; break;
                                                                    case 'Absent': $statusClass = 'badge bg-danger'; break;
                                                                    case 'Late': $statusClass = 'badge bg-warning'; break;
                                                                }
                                                            ?>
                                                            <span class="<?php echo $statusClass; ?>"><?php echo htmlspecialchars($attend['status']); ?></span>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="3" class="text-center">কোন উপস্থিতি রেকর্ড পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>