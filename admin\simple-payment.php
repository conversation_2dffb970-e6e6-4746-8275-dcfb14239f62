<?php
// Ultra simple payment system
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start output buffering to catch any errors
ob_start();

echo "<!DOCTYPE html><html><head><title>Simple Payment System</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>body{font-family:Arial;margin:20px;} .debug{background:#f8f9fa;padding:10px;margin:10px 0;border-radius:5px;}</style>";
echo "</head><body><div class='container'>";

echo "<h1>💰 Simple Payment System</h1>";

// Database connection
try {
    $servername = "127.0.0.1";
    $username = "root";
    $password = "";
    $dbname = "zfaw";
    
    $conn = new mysqli($servername, $username, $password, $dbname);
    
    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }
    
    echo "<div class='alert alert-success'>✅ Database connected</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ Database error: " . $e->getMessage() . "</div>";
    echo "</div></body></html>";
    exit();
}

// Handle payment
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['make_payment'])) {
    echo "<div class='debug'>";
    echo "<h3>🔄 Processing Payment</h3>";
    
    $feeId = intval($_POST['fee_id']);
    $amount = floatval($_POST['amount']);
    $method = $_POST['method'];
    $date = $_POST['date'];
    
    echo "<p><strong>Fee ID:</strong> $feeId</p>";
    echo "<p><strong>Amount:</strong> ৳$amount</p>";
    echo "<p><strong>Method:</strong> $method</p>";
    echo "<p><strong>Date:</strong> $date</p>";
    
    if ($feeId > 0 && $amount > 0) {
        try {
            // Step 1: Get fee info
            $feeQuery = "SELECT f.*, CONCAT(s.first_name, ' ', s.last_name) as student_name 
                        FROM fees f 
                        JOIN students s ON f.student_id = s.id 
                        WHERE f.id = $feeId";
            
            echo "<p><strong>Query:</strong> $feeQuery</p>";
            
            $result = $conn->query($feeQuery);
            
            if ($result && $result->num_rows > 0) {
                $fee = $result->fetch_assoc();
                echo "<p style='color:green;'>✅ Fee found: {$fee['student_name']} - {$fee['fee_type']}</p>";
                
                // Step 2: Check payments table exists
                $checkTable = $conn->query("SHOW TABLES LIKE 'payments'");
                if ($checkTable->num_rows === 0) {
                    echo "<p>🔧 Creating payments table...</p>";
                    $createTable = "CREATE TABLE payments (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        fee_id INT NOT NULL,
                        student_id INT NOT NULL,
                        amount DECIMAL(10,2) NOT NULL,
                        payment_date DATE NOT NULL,
                        payment_method VARCHAR(50) DEFAULT 'cash',
                        receipt_number VARCHAR(100),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )";
                    
                    if ($conn->query($createTable)) {
                        echo "<p style='color:green;'>✅ Payments table created</p>";
                    } else {
                        echo "<p style='color:red;'>❌ Table creation failed: " . $conn->error . "</p>";
                    }
                }
                
                // Step 3: Insert payment
                $receiptNo = 'RCP-' . date('Ymd') . '-' . $feeId . '-' . time();
                $insertPayment = "INSERT INTO payments (fee_id, student_id, amount, payment_date, payment_method, receipt_number) 
                                 VALUES ($feeId, {$fee['student_id']}, $amount, '$date', '$method', '$receiptNo')";
                
                echo "<p><strong>Insert Query:</strong> $insertPayment</p>";
                
                if ($conn->query($insertPayment)) {
                    echo "<p style='color:green;'>✅ Payment inserted with ID: " . $conn->insert_id . "</p>";
                    
                    // Step 4: Update fee
                    $currentPaid = isset($fee['paid']) ? $fee['paid'] : 0;
                    $newPaid = $currentPaid + $amount;
                    $newStatus = ($newPaid >= $fee['amount']) ? 'paid' : 'partial';
                    
                    // Check if paid column exists
                    $checkColumn = $conn->query("SHOW COLUMNS FROM fees LIKE 'paid'");
                    if ($checkColumn->num_rows === 0) {
                        echo "<p>🔧 Adding paid column to fees table...</p>";
                        $addColumn = "ALTER TABLE fees ADD COLUMN paid DECIMAL(10,2) DEFAULT 0";
                        $conn->query($addColumn);
                    }
                    
                    // Check if payment_status column exists
                    $checkStatusColumn = $conn->query("SHOW COLUMNS FROM fees LIKE 'payment_status'");
                    if ($checkStatusColumn->num_rows === 0) {
                        echo "<p>🔧 Adding payment_status column to fees table...</p>";
                        $addStatusColumn = "ALTER TABLE fees ADD COLUMN payment_status VARCHAR(20) DEFAULT 'due'";
                        $conn->query($addStatusColumn);
                    }
                    
                    $updateFee = "UPDATE fees SET paid = $newPaid, payment_status = '$newStatus' WHERE id = $feeId";
                    echo "<p><strong>Update Query:</strong> $updateFee</p>";
                    
                    if ($conn->query($updateFee)) {
                        echo "<div class='alert alert-success'>";
                        echo "<h4>🎉 Payment Successful!</h4>";
                        echo "<ul>";
                        echo "<li><strong>Receipt:</strong> $receiptNo</li>";
                        echo "<li><strong>Amount Paid:</strong> ৳$amount</li>";
                        echo "<li><strong>New Total Paid:</strong> ৳$newPaid</li>";
                        echo "<li><strong>Status:</strong> $newStatus</li>";
                        echo "</ul>";
                        echo "</div>";
                    } else {
                        echo "<p style='color:red;'>❌ Fee update failed: " . $conn->error . "</p>";
                    }
                } else {
                    echo "<p style='color:red;'>❌ Payment insert failed: " . $conn->error . "</p>";
                }
            } else {
                echo "<p style='color:red;'>❌ Fee not found</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color:red;'>❌ Error: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color:red;'>❌ Invalid data</p>";
    }
    echo "</div>";
}

// Show all fees
echo "<h2>📋 All Fees</h2>";
$allFeesQuery = "SELECT f.*, CONCAT(s.first_name, ' ', s.last_name) as student_name 
                FROM fees f 
                JOIN students s ON f.student_id = s.id 
                ORDER BY f.id DESC";

$allFeesResult = $conn->query($allFeesQuery);

if ($allFeesResult && $allFeesResult->num_rows > 0) {
    echo "<table class='table table-striped'>";
    echo "<tr><th>ID</th><th>Student</th><th>Fee Type</th><th>Amount</th><th>Paid</th><th>Due</th><th>Status</th><th>Action</th></tr>";
    
    while ($fee = $allFeesResult->fetch_assoc()) {
        $paid = isset($fee['paid']) ? $fee['paid'] : 0;
        $due = $fee['amount'] - $paid;
        $status = isset($fee['payment_status']) ? $fee['payment_status'] : 'due';
        
        echo "<tr>";
        echo "<td>{$fee['id']}</td>";
        echo "<td>{$fee['student_name']}</td>";
        echo "<td>{$fee['fee_type']}</td>";
        echo "<td>৳{$fee['amount']}</td>";
        echo "<td>৳$paid</td>";
        echo "<td>৳$due</td>";
        echo "<td>$status</td>";
        echo "<td>";
        
        if ($due > 0) {
            echo "<button class='btn btn-sm btn-success' onclick='payFee({$fee['id']}, $due, \"{$fee['student_name']}\", \"{$fee['fee_type']}\")'>Pay</button>";
        } else {
            echo "<span class='badge bg-success'>Paid</span>";
        }
        
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No fees found</p>";
}

// Payment form
echo "<div id='paymentForm' style='display:none;' class='mt-4'>";
echo "<div class='card'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h5>💳 Make Payment</h5>";
echo "</div>";
echo "<div class='card-body'>";
echo "<form method='post'>";
echo "<input type='hidden' id='pay_fee_id' name='fee_id'>";
echo "<div class='row mb-3'>";
echo "<div class='col-md-6'>";
echo "<label class='form-label'>Student & Fee:</label>";
echo "<input type='text' id='pay_info' class='form-control' readonly>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<label class='form-label'>Due Amount:</label>";
echo "<input type='text' id='pay_due' class='form-control' readonly>";
echo "</div>";
echo "</div>";
echo "<div class='row mb-3'>";
echo "<div class='col-md-4'>";
echo "<label class='form-label'>Payment Amount:</label>";
echo "<input type='number' name='amount' id='pay_amount' class='form-control' step='0.01' required>";
echo "</div>";
echo "<div class='col-md-4'>";
echo "<label class='form-label'>Payment Method:</label>";
echo "<select name='method' class='form-control'>";
echo "<option value='cash'>Cash</option>";
echo "<option value='bank'>Bank Transfer</option>";
echo "<option value='mobile'>Mobile Banking</option>";
echo "</select>";
echo "</div>";
echo "<div class='col-md-4'>";
echo "<label class='form-label'>Payment Date:</label>";
echo "<input type='date' name='date' class='form-control' value='" . date('Y-m-d') . "' required>";
echo "</div>";
echo "</div>";
echo "<div class='d-flex gap-2'>";
echo "<button type='submit' name='make_payment' class='btn btn-success'>Make Payment</button>";
echo "<button type='button' class='btn btn-secondary' onclick='hidePaymentForm()'>Cancel</button>";
echo "</div>";
echo "</form>";
echo "</div>";
echo "</div>";
echo "</div>";

// Recent payments
echo "<h2>📋 Recent Payments</h2>";
$paymentsQuery = "SELECT p.*, CONCAT(s.first_name, ' ', s.last_name) as student_name, f.fee_type 
                 FROM payments p 
                 JOIN students s ON p.student_id = s.id 
                 JOIN fees f ON p.fee_id = f.id 
                 ORDER BY p.id DESC LIMIT 10";

$paymentsResult = $conn->query($paymentsQuery);

if ($paymentsResult && $paymentsResult->num_rows > 0) {
    echo "<table class='table table-striped'>";
    echo "<tr><th>ID</th><th>Student</th><th>Fee Type</th><th>Amount</th><th>Method</th><th>Date</th><th>Receipt</th></tr>";
    
    while ($payment = $paymentsResult->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$payment['id']}</td>";
        echo "<td>{$payment['student_name']}</td>";
        echo "<td>{$payment['fee_type']}</td>";
        echo "<td>৳{$payment['amount']}</td>";
        echo "<td>{$payment['payment_method']}</td>";
        echo "<td>{$payment['payment_date']}</td>";
        echo "<td>{$payment['receipt_number']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No payments found</p>";
}

echo "<div class='mt-4'>";
echo "<a href='fee_management.php' class='btn btn-primary'>Back to Fee Management</a>";
echo "</div>";

echo "</div>";

echo "<script>";
echo "function payFee(feeId, dueAmount, studentName, feeType) {";
echo "  document.getElementById('pay_fee_id').value = feeId;";
echo "  document.getElementById('pay_info').value = studentName + ' - ' + feeType;";
echo "  document.getElementById('pay_due').value = '৳' + dueAmount;";
echo "  document.getElementById('pay_amount').value = dueAmount;";
echo "  document.getElementById('pay_amount').max = dueAmount;";
echo "  document.getElementById('paymentForm').style.display = 'block';";
echo "  document.getElementById('paymentForm').scrollIntoView();";
echo "}";
echo "function hidePaymentForm() {";
echo "  document.getElementById('paymentForm').style.display = 'none';";
echo "}";
echo "</script>";

echo "</body></html>";

// Flush output buffer
ob_end_flush();
?>
