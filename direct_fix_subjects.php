<?php
// Include database connection
require_once "includes/dbh.inc.php";

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>সরাসরি বিষয় ফিক্স</h1>";

// First, check if we have subjects
$subjectsQuery = "SELECT COUNT(*) as count FROM subjects";
$result = $conn->query($subjectsQuery);
$subjectCount = $result->fetch_assoc()['count'];

echo "<p>মোট বিষয় সংখ্যা: $subjectCount</p>";

// If no subjects, add some
if ($subjectCount < 5) {
    // Add demo subjects
    $demoSubjects = [
        ['বাংলা', 'BAN101', 'required'],
        ['ইংরেজি', 'ENG101', 'required'],
        ['গণিত', 'MATH101', 'required'],
        ['বিজ্ঞান', 'SCI101', 'required'],
        ['ইতিহাস', 'HIST101', 'optional'],
        ['ভূগোল', 'GEO101', 'optional'],
        ['অর্থনীতি', 'ECON101', 'optional'],
        ['রসায়ন', 'CHEM101', 'optional'],
        ['পদার্থবিজ্ঞান', 'PHY101', 'fourth'],
        ['জীববিজ্ঞান', 'BIO101', 'fourth'],
        ['কম্পিউটার', 'CSE101', 'fourth'],
        ['ধর্ম', 'REL101', 'optional']
    ];
    
    $insertSubjectQuery = "INSERT INTO subjects (subject_name, subject_code, category, is_active) VALUES (?, ?, ?, 1)";
    $stmt = $conn->prepare($insertSubjectQuery);
    
    foreach ($demoSubjects as $subject) {
        $stmt->bind_param("sss", $subject[0], $subject[1], $subject[2]);
        $stmt->execute();
        echo "<p>বিষয় যোগ করা হয়েছে: {$subject[0]} (ক্যাটাগরি: {$subject[2]})</p>";
    }
}

// Get all departments
$departmentsQuery = "SELECT id, department_name FROM departments";
$departments = $conn->query($departmentsQuery);

if (!$departments || $departments->num_rows == 0) {
    echo "<p>কোন বিভাগ পাওয়া যায়নি। অনুগ্রহ করে প্রথমে বিভাগ যোগ করুন।</p>";
    exit;
}

// Get all subjects
$subjectsQuery = "SELECT id, subject_name, category FROM subjects WHERE is_active = 1";
$subjects = $conn->query($subjectsQuery);

if (!$subjects || $subjects->num_rows == 0) {
    echo "<p>কোন বিষয় পাওয়া যায়নি। অনুগ্রহ করে প্রথমে বিষয় যোগ করুন।</p>";
    exit;
}

// Store subjects by category
$allSubjects = [];
while ($subject = $subjects->fetch_assoc()) {
    $allSubjects[] = $subject;
}

// Check if department_subject_types table exists
$tableExists = $conn->query("SHOW TABLES LIKE 'department_subject_types'")->num_rows > 0;

if (!$tableExists) {
    // Create the table
    $createTableQuery = "CREATE TABLE department_subject_types (
        id INT AUTO_INCREMENT PRIMARY KEY,
        department_id INT NOT NULL,
        subject_id INT NOT NULL,
        subject_type ENUM('required', 'optional', 'fourth') NOT NULL DEFAULT 'optional',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_dept_subject (department_id, subject_id),
        FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
    )";
    
    if ($conn->query($createTableQuery)) {
        echo "<p style='color:green'>department_subject_types টেবিল সফলভাবে তৈরি করা হয়েছে।</p>";
    } else {
        echo "<p style='color:red'>টেবিল তৈরি করতে ত্রুটি: " . $conn->error . "</p>";
        exit;
    }
}

// Begin transaction
$conn->begin_transaction();

try {
    // For each department, assign all subjects
    while ($department = $departments->fetch_assoc()) {
        $departmentId = $department['id'];
        $departmentName = $department['department_name'];
        
        echo "<h2>বিভাগ: {$departmentName} (ID: {$departmentId})</h2>";
        
        // First, ensure all subjects are in subject_departments table
        $insertDeptSubjectQuery = "INSERT IGNORE INTO subject_departments (department_id, subject_id) VALUES (?, ?)";
        $stmt = $conn->prepare($insertDeptSubjectQuery);
        
        foreach ($allSubjects as $subject) {
            $subjectId = $subject['id'];
            $stmt->bind_param("ii", $departmentId, $subjectId);
            $stmt->execute();
        }
        
        echo "<p>সকল বিষয় subject_departments টেবিলে যোগ করা হয়েছে।</p>";
        
        // Now, assign subject types in department_subject_types table
        $insertTypeQuery = "INSERT INTO department_subject_types (department_id, subject_id, subject_type) 
                           VALUES (?, ?, ?) 
                           ON DUPLICATE KEY UPDATE subject_type = VALUES(subject_type)";
        $stmt = $conn->prepare($insertTypeQuery);
        
        $requiredCount = 0;
        $optionalCount = 0;
        $fourthCount = 0;
        
        foreach ($allSubjects as $subject) {
            $subjectId = $subject['id'];
            $subjectType = $subject['category'];
            $stmt->bind_param("iis", $departmentId, $subjectId, $subjectType);
            $stmt->execute();
            
            if ($subjectType == 'required') {
                $requiredCount++;
            } elseif ($subjectType == 'optional') {
                $optionalCount++;
            } elseif ($subjectType == 'fourth') {
                $fourthCount++;
            }
        }
        
        echo "<p>বিষয় ক্যাটাগরি সেট করা হয়েছে:</p>";
        echo "<ul>";
        echo "<li>আবশ্যিক বিষয়: $requiredCount টি</li>";
        echo "<li>ঐচ্ছিক বিষয়: $optionalCount টি</li>";
        echo "<li>৪র্থ বিষয়: $fourthCount টি</li>";
        echo "</ul>";
    }
    
    // Commit transaction
    $conn->commit();
    
    echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>সফলভাবে আপডেট করা হয়েছে!</h3>";
    echo "<p>সকল বিভাগের জন্য বিষয় ক্যাটাগরি সঠিকভাবে সেট করা হয়েছে।</p>";
    echo "<p>এখন আপনি <a href='admin/student_subject_selection.php?id=STD-601523'>শিক্ষার্থী বিষয় নির্বাচন</a> পেজে যেতে পারেন।</p>";
    echo "</div>";
    
} catch (Exception $e) {
    // Rollback on error
    $conn->rollback();
    
    echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>ত্রুটি!</h3>";
    echo "<p>বিষয় ক্যাটাগরি সেট করতে ব্যর্থ: " . $e->getMessage() . "</p>";
    echo "</div>";
}

// Close connection
$conn->close();
?>
