<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    die('Unauthorized access');
}

echo "<h1>Fee Management Debug Tool</h1>";

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>🔍 Form Submission Debug</h2>";
    echo "<pre>";
    echo "POST Data:\n";
    print_r($_POST);
    echo "\nSESSION Data:\n";
    print_r($_SESSION);
    echo "</pre>";
    
    // Check specific fields
    $studentIds = isset($_POST['student_ids']) ? $_POST['student_ids'] : [];
    $feeType = isset($_POST['fee_type']) ? $_POST['fee_type'] : '';
    $amount = isset($_POST['amount']) ? floatval($_POST['amount']) : 0;
    
    echo "<h3>📊 Field Analysis:</h3>";
    echo "<ul>";
    echo "<li><strong>Student IDs:</strong> " . (empty($studentIds) ? "❌ Empty" : "✅ " . count($studentIds) . " students selected") . "</li>";
    echo "<li><strong>Fee Type:</strong> " . (empty($feeType) ? "❌ Empty" : "✅ " . htmlspecialchars($feeType)) . "</li>";
    echo "<li><strong>Amount:</strong> " . ($amount <= 0 ? "❌ Invalid" : "✅ " . $amount) . "</li>";
    echo "</ul>";
    
    if (!empty($studentIds)) {
        echo "<h3>📋 Selected Students:</h3>";
        echo "<ul>";
        foreach ($studentIds as $studentId) {
            echo "<li>Student ID: " . htmlspecialchars($studentId) . "</li>";
        }
        echo "</ul>";
    }
}

// Check database tables
echo "<h2>🗄️ Database Check</h2>";

// Check students table
$studentsQuery = "SELECT COUNT(*) as count FROM students";
$result = $conn->query($studentsQuery);
$studentCount = $result->fetch_assoc()['count'];
echo "<p><strong>Students in database:</strong> $studentCount</p>";

// Check sessions table
$sessionsQuery = "SELECT id, session_name FROM sessions ORDER BY id DESC LIMIT 5";
$result = $conn->query($sessionsQuery);
echo "<h3>📅 Recent Sessions:</h3>";
echo "<ul>";
while ($row = $result->fetch_assoc()) {
    echo "<li>ID: {$row['id']}, Name: {$row['session_name']}</li>";
}
echo "</ul>";

// Check classes table
$classesQuery = "SELECT id, class_name FROM classes ORDER BY id LIMIT 5";
$result = $conn->query($classesQuery);
echo "<h3>🏫 Classes:</h3>";
echo "<ul>";
while ($row = $result->fetch_assoc()) {
    echo "<li>ID: {$row['id']}, Name: {$row['class_name']}</li>";
}
echo "</ul>";

// Check fees table
$feesQuery = "SELECT COUNT(*) as count FROM fees";
$result = $conn->query($feesQuery);
$feeCount = $result->fetch_assoc()['count'];
echo "<p><strong>Fees in database:</strong> $feeCount</p>";

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <title>Fee Debug Tool</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>

<div class="debug-section">
    <h2>🧪 Test Fee Addition Form</h2>
    <form method="post" action="">
        <div class="mb-3">
            <label class="form-label">Select Students (for testing):</label>
            <div>
                <input type="checkbox" name="student_ids[]" value="1" id="student1">
                <label for="student1">Test Student 1</label>
            </div>
            <div>
                <input type="checkbox" name="student_ids[]" value="2" id="student2">
                <label for="student2">Test Student 2</label>
            </div>
        </div>
        
        <div class="mb-3">
            <label class="form-label">Fee Type:</label>
            <select name="fee_type" class="form-control">
                <option value="">Select Fee Type</option>
                <option value="tuition">Tuition Fee</option>
                <option value="admission">Admission Fee</option>
                <option value="exam">Exam Fee</option>
            </select>
        </div>
        
        <div class="mb-3">
            <label class="form-label">Amount:</label>
            <input type="number" name="amount" class="form-control" value="1000" step="0.01">
        </div>
        
        <div class="mb-3">
            <label class="form-label">Due Date:</label>
            <input type="date" name="due_date" class="form-control" value="<?php echo date('Y-m-d'); ?>">
        </div>
        
        <button type="submit" name="add_fee" class="btn btn-primary">Test Add Fee</button>
    </form>
</div>

<div class="debug-section">
    <h2>🔗 Quick Links</h2>
    <a href="fee_management.php" class="btn btn-success">Go to Fee Management</a>
    <a href="ajax/get_students.php?session_id=1&class_id=1" class="btn btn-info" target="_blank">Test Student AJAX</a>
</div>

<div class="debug-section">
    <h2>📝 Common Issues & Solutions</h2>
    <ul>
        <li><strong>No students showing:</strong> Check if sessions/classes exist and have students</li>
        <li><strong>Form not submitting:</strong> Check JavaScript console for errors</li>
        <li><strong>No success message:</strong> Check if session messages are being displayed</li>
        <li><strong>Database errors:</strong> Check error logs</li>
    </ul>
</div>

<script>
console.log('Debug page loaded');

// Test AJAX call
fetch('ajax/get_students.php?session_id=1&class_id=1')
    .then(response => response.json())
    .then(data => {
        console.log('AJAX Test Result:', data);
        if (data.success && data.students) {
            console.log('Students found:', data.students.length);
        } else {
            console.log('No students found or error occurred');
        }
    })
    .catch(error => {
        console.error('AJAX Test Error:', error);
    });
</script>

</body>
</html>
